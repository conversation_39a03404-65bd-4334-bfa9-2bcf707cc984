# Google Drive Integration for Docman

## 1. Overview
Enable tenants to integrate Google Drive similarly to existing SharePoint. Use a single Google Service Account; admin shares a Drive or folder to this account and provides the URL or ID.

## 2. Goals
- Provide seamless folder creation & mapping in Google Drive.  
- Sync folder renaming and permission updates based on entity owners.  
- Reuse existing `DocumentSetting`, `DocumentMapping`, `DocumentPermissionMapping` models.

## 3. User Stories
1. **Setup**  
   - As an admin, I can test and complete Google Drive integration by providing a shared folder or drive URL/ID.  
2. **Folder Management**  
   - As the system, when a client or matter is created or updated, I ensure a corresponding Drive folder exists and is named correctly.  
3. **Permission Sync**  
   - As the system, I sync Google Drive permissions to match current owners’ emails.

## 4. Functional Requirements

### 4.1 Configuration & Setup
- **Test Setup Endpoint**  
  - **Endpoint**: `POST /v1/integrations/google-drive/test-setup`  
  - **Payload**:  
    ```json
    { "url_or_id": "<folder_or_drive_url_or_id>" }
    ```  
  - **Action**: Validate that the Service Account has sufficient permissions (e.g., list/view) on the provided folder or drive.  
  - **Response**:  
    - `200 OK` with `{ "status": "success" }` if access is confirmed.  
    - `4xx/5xx` with error message if access is denied or invalid.

- **Complete Setup Endpoint**  
  - **Endpoint**: `POST /v1/integrations/google-drive/complete-setup`  
  - **Payload**:  
    ```json
    { "url_or_id": "<folder_or_drive_url_or_id>" }
    ```  
  - **Action**: Store the provided ID as the root folder/drive for this tenant (tenant derived from auth header).  
  - **Note**: The ID may represent either a folder or a full Drive; flag accordingly in `DocumentSetting`.

### 4.2 Folder Creation & Update
- **On Client/Matter Create**  
  - Check `DocumentMapping` for `provider="google"`; if missing, call Drive API `Files.create` under the configured root.  
  - Save mapping: `{ tenant_id, provider: "google", object_id, drive_id }`.  
- **On Rename**  
  - Call Drive API `Files.update` to rename the folder when client/matter name changes.

### 4.3 Permission Management
- Fetch current permissions via `Permissions.list`.  
- Compute diff against entity owners’ emails.  
- Use `Permissions.create`, `Permissions.update`, or `Permissions.delete` to align Drive ACLs.

### 4.4 APIs & SDK
- **Package**: `pkg/gdrive`  
- **SDK**: Official Google Drive Go client (`google.golang.org/api/drive/v3`).  
- **Auth**: Load a single Service Account JSON credential from a secure store (e.g., K8s secret or vault).  
- **Token Management**: Use JWT to generate access tokens; cache tokens until expiry.

## 5. Data Model Changes
- **DocumentSetting**  
  - Add keys:  
    ```go
    const (
      KeyGoogleRootID = "google_root_id"  // folder or drive
    )
    ```
- **DocumentMapping**  
  - Reuse existing table; set `provider = "google"`.

## 6. Non-Functional Requirements
- **Security**: Store Service Account JSON securely; restrict read access.  
- **Performance**: Cache tokens; batch API calls where possible; respect Drive API rate limits.  
- **Reliability**: Retry transient failures with exponential backoff.

## 7. Error Handling
- Return clear HTTP errors on setup/test failures.  
- Log API errors with context; continue processing non-critical workflows.

## 8. Estimate

| Task                                            | Effort |
|-------------------------------------------------|--------|
| Design SDK & Models                             | 1 day  |
| Implement `pkg/gdrive` client                   | 2 days |
| Test-setup & complete-setup endpoints            | 1 day  |
| Integrate create/update folder logic            | 2 days |
| Permission sync implementation                  | 1 day  |
| Unit & Integration Tests                        | 2 days |
| Documentation & Deployment                      | 1 day  |

**Total:** ~10 working days
