# Google Drive Shared Drive Support for Service Accounts

## Problem

When using Service Accounts to upload files to Google Drive, users encounter a 403 error:

```json
{
  "error": {
    "code": 403,
    "message": "Service Accounts do not have storage quota. Leverage shared drives (https://developers.google.com/workspace/drive/api/guides/about-shareddrives), or use OAuth delegation (http://support.google.com/a/answer/7281227) instead.",
    "errors": [
      {
        "message": "Service Accounts do not have storage quota. Leverage shared drives (https://developers.google.com/workspace/drive/api/guides/about-shareddrives), or use OAuth delegation (http://support.google.com/a/answer/7281227) instead.",
        "domain": "usageLimits",
        "reason": "storageQuotaExceeded"
      }
    ]
  }
}
```

## Root Cause

Service Accounts cannot upload files to **My Drive** because they don't have personal storage quota. Google requires using either:

1. **Shared Drives** (ONLY supported option)
2. **OAuth delegation** (more complex setup)

**Important:** Shared folders in My Drive are NOT supported, even when shared with the Service Account. Google Drive API treats them as My Drive storage and rejects Service Account uploads.

## Solution

### 1. Enhanced Resumable Upload Support

Updated `CreateResumableUploadURL` and `initiateResumableUpload` methods to:

- Add `supportsAllDrives=true` and `includeItemsFromAllDrives=true` query parameters
- Provide enhanced error handling for Service Account storage quota issues
- Include validation to prevent uploads to My Drive root

### 2. Validation Layer

Added `ValidateParentForServiceAccount` method that:

- Prevents empty parent folder IDs (which would default to My Drive root)
- Checks if parent folder is in a Shared Drive
- Checks if parent folder is shared with the Service Account (My Drive shared folders)
- Provides clear error messages with actionable guidance

### 3. Shared Drive and Folder Detection

Added helper methods:

- `IsSharedDrive` - Determine if a given ID is a Shared Drive
- `IsSharedWithMe` - Check if a folder is shared with the Service Account
- Enhanced validation to support both Shared Drives and shared My Drive folders

## Code Changes

### Key Files Modified

1. **`pkg/gdrive/service.go`**
   - Enhanced `CreateResumableUploadURL` with validation
   - Updated `initiateResumableUpload` with Shared Drive support
   - Added `ValidateParentForServiceAccount` method
   - Added `IsSharedDrive` helper method

2. **`pkg/gdrive/interfaces.go`**
   - Added `IsSharedDrive`, `IsSharedWithMe`, and `ValidateParentForServiceAccount` methods to `DriveClient` interface

3. **Tests**
   - Updated existing tests to handle validation
   - Added comprehensive test coverage for Shared Drive support

### Error Messages

The system now provides clear, actionable error messages:

```
// For empty parent folder ID
"parent folder ID is required for Service Account uploads - cannot upload to My Drive root"

// For Service Account storage quota
"service account storage quota exceeded - must use Shared Drives or OAuth delegation. Ensure parent folder is in a Shared Drive"

// For My Drive folder (even if shared)
"parent folder is in My Drive - Service Accounts cannot upload to My Drive folders, even when shared. Please use a Shared Drive instead"
```

## Usage Requirements

### For Upload Operations

1. **Always provide a parent folder ID** - never leave empty
2. **Use one of the supported folder types**:
   - Shared Drive root or folder within Shared Drive
   - My Drive folder that is shared with the Service Account
3. **Service Account must have access** to the target folder

### Example Valid Parent Folder IDs

```go
// ✅ Shared Drive root
parentFolderID := "0AKjqVk9XYZ123SharedDriveID"

// ✅ Folder within Shared Drive
parentFolderID := "1BLkrWl8YZ456FolderInSharedDrive"

// ❌ Empty (defaults to My Drive root)
parentFolderID := ""

// ❌ My Drive folder (even if shared with Service Account)
parentFolderID := "1CLmrXm9Z789MyDriveFolder"
```

**Critical:** Google Drive API does not support Service Account uploads to My Drive folders, regardless of sharing permissions.

## Testing

Run tests to verify Shared Drive support:

```bash
# Test Shared Drive functionality
go test -v ./pkg/gdrive/... -run "TestSharedDriveSupport"

# Test Service Account guidance
go test -v ./pkg/gdrive/... -run "TestServiceAccountGuidance"

# Test validation logic
go test -v ./pkg/gdrive/... -run "TestValidateParentForServiceAccount"
```

## Migration Guide

### For Existing Code

1. **Review parent folder IDs** - ensure they point to Shared Drive folders or shared My Drive folders
2. **Update error handling** - catch new validation errors
3. **Test uploads** - verify they work with supported folder types

### For New Implementations

1. **Use supported folder types** for Service Account uploads:
   - Shared Drives (recommended)
   - Shared My Drive folders (alternative)
2. **Implement proper error handling** for quota and permission issues
3. **Validate parent folder IDs** before upload attempts

## References

- [Google Drive Shared Drives Guide](https://developers.google.com/workspace/drive/api/guides/about-shareddrives)
- [OAuth Delegation Support](http://support.google.com/a/answer/7281227)
- [Service Account Limitations](https://developers.google.com/drive/api/guides/about-auth#service_accounts)
