# Event System Documentation

## Event Topics Overview

### 1. Client Events

#### `client.create` Event
- **Handler**: `pkg/handlers/sharepoint_client_create.go:50`
- **Endpoint**: `POST /internal/consume/sharepoint/client/crud`
- **Model**: `ConsumeClientReq`

**Event Data Structure**:
```json
{
  "topic": "client.create",
  "body": {
    "id": 123,
    "name": "Client Name",
    "short_name": "Short Name",
    "code": "CLIENT_CODE",
    "tenant_id": 1,
    "extra": {
      "current": {
        "name": "Current State Name", 
        "owners": [1, 2, 3],
        "owner_users": [
          {
            "id": 1,
            "email": "<EMAIL>",
            "name": "User Name",
            "avatar": "avatar_url"
          }
        ]
      },
      "old": {
        "name": "Old State Name",
        "owners": [1, 2],
        "owner_users": [...]
      }
    }
  }
}
```

**Current Processing (SharePoint)**:
- Creates client folder with format: `{short_name || name} - {code}`
- Creates matter parent folder inside client folder
- Manages permissions for owner users
- Stores mapping in `document_mapping` table

**Google Drive Integration**:
- Handled by `internal/service/gdrive/client_event_consumer.go`
- Endpoint: `POST /internal/consume/gdrive/client/crud`
- Calls `gdriveService.CreateClientFolder()` to create client folder
- Creates matter parent folder inside client folder using `gdriveService.CreateMatterParentFolder()`
- Manages permissions for owner users
- Stores mapping in `document_mapping` table with `provider_type = "gdrive"`

#### `client.update` Event
- **Handler**: `pkg/handlers/sharepoint_client_update.go`
- **Endpoint**: `POST /internal/consume/sharepoint/client/crud`
- **Model**: `ConsumeClientReq`
- **Similar structure to client.create**
- **Processing**: Updates folder name and permissions

**Event Data Structure**:
```json
{
  "topic": "client.update",
  "body": {
    "id": 123,
    "name": "Updated Client Name",
    "short_name": "Updated Short Name",
    "code": "CLIENT_CODE",
    "tenant_id": 1,
    "actor_id": 789,
    "no_notify": false,
    "extra": {
      "current": {
        "name": "Updated Client Name", 
        "owners": [1, 2, 3, 4],
        "owner_users": [
          {
            "id": 1,
            "email": "<EMAIL>",
            "name": "User Name",
            "avatar": "avatar_url"
          },
          {
            "id": 4,
            "email": "<EMAIL>",
            "name": "New User",
            "avatar": "avatar_url"
          }
        ]
      },
      "old": {
        "name": "Original Client Name",
        "owners": [1, 2, 3],
        "owner_users": [...]
      }
    }
  }
}
```

**Google Drive Integration**:
- ⚠️ **DEPRECATED**: `POST /internal/consume/gdrive/client/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/client/updated` - Coordinated processing
- Handled by `internal/service/gdrive/client_event_consumer.go`
- Updates folder name based on client name/code changes
- Updates permissions based on owner changes
- Maintains existing folder structure and mappings
- Processes changes in ownership and permissions
- Updates document mappings as needed

### 2. Matter Events

#### `matter.create` Event
- **Handler**: `pkg/handlers/sharepoint.go:609`
- ⚠️ **DEPRECATED**: `POST /internal/consume/sharepoint/matter/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/matter/created` - Coordinated processing
- **Model**: `ConsumeMatterReq`

**Event Data Structure**:
```json
{
  "topic": "matter.create",
  "body": {
    "id": 456,
    "client_id": 123,
    "name": "Matter Name",
    "code": "MATTER_CODE",
    "tenant_id": 1,
    "extra": {
      "current": {
        "name": "Current State Name",
        "owners": [1, 2, 3],
        "owner_users": [
          {
            "id": 1,
            "email": "<EMAIL>", 
            "name": "User Name",
            "avatar": "avatar_url"
          }
        ]
      },
      "old": {
        "name": "Old State Name",
        "owners": [1, 2],
        "owner_users": [...]
      }
    }
  }
}
```

**Current Processing (SharePoint)**:
- Creates matter folder with format: `{name} - {code}`
- Creates folder inside client's matter parent folder
- Manages permissions for owner users
- Stores mapping in `document_mapping` table

**Google Drive Integration**:
- ⚠️ **DEPRECATED**: `POST /internal/consume/gdrive/matter/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/matter/created` - Coordinated processing
- Handled by `internal/service/gdrive/matter_event_consumer.go`
- Calls `gdriveService.CreateMatterFolder()` to create matter folder
- Creates folder inside client's matter parent folder
- Manages permissions for owner users
- Stores mapping in `document_mapping` table with `provider_type = "gdrive"`

#### `matter.update` Event
- **Handler**: `pkg/handlers/sharepoint_matter_update.go`
- **Endpoint**: `POST /internal/consume/sharepoint/matter/crud`
- **Model**: `ConsumeMatterReq`
- **Similar structure to matter.create**
- **Processing**: Updates folder name and permissions

**Event Data Structure**:
```json
{
  "topic": "matter.update",
  "body": {
    "id": 456,
    "client_id": 123,
    "name": "Matter Name",
    "code": "MATTER_CODE",
    "tenant_id": 1,
    "actor_id": 789,
    "no_notify": false,
    "extra": {
      "current": {
        "name": "Updated Matter Name",
        "owners": [1, 2, 3],
        "owner_users": [
          {
            "id": 1,
            "email": "<EMAIL>", 
            "name": "User Name",
            "avatar": "avatar_url"
          }
        ]
      },
      "old": {
        "name": "Original Matter Name",
        "owners": [1, 2],
        "owner_users": [...]
      }
    }
  }
}
```

**Google Drive Integration**:
- ⚠️ **DEPRECATED**: `POST /internal/consume/gdrive/matter/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/matter/updated` - Coordinated processing
- Handled by `internal/service/gdrive/matter_event_consumer.go`
- Updates folder name and permissions based on matter changes
- Maintains existing folder structure and mappings
- Processes changes in ownership and permissions
- Updates document mappings as needed

### 3. Document Events

#### `docman.crud.document` Topic
- **Handler**: `pkg/handlers/document.go:352` - `ConsumeCrud()`
- **Endpoint**: `POST /internal/consume/documents/crud`
- **Model**: `ConsumeDocumentReq`

**Event Data Structure**:
```json
{
  "topic": "docman.crud.document",
  "body": {
    "action": 1, // 1=CREATE, 2=UPDATE, 3=DELETE
    "data": {
      "object_type": 1,     // 1=client, 2=staff, 3=matter
      "object_id": 123,
      "sub_object_id": 456,
      "name": "Document Name",
      "doc_type": 1,        // 1=folder, 2=file
      "created_user": 789,
      "tenant_id": 1
    }
  }
}
```

**Current Processing**:
- **CREATE/UPDATE (action 1,2)**: Creates or updates document record in database
- **DELETE (action 3)**: Deletes document record from database
- Does NOT handle physical file/folder operations
- Operates on `documents` table only

### 4. Billing Events

#### `billing.cost.attach` / `billing.cost.deattach`
- **Handlers**: `ConsumerBillingCostAttach()`, `ConsumerBillingCostDeattach()`
- **Purpose**: Attach/detach cost files to billing records

#### `billing.payment.attach` / `billing.payment.deattach` 
- **Handlers**: `ConsumerBillingPaymentAttach()`, `ConsumerBillingPaymentDeattach()`
- **Purpose**: Attach/detach payment files to billing records

## Event Consumer Infrastructure

### Transport Layer
- **File**: `pkg/transport/consumer.go`
- **Interface**: `Consumer`
- **Implementation**: `consumerImpl`
- **URL**: Configured via `CONSUMER_URL` constant

### Consumer Methods
- `Emit(ctx, topic, body)`: Synchronous event emission
- `SafeEmit(ctx, topic, body)`: Asynchronous event emission with error logging

### Event Message Format
```go
type ConsumerMessage struct {
    Topic string      `json:"topic"`
    Body  interface{} `json:"body"`
}
```

## Folder Naming Conventions

### Current SharePoint Implementation

#### Client Folder Naming
```go
// Logic from sharepoint_client_create.go:32-38
var folderName string
if body.Body.ShortName != "" {
    folderName = body.Body.ShortName
} else {
    folderName = body.Body.Name
}
clientFolder := fmt.Sprintf("%s - %s", folderName, body.Body.Code)
```
**Format**: `{ShortName || Name} - {Code}`
**Example**: `ACME Corp - ACME001`

#### Matter Folder Naming
```go
// Logic from sharepoint.go:605 and sharepoint_matter_create.go
matterFolder := fmt.Sprintf("%s - %s", body.Body.Name, body.Body.Code)
```
**Format**: `{Name} - {Code}`
**Example**: `Contract Dispute - MAT001`

### Google Drive Implementation

#### Client Folder Naming
- Uses the same naming convention as SharePoint
- **Format**: `{ShortName || Name} - {Code}`
- **Example**: `ACME Corp - ACME001`

#### Matter Parent Folder Naming
- Uses a fixed name: `Matters`
- Created inside each client folder

#### Matter Folder Naming
- Uses the same naming convention as SharePoint
- **Format**: `{Name} - {Code}`
- **Example**: `Contract Dispute - MAT001`

## Implemented Google Drive Event Consumers

### 1. Google Drive Client Event Consumer ⚠️ **DEPRECATED**
- **File**: `internal/service/gdrive/client_event_consumer.go`
- ⚠️ **DEPRECATED**: `POST /internal/consume/gdrive/client/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/client/created` and `/internal/consume/autodoc/client/updated` - Coordinated processing
- **Handler**: Processes `client.create` and `client.update` events
- **Actions**:
  - Creates client folder with proper naming
  - Creates matter parent folder inside client folder
  - Manages permissions for owner users
  - Stores mappings in `document_mapping` table

### 2. Google Drive Matter Event Consumer ⚠️ **DEPRECATED**
- **File**: `internal/service/gdrive/matter_event_consumer.go`
- ⚠️ **DEPRECATED**: `POST /internal/consume/gdrive/matter/crud` - Direct provider endpoint (may cause race conditions)
- ✅ **RECOMMENDED**: `POST /internal/consume/autodoc/matter/created` and `/internal/consume/autodoc/matter/updated` - Coordinated processing
- **Handler**: Processes `matter.create` and `matter.update` events
- **Actions**:
  - Creates matter folder inside client's matter parent folder
  - Manages permissions for owner users
  - Stores mappings in `document_mapping` table

### 3. Event Registration
- **Location**: `cmd/docman/main.go`
- **Routes**:
  ```go
  // ⚠️ DEPRECATED - Direct provider endpoints (may cause race conditions)
  app.Router().POST("/internal/consume/gdrive/client/crud", w(gdriveHandler.ConsumeGoogleDriveClientCrud))
  app.Router().POST("/internal/consume/gdrive/matter/crud", w(gdriveHandler.ConsumeGoogleDriveMatterCrud))

  // ✅ RECOMMENDED - Coordinated AutoDoc endpoints (prevents race conditions)
  app.Router().POST("/internal/consume/autodoc/client/created", w(autoDocHandler.ConsumeClientCreated))
  app.Router().POST("/internal/consume/autodoc/client/updated", w(autoDocHandler.ConsumeClientUpdated))
  app.Router().POST("/internal/consume/autodoc/matter/created", w(autoDocHandler.ConsumeMatterCreated))
  app.Router().POST("/internal/consume/autodoc/matter/updated", w(autoDocHandler.ConsumeMatterUpdated))
  ```

## Event Flow Architecture

```
External Service -> Consumer/Event System -> docman handlers -> Business Logic
                                         -> SharePoint handlers
                                         -> Google Drive handlers
```

## Google Drive Configuration Check Implementation

All Google Drive operations check if the integration is enabled before proceeding:

1. **Configuration Check**: Each operation verifies `gdrive_config.enabled = true` for the tenant
2. **Skip if Disabled**: If Google Drive is disabled, operations are skipped gracefully
3. **Logging**: Operations log when skipped due to disabled integration

**Implementation Pattern**:
```go
func (s *service) CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*CreateClientFolderResponse, error) {
    // Check if Google Drive is enabled first
    config, err := s.getGDriveConfig(ctx, req.TenantID)
    if err != nil || !config.Enabled {
        log.Info("Google Drive operation skipped due to disabled integration", 
            "tenant_id", req.TenantID, 
            "operation", "CreateClientFolder")
        return nil, nil // Return gracefully
    }
    
    // Proceed with Google Drive operations
    // ... implementation
}
```

## Folder Creation Sequence and Dependencies

The Google Drive folder structure has specific dependencies that must be respected:

1. **Client Folder** must be created first
   - Created by `client.create` event
   - Handled by `ClientEventConsumer.HandleClientCreated`
   - Creates the client's root folder

2. **Matter Parent Folder** must be created second
   - Named "Matters" by default
   - Created inside the client folder
   - Created by `client.create` event in the updated implementation
   - Can also be created manually via `CreateMatterParentFolder` API

3. **Matter Folders** must be created last
   - Created by `matter.create` event
   - Handled by `MatterEventConsumer.HandleMatterCreated`
   - Creates a matter-specific folder inside the "Matters" parent folder
   - **Requires** both client folder and matter parent folder to exist first

**Important:** When processing `matter.create` events, the system now automatically creates the matter parent folder if it doesn't exist. However, the client folder must already exist (created by a previous `client.create` event).

## 🚀 **Migration Guide: Direct Endpoints → Coordinated AutoDoc**

### **Why Migrate?**
- ✅ **Prevents race conditions** between folder creation and autodoc rules
- ✅ **Coordinates all providers** (GDrive, SharePoint, Internal) before autodoc execution
- ✅ **Automatic operation tracking** in database
- ✅ **Better error handling** across all providers

### **Migration Mapping**

| ❌ **Deprecated Endpoint** | ✅ **New Unified Endpoint** | **Event Type** |
|---------------------------|----------------------------|----------------|
| `POST /internal/consume/gdrive/client/crud` | `POST /internal/consume/autodoc/client/events` | client.create/client.update |
| `POST /internal/consume/sharepoint/client/crud` | `POST /internal/consume/autodoc/client/events` | client.create/client.update |
| `POST /internal/consume/gdrive/matter/crud` | `POST /internal/consume/autodoc/matter/events` | matter.create/matter.update |
| `POST /internal/consume/sharepoint/matter/crud` | `POST /internal/consume/autodoc/matter/events` | matter.create/matter.update |

**Individual Endpoints (Backward Compatibility):**

| ⚠️ **Individual Endpoint** | ✅ **Unified Endpoint** | **Event Type** |
|---------------------------|-------------------------|----------------|
| `POST /internal/consume/autodoc/client/created` | `POST /internal/consume/autodoc/client/events` | client.create |
| `POST /internal/consume/autodoc/client/updated` | `POST /internal/consume/autodoc/client/events` | client.update |
| `POST /internal/consume/autodoc/matter/created` | `POST /internal/consume/autodoc/matter/events` | matter.create |
| `POST /internal/consume/autodoc/matter/updated` | `POST /internal/consume/autodoc/matter/events` | matter.update |

### **Request Format Changes**

**Old Format (Direct Provider):**
```json
{
  "topic": "client.create",
  "body": {
    "id": 123,
    "name": "Test Client",
    "tenant_id": 1
  }
}
```

**New Format (Unified AutoDoc):**
```json
{
  "topic": "client.create",  // or "client.update"
  "body": {
    "id": 123,
    "name": "Test Client",
    "tenant_id": 1
  }
}
```

**Individual Endpoints Format (Backward Compatibility):**
```json
{
  "body": {
    "id": 123,
    "name": "Test Client",
    "tenant_id": 1
  }
}
```

### **Environment Configuration**

To control deprecation behavior:

```bash
# Phase 1: Show warnings but allow direct calls
COORDINATION_DEPRECATE_DIRECT_CALLS=false

# Phase 2: Block direct calls and force migration
COORDINATION_DEPRECATE_DIRECT_CALLS=true
```

### **Migration Steps**

1. **Update calling services** to use new AutoDoc endpoints
2. **Test coordinated processing** with your tenant
3. **Enable deprecation warnings** (`COORDINATION_DEPRECATE_DIRECT_CALLS=false`)
4. **Monitor logs** for direct endpoint usage
5. **Block direct calls** (`COORDINATION_DEPRECATE_DIRECT_CALLS=true`) after migration complete

## Important Notes

1. **Idempotency**: All event consumers handle duplicate events gracefully
2. **Error Handling**: Failed events are logged with appropriate context
3. **Provider Selection**: Events are processed by appropriate provider based on tenant configuration
4. **Backwards Compatibility**: Google Drive consumers do not interfere with existing SharePoint functionality
5. **Permissions**: Google Drive permission handling mirrors SharePoint permission logic
6. **Configuration Check**: All Google Drive operations verify `gdrive_config.enabled = true` before proceeding
7. **Folder Dependencies**: Matter folder creation requires client folder and matter parent folder to exist
 