# Google Drive Integration Technical Design

## Overview

This document outlines the technical architecture and implementation details of the Google Drive integration for the docman service. The integration provides a comprehensive document management solution that leverages Google Drive as a storage backend while maintaining compatibility with SharePoint migration patterns.

## System Architecture

The Google Drive integration follows a layered architecture pattern:

```
┌─────────────────────────────────────────────────────────────────┐
│                        API Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Setup Endpoints │  │ Document CRUD   │  │ Search Endpoints│  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│                      Service Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  FolderService  │  │ DocumentService │  │PermissionService│  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│                    Integration Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Google Drive   │  │ Rate Limiter    │  │ Event Consumers │  │
│  │  Client         │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│                    Persistence Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Document       │  │ DocumentMapping │  │ DocumentSetting │  │
│  │  Repository     │  │ Repository      │  │ Repository      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Google Drive Client (`pkg/gdrive`)

The `pkg/gdrive` package provides a clean abstraction over the Google Drive API:

```go
// DriveClient interface defines operations for Google Drive
type DriveClient interface {
    // Folder operations
    CreateFolder(ctx context.Context, name, parentID string) (*File, error)
    RenameFile(ctx context.Context, fileID, newName string) (*File, error)
    GetFileInfo(ctx context.Context, fileID string) (*File, error)
    DeleteFile(ctx context.Context, fileID string) error
    
    // Search operations
    FindFolderByName(ctx context.Context, name, parentID string) (*File, error)
    ListFilesInFolder(ctx context.Context, folderID string, opts *PaginationOptions) ([]*File, string, error)
    SearchFiles(ctx context.Context, query string, opts *PaginationOptions) ([]*File, string, error)
    
    // Upload operations
    CreateUploadURL(ctx context.Context, parentID, fileName string) (string, string, error)
    
    // Permission operations
    GetPermissions(ctx context.Context, fileID string) ([]*Permission, error)
    AddPermission(ctx context.Context, fileID, email, role string) (*Permission, error)
    RemovePermission(ctx context.Context, fileID, permissionID string) error
}
```

### 2. Service Layer

#### FolderService

Handles folder creation and management for clients and matters:

```go
// FolderService interface defines operations for folder management
type FolderService interface {
    CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*FolderResponse, error)
    CreateMatterFolder(ctx context.Context, req *CreateMatterFolderRequest) (*FolderResponse, error)
    RenameMatterFolder(ctx context.Context, req *RenameMatterFolderRequest) (*FolderResponse, error)
    GetFolderService() FolderService
}
```

#### DocumentService

Handles document CRUD operations:

```go
// DocumentService interface defines operations for document management
type DocumentService interface {
    CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error)
    GetDocument(ctx context.Context, driveFileID string) (*DocumentResponse, error)
    UpdateDocument(ctx context.Context, driveFileID string, req *UpdateDocumentRequest) (*DocumentResponse, error)
    DeleteDocument(ctx context.Context, driveFileID string) error
    ListDocuments(ctx context.Context, req *ListDocumentsRequest) (*DocumentListResponse, error)
    SearchDocuments(ctx context.Context, req *SearchDocumentsRequest) (*DocumentListResponse, error)
    UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error)
}
```

#### PermissionService

Handles permission synchronization:

```go
// PermissionService interface defines operations for permission management
type PermissionService interface {
    SyncGoogleDrivePermissions(ctx context.Context, req *SyncPermissionsRequest) error
    GetPermissions(ctx context.Context, driveFileID string) ([]*Permission, error)
}
```

### 3. Event Consumers

Event-driven architecture for automatic folder creation:

```go
// ClientEventConsumer handles client lifecycle events
type ClientEventConsumer interface {
    HandleClientCreated(ctx context.Context, event *ClientEvent) error
    HandleClientUpdated(ctx context.Context, event *ClientEvent) error
}

// MatterEventConsumer handles matter lifecycle events
type MatterEventConsumer interface {
    HandleMatterCreated(ctx context.Context, event *MatterEvent) error
    HandleMatterUpdated(ctx context.Context, event *MatterEvent) error
}
```

## Data Models

### DocumentSetting

Stores Google Drive configuration for tenants:

```go
// GDriveConfig represents the JSON configuration for Google Drive integration
type GDriveConfig struct {
    Enabled       bool      `json:"enabled"`
    RootID        string    `json:"root_id"`
    DriveID       string    `json:"drive_id,omitempty"`
    ResourceType  string    `json:"resource_type,omitempty"`
    PathConfig    PathConfig `json:"path_config,omitempty"`
    PermissionConfig PermissionConfig `json:"permission_config,omitempty"`
    CreatedAt     time.Time `json:"created_at,omitempty"`
    UpdatedAt     time.Time `json:"updated_at,omitempty"`
}

// PathConfig represents folder path templates
type PathConfig struct {
    ClientFolderPath string `json:"client_folder_path,omitempty"`
    MatterFolderPath string `json:"matter_folder_path,omitempty"`
    CaseFormat      string `json:"case_format,omitempty"`
    MaxLength       int    `json:"max_length,omitempty"`
    InvalidCharReplace string `json:"invalid_char_replace,omitempty"`
}

// PermissionConfig represents permission settings
type PermissionConfig struct {
    DefaultRole  string `json:"default_role,omitempty"`
    SyncOnCreate bool   `json:"sync_on_create,omitempty"`
    SyncOnUpdate bool   `json:"sync_on_update,omitempty"`
    RetryCount   int    `json:"retry_count,omitempty"`
}
```

### DocumentMapping

Maps internal document IDs to Google Drive file IDs:

```go
// DocumentMapping represents the mapping between internal IDs and Google Drive IDs
type DocumentMapping struct {
    ID        uint64    `json:"id" gorm:"primaryKey"`
    TenantID  uint64    `json:"tenant_id" gorm:"index"`
    Type      string    `json:"type" gorm:"index"` // "client", "matter"
    ObjectID  string    `json:"object_id" gorm:"index"`
    DriveID   string    `json:"drive_id" gorm:"index"`
    Provider  string    `json:"provider" gorm:"default:google"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### DocumentPermissionMapping

Tracks permission mappings for Google Drive folders:

```go
// DocumentPermissionMapping represents permission mappings
type DocumentPermissionMapping struct {
    ID           uint64    `json:"id" gorm:"primaryKey"`
    TenantID     uint64    `json:"tenant_id" gorm:"index"`
    DriveID      string    `json:"drive_id" gorm:"index"`
    Email        string    `json:"email" gorm:"index"`
    PermissionID string    `json:"permission_id"`
    Role         string    `json:"role"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

## Key Implementation Patterns

### 1. Dependency Injection

The system uses dependency injection for clean component separation:

```go
// Service initialization with dependency injection
func NewService(
    documentRepo repositories.DocumentRepository,
    mappingRepo repositories.DocumentMappingRepository,
    settingRepo repositories.DocumentSettingRepository,
    driveClient gdrive.DriveClient,
) Service {
    folderService := NewFolderService(mappingRepo, settingRepo, driveClient)
    documentService := NewDocumentService(documentRepo, mappingRepo, driveClient)
    permissionService := NewPermissionService(mappingRepo, driveClient)
    
    return &service{
        folderService:     folderService,
        documentService:   documentService,
        permissionService: permissionService,
        settingRepo:       settingRepo,
    }
}
```

### 2. Event-Driven Architecture

The system uses an event-driven approach for folder creation:

```
Client/Matter Event → Event Consumer → Folder Creation → Permission Sync
```

Event consumers handle client and matter lifecycle events:

```go
func (c *clientEventConsumer) HandleClientCreated(ctx context.Context, event *ClientEvent) error {
    // 1. Extract client data from event
    // 2. Get Google Drive configuration
    // 3. Create client folder
    // 4. Sync permissions if enabled
    // 5. Store mapping in database
}
```

### 3. Template-Based Naming

Folder names use configurable templates with variable substitution:

```go
// Template examples
clientFolderTemplate := "{{client_name}} - {{client_code}}"
matterFolderTemplate := "{{matter_name}} - {{matter_number}}"

// Variable substitution
func processTemplate(template string, data map[string]string) string {
    // Replace variables with actual values
    // Handle fallbacks: {short_name|name}
    // Apply formatting: case, length limits
    // Replace invalid characters
}
```

### 4. Rate Limiting and Quota Management

The system implements distributed rate limiting to prevent Google Drive API quota exhaustion:

```go
// Distributed rate limiter using Redis
type DistributedRateLimiter interface {
    // Wait blocks until the request can proceed
    Wait(ctx context.Context, key string) error

    // TryAcquire attempts to acquire a permit without blocking
    TryAcquire(ctx context.Context, key string) (bool, error)

    // GetStats returns current limiter statistics
    GetStats(ctx context.Context, key string) (*LimiterStats, error)

    // Close releases resources
    Close() error
}
```

Implementation uses a Redis-based token bucket algorithm:

```go
// Redis token bucket implementation
type RedisTokenBucket struct {
    client      redis.Cmdable
    capacity    int64
    refillRate  time.Duration
    keyPrefix   string
    nodeID      string

    // Lua scripts for atomic operations
    acquireScript *redis.Script
    refillScript  *redis.Script
}
```

Google Drive API quota limits:
- **Queries per 100 seconds per user**: 1,000
- **Queries per 100 seconds**: 10,000
- **Queries per day**: 1,000,000,000

### 5. Error Handling Strategy

The system uses structured error types with consistent error handling:

```go
// DocumentServiceError represents a structured error
type DocumentServiceError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

// Error codes
const (
    ErrCodeDocumentNotFound    = "DOCUMENT_NOT_FOUND"
    ErrCodeInvalidRequest      = "INVALID_REQUEST"
    ErrCodeDriveAPIError       = "DRIVE_API_ERROR"
    ErrCodeDatabaseError       = "DATABASE_ERROR"
    ErrCodePermissionDenied    = "PERMISSION_DENIED"
    ErrCodeQuotaExceeded       = "QUOTA_EXCEEDED"
)
```

Error handling patterns:
- Wrap errors with context using `fmt.Errorf("operation failed: %w", err)`
- Return structured errors for API consumption
- Log errors with context using `log.WithError(err).Error("operation failed")`
- Use sentinel errors for common error conditions

### 6. Logging Standards

The system uses structured logging with context:

```go
// Standard logging pattern
log := logger.WithCtx(ctx, "MethodName")
log.Infof("Operation starting param=%s", value)

// Error logging
if err != nil {
    log.WithError(err).Errorf("Operation failed param=%s", value)
    return nil, err
}

// Success logging
log.Infof("Operation completed successfully result=%s", result)
```

Key logging principles:
- Use `logger.WithCtx(ctx, "method_name")` for context-aware logging
- Include relevant fields for debugging
- Use scalar value logging pattern: `log.Infof("message key=%s", value)`
- Log at appropriate levels (debug, info, warn, error)
- Include operation context in all log messages

## Security Considerations

### 1. Service Account Authentication

The system uses a Google Service Account for authentication:

```go
// Service account credentials stored as base64-encoded JSON
// Environment variable: GOOGLE_CREDENTIALS_BASE64
func getCredentialsFromEnv() ([]byte, error) {
    credBase64 := os.Getenv("GOOGLE_CREDENTIALS_BASE64")
    if credBase64 == "" {
        return nil, errors.New("GOOGLE_CREDENTIALS_BASE64 environment variable not set")
    }

    return base64.StdEncoding.DecodeString(credBase64)
}
```

### 2. Input Validation

Comprehensive input validation for all operations:

```go
// Document name validation
func validateDocumentName(name string) error {
    if name == "" {
        return errors.New("document name is required")
    }

    if len(name) > 255 {
        return errors.New("document name exceeds maximum length of 255 characters")
    }

    // Check for invalid characters
    invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", "\x00"}
    for _, char := range invalidChars {
        if strings.Contains(name, char) {
            return fmt.Errorf("document name contains invalid character: %s", char)
        }
    }

    // Check for path traversal attempts
    traversalPatterns := []string{"../", "..\\", "..", "~/"}
    for _, pattern := range traversalPatterns {
        if strings.Contains(name, pattern) {
            return errors.New("document name contains path traversal pattern")
        }
    }

    return nil
}
```

### 3. Permission Management

Secure permission handling for Google Drive folders:

```go
// Permission sync with retry logic
func (s *permissionService) SyncGoogleDrivePermissions(ctx context.Context, req *SyncPermissionsRequest) error {
    // 1. Get current permissions from Google Drive
    // 2. Calculate permission diff (add/remove)
    // 3. Apply permission changes with retry logic
    // 4. Update permission mappings in database
}
```

## Configuration Management

The system uses a JSON-based configuration approach stored in the database:

```go
// Single key in DocumentSetting table: "gdrive_config"
// JSON structure for configuration
{
  "enabled": true,
  "root_id": "1aBcDeFgHiJkLmN",
  "drive_id": "0aBcDeFgHiJkLmN",
  "resource_type": "shared_drive",
  "path_config": {
    "client_folder_path": "{short_name|name} - {code}",
    "matter_folder_path": "{name} - {code}",
    "case_format": "original",
    "max_length": 255,
    "invalid_char_replace": "_"
  },
  "permission_config": {
    "default_role": "writer",
    "sync_on_create": true,
    "sync_on_update": true,
    "retry_count": 3
  }
}
```

Benefits of JSON configuration:
- **Atomic Operations**: Single DB call instead of multiple separate calls
- **Type Safety**: Structured configuration with helper methods
- **Extensibility**: Easy to add new fields without DB schema changes
- **Consistency**: Single source of truth for configuration

## Implementation Decisions

### 1. SharePoint Compatibility

The API is designed to be 100% compatible with SharePoint V3 API patterns:

- **Request/Response Formats**: Identical structure to SharePoint API
- **Error Handling**: Same error codes and messages
- **Upload Pattern**: URL generation for client-side uploads
- **Pagination**: Compatible pagination tokens and metadata
- **Search**: Same search query format and behavior

### 2. No Server-Side File Handling

Based on SharePoint compatibility analysis:

1. **Client-Side Upload**: Generate upload URL only, client uploads directly to Google Drive
2. **No Database Persistence**: No document records in database, only mappings
3. **Provider-Only Approach**: All metadata comes from Google Drive API
4. **Simple Operations**: No streaming, progress tracking, or complex file operations

### 3. Folder Hierarchy Maintenance

- Use DocumentMapping to track folder relationships
- When ParentID is empty, validate that ObjectType and ObjectID are provided
- Query DocumentMapping repository to find the corresponding Google Drive folder ID
- Maintain folder hierarchy without database persistence

### 4. Non-Blocking Permission Sync

- Folder operations continue even if permission sync fails
- Permission sync is configurable (can be enabled/disabled)
- Comprehensive logging for debugging permission issues
- Retry logic with exponential backoff for permission operations
