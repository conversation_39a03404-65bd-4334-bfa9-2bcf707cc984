Vì google không cho phép chúng ta sử dụng resumable URL để upload từ frontend, nê<PERSON> chúng ta sẽ phải upload file từ backend.
<PERSON><PERSON><PERSON> ta cần build proxy để upload file theo 2-steps pattern.
1. Generate upload URL (upload token), store thông tin parent folder, filename vào DB. Chúng ta đã có endpoint cho operation này (POST /v3/gdrive/documents/upload), hãy tận dụng lại endpoint này và return về client chính xác như body hiện tại
2. Upload file content to the generated URL, sử dụng gdrive service and content được stream từ request body
<PERSON>ãy giúp a lập plan chi tiết cho implement feature này
