# Google Drive Permission Removal Implementation Plan

**Document Version**: 1.2
**Updated**: 2025-07-17 22:02:27 +07
**Author**: Development Team
**Status**: Simplified Approach - Approved

## Problem Statement

Google Drive has a hierarchical permission inheritance issue where removing a user's permission from a client folder automatically removes their access to all nested matter folders, even when the user should retain access to specific matters.

### Current Behavior
1. User has access to Client A folder (inherited by all matters under Client A)
2. Use<PERSON> also has explicit access to Matter X under Client A
3. When removing user from Client A, Google Drive removes access to Matter X as well
4. This breaks the intended permission model where matter-specific access should be preserved

## Solution Overview - SIMPLIFIED APPROACH

Implement a simple "permission preservation" mechanism using a single `ClientID` field:

1. **Schema Enhancement**: Add `ClientID` field to `DocumentPermissionMapping` 
2. **Permission Tracking**: Set `ClientID > 0` for matter permissions to track parent client
3. **Preservation Logic**: After client permission removal, re-grant matter permissions

### Database Schema Changes - SIMPLIFIED

Add single field to existing `DocumentPermissionMapping` table:

```go
type DocumentPermissionMapping struct {
    // Existing fields
    ID           uint64    `json:"id" gorm:"primary_key"`
    TenantID     uint64    `json:"tenant_id" gorm:"index:idx_tenant_document_email"`
    DocumentID   string    `json:"document_id" gorm:"index:idx_tenant_document_email"`
    Email        string    `json:"email" gorm:"index:idx_tenant_document_email"`
    Role         string    `json:"role"`
    PermissionID string    `json:"permission_id"`
    Provider     string    `json:"provider"`
    
    // NEW FIELD - Simple approach
    ClientID     uint64    `json:"client_id" gorm:"index"` // Parent client ID for matter permissions only (0 for client permissions)
    
    // Existing timestamp fields
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    DeletedAt    *time.Time `json:"deleted_at" sql:"index"`
}
```

### Implementation Flow

1. **Permission Creation**:
   - When creating **matter permissions**, set `ClientID` to parent client ID
   - When creating **client permissions**, leave `ClientID` as 0 (no parent tracking needed)
2. **Permission Removal**:
   - Remove client permission normally
   - Query `DocumentPermissionMapping` for `ClientID = clientID AND Email = email`
   - Re-grant permissions to all matching matter folders
3. **Preservation**: User retains access to specific matters after client removal

This approach provides clean separation: only matter permissions track their parent client relationship.

## Simplified Implementation Plan

### Phase 1: Database Migration (0.5 day)

1. Add `ClientID` field to `DocumentPermissionMapping` model:

```go
// In pkg/repositories/migrate.go
func addClientIDToPermissionMapping(db *gorm.DB) error {
    return db.AutoMigrate(&model.DocumentPermissionMapping{}).Error
}
```

### Phase 2: Update Permission Creation Logic (1 day)

Modify the permission creation logic to store client association:

```go
// In internal/service/gdrive/service.go
func (s *service) ShareDocument(ctx context.Context, req *ShareDocumentRequest) (*ShareDocumentResponse, error) {
    // Existing permission creation code...
    
    // New logic to determine if this is a matter folder and store client association
    var clientID uint64
    
    // Check if this is a matter folder by querying document mapping
    mapping, err := s.documentMappingRepo.FindOne(ctx, &model.Query{
        Filters: []*model.Filter{
            model.NewFilterE("drive_id", req.DriveFileID),
            model.NewFilterE("tenant_id", req.TenantID),
        },
    })
    
    if err == nil && mapping != nil {
        if mapping.Type == model.DocTypeMatter {
            // For matter folders, use ParentID which is the ClientID
            clientID = mapping.ParentID
        }
        // For client folders, leave clientID as 0 (don't store parent relationship)
    }
    
    // Create permission mapping with additional metadata
    permissionMapping := &model.DocumentPermissionMapping{
        TenantID:     req.TenantID,
        DocumentID:   req.DriveFileID,
        Email:        req.Email,
        Role:         req.Role,
        PermissionID: permission.Id,
        Provider:     model.DocProviderGoogle,
        ClientID:     clientID, // Store client ID
    }
    
    // Save the permission mapping
    err = s.permissionMappingRepo.Create(ctx, permissionMapping)
    
    // Rest of existing code...
}

// Helper function to get client ID for a matter from document mapping
func (s *service) getClientIDForMatter(ctx context.Context, matterID uint64, tenantID uint64) (uint64, error) {
    // Get matter document mapping which already contains ClientID
    mapping, err := s.documentMappingRepo.FirstObjectMapping(
        ctx,
        model.DocTypeMatter,
        model.DocProviderGoogle,
        tenantID,
        matterID,
        0, // parentID not needed for this query
    )
    if err != nil {
        return 0, fmt.Errorf("failed to get matter mapping: %w", err)
    }

    // Return the ParentID which is the ClientID for matter folders
    return mapping.ParentID, nil
}
```

### Phase 3: Implement Permission Removal Logic (1 day)

Update the permission removal logic to check for and preserve matter folder access:

```go
// In internal/service/gdrive/service.go
func (s *service) RemovePermission(ctx context.Context, req *RemovePermissionRequest) error {
    log := logger.WithCtx(ctx, "RemovePermission")
    
    // Check if this is a client folder
    isClientFolder := false
    var clientID uint64
    
    // Query document mapping to determine document type
    docMapping, err := s.documentMappingRepo.FindOne(ctx, &model.Query{
        Filters: []*model.Filter{
            model.NewFilterE("drive_id", req.DriveFileID),
            model.NewFilterE("tenant_id", req.TenantID),
        },
    })
    
    if err == nil && docMapping != nil {
        if docMapping.Type == model.DocTypeClient {
            isClientFolder = true
            clientID = docMapping.ObjectID
            log.Infof("Removing permission from client folder client_id=%d", clientID)
        }
    }
    
    // If this is a client folder, check for matter permissions that need preservation
    var mattersToReshare []*model.DocumentPermissionMapping
    
    if isClientFolder {
        // Find all permission mappings for this email with this client ID
        mattersToReshare, err = s.permissionMappingRepo.Find(ctx, &model.Query{
            Filters: []*model.Filter{
                model.NewFilterE("email", req.Email),
                model.NewFilterE("tenant_id", req.TenantID),
                model.NewFilterE("client_id", clientID),
            },
        })
        
        if err != nil {
            log.WithError(err).Error("Failed to find matter permissions")
            // Continue with removal even if query fails
        }
    }
    
    // Remove the permission from Google Drive
    err = s.driveClient.RemovePermission(req.DriveFileID, req.PermissionID)
    if err != nil {
        log.WithError(err).Error("Failed to remove permission from Google Drive")
        return err
    }
    
    // Delete the permission mapping
    err = s.permissionMappingRepo.Delete(ctx, &model.Query{
        Filters: []*model.Filter{
            model.NewFilterE("document_id", req.DriveFileID),
            model.NewFilterE("email", req.Email),
            model.NewFilterE("tenant_id", req.TenantID),
        },
    })
    if err != nil {
        log.WithError(err).Error("Failed to delete permission mapping")
        // Continue execution even if mapping deletion fails
    }
    
    // If there are matters to reshare, do it
    if len(mattersToReshare) > 0 {
        log.Infof("Found %d matters that need to be reshared for email=%s", len(mattersToReshare), req.Email)
        err = s.reshareMattersForUser(ctx, mattersToReshare, req.Email, req.TenantID)
        if err != nil {
            log.WithError(err).Error("Failed to reshare matter folders")
            // Continue execution even if resharing fails
        }
    }
    
    log.Info("Permission removed successfully")
    return nil
}
```

### Phase 4: Implement Matter Resharing Logic (1 day)

```go
// In internal/service/gdrive/service.go
func (s *service) reshareMattersForUser(ctx context.Context, permissionMappings []*model.DocumentPermissionMapping, email string, tenantID uint64) error {
    log := logger.WithCtx(ctx, "reshareMattersForUser")
    log.Infof("Resharing %d matters for email=%s", len(permissionMappings), email)

    if len(permissionMappings) == 0 {
        return nil
    }

    successCount := 0
    failureCount := 0

    for _, mapping := range permissionMappings {
        // Skip if this is not a matter permission or it's the same as what we're removing
        if mapping.DocumentID == "" {
            continue
        }

        // Check if permission already exists
        existingPerms, err := s.driveClient.ListPermissions(ctx, mapping.DocumentID)
        if err != nil {
            log.WithError(err).Errorf("Failed to list permissions for folder drive_id=%s", mapping.DocumentID)
            failureCount++
            continue
        }

        // Check if user already has permission
        hasPermission := false
        for _, perm := range existingPerms {
            if perm.EmailAddress == email {
                hasPermission = true
                log.Infof("User already has permission on folder drive_id=%s", mapping.DocumentID)
                break
            }
        }

        if hasPermission {
            successCount++
            continue
        }

        // Create the new permission using existing gdrive package method
        permission := &drive.Permission{
            Role:         mapping.Role,
            Type:         "user",
            EmailAddress: email,
        }

        result, err := s.driveClient.CreatePermission(ctx, mapping.DocumentID, permission)
        if err != nil {
            log.WithError(err).Errorf("Failed to reshare folder drive_id=%s", mapping.DocumentID)
            failureCount++
            continue
        }

        // Update permission mapping with new permission ID
        mapping.PermissionID = result.Id
        err = s.permissionMappingRepo.Update(ctx, mapping)
        if err != nil {
            log.WithError(err).Errorf("Failed to update permission mapping for drive_id=%s", mapping.DocumentID)
            // Continue even if mapping update fails
        }

        log.Infof("Successfully reshared folder drive_id=%s for email=%s", mapping.DocumentID, email)
        successCount++
    }

    log.Infof("Resharing summary for email=%s: %d successful, %d failed, %d total",
        email, successCount, failureCount, len(permissionMappings))

    return nil
}
```



### Phase 5: Testing (1.5 days)

1. **Unit Tests**
   - Test permission removal logic with mocked repositories
   - Test resharing logic with mocked Google Drive client
   - Test error handling for API failures

2. **Integration Tests**
   - Test end-to-end permission removal and resharing
   - Verify matter folders retain access after client folder permission removal

3. **Test Cases**
   - Remove permission from client folder with no shared matters
   - Remove permission from client folder with shared matters
   - Remove permission from matter folder
   - Edge cases: missing mappings, API failures

## Timeline and Resources

### Estimated Timeline: 4-5 working days
- **Day 1**: Database migration and model updates
- **Day 2**: Permission creation logic updates
- **Day 3**: Sequential resharing implementation
- **Day 4-5**: Testing and bug fixes

### Required Resources
- 1 Backend Developer

## Success Criteria

- ✅ Users retain access to matters when removed from client folder
- ✅ Minimal changes to existing codebase
- ✅ Simple sequential processing suitable for background jobs
- ✅ Basic error handling and logging

## Next Steps

1. Review this simplified plan with the development team
2. Get approval for database schema changes
3. Implement and test the changes
4. Deploy to staging and validate
5. Roll out to production
