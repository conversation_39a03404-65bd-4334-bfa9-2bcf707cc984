# Google Drive Integration API Documentation

## Overview

This document provides comprehensive API documentation for the Google Drive integration endpoints. These APIs enable frontend applications to manage documents and folders in Google Drive through a unified RESTful interface that is compatible with SharePoint migration patterns.

## Base URL

All endpoints use the following base URL format:
```
https://API_HOST/docman/v3/gdrive/
```

## Authentication

All endpoints require authentication via Bear<PERSON> token in the Authorization header:

```http
Authorization: Bearer <token>
```

## Role-Based Authorization

The API implements role-based access control with the following roles:
- **Admin** (8): Full access to all operations including setup
- **Manager** (4): Full access to all operations including setup  
- **Staff** (1): Access to document operations (create, list, update, delete, upload)

## Common Headers

All requests should include these headers:

```http
Content-Type: application/json
Authorization: Bearer <token>
```

## Error Response Format

All endpoints return errors in the following standardized format:

```json
{
  "error": {
    "detail": "Error message description",
    "msg": "alternative error message if detail is empty"
  }
}
```

Common HTTP status codes:
- `400` - Bad Request (validation errors, invalid parameters)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `500` - Internal Server Error

## API Structure

The Google Drive V3 API is organized into logical groups:

- **Setup Operations**: `/v3/gdrive/setup/` - Configuration and integration setup (admin-only)
- **Document Operations**: `/v3/gdrive/documents/` - Day-to-day document management
- **Search Operations**: `/v3/gdrive/search/` - Document search and discovery
- **Internal Events**: `/internal/consume/gdrive/` - System event handling (internal only)

## Path Configuration

The Google Drive integration supports customizable folder naming patterns through PathConfig. This allows organizations to maintain consistent folder structures that match their business processes.

### Key Concepts

- **Template Variables**: Dynamic placeholders that get replaced with actual values
- **Client Folders**: Top-level folders for each client/organization
- **Matter Folders**: Sub-folders within client folders for specific matters/cases
- **Case Transformation**: Automatic text case conversion (original, lower, upper, title)
- **Security**: Built-in protection against path traversal attacks

### Template Variables

**Client Folder Variables:**
- `{name}` - Full client name
- `{short_name}` - Client short name (if available)
- `{code}` - Client code/identifier
- `{short_name|name}` - Use short_name if available, fallback to name

**Matter Folder Variables:**
- `{client_folder}` - Reference to the parent client folder (required)
- `{name}` - Matter/case name
- `{code}` - Matter/case code/identifier

### Example Folder Structure

With default configuration:
```json
{
  "path_config": {
    "client_folder_path": "{short_name|name} - {code}",
    "matter_folder_path": "{client_folder}/matters/{name} - {code}",
    "case_format": "original",
    "max_length": 255,
    "invalid_char_replace": "_"
  }
}
```

Results in:
```
📁 Acme Corp - AC001/                    (client folder, spaces preserved)
  📁 matters/                            (matter parent folder)
    📁 Contract Review - CR2024001/      (matter folder, spaces preserved)
    📁 Litigation Case - LIT2024002/     (matter folder, spaces preserved)
```

With custom configuration:
```json
{
  "path_config": {
    "client_folder_path": "{name} - {code}",
    "matter_folder_path": "{client_folder}/cases/{name}-{code}",
    "case_format": "title",
    "invalid_char_replace": "-"
  }
}
```

Results in:
```
📁 ACME CORP - AC001/                    (client folder, title case)
  📁 cases/                              (custom matter parent)
    📁 Contract Review-CR2024001/        (matter folder: {name}-{code} format)
    📁 Litigation Case-LIT2024002/       (matter folder: {name}-{code} format)
```

**Breakdown:**
- Template: `"{client_folder}/cases/{name}-{code}"`
- `{client_folder}` = "Acme Corp - AC001" (already processed, then title case applied)
- `{name}` = "Contract Review" (spaces preserved)
- `{code}` = "CR2024001"
- Combined: "Acme Corp - AC001/cases/Contract Review-CR2024001"
- `case_format: "title"` converts entire result to title case: "ACME CORP - AC001"
- Only invalid characters (`/`, `\`, `:`, `*`, `?`, `"`, `<`, `>`, `|`) are replaced

### Template Formatting Examples

**Important:** Google Drive allows spaces in folder names. Only specific invalid characters are replaced.

**Invalid Characters:** `/`, `\`, `:`, `*`, `?`, `"`, `<`, `>`, `|`, null character

| Template | Input Data | Output | Notes |
|----------|------------|--------|-------|
| `"{name} - {code}"` | name: "Contract Review"<br>code: "CR001" | "Contract Review - CR001" | Spaces preserved |
| `"{name}-{code}"` | name: "Contract Review"<br>code: "CR001" | "Contract Review-CR001" | Clean format |
| `"{code}_{name}"` | name: "Contract Review"<br>code: "CR001" | "CR001_Contract Review" | Code first |
| `"{name}"` | name: "Contract/Review" | "Contract_Review" | Invalid char `/` → `_` |
| `"{name}"` | name: "Contract*Review" | "Contract_Review" | Invalid char `*` → `_` |

**Case Format Examples:**
| case_format | Input | Output |
|-------------|-------|--------|
| `"original"` | "Contract Review" | "Contract Review" |
| `"lower"` | "Contract Review" | "contract review" |
| `"upper"` | "Contract Review" | "CONTRACT REVIEW" |
| `"title"` | "contract review" | "Contract Review" |

**Processing Order:**
1. Replace template variables with actual values
2. Replace only invalid characters (`/`, `\`, `:`, `*`, `?`, `"`, `<`, `>`, `|`) with `invalid_char_replace`
3. Apply `case_format` transformation
4. Truncate to `max_length` if needed

**Note:** The `invalid_char_replace` setting is currently defined but not yet implemented in the folder naming logic. Spaces are preserved in folder names.

---

## Setup Operations (Admin Only)

### 1. Test Google Drive Setup

**POST** `/v3/gdrive/setup/test-setup`

Tests Google Drive URL/ID validity and verifies service account access.

#### Request Body

```json
{
  "url_or_id": "string"  // Required: Google Drive URL or folder/drive ID
}
```

#### Response

**Status:** `200 OK`

```json
{
  "status": "success",
  "drive_info": {
    "id": "string",
    "name": "string",
    "type": "folder|drive"
  },
  "message": "Google Drive connection test successful"
}
```

#### Example

```bash
curl -X POST /v3/gdrive/setup/test-setup \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "url_or_id": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }'
```

### 2. Complete Google Drive Setup

**POST** `/v3/gdrive/setup/complete-setup`

Completes Google Drive integration setup for the tenant with optional path configuration.

#### Request Body

```json
{
  "url_or_id": "string",  // Required: Google Drive URL or folder/drive ID
  "enabled": true,        // Required: Enable/disable integration
  "path_config": {        // Optional: Folder path configuration
    "client_folder_path": "string",    // Optional: Client folder naming template
    "matter_folder_path": "string",    // Optional: Matter folder naming template
    "case_format": "string",           // Optional: Case transformation (original|lower|upper|title)
    "max_length": 255,                 // Optional: Maximum folder name length (1-255)
    "invalid_char_replace": "string"   // Optional: Single character to replace invalid chars
  }
}
```

#### Path Configuration Details

The `path_config` object allows customization of folder naming patterns:

- **client_folder_path**: Template for client folder names
  - Available variables: `{name}`, `{short_name}`, `{code}`
  - Default: `"{short_name|name} - {code}"`
  - Example: `"{name} - {code}"` → "Acme Corp - AC001"

- **matter_folder_path**: Template for matter folder names
  - Available variables: `{client_folder}`, `{name}`, `{code}`
  - Must contain `{client_folder}` variable
  - Default: `"{client_folder}/matters/{name} - {code}"`
  - Example: `"{client_folder}/cases/{name}"` → "Acme Corp - AC001/cases/Contract Review"

- **case_format**: Text case transformation
  - Values: `"original"`, `"lower"`, `"upper"`, `"title"`
  - Default: `"original"`

- **max_length**: Maximum folder name length
  - Range: 1-255 characters
  - Default: `255`

- **invalid_char_replace**: Character to replace invalid filename characters
  - Must be a single character
  - Default: `"_"`

#### Response

**Status:** `200 OK`

```json
{
  "status": "success",
  "message": "Google Drive integration has been enabled successfully",
  "enabled": true,
  "state": "enabled",
  "path_config": {
    "client_folder_path": "{short_name|name} - {code}",
    "matter_folder_path": "{client_folder}/matters/{name} - {code}",
    "case_format": "original",
    "max_length": 255,
    "invalid_char_replace": "_"
  }
}
```

#### Examples

**Basic Setup (using defaults):**
```bash
curl -X POST /v3/gdrive/setup/complete-setup \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "url_or_id": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "enabled": true
  }'
```

**Setup with Custom Path Configuration:**
```bash
curl -X POST /v3/gdrive/setup/complete-setup \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "url_or_id": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "enabled": true,
    "path_config": {
      "client_folder_path": "{name} - {code}",
      "matter_folder_path": "{client_folder}/cases/{name} - {code}",
      "case_format": "title",
      "max_length": 200,
      "invalid_char_replace": "-"
    }
  }'
```

#### Path Configuration Validation Errors

The API validates path configuration and returns specific error messages:

**Status:** `400 Bad Request`

```json
{
  "error": {
    "detail": "invalid path_config: <specific_error_message>"
  }
}
```

**Common Validation Errors:**

| Error | Description |
|-------|-------------|
| `case_format must be one of: original, lower, upper, title` | Invalid case format value |
| `max_length must be between 1 and 255` | Invalid max length value |
| `invalid_char_replace must be a single character` | Invalid replacement character |
| `path contains invalid traversal patterns` | Security violation (../, ./, etc.) |
| `invalid variable 'var_name' in client template` | Unknown variable in client_folder_path |
| `invalid variable 'var_name' in matter template` | Unknown variable in matter_folder_path |
| `matter folder path must contain {client_folder} variable` | Missing required variable |

**Example Error Response:**
```json
{
  "error": {
    "detail": "invalid path_config: case_format must be one of: original, lower, upper, title"
  }
}
```

---

## Document Operations

### 3. List Documents

Retrieve documents and folders from Google Drive.

**Endpoint:** `GET /v3/gdrive/documents`

**Authorization:** All roles (Admin=8, Manager=4, Staff=1)

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Conditional | Parent folder ID (required if object_type/object_id not provided) |
| `object_type` | string | Conditional | Object type for mapping (required if id not provided) |
| `object_id` | integer | Conditional | Object ID for mapping (required if id not provided) |
| `search` | string | No | Search query to filter results |
| `page_size` | integer | No | Number of results per page (default: 100, max: 1000) |
| `page_token` | string | No | Token for pagination |

#### Response Format

```json
{
  "data": {
    "documents": [
      {
        "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "Project Documents",
        "parent_id": 0,
        "doc_type": 1,
        "object_type": 0,
        "object_id": 0,
        "is_file": false,
        "is_internal": false,
        "has_child": true,
        "size": 0,
        "type": "folder",
        "date_created": "2024-01-15T10:30:00Z",
        "date_modified": "2024-01-20T14:45:00Z",
        "last_modified_by": "John Doe",
        "web_url": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "status": 1
      }
    ],
    "next_page_token": "next_token_here",
    "total_count": 25
  }
}
```

#### cURL Example

```bash
curl -X GET "https://API_HOST/docman/v3/gdrive/documents?object_type=client&object_id=123" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

### 4. Create Document/Folder

**POST** `/v3/gdrive/documents`

Creates a new folder in Google Drive.

#### Request Body

```json
{
  "name": "New Project Folder",
  "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "parent_path": "department/team1",
  "object_id": 12345,
  "object_type": "project"
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Folder name (max 255 characters) |
| `parent_id` | string | Conditional | Base parent folder ID (required if object_type/object_id not provided) |
| `parent_path` | string | No | Hierarchical path to navigate from the base parent folder |
| `object_id` | integer | Conditional | Object ID for mapping (required if parent_id not provided) |
| `object_type` | string | Conditional | Object type for mapping (required if parent_id not provided) |

#### Parent Resolution Logic

The API follows a specific hierarchy to determine the final parent folder:

1. **Base Parent Determination:**
   - If `parent_id` is provided, it is used as the base parent folder
   - If `parent_id` is missing but `object_type` and `object_id` are provided, the system looks up the corresponding folder ID from document mappings
   - If neither are provided but `parent_path` exists, "root" is used as the base parent

2. **Hierarchical Path Navigation:**
   - If `parent_path` is provided, the system navigates from the base parent folder through the specified path
   - Missing folders in the path are created automatically
   - The final folder in the path becomes the actual parent for the new document/folder

3. **Examples:**
   - `parent_id="client_folder_123", parent_path="department/team1"` → Creates "team1" inside "department" inside "client_folder_123"
   - `object_type="client", object_id=123, parent_path="projects/2023"` → Finds the client folder, then creates "2023" inside "projects" inside that client folder
   - `parent_path="department/team1"` (no parent_id or object mapping) → Creates "team1" inside "department" inside the root folder

#### Response Format

```json
{
  "data": {
    "id": "1NewFolderID123456789",
    "name": "New Project Folder",
    "parent_id": 12345,
    "doc_type": 1,
    "object_type": 1,
    "object_id": 12345,
    "is_file": false,
    "is_internal": false,
    "has_child": false,
    "size": 0,
    "type": "folder",
    "date_created": "2024-01-25T09:15:00Z",
    "date_modified": "2024-01-25T09:15:00Z",
    "last_modified_by": "Current User",
    "web_url": "https://drive.google.com/drive/folders/1NewFolderID123456789",
    "status": 1
  }
}
```

#### cURL Examples

**Create folder in a specific parent folder:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Project Folder",
    "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }'
```

**Create folder using object mapping:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Inside Client Folder",
    "object_id": 12345,
    "object_type": "client"
  }'
```

**Create folder with hierarchical path:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Team Document",
    "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "parent_path": "department/team1"
  }'
```

**Create folder with object mapping and hierarchical path:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Project Document",
    "object_id": 12345,
    "object_type": "client",
    "parent_path": "projects/2023"
  }'
```

### 5. Update Document

**PATCH** `/v3/gdrive/documents/{document_id}`

Updates the name of an existing folder or file.

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `document_id` | string | Yes | Google Drive file/folder ID |

#### Request Body

```json
{
  "name": "Updated Folder Name"
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | New name (max 255 characters) |

#### Response Format

```json
{
  "data": {
    "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "name": "Updated Folder Name",
    "doc_type": 1,
    "object_type": 1,
    "object_id": 12345,
    "is_file": false,
    "is_internal": false,
    "has_child": true,
    "size": 0,
    "type": "folder",
    "date_created": "2024-01-15T10:30:00Z",
    "date_modified": "2024-01-25T11:20:00Z",
    "last_modified_by": "Current User",
    "web_url": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "status": 1
  }
}
```

#### cURL Example

```bash
curl -X PATCH "https://API_HOST/docman/v3/gdrive/documents/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Folder Name"
  }'
```

### 6. Delete Document

**DELETE** `/v3/gdrive/documents/{document_id}`

Deletes an existing folder or file from Google Drive.

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `document_id` | string | Yes | Google Drive file/folder ID |

#### Response Format

```json
{
  "data": {
    "message": "Document deleted successfully",
    "deleted_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }
}
```

#### cURL Example

```bash
curl -X DELETE "https://API_HOST/docman/v3/gdrive/documents/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms" \
  -H "Authorization: Bearer <token>"
```

### 7. Generate Upload URL

**POST** `/v3/gdrive/documents/upload`

Generates a resumable upload URL for client-side file uploads (SharePoint-compatible pattern).

#### Request Body

```json
{
  "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "parent_path": "department/team1",
  "file_name": "large-document.pdf",
  "object_type": "client",
  "object_id": 12345
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Conditional | Base parent folder ID (required if object_type/object_id not provided) |
| `parent_path` | string | No | Hierarchical path to navigate from the base parent folder |
| `file_name` | string | Yes | Name of the file to be uploaded (max 255 characters) |
| `object_type` | string | Conditional | Object type for mapping lookup (required if id not provided) |
| `object_id` | number | Conditional | Object ID for mapping lookup (required if id not provided) |

**Parent Resolution Logic:**
1. First, a base parent folder is determined:
   - If `id` is provided, it's used directly as the base parent folder
   - If `object_type` and `object_id` are provided, they're used to look up the corresponding folder
   - If neither is provided but `parent_path` exists, "root" is used as the base parent
2. Then, if `parent_path` is provided, it's used to navigate from the base parent folder to create/find the final upload destination
3. If no valid parent specification is provided, the request will fail with a validation error

#### Response Format

```json
{
  "data": {
    "upload_url": "https://API_HOST/docman/v3/gdrive/upload/SESSION_TOKEN/content",
    "session_token": "SESSION_TOKEN",
    "expires_at": "2024-01-15T12:30:00Z"
  }
}
```

#### cURL Examples

**Upload to a specific folder by ID:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents/upload" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "file_name": "large-document.pdf"
  }'
```

**Upload using hierarchical path from root:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents/upload" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "parent_path": "department/team1",
    "file_name": "large-document.pdf"
  }'
```

**Upload using object mapping and hierarchical path:**
```bash
curl -X POST "https://API_HOST/docman/v3/gdrive/documents/upload" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "object_type": "client",
    "object_id": 12345,
    "parent_path": "projects/2023",
    "file_name": "report.pdf"
  }'
```

#### Upload Process

The file upload follows a 2-step process:

1. **Step 1**: Call this endpoint to get the upload URL and session token
2. **Step 2**: Use the returned `upload_url` to upload file content directly to the service

**Step 2 Example:**
```bash
curl -X PUT "https://API_HOST/docman/v3/gdrive/upload/SESSION_TOKEN/content" \
  -H "Content-Type: application/pdf" \
  -H "Content-Length: <file_size>" \
  --data-binary @large-document.pdf
```

**Note:** The upload content endpoint (Step 2) is publicly accessible without authentication as it uses the secure session token for authorization.

---

## Search Operations

### 8. Search Files

**GET** `/v3/gdrive/search/files`

Search for files across Google Drive by keyword with pagination support. This endpoint uses Google Drive's fullText search capability to find files by both filename and document content, returning only files (folders are excluded). Search is automatically scoped to the tenant's configured shared drive or shared folder.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `keyword` | string | Yes | Search keyword for file names and content |
| `page_size` | integer | No | Number of results per page (default: 100, max: 1000) |
| `next_page` | string | No | Pagination token for next page |

#### Example Request

```bash
curl -X GET "/v3/gdrive/search/files?keyword=project%20report&page_size=50" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Response

**Success (200 OK)**

```json
{
  "data": {
    "data": [
      {
        "docId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "Project Report.pdf",
        "size": 1048576,
        "webUrl": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
        "isFile": true,
        "dateModified": "2024-01-15T10:30:00Z",
        "dateCreated": "2024-01-10T09:00:00Z",
        "lastModifiedBy": "Google Drive User"
      }
    ],
    "meta": {
      "next_page": "next_page_token_123",
      "page_size": 50,
      "total": 1
    }
  }
}
```

---

## Event Consumer Endpoints

These endpoints are for internal service-to-service communication and handle events from other systems to automate Google Drive operations.

> **⚠️ DEPRECATION NOTICE**: Direct provider endpoints are deprecated in favor of coordinated AutoDoc endpoints to prevent race conditions. Use the new AutoDoc endpoints instead.

### 9. Process Client CRUD Events ⚠️ **DEPRECATED**

**POST** `/internal/consume/gdrive/client/crud`

> **🚨 DEPRECATED**: This endpoint is deprecated. Use `/internal/consume/autodoc/client/created` and `/internal/consume/autodoc/client/updated` for coordinated processing to avoid race conditions.

Handles client creation and update events to automatically create and manage client folders in Google Drive.

#### Request Body

```json
{
  "topic": "client.create",
  "body": {
    "client_id": 123,
    "name": "Test Client",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"},
      {"email": "<EMAIL>"}
    ]
  }
}
```

#### Response Format

```json
{
  "success": true,
  "message": "Client event processed successfully"
}
```

#### Example

```bash
curl -X POST "https://API_HOST/internal/consume/gdrive/client/crud" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "client.create",
    "body": {
      "client_id": 123,
      "name": "Test Client",
      "tenant_id": 1,
      "owner_users": [
        {"email": "<EMAIL>"},
        {"email": "<EMAIL>"}
      ]
    }
  }'
```

### 10. Process Matter CRUD Events ⚠️ **DEPRECATED**

**POST** `/internal/consume/gdrive/matter/crud`

> **🚨 DEPRECATED**: This endpoint is deprecated. Use `/internal/consume/autodoc/matter/created` and `/internal/consume/autodoc/matter/updated` for coordinated processing to avoid race conditions.

Handles matter creation and update events to automatically create and manage matter folders in Google Drive.

#### Request Body

```json
{
  "topic": "matter.create",
  "body": {
    "matter_id": 456,
    "client_id": 123,
    "name": "Test Matter",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"}
    ]
  }
}
```

#### Response Format

```json
{
  "success": true,
  "message": "Matter event processed successfully"
}
```

#### Example

```bash
curl -X POST "https://API_HOST/internal/consume/gdrive/matter/crud" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "matter.create",
    "body": {
      "matter_id": 456,
      "client_id": 123,
      "name": "Test Matter",
      "tenant_id": 1,
      "owner_users": [
        {"email": "<EMAIL>"}
      ]
    }
  }'
```

---

## ✅ **Recommended AutoDoc Endpoints**

These are the **new coordinated endpoints** that should be used instead of the deprecated direct provider endpoints above.

### 11. Process Client Events ✅ **RECOMMENDED** (Unified)

**POST** `/internal/consume/autodoc/client/events`

**Unified endpoint** that handles both client creation and update events with **coordinated processing** across all providers (GDrive, SharePoint, Internal) to prevent race conditions.

#### Request Body

**For client.create events:**
```json
{
  "topic": "client.create",
  "body": {
    "id": 123,
    "name": "Test Client",
    "code": "CLI001",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"},
      {"email": "<EMAIL>"}
    ]
  }
}
```

**For client.update events:**
```json
{
  "topic": "client.update",
  "body": {
    "id": 123,
    "name": "Updated Client Name",
    "code": "CLI001",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"},
      {"email": "<EMAIL>"}
    ]
  }
}
```

#### Benefits
- ✅ **Race condition prevention** - Folders created before autodoc rules execute
- ✅ **Multi-provider coordination** - All providers (GDrive, SharePoint, Internal) coordinated
- ✅ **Automatic operation tracking** - Database tracking of all operations
- ✅ **Error handling** - Robust error handling across all providers

### 12. Process Matter Events ✅ **RECOMMENDED** (Unified)

**POST** `/internal/consume/autodoc/matter/events`

**Unified endpoint** that handles both matter creation and update events with **coordinated processing** across all providers (GDrive, SharePoint, Internal) to prevent race conditions.

#### Request Body

**For matter.create events:**
```json
{
  "topic": "matter.create",
  "body": {
    "id": 789,
    "client_id": 123,
    "name": "Test Matter",
    "code": "MAT001",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"},
      {"email": "<EMAIL>"}
    ]
  }
}
```

**For matter.update events:**
```json
{
  "topic": "matter.update",
  "body": {
    "id": 789,
    "client_id": 123,
    "name": "Updated Matter Name",
    "code": "MAT001",
    "tenant_id": 1,
    "owner_users": [
      {"email": "<EMAIL>"},
      {"email": "<EMAIL>"}
    ]
  }
}
```

### 13. Individual Event Endpoints ⚠️ **DEPRECATED**

For backward compatibility, individual endpoints are still available but deprecated:

- `POST /internal/consume/autodoc/client/created` ⚠️ **Use `/internal/consume/autodoc/client/events` instead**
- `POST /internal/consume/autodoc/client/updated` ⚠️ **Use `/internal/consume/autodoc/client/events` instead**
- `POST /internal/consume/autodoc/matter/created` ⚠️ **Use `/internal/consume/autodoc/matter/events` instead**
- `POST /internal/consume/autodoc/matter/updated` ⚠️ **Use `/internal/consume/autodoc/matter/events` instead**

#### Migration Guide

**Old (Deprecated):**
```bash
# ❌ Direct provider calls (may cause race conditions)
curl -X POST "/internal/consume/gdrive/client/crud"
curl -X POST "/internal/consume/sharepoint/client/crud"
curl -X POST "/internal/consume/gdrive/matter/crud"
curl -X POST "/internal/consume/sharepoint/matter/crud"
```

**New (Recommended - Unified):**
```bash
# ✅ Unified coordinated processing (prevents race conditions)
curl -X POST "/internal/consume/autodoc/client/events" \
  -H "Content-Type: application/json" \
  -d '{"topic": "client.create", "body": {...}}'

curl -X POST "/internal/consume/autodoc/client/events" \
  -H "Content-Type: application/json" \
  -d '{"topic": "client.update", "body": {...}}'

curl -X POST "/internal/consume/autodoc/matter/events" \
  -H "Content-Type: application/json" \
  -d '{"topic": "matter.create", "body": {...}}'

curl -X POST "/internal/consume/autodoc/matter/events" \
  -H "Content-Type: application/json" \
  -d '{"topic": "matter.update", "body": {...}}'
```

**Individual Endpoints (Backward Compatibility):**
```bash
# ⚠️ Still supported but deprecated
curl -X POST "/internal/consume/autodoc/client/created"
curl -X POST "/internal/consume/autodoc/client/updated"
curl -X POST "/internal/consume/autodoc/matter/created"
curl -X POST "/internal/consume/autodoc/matter/updated"
```

---

## Error Handling

### Error Format

```json
{
  "error": "string",     // Error message
  "code": "string"       // Error code (optional)
}
```

### HTTP Status Codes

| Status | Description                    | Error Codes                           |
|--------|--------------------------------|---------------------------------------|
| 400    | Bad Request                    | Validation errors, invalid parameters |
| 401    | Unauthorized                   | Missing or invalid authentication     |
| 403    | Forbidden                      | Insufficient permissions              |
| 404    | Not Found                      | Document/folder not found             |
| 409    | Conflict                       | Duplicate document name               |
| 500    | Internal Server Error          | Server or Google Drive API errors     |

### Example Error Response

```json
{
  "error": "document name is required"
}
```

---

## Middleware

All Google Drive V3 endpoints include the following middleware:

1. **Authentication Middleware**: Validates JWT tokens
2. **Google Drive Auth Middleware**: Verifies Google Drive access for tenant
3. **Logging Middleware**: Logs all requests and responses
4. **Error Recovery Middleware**: Handles panics and converts to proper error responses

## SharePoint Compatibility

The Google Drive V3 API is designed to be 100% compatible with SharePoint V3 API patterns:

- **Request/Response Formats**: Identical structure to SharePoint API
- **Error Handling**: Same error codes and messages
- **Upload Pattern**: URL generation for client-side uploads
- **Pagination**: Compatible pagination tokens and metadata
- **Search**: Same search query format and behavior

This compatibility allows for seamless migration between SharePoint and Google Drive backends.

---

*Last Updated: 2025-07-15*
*API Version: v3*
