# Google Drive Integration Testing Guide

## Overview

This document provides comprehensive testing procedures for the Google Drive integration. It covers manual testing of API endpoints, validation of error handling, security testing, and performance considerations.

## Environment Setup

### Prerequisites

1. **Authentication Setup**
   - Valid JWT token with appropriate user roles (Admin, Manager, User)
   - Google Drive API credentials configured
   - Test Google Drive folder with proper permissions

2. **Test Environment**
   - API server running on development/staging environment
   - Access to test database with sample data
   - Network access to Google Drive API

3. **Test Data Preparation**
   - Sample client and matter records in database
   - Test files for upload (various formats and sizes)
   - Valid Google Drive folder IDs for testing

### Required Headers

#### For Public API Endpoints (External clients)
```json
{
  "Authorization": "Bearer <JWT_TOKEN>",
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

#### For Internal Service Endpoints (Service-to-service)
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json",
  "X-Tenant-ID": "<TENANT_ID>",
  "X-User-ID": "<USER_ID>",
  "X-User-Roles": "<INTEGER_BITWISE_VALUE>"
}
```

#### Role Examples (Bitwise Integer Values)
- **Sysadmin**: `X-User-Roles: 16` (binary: 10000 - highest level permissions)
- **Admin**: `X-User-Roles: 8` (binary: 1000 - admin permissions)
- **Manager**: `X-User-Roles: 4` (binary: 100 - manager permissions)
- **Lead**: `X-User-Roles: 2` (binary: 10 - lead permissions)
- **Staff**: `X-User-Roles: 1` (binary: 1 - basic staff permissions)
- **Combined Roles**: `X-User-Roles: 28` (binary: 11100 - Admin + Manager + Sysadmin for setup operations)

## Test Categories

### 1. Setup and Configuration

#### Test Case: Enable Google Drive Integration
**Endpoint:** `POST /v3/gdrive/setup/complete-setup`
**Headers:** Internal service headers with Admin role
```json
{
  "Content-Type": "application/json",
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "8"
}
```
**Payload (Folder ID):**
```json
{
  "enabled": true,
  "url_or_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "status": "success",
    "message": "Google Drive integration has been enabled successfully",
    "enabled": true,
    "state": "enabled"
  }
}
```
**Assertions:**
- Google Drive integration enabled for tenant
- Configuration stored in database
- Integration state updated correctly
- Success message returned

**cURL Command:**
```bash
curl -X POST "{{host}}/v3/gdrive/setup/complete-setup" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{admin_user_id}}" \
  -H "X-User-Roles: 8" \
  -d '{
    "enabled": true,
    "url_or_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }'
```

#### Test Case: Test Google Drive Setup
**Endpoint:** `POST /v3/gdrive/setup/test-setup`
**Headers:** Internal service headers with Admin role
```json
{
  "Content-Type": "application/json",
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "8"
}
```
**Payload:**
```json
{
  "url_or_id": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "status": "success",
    "drive_info": {
      "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
      "name": "Test Folder",
      "type": "folder"
    },
    "message": "Google Drive setup test successful"
  }
}
```
**Assertions:**
- Google Drive folder accessible
- Proper permissions verified
- Folder information correctly retrieved
- Setup validation passes

**cURL Command:**
```bash
curl -X POST "{{host}}/v3/gdrive/setup/test-setup" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{admin_user_id}}" \
  -H "X-User-Roles: 8" \
  -d '{
    "url_or_id": "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }'
```

### 2. Document CRUD Operations

#### Test Case: Create Document Record
**Endpoint:** `POST /v3/gdrive/documents`
**Headers:** Internal service headers with Admin role
```json
{
  "Content-Type": "application/json",
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "8"
}
```
**Payload:**
```json
{
  "name": "Test Document",
  "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "object_type": "matter",
  "object_id": 123,
  "description": "Test document for manual testing"
}
```
**Expected Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "generated_drive_id",
    "name": "Test Document",
    "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "web_view_link": "https://drive.google.com/...",
    "created_at": "2025-01-07T10:00:00Z"
  }
}
```
**Assertions:**
- Response contains valid Google Drive ID
- Document appears in Google Drive folder
- Database record created with correct mapping
- Web view link is accessible

**cURL Command:**
```bash
curl -X POST "{{host}}/v3/gdrive/documents" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{admin_user_id}}" \
  -H "X-User-Roles: 8" \
  -d '{
    "name": "Test Document",
    "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "object_type": "matter",
    "object_id": 123,
    "description": "Manual test document"
  }'
```

#### Test Case: List Documents with Filtering
**Endpoint:** `GET /v3/gdrive/documents`
**Headers:** Internal service headers with Staff role
```json
{
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "1"
}
```
**Query Parameters:**
```
?object_type=matter&object_id=123&limit=10&offset=0&search=test
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "drive_id_1",
        "name": "Test Document",
        "object_type": "matter",
        "object_id": 123,
        "web_view_link": "https://drive.google.com/...",
        "created_at": "2025-01-07T10:00:00Z"
      }
    ],
    "pagination": {
      "total": 1,
      "limit": 10,
      "offset": 0,
      "has_more": false
    }
  }
}
```
**Assertions:**
- Filtering works correctly for object_type and object_id
- Search functionality returns relevant results
- Pagination parameters are respected
- Response includes all required document fields

**cURL Command:**
```bash
curl -X GET "{{host}}/v3/gdrive/documents?object_type=matter&object_id=123&limit=10&offset=0&search=test" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{staff_user_id}}" \
  -H "X-User-Roles: 1"
```

#### Test Case: Update Document Metadata
**Endpoint:** `PATCH /v3/gdrive/documents/{document_id}`
**Headers:** Internal service headers with Manager role
```json
{
  "Content-Type": "application/json",
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "4"
}
```
**Payload:**
```json
{
  "name": "Updated Document Name",
  "description": "Updated description"
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "document_id",
    "name": "Updated Document Name",
    "description": "Updated description",
    "updated_at": "2025-01-07T10:30:00Z"
  }
}
```
**Assertions:**
- Document name updated in Google Drive
- Database record reflects changes
- Updated timestamp is current
- Original metadata preserved where not updated

**cURL Command:**
```bash
curl -X PATCH "{{host}}/v3/gdrive/documents/{{document_id}}" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{manager_user_id}}" \
  -H "X-User-Roles: 4" \
  -d '{
    "name": "Updated Document Name",
    "description": "Updated description"
  }'
```

#### Test Case: Delete Document
**Endpoint:** `DELETE /v3/gdrive/documents/{document_id}`
**Headers:** Internal service headers with Sysadmin role
```json
{
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "16"
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "message": "Document deleted successfully"
}
```
**Assertions:**
- Document removed from Google Drive
- Database mapping record deleted
- Document no longer accessible via web link
- Proper cleanup of associated metadata

**cURL Command:**
```bash
curl -X DELETE "{{host}}/v3/gdrive/documents/{{document_id}}" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{sysadmin_user_id}}" \
  -H "X-User-Roles: 16"
```

### 3. File Upload Functionality

Google Drive file upload follows a **2-step process**:
1. **Step 1**: Generate upload URL via API endpoint
2. **Step 2**: Upload file content to the generated URL

#### Test Case: Step 1 - Generate Upload URL
**Endpoint:** `POST /v3/gdrive/documents/upload`
**Headers:** Standard API headers (all roles including Staff=1 allowed)
```json
{
  "Content-Type": "application/json",
  "X-Tenant-ID": "1",
  "X-User-ID": "123",
  "X-User-Roles": "1"
}
```
**Payload:** JSON with file metadata
```json
{
  "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "file_name": "test-document.pdf"
}
```
**Expected Response:** `200 OK`
```json
{
  "data": {
    "upload_url": "https://www.googleapis.com/upload/drive/v3/files?uploadType=resumable&upload_id=AEnB2UqR..."
  }
}
```
**Assertions:**
- Valid resumable upload URL returned
- URL contains proper Google Drive upload endpoint
- Upload URL is accessible for subsequent file upload
- Response follows ginext format

**Step 1 cURL Command (Staff Role):**
```bash
curl -X POST "{{host}}/v3/gdrive/documents/upload" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{staff_user_id}}" \
  -H "X-User-Roles: 1" \
  -d '{
    "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "file_name": "test-document.pdf"
  }'
```

#### Test Case: Step 2 - Upload File Content
**Endpoint:** `PUT {upload_url_from_step1}`
**Headers:** Content-Type based on file type
```json
{
  "Content-Type": "application/pdf",
  "Content-Length": "1048576"
}
```
**Payload:** Raw file content (binary)
**Expected Response:** `200 OK` or `201 Created`
```json
{
  "id": "1AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
  "name": "test-document.pdf",
  "mimeType": "application/pdf",
  "size": "1048576",
  "webViewLink": "https://drive.google.com/file/d/1AbCdEfGhIjKlMnOpQrStUvWxYz1234567890/view"
}
```
**Assertions:**
- File successfully uploaded to Google Drive
- File metadata returned correctly
- File accessible via webViewLink
- File size matches uploaded content

**Step 2 cURL Command:**
```bash
# Use upload_url from Step 1 response
curl -X PUT "{{upload_url_from_step1}}" \
  -H "Content-Type: application/pdf" \
  -H "Content-Length: 1048576" \
  --data-binary "@test-document.pdf"
```

**Complete 2-Step Upload Example:**
```bash
# Step 1: Generate upload URL
UPLOAD_RESPONSE=$(curl -s -X POST "{{host}}/v3/gdrive/documents/upload" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{staff_user_id}}" \
  -H "X-User-Roles: 1" \
  -d '{
    "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "file_name": "test-document.pdf"
  }')

# Extract upload URL from response
UPLOAD_URL=$(echo $UPLOAD_RESPONSE | jq -r '.data.upload_url')

# Step 2: Upload file content
curl -X PUT "$UPLOAD_URL" \
  -H "Content-Type: application/pdf" \
  --data-binary "@test-document.pdf"
```

### 4. Event Consumer Endpoints

#### Test Case: Process Client CRUD Event
**Endpoint:** `POST /internal/consume/gdrive/client/crud`
**Headers:** Internal service headers
**Payload:**
```json
{
  "topic": "client.create",
  "body": {
    "client_id": 123,
    "name": "Test Client",
    "tenant_id": 1
  }
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "message": "Client event processed successfully"
}
```
**Assertions:**
- Client folder created in Google Drive
- Database mapping record created
- Folder permissions properly set
- Event processing logged

**cURL Command:**
```bash
curl -X POST "{{host}}/internal/consume/gdrive/client/crud" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "client.create",
    "body": {
      "client_id": 123,
      "name": "Test Client",
      "tenant_id": 1
    }
  }'
```

**Client Update Event cURL:**
```bash
curl -X POST "{{host}}/internal/consume/gdrive/client/crud" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "client.update",
    "body": {
      "client_id": 123,
      "name": "Updated Client Name",
      "tenant_id": 1
    }
  }'
```

#### Test Case: Process Matter CRUD Event
**Endpoint:** `POST /internal/consume/gdrive/matter/crud`
**Headers:** Internal service headers
**Payload:**
```json
{
  "topic": "matter.create",
  "body": {
    "matter_id": 456,
    "client_id": 123,
    "name": "Test Matter",
    "tenant_id": 1
  }
}
```
**Expected Response:** `200 OK`
```json
{
  "success": true,
  "message": "Matter event processed successfully"
}
```
**Assertions:**
- Matter folder created in Google Drive
- Database mapping record created
- Folder permissions properly set
- Event processing logged

**cURL Command:**
```bash
curl -X POST "{{host}}/internal/consume/gdrive/matter/crud" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "matter.create",
    "body": {
      "matter_id": 456,
      "client_id": 123,
      "name": "Test Matter",
      "tenant_id": 1
    }
  }'
```

### 5. Search Operations

#### Test Case: Search Files
**Endpoint:** `GET /v3/gdrive/search/files`
**Headers:** Standard API headers
```json
{
  "Authorization": "Bearer <token>",
  "Content-Type": "application/json"
}
```
**Query Parameters:**
```
?keyword=project%20report&page_size=50
```
**Expected Response:** `200 OK`
```json
{
  "data": {
    "data": [
      {
        "docId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "Project Report.pdf",
        "size": 1048576,
        "webUrl": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
        "isFile": true,
        "dateModified": "2024-01-15T10:30:00Z",
        "dateCreated": "2024-01-10T09:00:00Z",
        "lastModifiedBy": "Google Drive User"
      }
    ],
    "meta": {
      "next_page": "next_page_token_123",
      "page_size": 50,
      "total": 1
    }
  }
}
```
**Assertions:**
- Search returns relevant results
- Full-text search works across file content
- Pagination parameters are respected
- Response includes all required metadata

**cURL Command:**
```bash
curl -X GET "{{host}}/v3/gdrive/search/files?keyword=project%20report&page_size=50" \
  -H "Authorization: Bearer <token>"
```

## Security Testing

### 1. Input Validation and Security Testing

#### Test Case: SQL Injection Prevention
**Endpoint:** `GET /v3/gdrive/documents`
**Query Parameters:**
```
?search=' OR 1=1 --&object_type=matter' UNION SELECT * FROM users --
```
**Expected Response:** `200 OK` with sanitized results
**Assertions:**
- No SQL injection occurs
- Search query properly sanitized
- No sensitive data exposed
- Normal search results returned

**cURL Command:**
```bash
curl -X GET "{{host}}/v3/gdrive/documents?search=' OR 1=1 --&object_type=matter' UNION SELECT * FROM users --" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{staff_user_id}}" \
  -H "X-User-Roles: 1"
```

#### Test Case: Path Traversal Prevention
**Endpoint:** `POST /v3/gdrive/documents`
**Payload:**
```json
{
  "name": "../../../etc/passwd",
  "parent_id": "valid_folder_id",
  "object_type": "matter",
  "object_id": 123
}
```
**Expected Response:** `400 Bad Request`
```json
{
  "success": false,
  "error": "Invalid document name: contains invalid characters"
}
```
**Assertions:**
- Path traversal attempts blocked
- Filename validation prevents malicious names
- Clear error message returned
- No system files accessed

**cURL Command:**
```bash
curl -X POST "{{host}}/v3/gdrive/documents" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: {{tenant_id}}" \
  -H "X-User-ID: {{admin_user_id}}" \
  -H "X-User-Roles: 8" \
  -d '{
    "name": "../../../etc/passwd",
    "parent_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "object_type": "matter",
    "object_id": 123,
    "description": "Path traversal test"
  }'
```
