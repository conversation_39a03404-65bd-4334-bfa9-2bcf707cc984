# Google Drive Rate Limiting and Quota Management Design

**Document Version**: 1.1  
**Created**: 2025-06-28 11:14:41  
**Updated**: 2025-06-28 11:20:00  
**Author**: Development Team  
**Status**: Draft

## Overview

This document outlines the design for implementing **distributed** rate limiting and quota management for Google Drive API calls in the docman service. The implementation will ensure compliance with Google Drive API quotas while maintaining optimal performance across **multiple service nodes** using Redis as the distributed state store.

## Background

Google Drive API has the following quota limits:
- **Queries per 100 seconds per user**: 1,000
- **Queries per 100 seconds**: 10,000  
- **Queries per day**: 1,000,000,000

Our service runs on **multiple nodes** and needs to handle multiple tenants making concurrent requests, requiring **distributed rate limiting** to prevent quota exhaustion across all instances.

## Design Goals

1. **Prevent API quota exhaustion** across all tenants and service nodes
2. **Fair resource allocation** among tenants across distributed nodes
3. **Graceful degradation** when approaching limits
4. **Automatic retry** with exponential backoff
5. **Monitoring and alerting** for quota usage
6. **Configurable limits** for different environments
7. **Distributed coordination** using Redis for multi-node deployments

## Architecture

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Handler   │───▶│ Distributed     │───▶│ Google Drive API│
│   (Node 1)      │    │ Rate Limiter    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
┌─────────────────┐           ▼
│   API Handler   │    ┌─────────────────┐
│   (Node 2)      │───▶│     Redis       │
└─────────────────┘    │  Token Store    │
                       └─────────────────┘
┌─────────────────┐           │
│   API Handler   │           ▼
│   (Node N)      │    ┌─────────────────┐
└─────────────────┘    │   Metrics       │
                       └─────────────────┘
```

### 1. Distributed Rate Limiter Interface

```go
type DistributedRateLimiter interface {
    // Wait blocks until the request can proceed
    Wait(ctx context.Context, key string) error
    
    // TryAcquire attempts to acquire a permit without blocking
    TryAcquire(ctx context.Context, key string) (bool, error)
    
    // GetStats returns current limiter statistics
    GetStats(ctx context.Context, key string) (*LimiterStats, error)
    
    // Close releases resources
    Close() error
}

type LimiterStats struct {
    RequestsPerSecond float64
    AvailableTokens   int64
    TotalRequests     int64
    NodeID           string
}
```

### 2. Redis-Based Token Bucket

```go
type RedisTokenBucket struct {
    client      redis.Cmdable
    capacity    int64
    refillRate  time.Duration
    keyPrefix   string
    nodeID      string
    
    // Lua scripts for atomic operations
    acquireScript *redis.Script
    refillScript  *redis.Script
}

func NewRedisTokenBucket(client redis.Cmdable, capacity int64, refillRate time.Duration) *RedisTokenBucket {
    rtb := &RedisTokenBucket{
        client:     client,
        capacity:   capacity,
        refillRate: refillRate,
        keyPrefix:  "gdrive:rate_limit:",
        nodeID:     generateNodeID(),
    }
    
    // Initialize Lua scripts
    rtb.acquireScript = redis.NewScript(acquireTokensLuaScript)
    rtb.refillScript = redis.NewScript(refillTokensLuaScript)
    
    return rtb
}

// Lua script for atomic token acquisition
const acquireTokensLuaScript = `
local key = KEYS[1]
local capacity = tonumber(ARGV[1])
local tokens_requested = tonumber(ARGV[2])
local refill_rate_ms = tonumber(ARGV[3])
local current_time = tonumber(ARGV[4])

-- Get current state
local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
local current_tokens = tonumber(bucket[1]) or capacity
local last_refill = tonumber(bucket[2]) or current_time

-- Calculate tokens to add based on time elapsed
local time_elapsed = current_time - last_refill
local tokens_to_add = math.floor(time_elapsed / refill_rate_ms)

-- Refill tokens (capped at capacity)
current_tokens = math.min(capacity, current_tokens + tokens_to_add)

-- Check if we can acquire the requested tokens
if current_tokens >= tokens_requested then
    current_tokens = current_tokens - tokens_requested
    
    -- Update Redis state
    redis.call('HMSET', key, 
        'tokens', current_tokens,
        'last_refill', current_time,
        'last_access', current_time
    )
    redis.call('EXPIRE', key, 3600) -- 1 hour TTL
    
    return {1, current_tokens} -- Success
else
    -- Update last access time even on failure
    redis.call('HMSET', key,
        'tokens', current_tokens,
        'last_refill', current_time,
        'last_access', current_time
    )
    redis.call('EXPIRE', key, 3600)
    
    return {0, current_tokens} -- Failure
end
`

func (rtb *RedisTokenBucket) TryAcquire(ctx context.Context, key string, tokens int64) (bool, error) {
    redisKey := rtb.keyPrefix + key
    currentTime := time.Now().UnixMilli()
    refillRateMs := rtb.refillRate.Milliseconds()
    
    result, err := rtb.acquireScript.Run(ctx, rtb.client, 
        []string{redisKey}, 
        rtb.capacity, tokens, refillRateMs, currentTime,
    ).Result()
    
    if err != nil {
        return false, fmt.Errorf("redis token acquisition failed: %w", err)
    }
    
    resultSlice := result.([]interface{})
    success := resultSlice[0].(int64) == 1
    
    return success, nil
}
```

### 3. Distributed Quota Tracker

```go
type RedisQuotaTracker struct {
    client    redis.Cmdable
    keyPrefix string
    config    *QuotaConfig
    
    quotaScript *redis.Script
}

type QuotaConfig struct {
    DailyLimit   int64
    Per100sLimit int64
    UserLimit    int64
}

func NewRedisQuotaTracker(client redis.Cmdable, config *QuotaConfig) *RedisQuotaTracker {
    rqt := &RedisQuotaTracker{
        client:    client,
        keyPrefix: "gdrive:quota:",
        config:    config,
    }
    
    rqt.quotaScript = redis.NewScript(quotaCheckLuaScript)
    return rqt
}

const quotaCheckLuaScript = `
local daily_key = KEYS[1]
local per100s_key = KEYS[2]
local user_key = KEYS[3]

local daily_limit = tonumber(ARGV[1])
local per100s_limit = tonumber(ARGV[2])
local user_limit = tonumber(ARGV[3])
local requests = tonumber(ARGV[4])
local current_time = tonumber(ARGV[5])

-- Check current usage
local daily_usage = tonumber(redis.call('GET', daily_key)) or 0
local per100s_usage = tonumber(redis.call('GET', per100s_key)) or 0
local user_usage = tonumber(redis.call('GET', user_key)) or 0

-- Check if request would exceed limits
if (daily_usage + requests > daily_limit) or 
   (per100s_usage + requests > per100s_limit) or 
   (user_usage + requests > user_limit) then
    return {0, daily_usage, per100s_usage, user_usage} -- Quota exceeded
end

-- Increment usage counters
redis.call('INCRBY', daily_key, requests)
redis.call('INCRBY', per100s_key, requests)
redis.call('INCRBY', user_key, requests)

-- Set TTLs
redis.call('EXPIRE', daily_key, 86400)     -- 24 hours
redis.call('EXPIRE', per100s_key, 100)    -- 100 seconds
redis.call('EXPIRE', user_key, 100)       -- 100 seconds

return {1, daily_usage + requests, per100s_usage + requests, user_usage + requests}
`

func (rqt *RedisQuotaTracker) CheckAndConsumeQuota(ctx context.Context, userID string, requests int64) (*QuotaStatus, error) {
    now := time.Now()
    dailyKey := fmt.Sprintf("%sdaily:%s", rqt.keyPrefix, now.Format("2006-01-02"))
    per100sKey := fmt.Sprintf("%sper100s:%d", rqt.keyPrefix, now.Unix()/100)
    userKey := fmt.Sprintf("%suser:%s:%d", rqt.keyPrefix, userID, now.Unix()/100)
    
    result, err := rqt.quotaScript.Run(ctx, rqt.client,
        []string{dailyKey, per100sKey, userKey},
        rqt.config.DailyLimit, rqt.config.Per100sLimit, rqt.config.UserLimit,
        requests, now.Unix(),
    ).Result()
    
    if err != nil {
        return nil, fmt.Errorf("quota check failed: %w", err)
    }
    
    resultSlice := result.([]interface{})
    allowed := resultSlice[0].(int64) == 1
    dailyUsage := resultSlice[1].(int64)
    per100sUsage := resultSlice[2].(int64)
    userUsage := resultSlice[3].(int64)
    
    return &QuotaStatus{
        Allowed:      allowed,
        DailyUsage:   dailyUsage,
        Per100sUsage: per100sUsage,
        UserUsage:    userUsage,
    }, nil
}
```

### 4. Multi-Level Distributed Limiter

```go
type MultiLevelDistributedLimiter struct {
    globalLimiter *RedisTokenBucket
    userLimiter   *RedisTokenBucket
    quotaTracker  *RedisQuotaTracker
    
    config *RateLimitConfig
    nodeID string
}

type RateLimitConfig struct {
    GlobalRPS     float64
    UserRPS       float64
    BurstSize     int64
    QueueTimeout  time.Duration
    RetryAttempts int
    BackoffBase   time.Duration
    
    // Redis configuration
    RedisAddr     string
    RedisPassword string
    RedisDB       int
    RedisPoolSize int
}

func NewMultiLevelDistributedLimiter(config *RateLimitConfig) (*MultiLevelDistributedLimiter, error) {
    // Create Redis client
    rdb := redis.NewClient(&redis.Options{
        Addr:     config.RedisAddr,
        Password: config.RedisPassword,
        DB:       config.RedisDB,
        PoolSize: config.RedisPoolSize,
    })
    
    // Test connection
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    if err := rdb.Ping(ctx).Err(); err != nil {
        return nil, fmt.Errorf("redis connection failed: %w", err)
    }
    
    // Create distributed limiters
    globalRefillRate := time.Duration(float64(time.Second) / config.GlobalRPS)
    userRefillRate := time.Duration(float64(time.Second) / config.UserRPS)
    
    return &MultiLevelDistributedLimiter{
        globalLimiter: NewRedisTokenBucket(rdb, config.BurstSize, globalRefillRate),
        userLimiter:   NewRedisTokenBucket(rdb, config.BurstSize/10, userRefillRate),
        quotaTracker: NewRedisQuotaTracker(rdb, &QuotaConfig{
            DailyLimit:   1000000000,
            Per100sLimit: 10000,
            UserLimit:    1000,
        }),
        config: config,
        nodeID: generateNodeID(),
    }, nil
}

func (mdl *MultiLevelDistributedLimiter) Wait(ctx context.Context, userID string) error {
    // Check quota first
    quotaStatus, err := mdl.quotaTracker.CheckAndConsumeQuota(ctx, userID, 1)
    if err != nil {
        return fmt.Errorf("quota check failed: %w", err)
    }
    
    if !quotaStatus.Allowed {
        return fmt.Errorf("quota exceeded: daily=%d, per100s=%d, user=%d", 
            quotaStatus.DailyUsage, quotaStatus.Per100sUsage, quotaStatus.UserUsage)
    }
    
    // Try global limiter first
    acquired, err := mdl.globalLimiter.TryAcquire(ctx, "global", 1)
    if err != nil {
        return fmt.Errorf("global limiter error: %w", err)
    }
    
    if !acquired {
        // Wait with timeout
        ticker := time.NewTicker(100 * time.Millisecond)
        defer ticker.Stop()
        
        timeoutCtx, cancel := context.WithTimeout(ctx, mdl.config.QueueTimeout)
        defer cancel()
        
        for {
            select {
            case <-timeoutCtx.Done():
                return fmt.Errorf("global rate limit timeout")
            case <-ticker.C:
                acquired, err := mdl.globalLimiter.TryAcquire(ctx, "global", 1)
                if err != nil {
                    return fmt.Errorf("global limiter error: %w", err)
                }
                if acquired {
                    goto checkUser
                }
            }
        }
    }
    
checkUser:
    // Try user limiter
    userKey := fmt.Sprintf("user:%s", userID)
    acquired, err = mdl.userLimiter.TryAcquire(ctx, userKey, 1)
    if err != nil {
        return fmt.Errorf("user limiter error: %w", err)
    }
    
    if !acquired {
        // Wait with timeout for user limiter
        ticker := time.NewTicker(100 * time.Millisecond)
        defer ticker.Stop()
        
        timeoutCtx, cancel := context.WithTimeout(ctx, mdl.config.QueueTimeout)
        defer cancel()
        
        for {
            select {
            case <-timeoutCtx.Done():
                return fmt.Errorf("user rate limit timeout")
            case <-ticker.C:
                acquired, err := mdl.userLimiter.TryAcquire(ctx, userKey, 1)
                if err != nil {
                    return fmt.Errorf("user limiter error: %w", err)
                }
                if acquired {
                    return nil
                }
            }
        }
    }
    
    return nil
}
```

## Implementation Details

### 1. Redis Integration in Client

```go
// In pkg/gdrive/client.go
type Client struct {
    service           *drive.Service
    distributedLimiter *MultiLevelDistributedLimiter
    config            *Config
}

func NewClient(ctx context.Context, config *Config) (*Client, error) {
    // Create distributed rate limiter
    limiter, err := NewMultiLevelDistributedLimiter(config.RateLimit)
    if err != nil {
        return nil, fmt.Errorf("failed to create distributed limiter: %w", err)
    }
    
    return &Client{
        service:           driveService,
        distributedLimiter: limiter,
        config:            config,
    }, nil
}

func (c *Client) makeRequest(ctx context.Context, userID string, req func() error) error {
    // Wait for distributed rate limiter permission
    if err := c.distributedLimiter.Wait(ctx, userID); err != nil {
        return fmt.Errorf("distributed rate limiter wait failed: %w", err)
    }
    
    // Execute request with retry logic
    return c.executeWithRetry(ctx, req)
}
```

### 2. Configuration Management

```go
type RateLimitConfig struct {
    // Global limits
    GlobalQPS           float64 `yaml:"global_qps" env:"GDRIVE_GLOBAL_QPS" default:"100"`
    GlobalBurst         int64   `yaml:"global_burst" env:"GDRIVE_GLOBAL_BURST" default:"200"`
    
    // Per-user limits
    UserQPS             float64 `yaml:"user_qps" env:"GDRIVE_USER_QPS" default:"10"`
    UserBurst           int64   `yaml:"user_burst" env:"GDRIVE_USER_BURST" default:"20"`
    
    // Quota tracking
    DailyQuotaLimit     int64   `yaml:"daily_quota_limit" env:"GDRIVE_DAILY_QUOTA" default:"1000000000"`
    Per100sQuotaLimit   int64   `yaml:"per_100s_quota_limit" env:"GDRIVE_100S_QUOTA" default:"10000"`
    UserQuotaLimit      int64   `yaml:"user_quota_limit" env:"GDRIVE_USER_QUOTA" default:"1000"`
    
    // Retry configuration
    RetryAttempts       int           `yaml:"retry_attempts" env:"GDRIVE_RETRY_ATTEMPTS" default:"3"`
    BackoffBase         time.Duration `yaml:"backoff_base" env:"GDRIVE_BACKOFF_BASE" default:"1s"`
    QueueTimeout        time.Duration `yaml:"queue_timeout" env:"GDRIVE_QUEUE_TIMEOUT" default:"30s"`
    
    // Redis configuration
    RedisAddr          string `yaml:"redis_addr" env:"GDRIVE_REDIS_ADDR" default:"localhost:6379"`
    RedisPassword      string `yaml:"redis_password" env:"GDRIVE_REDIS_PASSWORD"`
    RedisDB            int    `yaml:"redis_db" env:"GDRIVE_REDIS_DB" default:"0"`
    RedisPoolSize      int    `yaml:"redis_pool_size" env:"GDRIVE_REDIS_POOL_SIZE" default:"10"`
    RedisConnTimeout   time.Duration `yaml:"redis_conn_timeout" env:"GDRIVE_REDIS_CONN_TIMEOUT" default:"5s"`
    RedisReadTimeout   time.Duration `yaml:"redis_read_timeout" env:"GDRIVE_REDIS_READ_TIMEOUT" default:"3s"`
    RedisWriteTimeout  time.Duration `yaml:"redis_write_timeout" env:"GDRIVE_REDIS_WRITE_TIMEOUT" default:"3s"`
}
```

### 3. Health Checks and Monitoring

```go
type HealthChecker struct {
    limiter *MultiLevelDistributedLimiter
}

func (hc *HealthChecker) CheckRedisHealth(ctx context.Context) error {
    // Test Redis connectivity
    testKey := "gdrive:health_check:" + generateNodeID()
    
    // Try to set and get a test value
    err := hc.limiter.globalLimiter.client.Set(ctx, testKey, "ok", time.Minute).Err()
    if err != nil {
        return fmt.Errorf("redis write failed: %w", err)
    }
    
    val, err := hc.limiter.globalLimiter.client.Get(ctx, testKey).Result()
    if err != nil {
        return fmt.Errorf("redis read failed: %w", err)
    }
    
    if val != "ok" {
        return fmt.Errorf("redis value mismatch")
    }
    
    // Clean up
    hc.limiter.globalLimiter.client.Del(ctx, testKey)
    
    return nil
}

// Metrics for distributed rate limiting
type DistributedMetrics struct {
    // Redis metrics
    RedisConnections    prometheus.GaugeVec
    RedisLatency       prometheus.HistogramVec
    RedisErrors        prometheus.CounterVec
    
    // Rate limiting metrics
    TokensAcquired     prometheus.CounterVec
    TokensRejected     prometheus.CounterVec
    QuotaUsage         prometheus.GaugeVec
    
    // Node metrics
    NodeRequests       prometheus.CounterVec
}
```

## Testing Strategy

### 1. Unit Tests with Redis Mock

```go
func TestRedisTokenBucket_TryAcquire(t *testing.T) {
    // Use miniredis for testing
    mr, err := miniredis.Run()
    require.NoError(t, err)
    defer mr.Close()
    
    client := redis.NewClient(&redis.Options{
        Addr: mr.Addr(),
    })
    
    tb := NewRedisTokenBucket(client, 10, time.Second)
    
    ctx := context.Background()
    
    // Test successful acquisition
    acquired, err := tb.TryAcquire(ctx, "test", 5)
    assert.NoError(t, err)
    assert.True(t, acquired)
    
    // Test insufficient tokens
    acquired, err = tb.TryAcquire(ctx, "test", 10)
    assert.NoError(t, err)
    assert.False(t, acquired)
}

func TestDistributedQuotaTracker(t *testing.T) {
    mr, err := miniredis.Run()
    require.NoError(t, err)
    defer mr.Close()
    
    client := redis.NewClient(&redis.Options{
        Addr: mr.Addr(),
    })
    
    tracker := NewRedisQuotaTracker(client, &QuotaConfig{
        DailyLimit:   1000,
        Per100sLimit: 100,
        UserLimit:    10,
    })
    
    ctx := context.Background()
    
    // Test quota consumption
    status, err := tracker.CheckAndConsumeQuota(ctx, "user1", 5)
    assert.NoError(t, err)
    assert.True(t, status.Allowed)
    assert.Equal(t, int64(5), status.UserUsage)
}
```

### 2. Integration Tests with Multiple Nodes

```go
func TestDistributedRateLimiter_MultiNode(t *testing.T) {
    // Start Redis container for testing
    redisContainer := setupRedisContainer(t)
    defer redisContainer.Terminate(context.Background())
    
    redisAddr := redisContainer.GetConnectionString()
    
    // Create multiple limiter instances (simulating different nodes)
    config := &RateLimitConfig{
        GlobalQPS:    10,
        UserQPS:      2,
        BurstSize:    20,
        RedisAddr:    redisAddr,
        QueueTimeout: 5 * time.Second,
    }
    
    limiter1, err := NewMultiLevelDistributedLimiter(config)
    require.NoError(t, err)
    defer limiter1.Close()
    
    limiter2, err := NewMultiLevelDistributedLimiter(config)
    require.NoError(t, err)
    defer limiter2.Close()
    
    // Test that both nodes respect the same global limit
    ctx := context.Background()
    var successCount atomic.Int64
    var wg sync.WaitGroup
    
    // Start requests from both nodes
    for i := 0; i < 2; i++ {
        limiter := limiter1
        if i == 1 {
            limiter = limiter2
        }
        
        wg.Add(1)
        go func(l *MultiLevelDistributedLimiter, nodeID int) {
            defer wg.Done()
            
            for j := 0; j < 50; j++ {
                err := l.Wait(ctx, fmt.Sprintf("user%d", j%5))
                if err == nil {
                    successCount.Add(1)
                }
                time.Sleep(10 * time.Millisecond)
            }
        }(limiter, i)
    }
    
    wg.Wait()
    
    // Verify that total successful requests respect global rate limit
    totalSuccess := successCount.Load()
    t.Logf("Total successful requests: %d", totalSuccess)
    
    // Should be around the expected rate limit
    assert.Less(t, totalSuccess, int64(80), "Too many requests succeeded")
    assert.Greater(t, totalSuccess, int64(20), "Too few requests succeeded")
}
```

## Deployment Considerations

### 1. Redis Configuration

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  docman:
    image: docman:latest
    environment:
      - GDRIVE_REDIS_ADDR=redis:6379
      - GDRIVE_GLOBAL_QPS=50
      - GDRIVE_USER_QPS=5
    depends_on:
      - redis
    deploy:
      replicas: 3  # Multiple nodes

volumes:
  redis_data:
```

### 2. Environment Variables

```bash
# Production settings
GDRIVE_REDIS_ADDR=redis-cluster.prod:6379
GDRIVE_REDIS_PASSWORD=secure_password
GDRIVE_REDIS_DB=0
GDRIVE_REDIS_POOL_SIZE=20
GDRIVE_GLOBAL_QPS=50
GDRIVE_USER_QPS=5

# Development settings
GDRIVE_REDIS_ADDR=localhost:6379
GDRIVE_REDIS_DB=1
GDRIVE_GLOBAL_QPS=10
GDRIVE_USER_QPS=2
```

### 3. Redis Monitoring

```yaml
# Prometheus alerts for Redis
groups:
  - name: redis-rate-limiting
    rules:
      - alert: RedisConnectionFailed
        expr: redis_connected_clients == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection lost"
          
      - alert: RedisHighLatency
        expr: redis_command_duration_seconds{quantile="0.95"} > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Redis high latency detected"
          
      - alert: RedisMemoryUsageHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage is high"
```

## Future Enhancements

1. **Redis Cluster Support**: Scale Redis horizontally for high availability
2. **Circuit Breaker**: Temporarily halt requests when Redis is unavailable
3. **Fallback to Local Limiting**: Use in-memory limiting when Redis is down
4. **Request Prioritization**: Priority queues for different request types
5. **Quota Forecasting**: Predict quota usage to prevent exhaustion
6. **Cross-Region Rate Limiting**: Coordinate limits across multiple regions

## References

- [Google Drive API Quotas](https://developers.google.com/drive/api/guides/limits)
- [Redis Rate Limiting Patterns](https://redis.io/docs/manual/patterns/distributed-locks/)
- [Distributed Rate Limiting](https://cloud.google.com/architecture/rate-limiting-strategies-techniques)
- [Token Bucket Algorithm](https://en.wikipedia.org/wiki/Token_bucket) 
 