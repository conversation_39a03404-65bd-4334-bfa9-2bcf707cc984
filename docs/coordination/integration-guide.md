# Coordination System Integration Guide

## Overview

This comprehensive guide explains how to:
1. **Integrate the coordination system** into existing event processing
2. **Add new event types** to the coordination system
3. **Add new providers** for folder operations
4. **Test and debug** integrations
5. **Deploy to production** safely

## Table of Contents

1. [System Integration](#system-integration)
2. [Adding New Event Types](#adding-new-event-types)
3. [Adding New Providers](#adding-new-providers)
4. [Testing Integration](#testing-integration)
5. [Troubleshooting](#troubleshooting)
6. [Quick Reference](#quick-reference)

## System Integration

## Current vs Coordinated Flow

### Current Flow (Without Coordination)
```
HTTP Request → AutoDocEventConsumer → EventRuleMatchingService → MatterEventConsumer → RuleMatchingService → RuleExecutionEngine
                                                                                                                    ↓
                                                                                                            Autodoc Rules Execute
                                                                                                            (May fail if folders don't exist)

Separately:
HTTP Request → GDrive/SharePoint/Internal Handlers → MatterEventConsumer.HandleMatterCreated() → FolderService.CreateMatterFolder()
```

### Coordinated Flow (With Coordination)
```
HTTP Request → AutoDocEventConsumer → CoordinatedEventRuleMatchingService → MatterEventCoordinator
                                                                                        ↓
                                                                            Initialize Coordination Tracking
                                                                                        ↓
                                                                    Trigger All Provider Operations (Parallel)
                                                                                        ↓
                                                            ┌─────────────────────────┼─────────────────────────┐
                                                            ↓                         ↓                         ↓
                                                    GDrive Operation          SharePoint Operation      Internal Operation
                                                            ↓                         ↓                         ↓
                                                    Notify Completion         Notify Completion         Notify Completion
                                                            ↓                         ↓                         ↓
                                                            └─────────────────────────┼─────────────────────────┘
                                                                                      ↓
                                                                        Check All Providers Complete
                                                                                      ↓
                                                                        Trigger Autodoc Rules Handler
                                                                                      ↓
                                                                        EventRuleMatchingService.ProcessEvent()
                                                                                      ↓
                                                                            Autodoc Rules Execute
                                                                            (Guaranteed folders exist)
```

## Adding New Event Types

When you need to add a new event type (e.g., `document.create`) to the coordination system:

### Step 1: Add Event Type to Router

Update `internal/coordination/integration/event_processing_integration.go`:

```go
// In CoordinatedEventRuleMatchingService.ProcessEvent()
switch eventType {
case "matter.create":
    return c.processCoordinatedMatterCreate(ctx, tenantID, eventData)
case "matter.update":
    return c.processCoordinatedMatterUpdate(ctx, tenantID, eventData)
case "client.create":
    return c.processCoordinatedClientCreate(ctx, tenantID, eventData)
case "client.update":
    return c.processCoordinatedClientUpdate(ctx, tenantID, eventData)
case "document.create":  // NEW EVENT TYPE
    log.Info("Using coordinated processing for document.create event")
    return c.processCoordinatedDocumentCreate(ctx, tenantID, eventData)
default:
    // Non-coordinated events use original service
    return c.originalService.ProcessEvent(ctx, tenantID, eventType, eventData)
}
```

### Step 2: Create Event Coordinator (if needed)

**Option A: Create New Coordinator**

If the new event needs separate coordination logic:

```go
// internal/coordination/document_event_coordinator.go
type DocumentEventCoordinator interface {
    ProcessDocumentCreateEvent(ctx context.Context, req *DocumentCreateEventRequest) error
    RegisterProviderOperation(provider string, operation ProviderOperation)
}

type DocumentCreateEventRequest struct {
    TenantID    uint64
    DocumentID  uint64
    MatterID    uint64  // Parent entity if applicable
    EventID     string
    EventData   map[string]interface{}
}
```

**Option B: Reuse Existing Coordinator**

If logic is similar to existing events:

```go
// Reuse MatterEventCoordinator for document events
func (c *CoordinatedEventRuleMatchingService) processCoordinatedDocumentCreate(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
    // Convert to MatterCreateEventRequest format
    req := &coordination.MatterCreateEventRequest{
        TenantID:  tenantID,
        MatterID:  extractDocumentID(eventData),
        ClientID:  extractMatterID(eventData),
        EventID:   generateEventID("document_create", eventData),
        EventData: eventData,
    }
    return c.matterEventCoordinator.ProcessMatterCreateEvent(ctx, req)
}
```

### Step 3: Update Event Type Mapping

Update `internal/handlers/autodoc_handler.go`:

```go
func (h *AutodocRulesHandler) determineEventType(operationType, entityType string) string {
    switch {
    case operationType == "matter_folder_creation" && entityType == "matter":
        return "matter.create"
    case operationType == "matter_folder_update" && entityType == "matter":
        return "matter.update"
    case operationType == "client_folder_creation" && entityType == "client":
        return "client.create"
    case operationType == "client_folder_update" && entityType == "client":
        return "client.update"
    case operationType == "document_processing" && entityType == "document":  // NEW
        return "document.create"
    default:
        return ""
    }
}
```

### Step 4: Register with Service Registry

Update `internal/coordination/service_registry.go`:

```go
// Add DocumentEventCoordinator to CoordinationServices struct
type CoordinationServices struct {
    OperationCoordinator   OperationCoordinator
    ProviderConfigService  ProviderConfigService
    DownstreamRegistry     DownstreamActionRegistry
    MatterEventCoordinator MatterEventCoordinator
    ClientEventCoordinator ClientEventCoordinator
    DocumentEventCoordinator DocumentEventCoordinator  // NEW
    // ... other fields
}

// In NewCoordinationServices()
documentEventCoordinator := NewDocumentEventCoordinator(operationCoordinator, providerConfigService)

// Register provider operations
documentEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
documentEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
documentEventCoordinator.RegisterProviderOperation("internal", internalOperation)
```

## Adding New Providers

When you need to add a new provider (e.g., Microsoft Teams) to the coordination system:

### Step 1: Create Provider Operation

```go
// internal/coordination/teams_provider_operation.go
type TeamsProviderOperation struct {
    teamsConsumer TeamsEventConsumer
}

func NewTeamsProviderOperation(consumer TeamsEventConsumer) ProviderOperation {
    return &TeamsProviderOperation{
        teamsConsumer: consumer,
    }
}

func (t *TeamsProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
    log := bilabllog.CreateContextLogger(ctx)
    log.Info("Executing Teams operation", map[string]interface{}{
        "operation_id": req.OperationID,
        "entity_id":    req.EntityID,
        "tenant_id":    req.TenantID,
    })

    // Parse payload and call Teams consumer
    var payload map[string]interface{}
    if err := json.Unmarshal([]byte(req.PayloadJSON), &payload); err != nil {
        return fmt.Errorf("failed to parse payload: %w", err)
    }

    return t.teamsConsumer.ConsumeMatterCreated(ctx, req.TenantID, payload)
}
```

### Step 2: Register Provider in Service Registry

```go
// In NewCoordinationServices()
teamsOperation := NewTeamsProviderOperation(config.TeamsConsumer)

// Register with all coordinators
matterEventCoordinator.RegisterProviderOperation("teams", teamsOperation)
clientEventCoordinator.RegisterProviderOperation("teams", teamsOperation)
documentEventCoordinator.RegisterProviderOperation("teams", teamsOperation)

// Add to CoordinationServices struct
return &CoordinationServices{
    // ... existing fields
    TeamsOperation: teamsOperation,  // NEW
}
```

### Step 3: Update Provider Config Service

```go
// internal/coordination/provider_config_service.go
func (p *providerConfigService) GetEnabledProviders(ctx context.Context, tenantID uint64, operationType string) ([]string, []string, error) {
    var required, optional []string

    // Always include internal
    required = append(required, "internal")

    // Check Teams config
    if teamsEnabled, _ := p.isTeamsEnabled(ctx, tenantID); teamsEnabled {
        required = append(required, "teams")
    }

    // Check GDrive config
    if gdriveEnabled, _ := p.isGDriveEnabled(ctx, tenantID); gdriveEnabled {
        required = append(required, "gdrive")
    }

    // Check SharePoint config
    if spEnabled, _ := p.isSharePointEnabled(ctx, tenantID); spEnabled {
        required = append(required, "sharepoint")
    }

    return required, optional, nil
}

func (p *providerConfigService) isTeamsEnabled(ctx context.Context, tenantID uint64) (bool, error) {
    setting, err := p.docSettingRepo.FindByTenantAndKey(ctx, tenantID, "teams_config")
    if err != nil {
        return false, err
    }

    var config struct {
        Enabled bool `json:"enabled"`
    }

    if err := json.Unmarshal([]byte(setting.Value), &config); err != nil {
        return false, err
    }

    return config.Enabled, nil
}
```

### Step 4: Update Integration Config

```go
// internal/coordination/integration/event_processing_integration.go
type CoordinationIntegrationConfig struct {
    // ... existing fields
    TeamsConsumer TeamsEventConsumer  // NEW
}

// In SetupCoordinationIntegration()
if config.TeamsConsumer != nil {
    teamsOperation := coordination.NewTeamsProviderOperation(config.TeamsConsumer)
    config.CoordinationServices.MatterEventCoordinator.RegisterProviderOperation("teams", teamsOperation)
    config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("teams", teamsOperation)
}
```

## Testing Integration

### Unit Test Template

```go
func TestYourEventCoordination(t *testing.T) {
    // Setup
    mockCoordinator := &MockEventCoordinator{}
    service := &CoordinatedEventRuleMatchingService{
        yourEventCoordinator: mockCoordinator,
    }

    // Test data
    eventData := map[string]interface{}{
        "entity_id": 123,
        "tenant_id": 456,
    }

    // Execute
    err := service.ProcessEvent(context.Background(), 456, "your.event", eventData)

    // Assert
    assert.NoError(t, err)
    mockCoordinator.AssertCalled(t, "ProcessYourEvent", mock.Anything, mock.Anything)
}
```

### Integration Test Template

```go
func TestYourProviderIntegration(t *testing.T) {
    // Setup coordination services
    coordinationServices := setupTestCoordinationServices()

    // Register your provider
    yourOperation := NewYourProviderOperation(mockYourConsumer)
    coordinationServices.MatterEventCoordinator.RegisterProviderOperation("your_provider", yourOperation)

    // Test coordination
    req := &MatterCreateEventRequest{
        TenantID: 123,
        MatterID: 456,
        EventID:  "test_event",
    }

    err := coordinationServices.MatterEventCoordinator.ProcessMatterCreateEvent(context.Background(), req)
    assert.NoError(t, err)

    // Verify your provider was called
    mockYourConsumer.AssertCalled(t, "ConsumeEvent", mock.Anything, mock.Anything, mock.Anything)
}
```

### Testing Checklist

When adding new integrations, ensure you:

- [ ] **Unit tests** for new coordinator/provider operations
- [ ] **Integration tests** for event routing
- [ ] **Database tests** for operation tracking
- [ ] **Error handling tests** for failure scenarios
- [ ] **Performance tests** for parallel execution

## Troubleshooting

### Common Issues and Solutions

#### Event Not Being Coordinated
**Symptoms**: Event goes directly to original service instead of coordination

**Solutions**:
- Check switch case in `CoordinatedEventRuleMatchingService.ProcessEvent()`
- Verify event type string matches exactly (case-sensitive)
- Check logs for "Using original processing for non-coordinated event"

#### Provider Not Triggered
**Symptoms**: Operation completes but provider operation not executed

**Solutions**:
- Verify provider registration in service registry
- Check provider config service returns provider as enabled
- Verify provider operation implements ProviderOperation interface correctly
- Check logs for provider operation execution

#### Database Errors
**Symptoms**: "no such table: operation_coordination_statuses" or similar

**Solutions**:
- Ensure database migration includes OperationCoordinationStatus model
- Check AutoMigrate includes all coordination models
- Verify database connection and permissions

#### Timeout Issues
**Symptoms**: Operations fail with timeout errors

**Solutions**:
- Adjust timeout configuration in operation coordinator
- Check provider operation performance
- Consider making slow providers optional instead of required

#### Race Conditions Still Occurring
**Symptoms**: Autodoc rules fail because folders don't exist

**Solutions**:
- Ensure coordination is enabled for the event type
- Verify all folder creation providers are registered
- Check operation completion logic waits for all providers

### Debugging Tips

#### Enable Debug Logging
```go
// Add to main.go or handler setup
bilabllog.SetLevel(logrus.DebugLevel)
```

#### Check Operation Status
```sql
-- Query coordination status
SELECT * FROM operation_coordination_statuses
WHERE operation_id = 'your_operation_id';

-- Check provider statuses
SELECT provider_name, status, error_message, completed_at
FROM operation_coordination_statuses
WHERE operation_id = 'your_operation_id';
```

#### Monitor Event Flow
Look for these log messages in sequence:
1. "Processing event with coordination"
2. "Using coordinated processing for [event_type] event"
3. "Initializing operation coordination"
4. "Triggering operation for provider"
5. "Provider operation completed successfully"
6. "Triggering downstream action after operation completion"

## Quick Reference

### Adding New Event Type Checklist
- [ ] Add case to ProcessEvent() switch statement
- [ ] Create or reuse event coordinator
- [ ] Update event type mapping in AutodocRulesHandler
- [ ] Register providers with coordinator
- [ ] Add integration tests
- [ ] Update documentation

### Adding New Provider Checklist
- [ ] Create ProviderOperation implementation
- [ ] Register with all coordinators in service registry
- [ ] Update provider config service
- [ ] Add to integration configuration
- [ ] Add provider-specific tests
- [ ] Update documentation

### Key Files to Modify

#### For New Event Types:
- `internal/coordination/integration/event_processing_integration.go` - Event routing
- `internal/handlers/autodoc_handler.go` - Event type mapping
- `internal/coordination/service_registry.go` - Coordinator registration

#### For New Providers:
- `internal/coordination/your_provider_operation.go` - Provider implementation
- `internal/coordination/provider_config_service.go` - Provider configuration
- `internal/coordination/service_registry.go` - Provider registration

### Code Templates

#### New Event Type Router
```go
case "your.event":  // NEW
    log.Info("Using coordinated processing for your.event event")
    return c.processCoordinatedYourEvent(ctx, tenantID, eventData)
```

#### New Provider Operation
```go
type YourProviderOperation struct {
    yourConsumer YourEventConsumer
}

func (y *YourProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
    // Implementation
}
```

#### Provider Registration
```go
yourOperation := NewYourProviderOperation(config.YourConsumer)
matterEventCoordinator.RegisterProviderOperation("your_provider", yourOperation)
```

### Monitoring Queries

```sql
-- Check recent operations
SELECT operation_id, overall_status, COUNT(*) as provider_count
FROM operation_coordination_statuses
WHERE created_at > NOW() - INTERVAL 1 HOUR
GROUP BY operation_id, overall_status;

-- Check failed operations
SELECT * FROM operation_coordination_statuses
WHERE overall_status = 'failed'
ORDER BY created_at DESC LIMIT 10;

-- Monitor provider performance
SELECT provider_name, AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)) as avg_duration
FROM operation_coordination_statuses
WHERE status = 'completed' AND completed_at IS NOT NULL
GROUP BY provider_name;
```

### Best Practices

1. **Reuse existing coordinators** when possible
2. **Follow naming conventions** for consistency
3. **Add comprehensive logging** for debugging
4. **Handle errors gracefully** with proper error messages
5. **Use database transactions** for consistency
6. **Test with real providers** before production
7. **Monitor performance** under load
8. **Document new integrations** for team knowledge

### Integration Flow Summary

```
New Event → CoordinatedEventRuleMatchingService
         → Check if coordinated (switch case)
         → Route to appropriate coordinator
         → Initialize operation tracking in database
         → Get enabled providers for tenant
         → Trigger all registered providers in parallel
         → Wait for all providers to complete
         → Execute autodoc rules (downstream action)
         → Update operation status to completed
```

This comprehensive guide provides everything needed to extend the coordination system with new event types and providers while maintaining reliability and performance.

## 🚀 **Unified AutoDoc Endpoints**

### **New Simplified Endpoints**

The AutoDoc system now provides **unified endpoints** that handle multiple event types:

#### **Client Events Unified Endpoint**
- **Endpoint**: `POST /internal/consume/autodoc/client/events`
- **Supports**: Both `client.create` and `client.update` events
- **Request Format**:
  ```json
  {
    "topic": "client.create",  // or "client.update"
    "body": {
      "id": 123,
      "name": "Test Client",
      "tenant_id": 1
    }
  }
  ```

#### **Matter Events Unified Endpoint**
- **Endpoint**: `POST /internal/consume/autodoc/matter/events`
- **Supports**: Both `matter.create` and `matter.update` events
- **Request Format**:
  ```json
  {
    "topic": "matter.create",  // or "matter.update"
    "body": {
      "id": 789,
      "client_id": 123,
      "name": "Test Matter",
      "tenant_id": 1
    }
  }
  ```

### **Endpoint Migration Summary**

| **Event Type** | ❌ **Deprecated** | ⚠️ **Individual** | ✅ **Unified** |
|---------------|------------------|-------------------|----------------|
| client.create | `/gdrive/client/crud` | `/autodoc/client/created` | `/autodoc/client/events` |
| client.update | `/gdrive/client/crud` | `/autodoc/client/updated` | `/autodoc/client/events` |
| matter.create | `/gdrive/matter/crud` | `/autodoc/matter/created` | `/autodoc/matter/events` |
| matter.update | `/gdrive/matter/crud` | `/autodoc/matter/updated` | `/autodoc/matter/events` |

### **Benefits of Unified Endpoints**
- ✅ **Fewer endpoints to maintain** - 2 instead of 4
- ✅ **Consistent request format** - Same structure for create/update
- ✅ **Simplified client integration** - One endpoint per entity type
- ✅ **Backward compatibility** - Individual endpoints still supported
    )
    
    // ... rest of setup ...
}

func setupCoordinationServices(db *gorm.DB, docSettingRepo repositories.DocumentSettingRepository, eventRuleService autodoc.EventRuleMatchingService) *coordination.CoordinationServices {
    // Create coordination repository
    statusRepo := repositories.NewOperationCoordinationStatusRepository(db)
    
    // Create coordination services
    services := coordination.NewCoordinationServiceBuilder().
        WithStatusRepository(statusRepo).
        WithDocSettingRepository(docSettingRepo).
        WithEventRuleMatchingService(eventRuleService).
        Build()
    
    return services
}
```

### Step 2: Database Migration

Add the coordination status table to your database migration:

```go
// In your migration file
func (m *Migration) Up() error {
    // ... existing migrations ...
    
    // Auto-migrate coordination status table
    if err := m.db.AutoMigrate(&model.OperationCoordinationStatus{}); err != nil {
        return fmt.Errorf("failed to migrate operation coordination status: %w", err)
    }
    
    return nil
}
```

### Step 3: Feature Flag Configuration

Add feature flag to control coordination:

```go
// In your configuration
type Config struct {
    // ... existing config ...
    
    Coordination struct {
        Enabled bool `yaml:"enabled" env:"COORDINATION_ENABLED" default:"false"`
    } `yaml:"coordination"`
}
```

### Step 4: Provider Consumer Integration

The existing provider consumers (GDrive, SharePoint, Internal) don't need modification. The coordination system wraps them:

```go
// Existing GDrive consumer continues to work unchanged
type MatterEventConsumer struct {
    folderService    FolderService
    docSettingRepo   repositories.DocumentSettingRepository
    // ... existing fields ...
}

func (c *MatterEventConsumer) HandleMatterCreated(ctx context.Context, payloadJSON string) error {
    // ... existing implementation unchanged ...
    // This method is called by GDriveProviderOperation wrapper
}
```

### Step 5: Gradual Rollout Strategy

#### Phase 1: Deploy with Coordination Disabled
```yaml
coordination:
  enabled: false
```

#### Phase 2: Enable for Test Tenants
```go
// In integration setup
coordinationEnabled := config.Coordination.Enabled || isTestTenant(tenantID)
```

#### Phase 3: Full Rollout
```yaml
coordination:
  enabled: true
```

## Configuration Options

### Environment Variables
```bash
# Enable/disable coordination
COORDINATION_ENABLED=true

# Coordination timeouts
COORDINATION_FOLDER_CREATION_TIMEOUT=5m
COORDINATION_CLEANUP_INTERVAL=1h
```

### Database Configuration
```sql
-- The coordination system will auto-create this table
CREATE TABLE operation_coordination_statuses (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    operation_id VARCHAR(255) NOT NULL UNIQUE,
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT NOT NULL,
    parent_entity_type VARCHAR(50),
    parent_entity_id BIGINT,
    provider_statuses JSONB,
    required_providers JSONB,
    optional_providers JSONB,
    overall_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    completed_at TIMESTAMP,
    downstream_triggered BOOLEAN NOT NULL DEFAULT FALSE,
    downstream_triggered_at TIMESTAMP,
    downstream_action VARCHAR(100),
    error_details JSONB,
    retry_count INTEGER NOT NULL DEFAULT 0,
    last_retry_at TIMESTAMP,
    timeout_at TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_operation_coordination_tenant ON operation_coordination_statuses(tenant_id);
CREATE INDEX idx_operation_coordination_operation ON operation_coordination_statuses(operation_id);
CREATE INDEX idx_operation_coordination_entity ON operation_coordination_statuses(entity_id);
CREATE INDEX idx_operation_coordination_status ON operation_coordination_statuses(overall_status);
```

## Monitoring and Observability

### Metrics to Track
- Coordination success rate
- Average coordination time
- Provider completion rates
- Timeout occurrences
- Autodoc rule execution delays

### Logging
The coordination system provides structured logging:
```json
{
  "level": "info",
  "msg": "Initiating coordinated matter.create processing",
  "event_id": "matter_456_create_1703123456",
  "matter_id": 456,
  "client_id": 789,
  "tenant_id": 123,
  "timestamp": "2023-12-21T10:30:56Z"
}
```

### Health Checks
```go
// Add coordination health check
func (h *HealthHandler) CheckCoordination(ctx context.Context) error {
    // Check if coordination services are healthy
    return coordinationServices.HealthCheck(ctx)
}
```

## Troubleshooting

### Common Issues

#### 1. Coordination Timeouts
**Symptom**: Operations stuck in "pending" status
**Solution**: Check provider health, increase timeout, or investigate slow providers

#### 2. Provider Failures
**Symptom**: Operations marked as "failed"
**Solution**: Check provider logs, verify configurations, implement retry logic

#### 3. Autodoc Rules Not Executing
**Symptom**: Rules not triggered after folder creation
**Solution**: Verify downstream handler registration, check coordination completion

### Debug Commands
```bash
# Check coordination status
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/status/$OPERATION_ID"

# List pending operations
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/pending"

# Retry failed operation
curl -X POST -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/retry/$OPERATION_ID"
```

## Benefits After Integration

1. **Reliability**: Autodoc rules only execute when folders are ready
2. **Consistency**: All providers complete before downstream actions
3. **Visibility**: Clear tracking of multi-provider operations
4. **Resilience**: Graceful handling of provider failures
5. **Backward Compatibility**: Existing code continues to work
6. **Gradual Rollout**: Can be enabled incrementally

## Performance Impact

- **Minimal Overhead**: Coordination adds ~10-50ms per operation
- **Parallel Execution**: Providers run concurrently, not sequentially
- **Database Impact**: One additional table with efficient indexing
- **Memory Usage**: Minimal additional memory for coordination state

The coordination system is designed to be lightweight and efficient while providing robust multi-provider operation management.
