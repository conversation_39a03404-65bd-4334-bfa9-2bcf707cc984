# Generic Multi-Provider Operation Coordinator

## Overview

The Generic Multi-Provider Operation Coordinator solves race conditions between folder creation operations and autodoc rule execution by ensuring all provider operations complete before triggering downstream actions.

## Problem Statement

**Race Condition Issue:**
```
Current Flow:
HTTP Request → AutoDoc Rules Execute → Folder Creation (may not exist yet)
             → GDrive/SharePoint/Internal Operations (parallel, async)
```

**Result:** Autodoc rules fail because folders don't exist when rules execute.

## Solution

**Coordinated Flow:**
```
HTTP Request → Coordination System → Initialize Operation Tracking
                                  → Trigger All Providers (parallel)
                                  → Wait for All Completions  
                                  → Execute Autodoc Rules
                                  → Guaranteed Success
```

## Architecture

### Core Components

#### 1. CoordinatedEventRuleMatchingService
- **Purpose**: Main entry point that routes events to coordination or original service
- **Location**: `internal/coordination/integration/event_processing_integration.go`
- **Key Method**: `ProcessEvent(ctx, tenantID, eventType, eventData)`

#### 2. Event Coordinators
- **MatterEventCoordinator**: Handles matter.create, matter.update events
- **ClientEventCoordinator**: Handles client.create, client.update events
- **Location**: `internal/coordination/`

#### 3. Provider Operations
- **GDriveProviderOperation**: Google Drive folder operations
- **SharePointProviderOperation**: SharePoint folder operations  
- **InternalProviderOperation**: Internal system operations
- **Location**: `internal/coordination/`

#### 4. Operation Coordinator
- **Purpose**: Tracks operation status and triggers downstream actions
- **Database**: `operation_coordination_statuses` table
- **Location**: `internal/coordination/operation_coordinator.go`

#### 5. Provider Config Service
- **Purpose**: Determines which providers are enabled for each tenant
- **Configuration**: Reads from `document_settings` table
- **Location**: `internal/coordination/provider_config_service.go`

### Database Schema

```sql
CREATE TABLE operation_coordination_statuses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    operation_id VARCHAR(255) NOT NULL UNIQUE,
    tenant_id BIGINT NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT NOT NULL,
    operation_type VARCHAR(100) NOT NULL,
    overall_status ENUM('pending', 'completed', 'failed', 'timeout') NOT NULL,
    required_providers JSON NOT NULL,
    optional_providers JSON NOT NULL,
    provider_statuses JSON NOT NULL,
    downstream_action VARCHAR(100) NOT NULL,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

## Supported Events

The system currently coordinates these event types:

### Matter Events
- **matter.create** - Matter creation with folder setup
- **matter.update** - Matter updates requiring folder changes

### Client Events  
- **client.create** - Client creation with folder setup
- **client.update** - Client updates requiring folder changes

### Event Flow
```
Event → CoordinatedEventRuleMatchingService 
     → Check if coordinated (switch case)
     → Route to appropriate coordinator
     → Initialize operation tracking
     → Get enabled providers for tenant
     → Trigger all providers in parallel
     → Wait for all completions
     → Execute autodoc rules
     → Update status to completed
```

## Key Benefits

### 1. Race Condition Elimination
- **Before**: Autodoc rules could execute before folders exist
- **After**: Folders guaranteed to exist when rules execute

### 2. Provider Coordination
- **Parallel Execution**: All providers run simultaneously for performance
- **Completion Tracking**: System waits for all required providers
- **Error Handling**: Failed providers don't block others

### 3. Current Handler Priority
- **Existing Logic First**: Current handlers process before autodoc actions
- **Backward Compatibility**: Non-coordinated events use original service
- **Gradual Rollout**: Can be enabled per tenant or globally

### 4. Automatic Operation Tracking
- **Database Persistence**: All operations tracked in database
- **Status Monitoring**: Real-time status of all provider operations
- **Audit Trail**: Complete history of coordination activities

## Configuration

### Environment Variables
```bash
# Enable/disable coordination system
COORDINATION_ENABLED=true

# Timeout for folder creation operations (seconds)
COORDINATION_FOLDER_CREATION_TIMEOUT=300

# Cleanup interval for old coordination records (seconds)  
COORDINATION_CLEANUP_INTERVAL=3600
```

### Provider Configuration
Each tenant can enable/disable providers via `document_settings`:

```json
// GDrive Configuration
{
  "key": "gdrive_config",
  "value": {
    "enabled": true,
    "root_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "drive_id": "0AEd3EhGff2SaUk9PVA"
  }
}

// SharePoint Configuration  
{
  "key": "sharepoint_config",
  "value": {
    "enabled": true,
    "site_url": "https://company.sharepoint.com/sites/legal",
    "library_name": "Documents"
  }
}
```

## Integration Points

### 1. Handler Setup
```go
// pkg/handlers/handler.go
coordinatedEventRuleMatchingService, err := integration.SetupCoordinationIntegration(&integration.CoordinationIntegrationConfig{
    CoordinationEnabled:                 config.CoordinationEnabled,
    OriginalEventRuleMatchingService:    originalEventRuleMatchingService,
    MatterEventConsumer:                 autoDocMatterEventConsumer,
    ClientEventConsumer:                 autoDocClientEventConsumer,
    CoordinationServices:                coordinationServices,
    GDriveConsumer:                      gdriveConsumer,
    SharePointConsumer:                  sharepointConsumer,
    InternalConsumer:                    internalConsumer,
})
```

### 2. Event Processing
```go
// Events are automatically routed based on event type
switch eventType {
case "matter.create", "matter.update":
    // Routes to MatterEventCoordinator
case "client.create", "client.update":  
    // Routes to ClientEventCoordinator
default:
    // Routes to original EventRuleMatchingService
}
```

### 3. Provider Operations
```go
// Each provider implements ProviderOperation interface
type ProviderOperation interface {
    Execute(ctx context.Context, req *ProviderOperationRequest) error
}

// Providers are registered with coordinators
matterEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
matterEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
matterEventCoordinator.RegisterProviderOperation("internal", internalOperation)
```

## Monitoring

### Log Messages to Monitor
```
✅ "Processing event with coordination"
✅ "Using coordinated processing for [event] event"
✅ "Initializing operation coordination"  
✅ "Triggering operation for provider"
✅ "Provider operation completed successfully"
✅ "Triggering downstream action after operation completion"
```

### Database Monitoring
```sql
-- Check recent operations
SELECT operation_id, overall_status, COUNT(*) as provider_count
FROM operation_coordination_statuses 
WHERE created_at > NOW() - INTERVAL 1 HOUR
GROUP BY operation_id, overall_status;

-- Check failed operations
SELECT * FROM operation_coordination_statuses 
WHERE overall_status = 'failed' 
ORDER BY created_at DESC LIMIT 10;
```

## Files Structure

```
internal/coordination/
├── integration/
│   └── event_processing_integration.go    # Main integration layer
├── matter_event_coordinator.go            # Matter event coordination
├── client_event_coordinator.go            # Client event coordination  
├── operation_coordinator.go               # Operation tracking
├── provider_config_service.go             # Provider configuration
├── service_registry.go                    # Service registration
├── gdrive_provider_operation.go           # GDrive operations
├── sharepoint_provider_operation.go       # SharePoint operations
└── internal_provider_operation.go         # Internal operations

domain/model/
└── operation_coordination.go              # Database models

pkg/repositories/
└── operation_coordination.go              # Database operations
```

## Documentation

### 📖 **Complete Documentation Suite**

1. **[README.md](./README.md)** - **This file** - System overview and architecture
2. **[Integration Guide](./integration-guide.md)** - **Comprehensive guide** for adding new event types and providers
3. **[Deployment Guide](./deployment-guide.md)** - **Production deployment** instructions and configuration

### 🎯 **What Each Document Covers**

#### README.md (This File)
- System overview and problem statement
- Architecture and core components
- Supported events and configuration
- Monitoring and file structure

#### Integration Guide
- Adding new event types (e.g., document.create)
- Adding new providers (e.g., Microsoft Teams)
- Testing templates and strategies
- Troubleshooting common issues
- Quick reference and code templates

#### Deployment Guide
- Production deployment checklist
- Environment configuration
- Database migration
- Gradual rollout strategy
- Monitoring and maintenance

## Quick Start

### For Developers Adding New Integrations
👉 **Go to [Integration Guide](./integration-guide.md)**

### For DevOps/Production Deployment
👉 **Go to [Deployment Guide](./deployment-guide.md)**

### For System Understanding
👉 **Continue reading this README**

## Status

✅ **Production Ready** - All tests passing, comprehensive error handling, backward compatible
✅ **Fully Documented** - Complete integration and deployment guides
✅ **Team Ready** - Clear documentation for developers and DevOps
