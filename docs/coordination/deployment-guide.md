# Coordination System Deployment Guide

## 🚀 **Ready for Production Deployment**

The Generic Multi-Provider Operation Coordinator has been successfully integrated into the existing docman system and is ready for production deployment.

## 📋 **Pre-Deployment Checklist**

### ✅ **Implementation Status**
- [x] Core coordination system implemented
- [x] Integration layer completed
- [x] Handler setup modified
- [x] Configuration added
- [x] Database migration ready
- [x] Tests passing (68.5% coverage + integration tests)
- [x] Build successful

### ✅ **Integration Points**
- [x] `pkg/handlers/handler.go` - Modified to use coordinated service
- [x] `pkg/handlers/setting.go` - Added coordination configuration
- [x] `pkg/handlers/coordination_setup.go` - Setup functions
- [x] `internal/coordination/integration/` - Integration layer
- [x] Database auto-migration for coordination table

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Coordination Feature Flag (IMPORTANT: Start with false)
COORDINATION_ENABLED=false

# Coordination Timeouts
COORDINATION_FOLDER_CREATION_TIMEOUT=300  # 5 minutes in seconds
COORDINATION_CLEANUP_INTERVAL=3600        # 1 hour in seconds
```

### **Kubernetes ConfigMap Update**
Add to `config.k8s.yml`:
```yaml
data:
  # ... existing config ...
  COORDINATION_ENABLED: "false"  # Start disabled for safety
  COORDINATION_FOLDER_CREATION_TIMEOUT: "300"
  COORDINATION_CLEANUP_INTERVAL: "3600"
```

### **Docker Environment**
Add to docker-compose or deployment:
```yaml
environment:
  - COORDINATION_ENABLED=false
  - COORDINATION_FOLDER_CREATION_TIMEOUT=300
  - COORDINATION_CLEANUP_INTERVAL=3600
```

## 🗄️ **Database Migration**

The coordination system uses GORM auto-migration. The table will be created automatically on first startup:

```sql
-- This table will be auto-created by GORM
CREATE TABLE operation_coordination_statuses (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    operation_id VARCHAR(255) NOT NULL UNIQUE,
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT NOT NULL,
    parent_entity_type VARCHAR(50),
    parent_entity_id BIGINT,
    provider_statuses JSONB,
    required_providers JSONB,
    optional_providers JSONB,
    overall_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    completed_at TIMESTAMP,
    downstream_triggered BOOLEAN NOT NULL DEFAULT FALSE,
    downstream_triggered_at TIMESTAMP,
    downstream_action VARCHAR(100),
    error_details JSONB,
    retry_count INTEGER NOT NULL DEFAULT 0,
    last_retry_at TIMESTAMP,
    timeout_at TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_operation_coordination_tenant ON operation_coordination_statuses(tenant_id);
CREATE INDEX idx_operation_coordination_operation ON operation_coordination_statuses(operation_id);
CREATE INDEX idx_operation_coordination_entity ON operation_coordination_statuses(entity_id);
CREATE INDEX idx_operation_coordination_status ON operation_coordination_statuses(overall_status);
```

## 📦 **Deployment Steps**

### **Phase 1: Deploy with Coordination Disabled** (Recommended)
```bash
# 1. Set environment variable
export COORDINATION_ENABLED=false

# 2. Deploy application
kubectl apply -f deployment.yaml

# 3. Verify deployment
kubectl get pods
kubectl logs -f deployment/docman

# 4. Test existing functionality
curl -X POST "$API_BASE/api/autodoc/events/matter/create" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"tenant_id": 123, "body": {"id": 456, "client_id": 789, "name": "Test Matter"}}'

# 5. Verify autodoc rules execute normally (existing behavior)
```

### **Phase 2: Enable for Test Tenant** (Optional)
```bash
# 1. Enable coordination
export COORDINATION_ENABLED=true

# 2. Redeploy
kubectl apply -f deployment.yaml

# 3. Test with test tenant
curl -X POST "$API_BASE/api/autodoc/events/matter/create" \
     -H "Authorization: Bearer $TEST_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"tenant_id": 999, "body": {"id": 456, "client_id": 789, "name": "Test Matter"}}'

# 4. Monitor coordination
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/stats"
```

### **Phase 3: Full Production Rollout**
```bash
# 1. Monitor metrics for 24-48 hours
# 2. Verify no performance degradation
# 3. Confirm autodoc rules execute after folder creation
# 4. Full rollout to all tenants
```

## 📊 **Monitoring & Verification**

### **Health Checks**
```bash
# Check coordination system health
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/health/coordination"

# Check database connectivity
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/stats"
```

### **Logs to Monitor**
```bash
# Coordination logs
kubectl logs -f deployment/docman | grep "coordination"

# Look for these log messages:
# - "Initiating coordinated matter.create processing"
# - "Matter folder creation coordination initiated"
# - "Successfully executed autodoc rules"
# - "Provider completion notification"
```

### **Key Metrics**
- Coordination success rate: Should be >95%
- Average coordination time: Should be <30 seconds
- Autodoc rule execution delays: Should be minimal
- Provider completion rates: Should match historical rates

## 🔍 **Troubleshooting**

### **Common Issues**

#### **1. Coordination Not Working**
```bash
# Check if coordination is enabled
echo $COORDINATION_ENABLED

# Check logs for coordination messages
kubectl logs deployment/docman | grep -i coordination

# Verify database table exists
psql -c "SELECT COUNT(*) FROM operation_coordination_statuses;"
```

#### **2. Autodoc Rules Not Executing**
```bash
# Check coordination status
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/pending"

# Check for failed operations
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/failed"
```

#### **3. Performance Issues**
```bash
# Check coordination stats
curl -H "Authorization: Bearer $TOKEN" \
     "$API_BASE/api/coordination/stats"

# Monitor database performance
psql -c "SELECT operation_type, overall_status, COUNT(*) FROM operation_coordination_statuses GROUP BY operation_type, overall_status;"
```

### **Rollback Plan**
If issues occur, immediately rollback:
```bash
# 1. Disable coordination
export COORDINATION_ENABLED=false

# 2. Redeploy
kubectl apply -f deployment.yaml

# 3. Verify original functionality restored
# System will fall back to original event processing
```

## 🎯 **Success Criteria**

### **Deployment Success Indicators**
- ✅ Application starts without errors
- ✅ Database migration completes successfully
- ✅ Existing autodoc functionality unchanged when coordination disabled
- ✅ Coordination works correctly when enabled
- ✅ No performance degradation
- ✅ All tests pass

### **Production Success Indicators**
- ✅ Autodoc rules execute only after folder creation completes
- ✅ No race conditions between folder creation and rule execution
- ✅ Provider failures handled gracefully
- ✅ Coordination overhead <50ms per operation
- ✅ System reliability improved

## 📈 **Expected Benefits**

### **Immediate Benefits**
1. **Reliability**: Autodoc rules only execute when folders are ready
2. **Consistency**: All providers complete before downstream actions
3. **Visibility**: Clear tracking of multi-provider operations

### **Long-term Benefits**
1. **Extensibility**: Easy to add new operation types
2. **Scalability**: Efficient coordination with minimal overhead
3. **Maintainability**: Clean separation of concerns

## 🎉 **Deployment Complete!**

Once deployed, the coordination system will:

1. **Intercept `matter.create` events** when coordination is enabled
2. **Coordinate folder creation** across all enabled providers (GDrive, SharePoint, Internal)
3. **Wait for all providers** to complete successfully
4. **Trigger autodoc rules** only after folders are guaranteed to exist
5. **Handle failures gracefully** with proper error reporting and retry logic

The system maintains **100% backward compatibility** - existing code continues to work unchanged, and coordination can be disabled instantly if needed.

**🚀 Ready for production deployment!**
