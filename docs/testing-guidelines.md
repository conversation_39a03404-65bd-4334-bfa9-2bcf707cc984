# Testing Guidelines

This document outlines the testing standards and practices for the docman service.

## Mock Generation with Mockery

### Overview

We use [mockery](https://github.com/vektra/mockery) to automatically generate mock implementations for interfaces. This ensures consistency, reduces manual maintenance, and provides better type safety.

### Configuration

Mock generation is configured in `.mockery.yaml`:

```yaml
version: v2.40.1
with-expecter: true
dir: "mocks"
mockname: "Mock{{.InterfaceName}}"
outpkg: "{{.PackageName}}"
filename: "mock_{{.InterfaceName}}.go"
all: true
keeptree: true
exported: true
recursive: true
inpackage: false
testonly: false
log-level: info

packages:
  # Repository interfaces
  bilabl/docman/pkg/repositories:
    interfaces:
      DocumentRepository:
        dir: "mocks/repositories"
      DocumentMappingRepository:
        dir: "mocks/repositories"
      # ... other repository interfaces

  # Service interfaces  
  bilabl/docman/internal/service/gdrive:
    interfaces:
      Service:
        dir: "mocks/service/gdrive"
      DocumentService:
        dir: "mocks/service/gdrive"
      FolderService:
        dir: "mocks/service/gdrive"

  # Google Drive client interfaces
  bilabl/docman/pkg/gdrive:
    interfaces:
      DriveClient:
        dir: "mocks/gdrive"
```

### Generating Mocks

#### Manual Generation
```bash
# Generate all mocks
make mocks

# Or directly with mockery
mockery --config .mockery.yaml
```

#### CI/CD Integration
Mocks should be regenerated automatically in CI/CD pipeline when interfaces change:

```yaml
# Example GitHub Actions step
- name: Generate mocks
  run: make mocks

- name: Check for uncommitted changes
  run: |
    if [[ -n $(git status --porcelain) ]]; then
      echo "Generated mocks have uncommitted changes"
      git diff
      exit 1
    fi
```

### Using Generated Mocks in Tests

#### Import Generated Mocks
```go
import (
    mock_repositories "bilabl/docman/mocks/repositories"
    mock_gdrive "bilabl/docman/mocks/service/gdrive"
)
```

#### Create Mock Instances
```go
func TestSomeFunction(t *testing.T) {
    // Create mock instances
    mockDocumentService := mock_gdrive.NewMockDocumentService(t)
    mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
    
    // Set up expectations
    mockDocumentService.On("CreateDocument", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateDocumentRequest) bool {
        return req.Name == "Test Document" && req.TenantID == testTenantID
    })).Return(expectedResponse, nil).Once()
    
    // Use mocks in your test...
    
    // Verify expectations
    mockDocumentService.AssertExpectations(t)
}
```

### Best Practices

#### DO NOT Manually Implement Mocks
❌ **Wrong:**
```go
// Manual mock implementation
type MockDocumentService struct {
    mock.Mock
}

func (m *MockDocumentService) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error) {
    args := m.Called(ctx, req)
    return args.Get(0).(*DocumentResponse), args.Error(1)
}
```

✅ **Correct:**
```go
// Use generated mock
mockDocumentService := mock_gdrive.NewMockDocumentService(t)
```

#### Keep Mocks Up to Date
- Regenerate mocks when interfaces change
- Include mock generation in pre-commit hooks or CI/CD
- Review generated mocks in code reviews

#### Mock Organization
- Generated mocks are organized by package structure under `mocks/`
- Repository mocks: `mocks/repositories/`
- Service mocks: `mocks/service/gdrive/`
- Client mocks: `mocks/gdrive/`

## Test Structure

### Test File Organization
```
internal/handlers/v3/gdrive/
├── handler.go
├── handler_test.go          # Main test file
├── validation.go
├── validation_test.go       # Validation-specific tests
└── errors.go
```

### Test Naming Conventions
```go
// Function: TestPackage_Function_Scenario
func TestDocumentsHandler_CreateV3_Success_AdminRole(t *testing.T) {}
func TestDocumentsHandler_CreateV3_ValidationError(t *testing.T) {}
func TestDocumentsHandler_CreateV3_AuthorizationError(t *testing.T) {}

// Validation: TestValidateFunction_Scenario  
func TestValidateCreateDocumentRequest_Success(t *testing.T) {}
func TestValidateCreateDocumentRequest_EmptyName(t *testing.T) {}
```

### Test Coverage Requirements
- Minimum 80% test coverage using `testify/assert`
- Include benchmarks and fuzz tests where appropriate
- Test all error paths and edge cases
- Test authorization for different user roles

### Running Tests
```bash
# Run all tests with coverage
go test -race -cover -v -count=1 -timeout=30s ./...

# Run specific test
go test -v ./internal/handlers/v3/gdrive -run TestDocumentsHandler_CreateV3_Success_AdminRole

# Run tests with coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## Error Handling in Tests

### Use Structured Error Assertions
```go
// Test error scenarios
err := ValidateCreateDocumentRequest(ctx, req)
assert.Error(t, err)
assert.Contains(t, err.Error(), "document name is required")
```

### Test HTTP Error Responses
```go
// Verify HTTP status codes
assert.Equal(t, http.StatusBadRequest, w.Code)

// Parse and verify error response structure
var errorResponse struct {
    Error string `json:"error"`
}
err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
assert.NoError(t, err)
assert.Contains(t, errorResponse.Error, "validation failed")
```

## Integration with Development Workflow

### Pre-commit Hooks
```bash
#!/bin/sh
# .git/hooks/pre-commit
make mocks
if [[ -n $(git status --porcelain mocks/) ]]; then
    echo "Mocks were regenerated. Please add them to your commit."
    exit 1
fi
```

### IDE Integration
Most IDEs support running `go generate` commands. Configure your IDE to run `make mocks` when interfaces change.

### Continuous Integration
Include mock generation and validation in your CI pipeline to ensure mocks stay synchronized with interfaces.
