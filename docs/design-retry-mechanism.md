# Google Drive Event Retry Mechanism Design

## Overview

This document outlines the design and implementation strategy for a comprehensive retry mechanism to handle failed Google Drive operations in the docman service. The system will automatically retry failed events with exponential backoff, manage dead letter handling, and provide comprehensive monitoring while following existing project patterns.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Event Handler  │───▶│  Event Consumer │───▶│ Folder Service  │
│     (HTTP)      │    │   (Business)    │    │   (Google API)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Failure Handler │    │   Failure Repo  │
                       │   (Interface)   │───▶│   (Database)    │
                       └─────────────────┘    └─────────────────┘
                              │                        
                              ▼                        
                       ┌─────────────────┐             
                       │ Retry Worker    │             
                       │ (In GDrive Svc) │             
                       └─────────────────┘             
```

## Database Schema Design

### EventFailure Model

```go
type EventFailure struct {
    Model                                    // ID, CreatedAt, UpdatedAt, DeletedAt
    TenantID     uint64    `gorm:"not null;index:idx_event_failure_tenant_status"` 
    EventType    string    `gorm:"size:100;not null;index:idx_event_failure_type_status"` 
    PayloadJSON  string    `gorm:"type:text;not null"` 
    ErrorMessage string    `gorm:"type:text;not null"` 
    RetryCount   int       `gorm:"default:0;not null"` 
    NextRetryAt  time.Time `gorm:"not null;index:idx_event_failure_next_retry"` 
    MaxRetries   int       `gorm:"default:5;not null"` 
    Status       string    `gorm:"size:20;default:'pending';not null;index:idx_event_failure_status"` 
    LastError    string    `gorm:"type:text"` 
}

// TableName ensures consistent table naming
func (EventFailure) TableName() string {
    return "event_failures"
}
```

### Database Migration

Since the project uses GORM auto migrate, the EventFailure model will be automatically migrated when the application starts. The model includes proper GORM tags to ensure correct table structure and constraints:

```go
// Auto migration will be handled in main.go or migration setup
func AutoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        &model.EventFailure{},
        // ... other models
    )
}

// GORM will automatically create indexes based on the tags in the model
// Additional indexes can be created programmatically if needed:
func CreateAdditionalIndexes(db *gorm.DB) error {
    // Composite index for the most common query pattern
    if err := db.Exec(`CREATE INDEX IF NOT EXISTS idx_event_failure_status_next_retry 
                       ON event_failures(status, next_retry_at) 
                       WHERE deleted_at IS NULL`).Error; err != nil {
        return err
    }
    
    // Add constraints programmatically if needed
    if err := db.Exec(`ALTER TABLE event_failures 
                       ADD CONSTRAINT IF NOT EXISTS chk_status 
                       CHECK (status IN ('pending', 'retrying', 'failed', 'success'))`).Error; err != nil {
        return err
    }
    
    return nil
}
```

**Auto Migration Benefits:**
- ✅ Automatic table creation and updates
- ✅ Column type inference from Go types  
- ✅ Index creation from GORM tags
- ✅ Consistent with existing project patterns
- ✅ No manual SQL migration files to maintain

## Core Components

### 1. Exponential Backoff Calculator

```go
type RetryCalculator struct {
    baseDelay    time.Duration
    maxDelay     time.Duration
    multiplier   float64
    jitterEnabled bool
}

func NewRetryCalculator(baseDelay, maxDelay time.Duration, multiplier float64, enableJitter bool) *RetryCalculator {
    return &RetryCalculator{
        baseDelay:     baseDelay,
        maxDelay:      maxDelay,
        multiplier:    multiplier,
        jitterEnabled: enableJitter,
    }
}

// Calculate next retry time with exponential backoff: 1m, 2m, 4m, 8m, 16m (max)
func (rc *RetryCalculator) CalculateNextRetry(retryCount int) time.Time {
    delay := rc.baseDelay
    
    // Calculate exponential delay
    for i := 0; i < retryCount && delay < rc.maxDelay; i++ {
        delay = time.Duration(float64(delay) * rc.multiplier)
    }
    
    // Cap at maximum delay
    if delay > rc.maxDelay {
        delay = rc.maxDelay
    }
    
    // Add jitter to prevent thundering herd problem
    if rc.jitterEnabled {
        jitterRange := float64(delay) * 0.2 // ±20% jitter
        jitter := time.Duration(rand.Float64()*jitterRange*2 - jitterRange)
        delay += jitter
        
        // Ensure delay doesn't go negative
        if delay < 0 {
            delay = rc.baseDelay
        }
    }
    
    return time.Now().Add(delay)
}
```

### 2. Event Failure Repository Interface

```go
type EventFailureRepository interface {
    Create(ctx context.Context, failure *model.EventFailure) error
    GetByID(ctx context.Context, id uint64) (*model.EventFailure, error)
    GetPendingRetries(ctx context.Context, limit int) ([]*model.EventFailure, error)
    UpdateRetryAttempt(ctx context.Context, id uint64, retryCount int, nextRetryAt time.Time, lastError string) error
    MarkAsSuccess(ctx context.Context, id uint64) error
    MarkAsFailed(ctx context.Context, id uint64, finalError string) error
    GetStatistics(ctx context.Context, tenantID uint64) (*RetryStatistics, error)
    CleanupOldEvents(ctx context.Context, successRetention, failureRetention time.Duration) error
}

type RetryStatistics struct {
    TenantID      uint64 `json:"tenant_id"`
    PendingCount  int    `json:"pending_count"`
    RetryingCount int    `json:"retrying_count"`
    FailedCount   int    `json:"failed_count"`
    SuccessCount  int    `json:"success_count"`
    TotalCount    int    `json:"total_count"`
}
```

### 3. Repository Implementation

```go
type eventFailureRepository struct {
    db *gorm.DB
}

func NewEventFailureRepository(db *gorm.DB) repositories.EventFailureRepository {
    return &eventFailureRepository{db: db}
}

func (r *eventFailureRepository) Create(ctx context.Context, failure *model.EventFailure) error {
    return r.db.WithContext(ctx).Create(failure).Error
}

func (r *eventFailureRepository) GetPendingRetries(ctx context.Context, limit int) ([]*model.EventFailure, error) {
    var failures []*model.EventFailure
    err := r.db.WithContext(ctx).
        Where("status = ? AND next_retry_at <= ?", "pending", time.Now()).
        Order("next_retry_at ASC").
        Limit(limit).
        Find(&failures).Error
    return failures, err
}

func (r *eventFailureRepository) UpdateRetryAttempt(ctx context.Context, id uint64, retryCount int, nextRetryAt time.Time, lastError string) error {
    return r.db.WithContext(ctx).Model(&model.EventFailure{}).
        Where("id = ?", id).
        Updates(map[string]interface{}{
            "retry_count":   retryCount,
            "next_retry_at": nextRetryAt,
            "last_error":    lastError,
            "status":        "pending",
            "updated_at":    time.Now(),
        }).Error
}

func (r *eventFailureRepository) MarkAsSuccess(ctx context.Context, id uint64) error {
    return r.db.WithContext(ctx).Model(&model.EventFailure{}).
        Where("id = ?", id).
        Updates(map[string]interface{}{
            "status":     "success",
            "updated_at": time.Now(),
        }).Error
}

func (r *eventFailureRepository) MarkAsFailed(ctx context.Context, id uint64, finalError string) error {
    return r.db.WithContext(ctx).Model(&model.EventFailure{}).
        Where("id = ?", id).
        Updates(map[string]interface{}{
            "status":     "failed",
            "last_error": finalError,
            "updated_at": time.Now(),
        }).Error
}
```

## Error Classification

### Google API Error Classification

```go
type ErrorClassification struct {
    ShouldRetry   bool
    CustomDelay   *time.Duration // Override exponential backoff if needed
    Reason        string
    IsPermanent   bool
}

func ClassifyGoogleDriveError(err error) ErrorClassification {
    // Handle nil error
    if err == nil {
        return ErrorClassification{ShouldRetry: false, Reason: "no_error"}
    }
    
    // Check for Google API errors
    if googleErr, ok := err.(*googleapi.Error); ok {
        return classifyGoogleAPIError(googleErr)
    }
    
    // Check for HTTP errors
    if httpErr, ok := err.(*url.Error); ok {
        return classifyHTTPError(httpErr)
    }
    
    // Check for context errors
    if errors.Is(err, context.DeadlineExceeded) {
        return ErrorClassification{
            ShouldRetry: true,
            Reason:      "timeout",
        }
    }
    
    if errors.Is(err, context.Canceled) {
        return ErrorClassification{
            ShouldRetry: false,
            Reason:      "canceled",
        }
    }
    
    // Default: retry unknown errors
    return ErrorClassification{
        ShouldRetry: true,
        Reason:      "unknown_error",
    }
}

func classifyGoogleAPIError(err *googleapi.Error) ErrorClassification {
    switch err.Code {
    case 400: // Bad Request
        return ErrorClassification{
            ShouldRetry: false,
            IsPermanent: true,
            Reason:      "bad_request",
        }
    case 401: // Unauthorized
        return ErrorClassification{
            ShouldRetry: false,
            IsPermanent: true,
            Reason:      "unauthorized",
        }
    case 403: // Forbidden
        if strings.Contains(err.Message, "quota") {
            // Quota exceeded - retry with longer delay
            delay := time.Hour
            return ErrorClassification{
                ShouldRetry: true,
                CustomDelay: &delay,
                Reason:      "quota_exceeded",
            }
        }
        return ErrorClassification{
            ShouldRetry: false,
            IsPermanent: true,
            Reason:      "forbidden",
        }
    case 404: // Not Found
        return ErrorClassification{
            ShouldRetry: false,
            IsPermanent: true,
            Reason:      "not_found",
        }
    case 429: // Too Many Requests
        delay := 30 * time.Minute
        return ErrorClassification{
            ShouldRetry: true,
            CustomDelay: &delay,
            Reason:      "rate_limited",
        }
    case 500, 502, 503, 504: // Server Errors
        return ErrorClassification{
            ShouldRetry: true,
            Reason:      "server_error",
        }
    default:
        return ErrorClassification{
            ShouldRetry: true,
            Reason:      "unknown_google_error",
        }
    }
}

func classifyHTTPError(err *url.Error) ErrorClassification {
    if err.Timeout() {
        return ErrorClassification{
            ShouldRetry: true,
            Reason:      "network_timeout",
        }
    }
    
    if err.Temporary() {
        return ErrorClassification{
            ShouldRetry: true,
            Reason:      "temporary_network_error",
        }
    }
    
    return ErrorClassification{
        ShouldRetry: false,
        IsPermanent: true,
        Reason:      "network_error",
    }
}
```

## Configuration

### Environment Variables (Following Project Patterns)

```bash
# Google Drive Retry Configuration
DOCMAN_GDRIVE_RETRY_ENABLED=true
DOCMAN_GDRIVE_RETRY_WORKER_INTERVAL=30s
DOCMAN_GDRIVE_RETRY_BATCH_SIZE=50
DOCMAN_GDRIVE_RETRY_MAX_ATTEMPTS=5

# Exponential Backoff Configuration
DOCMAN_GDRIVE_RETRY_BASE_DELAY=1m
DOCMAN_GDRIVE_RETRY_MAX_DELAY=16m
DOCMAN_GDRIVE_RETRY_MULTIPLIER=2.0
DOCMAN_GDRIVE_RETRY_JITTER_ENABLED=true

# Cleanup Configuration
DOCMAN_GDRIVE_CLEANUP_ENABLED=true
DOCMAN_GDRIVE_CLEANUP_INTERVAL=24h
DOCMAN_GDRIVE_CLEANUP_SUCCESS_RETENTION=7d
DOCMAN_GDRIVE_CLEANUP_FAILURE_RETENTION=30d
```

### Configuration Struct

```go
type RetryConfig struct {
    Enabled              bool          `env:"DOCMAN_GDRIVE_RETRY_ENABLED" envDefault:"true"`
    WorkerInterval       time.Duration `env:"DOCMAN_GDRIVE_RETRY_WORKER_INTERVAL" envDefault:"30s"`
    BatchSize            int           `env:"DOCMAN_GDRIVE_RETRY_BATCH_SIZE" envDefault:"50"`
    MaxAttempts          int           `env:"DOCMAN_GDRIVE_RETRY_MAX_ATTEMPTS" envDefault:"5"`
    BaseDelay            time.Duration `env:"DOCMAN_GDRIVE_RETRY_BASE_DELAY" envDefault:"1m"`
    MaxDelay             time.Duration `env:"DOCMAN_GDRIVE_RETRY_MAX_DELAY" envDefault:"16m"`
    Multiplier           float64       `env:"DOCMAN_GDRIVE_RETRY_MULTIPLIER" envDefault:"2.0"`
    JitterEnabled        bool          `env:"DOCMAN_GDRIVE_RETRY_JITTER_ENABLED" envDefault:"true"`
    CleanupEnabled       bool          `env:"DOCMAN_GDRIVE_CLEANUP_ENABLED" envDefault:"true"`
    CleanupInterval      time.Duration `env:"DOCMAN_GDRIVE_CLEANUP_INTERVAL" envDefault:"24h"`
    SuccessRetention     time.Duration `env:"DOCMAN_GDRIVE_CLEANUP_SUCCESS_RETENTION" envDefault:"168h"` // 7 days
    FailureRetention     time.Duration `env:"DOCMAN_GDRIVE_CLEANUP_FAILURE_RETENTION" envDefault:"720h"` // 30 days
}

// Validate ensures configuration values are reasonable
func (c *RetryConfig) Validate() error {
    if c.BatchSize <= 0 {
        return fmt.Errorf("batch size must be positive, got %d", c.BatchSize)
    }
    if c.MaxAttempts <= 0 {
        return fmt.Errorf("max attempts must be positive, got %d", c.MaxAttempts)
    }
    if c.BaseDelay <= 0 {
        return fmt.Errorf("base delay must be positive, got %v", c.BaseDelay)
    }
    if c.MaxDelay < c.BaseDelay {
        return fmt.Errorf("max delay (%v) must be >= base delay (%v)", c.MaxDelay, c.BaseDelay)
    }
    if c.Multiplier <= 1.0 {
        return fmt.Errorf("multiplier must be > 1.0, got %f", c.Multiplier)
    }
    return nil
}
```

## Integration Points

### 1. Failure Handler Interface

```go
// FailureHandler provides a common interface for handling event failures
type FailureHandler interface {
    RecordFailure(ctx context.Context, eventType string, payload interface{}, err error) error
}

type failureHandler struct {
    eventFailureRepo repositories.EventFailureRepository
    retryCalculator  *RetryCalculator
    logger           logger.Logger
}

func NewFailureHandler(
    repo repositories.EventFailureRepository,
    calculator *RetryCalculator,
    logger logger.Logger,
) FailureHandler {
    return &failureHandler{
        eventFailureRepo: repo,
        retryCalculator:  calculator,
        logger:           logger,
    }
}

func (fh *failureHandler) RecordFailure(ctx context.Context, eventType string, payload interface{}, err error) error {
    log := fh.logger.WithCtx(ctx, "RecordFailure").WithFields(map[string]interface{}{
        "event_type": eventType,
        "error":      err.Error(),
    })
    
    // Extract tenant ID from payload
    tenantID, extractErr := extractTenantIDFromPayload(payload)
    if extractErr != nil {
        log.WithError(extractErr).Error("Failed to extract tenant ID from payload")
        return extractErr
    }
    
    // Serialize payload
    payloadBytes, marshalErr := json.Marshal(payload)
    if marshalErr != nil {
        log.WithError(marshalErr).Error("Failed to marshal payload")
        return marshalErr
    }
    
    // Classify error to determine if it should be retried
    classification := ClassifyGoogleDriveError(err)
    if !classification.ShouldRetry {
        log.WithField("reason", classification.Reason).Info("Error classified as non-retryable, skipping failure recording")
        return nil
    }
    
    // Calculate next retry time
    nextRetryAt := fh.retryCalculator.CalculateNextRetry(0)
    if classification.CustomDelay != nil {
        nextRetryAt = time.Now().Add(*classification.CustomDelay)
    }
    
    failure := &model.EventFailure{
        TenantID:     tenantID,
        EventType:    eventType,
        PayloadJSON:  string(payloadBytes),
        ErrorMessage: err.Error(),
        RetryCount:   0,
        NextRetryAt:  nextRetryAt,
        MaxRetries:   5, // Will be configurable
        Status:       "pending",
    }
    
    if err := fh.eventFailureRepo.Create(ctx, failure); err != nil {
        log.WithError(err).Error("Failed to create event failure record")
        return err
    }
    
    log.WithField("failure_id", failure.ID).Info("Event failure recorded for retry")
    return nil
}

func extractTenantIDFromPayload(payload interface{}) (uint64, error) {
    payloadMap, ok := payload.(map[string]interface{})
    if !ok {
        return 0, fmt.Errorf("payload is not a map")
    }
    
    tenantID, exists := payloadMap["tenant_id"]
    if !exists {
        return 0, fmt.Errorf("tenant_id not found in payload")
    }
    
    switch v := tenantID.(type) {
    case float64:
        return uint64(v), nil
    case int:
        return uint64(v), nil
    case int64:
        return uint64(v), nil
    case uint64:
        return v, nil
    default:
        return 0, fmt.Errorf("tenant_id has invalid type: %T", v)
    }
}
```

### 2. Updated Event Consumers

```go
// Update ClientEventConsumer to use FailureHandler
func NewClientEventConsumer(
    folderService FolderService,
    documentSettingRepo repositories.DocumentSettingRepository,
    failureHandler FailureHandler,
) *ClientEventConsumer {
    return &ClientEventConsumer{
        folderService:       folderService,
        documentSettingRepo: documentSettingRepo,
        failureHandler:      failureHandler,
    }
}

func (c *ClientEventConsumer) HandleClientCreated(ctx context.Context, payloadJSON string) error {
    log := logger.WithCtx(ctx, "HandleClientCreated")
    
    // Parse payload
    var payload ClientCreatePayload
    if err := json.Unmarshal([]byte(payloadJSON), &payload); err != nil {
        log.WithError(err).Error("Failed to parse client create payload")
        return err
    }
    
    // Create client folder
    if err := c.folderService.CreateClientFolder(ctx, payload.TenantID, payload.ClientID, payload.ClientName); err != nil {
        log.WithError(err).Error("Failed to create client folder")
        
        // Record failure for retry
        if recordErr := c.failureHandler.RecordFailure(ctx, "client_folder_creation", payload, err); recordErr != nil {
            log.WithError(recordErr).Error("Failed to record failure")
        }
        
        return err
    }
    
    log.Info("Client folder created successfully")
    return nil
}

// Similar updates for MatterEventConsumer
func NewMatterEventConsumer(
    folderService FolderService,
    documentSettingRepo repositories.DocumentSettingRepository,
    failureHandler FailureHandler,
) *MatterEventConsumer {
    return &MatterEventConsumer{
        folderService:       folderService,
        documentSettingRepo: documentSettingRepo,
        failureHandler:      failureHandler,
    }
}
```

### 3. Retry Worker (Integrated into Service)

```go
type RetryWorker struct {
    eventFailureRepo    repositories.EventFailureRepository
    clientEventConsumer *ClientEventConsumer
    matterEventConsumer *MatterEventConsumer
    failureHandler      FailureHandler
    retryCalculator     *RetryCalculator
    config              *RetryConfig
    logger              logger.Logger
    stopChan            chan struct{}
    workerWG            sync.WaitGroup
}

func NewRetryWorker(
    eventFailureRepo repositories.EventFailureRepository,
    clientEventConsumer *ClientEventConsumer,
    matterEventConsumer *MatterEventConsumer,
    failureHandler FailureHandler,
    retryCalculator *RetryCalculator,
    config *RetryConfig,
    logger logger.Logger,
) *RetryWorker {
    return &RetryWorker{
        eventFailureRepo:    eventFailureRepo,
        clientEventConsumer: clientEventConsumer,
        matterEventConsumer: matterEventConsumer,
        failureHandler:      failureHandler,
        retryCalculator:     retryCalculator,
        config:              config,
        logger:              logger,
        stopChan:            make(chan struct{}),
    }
}

func (rw *RetryWorker) Start(ctx context.Context) error {
    if !rw.config.Enabled {
        rw.logger.Info("Retry worker is disabled")
        return nil
    }
    
    rw.logger.Info("Starting retry worker")
    
    rw.workerWG.Add(1)
    go rw.retryWorkerLoop(ctx)
    
    if rw.config.CleanupEnabled {
        rw.workerWG.Add(1)
        go rw.cleanupWorkerLoop(ctx)
    }
    
    return nil
}

func (rw *RetryWorker) Stop() error {
    rw.logger.Info("Stopping retry worker")
    close(rw.stopChan)
    rw.workerWG.Wait()
    rw.logger.Info("Retry worker stopped")
    return nil
}

func (rw *RetryWorker) retryWorkerLoop(ctx context.Context) {
    defer rw.workerWG.Done()
    
    ticker := time.NewTicker(rw.config.WorkerInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-rw.stopChan:
            return
        case <-ticker.C:
            if err := rw.processRetries(ctx); err != nil {
                rw.logger.WithError(err).Error("Failed to process retries")
            }
        }
    }
}

func (rw *RetryWorker) processRetries(ctx context.Context) error {
    log := rw.logger.WithCtx(ctx, "processRetries")
    
    // Get pending retries
    failures, err := rw.eventFailureRepo.GetPendingRetries(ctx, rw.config.BatchSize)
    if err != nil {
        return fmt.Errorf("failed to get pending retries: %w", err)
    }
    
    if len(failures) == 0 {
        return nil
    }
    
    log.WithField("count", len(failures)).Info("Processing retry batch")
    
    for _, failure := range failures {
        if err := rw.processRetry(ctx, failure); err != nil {
            log.WithError(err).WithField("failure_id", failure.ID).Error("Failed to process retry")
        }
    }
    
    return nil
}

func (rw *RetryWorker) processRetry(ctx context.Context, failure *model.EventFailure) error {
    log := rw.logger.WithCtx(ctx, "processRetry").WithFields(map[string]interface{}{
        "failure_id":  failure.ID,
        "event_type":  failure.EventType,
        "tenant_id":   failure.TenantID,
        "retry_count": failure.RetryCount,
    })
    
    log.Info("Processing retry")
    
    // Attempt retry based on event type
    var retryErr error
    switch failure.EventType {
    case "client_folder_creation":
        retryErr = rw.clientEventConsumer.HandleClientCreated(ctx, failure.PayloadJSON)
    case "matter_folder_creation":
        retryErr = rw.matterEventConsumer.HandleMatterCreated(ctx, failure.PayloadJSON)
    default:
        log.Error("Unknown event type")
        return rw.eventFailureRepo.MarkAsFailed(ctx, failure.ID, "unknown_event_type")
    }
    
    if retryErr == nil {
        // Retry succeeded
        log.Info("Retry succeeded")
        return rw.eventFailureRepo.MarkAsSuccess(ctx, failure.ID)
    }
    
    // Retry failed, check if we should retry again
    classification := ClassifyGoogleDriveError(retryErr)
    if !classification.ShouldRetry || failure.RetryCount >= failure.MaxRetries {
        // Permanent failure or max retries exceeded
        log.WithError(retryErr).Info("Marking as permanently failed")
        return rw.eventFailureRepo.MarkAsFailed(ctx, failure.ID, retryErr.Error())
    }
    
    // Schedule next retry
    nextRetryAt := rw.retryCalculator.CalculateNextRetry(failure.RetryCount + 1)
    if classification.CustomDelay != nil {
        nextRetryAt = time.Now().Add(*classification.CustomDelay)
    }
    
    log.WithField("next_retry_at", nextRetryAt).Info("Scheduling next retry")
    return rw.eventFailureRepo.UpdateRetryAttempt(
        ctx,
        failure.ID,
        failure.RetryCount+1,
        nextRetryAt,
        retryErr.Error(),
    )
}
```

### 4. Service Layer Updates

```go
// Update Service interface
type Service interface {
    // ... existing methods
    GetRetryWorker() *RetryWorker
    GetEventFailureRepo() repositories.EventFailureRepository
    GetFailureHandler() FailureHandler
    StartBackgroundWorkers(ctx context.Context) error
    StopBackgroundWorkers() error
}

// Update service implementation
type service struct {
    // ... existing fields
    eventFailureRepo repositories.EventFailureRepository
    failureHandler   FailureHandler
    retryWorker      *RetryWorker
    retryConfig      *RetryConfig
}

func NewService(
    gdriveClient *gdrive.Client,
    folderService FolderService,
    documentSettingRepo repositories.DocumentSettingRepository,
    eventFailureRepo repositories.EventFailureRepository,
    retryConfig *RetryConfig,
    logger logger.Logger,
) Service {
    // Create retry calculator
    retryCalculator := NewRetryCalculator(
        retryConfig.BaseDelay,
        retryConfig.MaxDelay,
        retryConfig.Multiplier,
        retryConfig.JitterEnabled,
    )
    
    // Create failure handler
    failureHandler := NewFailureHandler(eventFailureRepo, retryCalculator, logger)
    
    // Create event consumers with failure handler
    clientEventConsumer := NewClientEventConsumer(folderService, documentSettingRepo, failureHandler)
    matterEventConsumer := NewMatterEventConsumer(folderService, documentSettingRepo, failureHandler)
    
    // Create retry worker
    retryWorker := NewRetryWorker(
        eventFailureRepo,
        clientEventConsumer,
        matterEventConsumer,
        failureHandler,
        retryCalculator,
        retryConfig,
        logger,
    )
    
    return &service{
        gdriveClient:          gdriveClient,
        folderService:         folderService,
        documentSettingRepo:   documentSettingRepo,
        eventFailureRepo:      eventFailureRepo,
        failureHandler:        failureHandler,
        retryWorker:          retryWorker,
        retryConfig:          retryConfig,
        clientEventConsumer:   clientEventConsumer,
        matterEventConsumer:   matterEventConsumer,
    }
}

func (s *service) StartBackgroundWorkers(ctx context.Context) error {
    return s.retryWorker.Start(ctx)
}

func (s *service) StopBackgroundWorkers() error {
    return s.retryWorker.Stop()
}

func (s *service) GetRetryWorker() *RetryWorker {
    return s.retryWorker
}

func (s *service) GetEventFailureRepo() repositories.EventFailureRepository {
    return s.eventFailureRepo
}

func (s *service) GetFailureHandler() FailureHandler {
    return s.failureHandler
}
```

### 5. Handler Updates

```go
// Update handlers to use failure handler instead of direct repository access
func (h *Handler) ConsumeGoogleDriveClientCrud(r *ginext.Request) (*ginext.Response, error) {
    ctx := r.Context()
    log := logger.WithCtx(ctx, "ConsumeGoogleDriveClientCrud")

    var body map[string]interface{}
    r.MustBind(&body)

    // Convert body to JSON string for event consumer
    payloadBytes, err := json.Marshal(body)
    if err != nil {
        log.WithError(err).Error("Failed to marshal event payload")
        return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
    }
    payloadJSON := string(payloadBytes)

    // Extract topic to determine event type
    topic, ok := body["topic"].(string)
    if !ok {
        log.Error("Missing or invalid topic in request body")
        return nil, ginext.NewError(http.StatusBadRequest, "Missing or invalid topic")
    }

    // Get event consumer from service (already has failure handler injected)
    clientEventConsumer := h.gdriveService.GetClientEventConsumer()

    // Process event based on topic
    var err error
    switch topic {
    case "client.create":
        err = clientEventConsumer.HandleClientCreated(ctx, payloadJSON)
    case "client.update":
        err = clientEventConsumer.HandleClientUpdated(ctx, payloadJSON)
    default:
        log.WithField("topic", topic).Error("Unsupported topic")
        return nil, ginext.NewError(http.StatusBadRequest, "Unsupported topic")
    }

    if err != nil {
        log.WithError(err).Error("Failed to process client event")
        // Return 500 to trigger external system retry
        return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
    }

    return ginext.NewResponse(http.StatusOK, gin.H{"message": "Event processed successfully"}), nil
}

// Similar update for matter event handler
func (h *Handler) ConsumeGoogleDriveMatterCrud(r *ginext.Request) (*ginext.Response, error) {
    ctx := r.Context()
    log := logger.WithCtx(ctx, "ConsumeGoogleDriveMatterCrud")

    var body map[string]interface{}
    r.MustBind(&body)

    // Convert body to JSON string for event consumer
    payloadBytes, err := json.Marshal(body)
    if err != nil {
        log.WithError(err).Error("Failed to marshal event payload")
        return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
    }
    payloadJSON := string(payloadBytes)

    // Extract topic to determine event type
    topic, ok := body["topic"].(string)
    if !ok {
        log.Error("Missing or invalid topic in request body")
        return nil, ginext.NewError(http.StatusBadRequest, "Missing or invalid topic")
    }

    // Get event consumer from service (already has failure handler injected)
    matterEventConsumer := h.gdriveService.GetMatterEventConsumer()

    // Process event based on topic
    switch topic {
    case "matter.create":
        err = matterEventConsumer.HandleMatterCreated(ctx, payloadJSON)
    case "matter.update":
        err = matterEventConsumer.HandleMatterUpdated(ctx, payloadJSON)
    default:
        log.WithField("topic", topic).Error("Unsupported topic")
        return nil, ginext.NewError(http.StatusBadRequest, "Unsupported topic")
    }

    if err != nil {
        log.WithError(err).Error("Failed to process matter event")
        // Return 500 to trigger external system retry
        return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
    }

    return ginext.NewResponse(http.StatusOK, gin.H{"message": "Event processed successfully"}), nil
}
```

## Testing Strategy

### Unit Tests

```go
// Test RetryCalculator
func TestRetryCalculator_ExponentialBackoff(t *testing.T) {
    calculator := NewRetryCalculator(time.Minute, 16*time.Minute, 2.0, false)
    
    expected := []time.Duration{
        time.Minute,       // retry 0
        2 * time.Minute,   // retry 1  
        4 * time.Minute,   // retry 2
        8 * time.Minute,   // retry 3
        16 * time.Minute,  // retry 4 (max)
        16 * time.Minute,  // retry 5 (capped)
    }
    
    for i, expectedDelay := range expected {
        nextRetry := calculator.CalculateNextRetry(i)
        actualDelay := nextRetry.Sub(time.Now())
        
        assert.InDelta(t, float64(expectedDelay), float64(actualDelay), float64(time.Second))
    }
}

// Test Error Classification
func TestClassifyGoogleDriveError(t *testing.T) {
    tests := []struct {
        name     string
        err      error
        expected ErrorClassification
    }{
        {
            name: "nil_error",
            err:  nil,
            expected: ErrorClassification{
                ShouldRetry: false,
                Reason:      "no_error",
            },
        },
        {
            name: "rate_limit_error",
            err:  &googleapi.Error{Code: 429, Message: "Too Many Requests"},
            expected: ErrorClassification{
                ShouldRetry: true,
                CustomDelay: &[]time.Duration{30 * time.Minute}[0],
                Reason:      "rate_limited",
            },
        },
        {
            name: "quota_exceeded",
            err:  &googleapi.Error{Code: 403, Message: "quota exceeded"},
            expected: ErrorClassification{
                ShouldRetry: true,
                CustomDelay: &[]time.Duration{time.Hour}[0],
                Reason:      "quota_exceeded",
            },
        },
        {
            name: "auth_error",
            err:  &googleapi.Error{Code: 401, Message: "Unauthorized"},
            expected: ErrorClassification{
                ShouldRetry: false,
                IsPermanent: true,
                Reason:      "unauthorized",
            },
        },
        {
            name: "context_timeout",
            err:  context.DeadlineExceeded,
            expected: ErrorClassification{
                ShouldRetry: true,
                Reason:      "timeout",
            },
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := ClassifyGoogleDriveError(tt.err)
            assert.Equal(t, tt.expected.ShouldRetry, result.ShouldRetry)
            assert.Equal(t, tt.expected.Reason, result.Reason)
            assert.Equal(t, tt.expected.IsPermanent, result.IsPermanent)
            
            if tt.expected.CustomDelay != nil {
                assert.NotNil(t, result.CustomDelay)
                assert.Equal(t, *tt.expected.CustomDelay, *result.CustomDelay)
            } else {
                assert.Nil(t, result.CustomDelay)
            }
        })
    }
}

// Test FailureHandler
func TestFailureHandler_RecordFailure(t *testing.T) {
    mockRepo := &mocks.EventFailureRepository{}
    calculator := NewRetryCalculator(time.Minute, 16*time.Minute, 2.0, false)
    logger := &mocks.Logger{}
    
    handler := NewFailureHandler(mockRepo, calculator, logger)
    
    payload := map[string]interface{}{
        "tenant_id": uint64(123),
        "client_id": uint64(456),
    }
    
    err := errors.New("Google API error")
    
    // Mock expectations
    mockRepo.On("Create", mock.Anything, mock.MatchedBy(func(failure *model.EventFailure) bool {
        return failure.TenantID == 123 &&
               failure.EventType == "test_event" &&
               failure.Status == "pending"
    })).Return(nil)
    
    // Test
    err = handler.RecordFailure(context.Background(), "test_event", payload, err)
    
    assert.NoError(t, err)
    mockRepo.AssertExpectations(t)
}
```

### Integration Tests

```go
func TestRetryWorker_Integration(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    // Create repositories and services
    eventFailureRepo := repositories.NewEventFailureRepository(db)
    mockFolderService := &mocks.FolderService{}
    mockDocumentSettingRepo := &mocks.DocumentSettingRepository{}
    
    // Setup config
    config := &RetryConfig{
        Enabled:         true,
        WorkerInterval:  100 * time.Millisecond, // Fast for testing
        BatchSize:       10,
        MaxAttempts:     3,
        BaseDelay:       time.Second,
        MaxDelay:        time.Minute,
        Multiplier:      2.0,
        JitterEnabled:   false,
    }
    
    calculator := NewRetryCalculator(
        config.BaseDelay,
        config.MaxDelay,
        config.Multiplier,
        config.JitterEnabled,
    )
    
    logger := &mocks.Logger{}
    failureHandler := NewFailureHandler(eventFailureRepo, calculator, logger)
    
    clientEventConsumer := NewClientEventConsumer(mockFolderService, mockDocumentSettingRepo, failureHandler)
    matterEventConsumer := NewMatterEventConsumer(mockFolderService, mockDocumentSettingRepo, failureHandler)
    
    retryWorker := NewRetryWorker(
        eventFailureRepo,
        clientEventConsumer,
        matterEventConsumer,
        failureHandler,
        calculator,
        config,
        logger,
    )
    
    // Create a failed event
    failure := &model.EventFailure{
        TenantID:     123,
        EventType:    "client_folder_creation",
        PayloadJSON:  `{"tenant_id": 123, "client_id": 456, "client_name": "Test Client"}`,
        ErrorMessage: "Initial error",
        RetryCount:   0,
        NextRetryAt:  time.Now().Add(-time.Minute), // Ready for retry
        MaxRetries:   3,
        Status:       "pending",
    }
    
    err := eventFailureRepo.Create(context.Background(), failure)
    require.NoError(t, err)
    
    // Mock successful retry
    mockFolderService.On("CreateClientFolder", mock.Anything, uint64(123), uint64(456), "Test Client").Return(nil)
    
    // Start retry worker
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    err = retryWorker.Start(ctx)
    require.NoError(t, err)
    
    // Wait for processing
    time.Sleep(500 * time.Millisecond)
    
    // Stop worker
    err = retryWorker.Stop()
    require.NoError(t, err)
    
    // Verify retry was processed
    updated, err := eventFailureRepo.GetByID(context.Background(), failure.ID)
    require.NoError(t, err)
    assert.Equal(t, "success", updated.Status)
    
    mockFolderService.AssertExpectations(t)
}
```

## Implementation Timeline

| Phase | Duration | Description |
|-------|----------|-------------|
| 1 | 1-2 hours | EventFailure model creation and GORM auto migrate setup |
| 2 | 2-3 hours | Error classification and retry calculator |
| 3 | 3-4 hours | Failure handler and integration interfaces |
| 4 | 2-3 hours | Retry worker implementation |
| 5 | 2-3 hours | Service layer integration and DI updates |
| 6 | 1-2 hours | Handler updates and configuration |
| 7 | 3-4 hours | Comprehensive testing |
| 8 | 1-2 hours | Documentation and deployment setup |
| **Total** | **15-23 hours** | **2-3 working days** |

---

*This design document addresses the architectural, configuration, and integration issues while following the project's existing patterns and best practices for a robust retry mechanism implementation.*
