# AutoDoc Feature Test Plan

> **QA Lead**: Comprehensive test plan for AutoDoc document automation system  
> **Last Updated**: 2025-08-17  
> **Test Environment**: Development/Staging  
> **Base URL**: `http://localhost:8088/v3/autodoc`

## 📋 Test Overview

### System Under Test
AutoDoc is a document automation system that:
- Triggers on system events (client.create, client.update, matter.create, matter.update)
- Executes rules with configurable triggers and actions
- Supports cross-provider operations (internal storage, Google Drive, SharePoint)
- Processes templates with placeholder replacement
- Operates in multi-tenant environment with complete tenant isolation

### Test Scope
- API Endpoint Testing (CRUD operations)
- Rule Engine Testing (trigger matching, condition evaluation)
- Action Execution Testing (file/folder operations)
- Event Integration Testing (end-to-end processing)
- Security & Isolation Testing (tenant separation)
- Error Handling Testing (validation, failures)
- Performance Testing (load testing)

### Test Environment Setup
```bash
# Start service
svcman restart docman

# Verify service status
curl -f http://localhost:8088/health

# Check logs
tail -f /usr/local/var/log/runlog_docman.log | grep autodoc
```

---

## 🚀 1. API Endpoint Testing

### 1.1 Rule Management APIs

#### TC-API-001: Create AutoDoc Rule - Valid Data
**Category**: API Endpoint Testing  
**Description**: Verify successful creation of AutoDoc rule with valid configuration

**Preconditions**: 
- Valid tenant authentication token
- AutoDocRoot exists with template files

**Test Data**:
```json
{
  "name": "Client Folder Creation",
  "description": "Create folder structure for new clients",
  "trigger_type": "client.create",
  "trigger_rules": {
    "body.extra.current.stage_text": "initial"
  },
  "rule_config": [
    {
      "action_type": "copy_folder",
      "source_path": "templates/client-folder",
      "target_path": "{client_folder}",
      "provider": "internal",
      "target_provider": "gdrive"
    }
  ],
  "is_active": true
}
```

**Execution Steps**:
1. Send POST request to `/v3/autodoc/rules`
2. Include Bearer token in Authorization header
3. Set Content-Type to application/json

**Expected Results**:
- HTTP 201 Created status
- Response contains rule ID and all submitted fields
- Rule appears in GET `/v3/autodoc/rules` list
- `created_at` and `updated_at` timestamps populated

**Endpoint**: `POST /v3/autodoc/rules`

#### TC-API-002: Create AutoDoc Rule - Invalid Data
**Category**: API Endpoint Testing  
**Description**: Verify proper validation errors for invalid rule data

**Test Data**:
```json
{
  "name": "",
  "trigger_type": "invalid.event",
  "rule_config": []
}
```

**Expected Results**:
- HTTP 400 Bad Request status
- Error response with validation details
- No rule created in database

**Endpoint**: `POST /v3/autodoc/rules`

#### TC-API-003: List AutoDoc Rules
**Category**: API Endpoint Testing  
**Description**: Verify rule listing with pagination and filtering

**Execution Steps**:
1. Create multiple test rules
2. GET `/v3/autodoc/rules?page=1&limit=5`
3. GET `/v3/autodoc/rules?active_only=true`

**Expected Results**:
- HTTP 200 OK status
- Correct pagination metadata
- Only active rules when filtered
- Tenant-isolated results

**Endpoint**: `GET /v3/autodoc/rules`

#### TC-API-004: Update AutoDoc Rule
**Category**: API Endpoint Testing  
**Description**: Verify rule update functionality

**Test Data**:
```json
{
  "name": "Updated Rule Name",
  "is_active": false,
  "description": "Updated description"
}
```

**Expected Results**:
- HTTP 200 OK status
- Updated fields reflected in response
- `updated_at` timestamp changed
- Original rule ID preserved

**Endpoint**: `PUT /v3/autodoc/rules/{id}`

#### TC-API-005: Delete AutoDoc Rule
**Category**: API Endpoint Testing  
**Description**: Verify rule deletion

**Expected Results**:
- HTTP 204 No Content status
- Rule no longer appears in list
- Subsequent GET returns 404

**Endpoint**: `DELETE /v3/autodoc/rules/{id}`

### 1.2 AutoDocRoot Management

#### TC-API-006: List AutoDocRoot Contents
**Category**: API Endpoint Testing  
**Description**: Verify AutoDocRoot listing and creation

**Execution Steps**:
1. GET `/v3/documents?autodoc_root=true`
2. Verify AutoDocRoot creation if not exists

**Expected Results**:
- HTTP 200 OK status
- AutoDocRoot folder created automatically
- Template files listed correctly
- Tenant-isolated content

**Endpoint**: `GET /v3/documents?autodoc_root=true`

---

## 🎯 2. Rule Engine Testing

### 2.1 Trigger Condition Evaluation

#### TC-ENGINE-001: Simple Trigger Condition
**Category**: Rule Engine Testing  
**Description**: Verify rule triggers with simple field matching

**Test Data**:
Rule trigger: `{"body.extra.current.stage_text": "initial"}`
Event data:
```json
{
  "topic": "client.create",
  "body": {
    "id": 123,
    "extra": {"current": {"stage_text": "initial"}}
  }
}
```

**Expected Results**:
- Rule triggers successfully
- Action execution initiated
- Correct placeholder resolution

**Command**: `curl -X POST "http://localhost:8088/v3/autodoc/rules/1/execute" -H "X-Tenant-ID: 1" -d @test_event.json`

#### TC-ENGINE-002: Complex Operator Conditions
**Category**: Rule Engine Testing  
**Description**: Verify rule triggers with operator-based conditions

**Test Data**:
Rule trigger:
```json
{
  "body.name": {
    "operator": "contains",
    "value": "Corp"
  },
  "body.extra.current.stage_text": {
    "operator": "in",
    "value": ["initial", "active"]
  }
}
```

**Expected Results**:
- Rule evaluates multiple conditions correctly
- AND logic applied between fields
- Triggers only when all conditions met

#### TC-ENGINE-003: Array Conditions (Multiple Conditions per Field)
**Category**: Rule Engine Testing  
**Description**: Verify array-based condition evaluation

**Test Data**:
```json
{
  "body.extra.current.stage_text": [
    {
      "operator": "ne",
      "value": "{body.extra.old.stage_text}"
    },
    {
      "operator": "eq",
      "value": "active"
    }
  ]
}
```

**Expected Results**:
- All array conditions evaluated with AND logic
- Dynamic placeholder comparison works
- Stage change detection functions correctly

#### TC-ENGINE-004: Trigger Condition - No Match
**Category**: Rule Engine Testing  
**Description**: Verify rule does not trigger when conditions not met

**Test Data**:
Rule expects stage "initial", event has stage "closed"

**Expected Results**:
- Rule does not trigger
- No actions executed
- No error generated

#### TC-ENGINE-005: Event Type Filtering
**Category**: Rule Engine Testing  
**Description**: Verify rules only trigger for correct event types

**Test Data**:
- Rule configured for "client.create"
- Send "matter.create" event

**Expected Results**:
- Rule does not trigger
- Event type mismatch handled correctly

---

## 🔧 3. Action Execution Testing

### 3.1 File Operations

#### TC-ACTION-001: Copy File - Internal to Google Drive
**Category**: Action Execution Testing  
**Description**: Verify file copy from internal storage to Google Drive

**Preconditions**:
- Source file exists in AutoDocRoot
- Google Drive integration configured
- Target folder accessible

**Test Data**:
```json
{
  "action_type": "copy_file",
  "source_path": "templates/welcome.docx",
  "target_path": "{client_folder}/Welcome Letter.docx",
  "provider": "internal",
  "target_provider": "gdrive"
}
```

**Expected Results**:
- File copied successfully
- Correct filename and location
- File content preserved
- Placeholder resolution applied

#### TC-ACTION-002: Copy Folder - Cross Provider
**Category**: Action Execution Testing  
**Description**: Verify folder copy with nested structure

**Test Data**:
```json
{
  "action_type": "copy_folder",
  "source_path": "templates/client-package",
  "target_path": "{client_folder}/Client Package",
  "provider": "internal",
  "target_provider": "gdrive"
}
```

**Expected Results**:
- Entire folder structure copied
- All nested files and folders preserved
- Correct permissions applied
- Target folder created if not exists

#### TC-ACTION-003: Generate Document with Placeholders
**Category**: Action Execution Testing  
**Description**: Verify document generation with placeholder replacement

**Test Data**:
Template with placeholders: `{client_name}`, `{date}`, `{client_code}`

**Expected Results**:
- Document generated successfully
- All placeholders replaced with actual values
- Document format preserved
- Correct target location

### 3.2 Provider Integration

#### TC-ACTION-004: Google Drive Integration
**Category**: Action Execution Testing  
**Description**: Verify Google Drive operations

**Test Cases**:
- Upload to shared drive
- Create folder structure
- Set permissions
- Handle quota limits

**Expected Results**:
- All operations complete successfully
- Proper error handling for failures
- Correct folder hierarchy

#### TC-ACTION-005: Internal Storage Operations
**Category**: Action Execution Testing  
**Description**: Verify internal storage operations

**Expected Results**:
- File operations within tenant boundaries
- Correct path resolution
- Proper file metadata

---

## 🔄 4. Event Integration Testing

### 4.1 End-to-End Event Processing

#### TC-EVENT-001: Client Creation Event Processing
**Category**: Event Integration Testing  
**Description**: Verify complete client.create event processing

**Test Data**:
```json
{
  "topic": "client.create",
  "body": {
    "id": 10219,
    "name": "Test Corporation",
    "code": "TC001",
    "tenant_id": 1,
    "extra": {
      "current": {
        "stage_text": "initial",
        "owners": [23, 45]
      }
    }
  }
}
```

**Execution Steps**:
1. Configure rule for client.create
2. Trigger actual client creation event
3. Monitor rule execution
4. Verify document creation

**Expected Results**:
- Event received and processed
- Rule triggered automatically
- Documents created in correct location
- Execution logged properly

#### TC-EVENT-002: Matter Update Event Processing
**Category**: Event Integration Testing  
**Description**: Verify matter.update event with stage change

**Expected Results**:
- Stage change detected correctly
- Conditional rules trigger appropriately
- Cross-provider operations complete

### 4.2 Event Consumer Integration

#### TC-EVENT-003: Event Consumer Reliability
**Category**: Event Integration Testing  
**Description**: Verify event consumer handles failures gracefully

**Test Scenarios**:
- Network interruption during processing
- Invalid event data
- Provider unavailability

**Expected Results**:
- Failed events retried appropriately
- Error logging comprehensive
- System remains stable

---

## 🔒 5. Security & Isolation Testing

### 5.1 Tenant Isolation

#### TC-SECURITY-001: Tenant Data Isolation
**Category**: Security Testing  
**Description**: Verify complete tenant data separation

**Test Data**:
- Create rules for Tenant A and Tenant B
- Attempt cross-tenant access

**Expected Results**:
- Tenant A cannot access Tenant B rules
- API returns 404 for cross-tenant requests
- Database queries properly filtered

#### TC-SECURITY-002: Authentication Requirements
**Category**: Security Testing  
**Description**: Verify all endpoints require valid authentication

**Test Cases**:
- Request without Bearer token
- Request with invalid token
- Request with expired token

**Expected Results**:
- HTTP 401 Unauthorized for all cases
- No data exposure
- Proper error messages

### 5.2 Authorization Testing

#### TC-SECURITY-003: Rule Ownership Validation
**Category**: Security Testing  
**Description**: Verify users can only modify their tenant's rules

**Expected Results**:
- Cross-tenant rule modification blocked
- Proper ownership validation
- Audit trail maintained

---

## ⚠️ 6. Error Handling Testing

### 6.1 Validation Testing

#### TC-ERROR-001: Rule Validation Errors
**Category**: Error Handling Testing  
**Description**: Verify comprehensive input validation

**Test Cases**:
| Field | Invalid Value | Expected Error |
|-------|---------------|----------------|
| name | Empty string | "Name is required" |
| name | 256+ characters | "Name too long" |
| trigger_type | "invalid.type" | "Invalid trigger type" |
| rule_config | Empty array | "At least one action required" |
| source_path | Non-existent path | "Source path not found" |

**Expected Results**:
- Specific validation messages
- HTTP 400 status codes
- No partial data saved

#### TC-ERROR-002: Provider Failure Handling
**Category**: Error Handling Testing  
**Description**: Verify graceful handling of provider failures

**Test Scenarios**:
- Google Drive API unavailable
- SharePoint authentication failure
- Network timeout during file transfer

**Expected Results**:
- Appropriate error messages
- Retry mechanisms activated
- System stability maintained

### 6.2 Edge Cases

#### TC-ERROR-003: Large File Handling
**Category**: Error Handling Testing  
**Description**: Verify handling of large files and folders

**Test Data**:
- Files > 100MB
- Folders with 1000+ files
- Deep folder hierarchies (10+ levels)

**Expected Results**:
- Appropriate timeout handling
- Memory usage controlled
- Progress tracking available

---

## 📊 7. Performance Testing

### 7.1 Load Testing

#### TC-PERF-001: Concurrent Rule Execution
**Category**: Performance Testing  
**Description**: Verify system handles multiple simultaneous rule executions

**Test Data**:
- 50 concurrent rule executions
- Mix of file and folder operations
- Cross-provider operations

**Expected Results**:
- All executions complete successfully
- Response time < 5 seconds per rule
- No resource exhaustion
- Proper queue management

**Command**:
```bash
# Load test script
for i in {1..50}; do
  curl -X POST "http://localhost:8088/v3/autodoc/rules/1/execute" \
    -H "X-Tenant-ID: 1" -H "X-User-ID: $i" &
done
wait
```

#### TC-PERF-002: Large Volume Event Processing
**Category**: Performance Testing  
**Description**: Verify system handles high event volume

**Test Data**:
- 1000 events in 1 minute
- Multiple tenants
- Various event types

**Expected Results**:
- All events processed
- No event loss
- Memory usage stable
- Response time degradation < 50%

### 7.2 Resource Usage

#### TC-PERF-003: Memory Usage Monitoring
**Category**: Performance Testing  
**Description**: Monitor memory usage during extended operations

**Execution Steps**:
1. Monitor baseline memory usage
2. Execute 100 file copy operations
3. Monitor memory throughout process
4. Verify memory cleanup

**Expected Results**:
- Memory usage returns to baseline
- No memory leaks detected
- Garbage collection effective

---

## 🧪 8. Integration Testing

### 8.1 End-to-End Scenarios

#### TC-INTEGRATION-001: Complete Client Onboarding Workflow
**Category**: Integration Testing  
**Description**: Test complete client onboarding automation

**Scenario**:
1. Client created with stage "initial"
2. Rule triggers folder creation
3. Welcome documents generated
4. Client stage updated to "active"
5. Additional documents created

**Expected Results**:
- All automation steps complete
- Correct document hierarchy
- Proper timing and sequencing

#### TC-INTEGRATION-002: Multi-Provider Document Workflow
**Category**: Integration Testing  
**Description**: Test document workflow across multiple providers

**Scenario**:
1. Template stored in internal storage
2. Copy to Google Drive client folder
3. Generate personalized document
4. Store final document in SharePoint

**Expected Results**:
- Cross-provider operations seamless
- Document integrity maintained
- Proper error handling

---

## 📋 Test Execution Checklist

### Pre-Test Setup
- [ ] Service running on port 8088
- [ ] Database initialized with test data
- [ ] Google Drive integration configured
- [ ] SharePoint integration configured (if available)
- [ ] Test tenant accounts created
- [ ] AutoDocRoot populated with templates

### Test Data Preparation
- [ ] Sample client/matter records
- [ ] Template documents (DOCX, PDF)
- [ ] Test folder structures
- [ ] Event payloads prepared
- [ ] Authentication tokens generated

### Post-Test Cleanup
- [ ] Remove test rules
- [ ] Clean up generated documents
- [ ] Reset test tenant data
- [ ] Archive test logs
- [ ] Document any issues found

---

## 🎯 Success Criteria

### Functional Requirements
- ✅ All API endpoints respond correctly
- ✅ Rule engine evaluates conditions accurately
- ✅ Actions execute successfully across providers
- ✅ Events trigger rules appropriately
- ✅ Tenant isolation maintained
- ✅ Error handling comprehensive

### Performance Requirements
- ✅ Rule execution < 5 seconds
- ✅ API response time < 500ms
- ✅ Support 100 concurrent operations
- ✅ Memory usage stable under load

### Security Requirements
- ✅ Authentication required for all endpoints
- ✅ Tenant data completely isolated
- ✅ No unauthorized access possible
- ✅ Audit trail comprehensive

---

*This test plan ensures comprehensive coverage of the AutoDoc feature functionality, performance, and security requirements.*
