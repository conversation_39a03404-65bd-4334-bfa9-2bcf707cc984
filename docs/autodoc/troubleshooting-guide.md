# AutoDoc Troubleshooting Guide

> **Last Updated**: August 2025  
> **Focus**: Critical Issues & Provider-Specific Behavior

## 🚨 Critical Issues & Solutions

### Issue 1: Generated Documents Invalid/Empty

#### Symptoms
- Documents created in database but have empty `key` field
- Documents appear in listings but are undownloadable
- Log shows: `Successfully saved new processed document with context: document_key=""`

#### Root Cause
Missing content upload step in `saveProcessedDocumentWithContext` function.

#### Solution
Ensure content upload is executed after document creation:

```go
// Upload the processed content to storage
if len(content) > 0 {
    uploadResult, err := s.docxProcessor.UploadProcessedDocument(ctx, uploadFilename, content, userID, tenantID, userRoles)
    if err != nil {
        log.WithError(err).Error("Failed to upload processed document content")
    } else {
        document.Key = uploadResult.Key
        log.WithField("upload_key", uploadResult.Key).Info("Successfully uploaded processed document content")
    }
}
```

#### Verification
- Check document has non-empty `key` field
- Verify file size > 0 in logs
- Test document download functionality

---

### Issue 2: Incorrect Document Placement

#### Symptoms
- Documents placed as children of client/matter folders instead of at same level
- Internal provider documents have `parent_id=folder_id` instead of `parent_id=0`
- Google Drive documents missing proper folder hierarchy

#### Root Cause
Provider-specific folder model differences not handled correctly.

#### Solution
Provider-specific logic in action handlers:

**Internal Provider (Virtual Folders)**:
```go
// For internal provider only
if targetParent.ObjectType > 0 && targetParent.ObjectID > 0 && targetParent.ParentID > 0 {
    adjustedTargetParent = &TargetParentResult{
        ParentID:   0, // Root level for internal provider
        ObjectType: targetParent.ObjectType,
        ObjectID:   targetParent.ObjectID,
        TenantID:   targetParent.TenantID,
    }
}
```

**Google Drive Provider (Physical Folders)**:
- No adjustment needed - maintains hierarchical structure
- Documents should have `parent_id=folder_id`

#### Expected Results

**Internal Provider Structure**:
```
Root Level (parent_id=0)
├── Client Folder: object_type=1, object_id=10227, parent_id=0
├── Generated Document: object_type=1, object_id=10227, parent_id=0 ✅
└── Copied Files/Folders: object_type=1, object_id=10227, parent_id=0 ✅
```

**Google Drive Provider Structure**:
```
Drive Root
├── Client Folder (ID: gdrive_folder_123)
    ├── Generated Document (parent_id=gdrive_folder_123) ✅
    └── Copied Files/Folders (parent_id=gdrive_folder_123) ✅
```

---

## 🔍 Debugging Techniques

### Log Analysis
```bash
# Check for content upload issues
tail -f /usr/local/var/log/runlog_docman.log | grep -E "(upload|content|document_key)"

# Check for placement issues
tail -f /usr/local/var/log/runlog_docman.log | grep -E "(parent_id|object_type|object_id)"

# Check specific request
grep "REQUEST_ID" /usr/local/var/log/runlog_docman.log | head -20
```

### Database Verification
```bash
# Check document structure
curl -s "http://localhost:8088/v1/documents/DOCUMENT_ID" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 1" -H "X-User-Roles: 8" | \
  jq '.data | {id, name, parent_id, object_type, object_id, key}'

# Check documents for client
curl -s "http://localhost:8088/v1/documents?object_type=1&object_id=CLIENT_ID" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 1" -H "X-User-Roles: 8" | \
  jq '.data[] | {id, name, parent_id, object_type, object_id}'
```

### Service Health
```bash
# Restart service
svcman restart docman

# Check service status
curl -s "http://localhost:8088/health"

# Test AutoDoc functionality
curl -s "http://localhost:8088/v3/autodoc/variables?object_type=1" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 1" -H "X-User-Roles: 8"
```

---

## ⚠️ Provider-Specific Considerations

### Internal Provider (Bilabl Storage)
- **Folder Model**: Virtual/logical groupings
- **Document Placement**: `parent_id=0` with object context
- **Object Association**: Via `object_type` and `object_id` fields
- **Use Case**: Internal document management

### Google Drive Provider
- **Folder Model**: Physical hierarchical structure
- **Document Placement**: `parent_id=folder_id` for hierarchy
- **Object Association**: Via Google Drive folder structure
- **Use Case**: External client collaboration

### Key Differences
| Aspect | Internal Provider | Google Drive Provider |
|--------|------------------|----------------------|
| Folder Type | Virtual/Logical | Physical/Hierarchical |
| Parent ID | 0 (root level) | Actual folder ID |
| Object Context | object_type + object_id | Folder hierarchy |
| Document Access | Via object association | Via folder permissions |

---

## 🛠️ Quick Fixes

### Force Document Regeneration
```bash
# Trigger client update event to regenerate documents
curl -X POST "http://localhost:4041/v1/clients/CLIENT_ID" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 1" -H "X-User-Roles: 8" \
  -d '{"stage": 256}'
```

### Manual Rule Execution
```bash
# Execute specific rule manually
curl -X POST "http://localhost:8088/v3/autodoc/rules/RULE_ID/execute" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 1" -H "X-User-Roles: 8" \
  -d '{"object_id": OBJECT_ID, "object_type": OBJECT_TYPE}'
```

---

## 📋 Checklist for New Issues

### Document Generation Issues
- [ ] Check document has non-empty `key` field
- [ ] Verify content upload logs show success
- [ ] Confirm file size > 0 in upload result
- [ ] Test document download functionality

### Document Placement Issues
- [ ] Identify provider type (internal vs external)
- [ ] Check expected folder model for provider
- [ ] Verify `parent_id` matches provider expectations
- [ ] Confirm object context is preserved

### General Debugging
- [ ] Check service logs for errors
- [ ] Verify rule conditions match event data
- [ ] Test rule execution manually
- [ ] Confirm provider credentials and permissions

---

## 🛡️ Override Behavior Troubleshooting

### Issue: Documents Not Being Created

#### Symptoms
- Rule executes successfully but no documents appear
- Logs show: `Document generation skipped due to existing file and override=false`
- No error messages, but expected files missing

#### Root Cause
Default `override: false` behavior prevents overwriting existing files.

#### Solution
**Option 1: Enable Override (Update Mode)**
```json
{
  "action_type": "generate_document",
  "source_path": "templates/contract.docx",
  "target_path": "{client_folder}/Contract.docx",
  "override": true
}
```

**Option 2: Use Unique Filenames (Safe Mode)**
```json
{
  "action_type": "generate_document",
  "source_path": "templates/contract.docx",
  "target_path": "{client_folder}/Contract - {timestamp}.docx",
  "override": false
}
```

#### Verification
- Check logs for override-related messages
- Verify `override` field in rule configuration
- Test with unique target paths

#### Log Message Examples

**Safe Mode Skip (override: false)**
```json
{
  "level": "warning",
  "msg": "Document with same name already exists, skipping generation due to override=false",
  "existing_doc_id": 1234,
  "existing_doc_name": "Welcome Letter.docx",
  "override_setting": false
}
```

**Skipped Operation**
```json
{
  "level": "info",
  "msg": "Document generation skipped due to existing file and override=false",
  "filename": "Welcome Letter.docx",
  "override": false,
  "parent_id": 123
}
```

### Issue: Unexpected File Overwrites

#### Symptoms
- Existing files being replaced unexpectedly
- Data loss from previous document versions
- Users complaining about missing content

#### Root Cause
`override: true` setting replacing existing files.

#### Solution
**Review Override Settings**
```bash
# Check rule configuration
GET /v3/autodoc/rules/{rule_id}

# Look for override: true in actions
```

**Implement Safe Mode**
```json
{
  "action_type": "copy_file",
  "source_path": "templates/important-doc.docx",
  "target_path": "{client_folder}/Important Document.docx",
  "override": false
}
```

#### Prevention
- Use `override: false` for initial setup rules
- Use `override: true` only for intentional updates
- Implement versioned filenames for safety

### Override Best Practices

#### Safe Mode (Default)
- ✅ **Initial client/matter setup**
- ✅ **One-time document generation**
- ✅ **Template distribution**
- ✅ **Backup operations**

#### Update Mode (Explicit)
- ✅ **Template updates**
- ✅ **Policy refreshes**
- ✅ **Content synchronization**
- ✅ **Scheduled updates**

---

*This guide focuses on the most critical AutoDoc issues encountered in production. For comprehensive documentation, see [AUTODOC_COMPLETE.md](./AUTODOC_COMPLETE.md).*
