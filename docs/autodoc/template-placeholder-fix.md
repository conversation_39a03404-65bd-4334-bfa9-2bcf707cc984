# Template Placeholder Variable Replacement Issue Fix

## Issue Summary
**Date:** 2025-08-15  
**Status:** ✅ RESOLVED  

Template placeholders in DOCX content were not being replaced with actual client data, while filename placeholders worked correctly.

## Problem Description

### Symptoms
- ✅ Filename replacement worked: `generated-{name}.docx` → `generated-AB Company.docx`
- ❌ Content placeholders not replaced: `{client.name}`, `{client.email}`, etc. remained as-is in DOCX

### Root Cause Analysis

1. **Client ID Type Mismatch**
   - J<PERSON><PERSON> unmarshaling converted `id` from `int` to `float64`
   - Original code only handled `uint64` and `int` types
   - `clientID` remained 0, so `enrichClientData` was never called

2. **Struct to Map Conversion Issue**
   - Client service returned a struct, not `map[string]interface{}`
   - `normalizeClientObject` failed to convert struct to map
   - Result: empty client data object `{}`

## Solution Implemented

### 1. Fixed Client ID Type Detection (Simplified Approach)
**File:** `internal/service/autodoc/rule_execution_engine.go`

**Final Implementation (Recommended):**
```go
// Extract client ID from event data - convert to string first for simplicity
var clientID uint64
if idValue := eventData["id"]; idValue != nil {
    idStr := fmt.Sprintf("%v", idValue)
    if parsedID, err := strconv.ParseUint(idStr, 10, 64); err == nil {
        clientID = parsedID
        placeholders["id"] = clientID
        log.Debugf("Added client id: %v", clientID)
    } else {
        log.WithField("id_value", idValue).WithError(err).Debug("Could not parse client ID from event data")
    }
}
```

**Previous Complex Approach (Replaced):**
```go
// OLD: Multiple type assertions - more complex
if id, ok := eventData["id"].(uint64); ok {
    clientID = id
} else if id, ok := eventData["id"].(int); ok {
    clientID = uint64(id)
} else if id, ok := eventData["id"].(float64); ok {
    clientID = uint64(id)
} else {
    // handle error
}
```

### 2. Fixed Struct to Map Conversion
**File:** `internal/service/autodoc/rule_execution_engine.go`

```go
func (e *ruleExecutionEngine) normalizeClientObject(clientObject interface{}) map[string]interface{} {
    normalized := make(map[string]interface{})

    // Convert client object to map using JSON marshal/unmarshal
    // This handles both struct and map types
    jsonBytes, err := json.Marshal(clientObject)
    if err != nil {
        return normalized
    }

    var clientMap map[string]interface{}
    if err := json.Unmarshal(jsonBytes, &clientMap); err != nil {
        return normalized
    }

    // Copy all original fields
    for key, value := range clientMap {
        normalized[key] = value
    }
    
    // ... rest of field mappings
}
```

## Verification

### Before Fix
```json
{
  "level": "info",
  "msg": "Extracted placeholder data rule_id=4 placeholders=map[client:map[] date:2025-08-15 ...]"
}
```

### After Fix
```json
{
  "level": "info", 
  "msg": "Extracted placeholder data rule_id=4 placeholders=map[client:map[name:Tom 007 Subsidinary 1 email:<EMAIL> office_phone:12345678899 ...] date:2025-08-15 ...]"
}
```

### File Size Change
- **Before:** 15537 bytes (template) → 13731 bytes (no replacement)
- **After:** 15537 bytes (template) → 13661 bytes (with replacement)

## Impact
- ✅ Template placeholders now correctly replaced with client data
- ✅ Generated documents contain actual client information
- ✅ No breaking changes to existing functionality
- ✅ Maintains backward compatibility

## Files Modified
- `internal/service/autodoc/rule_execution_engine.go`
  - Added `float64` type handling for client ID extraction
  - Implemented JSON marshal/unmarshal for struct-to-map conversion
  - Added debug logging for troubleshooting

## Testing
- ✅ Manual testing with rule ID 4 (client.update trigger)
- ✅ Verified client data fetch from service
- ✅ Confirmed placeholder replacement in DOCX content
- ✅ File size reduction indicates successful replacement

## Approach Comparison

### Complex Approach (Initial Fix):
```go
// Multiple type assertions - handles specific types
if id, ok := eventData["id"].(uint64); ok {
    clientID = id
} else if id, ok := eventData["id"].(int); ok {
    clientID = uint64(id)
} else if id, ok := eventData["id"].(float64); ok {
    clientID = uint64(id)
} else {
    // handle error
}
```

### Simplified Approach (Final Implementation):
```go
// String conversion first - handles any type
if idValue := eventData["id"]; idValue != nil {
    idStr := fmt.Sprintf("%v", idValue)
    if parsedID, err := strconv.ParseUint(idStr, 10, 64); err == nil {
        clientID = parsedID
    }
}
```

### Benefits of Simplified Approach:
- ✅ **Shorter code** - Single conversion instead of multiple type assertions
- ✅ **More flexible** - Handles any type (int, float64, string, etc.)
- ✅ **Easier maintenance** - No need to add cases for new types
- ✅ **More robust** - `fmt.Sprintf("%v")` converts most types safely

## Key Learnings
- **String-first conversion pattern** is often simpler than multiple type assertions
- **JSON unmarshaling** can produce unexpected types (float64 instead of int)
- **Struct-to-map conversion** via JSON marshal/unmarshal is reliable for dynamic field access
- **Template processing** requires proper data structure preparation

## Related Issues
- Template filename replacement was already working correctly
- Only content placeholders were affected by this issue
