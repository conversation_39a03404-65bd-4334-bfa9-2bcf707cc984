# AutoDoc Rules - UI Integration Guide

> **Target Audience**: Frontend developers integrating with AutoDoc API  
> **Last Updated**: 2025-08-14  
> **API Version**: v3

## 📋 Table of Contents

1. [Overview](#overview)
2. [API Endpoints](#api-endpoints)
3. [Rule Structure](#rule-structure)
4. [Trigger Types](#trigger-types)
5. [Trigger Rules](#trigger-rules)
6. [Actions](#actions)
7. [Placeholders](#placeholders)
8. [UI Implementation Examples](#ui-implementation-examples)
9. [Validation & Error Handling](#validation--error-handling)
10. [Testing & Debugging](#testing--debugging)

---

## 🎯 Overview

AutoDoc Rules enable automatic document operations based on system events. Rules consist of:

- **Trigger**: When the rule should execute (event type + conditions)
- **Actions**: What operations to perform (copy files, create folders, generate documents)
- **Providers**: Where to source and store documents (internal, Google Drive, SharePoint)

### Key Concepts

- **Event-Driven**: Rules trigger on system events (client.create, matter.update, etc.)
- **Cross-Provider**: Copy between different document systems
- **Template-Based**: Use placeholders for dynamic paths and content
- **Tenant-Isolated**: All rules are tenant-specific

---

## 🚀 API Endpoints

### Base URL
All endpoints use: `/v3/autodoc/rules`

### Authentication
All requests require Bearer token authentication:
```javascript
headers: {
  'Authorization': 'Bearer <jwt_token>',
  'Content-Type': 'application/json'
}
```

### Core Endpoints

#### 1. List Rules
```http
GET /v3/autodoc/rules
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `active_only` (optional): Filter active rules only

**Response:**
```json
{
  "rules": [
    {
      "id": 1,
      "tenant_id": 123,
      "name": "Client Folder Creation",
      "description": "Create folder structure for new clients",
      "trigger_type": "client.create",
      "trigger_rules": {},
      "rule_config": [
        {
          "action_type": "copy_folder",
          "source_path": "templates/client-folder",
          "target_path": "{client_folder}",
          "provider": "internal",
          "target_provider": "gdrive",
          "override": false
        }
      ],
      "is_active": true,
      "created_at": "2025-08-14T10:30:00Z",
      "updated_at": "2025-08-14T10:30:00Z"
    }
  ],
  "total": 1
}
```

#### 2. Create Rule
```http
POST /v3/autodoc/rules
```

**Request Body:**
```json
{
  "name": "Client Welcome Package",
  "description": "Create welcome documents for new clients",
  "trigger_type": "client.create",
  "trigger_rules": {
    "body.extra.current.stage_text": "initial"
  },
  "rule_config": [
    {
      "action_type": "copy_file",
      "source_path": "templates/welcome-letter.docx",
      "target_path": "{client_folder}/Welcome Letter.docx",
      "provider": "internal",
      "target_provider": "gdrive"
    }
  ],
  "is_active": true
}
```

#### 3. Update Rule
```http
PUT /v3/autodoc/rules/{id}
```

**Request Body:** (All fields optional for partial updates)
```json
{
  "name": "Updated Rule Name",
  "is_active": false,
  "rule_config": [
    {
      "action_type": "copy_folder",
      "source_path": "templates/updated-folder",
      "target_path": "{client_folder}",
      "provider": "internal",
      "target_provider": "gdrive"
    }
  ]
}
```

#### 4. Delete Rule
```http
DELETE /v3/autodoc/rules/{id}
```

#### 5. Execute Rule Manually
```http
POST /v3/autodoc/rules/{id}/execute
```

**Request Body:**
```json
{
  "event_data": {
    "client_id": 123,
    "client_name": "Test Client",
    "client_code": "TC001"
  }
}
```

**Response:**
```json
{
  "rule_id": 1,
  "execution_id": "exec_abc123",
  "status": "completed",
  "actions_executed": 1,
  "actions_failed": 0,
  "execution_time_ms": 450,
  "results": [
    {
      "action_type": "copy_folder",
      "status": "success",
      "source_path": "templates/client-folder",
      "target_path": "Test Client - TC001",
      "created_documents": 5
    }
  ]
}
```

---

## 🏗️ Rule Structure

### Complete Rule Object
```json
{
  "id": 1,
  "tenant_id": 123,
  "name": "Rule Name",
  "description": "Rule description",
  "trigger_type": "client.create",
  "trigger_rules": {
    "field_path": "expected_value",
    "nested.field": {
      "operator": "eq",
      "value": "specific_value"
    }
  },
  "rule_config": [
    {
      "action_type": "copy_file",
      "source_path": "templates/document.docx",
      "target_path": "{client_folder}/Document.docx",
      "provider": "internal",
      "target_provider": "gdrive",
      "override": false
    }
  ],
  "is_active": true,
  "created_user": 456,
  "created_at": "2025-08-14T10:30:00Z",
  "updated_at": "2025-08-14T10:30:00Z"
}
```

### Field Validation Rules

| Field | Type | Required | Validation |
|-------|------|----------|------------|
| `name` | string | ✅ | 1-255 characters |
| `description` | string | ❌ | Max 1000 characters |
| `trigger_type` | string | ✅ | Must be valid event type |
| `trigger_rules` | object | ✅ | Valid condition syntax |
| `rule_config` | array | ✅ | Min 1 action, valid actions |
| `is_active` | boolean | ❌ | Default: false |

---

## ⚡ Trigger Types

### Supported Event Types

| Event Type | Description | When Triggered |
|------------|-------------|----------------|
| `client.create` | New client created | Client record inserted |
| `client.update` | Client information updated | Client record modified |
| `matter.create` | New matter created | Matter record inserted |
| `matter.update` | Matter information updated | Matter record modified |

### Event Data Structure

#### Client Events
```json
{
  "topic": "client.create",
  "body": {
    "id": 10219,
    "name": "Acme Corporation",
    "short_name": "ACME",
    "code": "C06855",
    "tenant_id": 1,
    "extra": {
      "current": {
        "name": "Acme Corporation",
        "owners": [23, 45],
        "stage_name": "Initial",
        "stage_text": "initial"
      },
      "old": {
        "stage_name": "Lead",
        "stage_text": "lead"
      }
    }
  }
}
```

#### Matter Events
```json
{
  "topic": "matter.create",
  "body": {
    "id": 123,
    "client_id": 456,
    "name": "Contract Review Matter",
    "code": "M00123",
    "tenant_id": 1,
    "actor_id": 789,
    "extra": {
      "current": {
        "name": "Contract Review Matter",
        "owners": [101, 102],
        "matter_type": "contract"
      }
    }
  }
}
```

---

## 🎯 Trigger Rules

Trigger rules define **when** a rule should execute based on event data. The system supports multiple condition formats for maximum flexibility.

### 1. Simple Conditions (Direct Value Matching)
```json
{
  "trigger_rules": {
    "body.name": "Specific Client Name",
    "body.extra.current.stage_text": "initial"
  }
}
```

### 2. Complex Conditions (Operator-Based)
```json
{
  "trigger_rules": {
    "body.extra.current.stage_text": {
      "operator": "in",
      "value": ["initial", "active"]
    },
    "body.name": {
      "operator": "contains",
      "value": "Corp"
    }
  }
}
```

### 3. Array Conditions (Multiple Conditions per Field)
**This is the recommended format for complex logic:**
```json
{
  "trigger_rules": {
    "body.extra.current.stage_text": [
      {
        "operator": "ne",
        "value": "{body.extra.old.stage_text}"
      },
      {
        "operator": "eq",
        "value": "initial"
      }
    ],
    "body.name": [
      {
        "operator": "contains",
        "value": "Corp"
      },
      {
        "operator": "not_contains",
        "value": "Test"
      }
    ]
  }
}
```

**Array Logic**: All conditions in an array must be true (AND logic).

### Supported Operators

| Operator | Description | Value Type | Example |
|----------|-------------|------------|---------|
| `eq` | Equals | Any | `{"operator": "eq", "value": "initial"}` |
| `ne` | Not equals | Any | `{"operator": "ne", "value": "closed"}` |
| `in` | Value in array | Array | `{"operator": "in", "value": ["active", "pending"]}` |
| `not_in` | Value not in array | Array | `{"operator": "not_in", "value": ["closed", "archived"]}` |
| `contains` | String contains | String | `{"operator": "contains", "value": "LLC"}` |
| `not_contains` | String doesn't contain | String | `{"operator": "not_contains", "value": "test"}` |
| `starts_with` | String starts with | String | `{"operator": "starts_with", "value": "John"}` |
| `ends_with` | String ends with | String | `{"operator": "ends_with", "value": "Inc"}` |
| `gt` | Greater than | Number | `{"operator": "gt", "value": 1000}` |
| `gte` | Greater than or equal | Number | `{"operator": "gte", "value": 100}` |
| `lt` | Less than | Number | `{"operator": "lt", "value": 50}` |
| `lte` | Less than or equal | Number | `{"operator": "lte", "value": 10}` |
| `exists` | Field exists | null | `{"operator": "exists"}` |
| `not_exists` | Field doesn't exist | null | `{"operator": "not_exists"}` |

### 4. Mixed Conditions (Different Formats per Field)
```json
{
  "trigger_rules": {
    "body.name": "Specific Client Name",
    "body.extra.current.stage_text": {
      "operator": "in",
      "value": ["initial", "active"]
    },
    "body.extra.current.owners": [
      {
        "operator": "exists"
      },
      {
        "operator": "not_in",
        "value": []
      }
    ]
  }
}
```

### 5. Advanced Array Conditions
**Stage Change Detection Example:**
```json
{
  "trigger_rules": {
    "body.extra.current.stage_text": [
      {
        "operator": "ne",
        "value": "{body.extra.old.stage_text}"
      },
      {
        "operator": "eq",
        "value": "initial"
      }
    ]
  }
}
```
*This matches when stage changes TO "initial" from any other stage.*

**Client Type and Status Example:**
```json
{
  "trigger_rules": {
    "body.name": [
      {
        "operator": "contains",
        "value": "Corp"
      },
      {
        "operator": "not_contains",
        "value": "Test"
      }
    ],
    "body.extra.current.stage_text": [
      {
        "operator": "in",
        "value": ["active", "initial"]
      }
    ]
  }
}
```
*This matches corporate clients (contains "Corp", not "Test") in active/initial stages.*

### Field Path Examples
```json
{
  "body.id": 123,                           // Direct field
  "body.extra.current.stage_text": "initial", // Nested field
  "body.extra.current.owners": [23, 45]       // Array field
}
```

### Condition Evaluation Logic

#### Field-Level Logic (Between Fields)
All fields in `trigger_rules` use **AND** logic:
```json
{
  "trigger_rules": {
    "body.name": "Corp Client",           // Field 1: must match
    "body.extra.current.stage_text": "initial"  // Field 2: must also match
  }
}
```
*Both conditions must be true.*

#### Array-Level Logic (Within Field)
All conditions in an array use **AND** logic:
```json
{
  "trigger_rules": {
    "body.extra.current.stage_text": [
      {"operator": "ne", "value": "closed"},    // Condition 1: must be true
      {"operator": "in", "value": ["active", "initial"]}  // Condition 2: must also be true
    ]
  }
}
```
*All array conditions must be true.*

#### Practical Examples

**Example 1: Client Stage Change**
```json
{
  "trigger_rules": {
    "body.extra.current.stage_text": [
      {
        "operator": "ne",
        "value": "{body.extra.old.stage_text}"
      },
      {
        "operator": "eq",
        "value": "initial"
      }
    ]
  }
}
```
*Triggers when stage changes AND new stage is "initial".*

**Example 2: Corporate Client Activation**
```json
{
  "trigger_rules": {
    "body.name": [
      {
        "operator": "contains",
        "value": "Corp"
      },
      {
        "operator": "not_contains",
        "value": "Test"
      }
    ],
    "body.extra.current.stage_text": "active"
  }
}
```
*Triggers for corporate clients (contains "Corp", not "Test") when stage is "active".*

---

## 🔧 Actions

Actions define **what** operations to perform when a rule triggers.

### Supported Action Types

| Action Type | Description | Use Case |
|-------------|-------------|----------|
| `copy_file` | Copy single file | Copy template to client folder |
| `copy_folder` | Copy entire folder | Copy folder structure |
| `generate_document` | Generate from template | Create personalized documents |

### Action Structure
```json
{
  "action_type": "copy_file",
  "source_path": "templates/contract.docx",
  "target_path": "{client_folder}/Contract.docx",
  "provider": "internal",
  "target_provider": "gdrive",
  "override": false
}
```

### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `action_type` | string | ✅ | Type of action to perform |
| `source_path` | string | ✅ | Path from AutoDocRoot |
| `target_path` | string | ✅ | Destination path with placeholders |
| `provider` | string | ❌ | Source provider (default: "internal") |
| `target_provider` | string | ❌ | Target provider (uses tenant default if empty) |
| `override` | boolean | ❌ | Whether to override existing files/folders (default: false) |

### Override Behavior

The `override` field controls what happens when a file or folder with the same name already exists at the target location.

#### Behavior Matrix

| Override | File Exists | Behavior | Log Level | Use Case |
|----------|-------------|----------|-----------|----------|
| `false` (default) | ❌ No | ✅ Create new | Info | Initial setup |
| `false` (default) | ✅ Yes | ⚠️ Skip operation | Warning | Safe mode |
| `true` | ❌ No | ✅ Create new | Info | Update mode |
| `true` | ✅ Yes | ✅ Replace existing | Info | Content refresh |

#### Override Examples

**Safe Mode (override: false)**
```json
{
  "action_type": "generate_document",
  "source_path": "templates/welcome.docx",
  "target_path": "{client_folder}/Welcome Letter.docx",
  "override": false
}
```
- ✅ **If file doesn't exist**: Creates new document
- ⚠️ **If file exists**: Skips operation, logs warning, continues processing
- 🛡️ **Data Safety**: No accidental overwrites

**Update Mode (override: true)**
```json
{
  "action_type": "copy_file",
  "source_path": "templates/updated-contract.docx",
  "target_path": "{client_folder}/Contract.docx",
  "override": true
}
```
- ✅ **If file doesn't exist**: Creates new document
- ✅ **If file exists**: Replaces with new content
- 🔄 **Use Case**: Template updates, content refresh

#### Decision Guide

**Use `override: false` when:**
- ✅ Setting up new clients/matters
- ✅ One-time document generation
- ✅ Preserving existing content is critical
- ✅ Unsure about impact of overwrites

**Use `override: true` when:**
- ✅ Updating templates or policies
- ✅ Refreshing content regularly
- ✅ Synchronizing from authoritative source
- ✅ Intentionally replacing outdated content

**Use unique filenames when:**
- ✅ Want to keep history of changes
- ✅ Need audit trail of document versions
- ✅ Multiple updates expected over time

#### Safety Tips
1. **Start with `override: false`** - Safer default behavior
2. **Test rules thoroughly** - Verify behavior in development
3. **Use descriptive filenames** - Include timestamps or versions when needed
4. **Monitor logs** - Watch for unexpected skips or overwrites
5. **Document intent** - Add comments explaining override choices

### Provider Options

| Provider | Description | Status |
|----------|-------------|--------|
| `internal` | Internal document storage | ✅ Full support |
| `gdrive` | Google Drive integration | ✅ Full support |
| `sharepoint` | SharePoint integration | 🔄 Framework ready |

### Cross-Provider Examples

#### Internal to Google Drive (Safe Mode)
```json
{
  "action_type": "copy_file",
  "source_path": "templates/welcome.docx",
  "target_path": "{client_folder}/Welcome.docx",
  "provider": "internal",
  "target_provider": "gdrive",
  "override": false
}
```

#### Google Drive to Internal (Update Mode)
```json
{
  "action_type": "copy_folder",
  "source_path": "shared/templates",
  "target_path": "{matter_folder}/Templates",
  "provider": "gdrive",
  "target_provider": "internal",
  "override": true
}
```

#### Document Generation with Override
```json
{
  "action_type": "generate_document",
  "source_path": "templates/contract-template.docx",
  "target_path": "{client_folder}/Contract - {client.short_name}.docx",
  "provider": "internal",
  "target_provider": "gdrive",
  "override": false
}
```

---

## 🏷️ Placeholders

Placeholders enable dynamic path generation using event data.

### Available Placeholders

#### Direct Event Data
- `{topic}` → "client.create"
- `{body.name}` → "Acme Corporation"
- `{body.code}` → "C06855"
- `{body.extra.current.stage_text}` → "initial"

#### Special Folder Placeholders
- `{client_folder}` → Auto-resolves to client's root folder
- `{matter_folder}` → Auto-resolves to matter's root folder

#### Timestamp Placeholders
- `{timestamp}` → "20250814_143022"
- `{date}` → "2025-08-14"

### Placeholder Examples

#### Dynamic File Names
```json
{
  "target_path": "{client_folder}/Welcome Letter - {body.name} - {date}.docx"
}
// Result: "Acme Corporation/Welcome Letter - Acme Corporation - 2025-08-14.docx"
```

#### Nested Folder Structure
```json
{
  "target_path": "{client_folder}/Documents/{body.code}_Templates"
}
// Result: "Acme Corporation/Documents/C06855_Templates"
```

#### Conditional Paths
```json
{
  "target_path": "{matter_folder}/{body.extra.current.matter_type}/Checklist.docx"
}
// Result: "Contract Matter/contract/Checklist.docx"
```

---

## 💻 UI Implementation Examples

### React Component Example

```jsx
import React, { useState, useEffect } from 'react';

const AutoDocRulesManager = () => {
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch rules
  const fetchRules = async () => {
    setLoading(true);
    try {
      const response = await fetch('/v3/autodoc/rules', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      setRules(data.rules);
    } catch (error) {
      console.error('Failed to fetch rules:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create new rule
  const createRule = async (ruleData) => {
    try {
      const response = await fetch('/v3/autodoc/rules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ruleData)
      });
      
      if (response.ok) {
        fetchRules(); // Refresh list
      } else {
        const error = await response.json();
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Failed to create rule:', error);
    }
  };

  // Toggle rule active status
  const toggleRule = async (ruleId, isActive) => {
    try {
      const response = await fetch(`/v3/autodoc/rules/${ruleId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: !isActive })
      });
      
      if (response.ok) {
        fetchRules(); // Refresh list
      }
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  };

  useEffect(() => {
    fetchRules();
  }, []);

  return (
    <div className="autodoc-rules-manager">
      <h2>AutoDoc Rules</h2>
      
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="rules-list">
          {rules.map(rule => (
            <div key={rule.id} className="rule-card">
              <h3>{rule.name}</h3>
              <p>{rule.description}</p>
              <div className="rule-details">
                <span>Trigger: {rule.trigger_type}</span>
                <span>Actions: {rule.rule_config.length}</span>
                <button 
                  onClick={() => toggleRule(rule.id, rule.is_active)}
                  className={rule.is_active ? 'active' : 'inactive'}
                >
                  {rule.is_active ? 'Active' : 'Inactive'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### Form Builder Example

```jsx
const RuleFormBuilder = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    trigger_type: 'client.create',
    trigger_rules: {},
    rule_config: [],
    is_active: false
  });

  const triggerTypes = [
    { value: 'client.create', label: 'Client Created' },
    { value: 'client.update', label: 'Client Updated' },
    { value: 'matter.create', label: 'Matter Created' },
    { value: 'matter.update', label: 'Matter Updated' }
  ];

  const actionTypes = [
    { value: 'copy_file', label: 'Copy File' },
    { value: 'copy_folder', label: 'Copy Folder' },
    { value: 'generate_document', label: 'Generate Document' }
  ];

  const providers = [
    { value: 'internal', label: 'Internal Storage' },
    { value: 'gdrive', label: 'Google Drive' },
    { value: 'sharepoint', label: 'SharePoint' }
  ];

  const addAction = () => {
    setFormData(prev => ({
      ...prev,
      rule_config: [
        ...prev.rule_config,
        {
          action_type: 'copy_file',
          source_path: '',
          target_path: '',
          provider: 'internal',
          target_provider: ''
        }
      ]
    }));
  };

  const updateAction = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      rule_config: prev.rule_config.map((action, i) => 
        i === index ? { ...action, [field]: value } : action
      )
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="rule-form">
      <div className="form-group">
        <label>Rule Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          required
          maxLength={255}
        />
      </div>

      <div className="form-group">
        <label>Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          maxLength={1000}
        />
      </div>

      <div className="form-group">
        <label>Trigger Type</label>
        <select
          value={formData.trigger_type}
          onChange={(e) => setFormData(prev => ({ ...prev, trigger_type: e.target.value }))}
        >
          {triggerTypes.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <label>Actions</label>
        {formData.rule_config.map((action, index) => (
          <div key={index} className="action-config">
            <select
              value={action.action_type}
              onChange={(e) => updateAction(index, 'action_type', e.target.value)}
            >
              {actionTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            
            <input
              type="text"
              placeholder="Source Path"
              value={action.source_path}
              onChange={(e) => updateAction(index, 'source_path', e.target.value)}
            />
            
            <input
              type="text"
              placeholder="Target Path"
              value={action.target_path}
              onChange={(e) => updateAction(index, 'target_path', e.target.value)}
            />
            
            <select
              value={action.provider}
              onChange={(e) => updateAction(index, 'provider', e.target.value)}
            >
              {providers.map(provider => (
                <option key={provider.value} value={provider.value}>
                  {provider.label}
                </option>
              ))}
            </select>
            
            <select
              value={action.target_provider}
              onChange={(e) => updateAction(index, 'target_provider', e.target.value)}
            >
              <option value="">Use Tenant Default</option>
              {providers.map(provider => (
                <option key={provider.value} value={provider.value}>
                  {provider.label}
                </option>
              ))}
            </select>
          </div>
        ))}
        
        <button type="button" onClick={addAction}>
          Add Action
        </button>
      </div>

      <div className="form-group">
        <label>
          <input
            type="checkbox"
            checked={formData.is_active}
            onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
          />
          Active
        </label>
      </div>

      <button type="submit">Create Rule</button>
    </form>
  );
};
```

---

## ⚠️ Validation & Error Handling

### Common Validation Errors

| Error Code | Message | Solution |
|------------|---------|----------|
| `400` | "Rule name is required" | Provide non-empty name |
| `400` | "Invalid trigger type" | Use supported trigger type |
| `400` | "Invalid trigger rules" | Check condition syntax |
| `400` | "Rule config is required" | Add at least one action |
| `404` | "Rule not found" | Check rule ID exists |

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "name": ["Name is required"],
      "trigger_type": ["Invalid trigger type"]
    }
  }
}
```

### Client-Side Validation

```javascript
const validateRule = (ruleData) => {
  const errors = {};

  // Name validation
  if (!ruleData.name || ruleData.name.trim().length === 0) {
    errors.name = 'Rule name is required';
  } else if (ruleData.name.length > 255) {
    errors.name = 'Rule name cannot exceed 255 characters';
  }

  // Trigger type validation
  const validTriggerTypes = ['client.create', 'client.update', 'matter.create', 'matter.update'];
  if (!validTriggerTypes.includes(ruleData.trigger_type)) {
    errors.trigger_type = 'Invalid trigger type';
  }

  // Trigger rules validation
  if (ruleData.trigger_rules) {
    const triggerRulesErrors = validateTriggerRules(ruleData.trigger_rules);
    if (triggerRulesErrors.length > 0) {
      errors.trigger_rules = triggerRulesErrors;
    }
  }

  // Actions validation
  if (!ruleData.rule_config || ruleData.rule_config.length === 0) {
    errors.rule_config = 'At least one action is required';
  } else {
    ruleData.rule_config.forEach((action, index) => {
      if (!action.source_path) {
        errors[`action_${index}_source_path`] = 'Source path is required';
      }
      if (!action.target_path) {
        errors[`action_${index}_target_path`] = 'Target path is required';
      }
    });
  }

  return Object.keys(errors).length > 0 ? errors : null;
};

const validateTriggerRules = (triggerRules) => {
  const errors = [];
  const validOperators = ['eq', 'ne', 'in', 'not_in', 'contains', 'not_contains',
                         'starts_with', 'ends_with', 'gt', 'gte', 'lt', 'lte',
                         'exists', 'not_exists'];

  Object.entries(triggerRules).forEach(([field, conditions]) => {
    if (!field || field.trim().length === 0) {
      errors.push('Field path cannot be empty');
      return;
    }

    // Handle different condition formats
    const conditionsArray = Array.isArray(conditions) ? conditions : [conditions];

    conditionsArray.forEach((condition, index) => {
      if (typeof condition === 'string' || typeof condition === 'number' || typeof condition === 'boolean') {
        // Simple value - valid
        return;
      }

      if (typeof condition === 'object' && condition !== null) {
        // Complex condition object
        if (condition.operator) {
          // Standard format: {"operator": "eq", "value": "something"}
          if (!validOperators.includes(condition.operator)) {
            errors.push(`Invalid operator "${condition.operator}" for field "${field}"`);
          }

          if (!['exists', 'not_exists'].includes(condition.operator) && condition.value === undefined) {
            errors.push(`Value is required for operator "${condition.operator}" in field "${field}"`);
          }
        } else {
          // Legacy format: {"eq": "value"} or {"in": ["value1", "value2"]}
          const operators = Object.keys(condition);
          if (operators.length !== 1) {
            errors.push(`Condition must have exactly one operator for field "${field}"`);
          } else {
            const operator = operators[0];
            if (!validOperators.includes(operator)) {
              errors.push(`Invalid operator "${operator}" for field "${field}"`);
            }
          }
        }
      } else {
        errors.push(`Invalid condition format for field "${field}" at index ${index}`);
      }
    });
  });

  return errors;
};
```

### Change Detection for Update Rules

For `.update` trigger types (`matter.update`, `client.update`), the system provides warnings when rules don't include change detection logic:

#### Warning Message
When creating or updating a rule with `.update` trigger type that doesn't check for data changes, you'll receive:

```json
{
  "id": 123,
  "name": "Client Update Rule",
  "trigger_type": "client.update",
  "message": "Warning: client.update rule does not check for data changes. Consider adding previous/current field comparisons to ensure rule only triggers when data actually changes."
}
```

#### Recommended Pattern for Update Rules

**❌ Without Change Detection (triggers warning):**
```json
{
  "trigger_type": "client.update",
  "trigger_rules": {
    "client_type": "corporate"
  }
}
```

**✅ With Change Detection (no warning):**
```json
{
  "trigger_type": "client.update",
  "trigger_rules": {
    "extra.current.stage_text": "active",
    "extra.previous.stage_text": "pending"
  }
}
```

#### Available Change Detection Fields

**For `matter.update`:**
- `extra.current.category_name` / `extra.previous.category_name`
- Other matter fields with current/previous comparison

**For `client.update`:**
- `extra.current.stage_text` / `extra.previous.stage_text`
- Other client fields with current/previous comparison

---

## 🧪 Testing & Debugging

### Manual Rule Testing

Use the execute endpoint to test rules with sample data:

```javascript
const testRule = async (ruleId, testData) => {
  try {
    const response = await fetch(`/v3/autodoc/rules/${ruleId}/execute`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event_data: testData
      })
    });
    
    const result = await response.json();
    console.log('Execution result:', result);
    return result;
  } catch (error) {
    console.error('Test execution failed:', error);
  }
};

// Test client creation rule
testRule(1, {
  client_id: 123,
  client_name: "Test Client",
  client_code: "TC001"
});
```

### Debug Checklist

1. **Rule Not Triggering**
   - ✅ Check `is_active` is true
   - ✅ Verify `trigger_type` matches event
   - ✅ Validate `trigger_rules` conditions
   - ✅ Check event data structure

2. **Action Failures**
   - ✅ Verify source path exists in AutoDocRoot
   - ✅ Check placeholder syntax
   - ✅ Validate provider availability
   - ✅ Confirm target folder permissions

3. **Cross-Provider Issues**
   - ✅ Check provider configuration
   - ✅ Verify external ID mappings
   - ✅ Test provider connectivity

### Logging & Monitoring

Monitor rule execution through:
- API response status codes
- Execution result details
- Server logs for detailed errors
- Rule execution history

---

## 🎨 UI/UX Best Practices

### Rule Builder Interface

#### Recommended UI Flow
1. **Rule Basic Info** → Name, Description, Active status
2. **Trigger Configuration** → Event type, Conditions builder
3. **Actions Configuration** → Action type, Paths, Providers
4. **Preview & Test** → Show generated paths, Test execution
5. **Save & Activate** → Final confirmation

#### Condition Builder Component
```jsx
const ConditionBuilder = ({ triggerRules, onChange }) => {
  const addField = () => {
    const newField = 'body.name';
    onChange({
      ...triggerRules,
      [newField]: [{ operator: 'eq', value: '' }]
    });
  };

  const addConditionToField = (field) => {
    const existingConditions = triggerRules[field] || [];
    const newCondition = { operator: 'eq', value: '' };

    // Convert to array format if not already
    let conditionsArray;
    if (Array.isArray(existingConditions)) {
      conditionsArray = [...existingConditions, newCondition];
    } else {
      // Convert single condition to array
      conditionsArray = [existingConditions, newCondition];
    }

    onChange({
      ...triggerRules,
      [field]: conditionsArray
    });
  };

  const updateCondition = (field, conditionIndex, property, value) => {
    const fieldConditions = triggerRules[field];

    if (Array.isArray(fieldConditions)) {
      const updatedConditions = fieldConditions.map((condition, index) =>
        index === conditionIndex
          ? { ...condition, [property]: value }
          : condition
      );

      onChange({
        ...triggerRules,
        [field]: updatedConditions
      });
    } else {
      // Single condition - convert to array format
      const updatedCondition = { ...fieldConditions, [property]: value };
      onChange({
        ...triggerRules,
        [field]: [updatedCondition]
      });
    }
  };

  return (
    <div className="condition-builder">
      {Object.entries(triggerRules).map(([field, conditions]) => (
        <div key={field} className="field-group">
          <div className="field-header">
            <select
              value={field}
              onChange={(e) => {
                const newField = e.target.value;
                const { [field]: oldConditions, ...rest } = triggerRules;
                onChange({ ...rest, [newField]: oldConditions });
              }}
            >
              <option value="body.name">Client Name</option>
              <option value="body.code">Client Code</option>
              <option value="body.extra.current.stage_text">Stage</option>
              <option value="body.extra.current.owners">Owners</option>
            </select>
            <button onClick={() => addConditionToField(field)}>
              Add Condition
            </button>
          </div>

          <div className="conditions-list">
            {(Array.isArray(conditions) ? conditions : [conditions]).map((condition, conditionIndex) => (
              <div key={conditionIndex} className="condition-row">
                <select
                  value={condition.operator || 'eq'}
                  onChange={(e) => updateCondition(field, conditionIndex, 'operator', e.target.value)}
                >
                  <option value="eq">Equals</option>
                  <option value="ne">Not Equals</option>
                  <option value="contains">Contains</option>
                  <option value="not_contains">Not Contains</option>
                  <option value="in">In List</option>
                  <option value="not_in">Not In List</option>
                  <option value="exists">Field Exists</option>
                  <option value="not_exists">Field Not Exists</option>
                </select>

                {!['exists', 'not_exists'].includes(condition.operator) && (
                  <input
                    type="text"
                    value={condition.value || ''}
                    onChange={(e) => updateCondition(field, conditionIndex, 'value', e.target.value)}
                    placeholder="Value (use comma for arrays)"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      ))}

      <button onClick={addField} className="add-field-btn">
        Add Field
      </button>
    </div>
  );
};
```

#### Path Builder with Placeholder Helper
```jsx
const PathBuilder = ({ value, onChange, availablePlaceholders }) => {
  const insertPlaceholder = (placeholder) => {
    const newValue = value + `{${placeholder}}`;
    onChange(newValue);
  };

  return (
    <div className="path-builder">
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Enter path with placeholders"
      />

      <div className="placeholder-helper">
        <h4>Available Placeholders:</h4>
        {availablePlaceholders.map(placeholder => (
          <button
            key={placeholder.key}
            type="button"
            onClick={() => insertPlaceholder(placeholder.key)}
            title={placeholder.description}
          >
            {`{${placeholder.key}}`}
          </button>
        ))}
      </div>

      <div className="path-preview">
        <strong>Preview:</strong> {generatePreview(value)}
      </div>
    </div>
  );
};
```

### User Experience Guidelines

#### Visual Indicators
- **Active Rules**: Green indicator, "Active" badge
- **Inactive Rules**: Gray indicator, "Inactive" badge
- **Rule Errors**: Red indicator, error message tooltip
- **Cross-Provider Actions**: Special icon showing provider flow

#### Feedback & Notifications
- **Success**: "Rule created successfully"
- **Validation Error**: Highlight invalid fields with specific messages
- **Execution Status**: Real-time status updates during manual execution
- **Provider Status**: Show provider health in action configuration

---

## 🔄 Advanced Integration Patterns

### Real-Time Rule Monitoring

```jsx
const RuleMonitor = ({ ruleId }) => {
  const [executions, setExecutions] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(async () => {
        try {
          const response = await fetch(`/v3/autodoc/rules/${ruleId}/executions`);
          const data = await response.json();
          setExecutions(data.executions);
        } catch (error) {
          console.error('Failed to fetch executions:', error);
        }
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [ruleId, isMonitoring]);

  return (
    <div className="rule-monitor">
      <button onClick={() => setIsMonitoring(!isMonitoring)}>
        {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
      </button>

      <div className="executions-list">
        {executions.map(execution => (
          <div key={execution.id} className={`execution-item ${execution.status}`}>
            <span>{execution.created_at}</span>
            <span>{execution.status}</span>
            <span>{execution.actions_executed}/{execution.actions_total}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Bulk Operations

```jsx
const BulkRuleOperations = ({ selectedRules, onComplete }) => {
  const bulkToggleActive = async (isActive) => {
    const promises = selectedRules.map(ruleId =>
      fetch(`/v3/autodoc/rules/${ruleId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: isActive })
      })
    );

    try {
      await Promise.all(promises);
      onComplete('success', `${selectedRules.length} rules updated`);
    } catch (error) {
      onComplete('error', 'Some operations failed');
    }
  };

  return (
    <div className="bulk-operations">
      <button onClick={() => bulkToggleActive(true)}>
        Activate Selected ({selectedRules.length})
      </button>
      <button onClick={() => bulkToggleActive(false)}>
        Deactivate Selected ({selectedRules.length})
      </button>
    </div>
  );
};
```

---

## 📊 Performance Considerations

### Optimization Tips

1. **Pagination**: Always use pagination for rule lists
2. **Debouncing**: Debounce search and filter inputs
3. **Caching**: Cache rule data and refresh strategically
4. **Lazy Loading**: Load rule details on demand
5. **Batch Updates**: Group multiple rule updates

### Example Implementation

```jsx
const OptimizedRulesList = () => {
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);

  // Debounced search
  const debouncedSearch = useCallback(
    debounce(async (term) => {
      setLoading(true);
      try {
        const response = await fetch(
          `/v3/autodoc/rules?search=${term}&page=1&limit=20`
        );
        const data = await response.json();
        setRules(data.rules);
        setPage(1);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    if (searchTerm) {
      debouncedSearch(searchTerm);
    } else {
      fetchRules(page);
    }
  }, [searchTerm, page]);

  return (
    <div className="optimized-rules-list">
      <input
        type="text"
        placeholder="Search rules..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />

      {loading ? (
        <div>Loading...</div>
      ) : (
        <VirtualizedList
          items={rules}
          renderItem={({ item }) => <RuleCard rule={item} />}
          itemHeight={120}
        />
      )}
    </div>
  );
};
```

---

## 📚 Additional Resources

- **[AUTODOC_COMPLETE.md](./AUTODOC_COMPLETE.md)**: Complete technical documentation
- **[README.md](./README.md)**: Quick start guide
- **API Specification**: `/api/api.yml` - OpenAPI 3.0 spec

### Quick Reference Cards

#### Trigger Types Quick Reference
```
client.create  → New client created
client.update  → Client information changed
matter.create  → New matter created
matter.update  → Matter information changed
```

#### Action Types Quick Reference
```
copy_file      → Copy single file
copy_folder    → Copy entire folder structure
generate_document → Generate from template with placeholders
```

#### Trigger Rules Format Quick Reference
```
Simple:     {"field": "value"}
Complex:    {"field": {"operator": "eq", "value": "something"}}
Array:      {"field": [{"operator": "ne", "value": "old"}, {"operator": "eq", "value": "new"}]}
Mixed:      {"field1": "value", "field2": [{"operator": "in", "value": ["a", "b"]}]}
```

#### Common Operators Quick Reference
```
eq, ne          → Equals, Not equals
in, not_in      → In array, Not in array
contains        → String contains substring
starts_with     → String starts with value
exists          → Field exists in event data
gt, gte, lt, lte → Numeric comparisons
```

#### Common Placeholders Quick Reference
```
{client_folder}    → Auto-resolved client folder path
{matter_folder}    → Auto-resolved matter folder path
{body.name}        → Entity name from event
{body.code}        → Entity code from event
{date}             → Current date (YYYY-MM-DD)
{timestamp}        → Current timestamp (YYYYMMDD_HHMMSS)
```

---

*This comprehensive guide provides everything needed for successful AutoDoc Rules UI integration. For backend implementation details and advanced configuration, refer to the complete technical documentation.*
