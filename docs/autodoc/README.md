# AutoDoc Documentation

> **📖 Complete Documentation**: See [AUTODOC_COMPLETE.md](./AUTODOC_COMPLETE.md) for comprehensive documentation

## Quick Start

AutoDoc is a document automation system that allows you to create rules that automatically generate documents based on events from matters and clients.

## 🎯 Key Features

- **Event-Driven**: Responds to client/matter lifecycle events
- **Multi-Provider**: Works with internal storage, Google Drive, SharePoint
- **Template Processing**: Dynamic placeholder replacement in documents
- **Cross-Platform**: Copy files/folders between different storage providers
- **Flexible Rules**: Complex conditional logic and multiple actions per rule
- **Override Control**: Safe-by-default with optional override for updates

### Base URL
All API endpoints are prefixed with `/v3/autodoc`

### Authentication
- All requests require valid tenant authentication
- Tenant ID is extracted from request context
- All operations are tenant-isolated for security

## 📚 Documentation Structure

- **[AUTODOC_COMPLETE.md](./AUTODOC_COMPLETE.md)**: Complete technical documentation
- **[troubleshooting-guide.md](./troubleshooting-guide.md)**: Critical issues & provider-specific behavior
- **[README.md](./README.md)**: This quick start guide

## 🚀 Quick API Reference

### Core Endpoints

#### AutoDocRoot Management
**GET** `/v3/documents?autodoc_root=true`
- Lists template files in tenant's AutoDocRoot
- Creates AutoDocRoot if it doesn't exist

#### Document Automation Rules
**GET** `/v3/autodoc/rules` - List all rules
**POST** `/v3/autodoc/rules` - Create new rule
**POST** `/v3/autodoc/rules/{id}/execute` - Execute rule manually

### Example Rule Creation
```json
{
  "name": "Client Folder Creation",
  "trigger": {
    "event_type": "client.create"
  },
  "actions": [
    {
      "action_type": "copy_folder",
      "source_path": "templates/client-folder",
      "target_path": "{client_folder}",
      "provider": "internal",
      "target_provider": "gdrive",
      "override": false
    }
  ]
}
    ]
  }
}
```

## 🔧 Development

### Build & Test
```bash
# Build
go build -o bin/server ./cmd/server

# Test AutoDoc
go test -v ./internal/service/autodoc

# Test All
go test -race -cover -v -count=1 -timeout=30s ./...
```

### Debug
```bash
# Check service logs
tail -f /usr/local/var/log/runlog_docman.log | grep autodoc

# Test rule execution
curl -X POST "http://localhost:8088/v3/autodoc/rules/1/execute" \
  -H "X-Tenant-ID: 1" -H "X-User-ID: 123"
```

## 📖 Complete Documentation

For comprehensive documentation including technical design, implementation details, deployment guide, and troubleshooting, see **[AUTODOC_COMPLETE.md](./AUTODOC_COMPLETE.md)**.

---

*For detailed API documentation, technical architecture, and implementation guides, refer to the complete documentation file.*
