---
type: "manual"
---

## Test endpoint locally

- After finish implementation, run `svcman restart docman` to restart server
- Server's running at `http://localhost:8088`
- For local testing, using headers based authorization, including these headers
    - `X-Tenant-ID: 1`
    - `X-User-ID: 1`
    - `X-User-Roles: 8` (admin role)
- Log file is at `/usr/local/var/log/runlog_docman.log`, check it to further debug

