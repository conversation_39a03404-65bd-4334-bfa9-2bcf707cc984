resolve-type-alias: false
disable-version-string: true
issue-845-fix: true
with-expecter: true
packages:
  "bilabl/docman/pkg/repositories":
    config:
      dir: mocks/repositories
      outpkg: repositories
    interfaces:
      DocumentRepository: ~
      DocumentConfigRepository: ~
      DocumentMappingRepository: ~
      DocumentSettingRepository: ~
      DocumentPermissionMappingRepository: ~
      UploadSessionRepository: ~
  "bilabl/docman/pkg/sharepointclient":
    config:
      dir: mocks/sharepointclient
      outpkg: sharepointclient
    interfaces:
      Client: ~
  "bilabl/docman/pkg/gdrive":
    config:
      dir: mocks/gdrive
      outpkg: gdrive
    interfaces:
      DriveClient: ~
  "bilabl/docman/internal/service/gdrive":
    config:
      dir: mocks/service/gdrive
      outpkg: gdrive
    interfaces:
      Service: ~
      DocumentService: ~
      FolderService: ~
  "bilabl/docman/internal/service/autodoc":
    config:
      dir: mocks/service/autodoc
      outpkg: autodoc
    interfaces:
      AutoDocService: ~
      ActionHandler: ~
      ActionHandlerRegistry: ~
      DocumentService: ~
      DocumentServiceRegistry: ~
      HealthChecker: ~
      RuleMatchingService: ~
      EventRuleMatchingService: ~
      RuleExecutionEngine: ~
      MappingService: ~
      UploadProvider: ~
      UploadProviderRegistry: ~
      FileProvider: ~
      FileProviderRegistry: ~
