package gdrive

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"bilabl/docman/internal/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"gitlab.com/goxp/cloud0/ginext"
)

// UploadHandler handles Google Drive file upload operations for V3 API
type UploadHandler struct {
	documentService gdrive.DocumentService
	serviceURL      string
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(documentService gdrive.DocumentService, serviceURL string) *UploadHandler {
	return &UploadHandler{
		documentService: documentService,
		serviceURL:      serviceURL,
	}
}

// UploadV3 handles POST /v3/gdrive/documents/upload
func (h *UploadHandler) UploadV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "UploadV3")

	req := &UploadV3Request{}
	r.MustBind(req)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Generating upload URL file_name=%s parent_drive_id=%s tenant_id=%d", req.FileName, req.ID, act.TenantID)

	// Validate request
	if err := ValidateUploadRequest(r.Context(), req); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Resolve parent ID (with hierarchical path support)
	parentID, err := h.resolveParentID(r.Context(), req, &act)
	if err != nil {
		log.WithError(err).Error("Failed to resolve parent ID")
		return handleServiceError(r.Context(), err)
	}

	// Create upload session for 2-step upload proxy
	sessionReq := &gdrive.CreateUploadSessionRequest{
		TenantID:      act.TenantID,
		FileName:      req.FileName,
		ParentDriveID: parentID,
	}

	sessionResult, err := h.documentService.CreateUploadSession(r.Context(), sessionReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to create upload session file_name=%s parent_drive_id=%s", req.FileName, req.ID)
		return handleServiceError(r.Context(), err)
	}

	uploadURL := fmt.Sprintf("%s/v3/gdrive/upload/%s", h.serviceURL, sessionResult.SessionToken)

	response := &UploadV3Response{
		UploadUrl:    uploadURL,
		SessionToken: sessionResult.SessionToken,
		ExpiresAt:    sessionResult.ExpiresAt.Format(time.RFC3339),
	}

	log.Infof("Successfully created upload session file_name=%s session_token=%s", req.FileName, sessionResult.SessionToken)
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// UploadContentV3 handles file content upload for 2-step upload process
func (h *UploadHandler) UploadContentV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "UploadContentV3")

	// Get session token from URL parameter
	sessionToken := r.GinCtx.Param("session_token")
	if sessionToken == "" {
		log.Error("Missing session token in URL")
		return newErrorResponse(http.StatusBadRequest, "Session token is required")
	}

	// Get content type from header
	contentType := r.GinCtx.GetHeader("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream" // Default content type
	}

	// Get content length from header
	contentLength := r.GinCtx.Request.ContentLength

	log.Infof("Uploading file content session_token=%s content_type=%s content_length=%d",
		sessionToken, contentType, contentLength)

	// Get upload session (globally unique token, no tenant validation needed)
	session, err := h.documentService.GetUploadSession(r.Context(), sessionToken)
	if err != nil {
		log.WithError(err).Errorf("Failed to get upload session session_token=%s", sessionToken)
		return handleServiceError(r.Context(), err)
	}

	// Upload file content
	result, err := h.documentService.UploadFileContent(r.Context(), sessionToken, r.GinCtx.Request.Body, contentType, contentLength)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload file content session_token=%s", sessionToken)
		return handleServiceError(r.Context(), err)
	}

	response := &UploadContentV3Response{
		Status:       result.Status,
		DriveFileID:  result.DriveFileID,
		FileName:     result.FileName,
		FileSize:     result.FileSize,
		ContentType:  result.ContentType,
		SessionToken: result.SessionToken,
	}

	log.Infof("Successfully uploaded file content session_token=%s drive_file_id=%s file_name=%s",
		sessionToken, result.DriveFileID, session.FileName)
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// resolveParentID resolves the parent folder ID using hierarchical path or fallback methods
func (h *UploadHandler) resolveParentID(ctx context.Context, req *UploadV3Request, act *actor.Actor) (string, error) {
	log := logger.WithCtx(ctx, "resolveParentID")

	// 1. If both parent_id and parent_path: resolve hierarchical path from parent_id
	if req.ID != "" && req.ParentPath != "" {
		log.Infof("Resolving hierarchical path parent_path=%s from base parent_id=%s", req.ParentPath, req.ID)
		pathReq := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:    act.TenantID,
			ParentPath:  req.ParentPath,
			RootDriveID: req.ID,
			ObjectType:  req.ObjectType,
			ObjectID:    req.ObjectID,
		}
		pathResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve hierarchical path parent_path=%s from base parent_id=%s", req.ParentPath, req.ID)
			return "", fmt.Errorf("failed to resolve parent path: %w", err)
		}
		log.Infof("Successfully resolved hierarchical path to final_parent_id=%s", pathResp.FinalParentID)
		return pathResp.FinalParentID, nil
	}

	// 2. If only parent_id: return it
	if req.ID != "" {
		log.Infof("Using provided parent_id=%s as base folder", req.ID)
		return req.ID, nil
	}

	// 3. If object_type/object_id and parent_path: resolve mapping, then hierarchical path
	if req.ObjectType != "" && req.ObjectID != 0 && req.ParentPath != "" {
		log.Infof("Resolving mapping for object_type=%s object_id=%d as base for parent_path=%s", req.ObjectType, req.ObjectID, req.ParentPath)
		// Resolve mapping
		pathReq := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:   act.TenantID,
			ObjectType: req.ObjectType,
			ObjectID:   req.ObjectID,
		}
		mappingResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve object mapping object_type=%s object_id=%d", req.ObjectType, req.ObjectID)
			return "", fmt.Errorf("failed to resolve parent folder from object mapping: %w", err)
		}
		baseParentID := mappingResp.FinalParentID
		log.Infof("Successfully resolved object mapping to parent_id=%s", baseParentID)
		// Now resolve hierarchical path from mapping
		pathReq2 := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:    act.TenantID,
			ParentPath:  req.ParentPath,
			RootDriveID: baseParentID,
			ObjectType:  req.ObjectType,
			ObjectID:    req.ObjectID,
		}
		pathResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq2)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve hierarchical path parent_path=%s from base parent_id=%s", req.ParentPath, baseParentID)
			return "", fmt.Errorf("failed to resolve parent path: %w", err)
		}
		log.Infof("Successfully resolved hierarchical path to final_parent_id=%s", pathResp.FinalParentID)
		return pathResp.FinalParentID, nil
	}

	// 4. If only object_type/object_id: resolve mapping, return
	if req.ObjectType != "" && req.ObjectID != 0 {
		log.Infof("Resolving mapping for object_type=%s object_id=%d", req.ObjectType, req.ObjectID)
		pathReq := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:   act.TenantID,
			ObjectType: req.ObjectType,
			ObjectID:   req.ObjectID,
		}
		mappingResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve object mapping object_type=%s object_id=%d", req.ObjectType, req.ObjectID)
			return "", fmt.Errorf("failed to resolve parent folder from object mapping: %w", err)
		}
		log.Infof("Successfully resolved object mapping to parent_id=%s", mappingResp.FinalParentID)
		return mappingResp.FinalParentID, nil
	}

	// 5. If only parent_path: use "root" as base, resolve hierarchical path
	if req.ParentPath != "" {
		log.Infof("No base parent specified, using 'root' as default for parent_path")
		pathReq := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:    act.TenantID,
			ParentPath:  req.ParentPath,
			RootDriveID: "root",
			ObjectType:  req.ObjectType,
			ObjectID:    req.ObjectID,
		}
		pathResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve hierarchical path parent_path=%s from base_parent_id=root", req.ParentPath)
			return "", fmt.Errorf("failed to resolve parent path: %w", err)
		}
		log.Infof("Successfully resolved hierarchical path to final_parent_id=%s", pathResp.FinalParentID)
		return pathResp.FinalParentID, nil
	}

	// 6. No valid parent specification
	return "", fmt.Errorf("no valid parent specification provided")
}
