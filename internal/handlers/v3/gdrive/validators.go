package gdrive

import (
	"context"
	"fmt"
	"strings"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// ValidateCreateDocumentRequest validates create document request parameters
func ValidateCreateDocumentRequest(ctx context.Context, req *CreateDocumentV3Request) error {
	log := logger.WithCtx(ctx, "ValidateCreateDocumentRequest")

	log.Infof("Validating create document request name=%s parent_id=%s parent_path=%s", req.Name, req.ParentID, req.ParentPath)

	if req.Name == "" {
		log.Error("Document name is required")
		return fmt.Errorf("document name is required")
	}

	// Validate parent specification - need at least one way to determine parent
	hasParentID := req.ParentID != ""
	hasParentPath := req.ParentPath != ""
	hasObjectMapping := req.ObjectType != "" && req.ObjectID != 0

	// Check if we have at least one valid parent specification
	if !hasParentID && !hasParentPath && !hasObjectMapping {
		log.Error("Must provide parent_id, parent_path, or both object_type and object_id")
		return fmt.Errorf("must provide parent_id, parent_path, or both object_type and object_id")
	}

	// Check for incomplete object mapping
	if (req.ObjectType != "" && req.ObjectID == 0) || (req.ObjectType == "" && req.ObjectID != 0) {
		log.Error("Must provide parent_id, parent_path, or both object_type and object_id")
		return fmt.Errorf("must provide parent_id, parent_path, or both object_type and object_id")
	}

	// Validate parent_path format if provided
	if hasParentPath {
		if err := validateParentPath(req.ParentPath); err != nil {
			log.WithError(err).Errorf("Invalid parent_path parent_path=%s", req.ParentPath)
			return fmt.Errorf("invalid parent_path: %w", err)
		}
	}

	// Validate document name
	if err := validateDocumentName(req.Name); err != nil {
		log.WithError(err).Errorf("Invalid document name name=%s", req.Name)
		return fmt.Errorf("invalid document name: %w", err)
	}

	log.Infof("Create document request validation passed name=%s", req.Name)
	return nil
}

// ValidateListDocumentRequest validates list document request parameters
func ValidateListDocumentRequest(ctx context.Context, req *ListDocumentV3Request) error {
	log := logger.WithCtx(ctx, "ValidateListDocumentRequest")

	log.Infof("Validating list document request id=%s object_type=%s object_id=%s",
		req.ID, req.ObjectType, req.ObjectID)

	if req.ID == "" && (req.ObjectType == "" || req.ObjectID == "") {
		log.Error("Either id or both object_type and object_id must be provided")
		return fmt.Errorf("either id or both object_type and object_id must be provided")
	}

	// Validate and normalize page size
	if req.PageSize < 0 {
		log.Errorf("Invalid page size page_size=%d", req.PageSize)
		return fmt.Errorf("page_size must be non-negative")
	}
	if req.PageSize > 1000 {
		log.Infof("Page size capped to maximum page_size=%d max=1000", req.PageSize)
		req.PageSize = 1000 // Cap at Google Drive maximum
	}
	if req.PageSize == 0 {
		req.PageSize = 100 // Default
		log.Infof("Using default page size page_size=%d", req.PageSize)
	}

	// Sanitize search query if provided
	if req.Search != "" {
		req.Search = sanitizeSearchQuery(req.Search)
		log.Infof("Sanitized search query search=%s", req.Search)
	}

	log.Infof("List document request validation passed page_size=%d", req.PageSize)
	return nil
}

// ValidateUpdateDocumentRequest validates update document request parameters
func ValidateUpdateDocumentRequest(ctx context.Context, req *UpdateDocumentV3Request) error {
	log := logger.WithCtx(ctx, "ValidateUpdateDocumentRequest")

	log.Infof("Validating update document request name=%s", req.Name)

	if req.Name == "" {
		log.Error("Document name is required")
		return fmt.Errorf("document name is required")
	}

	// Validate document name
	if err := validateDocumentName(req.Name); err != nil {
		log.WithError(err).Errorf("Invalid document name name=%s", req.Name)
		return fmt.Errorf("invalid document name: %w", err)
	}

	log.Infof("Update document request validation passed name=%s", req.Name)
	return nil
}

// ValidateUploadRequest validates upload request parameters
func ValidateUploadRequest(ctx context.Context, req *UploadV3Request) error {
	log := logger.WithCtx(ctx, "ValidateUploadRequest")

	log.Infof("Validating upload request file_name=%s parent_id=%s parent_path=%s", req.FileName, req.ID, req.ParentPath)

	if req.FileName == "" {
		log.Error("File name is required")
		return fmt.Errorf("file name is required")
	}

	// Validate parent specification - need at least one way to determine parent
	hasParentID := req.ID != ""
	hasParentPath := req.ParentPath != ""
	hasObjectMapping := req.ObjectType != "" && req.ObjectID != 0

	// Check if we have at least one valid parent specification
	if !hasParentID && !hasParentPath && !hasObjectMapping {
		log.Error("Must provide id, parent_path, or both object_type and object_id")
		return fmt.Errorf("must provide id, parent_path, or both object_type and object_id")
	}

	// Check for incomplete object mapping
	if (req.ObjectType != "" && req.ObjectID == 0) || (req.ObjectType == "" && req.ObjectID != 0) {
		log.Error("Either id or both object_type and object_id must be provided")
		return fmt.Errorf("either id or both object_type and object_id must be provided")
	}

	// Validate parent_path format if provided
	if hasParentPath {
		if err := validateParentPath(req.ParentPath); err != nil {
			log.WithError(err).Errorf("Invalid parent_path parent_path=%s", req.ParentPath)
			return fmt.Errorf("invalid parent_path: %w", err)
		}
	}

	// Validate file name
	if err := validateDocumentName(req.FileName); err != nil {
		log.WithError(err).Errorf("Invalid file name file_name=%s", req.FileName)
		return fmt.Errorf("invalid file name: %w", err)
	}

	log.Infof("Upload request validation passed file_name=%s", req.FileName)
	return nil
}

// validateDocumentName validates document/file name according to Google Drive rules
func validateDocumentName(name string) error {
	if len(name) == 0 {
		return fmt.Errorf("document name cannot be empty")
	}

	if len(name) > 255 {
		return fmt.Errorf("document name exceeds maximum length of 255 characters")
	}

	// Check for invalid characters (Google Drive restrictions)
	invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range invalidChars {
		if strings.Contains(name, char) {
			return fmt.Errorf("document name contains invalid character: %s", char)
		}
	}

	// Check for reserved names
	reservedNames := []string{".", "..", "CON", "PRN", "AUX", "NUL"}
	upperName := strings.ToUpper(strings.TrimSpace(name))
	for _, reserved := range reservedNames {
		if upperName == reserved {
			return fmt.Errorf("document name is reserved: %s", name)
		}
	}

	// Check for leading/trailing whitespace
	if strings.TrimSpace(name) != name {
		return fmt.Errorf("document name cannot have leading or trailing whitespace")
	}

	return nil
}

// validateParentPath validates the hierarchical folder path format
func validateParentPath(parentPath string) error {
	if parentPath == "" {
		return fmt.Errorf("parent_path cannot be empty")
	}

	// Remove leading and trailing slashes
	cleanPath := strings.Trim(parentPath, "/")
	if cleanPath == "" {
		return fmt.Errorf("parent_path cannot contain only slashes")
	}

	// Split path into segments
	segments := strings.Split(cleanPath, "/")

	// Validate each segment
	for i, segment := range segments {
		// Remove extra spaces and check for empty segments
		segment = strings.TrimSpace(segment)
		if segment == "" {
			return fmt.Errorf("parent_path segment %d is empty or contains only spaces", i+1)
		}

		// Validate segment as a folder name
		if err := validateDocumentName(segment); err != nil {
			return fmt.Errorf("parent_path segment %d (%s) is invalid: %w", i+1, segment, err)
		}
	}

	return nil
}

// sanitizeSearchQuery sanitizes search query to prevent injection attacks
func sanitizeSearchQuery(query string) string {
	// Remove potentially dangerous characters first
	query = strings.ReplaceAll(query, "\"", "")
	query = strings.ReplaceAll(query, ";", "")
	query = strings.ReplaceAll(query, "\\", "")

	// Then escape single quotes for Google Drive API (replace ' with \')
	query = strings.ReplaceAll(query, "'", "\\'")

	// Limit length
	if len(query) > 100 {
		query = query[:100]
	}

	return strings.TrimSpace(query)
}

// ValidateSearchFilesRequest validates search files request parameters
func ValidateSearchFilesRequest(ctx context.Context, req *SearchFilesV3Request) error {
	log := logger.WithCtx(ctx, "ValidateSearchFilesRequest")

	log.Infof("Validating search files request keyword=%s page_size=%d", req.Keyword, req.PageSize)

	if req.Keyword == "" {
		log.Error("Search keyword is required")
		return fmt.Errorf("search keyword is required")
	}

	// Validate and normalize page size
	if req.PageSize < 0 {
		log.Errorf("Invalid page size page_size=%d", req.PageSize)
		return fmt.Errorf("page_size must be non-negative")
	}
	if req.PageSize > 1000 {
		log.Infof("Page size capped to maximum page_size=%d max=1000", req.PageSize)
		req.PageSize = 1000 // Cap at Google Drive maximum
	}
	if req.PageSize == 0 {
		req.PageSize = 100 // Default
		log.Infof("Using default page size page_size=%d", req.PageSize)
	}

	// Sanitize search keyword
	req.Keyword = sanitizeSearchQuery(req.Keyword)
	log.Infof("Sanitized search keyword keyword=%s", req.Keyword)

	log.Infof("Search files request validation passed keyword=%s page_size=%d", req.Keyword, req.PageSize)
	return nil
}
