package gdrive

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"bilabl/docman/domain/model"
	gdriveService "bilabl/docman/internal/service/gdrive"
	gdriveSvc "bilabl/docman/pkg/gdrive"

	mock_repositories "bilabl/docman/mocks/repositories"
	mock_gdrive "bilabl/docman/mocks/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/errors"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

// Test constants
const (
	testTenantID = uint64(1)
	testUserID   = uint64(123)
	validURLOrID = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
	shortURLOrID = "short"
)

func TestSetupHandler_TestSetupV3_Simple(t *testing.T) {
	// Setup mock service
	mockService := mock_gdrive.NewMockService(t)
	driveInfo := &gdriveSvc.DriveInfo{
		ID:   "drive-123",
		Name: "Test Drive",
		Type: gdriveSvc.ResourceTypeSharedDrive,
	}
	mockService.On("TestSetup", mock.Anything, validURLOrID).Return(driveInfo, nil).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create ginext handler function
	handler := func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.TestSetupV3(r)
	}

	// Create request body
	requestBody := &TestSetupV3Request{
		URLOrID: validURLOrID,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware using proper auth context key
	engine.Use(func(c *gin.Context) {
		// Set headers that actor.FromGinCtx() expects
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/test-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return handler(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/test-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Debug: Print response body
	t.Logf("Response body: %s", w.Body.String())

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data TestSetupV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "success", response.Status)
	assert.NotNil(t, response.DriveInfo)
	assert.Equal(t, "drive-123", response.DriveInfo.ID)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_CompleteSetupV3_Success(t *testing.T) {
	// Setup mock service
	mockService := mock_gdrive.NewMockService(t)
	expectedResponse := &gdriveService.CompleteSetupResponse{
		AppliedPathConfig: &model.PathConfig{
			ClientFolderPath:   "{short_name|name} - {code}",
			MatterFolderPath:   "{client_folder}/matters/{name} - {code}",
			CaseFormat:         "original",
			MaxLength:          255,
			InvalidCharReplace: "_",
		},
	}
	mockService.On("CompleteSetup", mock.Anything, mock.MatchedBy(func(req *gdriveService.CompleteSetupRequest) bool {
		return req.URLOrID == validURLOrID && req.TenantID == testTenantID && req.Enabled == true && req.PathConfig == nil
	})).Return(expectedResponse, nil).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body
	requestBody := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: true,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware using proper auth context key
	engine.Use(func(c *gin.Context) {
		// Set headers that actor.FromGinCtx() expects
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/complete-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.CompleteSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/complete-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CompleteSetupV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "success", response.Status)
	assert.True(t, response.Enabled)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_CompleteSetupV3_AuthorizationError(t *testing.T) {
	// Setup mock service (no calls expected)
	mockService := mock_gdrive.NewMockService(t)

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body
	requestBody := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: true,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add staff user to context middleware (should be unauthorized)
	engine.Use(func(c *gin.Context) {
		// Set headers for staff user (role=1, should be unauthorized)
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "1") // Staff role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Staff User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/complete-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.CompleteSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/complete-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get authorization error
	assert.Equal(t, http.StatusForbidden, w.Code)

	// Verify mocks (no service calls should have been made)
	mockService.AssertExpectations(t)
}

func TestSetupHandler_TestSetupV3_ValidationError(t *testing.T) {
	// Setup mock service (no calls expected for validation errors)
	mockService := mock_gdrive.NewMockService(t)

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body with empty URL (should fail validation)
	requestBody := &TestSetupV3Request{
		URLOrID: "",
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers that actor.FromGinCtx() expects
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/test-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.TestSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/test-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get validation error
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Verify mocks (no service calls should have been made)
	mockService.AssertExpectations(t)
}

func TestSetupHandler_TestSetupV3_ServiceError(t *testing.T) {
	// Setup mock service to return error
	mockService := mock_gdrive.NewMockService(t)
	mockService.On("TestSetup", mock.Anything, validURLOrID).
		Return(nil, errors.NewBadDataErr("Invalid Google Drive URL")).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body
	requestBody := &TestSetupV3Request{
		URLOrID: validURLOrID,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers that actor.FromGinCtx() expects
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/test-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.TestSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/test-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get service error
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_CompleteSetupV3_ManagerUser(t *testing.T) {
	// Setup mock service
	mockService := mock_gdrive.NewMockService(t)
	expectedResponse := &gdriveService.CompleteSetupResponse{
		AppliedPathConfig: &model.PathConfig{
			ClientFolderPath:   "{short_name|name} - {code}",
			MatterFolderPath:   "{client_folder}/matters/{name} - {code}",
			CaseFormat:         "original",
			MaxLength:          255,
			InvalidCharReplace: "_",
		},
	}
	mockService.On("CompleteSetup", mock.Anything, mock.MatchedBy(func(req *gdriveService.CompleteSetupRequest) bool {
		return req.URLOrID == validURLOrID && req.TenantID == testTenantID && req.Enabled == false
	})).Return(expectedResponse, nil).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body
	requestBody := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: false,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add manager user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for manager user (role=4, should be authorized)
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "4") // Manager role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Manager User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/complete-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.CompleteSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/complete-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CompleteSetupV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "success", response.Status)
	assert.False(t, response.Enabled)
	assert.Equal(t, "disabled", response.State)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_CompleteSetupV3_SysadminUser(t *testing.T) {
	// Setup mock service
	mockService := mock_gdrive.NewMockService(t)
	expectedResponse := &gdriveService.CompleteSetupResponse{
		AppliedPathConfig: &model.PathConfig{
			ClientFolderPath:   "{short_name|name} - {code}",
			MatterFolderPath:   "{client_folder}/matters/{name} - {code}",
			CaseFormat:         "original",
			MaxLength:          255,
			InvalidCharReplace: "_",
		},
	}
	mockService.On("CompleteSetup", mock.Anything, mock.MatchedBy(func(req *gdriveService.CompleteSetupRequest) bool {
		return req.URLOrID == validURLOrID && req.TenantID == testTenantID && req.Enabled == true
	})).Return(expectedResponse, nil).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body
	requestBody := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: true,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add sysadmin user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for sysadmin user (role=16, should be authorized)
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "16") // Sysadmin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Sysadmin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/complete-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.CompleteSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/complete-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CompleteSetupV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "success", response.Status)
	assert.True(t, response.Enabled)
	assert.Equal(t, "enabled", response.State)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_TestSetupV3_ShortURL(t *testing.T) {
	// Setup mock service (no calls expected for validation errors)
	mockService := mock_gdrive.NewMockService(t)

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body with short URL (should fail validation)
	requestBody := &TestSetupV3Request{
		URLOrID: shortURLOrID,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers that actor.FromGinCtx() expects
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/test-setup", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.TestSetupV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/test-setup", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get validation error
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Verify mocks (no service calls should have been made)
	mockService.AssertExpectations(t)
}

// ========================================
// Document Handler Tests
// ========================================

func TestDocumentsHandler_CreateV3_Success_AdminRole(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	expectedResponse := &gdriveService.DocumentResponse{
		DriveFileID: "folder-123",
		Name:        "Test Document",
	}
	mockDocumentService.On("CreateDocument", mock.Anything, mock.MatchedBy(func(req *gdriveService.CreateDocumentRequest) bool {
		return req.Name == "Test Document" && req.TenantID == testTenantID
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Create request body
	requestBody := &CreateDocumentV3Request{
		Name:       "Test Document",
		ParentID:   "parent-123",
		ObjectType: "matter",
		ObjectID:   123,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add admin user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for admin user (role=8, should be authorized)
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.CreateV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/documents", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CreateDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "folder-123", response.ID)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

func TestDocumentsHandler_CreateV3_Success_StaffRole(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	expectedResponse := &gdriveService.DocumentResponse{
		DriveFileID: "folder-456",
		Name:        "Staff Test Document",
	}
	mockDocumentService.On("CreateDocument", mock.Anything, mock.MatchedBy(func(req *gdriveService.CreateDocumentRequest) bool {
		return req.Name == "Staff Test Document" && req.TenantID == testTenantID
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Create request body
	requestBody := &CreateDocumentV3Request{
		Name:       "Staff Test Document",
		ParentID:   "parent-456",
		ObjectType: "matter",
		ObjectID:   456,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add staff user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for staff user (role=1, should be authorized for document creation)
		c.Request.Header.Set("x-user-id", "456")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "1") // Staff role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Staff User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.CreateV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/documents", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - Staff role should be able to create documents
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CreateDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "folder-456", response.ID)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

func TestDocumentsHandler_CreateV3_Success_ManagerRole(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	expectedResponse := &gdriveService.DocumentResponse{
		DriveFileID: "folder-789",
		Name:        "Manager Test Document",
	}
	mockDocumentService.On("CreateDocument", mock.Anything, mock.MatchedBy(func(req *gdriveService.CreateDocumentRequest) bool {
		return req.Name == "Manager Test Document" && req.TenantID == testTenantID
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Create request body
	requestBody := &CreateDocumentV3Request{
		Name:       "Manager Test Document",
		ParentID:   "parent-789",
		ObjectType: "project",
		ObjectID:   789,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add manager user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for manager user (role=4, should be authorized)
		c.Request.Header.Set("x-user-id", "789")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "4") // Manager role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Manager User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.CreateV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/documents", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CreateDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "folder-789", response.ID)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

func TestDocumentsHandler_CreateV3_Success_SysadminRole(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	expectedResponse := &gdriveService.DocumentResponse{
		DriveFileID: "folder-999",
		Name:        "Sysadmin Test Document",
	}
	mockDocumentService.On("CreateDocument", mock.Anything, mock.MatchedBy(func(req *gdriveService.CreateDocumentRequest) bool {
		return req.Name == "Sysadmin Test Document" && req.TenantID == testTenantID
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Create request body
	requestBody := &CreateDocumentV3Request{
		Name:       "Sysadmin Test Document",
		ParentID:   "parent-999",
		ObjectType: "client",
		ObjectID:   999,
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add sysadmin user to context middleware
	engine.Use(func(c *gin.Context) {
		// Set headers for sysadmin user (role=16, should be authorized)
		c.Request.Header.Set("x-user-id", "999")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "16") // Sysadmin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Sysadmin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.POST("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.CreateV3(r)
	}))

	// Create request
	reqBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest("POST", "/documents", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data CreateDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Equal(t, "folder-999", response.ID)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

func TestDocumentsHandler_ListV3_WebURL_Mapping(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	// Mock response with WebViewLink
	expectedResponse := &gdriveService.DocumentListResponse{
		Documents: []*gdriveService.DocumentResponse{
			{
				DriveFileID: "file-123",
				Name:        "Test Document.pdf",
				TenantID:    testTenantID,
				ContentType: "application/pdf",
				WebViewLink: "https://drive.google.com/file/d/file-123/view",
				IsFile:      true,
				Size:        1024,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			{
				DriveFileID: "folder-456",
				Name:        "Test Folder",
				TenantID:    testTenantID,
				ContentType: "application/vnd.google-apps.folder",
				WebViewLink: "https://drive.google.com/drive/folders/folder-456",
				IsFile:      false,
				Size:        0,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
		},
		NextPageToken: "",
		Total:         2,
	}

	mockDocumentService.On("ListDocuments", mock.Anything, mock.MatchedBy(func(req *gdriveService.ListDocumentsRequest) bool {
		return req.TenantID == testTenantID && req.ParentDriveID == "root"
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add user to context middleware
	engine.Use(func(c *gin.Context) {
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Admin User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.GET("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.ListV3(r)
	}))

	// Create request
	req := httptest.NewRequest("GET", "/documents?id=root", nil)

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data ListDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Len(t, response.Data, 2)

	// Verify WebURL mapping for file
	fileDoc := response.Data[0]
	assert.Equal(t, "file-123", fileDoc.ID)
	assert.Equal(t, "Test Document.pdf", fileDoc.Name)
	assert.Equal(t, "https://drive.google.com/file/d/file-123/view", fileDoc.WebURL)
	assert.True(t, fileDoc.IsFile)
	assert.Equal(t, int64(1024), fileDoc.Size)

	// Verify WebURL mapping for folder
	folderDoc := response.Data[1]
	assert.Equal(t, "folder-456", folderDoc.ID)
	assert.Equal(t, "Test Folder", folderDoc.Name)
	assert.Equal(t, "https://drive.google.com/drive/folders/folder-456", folderDoc.WebURL)
	assert.False(t, folderDoc.IsFile)
	assert.Equal(t, int64(0), folderDoc.Size)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

func TestDocumentsHandler_ListV3_DateFields_Mapping(t *testing.T) {
	// Setup mock services
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	// Fixed timestamps for testing
	createdTime := time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)
	modifiedTime := time.Date(2024, 1, 20, 14, 45, 0, 0, time.UTC)

	// Mock response with date fields
	expectedResponse := &gdriveService.DocumentListResponse{
		Documents: []*gdriveService.DocumentResponse{
			{
				DriveFileID: "file-123",
				Name:        "Test Document.pdf",
				TenantID:    testTenantID,
				ContentType: "application/pdf",
				WebViewLink: "https://drive.google.com/file/d/file-123/view",
				IsFile:      true,
				Size:        2048,
				CreatedAt:   createdTime,
				UpdatedAt:   modifiedTime,
			},
		},
		NextPageToken: "",
		Total:         1,
	}

	mockDocumentService.On("ListDocuments", mock.Anything, mock.MatchedBy(func(req *gdriveService.ListDocumentsRequest) bool {
		return req.TenantID == testTenantID && req.ParentDriveID == "root"
	})).Return(expectedResponse, nil).Once()

	// Create handler
	documentsHandler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add user to context middleware
	engine.Use(func(c *gin.Context) {
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("X-User-Roles", "1") // Staff role to test authorization
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Staff User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.GET("/documents", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return documentsHandler.ListV3(r)
	}))

	// Create request
	req := httptest.NewRequest("GET", "/documents?id=root", nil)

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data ListDocumentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	response := responseWrapper.Data
	assert.Len(t, response.Data, 1)

	// Verify all field mappings
	doc := response.Data[0]
	assert.Equal(t, "file-123", doc.ID)
	assert.Equal(t, "Test Document.pdf", doc.Name)
	assert.Equal(t, "https://drive.google.com/file/d/file-123/view", doc.WebURL)
	assert.True(t, doc.IsFile)
	assert.Equal(t, int64(2048), doc.Size)
	assert.Equal(t, 2, doc.DocType)   // file type
	assert.Equal(t, ".pdf", doc.Type) // file extension
	assert.False(t, doc.IsInternal)   // Always false for Google Drive
	assert.Equal(t, 1, doc.Status)    // Always active

	// Verify date fields are properly mapped
	assert.Equal(t, createdTime, doc.DateCreated)
	assert.Equal(t, modifiedTime, doc.DateModified)
	assert.Equal(t, "Google Drive User", doc.LastModifiedBy)

	// Verify mocks
	mockDocumentService.AssertExpectations(t)
}

// ========================================
// Validation Functions Tests
// ========================================

func TestValidateTestSetupRequest_Success(t *testing.T) {
	ctx := context.Background()

	req := &TestSetupV3Request{
		URLOrID: validURLOrID,
	}

	err := ValidateTestSetupRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateTestSetupRequest_EmptyURL(t *testing.T) {
	ctx := context.Background()

	req := &TestSetupV3Request{
		URLOrID: "",
	}

	err := ValidateTestSetupRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "url_or_id is required")
}

func TestValidateTestSetupRequest_ShortURL(t *testing.T) {
	ctx := context.Background()

	req := &TestSetupV3Request{
		URLOrID: "short",
	}

	err := ValidateTestSetupRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "too short to be valid")
}

func TestValidateCompleteSetupRequest_Success(t *testing.T) {
	ctx := context.Background()

	req := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: true,
	}

	err := ValidateCompleteSetupRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateCompleteSetupRequest_EmptyURL(t *testing.T) {
	ctx := context.Background()

	req := &CompleteSetupV3Request{
		URLOrID: "",
		Enabled: true,
	}

	err := ValidateCompleteSetupRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "url_or_id is required")
}

func TestValidateCompleteSetupRequest_ShortURL(t *testing.T) {
	ctx := context.Background()

	req := &CompleteSetupV3Request{
		URLOrID: shortURLOrID,
		Enabled: false,
	}

	err := ValidateCompleteSetupRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "too short to be valid")
}

func TestValidateCreateDocumentRequest_Success(t *testing.T) {
	ctx := context.Background()

	req := &CreateDocumentV3Request{
		ParentID:   "parent-123",
		Name:       "Test Document",
		ObjectID:   123,
		ObjectType: "project",
	}

	err := ValidateCreateDocumentRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateCreateDocumentRequest_EmptyName(t *testing.T) {
	ctx := context.Background()

	req := &CreateDocumentV3Request{
		ParentID:   "parent-123",
		Name:       "",
		ObjectID:   123,
		ObjectType: "project",
	}

	err := ValidateCreateDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "document name is required")
}

func TestValidateCreateDocumentRequest_NoParentNoObject(t *testing.T) {
	ctx := context.Background()

	req := &CreateDocumentV3Request{
		ParentID:   "",
		Name:       "Test Document",
		ObjectID:   0,
		ObjectType: "",
	}

	err := ValidateCreateDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must provide parent_id, parent_path, or both object_type and object_id")
}

func TestValidateCreateDocumentRequest_WithObjectTypeOnly(t *testing.T) {
	ctx := context.Background()

	req := &CreateDocumentV3Request{
		ParentID:   "",
		Name:       "Test Document",
		ObjectID:   0,
		ObjectType: "project",
	}

	err := ValidateCreateDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must provide parent_id, parent_path, or both object_type and object_id")
}

func TestValidateListDocumentRequest_Success_WithID(t *testing.T) {
	ctx := context.Background()

	req := &ListDocumentV3Request{
		ID:       "folder-123",
		PageSize: 10,
	}

	err := ValidateListDocumentRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateListDocumentRequest_Success_WithObjectInfo(t *testing.T) {
	ctx := context.Background()

	req := &ListDocumentV3Request{
		ObjectType: "project",
		ObjectID:   "123",
		PageSize:   20,
	}

	err := ValidateListDocumentRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateListDocumentRequest_NoIDNoObject(t *testing.T) {
	ctx := context.Background()

	req := &ListDocumentV3Request{
		ID:         "",
		ObjectType: "",
		ObjectID:   "",
		PageSize:   10,
	}

	err := ValidateListDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "either id or both object_type and object_id must be provided")
}

func TestValidateListDocumentRequest_NegativePageSize(t *testing.T) {
	ctx := context.Background()

	req := &ListDocumentV3Request{
		ID:       "folder-123",
		PageSize: -1,
	}

	err := ValidateListDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "page_size must be non-negative")
}

func TestValidateUpdateDocumentRequest_Success(t *testing.T) {
	ctx := context.Background()

	req := &UpdateDocumentV3Request{
		Name: "Updated Document",
	}

	err := ValidateUpdateDocumentRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateUpdateDocumentRequest_EmptyName(t *testing.T) {
	ctx := context.Background()

	req := &UpdateDocumentV3Request{
		Name: "",
	}

	err := ValidateUpdateDocumentRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "document name is required")
}

func TestValidateUploadRequest_Success(t *testing.T) {
	ctx := context.Background()

	req := &UploadV3Request{
		ID:       "parent-123",
		FileName: "test-file.pdf",
	}

	err := ValidateUploadRequest(ctx, req)
	assert.NoError(t, err)
}

func TestValidateUploadRequest_EmptyFileName(t *testing.T) {
	ctx := context.Background()

	req := &UploadV3Request{
		ID:       "parent-123",
		FileName: "",
	}

	err := ValidateUploadRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "file name is required")
}

func TestValidateUploadRequest_EmptyParentID(t *testing.T) {
	ctx := context.Background()

	req := &UploadV3Request{
		ID:       "",
		FileName: "test-file.pdf",
	}

	err := ValidateUploadRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must provide id, parent_path, or both object_type and object_id")
}

// ========================================
// Error Handling Functions Tests
// ========================================

func TestHandleValidationError(t *testing.T) {
	ctx := context.Background()
	testErr := fmt.Errorf("validation failed")

	resp, err := handleValidationError(ctx, testErr)

	// Should return response with error data and no error
	assert.NotNil(t, resp)
	assert.NoError(t, err)

	// Check response status and body
	assert.Equal(t, http.StatusBadRequest, resp.Code)

	// Check response body structure
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, "validation failed")
}

func TestHandleAuthorizationError(t *testing.T) {
	ctx := context.Background()
	message := "Insufficient permissions"

	resp, err := handleAuthorizationError(ctx, message)

	// Should return response with error data and no error
	assert.NotNil(t, resp)
	assert.NoError(t, err)

	// Check response status and body
	assert.Equal(t, http.StatusForbidden, resp.Code)

	// Check response body structure
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, message)
}

func TestHandleAuthenticationError(t *testing.T) {
	ctx := context.Background()
	message := "Authentication failed"

	resp, err := handleAuthenticationError(ctx, message)

	// Should return response with error data and no error
	assert.NotNil(t, resp)
	assert.NoError(t, err)

	// Check response status and body
	assert.Equal(t, http.StatusUnauthorized, resp.Code)

	// Check response body structure
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, message)
}

// TestHandleServiceError_DocumentMappingNotFound tests that DocumentMapping not found errors return 404
func TestHandleServiceError_DocumentMappingNotFound(t *testing.T) {
	ctx := context.Background()

	// Test DocumentServiceError with ErrCodeDocumentNotFound
	docErr := &gdriveService.DocumentServiceError{
		Code:    gdriveService.ErrCodeDocumentNotFound,
		Message: "No document mapping found for matter with ID 12088",
	}

	resp, err := handleServiceError(ctx, docErr)

	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check that it's a ginext.Response with correct status
	assert.Equal(t, http.StatusNotFound, resp.Code)

	// Check response body structure - WithRawBody returns data directly
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, "No document mapping found for matter with ID 12088")
}

// TestHandleServiceError_FolderCreationNotFound tests that FolderCreationError with ErrCodeNotFound returns 404
func TestHandleServiceError_FolderCreationNotFound(t *testing.T) {
	ctx := context.Background()

	// Test FolderCreationError with ErrCodeNotFound
	folderErr := &gdriveService.FolderCreationError{
		Code:    gdriveService.ErrCodeNotFound,
		Message: "Client folder not found. Create client folder first.",
	}

	resp, err := handleServiceError(ctx, folderErr)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusNotFound, resp.Code)

	// Check response body structure - WithRawBody returns data directly
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, "Client folder not found")
}

// TestHandleServiceError_DatabaseError tests that database errors return 400 (not 500)
func TestHandleServiceError_DatabaseError(t *testing.T) {
	ctx := context.Background()

	// Test DocumentServiceError with ErrCodeInvalidRequest (database error)
	docErr := &gdriveService.DocumentServiceError{
		Code:    gdriveService.ErrCodeInvalidRequest,
		Message: "Failed to query document mapping for matter with ID 12088",
		Details: "database connection error",
	}

	resp, err := handleServiceError(ctx, docErr)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusBadRequest, resp.Code)

	// Check response body structure - WithRawBody returns data directly
	errorResp, ok := resp.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
	assert.Contains(t, errorResp.Error.Msg, "Failed to query document mapping")
}

// ========================================
// Helper Functions Tests
// ========================================

func TestHasSetupRoles_AdminRole(t *testing.T) {
	result := hasSetupRoles(userrole.RoleAdmin)
	assert.True(t, result)
}

func TestHasSetupRoles_ManagerRole(t *testing.T) {
	result := hasSetupRoles(userrole.RoleManager)
	assert.True(t, result)
}

func TestHasSetupRoles_SysadminRole(t *testing.T) {
	result := hasSetupRoles(userrole.RoleSysadmin)
	assert.True(t, result)
}

func TestHasSetupRoles_StaffRole(t *testing.T) {
	result := hasSetupRoles(userrole.RoleStaff)
	assert.False(t, result)
}

func TestHasSetupRoles_MultipleRoles(t *testing.T) {
	// Test with admin + staff roles
	result := hasSetupRoles(userrole.RoleAdmin | userrole.RoleStaff)
	assert.True(t, result)
}

func TestHasSetupRoles_NoRoles(t *testing.T) {
	result := hasSetupRoles(userrole.Role(0))
	assert.False(t, result)
}

func TestValidateDocumentName_Success(t *testing.T) {
	validNames := []string{
		"Test Document",
		"file.pdf",
		"My Project 2023",
		"document-with-dashes",
		"document_with_underscores",
		"123456",
	}

	for _, name := range validNames {
		t.Run(name, func(t *testing.T) {
			err := validateDocumentName(name)
			assert.NoError(t, err, "Expected %s to be valid", name)
		})
	}
}

func TestValidateDocumentName_EmptyName(t *testing.T) {
	err := validateDocumentName("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot be empty")
}

func TestValidateDocumentName_TooLong(t *testing.T) {
	longName := strings.Repeat("a", 256) // 256 characters
	err := validateDocumentName(longName)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "exceeds maximum length")
}

func TestValidateDocumentName_InvalidCharacters(t *testing.T) {
	invalidNames := []string{
		"file/name",
		"file\\name",
		"file:name",
		"file*name",
		"file?name",
		"file\"name",
		"file<name",
		"file>name",
		"file|name",
	}

	for _, name := range invalidNames {
		t.Run(name, func(t *testing.T) {
			err := validateDocumentName(name)
			assert.Error(t, err, "Expected %s to be invalid", name)
			assert.Contains(t, err.Error(), "invalid character")
		})
	}
}

func TestValidateDocumentName_ReservedNames(t *testing.T) {
	reservedNames := []string{
		".",
		"..",
		"CON",
		"PRN",
		"AUX",
		"NUL",
		"con", // case insensitive
		"prn",
	}

	for _, name := range reservedNames {
		t.Run(name, func(t *testing.T) {
			err := validateDocumentName(name)
			assert.Error(t, err, "Expected %s to be reserved", name)
			assert.Contains(t, err.Error(), "reserved")
		})
	}
}

func TestValidateDocumentName_WhitespaceIssues(t *testing.T) {
	invalidNames := []string{
		" leading-space",
		"trailing-space ",
		"  both-spaces  ",
		"\tleading-tab",
		"trailing-tab\t",
	}

	for _, name := range invalidNames {
		t.Run(fmt.Sprintf("'%s'", name), func(t *testing.T) {
			err := validateDocumentName(name)
			assert.Error(t, err, "Expected '%s' to be invalid due to whitespace", name)
			assert.Contains(t, err.Error(), "whitespace")
		})
	}
}

func TestSanitizeSearchQuery_Success(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Normal query",
			input:    "test document",
			expected: "test document",
		},
		{
			name:     "Query with spaces",
			input:    "  test document  ",
			expected: "test document",
		},
		{
			name:     "Empty query",
			input:    "",
			expected: "",
		},
		{
			name:     "Query with single quotes",
			input:    "test 'document'",
			expected: "test \\'document\\'",
		},
		{
			name:     "Query with double quotes",
			input:    "test \"document\"",
			expected: "test document",
		},
		{
			name:     "Query with semicolons",
			input:    "test;document;name",
			expected: "testdocumentname",
		},
		{
			name:     "Query with backslashes",
			input:    "test\\document\\name",
			expected: "testdocumentname",
		},
		{
			name:     "Query with all dangerous chars",
			input:    "test'\";\\ document",
			expected: "test\\' document",
		},
		{
			name:     "Long query (over 100 chars)",
			input:    strings.Repeat("a", 150),
			expected: strings.Repeat("a", 100),
		},
		{
			name:     "Long query with dangerous chars",
			input:    strings.Repeat("a'\"", 50),       // 150 chars: "a'\"a'\"a'\"..."
			expected: strings.Repeat("a\\'", 33) + "a", // 33 * 3 + 1 = 100 chars exactly
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := sanitizeSearchQuery(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// ========================================
// Page Size Validation Tests
// ========================================

func TestValidateListDocumentRequest_PageSizeNormalization(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name         string
		inputSize    int
		expectedSize int
	}{
		{
			name:         "Zero page size (default)",
			inputSize:    0,
			expectedSize: 100,
		},
		{
			name:         "Valid page size",
			inputSize:    50,
			expectedSize: 50,
		},
		{
			name:         "Maximum page size",
			inputSize:    1000,
			expectedSize: 1000,
		},
		{
			name:         "Over maximum page size (capped)",
			inputSize:    1500,
			expectedSize: 1000,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req := &ListDocumentV3Request{
				ID:       "folder-123",
				PageSize: tc.inputSize,
			}

			err := ValidateListDocumentRequest(ctx, req)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedSize, req.PageSize)
		})
	}
}

func TestValidateListDocumentRequest_SearchQuerySanitization(t *testing.T) {
	ctx := context.Background()

	req := &ListDocumentV3Request{
		ID:       "folder-123",
		PageSize: 10,
		Search:   "test'\";\\ query",
	}

	err := ValidateListDocumentRequest(ctx, req)
	assert.NoError(t, err)
	assert.Equal(t, "test\\' query", req.Search) // Should be sanitized
}

// ========================================
// Edge Cases and Boundary Conditions Tests
// ========================================

func TestValidateCreateDocumentRequest_EdgeCases(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name        string
		req         *CreateDocumentV3Request
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid with ParentID only",
			req: &CreateDocumentV3Request{
				ParentID:   "parent-123",
				Name:       "Test Document",
				ObjectID:   0,
				ObjectType: "",
			},
			expectError: false,
		},
		{
			name: "Valid with ObjectType and ObjectID only",
			req: &CreateDocumentV3Request{
				ParentID:   "",
				Name:       "Test Document",
				ObjectID:   123,
				ObjectType: "project",
			},
			expectError: false,
		},
		{
			name: "Invalid with ObjectID but no ObjectType",
			req: &CreateDocumentV3Request{
				ParentID:   "",
				Name:       "Test Document",
				ObjectID:   123,
				ObjectType: "",
			},
			expectError: true,
			errorMsg:    "must provide parent_id, parent_path, or both object_type and object_id",
		},
		{
			name: "Invalid with ObjectType but no ObjectID",
			req: &CreateDocumentV3Request{
				ParentID:   "",
				Name:       "Test Document",
				ObjectID:   0,
				ObjectType: "project",
			},
			expectError: true,
			errorMsg:    "must provide parent_id, parent_path, or both object_type and object_id",
		},
		{
			name: "Invalid document name with special chars",
			req: &CreateDocumentV3Request{
				ParentID:   "parent-123",
				Name:       "test/document",
				ObjectID:   0,
				ObjectType: "",
			},
			expectError: true,
			errorMsg:    "invalid document name",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := ValidateCreateDocumentRequest(ctx, tc.req)
			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// ========== PathConfig Tests ==========

func TestSetupHandler_CompleteSetupV3_WithPathConfig_Success(t *testing.T) {
	// Setup mock service
	mockService := mock_gdrive.NewMockService(t)

	// Expected PathConfig that should be applied (merged with defaults)
	expectedAppliedConfig := &model.PathConfig{
		ClientFolderPath:   "{name} - {code}",                        // Custom value
		MatterFolderPath:   "{client_folder}/custom/{name} - {code}", // Custom value
		CaseFormat:         "title",                                  // Custom value
		MaxLength:          200,                                      // Custom value
		InvalidCharReplace: "-",                                      // Custom value
	}

	expectedResponse := &gdriveService.CompleteSetupResponse{
		AppliedPathConfig: expectedAppliedConfig,
	}

	mockService.On("CompleteSetup", mock.Anything, mock.MatchedBy(func(req *gdriveService.CompleteSetupRequest) bool {
		return req.URLOrID == validURLOrID &&
			req.TenantID == testTenantID &&
			req.Enabled == true &&
			req.PathConfig != nil &&
			req.PathConfig.ClientFolderPath == "{name} - {code}" &&
			req.PathConfig.MatterFolderPath == "{client_folder}/custom/{name} - {code}" &&
			req.PathConfig.CaseFormat == "title" &&
			req.PathConfig.MaxLength == 200 &&
			req.PathConfig.InvalidCharReplace == "-"
	})).Return(expectedResponse, nil).Once()

	// Create handler
	setupHandler := NewSetupHandler(mockService, nil, nil)

	// Create request body with PathConfig
	requestBody := &CompleteSetupV3Request{
		URLOrID: validURLOrID,
		Enabled: true,
		PathConfig: &PathConfigV3{
			ClientFolderPath:   "{name} - {code}",
			MatterFolderPath:   "{client_folder}/custom/{name} - {code}",
			CaseFormat:         "title",
			MaxLength:          200,
			InvalidCharReplace: "-",
		},
	}

	// Use HTTP test approach
	w := httptest.NewRecorder()
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware
	engine.Use(func(c *gin.Context) {
		c.Request.Header.Set("x-user-id", "123")
		c.Request.Header.Set("x-tenant-id", "1")
		c.Request.Header.Set("x-user-roles", "8") // Admin role
		c.Next()
	})

	// Create ginext handler function
	handler := func(r *ginext.Request) (*ginext.Response, error) {
		return setupHandler.CompleteSetupV3(r)
	}

	// Register route
	engine.POST("/test", ginext.WrapHandler(handler))

	// Create request
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	var ginextResponse struct {
		Data CompleteSetupV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &ginextResponse)
	assert.NoError(t, err)

	// Get the actual response data
	response := ginextResponse.Data

	// Verify response content
	assert.Equal(t, "success", response.Status)
	assert.Equal(t, "Google Drive integration has been enabled successfully", response.Message)
	assert.True(t, response.Enabled)
	assert.Equal(t, "enabled", response.State)

	// Verify PathConfig in response
	assert.NotNil(t, response.PathConfig)
	assert.Equal(t, "{name} - {code}", response.PathConfig.ClientFolderPath)
	assert.Equal(t, "{client_folder}/custom/{name} - {code}", response.PathConfig.MatterFolderPath)
	assert.Equal(t, "title", response.PathConfig.CaseFormat)
	assert.Equal(t, 200, response.PathConfig.MaxLength)
	assert.Equal(t, "-", response.PathConfig.InvalidCharReplace)

	// Verify mocks
	mockService.AssertExpectations(t)
}

func TestSetupHandler_CompleteSetupV3_PathConfig_ValidationErrors(t *testing.T) {
	testCases := []struct {
		name        string
		pathConfig  *PathConfigV3
		expectedErr string
	}{
		{
			name: "Invalid CaseFormat",
			pathConfig: &PathConfigV3{
				CaseFormat: "invalid_format",
			},
			expectedErr: "case_format must be one of: original, lower, upper, title",
		},
		{
			name: "Invalid MaxLength - too small",
			pathConfig: &PathConfigV3{
				MaxLength: -1,
			},
			expectedErr: "max_length must be between 1 and 255",
		},
		{
			name: "Invalid MaxLength - too large",
			pathConfig: &PathConfigV3{
				MaxLength: 300,
			},
			expectedErr: "max_length must be between 1 and 255",
		},
		{
			name: "Invalid InvalidCharReplace - multiple characters",
			pathConfig: &PathConfigV3{
				InvalidCharReplace: "abc",
			},
			expectedErr: "invalid_char_replace must be a single character",
		},
		{
			name: "Invalid ClientFolderPath - path traversal",
			pathConfig: &PathConfigV3{
				ClientFolderPath: "../{name}",
			},
			expectedErr: "path contains invalid traversal patterns",
		},
		{
			name: "Invalid ClientFolderPath - invalid variable",
			pathConfig: &PathConfigV3{
				ClientFolderPath: "{invalid_var} - {code}",
			},
			expectedErr: "invalid variable 'invalid_var' in client template",
		},
		{
			name: "Invalid MatterFolderPath - missing client_folder",
			pathConfig: &PathConfigV3{
				MatterFolderPath: "{name} - {code}",
			},
			expectedErr: "matter folder path must contain {client_folder} variable",
		},
		{
			name: "Invalid MatterFolderPath - invalid variable",
			pathConfig: &PathConfigV3{
				MatterFolderPath: "{client_folder}/{invalid_var}",
			},
			expectedErr: "invalid variable 'invalid_var' in matter template",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mock service (should not be called due to validation error)
			mockService := mock_gdrive.NewMockService(t)

			// Create handler
			setupHandler := NewSetupHandler(mockService, nil, nil)

			// Create request body with invalid PathConfig
			requestBody := &CompleteSetupV3Request{
				URLOrID:    validURLOrID,
				Enabled:    true,
				PathConfig: tc.pathConfig,
			}

			// Use HTTP test approach
			w := httptest.NewRecorder()
			engine := gin.Default()

			// Add error handling middleware
			engine.Use(ginext.CreateErrorHandler(false))

			// Add actor to context middleware
			engine.Use(func(c *gin.Context) {
				c.Request.Header.Set("x-user-id", "123")
				c.Request.Header.Set("x-tenant-id", "1")
				c.Request.Header.Set("x-user-roles", "8") // Admin role
				c.Next()
			})

			// Create ginext handler function
			handler := func(r *ginext.Request) (*ginext.Response, error) {
				return setupHandler.CompleteSetupV3(r)
			}

			// Register route
			engine.POST("/test", ginext.WrapHandler(handler))

			// Create request
			jsonBody, _ := json.Marshal(requestBody)
			req, _ := http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")

			// Execute request
			engine.ServeHTTP(w, req)

			// Verify error response
			assert.Equal(t, http.StatusBadRequest, w.Code)

			// Check that error message contains expected text
			responseBody := w.Body.String()
			assert.Contains(t, responseBody, tc.expectedErr)

			// Verify mocks (should not be called)
			mockService.AssertExpectations(t)
		})
	}
}
