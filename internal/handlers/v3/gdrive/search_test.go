package gdrive

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"bilabl/docman/internal/service/gdrive"
	mock_repositories "bilabl/docman/mocks/repositories"
	mock_gdrive "bilabl/docman/mocks/service/gdrive"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

// Test constants for search functionality
const (
	testSearchKeyword = "test document"
	testFileID1       = "file123"
	testFileID2       = "file456"
	testFileName1     = "test document.pdf"
	testFileName2     = "another test.docx"
	testWebURL1       = "https://drive.google.com/file/d/file123/view"
	testWebURL2       = "https://drive.google.com/file/d/file456/view"
)

func setupSearchTestHandler(t *testing.T) (*DocumentsHandler, *mock_gdrive.MockDocumentService) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	mockDocumentMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)

	handler := NewDocumentsHandler(mockDocumentService, mockDocumentMappingRepo)
	return handler, mockDocumentService
}

func createSearchTestEngine(handler *DocumentsHandler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	engine := gin.Default()

	// Add error handling middleware
	engine.Use(ginext.CreateErrorHandler(false))

	// Add actor to context middleware
	engine.Use(func(c *gin.Context) {
		c.Request.Header.Set("x-user-id", fmt.Sprintf("%d", testUserID))
		c.Request.Header.Set("x-tenant-id", fmt.Sprintf("%d", testTenantID))
		c.Request.Header.Set("X-User-Roles", "8") // Admin role
		c.Request.Header.Set("x-user-email", "<EMAIL>")
		c.Request.Header.Set("x-user-name", "Test User")
		c.Request.Header.Set("x-tenant-tz-data", `{"name":"UTC"}`)
		c.Next()
	})

	engine.GET("/search/files", ginext.WrapHandler(func(r *ginext.Request) (*ginext.Response, error) {
		return handler.SearchFilesV3(r)
	}))

	return engine
}

func TestSearchFilesV3_Success(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations
	expectedFiles := []*gdrive.DocumentResponse{
		{
			DriveFileID: testFileID1,
			Name:        testFileName1,
			TenantID:    testTenantID,
			ContentType: "application/pdf",
			WebViewLink: testWebURL1,
			IsFile:      true,
			Size:        1024,
			CreatedAt:   time.Now().Add(-24 * time.Hour),
			UpdatedAt:   time.Now().Add(-1 * time.Hour),
		},
		{
			DriveFileID: testFileID2,
			Name:        testFileName2,
			TenantID:    testTenantID,
			ContentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			WebViewLink: testWebURL2,
			IsFile:      true,
			Size:        2048,
			CreatedAt:   time.Now().Add(-48 * time.Hour),
			UpdatedAt:   time.Now().Add(-2 * time.Hour),
		},
	}

	mockService.On("SearchDocuments", mock.Anything, mock.MatchedBy(func(req *gdrive.SearchDocumentsRequest) bool {
		return req.TenantID == testTenantID &&
			req.Query == testSearchKeyword &&
			req.ParentDriveID == "root" &&
			req.PageSize == 100
	})).Return(&gdrive.DocumentListResponse{
		Documents:     expectedFiles,
		NextPageToken: "",
		Total:         2,
	}, nil).Once()

	// Create test engine and execute request
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", fmt.Sprintf("/search/files?keyword=%s", url.QueryEscape(testSearchKeyword)), nil)
	engine.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data SearchFilesV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	searchResp := responseWrapper.Data

	// Verify response structure
	assert.Len(t, searchResp.Data, 2)
	assert.Equal(t, 100, searchResp.Meta.PageSize)
	assert.Equal(t, 2, searchResp.Meta.Total)
	assert.Empty(t, searchResp.Meta.NextPage)

	// Verify first file
	file1 := searchResp.Data[0]
	assert.Equal(t, testFileID1, file1.DocID)
	assert.Equal(t, testFileName1, file1.Name)
	assert.Equal(t, uint64(1024), file1.Size)
	assert.Equal(t, testWebURL1, file1.WebURL)
	assert.True(t, file1.IsFile)
	assert.NotEmpty(t, file1.DateCreated)
	assert.NotEmpty(t, file1.DateModified)
	assert.Equal(t, "Google Drive User", file1.LastModifiedBy)

	// Verify second file
	file2 := searchResp.Data[1]
	assert.Equal(t, testFileID2, file2.DocID)
	assert.Equal(t, testFileName2, file2.Name)
	assert.Equal(t, uint64(2048), file2.Size)
	assert.Equal(t, testWebURL2, file2.WebURL)
	assert.True(t, file2.IsFile)

	mockService.AssertExpectations(t)
}

func TestSearchFilesV3_WithPagination(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations with pagination
	expectedFiles := []*gdrive.DocumentResponse{
		{
			DriveFileID: testFileID1,
			Name:        testFileName1,
			TenantID:    testTenantID,
			IsFile:      true,
			Size:        1024,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mockService.On("SearchDocuments", mock.Anything, mock.MatchedBy(func(req *gdrive.SearchDocumentsRequest) bool {
		return req.TenantID == testTenantID &&
			req.Query == testSearchKeyword &&
			req.PageSize == 50 &&
			req.PageToken == "next_page_token"
	})).Return(&gdrive.DocumentListResponse{
		Documents:     expectedFiles,
		NextPageToken: "next_page_token_2",
		Total:         1,
	}, nil).Once()

	// Create test engine and execute request with pagination
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", fmt.Sprintf("/search/files?keyword=%s&page_size=50&next_page=next_page_token", url.QueryEscape(testSearchKeyword)), nil)
	engine.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data SearchFilesV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	searchResp := responseWrapper.Data

	// Verify pagination
	assert.Len(t, searchResp.Data, 1)
	assert.Equal(t, 50, searchResp.Meta.PageSize)
	assert.Equal(t, "next_page_token_2", searchResp.Meta.NextPage)

	mockService.AssertExpectations(t)
}

func TestSearchFilesV3_EmptyResults(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations for empty results
	mockService.On("SearchDocuments", mock.Anything, mock.MatchedBy(func(req *gdrive.SearchDocumentsRequest) bool {
		return req.TenantID == testTenantID &&
			req.Query == "nonexistent"
	})).Return(&gdrive.DocumentListResponse{
		Documents:     []*gdrive.DocumentResponse{},
		NextPageToken: "",
		Total:         0,
	}, nil).Once()

	// Create test engine and execute request
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/search/files?keyword=nonexistent", nil)
	engine.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data SearchFilesV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	searchResp := responseWrapper.Data

	// Verify empty results
	assert.Len(t, searchResp.Data, 0)
	assert.Equal(t, 100, searchResp.Meta.PageSize)
	assert.Equal(t, 0, searchResp.Meta.Total)
	assert.Empty(t, searchResp.Meta.NextPage)

	mockService.AssertExpectations(t)
}

func TestSearchFilesV3_MissingKeyword(t *testing.T) {
	handler, _ := setupSearchTestHandler(t)

	// Create test engine and execute request without keyword
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/search/files", nil) // No keyword parameter
	engine.ServeHTTP(w, req)

	// Assertions - should return validation error
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestSearchFilesV3_ServiceError(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations for service error
	mockService.On("SearchDocuments", mock.Anything, mock.Anything).Return(
		nil, &gdrive.DocumentServiceError{
			Code:    gdrive.ErrCodeDriveAPIError,
			Message: "Google Drive API error",
			Details: "Rate limit exceeded",
		}).Once()

	// Create test engine and execute request
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", fmt.Sprintf("/search/files?keyword=%s", url.QueryEscape(testSearchKeyword)), nil)
	engine.ServeHTTP(w, req)

	// Assertions - should return service error
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	mockService.AssertExpectations(t)
}

func TestSearchFilesV3_MaxPageSize(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations with capped page size
	mockService.On("SearchDocuments", mock.Anything, mock.MatchedBy(func(req *gdrive.SearchDocumentsRequest) bool {
		return req.PageSize == 1000 // Should be capped at maximum
	})).Return(&gdrive.DocumentListResponse{
		Documents:     []*gdrive.DocumentResponse{},
		NextPageToken: "",
		Total:         0,
	}, nil).Once()

	// Create test engine and execute request with oversized page size
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", fmt.Sprintf("/search/files?keyword=%s&page_size=2000", url.QueryEscape(testSearchKeyword)), nil)
	engine.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data SearchFilesV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	// Verify page size was capped
	assert.Equal(t, 1000, responseWrapper.Data.Meta.PageSize)

	mockService.AssertExpectations(t)
}

func TestSearchFilesV3_UsesSharedDriveID(t *testing.T) {
	handler, mockService := setupSearchTestHandler(t)

	// Setup mock expectations - verify that search uses shared drive ID instead of "root"
	mockService.On("SearchDocuments", mock.Anything, mock.MatchedBy(func(req *gdrive.SearchDocumentsRequest) bool {
		// Verify that ParentDriveID is "root" in the request (this will be resolved to shared drive ID in service)
		return req.TenantID == testTenantID &&
			req.Query == testSearchKeyword &&
			req.ParentDriveID == "root" && // Handler passes "root", service should resolve to shared drive ID
			req.PageSize == 100
	})).Return(&gdrive.DocumentListResponse{
		Documents:     []*gdrive.DocumentResponse{},
		NextPageToken: "",
		Total:         0,
	}, nil).Once()

	// Create test engine and execute request
	engine := createSearchTestEngine(handler)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", fmt.Sprintf("/search/files?keyword=%s", url.QueryEscape(testSearchKeyword)), nil)
	engine.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data SearchFilesV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)

	// Verify search was called with correct parameters
	mockService.AssertExpectations(t)
}
