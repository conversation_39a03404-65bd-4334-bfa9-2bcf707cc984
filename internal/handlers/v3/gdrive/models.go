package gdrive

import (
	"time"

	gdriveSvc "bilabl/docman/pkg/gdrive"
)

// Request Models (SharePoint Compatible)

// ListDocumentV3Request contains parameters for listing documents
type ListDocumentV3Request struct {
	ID         string `json:"id" query:"id" form:"id"`                            // Direct folder ID
	ObjectID   string `json:"object_id" query:"object_id" form:"object_id"`       // Object ID for mapping lookup
	ObjectType string `json:"object_type" query:"object_type" form:"object_type"` // Object type for mapping lookup
	Search     string `json:"search" query:"search" form:"search"`                // Search query
	PageSize   int    `json:"page_size" query:"page_size" form:"page_size"`       // Number of items per page
	PageToken  string `json:"page_token" query:"page_token" form:"page_token"`    // Pagination token
}

// CreateDocumentV3Request contains parameters for creating a document/folder
type CreateDocumentV3Request struct {
	ParentID   string `json:"parent_id"`               // Parent folder ID (optional if parent_path provided)
	ParentPath string `json:"parent_path"`             // Hierarchical folder path (e.g., "parent/child1/child2")
	Name       string `json:"name" binding:"required"` // Document/folder name
	ObjectID   uint64 `json:"object_id"`               // Optional object ID for mapping
	ObjectType string `json:"object_type"`             // Optional object type for mapping
}

// UpdateDocumentV3Request contains parameters for updating a document
type UpdateDocumentV3Request struct {
	Name       string `json:"name" binding:"required"` // New document name
	ObjectID   uint64 `json:"object_id"`               // Optional object ID for mapping
	ObjectType string `json:"object_type"`             // Optional object type for mapping
}

// UploadV3Request contains parameters for file upload URL generation
type UploadV3Request struct {
	ID         string `json:"id"`                           // Parent folder ID (optional if parent_path provided)
	ParentPath string `json:"parent_path"`                  // Hierarchical folder path (e.g., "parent/child1/child2")
	FileName   string `json:"file_name" binding:"required"` // File name for upload
	ObjectID   uint64 `json:"object_id"`                    // Optional object ID for mapping
	ObjectType string `json:"object_type"`                  // Optional object type for mapping
}

// SearchFilesV3Request contains parameters for file search operations
type SearchFilesV3Request struct {
	Keyword  string `json:"keyword" query:"keyword" form:"keyword" binding:"required"` // Search keyword
	NextPage string `json:"next_page" query:"next_page" form:"next_page"`              // Pagination token
	PageSize int    `json:"page_size" query:"page_size" form:"page_size"`              // Number of results per page
}

// Response Models (SharePoint Compatible)

// DocumentV3Response represents a document/folder in V3 API format
type DocumentV3Response struct {
	ID             string    `json:"id"`               // Drive file ID
	Name           string    `json:"name"`             // Document name
	ParentID       uint64    `json:"parent_id"`        // Parent ID (for compatibility)
	DocType        int       `json:"doc_type"`         // Document type (1=folder, 2=file)
	ObjectType     int       `json:"object_type"`      // Object type (for compatibility)
	ObjectID       uint64    `json:"object_id"`        // Object ID (for compatibility)
	IsFile         bool      `json:"is_file"`          // True if file, false if folder
	IsInternal     bool      `json:"is_internal"`      // Internal flag (always false for Google Drive)
	HasChild       bool      `json:"has_child"`        // True if folder has children
	Size           int64     `json:"size"`             // File size in bytes
	Type           string    `json:"type"`             // File extension/type
	DateCreated    time.Time `json:"date_created"`     // Creation date
	DateModified   time.Time `json:"date_modified"`    // Last modified date
	LastModifiedBy string    `json:"last_modified_by"` // Last modifier name
	WebURL         string    `json:"web_url"`          // Web view URL
	Status         int       `json:"status"`           // Status (always 1 for active)
}

// ListDocumentV3Response represents the response for list operations
type ListDocumentV3Response struct {
	Cwd  DocumentV3Response   `json:"cwd"`  // Current working directory info
	Data []DocumentV3Response `json:"data"` // List of documents
	Meta PaginationMeta       `json:"meta"` // Pagination metadata
}

// CreateDocumentV3Response represents the response for create operations
type CreateDocumentV3Response struct {
	ID string `json:"id"` // Created document ID
}

// UpdateDocumentV3Response represents the response for update operations
type UpdateDocumentV3Response struct {
	ID   string `json:"id"`   // Updated document ID
	Name string `json:"name"` // Updated document name
}

// UploadV3Response represents the response for upload session creation
type UploadV3Response struct {
	UploadUrl    string `json:"upload_url"`    // Upload URL (session token for compatibility)
	SessionToken string `json:"session_token"` // Upload session token
	ExpiresAt    string `json:"expires_at"`    // Session expiration time (RFC3339 format)
}

// UploadContentV3Response represents the response for file content upload
type UploadContentV3Response struct {
	Status       string `json:"status"`        // Upload status (completed, failed)
	DriveFileID  string `json:"drive_file_id"` // Google Drive file ID
	FileName     string `json:"file_name"`     // File name
	FileSize     int64  `json:"file_size"`     // File size in bytes
	ContentType  string `json:"content_type"`  // MIME type
	SessionToken string `json:"session_token"` // Session token used
}

// PaginationMeta contains pagination information
type PaginationMeta struct {
	NextPage string `json:"next_page"` // Next page token
	PageSize int    `json:"page_size"` // Current page size
	Total    int    `json:"total"`     // Total number of items
	HasNext  bool   `json:"has_next"`  // Whether there are more pages
}

// Error response structure (SharePoint compatible)
type ErrorV3Response struct {
	ErrorCode    string `json:"error_code"`        // Error code
	ErrorMessage string `json:"error_message"`     // Error message
	Details      string `json:"details,omitempty"` // Optional error details
}

// FileItem represents a file in search results (SharePoint compatible)
type FileItem struct {
	DocID          string `json:"docId"`          // Google Drive file ID
	Name           string `json:"name"`           // File name
	Size           uint64 `json:"size"`           // File size in bytes
	WebURL         string `json:"webUrl"`         // Web view URL
	IsFile         bool   `json:"isFile"`         // Always true for search results
	DateModified   string `json:"dateModified"`   // Last modified date (ISO format)
	DateCreated    string `json:"dateCreated"`    // Creation date (ISO format)
	LastModifiedBy string `json:"lastModifiedBy"` // Last modifier name
}

// FileListMeta represents pagination metadata for search results
type FileListMeta struct {
	NextPage string `json:"next_page"` // Next page token
	PageSize int    `json:"page_size"` // Current page size
	Total    int    `json:"total"`     // Total results count
}

// SearchFilesV3Response represents the response for file search operations
type SearchFilesV3Response struct {
	Data []FileItem   `json:"data"` // Search results
	Meta FileListMeta `json:"meta"` // Pagination metadata
}

// Setup Request Models (V3 Enhanced)

// TestSetupV3Request contains parameters for testing Google Drive setup
type TestSetupV3Request struct {
	URLOrID string `json:"url_or_id" binding:"required"` // Google Drive URL or ID
}

// PathConfigV3 represents configurable folder path templates for API
type PathConfigV3 struct {
	ClientFolderPath   string `json:"client_folder_path,omitempty"`   // e.g., "{short_name|name} - {code}"
	MatterFolderPath   string `json:"matter_folder_path,omitempty"`   // e.g., "{client_folder}/matters/{name} - {code}"
	CaseFormat         string `json:"case_format,omitempty"`          // original, lower, upper, title
	MaxLength          int    `json:"max_length,omitempty"`           // default: 255
	InvalidCharReplace string `json:"invalid_char_replace,omitempty"` // default: "_"
}

// CompleteSetupV3Request contains parameters for completing Google Drive setup
type CompleteSetupV3Request struct {
	URLOrID    string        `json:"url_or_id" binding:"required"` // Google Drive URL or ID
	Enabled    bool          `json:"enabled"`                      // Enable/disable integration
	PathConfig *PathConfigV3 `json:"path_config,omitempty"`        // Optional path configuration
}

// Setup Response Models (V3 Enhanced)

// TestSetupV3Response represents the response for test setup operations
type TestSetupV3Response struct {
	Status    string               `json:"status"`     // Operation status
	DriveInfo *gdriveSvc.DriveInfo `json:"drive_info"` // Google Drive information
	Message   string               `json:"message"`    // Success message
}

// CompleteSetupV3Response represents the response for complete setup operations
type CompleteSetupV3Response struct {
	Status     string        `json:"status"`                // Operation status
	Message    string        `json:"message"`               // Success/error message
	Enabled    bool          `json:"enabled"`               // Current enabled state
	State      string        `json:"state"`                 // Human-readable state (enabled/disabled)
	PathConfig *PathConfigV3 `json:"path_config,omitempty"` // Applied path configuration
}
