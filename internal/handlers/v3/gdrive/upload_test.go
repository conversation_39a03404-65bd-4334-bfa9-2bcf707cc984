package gdrive

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"bilabl/docman/internal/service/gdrive"
	mock_gdrive "bilabl/docman/mocks/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/actor"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

func TestUploadV3_WithParentID(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test upload session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "test_token_123",
		UploadURL:       "test_token_123",
		ExpiresAt:       time.Time{}, // Empty time instead of nil
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	// Setup request with parent_id only
	reqBody := UploadV3Request{
		ID:       "folder_123",
		FileName: "test.pdf",
	}

	// Mock expectations
	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "folder_123" && req.FileName == "test.pdf" && req.TenantID == uint64(0)
	})).Return(sessionResponse, nil).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context
	act := actor.Actor{TenantID: 0} // Set TenantID to 0 to match the value in the test
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Execute request
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_WithParentPathOnly(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test upload session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "test_token_456",
		UploadURL:       "test_token_456",
		ExpiresAt:       time.Time{}, // Empty time instead of nil
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	// Setup request with parent_path only
	reqBody := UploadV3Request{
		ParentPath: "department/team1",
		FileName:   "test.pdf",
	}

	// Mock hierarchical path resolution - should use "root" as default root
	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "team1_folder_id",
		CreatedFolders: []string{"department_id", "team1_folder_id"},
		ResolvedPath:   "department/team1",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "department/team1" && req.RootDriveID == "root" && req.TenantID == uint64(0)
	})).Return(pathResp, nil).Once()

	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "team1_folder_id" && req.FileName == "test.pdf" && req.TenantID == uint64(0)
	})).Return(sessionResponse, nil).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context
	act := actor.Actor{TenantID: 0} // Set TenantID to 0 to match the value in the test
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Execute request
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_WithParentIDAndPath(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test upload session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "test_token_789",
		UploadURL:       "test_token_789",
		ExpiresAt:       time.Time{}, // Empty time instead of nil
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	// Setup request with both parent_id and parent_path
	reqBody := UploadV3Request{
		ID:         "base_folder_id",
		ParentPath: "projects/2023",
		FileName:   "report.pdf",
	}

	// Mock hierarchical path resolution - should use parent_id as root
	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "2023_folder_id",
		CreatedFolders: []string{"projects_id", "2023_folder_id"},
		ResolvedPath:   "projects/2023",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "projects/2023" && req.RootDriveID == "base_folder_id" && req.TenantID == uint64(0)
	})).Return(pathResp, nil).Once()

	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "2023_folder_id" && req.FileName == "report.pdf" && req.TenantID == uint64(0)
	})).Return(sessionResponse, nil).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context
	act := actor.Actor{TenantID: 0} // Set TenantID to 0 to match the value in the test
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Execute request
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_WithObjectMappingAndPath(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test upload session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "test_token_object_mapping",
		UploadURL:       "test_token_object_mapping",
		ExpiresAt:       time.Time{}, // Empty time instead of nil
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	// Setup request with object mapping and parent_path
	reqBody := UploadV3Request{
		ObjectType: "client",
		ObjectID:   123,
		ParentPath: "documents/contracts",
		FileName:   "agreement.pdf",
	}

	// Mock object mapping resolution
	objectMappingResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID: "client_folder_id",
	}

	// Mock hierarchical path resolution
	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "contracts_folder_id",
		CreatedFolders: []string{"documents_id", "contracts_folder_id"},
		ResolvedPath:   "documents/contracts",
	}

	// First, resolve the object mapping to get the base folder ID
	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ObjectType == "client" && req.ObjectID == uint64(123) && req.TenantID == uint64(0)
	})).Return(objectMappingResp, nil).Once()

	// Then, resolve the hierarchical path starting from the base folder
	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "documents/contracts" && req.RootDriveID == "client_folder_id" && req.TenantID == uint64(0)
	})).Return(pathResp, nil).Once()

	// Finally, create the upload session
	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "contracts_folder_id" && req.FileName == "agreement.pdf" && req.TenantID == uint64(0)
	})).Return(sessionResponse, nil).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context
	act := actor.Actor{TenantID: 0} // Set TenantID to 0 to match the value in the test
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Execute request
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadContentV3_NoAuthenticationRequired(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "upload_test_token_123",
		UploadURL:       "upload_test_token_123",
		FileName:        "test.pdf",
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	// Create test upload content response
	uploadResponse := &gdrive.UploadContentResponse{
		Status:      "completed",
		DriveFileID: "file_123",
	}

	// Mock expectations - no tenant validation needed
	mockDocumentService.On("GetUploadSession", mock.Anything, "upload_test_token_123").
		Return(sessionResponse, nil).Once()

	mockDocumentService.On("UploadFileContent", mock.Anything, "upload_test_token_123", mock.Anything, "application/pdf", int64(17)).
		Return(uploadResponse, nil).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	engine := gin.New()

	// Register route WITHOUT authentication middleware (matching actual route)
	engine.PUT("/upload/:session_token/content", ginext.WrapHandler(handler.UploadContentV3))

	// Create request WITHOUT authentication headers (simulating public access)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/upload/upload_test_token_123/content", strings.NewReader("test file content"))
	req.Header.Set("Content-Type", "application/pdf")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body - ginext wraps response in data field
	var responseWrapper struct {
		Data UploadContentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)
	assert.Equal(t, "completed", responseWrapper.Data.Status)
	assert.Equal(t, "file_123", responseWrapper.Data.DriveFileID)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadContentV3_EmptySessionToken(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	engine := gin.New()

	// Register route
	engine.PUT("/upload/:session_token/content", ginext.WrapHandler(handler.UploadContentV3))

	// Create request with empty session token
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/upload/empty/content", strings.NewReader("test file content"))

	// Mock expectation for empty token
	mockDocumentService.On("GetUploadSession", mock.Anything, "empty").
		Return(nil, &gdrive.DocumentServiceError{
			Code:    gdrive.ErrCodeDocumentNotFound,
			Message: "Upload session not found",
		}).Once()

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get 404 because session not found
	assert.Equal(t, http.StatusNotFound, w.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadContentV3_SessionNotFound(t *testing.T) {
	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Mock expectations - session not found
	mockDocumentService.On("GetUploadSession", mock.Anything, "non_existent_token").
		Return(nil, &gdrive.DocumentServiceError{
			Code:    gdrive.ErrCodeDocumentNotFound,
			Message: "Upload session not found",
		}).Once()

	// Setup HTTP test
	gin.SetMode(gin.TestMode)
	engine := gin.New()

	// Register route
	engine.PUT("/upload/:session_token/content", ginext.WrapHandler(handler.UploadContentV3))

	// Create request with non-existent session token
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/upload/non_existent_token/content", strings.NewReader("test file content"))
	req.Header.Set("Content-Type", "application/pdf")

	// Execute request
	engine.ServeHTTP(w, req)

	// Verify response - should get 404 for session not found
	assert.Equal(t, http.StatusNotFound, w.Code)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadContentV3_PublicRoute_Integration(t *testing.T) {
	// This test verifies that the upload content endpoint is truly public
	// by testing the actual route configuration without any auth middleware

	// Setup mock service
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	// Create test session response
	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "public_test_token_456",
		UploadURL:       "public_test_token_456",
		FileName:        "public_test.pdf",
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/public_test",
	}

	// Create test upload content response
	uploadResponse := &gdrive.UploadContentResponse{
		Status:      "completed",
		DriveFileID: "public_file_456",
	}

	// Mock expectations
	mockDocumentService.On("GetUploadSession", mock.Anything, "public_test_token_456").
		Return(sessionResponse, nil).Once()

	mockDocumentService.On("UploadFileContent", mock.Anything, "public_test_token_456", mock.Anything, "application/pdf", int64(21)).
		Return(uploadResponse, nil).Once()

	// Setup HTTP test with route structure matching actual handler registration
	gin.SetMode(gin.TestMode)
	engine := gin.New()

	// Simulate the actual route structure from pkg/handlers/handler.go
	v3Group := engine.Group("/v3")
	gdriveGroup := v3Group.Group("/gdrive")

	// This is the PUBLIC route (no auth middleware)
	gdriveGroup.PUT("/upload/:session_token/content", ginext.WrapHandler(handler.UploadContentV3))

	// Create request WITHOUT any authentication headers
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/v3/gdrive/upload/public_test_token_456/content", strings.NewReader("public test file data"))
	req.Header.Set("Content-Type", "application/pdf")

	// Execute request - this should work without authentication
	engine.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body
	var responseWrapper struct {
		Data UploadContentV3Response `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)
	assert.Equal(t, "completed", responseWrapper.Data.Status)
	assert.Equal(t, "public_file_456", responseWrapper.Data.DriveFileID)

	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_OnlyParentID(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "token_parentid",
		UploadURL:       "token_parentid",
		ExpiresAt:       time.Time{},
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	reqBody := UploadV3Request{
		ID:       "folder_abc",
		FileName: "file.pdf",
	}

	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "folder_abc" && req.FileName == "file.pdf"
	})).Return(sessionResponse, nil).Once()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)
	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_OnlyObjectMapping(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "token_objmap",
		UploadURL:       "token_objmap",
		ExpiresAt:       time.Time{},
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	reqBody := UploadV3Request{
		ObjectType: "client",
		ObjectID:   42,
		FileName:   "file.pdf",
	}

	mappingResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID: "mapped_folder_id",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ObjectType == "client" && req.ObjectID == 42 && req.ParentPath == ""
	})).Return(mappingResp, nil).Once()

	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "mapped_folder_id" && req.FileName == "file.pdf"
	})).Return(sessionResponse, nil).Once()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)
	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_ParentIDAndParentPath(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "token_id_path",
		UploadURL:       "token_id_path",
		ExpiresAt:       time.Time{},
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	reqBody := UploadV3Request{
		ID:         "base_id",
		ParentPath: "a/b",
		FileName:   "file.pdf",
	}

	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "b_folder_id",
		CreatedFolders: []string{"a_id", "b_folder_id"},
		ResolvedPath:   "a/b",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "a/b" && req.RootDriveID == "base_id"
	})).Return(pathResp, nil).Once()

	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "b_folder_id" && req.FileName == "file.pdf"
	})).Return(sessionResponse, nil).Once()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)
	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_ObjectMappingAndParentPath(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "token_objmap_path",
		UploadURL:       "token_objmap_path",
		ExpiresAt:       time.Time{},
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	reqBody := UploadV3Request{
		ObjectType: "client",
		ObjectID:   99,
		ParentPath: "x/y",
		FileName:   "file.pdf",
	}

	mappingResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID: "mapped_folder_id2",
	}
	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "y_folder_id",
		CreatedFolders: []string{"x_id", "y_folder_id"},
		ResolvedPath:   "x/y",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ObjectType == "client" && req.ObjectID == 99 && req.ParentPath == ""
	})).Return(mappingResp, nil).Once()
	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "x/y" && req.RootDriveID == "mapped_folder_id2"
	})).Return(pathResp, nil).Once()
	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "y_folder_id" && req.FileName == "file.pdf"
	})).Return(sessionResponse, nil).Once()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)
	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_OnlyParentPath(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	sessionResponse := &gdrive.UploadSessionResponse{
		SessionToken:    "token_path_only",
		UploadURL:       "token_path_only",
		ExpiresAt:       time.Time{},
		Status:          "pending",
		GoogleUploadURL: "https://upload.google.com/test",
	}

	reqBody := UploadV3Request{
		ParentPath: "folderA/folderB",
		FileName:   "file.pdf",
	}

	pathResp := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "folderB_id",
		CreatedFolders: []string{"folderA_id", "folderB_id"},
		ResolvedPath:   "folderA/folderB",
	}

	mockDocumentService.On("ResolveHierarchicalPath", mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
		return req.ParentPath == "folderA/folderB" && req.RootDriveID == "root"
	})).Return(pathResp, nil).Once()
	mockDocumentService.On("CreateUploadSession", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateUploadSessionRequest) bool {
		return req.ParentDriveID == "folderB_id" && req.FileName == "file.pdf"
	})).Return(sessionResponse, nil).Once()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.Code)
	mockDocumentService.AssertExpectations(t)
}

func TestUploadV3_Invalid_NoParentSpec(t *testing.T) {
	mockDocumentService := mock_gdrive.NewMockDocumentService(t)
	handler := NewUploadHandler(mockDocumentService, "http://localhost:8088")

	reqBody := UploadV3Request{
		FileName: "file.pdf",
	}

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	act := actor.Actor{TenantID: 0}
	c.Set("actor", act)
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/upload", strings.NewReader(string(jsonBody)))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	r := ginext.NewRequest(c)
	resp, err := handler.UploadV3(r)
	assert.NotNil(t, resp)
	assert.NoError(t, err)
	assert.Equal(t, 400, resp.Code)
	if resp.Body != nil {
		errResp, ok := resp.Body.(*ErrorResponse)
		assert.True(t, ok, "Expected ErrorResponse but got %T", resp.Body)
		assert.Contains(t, errResp.Error.Msg, "must provide id, parent_path, or both object_type and object_id")
	}
}
