package gdrive

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateParentPath(t *testing.T) {
	tests := []struct {
		name        string
		parentPath  string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid simple path",
			parentPath:  "parent",
			expectError: false,
		},
		{
			name:        "Valid nested path",
			parentPath:  "parent/child1/child2",
			expectError: false,
		},
		{
			name:        "Valid path with leading slash",
			parentPath:  "/parent/child1",
			expectError: false,
		},
		{
			name:        "Valid path with trailing slash",
			parentPath:  "parent/child1/",
			expectError: false,
		},
		{
			name:        "Valid path with both leading and trailing slashes",
			parentPath:  "/parent/child1/",
			expectError: false,
		},
		{
			name:        "Empty path",
			parentPath:  "",
			expectError: true,
			errorMsg:    "parent_path cannot be empty",
		},
		{
			name:        "Only slashes",
			parentPath:  "///",
			expectError: true,
			errorMsg:    "parent_path cannot contain only slashes",
		},
		{
			name:        "Empty segment",
			parentPath:  "parent//child",
			expectError: true,
			errorMsg:    "parent_path segment 2 is empty",
		},
		{
			name:        "Segment with only spaces",
			parentPath:  "parent/   /child",
			expectError: true,
			errorMsg:    "parent_path segment 2 is empty or contains only spaces",
		},
		{
			name:        "Invalid character in segment",
			parentPath:  "parent/child*/grandchild",
			expectError: true,
			errorMsg:    "parent_path segment 2 (child*) is invalid",
		},
		{
			name:        "Reserved name",
			parentPath:  "parent/CON/child",
			expectError: true,
			errorMsg:    "parent_path segment 2 (CON) is invalid",
		},
		{
			name:        "Segment too long",
			parentPath:  "parent/" + string(make([]byte, 256)) + "/child",
			expectError: true,
			errorMsg:    "parent_path segment 2",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := validateParentPath(tc.parentPath)
			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateCreateDocumentRequest_WithParentPath(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name        string
		req         *CreateDocumentV3Request
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid with parent_path only",
			req: &CreateDocumentV3Request{
				Name:       "Test Document",
				ParentPath: "parent/child1",
				ObjectType: "client",
				ObjectID:   123,
			},
			expectError: false,
		},
		{
			name: "Valid with parent_id only",
			req: &CreateDocumentV3Request{
				Name:     "Test Document",
				ParentID: "folder123",
			},
			expectError: false,
		},
		{
			name: "Valid with object mapping only",
			req: &CreateDocumentV3Request{
				Name:       "Test Document",
				ObjectType: "client",
				ObjectID:   123,
			},
			expectError: false,
		},
		{
			name: "Valid with parent_path and parent_id",
			req: &CreateDocumentV3Request{
				Name:       "Test Document",
				ParentPath: "parent/child1",
				ParentID:   "folder123",
			},
			expectError: false,
		},
		{
			name: "Invalid - no parent specification",
			req: &CreateDocumentV3Request{
				Name: "Test Document",
			},
			expectError: true,
			errorMsg:    "must provide parent_id, parent_path, or both object_type and object_id",
		},
		{
			name: "Invalid - empty name",
			req: &CreateDocumentV3Request{
				Name:       "",
				ParentPath: "parent/child1",
			},
			expectError: true,
			errorMsg:    "document name is required",
		},
		{
			name: "Invalid - bad parent_path",
			req: &CreateDocumentV3Request{
				Name:       "Test Document",
				ParentPath: "parent//child",
			},
			expectError: true,
			errorMsg:    "invalid parent_path",
		},
		{
			name: "Invalid - object type without object id",
			req: &CreateDocumentV3Request{
				Name:       "Test Document",
				ObjectType: "client",
			},
			expectError: true,
			errorMsg:    "must provide parent_id, parent_path, or both object_type and object_id",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := ValidateCreateDocumentRequest(ctx, tc.req)
			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUploadRequest_WithParentPath(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name        string
		req         *UploadV3Request
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid with parent_path only",
			req: &UploadV3Request{
				FileName:   "test.pdf",
				ParentPath: "parent/child1",
				ObjectType: "client",
				ObjectID:   123,
			},
			expectError: false,
		},
		{
			name: "Valid with id only",
			req: &UploadV3Request{
				FileName: "test.pdf",
				ID:       "folder123",
			},
			expectError: false,
		},
		{
			name: "Valid with object mapping only",
			req: &UploadV3Request{
				FileName:   "test.pdf",
				ObjectType: "client",
				ObjectID:   123,
			},
			expectError: false,
		},
		{
			name: "Invalid - no parent specification",
			req: &UploadV3Request{
				FileName: "test.pdf",
			},
			expectError: true,
			errorMsg:    "must provide id, parent_path, or both object_type and object_id",
		},
		{
			name: "Invalid - empty filename",
			req: &UploadV3Request{
				FileName:   "",
				ParentPath: "parent/child1",
			},
			expectError: true,
			errorMsg:    "file name is required",
		},
		{
			name: "Invalid - bad parent_path",
			req: &UploadV3Request{
				FileName:   "test.pdf",
				ParentPath: "parent//child",
			},
			expectError: true,
			errorMsg:    "invalid parent_path",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := ValidateUploadRequest(ctx, tc.req)
			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
