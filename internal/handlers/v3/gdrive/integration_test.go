package gdrive

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/mocks/repositories"
	mockService "bilabl/docman/mocks/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/actor"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

func setupTestHandler(t *testing.T) (*DocumentsHandler, *mockService.MockDocumentService, *repositories.MockDocumentMappingRepository) {
	// Setup mocks
	mockDocumentService := mockService.NewMockDocumentService(t)
	mockMappingRepo := repositories.NewMockDocumentMappingRepository(t)

	// Create handler
	handler := NewDocumentsHandler(mockDocumentService, mockMappingRepo)

	return handler, mockDocumentService, mockMappingRepo
}

func TestCreateV3_WithHierarchicalPath_Success(t *testing.T) {
	handler, mockDocumentService, _ := setupTestHandler(t)

	// Setup request
	reqBody := CreateDocumentV3Request{
		Name:       "New Folder",
		ParentPath: "parent/child1",
		ParentID:   "root123", // Root for path resolution
	}

	// Mock hierarchical path resolution
	// Note: ParentID is now used as RootDriveID for the hierarchical path
	pathReq := &gdrive.ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "parent/child1",
		RootDriveID: "root123", // ParentID is used as the base folder
		ObjectType:  "",
		ObjectID:    0,
	}
	pathResponse := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "child1_123",
		CreatedFolders: []string{"child1_123"},
		ResolvedPath:   "parent/child1",
	}
	mockDocumentService.EXPECT().
		ResolveHierarchicalPath(mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
			return req.ParentPath == pathReq.ParentPath && req.RootDriveID == pathReq.RootDriveID
		})).
		Return(pathResponse, nil).
		Once()

	// Mock document creation
	createReq := &gdrive.CreateDocumentRequest{
		TenantID:   1,
		Name:       "New Folder",
		DocType:    1,            // DocTypeDir
		ParentID:   "child1_123", // Final resolved parent ID
		ObjectID:   0,
		ObjectType: "",
	}
	createResponse := &gdrive.DocumentResponse{
		DriveFileID: "new_folder_123",
		Name:        "New Folder",
	}
	mockDocumentService.EXPECT().
		CreateDocument(mock.Anything, mock.MatchedBy(func(req *gdrive.CreateDocumentRequest) bool {
			return req.Name == createReq.Name && req.ParentID == createReq.ParentID
		})).
		Return(createResponse, nil).
		Once()

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context with tenant ID
	act := &actor.Actor{TenantID: 1}
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{GinCtx: c}

	// Execute
	response, err := handler.CreateV3(ginextReq)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, response)

	mockDocumentService.AssertExpectations(t)
}

func TestCreateV3_WithHierarchicalPath_ValidationError(t *testing.T) {
	handler, _, _ := setupTestHandler(t)

	// Setup request with invalid parent path
	reqBody := CreateDocumentV3Request{
		Name:       "New Folder",
		ParentPath: "parent//child1", // Invalid path with double slash
	}

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context
	act := &actor.Actor{TenantID: 1}
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{GinCtx: c}

	// Execute
	response, err := handler.CreateV3(ginextReq)

	// Assert - validation errors are handled by returning a response with status 400
	// and no error (the error is wrapped in the response)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, http.StatusBadRequest, response.Code)

	// Check that the response body contains the validation error message
	errorResp, ok := response.Body.(*ErrorResponse)
	assert.True(t, ok, "Expected ErrorResponse but got %T", response.Body)
	assert.Contains(t, errorResp.Error.Msg, "invalid parent_path")
}

func TestCreateV3_WithOnlyParentPath_Success(t *testing.T) {
	handler, mockDocumentService, _ := setupTestHandler(t)

	// Setup request with only parent_path (no parent_id)
	reqBody := CreateDocumentV3Request{
		Name:       "New Folder",
		ParentPath: "parent/child1",
		// No ParentID provided
	}

	// Mock hierarchical path resolution - should use "root" as default RootDriveID
	pathReq := &gdrive.ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "parent/child1",
		RootDriveID: "root", // Default root when no ParentID is provided
		ObjectType:  "",
		ObjectID:    0,
	}
	pathResponse := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "child1_456",
		CreatedFolders: []string{"child1_456"},
		ResolvedPath:   "parent/child1",
	}
	mockDocumentService.EXPECT().
		ResolveHierarchicalPath(mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
			return req.ParentPath == pathReq.ParentPath && req.RootDriveID == pathReq.RootDriveID
		})).
		Return(pathResponse, nil).
		Once()

	// Mock document creation
	createReq := &gdrive.CreateDocumentRequest{
		TenantID:   1,
		Name:       "New Folder",
		DocType:    1,            // DocTypeDir
		ParentID:   "child1_456", // Final resolved parent ID
		ObjectID:   0,
		ObjectType: "",
	}
	createResponse := &gdrive.DocumentResponse{
		DriveFileID: "new_folder_456",
		Name:        "New Folder",
	}
	mockDocumentService.EXPECT().
		CreateDocument(mock.Anything, mock.MatchedBy(func(req *gdrive.CreateDocumentRequest) bool {
			return req.Name == createReq.Name && req.ParentID == createReq.ParentID
		})).
		Return(createResponse, nil).
		Once()

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context with tenant ID
	act := &actor.Actor{TenantID: 1}
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{GinCtx: c}

	// Execute
	response, err := handler.CreateV3(ginextReq)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, response)

	mockDocumentService.AssertExpectations(t)
}

func TestCreateV3_WithObjectMapping_AndPath_Success(t *testing.T) {
	handler, mockDocumentService, mockMappingRepo := setupTestHandler(t)

	// Setup request with object mapping and parent_path
	reqBody := CreateDocumentV3Request{
		Name:       "New Folder",
		ParentPath: "department/team1",
		ObjectType: "client",
		ObjectID:   123,
		// No ParentID provided
	}

	// Mock document mapping repository call
	mockMapping := &model.DocumentMapping{
		DriveID: "client_folder_789",
	}
	mockMappingRepo.EXPECT().
		FindOne(mock.Anything, mock.MatchedBy(func(query interface{}) bool {
			// Basic check - in real test we would verify filters
			return true
		})).
		Return(mockMapping, nil).
		Once()

	// Mock hierarchical path resolution using the mapped folder ID
	pathReq := &gdrive.ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "department/team1",
		RootDriveID: "client_folder_789", // ID from document mapping
		ObjectType:  "client",
		ObjectID:    123,
	}
	pathResponse := &gdrive.ResolveHierarchicalPathResponse{
		FinalParentID:  "team1_folder_123",
		CreatedFolders: []string{"department_folder", "team1_folder_123"},
		ResolvedPath:   "department/team1",
	}
	mockDocumentService.EXPECT().
		ResolveHierarchicalPath(mock.Anything, mock.MatchedBy(func(req *gdrive.ResolveHierarchicalPathRequest) bool {
			return req.ParentPath == pathReq.ParentPath &&
				req.RootDriveID == pathReq.RootDriveID &&
				req.ObjectType == pathReq.ObjectType &&
				req.ObjectID == pathReq.ObjectID
		})).
		Return(pathResponse, nil).
		Once()

	// Mock document creation
	createReq := &gdrive.CreateDocumentRequest{
		TenantID:   1,
		Name:       "New Folder",
		DocType:    1,                  // DocTypeDir
		ParentID:   "team1_folder_123", // Final resolved parent ID
		ObjectID:   123,
		ObjectType: "client",
	}
	createResponse := &gdrive.DocumentResponse{
		DriveFileID: "new_folder_789",
		Name:        "New Folder",
	}
	mockDocumentService.EXPECT().
		CreateDocument(mock.Anything, mock.MatchedBy(func(req *gdrive.CreateDocumentRequest) bool {
			return req.Name == createReq.Name &&
				req.ParentID == createReq.ParentID &&
				req.ObjectID == createReq.ObjectID &&
				req.ObjectType == createReq.ObjectType
		})).
		Return(createResponse, nil).
		Once()

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Set actor context with tenant ID
	act := &actor.Actor{TenantID: 1}
	c.Set("actor", act)

	// Create request
	jsonBody, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", "/v3/gdrive/documents", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{GinCtx: c}

	// Execute
	response, err := handler.CreateV3(ginextReq)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, response)

	mockDocumentService.AssertExpectations(t)
	mockMappingRepo.AssertExpectations(t)
}
