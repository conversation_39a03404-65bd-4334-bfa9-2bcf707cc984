package gdrive

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"gitlab.com/goxp/cloud0/ginext"
)

// DocumentsHandler handles Google Drive document operations for V3 API
type DocumentsHandler struct {
	documentService     gdrive.DocumentService
	documentMappingRepo repositories.DocumentMappingRepository
}

// NewDocumentsHandler creates a new documents handler
func NewDocumentsHandler(
	documentService gdrive.DocumentService,
	documentMappingRepo repositories.DocumentMappingRepository,
) *DocumentsHandler {
	return &DocumentsHandler{
		documentService:     documentService,
		documentMappingRepo: documentMappingRepo,
	}
}

// CreateV3 creates a new document or folder
func (h *DocumentsHandler) CreateV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "CreateV3")

	req := &CreateDocumentV3Request{}
	r.MustBind(req)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Creating document name=%s tenant_id=%d", req.Name, act.TenantID)

	// Validate request
	if err := ValidateCreateDocumentRequest(r.Context(), req); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Resolve parent ID (with hierarchical path support)
	parentID, err := h.resolveParentID(r.Context(), req, &act)
	if err != nil {
		log.WithError(err).Error("Failed to resolve parent ID")
		return handleServiceError(r.Context(), err)
	}

	// Convert to service layer request
	serviceReq := &gdrive.CreateDocumentRequest{
		TenantID:   act.TenantID,
		Name:       req.Name,
		DocType:    model.DocTypeDir, // V3 API primarily creates folders
		ParentID:   parentID,
		ObjectID:   req.ObjectID,
		ObjectType: req.ObjectType,
	}

	// Call service layer
	result, err := h.documentService.CreateDocument(r.Context(), serviceReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to create document name=%s", req.Name)
		return handleServiceError(r.Context(), err)
	}

	// Convert to V3 response
	response := &CreateDocumentV3Response{
		ID: result.DriveFileID,
	}

	log.Infof("Successfully created document drive_file_id=%s name=%s", result.DriveFileID, req.Name)
	return ginext.NewResponseData(http.StatusCreated, response), nil
}

// ListV3 handles GET /v3/gdrive/documents
func (h *DocumentsHandler) ListV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "ListV3")

	query := &ListDocumentV3Request{}
	r.MustBind(query)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Listing documents tenant_id=%d parent_id=%s search=%s", act.TenantID, query.ID, query.Search)

	// Validate request
	if err := ValidateListDocumentRequest(r.Context(), query); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Resolve parent folder ID
	parentDriveID, err := h.resolveParentDriveID(r.Context(), act.TenantID, query)
	if err != nil {
		log.WithError(err).Error("Failed to resolve parent drive ID")
		return handleServiceError(r.Context(), err)
	}

	// Handle search vs list
	if query.Search != "" {
		log.Infof("Performing search query=%s parent_drive_id=%s", query.Search, parentDriveID)
		return h.handleSearch(r.Context(), act.TenantID, parentDriveID, query)
	}

	log.Infof("Performing list parent_drive_id=%s", parentDriveID)
	return h.handleList(r.Context(), act.TenantID, parentDriveID, query)
}

// UpdateV3 handles PATCH /v3/gdrive/documents/:document_id
func (h *DocumentsHandler) UpdateV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "UpdateV3")

	documentID := r.Param("document_id")
	req := &UpdateDocumentV3Request{}
	r.MustBind(req)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Updating document drive_file_id=%s new_name=%s tenant_id=%d", documentID, req.Name, act.TenantID)

	// Validate request
	if err := ValidateUpdateDocumentRequest(r.Context(), req); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Validate document ID
	if documentID == "" {
		log.Error("Document ID is required")
		return handleValidationError(r.Context(), fmt.Errorf("document ID is required"))
	}

	// Call service layer
	serviceReq := &gdrive.UpdateDocumentRequest{
		Name: req.Name,
	}

	result, err := h.documentService.UpdateDocument(r.Context(), documentID, serviceReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to update document drive_file_id=%s", documentID)
		return handleServiceError(r.Context(), err)
	}

	response := &UpdateDocumentV3Response{
		ID:   result.DriveFileID,
		Name: result.Name,
	}

	log.Infof("Successfully updated document drive_file_id=%s name=%s", result.DriveFileID, result.Name)
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// DeleteV3 handles DELETE /v3/gdrive/documents/:document_id
func (h *DocumentsHandler) DeleteV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "DeleteV3")

	documentID := r.Param("document_id")
	act := actor.FromGinCtx(r.GinCtx)

	log.Infof("Deleting document drive_file_id=%s tenant_id=%d", documentID, act.TenantID)

	// Validate document ID
	if documentID == "" {
		log.Error("Document ID is required")
		return handleValidationError(r.Context(), fmt.Errorf("document ID is required"))
	}

	err := h.documentService.DeleteDocument(r.Context(), documentID)
	if err != nil {
		log.WithError(err).Errorf("Failed to delete document drive_file_id=%s", documentID)
		return handleServiceError(r.Context(), err)
	}

	log.Infof("Successfully deleted document drive_file_id=%s", documentID)
	return ginext.NewResponseData(http.StatusNoContent, nil), nil
}

// SearchFilesV3 handles GET /v3/gdrive/search/files
// @Summary Search files in Google Drive
// @Description Search for files by keyword across Google Drive with pagination support
// @Tags Google Drive V3
// @Accept json
// @Produce json
// @Param keyword query string true "Search keyword"
// @Param page_size query int false "Number of results per page (default: 100, max: 1000)"
// @Param next_page query string false "Next page token for pagination"
// @Success 200 {object} SearchFilesV3Response "Search results"
// @Failure 400 {object} ErrorV3Response "Bad request"
// @Failure 401 {object} ErrorV3Response "Unauthorized"
// @Failure 500 {object} ErrorV3Response "Internal server error"
// @Router /v3/gdrive/search/files [get]
func (h *DocumentsHandler) SearchFilesV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "SearchFilesV3")

	query := &SearchFilesV3Request{}
	r.MustBind(query)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Searching files keyword=%s tenant_id=%d page_size=%d", query.Keyword, act.TenantID, query.PageSize)

	// Validate request
	if err := ValidateSearchFilesRequest(r.Context(), query); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Set default page size if not provided
	pageSize := query.PageSize
	if pageSize <= 0 {
		pageSize = 100 // Default page size matching SharePoint
	} else if pageSize > 1000 {
		pageSize = 1000 // Maximum page size for Google Drive API
	}

	// Set context timeout for search operations (matching SharePoint pattern)
	ctx, cancel := context.WithTimeout(r.Context(), 60*time.Second)
	defer cancel()

	// Perform search using Google Drive service
	searchReq := &gdrive.SearchDocumentsRequest{
		TenantID:      act.TenantID,
		ParentDriveID: "root", // Search globally across all accessible files
		Query:         query.Keyword,
		PageSize:      pageSize,
		PageToken:     query.NextPage,
	}

	result, err := h.documentService.SearchDocuments(ctx, searchReq)
	if err != nil {
		log.WithError(err).Errorf("Search failed keyword=%s", query.Keyword)
		return handleServiceError(r.Context(), err)
	}

	// Convert to search response format (SharePoint compatible)
	files := make([]FileItem, 0, len(result.Documents))
	for _, doc := range result.Documents {
		// Only include files in search results (not folders)
		if doc.IsFile {
			file := FileItem{
				DocID:          doc.DriveFileID,
				Name:           doc.Name,
				Size:           uint64(doc.Size),
				WebURL:         doc.WebViewLink,
				IsFile:         true,
				DateModified:   formatGoogleDriveTime(doc.UpdatedAt),
				DateCreated:    formatGoogleDriveTime(doc.CreatedAt),
				LastModifiedBy: extractLastModifiedBy(doc.WebViewLink), // Extract from metadata if available
			}
			files = append(files, file)
		}
	}

	// Build response with pagination metadata
	response := &SearchFilesV3Response{
		Data: files,
		Meta: FileListMeta{
			NextPage: result.NextPageToken,
			PageSize: pageSize,
			Total:    len(files),
		},
	}

	log.Infof("Search completed successfully keyword=%s results_count=%d", query.Keyword, len(files))
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// Helper functions

// resolveParentDriveID resolves parent folder ID from request parameters
func (h *DocumentsHandler) resolveParentDriveID(ctx context.Context, tenantID uint64, query *ListDocumentV3Request) (string, error) {
	log := logger.WithCtx(ctx, "resolveParentDriveID")

	if query.ID != "" {
		log.Infof("Using provided parent ID parent_id=%s", query.ID)
		return query.ID, nil
	}

	log.Infof("Resolving parent ID from object mapping object_type=%s object_id=%s tenant_id=%d",
		query.ObjectType, query.ObjectID, tenantID)

	// Get drive_id from DocumentMapping
	filters := []*model.Filter{
		model.NewFilterE("tenant_id", tenantID),
		model.NewFilterE("object_id", query.ObjectID),
		model.NewFilterE("type", query.ObjectType),
		model.NewFilterE("provider", model.DocProviderGoogle),
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	drive, err := h.documentMappingRepo.FindOne(ctx, queryBuild)
	if err != nil {
		log.WithError(err).Errorf("Failed to find document mapping object_type=%s object_id=%s",
			query.ObjectType, query.ObjectID)

		// Handle "record not found" case with structured error
		if err.Error() == "record not found" {
			return "", &gdrive.DocumentServiceError{
				Code:    gdrive.ErrCodeDocumentNotFound,
				Message: fmt.Sprintf("No document mapping found for %s with ID %s", query.ObjectType, query.ObjectID),
			}
		}

		// Handle other database errors
		return "", &gdrive.DocumentServiceError{
			Code:    gdrive.ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Failed to query document mapping for %s with ID %s", query.ObjectType, query.ObjectID),
		}
	}

	// Handle nil mapping case
	if drive == nil {
		return "", &gdrive.DocumentServiceError{
			Code:    gdrive.ErrCodeDocumentNotFound,
			Message: fmt.Sprintf("No document mapping found for %s with ID %s", query.ObjectType, query.ObjectID),
		}
	}

	log.Infof("Successfully resolved parent ID drive_id=%s object_type=%s object_id=%s",
		drive.DriveID, query.ObjectType, query.ObjectID)

	return drive.DriveID, nil
}

// handleSearch performs search operation
func (h *DocumentsHandler) handleSearch(ctx context.Context, tenantID uint64, parentDriveID string, query *ListDocumentV3Request) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleSearch")

	log.Infof("Performing search query=%s parent_drive_id=%s tenant_id=%d", query.Search, parentDriveID, tenantID)

	serviceReq := &gdrive.SearchDocumentsRequest{
		TenantID:      tenantID,
		ParentDriveID: parentDriveID,
		Query:         query.Search,
		PageSize:      query.PageSize,
		PageToken:     query.PageToken,
	}

	result, err := h.documentService.SearchDocuments(ctx, serviceReq)
	if err != nil {
		log.WithError(err).Errorf("Search failed query=%s parent_drive_id=%s", query.Search, parentDriveID)
		return handleServiceError(ctx, err)
	}

	response := h.convertToListResponse(ctx, result, parentDriveID)
	log.Infof("Search completed successfully query=%s results_count=%d", query.Search, len(response.Data))

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// handleList performs list operation
func (h *DocumentsHandler) handleList(ctx context.Context, tenantID uint64, parentDriveID string, query *ListDocumentV3Request) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleList")

	log.Infof("Performing list parent_drive_id=%s tenant_id=%d page_size=%d", parentDriveID, tenantID, query.PageSize)

	serviceReq := &gdrive.ListDocumentsRequest{
		TenantID:      tenantID,
		ParentDriveID: parentDriveID,
		PageSize:      query.PageSize,
		PageToken:     query.PageToken,
	}

	result, err := h.documentService.ListDocuments(ctx, serviceReq)
	if err != nil {
		log.WithError(err).Errorf("List failed parent_drive_id=%s", parentDriveID)
		return handleServiceError(ctx, err)
	}

	response := h.convertToListResponse(ctx, result, parentDriveID)
	log.Infof("List completed successfully parent_drive_id=%s results_count=%d", parentDriveID, len(response.Data))

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// convertToListResponse converts service layer response to V3 API format
func (h *DocumentsHandler) convertToListResponse(ctx context.Context, result *gdrive.DocumentListResponse, parentDriveID string) *ListDocumentV3Response {
	log := logger.WithCtx(ctx, "convertToListResponse")

	log.Infof("Converting list response documents_count=%d parent_drive_id=%s", len(result.Documents), parentDriveID)

	// Convert documents
	documents := make([]DocumentV3Response, 0, len(result.Documents))
	for _, doc := range result.Documents {
		v3Doc := h.convertToDocumentV3Response(doc)
		documents = append(documents, v3Doc)
	}

	// Get current working directory info (parent folder)
	cwd := DocumentV3Response{
		ID:           parentDriveID,
		Name:         "Current Folder",
		IsFile:       false,
		IsInternal:   false,
		HasChild:     len(documents) > 0,
		Size:         0,
		Type:         "folder",
		DateCreated:  time.Now(),
		DateModified: time.Now(),
		Status:       1,
	}

	// Create pagination metadata
	meta := PaginationMeta{
		NextPage: result.NextPageToken,
		PageSize: len(documents),
		Total:    result.Total,
		HasNext:  result.NextPageToken != "",
	}

	response := &ListDocumentV3Response{
		Cwd:  cwd,
		Data: documents,
		Meta: meta,
	}

	log.Infof("Successfully converted list response documents_count=%d", len(documents))
	return response
}

// convertToDocumentV3Response converts service document to V3 format
func (h *DocumentsHandler) convertToDocumentV3Response(doc *gdrive.DocumentResponse) DocumentV3Response {
	// Determine document type
	docType := 1 // folder
	if doc.IsFile {
		docType = 2 // file
	}

	// Extract file extension
	fileType := "folder"
	if doc.IsFile && doc.ContentType != "" {
		fileType = helper.ExtensionByType(doc.ContentType)
	}

	return DocumentV3Response{
		ID:             doc.DriveFileID,
		Name:           doc.Name,
		ParentID:       0, // Not used in Google Drive context
		DocType:        docType,
		ObjectType:     0, // Not used in Google Drive context
		ObjectID:       0, // Not used in Google Drive context
		IsFile:         doc.IsFile,
		IsInternal:     false, // Always false for Google Drive
		HasChild:       false, // Would need additional API call to determine
		Size:           doc.Size,
		Type:           fileType,
		DateCreated:    doc.CreatedAt,
		DateModified:   doc.UpdatedAt,
		LastModifiedBy: "Google Drive User", // Google Drive doesn't provide this easily
		WebURL:         doc.WebViewLink,
		Status:         1, // Always active
	}
}

// formatGoogleDriveTime formats time.Time to Google Drive API compatible string format
func formatGoogleDriveTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(time.RFC3339)
}

// extractLastModifiedBy extracts the last modified user from Google Drive metadata
// Since Google Drive API doesn't easily provide this in search results, we return a default
func extractLastModifiedBy(_ string) string {
	// Google Drive doesn't provide last modified user in search results easily
	// This would require additional API calls to get file metadata
	// For now, return a default value to match SharePoint API structure
	return "Google Drive User"
}

// resolveParentID resolves the parent ID for document operations
func (h *DocumentsHandler) resolveParentID(ctx context.Context, req *CreateDocumentV3Request, act *actor.Actor) (string, error) {
	log := logger.WithCtx(ctx, "resolveParentID")

	// First, try to resolve parent ID directly or through object mapping
	var baseParentID string

	// If parent_id is provided directly, use it
	if req.ParentID != "" {
		log.Infof("Using provided parent_id=%s as base folder", req.ParentID)
		baseParentID = req.ParentID
	} else if req.ObjectType != "" && req.ObjectID != 0 {
		// If object mapping is provided, resolve through mapping
		log.Infof("Resolving base parent using object mapping object_type=%s object_id=%d", req.ObjectType, req.ObjectID)

		// Get mapping from repository
		filters := []*model.Filter{
			model.NewFilterE("tenant_id", act.TenantID),
			model.NewFilterE("object_type", req.ObjectType),
			model.NewFilterE("object_id", req.ObjectID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		}
		query := helper.BuildQuery("", filters, nil, nil)
		mapping, err := h.documentMappingRepo.FindOne(ctx, query)
		if err != nil {
			log.WithError(err).Errorf("Failed to find document mapping object_type=%s object_id=%d", req.ObjectType, req.ObjectID)
			return "", fmt.Errorf("failed to resolve parent folder: %w", err)
		}

		log.Infof("Successfully resolved object mapping to parent_id=%s", mapping.DriveID)
		baseParentID = mapping.DriveID
	} else if req.ParentPath == "" {
		// If no parent specification provided at all, this is an error
		return "", fmt.Errorf("no valid parent specification provided")
	} else {
		// If only parent_path is provided without a base parent, use "root" as default
		log.Infof("No base parent specified, using 'root' as default for parent_path")
		baseParentID = "root"
	}

	// If parent_path is provided, resolve hierarchical path starting from the base parent
	if req.ParentPath != "" {
		log.Infof("Resolving hierarchical path parent_path=%s from base_parent_id=%s", req.ParentPath, baseParentID)

		// Prepare request for path resolution
		pathReq := &gdrive.ResolveHierarchicalPathRequest{
			TenantID:    act.TenantID,
			ParentPath:  req.ParentPath,
			RootDriveID: baseParentID, // Use the resolved base parent as root
			ObjectType:  req.ObjectType,
			ObjectID:    req.ObjectID,
		}

		// Call service to resolve path
		pathResp, err := h.documentService.ResolveHierarchicalPath(ctx, pathReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to resolve hierarchical path parent_path=%s from base_parent_id=%s", req.ParentPath, baseParentID)
			return "", fmt.Errorf("failed to resolve parent path: %w", err)
		}

		log.Infof("Successfully resolved hierarchical path to final_parent_id=%s", pathResp.FinalParentID)
		return pathResp.FinalParentID, nil
	}

	// If no parent_path, return the base parent ID
	return baseParentID, nil
}
