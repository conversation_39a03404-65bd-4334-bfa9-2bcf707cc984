package gdrive

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/domain/errors"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"gitlab.com/goxp/cloud0/ginext"
)

// SetupHandler handles Google Drive setup operations for both V1 and V3 APIs
type SetupHandler struct {
	gdriveService       gdrive.Service
	clientEventConsumer *gdrive.ClientEventConsumer
	matterEventConsumer *gdrive.MatterEventConsumer
}

// NewSetupHandler creates a new setup handler
func NewSetupHandler(
	gdriveService gdrive.Service,
	clientEventConsumer *gdrive.ClientEventConsumer,
	matterEventConsumer *gdrive.MatterEventConsumer,
) *SetupHandler {
	return &SetupHandler{
		gdriveService:       gdriveService,
		clientEventConsumer: clientEventConsumer,
		matterEventConsumer: matterEventConsumer,
	}
}

// TestSetupV3 handles POST /v3/gdrive/setup/test-setup
func (h *SetupHandler) TestSetupV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "TestSetupV3")

	req := &TestSetupV3Request{}
	r.MustBind(req)

	log.Infof("Testing Google Drive setup url_or_id=%s", req.URLOrID)

	// Validate request
	if err := ValidateTestSetupRequest(r.Context(), req); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Call service layer
	info, err := h.gdriveService.TestSetup(r.Context(), req.URLOrID)
	if err != nil {
		log.WithError(err).Errorf("Failed to test Google Drive setup url_or_id=%s", req.URLOrID)
		return handleServiceError(r.Context(), err)
	}

	// Convert to V3 response format
	response := &TestSetupV3Response{
		Status:    "success",
		DriveInfo: info,
		Message:   "Google Drive connection test successful",
	}

	log.Infof("Successfully tested Google Drive setup drive_id=%s drive_type=%s", info.ID, info.Type)
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// CompleteSetupV3 handles POST /v3/gdrive/setup/complete-setup
func (h *SetupHandler) CompleteSetupV3(r *ginext.Request) (*ginext.Response, error) {
	log := logger.WithCtx(r.Context(), "CompleteSetupV3")

	req := &CompleteSetupV3Request{}
	r.MustBind(req)

	act := actor.FromGinCtx(r.GinCtx)
	log.Infof("Completing Google Drive setup url_or_id=%s enabled=%t tenant_id=%d user_id=%d",
		req.URLOrID, req.Enabled, act.TenantID, act.ID)

	// Validate request
	if err := ValidateCompleteSetupRequest(r.Context(), req); err != nil {
		return handleValidationError(r.Context(), err)
	}

	// Check authorization (admin/manager roles required)
	if !hasSetupRoles(act.Roles) {
		log.Errorf("User not authorized for Google Drive setup user_id=%d roles=%d", act.ID, act.Roles)
		return handleAuthorizationError(r.Context(), "Insufficient permissions for Google Drive setup")
	}

	// Convert to service layer request
	serviceReq := &gdrive.CompleteSetupRequest{
		URLOrID:    req.URLOrID,
		TenantID:   act.TenantID,
		Enabled:    req.Enabled,
		PathConfig: convertPathConfigToModel(req.PathConfig),
	}

	// Call service layer
	serviceResp, err := h.gdriveService.CompleteSetup(r.Context(), serviceReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to complete Google Drive setup url_or_id=%s tenant_id=%d",
			req.URLOrID, act.TenantID)
		return handleServiceError(r.Context(), err)
	}

	// Build response
	status := "enabled"
	message := "Google Drive integration has been enabled successfully"
	if !req.Enabled {
		status = "disabled"
		message = "Google Drive integration has been disabled successfully"
	}

	response := &CompleteSetupV3Response{
		Status:     "success",
		Message:    message,
		Enabled:    req.Enabled,
		State:      status,
		PathConfig: convertPathConfigFromModel(serviceResp.AppliedPathConfig),
	}

	log.Infof("Successfully completed Google Drive setup tenant_id=%d enabled=%t", act.TenantID, req.Enabled)
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// hasSetupRoles checks if the user has required roles for setup operations
func hasSetupRoles(roles userrole.Role) bool {
	requiredRoles := userrole.RoleAdmin | userrole.RoleManager | userrole.RoleSysadmin
	return userrole.HasAnyRoles(roles, requiredRoles)
}

// ValidateTestSetupRequest validates test setup request parameters
func ValidateTestSetupRequest(ctx context.Context, req *TestSetupV3Request) error {
	log := logger.WithCtx(ctx, "ValidateTestSetupRequest")

	log.Infof("Validating test setup request url_or_id=%s", req.URLOrID)

	if req.URLOrID == "" {
		log.Error("URL or ID is required")
		return errors.NewBadDataErr("url_or_id is required")
	}

	// Basic URL/ID format validation
	if len(req.URLOrID) < 10 {
		log.Errorf("Invalid URL or ID format url_or_id=%s", req.URLOrID)
		return errors.NewBadDataErr("url_or_id appears to be too short to be valid")
	}

	log.Infof("Test setup request validation passed url_or_id=%s", req.URLOrID)
	return nil
}

// ValidateCompleteSetupRequest validates complete setup request parameters
func ValidateCompleteSetupRequest(ctx context.Context, req *CompleteSetupV3Request) error {
	log := logger.WithCtx(ctx, "ValidateCompleteSetupRequest")

	log.Infof("Validating complete setup request url_or_id=%s enabled=%t", req.URLOrID, req.Enabled)

	if req.URLOrID == "" {
		log.Error("URL or ID is required")
		return errors.NewBadDataErr("url_or_id is required")
	}

	// Basic URL/ID format validation
	if len(req.URLOrID) < 10 {
		log.Errorf("Invalid URL or ID format url_or_id=%s", req.URLOrID)
		return errors.NewBadDataErr("url_or_id appears to be too short to be valid")
	}

	// Validate PathConfig if provided
	if req.PathConfig != nil {
		if err := validatePathConfigAPI(ctx, req.PathConfig); err != nil {
			log.WithError(err).Error("Invalid path configuration")
			return errors.NewBadDataErr(err.Error())
		}
	}

	log.Infof("Complete setup request validation passed url_or_id=%s", req.URLOrID)
	return nil
}

// validatePathConfigAPI validates the PathConfig from API request
func validatePathConfigAPI(ctx context.Context, config *PathConfigV3) error {
	log := logger.WithCtx(ctx, "validatePathConfigAPI")

	// Validate CaseFormat
	if config.CaseFormat != "" {
		validFormats := []string{"original", "lower", "upper", "title"}
		isValid := false
		for _, format := range validFormats {
			if config.CaseFormat == format {
				isValid = true
				break
			}
		}
		if !isValid {
			log.Errorf("Invalid case_format=%s", config.CaseFormat)
			return fmt.Errorf("case_format must be one of: %s", strings.Join(validFormats, ", "))
		}
	}

	// Validate MaxLength (only if provided, 0 means use default)
	if config.MaxLength != 0 && (config.MaxLength < 1 || config.MaxLength > 255) {
		log.Errorf("Invalid max_length=%d", config.MaxLength)
		return fmt.Errorf("max_length must be between 1 and 255")
	}

	// Validate InvalidCharReplace
	if config.InvalidCharReplace != "" && len(config.InvalidCharReplace) != 1 {
		log.Errorf("Invalid invalid_char_replace=%s", config.InvalidCharReplace)
		return fmt.Errorf("invalid_char_replace must be a single character")
	}

	// Validate folder path templates
	if config.ClientFolderPath != "" {
		if err := validateClientFolderPathTemplate(config.ClientFolderPath); err != nil {
			log.WithError(err).Errorf("Invalid client_folder_path=%s", config.ClientFolderPath)
			return fmt.Errorf("client_folder_path: %s", err.Error())
		}
	}

	if config.MatterFolderPath != "" {
		if err := validateMatterFolderPathTemplate(config.MatterFolderPath); err != nil {
			log.WithError(err).Errorf("Invalid matter_folder_path=%s", config.MatterFolderPath)
			return fmt.Errorf("matter_folder_path: %s", err.Error())
		}
	}

	log.Info("Path configuration validation passed")
	return nil
}

// validateClientFolderPathTemplate validates client folder path template
func validateClientFolderPathTemplate(path string) error {
	if strings.TrimSpace(path) == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// Check for path traversal attempts
	if strings.Contains(path, "..") || strings.Contains(path, "~") {
		return fmt.Errorf("path contains invalid traversal patterns")
	}

	// Validate template variables
	validClientVars := map[string]bool{
		"name":       true,
		"short_name": true,
		"code":       true,
		"id":         true,
	}

	return validateTemplateVariables(path, validClientVars, "client")
}

// validateMatterFolderPathTemplate validates matter folder path template
func validateMatterFolderPathTemplate(path string) error {
	if strings.TrimSpace(path) == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// Check for path traversal attempts
	if strings.Contains(path, "..") || strings.Contains(path, "~") {
		return fmt.Errorf("path contains invalid traversal patterns")
	}

	// Must contain {client_folder} variable for proper hierarchy
	if !strings.Contains(path, "{client_folder}") {
		return fmt.Errorf("matter folder path must contain {client_folder} variable")
	}

	// Validate template variables
	validMatterVars := map[string]bool{
		"client_folder": true,
		"name":          true,
		"code":          true,
		"id":            true,
		"client_id":     true,
	}

	return validateTemplateVariables(path, validMatterVars, "matter")
}

// validateTemplateVariables validates template variables in a path
func validateTemplateVariables(template string, validVars map[string]bool, segmentType string) error {
	// Find all variables in template
	varPattern := `\{([^}]+)\}`
	re := regexp.MustCompile(varPattern)
	matches := re.FindAllStringSubmatch(template, -1)

	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		varName := match[1]

		// Handle fallback syntax like {short_name|name}
		if strings.Contains(varName, "|") {
			fallbackVars := strings.Split(varName, "|")
			allValid := true
			for _, fVar := range fallbackVars {
				fVar = strings.TrimSpace(fVar)
				if !validVars[fVar] {
					allValid = false
					break
				}
			}
			if !allValid {
				return fmt.Errorf("invalid fallback variable '%s' in %s template", varName, segmentType)
			}
		} else if !validVars[varName] {
			return fmt.Errorf("invalid variable '%s' in %s template", varName, segmentType)
		}
	}

	return nil
}

// convertPathConfigToModel converts API PathConfig to domain model PathConfig
func convertPathConfigToModel(apiConfig *PathConfigV3) *model.PathConfig {
	if apiConfig == nil {
		return nil
	}

	return &model.PathConfig{
		ClientFolderPath:   apiConfig.ClientFolderPath,
		MatterFolderPath:   apiConfig.MatterFolderPath,
		CaseFormat:         apiConfig.CaseFormat,
		MaxLength:          apiConfig.MaxLength,
		InvalidCharReplace: apiConfig.InvalidCharReplace,
	}
}

// convertPathConfigFromModel converts domain model PathConfig to API PathConfig
func convertPathConfigFromModel(modelConfig *model.PathConfig) *PathConfigV3 {
	if modelConfig == nil {
		return nil
	}

	return &PathConfigV3{
		ClientFolderPath:   modelConfig.ClientFolderPath,
		MatterFolderPath:   modelConfig.MatterFolderPath,
		CaseFormat:         modelConfig.CaseFormat,
		MaxLength:          modelConfig.MaxLength,
		InvalidCharReplace: modelConfig.InvalidCharReplace,
	}
}

// ========== EVENT CONSUMER METHODS ==========

// ClientEventRequest defines the structure for client event requests
type ClientEventRequest struct {
	Topic string                 `json:"topic" binding:"required"`
	Body  map[string]interface{} `json:"body" binding:"required"`
}

// ConsumeGoogleDriveClientCrud handles client events for Google Drive folder operations
func (h *SetupHandler) ConsumeGoogleDriveClientCrud(r *ginext.Request) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "ConsumeGoogleDriveClientCrud")

	var req ClientEventRequest
	r.MustBind(&req)

	log.Infof("Received client event request topic=%s", req.Topic)

	payloadBytes, err := json.Marshal(req)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event payload")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
	}
	payloadJSON := string(payloadBytes)

	if req.Topic == "" {
		log.Error("Missing topic in event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing topic in request")
	}

	log.Infof("Processing Google Drive client event topic=%s", req.Topic)

	if h.clientEventConsumer == nil {
		log.Error("Client event consumer is not initialized")
		return nil, ginext.NewError(http.StatusInternalServerError, "Service configuration error")
	}

	var eventErr error
	switch req.Topic {
	case "client.create":
		eventErr = h.clientEventConsumer.HandleClientCreated(ctx, payloadJSON)
	case "client.update":
		eventErr = h.clientEventConsumer.HandleClientUpdated(ctx, payloadJSON)
	default:
		log.Infof("Unsupported topic, skipping topic=%s", req.Topic)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	if eventErr != nil {
		log.WithError(eventErr).Errorf("Failed to process client event topic=%s", req.Topic)
		// Return 500 so event system will retry
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+eventErr.Error())
	}

	log.Infof("Successfully processed Google Drive client event topic=%s", req.Topic)
	return ginext.NewResponseData(http.StatusOK, map[string]string{"status": "success"}), nil
}

// MatterEventRequest defines the structure for matter event requests
type MatterEventRequest struct {
	Topic string                 `json:"topic" binding:"required"`
	Body  map[string]interface{} `json:"body" binding:"required"`
}

// ConsumeGoogleDriveMatterCrud handles matter events for Google Drive folder operations
func (h *SetupHandler) ConsumeGoogleDriveMatterCrud(r *ginext.Request) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "ConsumeGoogleDriveMatterCrud")

	var req MatterEventRequest
	r.MustBind(&req)

	log.Infof("Received matter event request topic=%s", req.Topic)

	payloadBytes, err := json.Marshal(req)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event payload")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event")
	}
	payloadJSON := string(payloadBytes)

	if req.Topic == "" {
		log.Error("Missing topic in event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing topic in request")
	}

	log.Infof("Processing Google Drive matter event topic=%s", req.Topic)

	if h.matterEventConsumer == nil {
		log.Error("Matter event consumer is not initialized")
		return nil, ginext.NewError(http.StatusInternalServerError, "Service configuration error")
	}

	var eventErr error
	switch req.Topic {
	case "matter.create":
		eventErr = h.matterEventConsumer.HandleMatterCreated(ctx, payloadJSON)
	case "matter.update":
		eventErr = h.matterEventConsumer.HandleMatterUpdated(ctx, payloadJSON)
	default:
		log.Infof("Unsupported topic, skipping topic=%s", req.Topic)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	if eventErr != nil {
		log.WithError(eventErr).Errorf("Failed to process matter event topic=%s", req.Topic)
		// Return 500 so event system will retry
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+eventErr.Error())
	}

	log.Infof("Successfully processed Google Drive matter event topic=%s", req.Topic)
	return ginext.NewResponseData(http.StatusOK, map[string]string{"status": "success"}), nil
}
