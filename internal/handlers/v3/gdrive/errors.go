package gdrive

import (
	"context"
	"net/http"

	"bilabl/docman/internal/service/gdrive"

	"code.mybil.net/gophers/gokit/domain/errors"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"gitlab.com/goxp/cloud0/ginext"
)

// ErrorResponse represents the standardized error response format
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail contains the error message
type ErrorDetail struct {
	Msg string `json:"msg"`
}

// newErrorResponse creates a standardized error response
func newErrorResponse(statusCode int, message string) (*ginext.Response, error) {
	errorResp := &ErrorResponse{
		Error: ErrorDetail{
			Msg: message,
		},
	}

	// Create response without data wrapper using WithRawBody
	return ginext.NewResponse(statusCode, ginext.WithRawBody(errorResp)), nil
}

// HandleServiceError handles service errors (exported for testing)
func HandleServiceError(ctx context.Context, err error) (*ginext.Response, error) {
	return handleServiceError(ctx, err)
}

// SharePoint-compatible error handling with standardized logging
func handleServiceError(ctx context.Context, err error) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleServiceError")

	if docErr, ok := err.(*gdrive.DocumentServiceError); ok {
		log.WithError(err).Errorf("Service error occurred code=%s message=%s", docErr.Code, docErr.Message)

		switch docErr.Code {
		case gdrive.ErrCodeInvalidRequest:
			return newErrorResponse(http.StatusBadRequest, docErr.Message)
		case gdrive.ErrCodeDocumentNotFound:
			return newErrorResponse(http.StatusNotFound, docErr.Message)
		case gdrive.ErrCodeDriveAPIError:
			return newErrorResponse(http.StatusInternalServerError, docErr.Message)
		case gdrive.ErrCodePermissionDenied:
			return newErrorResponse(http.StatusForbidden, docErr.Message)
		default:
			log.Errorf("Unknown service error code code=%s", docErr.Code)
			return newErrorResponse(http.StatusInternalServerError, "An unexpected error occurred")
		}
	}

	// Handle FolderCreationError for folder operations
	if folderErr, ok := err.(*gdrive.FolderCreationError); ok {
		log.WithError(err).Errorf("Folder creation error occurred code=%s message=%s", folderErr.Code, folderErr.Message)

		switch folderErr.Code {
		case gdrive.ErrCodeNotFound:
			return newErrorResponse(http.StatusNotFound, folderErr.Message)
		case gdrive.ErrCodeConfigMissing:
			return newErrorResponse(http.StatusBadRequest, folderErr.Message)
		case gdrive.ErrCodePermissionDenied:
			return newErrorResponse(http.StatusForbidden, folderErr.Message)
		case gdrive.ErrCodeFolderExists:
			return newErrorResponse(http.StatusConflict, folderErr.Message)
		case gdrive.ErrCodeAPIError:
			return newErrorResponse(http.StatusInternalServerError, folderErr.Message)
		case gdrive.ErrCodeInvalidPath:
			return newErrorResponse(http.StatusBadRequest, folderErr.Message)
		case gdrive.ErrCodeTemplateError:
			return newErrorResponse(http.StatusBadRequest, folderErr.Message)
		default:
			log.Errorf("Unknown folder creation error code code=%s", folderErr.Code)
			return newErrorResponse(http.StatusInternalServerError, "An unexpected error occurred")
		}
	}

	// Handle gokit errors - convert to standardized format
	if gokitErr, ok := err.(errors.Error); ok {
		log.WithError(err).Errorf("Gokit error occurred code=%d message=%s", gokitErr.ErrCode, gokitErr.Message)

		// Map gokit error codes to HTTP status codes
		var statusCode int
		switch gokitErr.ErrCode {
		case errors.ErrCodeBadData:
			statusCode = http.StatusBadRequest
		case errors.ErrCodeUnauthorized:
			statusCode = http.StatusBadRequest // Use 400 for invalid URL/ID instead of 403
		default:
			statusCode = http.StatusInternalServerError
		}

		// Use standardized error response format
		return newErrorResponse(statusCode, gokitErr.Message)
	}

	log.WithError(err).Error("Unexpected error occurred")
	return newErrorResponse(http.StatusInternalServerError, "An unexpected error occurred")
}

// HandleValidationError creates a standardized validation error response (exported for testing)
func HandleValidationError(ctx context.Context, err error) (*ginext.Response, error) {
	return handleValidationError(ctx, err)
}

// handleValidationError creates a standardized validation error response
func handleValidationError(ctx context.Context, err error) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleValidationError")
	log.WithError(err).Error("Request validation failed")

	return newErrorResponse(http.StatusBadRequest, err.Error())
}

// handleAuthenticationError creates a standardized authentication error response
func handleAuthenticationError(ctx context.Context, message string) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleAuthenticationError")
	log.Errorf("Authentication failed message=%s", message)

	return newErrorResponse(http.StatusUnauthorized, message)
}

// handleAuthorizationError creates a standardized authorization error response
func handleAuthorizationError(ctx context.Context, message string) (*ginext.Response, error) {
	log := logger.WithCtx(ctx, "handleAuthorizationError")
	log.Errorf("Authorization failed message=%s", message)

	return newErrorResponse(http.StatusForbidden, message)
}
