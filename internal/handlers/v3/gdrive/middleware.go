package gdrive

import (
	"net/http"
	"time"

	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"github.com/gin-gonic/gin"
)

// GDriveAuthMiddleware validates Google Drive access for the tenant
func GDriveAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.WithCtx(c.Request.Context(), "GDriveAuthMiddleware")

		// Verify Google Drive integration is enabled for tenant
		act := actor.FromGinCtx(c)
		log.Infof("Checking Google Drive access tenant_id=%d", act.TenantID)

		// TODO: Check if Google Drive is configured for this tenant
		// This would typically involve checking the tenant's settings
		// For now, we'll assume it's always enabled

		log.Infof("Google Drive access verified tenant_id=%d", act.TenantID)
		c.Next()
	}
}

// GDriveLoggingMiddleware logs Google Drive API requests
func GDriveLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.WithCtx(c.Request.Context(), "GDriveLoggingMiddleware")

		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		log.Infof("Google Drive API request started method=%s path=%s", method, path)

		c.Next()

		duration := time.Since(start)
		status := c.Writer.Status()

		log.Infof("Google Drive API request completed method=%s path=%s status=%d duration=%v",
			method, path, status, duration)
	}
}

// GDriveRateLimitMiddleware implements rate limiting for Google Drive API calls
func GDriveRateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.WithCtx(c.Request.Context(), "GDriveRateLimitMiddleware")

		// TODO: Implement rate limiting logic
		// This would typically involve checking request counts per tenant/user
		// and applying appropriate limits to prevent API quota exhaustion

		act := actor.FromGinCtx(c)
		log.Infof("Rate limit check passed tenant_id=%d", act.TenantID)

		c.Next()
	}
}

// GDriveErrorRecoveryMiddleware handles panics and converts them to proper error responses
func GDriveErrorRecoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.WithCtx(c.Request.Context(), "GDriveErrorRecoveryMiddleware")

		defer func() {
			if err := recover(); err != nil {
				log.Errorf("Panic recovered in Google Drive API error=%v", err)

				// Return a generic error response
				c.JSON(500, gin.H{
					"error_code":    "INTERNAL_ERROR",
					"error_message": "An unexpected error occurred",
				})
				c.Abort()
			}
		}()

		c.Next()
	}
}

// GDriveAdminMiddleware validates admin permissions for setup operations
func GDriveAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.WithCtx(c.Request.Context(), "GDriveAdminMiddleware")

		act := actor.FromGinCtx(c)
		log.Infof("Checking admin permissions user_id=%d tenant_id=%d roles=%d", act.ID, act.TenantID, act.Roles)

		// Check if user has required roles for setup operations
		requiredRoles := userrole.RoleAdmin | userrole.RoleManager | userrole.RoleSysadmin
		if !userrole.HasAnyRoles(act.Roles, requiredRoles) {
			log.Errorf("Insufficient permissions for setup operations user_id=%d roles=%d", act.ID, act.Roles)
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions for Google Drive setup operations",
			})
			c.Abort()
			return
		}

		log.Infof("Admin permissions verified user_id=%d", act.ID)
		c.Next()
	}
}
