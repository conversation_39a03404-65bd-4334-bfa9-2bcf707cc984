package handlers

import (
	"context"
	"fmt"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/coordination"
	"bilabl/docman/pkg/bilabllog"
)

// EventRuleMatchingService interface for autodoc rule processing
type EventRuleMatchingService interface {
	ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error
}

// AutodocRulesHandler handles autodoc rule execution after folder creation completion
type AutodocRulesHandler struct {
	eventRuleMatchingService EventRuleMatchingService
}

// NewAutodocRulesHandler creates a new autodoc rules handler
func NewAutodocRulesHandler(eventRuleMatchingService EventRuleMatchingService) coordination.DownstreamActionHandler {
	return &AutodocRulesHandler{
		eventRuleMatchingService: eventRuleMatchingService,
	}
}

// GetActionType returns the action type this handler supports
func (h *AutodocRules<PERSON>andler) GetActionType() string {
	return "autodoc_rules"
}

// CanExecute validates if action can be executed
func (h *AutodocRulesHandler) CanExecute(ctx context.Context, status *model.OperationCoordinationStatus) bool {
	// Support both matter and client operations
	validOperationTypes := []string{
		"matter_folder_creation", // matter.create
		"matter_folder_update",   // matter.update
		"client_folder_creation", // client.create
		"client_folder_update",   // client.update
	}

	isValidOperation := false
	for _, validType := range validOperationTypes {
		if status.OperationType == validType {
			isValidOperation = true
			break
		}
	}
	if !isValidOperation {
		return false
	}

	// Support both matter and client entities
	if status.EntityType != "matter" && status.EntityType != "client" {
		return false
	}

	if status.OverallStatus != model.CompletionStatusCompleted {
		return false
	}

	// Must have required data
	if status.EntityID == 0 || status.TenantID == 0 {
		return false
	}

	return true
}

// HandleAction handles autodoc rule execution after folder creation completion
func (h *AutodocRulesHandler) HandleAction(ctx context.Context, status *model.OperationCoordinationStatus) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Executing autodoc rules after folder creation completion", map[string]interface{}{
		"operation_id":   status.OperationID,
		"operation_type": status.OperationType,
		"entity_type":    status.EntityType,
		"entity_id":      status.EntityID,
		"tenant_id":      status.TenantID,
	})

	// Extract matter data from metadata
	metadata := status.GetMetadata()

	// Create event data for autodoc processing
	eventData := map[string]interface{}{
		"id":        status.EntityID,
		"tenant_id": status.TenantID,
	}

	// Add parent entity data if available
	if status.ParentEntityID != 0 {
		eventData["client_id"] = status.ParentEntityID
	}

	// Add additional fields from metadata
	for key, value := range metadata {
		// Skip system fields to avoid conflicts
		if key != "id" && key != "tenant_id" && key != "client_id" {
			eventData[key] = value
		}
	}

	log.Info("Triggering autodoc rules", map[string]interface{}{
		"operation_id": status.OperationID,
		"entity_type":  status.EntityType,
		"entity_id":    status.EntityID,
		"tenant_id":    status.TenantID,
		"event_data":   eventData,
	})

	// Determine event type based on operation type and entity type
	eventType := h.determineEventType(status.OperationType, status.EntityType)
	if eventType == "" {
		log.Warn("Unknown operation type, skipping autodoc rules", map[string]interface{}{
			"operation_type": status.OperationType,
			"entity_type":    status.EntityType,
		})
		return nil
	}

	log.Info("Determined event type for autodoc processing", map[string]interface{}{
		"operation_type": status.OperationType,
		"entity_type":    status.EntityType,
		"event_type":     eventType,
	})

	// Process autodoc rules with determined event type
	err := h.eventRuleMatchingService.ProcessEvent(ctx, status.TenantID, eventType, eventData)
	if err != nil {
		log.WithError(err).Error("Failed to process autodoc rules")
		return fmt.Errorf("failed to process autodoc rules: %w", err)
	}

	log.Info("Successfully executed autodoc rules", map[string]interface{}{
		"operation_id": status.OperationID,
		"entity_type":  status.EntityType,
		"entity_id":    status.EntityID,
		"tenant_id":    status.TenantID,
		"event_type":   eventType,
	})

	return nil
}

// determineEventType determines the event type based on operation type and entity type
func (h *AutodocRulesHandler) determineEventType(operationType, entityType string) string {
	// Map operation types to event types
	switch {
	case operationType == "matter_folder_creation" && entityType == "matter":
		return "matter.create"
	case operationType == "matter_folder_update" && entityType == "matter":
		return "matter.update"
	case operationType == "client_folder_creation" && entityType == "client":
		return "client.create"
	case operationType == "client_folder_update" && entityType == "client":
		return "client.update"
	default:
		// Return empty string for unknown combinations
		return ""
	}
}
