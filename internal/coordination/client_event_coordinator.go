package coordination

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// ClientEventCoordinator coordinates client-related events across multiple providers
type ClientEventCoordinator interface {
	// ProcessClientCreateEvent processes a client.create event with coordination
	ProcessClientCreateEvent(ctx context.Context, req *ClientCreateEventRequest) error

	// ProcessClientUpdateEvent processes a client.update event with coordination
	ProcessClientUpdateEvent(ctx context.Context, req *ClientUpdateEventRequest) error

	// RegisterProviderOperation registers a provider operation
	RegisterProviderOperation(provider string, operation ProviderOperation)
}

// ClientCreateEventRequest represents a client.create event request
type ClientCreateEventRequest struct {
	TenantID  uint64
	ClientID  uint64
	EventID   string
	EventData map[string]interface{}
}

// ClientUpdateEventRequest represents a client.update event request
type ClientUpdateEventRequest struct {
	TenantID  uint64
	ClientID  uint64
	EventID   string
	EventData map[string]interface{}
}

// clientEventCoordinator implements ClientEventCoordinator
type clientEventCoordinator struct {
	operationCoordinator  OperationCoordinator
	providerConfigService ProviderConfigService
	providerOperations    map[string]ProviderOperation
}

// NewClientEventCoordinator creates a new client event coordinator
func NewClientEventCoordinator(
	operationCoordinator OperationCoordinator,
	providerConfigService ProviderConfigService,
) ClientEventCoordinator {
	return &clientEventCoordinator{
		operationCoordinator:  operationCoordinator,
		providerConfigService: providerConfigService,
		providerOperations:    make(map[string]ProviderOperation),
	}
}

// RegisterProviderOperation registers a provider operation
func (c *clientEventCoordinator) RegisterProviderOperation(provider string, operation ProviderOperation) {
	c.providerOperations[provider] = operation
}

// ProcessClientCreateEvent processes a client.create event with coordination
func (c *clientEventCoordinator) ProcessClientCreateEvent(ctx context.Context, req *ClientCreateEventRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing coordinated client.create event", map[string]interface{}{
		"event_id":  req.EventID,
		"tenant_id": req.TenantID,
		"client_id": req.ClientID,
	})

	// Get enabled providers for client folder creation
	requiredProviders, optionalProviders, err := c.providerConfigService.GetEnabledProviders(ctx, req.TenantID, "client_folder_creation")
	if err != nil {
		log.WithError(err).Error("Failed to get enabled providers for client.create")
		return fmt.Errorf("failed to get enabled providers: %w", err)
	}

	log.Info("Enabled providers for client.create", map[string]interface{}{
		"tenant_id":          req.TenantID,
		"required_providers": requiredProviders,
		"optional_providers": optionalProviders,
	})

	// Generate unique operation ID if not provided
	operationID := req.EventID
	if operationID == "" {
		operationID = fmt.Sprintf("client_%d_folder_creation_%d", req.ClientID, time.Now().Unix())
	}

	// Initialize coordination for client.create
	initReq := &InitializeOperationRequest{
		TenantID:          req.TenantID,
		OperationType:     "client_folder_creation",
		OperationID:       operationID,
		EntityType:        "client",
		EntityID:          req.ClientID,
		ParentEntityType:  "",
		ParentEntityID:    0,
		RequiredProviders: requiredProviders,
		OptionalProviders: optionalProviders,
		DownstreamAction:  "autodoc_rules",
		TimeoutDuration:   c.providerConfigService.GetOperationTimeout(ctx, req.TenantID, "client_folder_creation"),
		Metadata:          req.EventData,
	}

	if err := c.operationCoordinator.InitializeOperation(ctx, initReq); err != nil {
		log.WithError(err).Error("Failed to initialize operation coordination for client.create")
		return fmt.Errorf("failed to initialize operation coordination: %w", err)
	}

	// Create completion callback
	completionCallback := func(ctx context.Context, operationID, provider string, success bool, err error) {
		status := model.ProviderStatusCompleted
		if !success {
			status = model.ProviderStatusFailed
		}

		completionReq := &ProviderCompletionRequest{
			OperationID: operationID,
			Provider:    provider,
			Status:      status,
			Error:       err,
		}

		if notifyErr := c.operationCoordinator.NotifyProviderCompletion(ctx, completionReq); notifyErr != nil {
			log.WithError(notifyErr).Error("Failed to notify provider completion for client.create")
		}
	}

	// Trigger provider operations for client.create
	if err := c.triggerProviderOperations(ctx, req, operationID, requiredProviders, completionCallback); err != nil {
		log.WithError(err).Error("Failed to trigger provider operations for client.create")
		return fmt.Errorf("failed to trigger provider operations: %w", err)
	}

	log.Info("Client folder creation coordination initiated", map[string]interface{}{
		"operation_id":       operationID,
		"client_id":          req.ClientID,
		"required_providers": requiredProviders,
	})

	return nil
}

// ProcessClientUpdateEvent processes a client.update event with coordination
func (c *clientEventCoordinator) ProcessClientUpdateEvent(ctx context.Context, req *ClientUpdateEventRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing coordinated client.update event", map[string]interface{}{
		"event_id":  req.EventID,
		"tenant_id": req.TenantID,
		"client_id": req.ClientID,
	})

	// Get enabled providers for client folder updates
	requiredProviders, optionalProviders, err := c.providerConfigService.GetEnabledProviders(ctx, req.TenantID, "client_folder_update")
	if err != nil {
		log.WithError(err).Error("Failed to get enabled providers for client.update")
		return fmt.Errorf("failed to get enabled providers: %w", err)
	}

	log.Info("Enabled providers for client.update", map[string]interface{}{
		"tenant_id":          req.TenantID,
		"required_providers": requiredProviders,
		"optional_providers": optionalProviders,
	})

	// Generate unique operation ID if not provided
	operationID := req.EventID
	if operationID == "" {
		operationID = fmt.Sprintf("client_%d_folder_update_%d", req.ClientID, time.Now().Unix())
	}

	// Initialize coordination for client.update
	initReq := &InitializeOperationRequest{
		TenantID:          req.TenantID,
		OperationType:     "client_folder_update",
		OperationID:       operationID,
		EntityType:        "client",
		EntityID:          req.ClientID,
		ParentEntityType:  "",
		ParentEntityID:    0,
		RequiredProviders: requiredProviders,
		OptionalProviders: optionalProviders,
		DownstreamAction:  "autodoc_rules",
		TimeoutDuration:   c.providerConfigService.GetOperationTimeout(ctx, req.TenantID, "client_folder_update"),
		Metadata:          req.EventData,
	}

	if err := c.operationCoordinator.InitializeOperation(ctx, initReq); err != nil {
		log.WithError(err).Error("Failed to initialize operation coordination for client.update")
		return fmt.Errorf("failed to initialize operation coordination: %w", err)
	}

	// Create completion callback
	completionCallback := func(ctx context.Context, operationID, provider string, success bool, err error) {
		status := model.ProviderStatusCompleted
		if !success {
			status = model.ProviderStatusFailed
		}

		completionReq := &ProviderCompletionRequest{
			OperationID: operationID,
			Provider:    provider,
			Status:      status,
			Error:       err,
		}

		if notifyErr := c.operationCoordinator.NotifyProviderCompletion(ctx, completionReq); notifyErr != nil {
			log.WithError(notifyErr).Error("Failed to notify provider completion for client.update")
		}
	}

	// Convert ClientUpdateEventRequest to ClientCreateEventRequest for provider operations
	// (providers handle both create and update similarly)
	createReq := &ClientCreateEventRequest{
		TenantID:  req.TenantID,
		ClientID:  req.ClientID,
		EventID:   req.EventID,
		EventData: req.EventData,
	}

	// Trigger provider operations for client.update (reuse create logic)
	if err := c.triggerProviderOperations(ctx, createReq, operationID, requiredProviders, completionCallback); err != nil {
		log.WithError(err).Error("Failed to trigger provider operations for client.update")
		return fmt.Errorf("failed to trigger provider operations: %w", err)
	}

	log.Info("Client update coordination initiated", map[string]interface{}{
		"operation_id":       operationID,
		"client_id":          req.ClientID,
		"required_providers": requiredProviders,
	})

	return nil
}

// triggerProviderOperations triggers operations for all required providers
func (c *clientEventCoordinator) triggerProviderOperations(ctx context.Context, req *ClientCreateEventRequest, operationID string, providers []string, callback ProviderOperationCallback) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Create payload JSON for provider operations
	payloadJSON, err := c.createProviderPayload(req)
	if err != nil {
		log.WithError(err).Error("Failed to create provider payload")
		return fmt.Errorf("failed to create provider payload: %w", err)
	}

	// Trigger operations for each provider
	for _, provider := range providers {
		operation, exists := c.providerOperations[provider]
		if !exists {
			log.Warn("Provider operation not registered", map[string]interface{}{
				"provider": provider,
			})
			continue
		}

		log.Info("Triggering operation for provider", map[string]interface{}{
			"provider":     provider,
			"operation_id": operationID,
		})

		// Create provider operation request
		providerReq := &ProviderOperationRequest{
			OperationID:   operationID,
			OperationType: "client_folder_creation",
			EntityType:    "client",
			EntityID:      req.ClientID,
			TenantID:      req.TenantID,
			PayloadJSON:   string(payloadJSON),
			Metadata:      req.EventData,
		}

		// Execute provider operation asynchronously
		go func(provider string, operation ProviderOperation, req *ProviderOperationRequest) {
			if err := operation.Execute(ctx, req); err != nil {
				log.WithError(err).Error("Provider operation failed", map[string]interface{}{
					"provider":     provider,
					"operation_id": operationID,
				})
				callback(ctx, operationID, provider, false, err)
			} else {
				log.Info("Provider operation completed successfully", map[string]interface{}{
					"provider":     provider,
					"operation_id": operationID,
				})
				callback(ctx, operationID, provider, true, nil)
			}
		}(provider, operation, providerReq)
	}

	return nil
}

// createProviderPayload creates payload for provider operations
func (c *clientEventCoordinator) createProviderPayload(req *ClientCreateEventRequest) ([]byte, error) {
	// Create payload similar to matter events but for client
	payload := map[string]interface{}{
		"topic": "client.create",
		"body": map[string]interface{}{
			"id":        req.ClientID,
			"tenant_id": req.TenantID,
		},
	}

	// Add additional data from event
	if req.EventData != nil {
		if body, ok := payload["body"].(map[string]interface{}); ok {
			for key, value := range req.EventData {
				body[key] = value
			}
		}
	}

	return json.Marshal(payload)
}
