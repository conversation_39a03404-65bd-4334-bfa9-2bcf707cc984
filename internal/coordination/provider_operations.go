package coordination

import (
	"context"
	"sync"

	"bilabl/docman/pkg/bilabllog"
)

// GDriveMatterEventConsumer interface for existing GDrive consumer
type GDriveMatterEventConsumer interface {
	HandleMatterCreated(ctx context.Context, payloadJSON string) error
}

// GDriveClientEventConsumer interface for existing GDrive client consumer
type GDriveClientEventConsumer interface {
	HandleClientCreated(ctx context.Context, payloadJSON string) error
	HandleClientUpdated(ctx context.Context, payloadJSON string) error
}

// SharePointMatterEventConsumer interface for existing SharePoint consumer
type SharePointMatterEventConsumer interface {
	HandleMatterCreated(ctx context.Context, payloadJSON string) error
}

// InternalMatterEventConsumer interface for existing Internal consumer
type InternalMatterEventConsumer interface {
	HandleMatterCreated(ctx context.Context, payloadJSON string) error
}

// GDriveProviderOperation wraps the existing GDrive consumer for matter events
type GDriveProviderOperation struct {
	consumer            GDriveMatterEventConsumer
	completionCallbacks map[string]ProviderOperationCallback
	mu                  sync.RWMutex
}

// NewGDriveProviderOperation creates a new GDrive provider operation for matter events
func NewGDriveProviderOperation(consumer GDriveMatterEventConsumer) ProviderOperation {
	return &GDriveProviderOperation{
		consumer:            consumer,
		completionCallbacks: make(map[string]ProviderOperationCallback),
	}
}

// GDriveClientProviderOperation wraps the existing GDrive client consumer
type GDriveClientProviderOperation struct {
	consumer            GDriveClientEventConsumer
	completionCallbacks map[string]ProviderOperationCallback
	mu                  sync.RWMutex
}

// NewGDriveClientProviderOperation creates a new GDrive provider operation for client events
func NewGDriveClientProviderOperation(consumer GDriveClientEventConsumer) ProviderOperation {
	return &GDriveClientProviderOperation{
		consumer:            consumer,
		completionCallbacks: make(map[string]ProviderOperationCallback),
	}
}

// Execute executes the GDrive folder creation operation for matter events
func (op *GDriveProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Executing GDrive matter folder creation operation", map[string]interface{}{
		"operation_id": req.OperationID,
		"entity_id":    req.EntityID,
		"entity_type":  req.EntityType,
		"tenant_id":    req.TenantID,
	})

	// Execute the existing consumer logic for matter events
	err := op.consumer.HandleMatterCreated(ctx, req.PayloadJSON)

	// Notify completion
	op.notifyCompletion(ctx, req.OperationID, "gdrive", err == nil, err)

	return err
}

// Execute executes the GDrive folder creation operation for client events
func (op *GDriveClientProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Executing GDrive client folder creation operation", map[string]interface{}{
		"operation_id":    req.OperationID,
		"entity_id":       req.EntityID,
		"entity_type":     req.EntityType,
		"operation_type":  req.OperationType,
		"tenant_id":       req.TenantID,
	})

	var err error

	// Determine which client handler to call based on operation type
	switch req.OperationType {
	case "client_folder_creation":
		log.Info("Calling HandleClientCreated for client folder creation")
		err = op.consumer.HandleClientCreated(ctx, req.PayloadJSON)
	case "client_folder_update":
		log.Info("Calling HandleClientUpdated for client folder update")
		err = op.consumer.HandleClientUpdated(ctx, req.PayloadJSON)
	default:
		log.WithField("operation_type", req.OperationType).Warn("Unknown client operation type, defaulting to HandleClientCreated")
		err = op.consumer.HandleClientCreated(ctx, req.PayloadJSON)
	}

	// Notify completion
	op.notifyCompletion(ctx, req.OperationID, "gdrive", err == nil, err)

	return err
}

// GetProviderName returns the provider name for matter operations
func (op *GDriveProviderOperation) GetProviderName() string {
	return "gdrive"
}

// GetProviderName returns the provider name for client operations
func (op *GDriveClientProviderOperation) GetProviderName() string {
	return "gdrive"
}

// SetCompletionCallback sets the completion callback for an operation (matter)
func (op *GDriveProviderOperation) SetCompletionCallback(operationID string, callback ProviderOperationCallback) {
	op.mu.Lock()
	defer op.mu.Unlock()
	op.completionCallbacks[operationID] = callback
}

// SetCompletionCallback sets the completion callback for an operation (client)
func (op *GDriveClientProviderOperation) SetCompletionCallback(operationID string, callback ProviderOperationCallback) {
	op.mu.Lock()
	defer op.mu.Unlock()
	op.completionCallbacks[operationID] = callback
}

// notifyCompletion notifies completion callback (matter)
func (op *GDriveProviderOperation) notifyCompletion(ctx context.Context, operationID, provider string, success bool, err error) {
	op.mu.RLock()
	callback, exists := op.completionCallbacks[operationID]
	op.mu.RUnlock()

	if exists && callback != nil {
		callback(ctx, operationID, provider, success, err)

		// Clean up callback after use
		op.mu.Lock()
		delete(op.completionCallbacks, operationID)
		op.mu.Unlock()
	}
}

// notifyCompletion notifies completion callback (client)
func (op *GDriveClientProviderOperation) notifyCompletion(ctx context.Context, operationID, provider string, success bool, err error) {
	op.mu.RLock()
	callback, exists := op.completionCallbacks[operationID]
	op.mu.RUnlock()

	if exists && callback != nil {
		callback(ctx, operationID, provider, success, err)

		// Clean up callback after use
		op.mu.Lock()
		delete(op.completionCallbacks, operationID)
		op.mu.Unlock()
	}
}

// SharePointProviderOperation wraps the existing SharePoint consumer
type SharePointProviderOperation struct {
	consumer            SharePointMatterEventConsumer
	completionCallbacks map[string]ProviderOperationCallback
	mu                  sync.RWMutex
}

// NewSharePointProviderOperation creates a new SharePoint provider operation
func NewSharePointProviderOperation(consumer SharePointMatterEventConsumer) ProviderOperation {
	return &SharePointProviderOperation{
		consumer:            consumer,
		completionCallbacks: make(map[string]ProviderOperationCallback),
	}
}

// Execute executes the SharePoint folder creation operation
func (op *SharePointProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Executing SharePoint folder creation operation", map[string]interface{}{
		"operation_id": req.OperationID,
		"entity_id":    req.EntityID,
		"tenant_id":    req.TenantID,
	})

	// Execute the existing consumer logic
	err := op.consumer.HandleMatterCreated(ctx, req.PayloadJSON)

	// Notify completion
	op.notifyCompletion(ctx, req.OperationID, "sharepoint", err == nil, err)

	return err
}

// GetProviderName returns the provider name
func (op *SharePointProviderOperation) GetProviderName() string {
	return "sharepoint"
}

// SetCompletionCallback sets the completion callback for an operation
func (op *SharePointProviderOperation) SetCompletionCallback(operationID string, callback ProviderOperationCallback) {
	op.mu.Lock()
	defer op.mu.Unlock()
	op.completionCallbacks[operationID] = callback
}

// notifyCompletion notifies completion callback
func (op *SharePointProviderOperation) notifyCompletion(ctx context.Context, operationID, provider string, success bool, err error) {
	op.mu.RLock()
	callback, exists := op.completionCallbacks[operationID]
	op.mu.RUnlock()

	if exists && callback != nil {
		callback(ctx, operationID, provider, success, err)

		// Clean up callback after use
		op.mu.Lock()
		delete(op.completionCallbacks, operationID)
		op.mu.Unlock()
	}
}

// InternalProviderOperation wraps the existing Internal consumer
type InternalProviderOperation struct {
	consumer            InternalMatterEventConsumer
	completionCallbacks map[string]ProviderOperationCallback
	mu                  sync.RWMutex
}

// NewInternalProviderOperation creates a new Internal provider operation
func NewInternalProviderOperation(consumer InternalMatterEventConsumer) ProviderOperation {
	return &InternalProviderOperation{
		consumer:            consumer,
		completionCallbacks: make(map[string]ProviderOperationCallback),
	}
}

// Execute executes the Internal folder creation operation
func (op *InternalProviderOperation) Execute(ctx context.Context, req *ProviderOperationRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Executing Internal folder creation operation", map[string]interface{}{
		"operation_id": req.OperationID,
		"entity_id":    req.EntityID,
		"tenant_id":    req.TenantID,
	})

	// Execute the existing consumer logic
	var err error
	if op.consumer != nil {
		err = op.consumer.HandleMatterCreated(ctx, req.PayloadJSON)
	} else {
		log.Warn("Internal consumer not configured - skipping internal folder creation")
		// For now, consider this as successful completion
		err = nil
	}

	// Notify completion
	op.notifyCompletion(ctx, req.OperationID, "internal", err == nil, err)

	return err
}

// GetProviderName returns the provider name
func (op *InternalProviderOperation) GetProviderName() string {
	return "internal"
}

// SetCompletionCallback sets the completion callback for an operation
func (op *InternalProviderOperation) SetCompletionCallback(operationID string, callback ProviderOperationCallback) {
	op.mu.Lock()
	defer op.mu.Unlock()
	op.completionCallbacks[operationID] = callback
}

// notifyCompletion notifies completion callback
func (op *InternalProviderOperation) notifyCompletion(ctx context.Context, operationID, provider string, success bool, err error) {
	op.mu.RLock()
	callback, exists := op.completionCallbacks[operationID]
	op.mu.RUnlock()

	if exists && callback != nil {
		callback(ctx, operationID, provider, success, err)

		// Clean up callback after use
		op.mu.Lock()
		delete(op.completionCallbacks, operationID)
		op.mu.Unlock()
	}
}
