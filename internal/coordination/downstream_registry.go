package coordination

import (
	"context"
	"fmt"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// DownstreamActionHandler handles downstream actions after coordination completes
type DownstreamActionHandler interface {
	// HandleAction handles downstream action after coordination completes
	HandleAction(ctx context.Context, status *model.OperationCoordinationStatus) error

	// GetActionType returns the action type this handler supports
	GetActionType() string

	// CanExecute validates if action can be executed
	CanExecute(ctx context.Context, status *model.OperationCoordinationStatus) bool
}

// downstreamActionRegistry implements DownstreamActionRegistry
type downstreamActionRegistry struct {
	handlers map[string]DownstreamActionHandler
}

// NewDownstreamActionRegistry creates a new downstream action registry
func NewDownstreamActionRegistry() DownstreamActionRegistry {
	return &downstreamActionRegistry{
		handlers: make(map[string]DownstreamActionHandler),
	}
}

// RegisterHandler registers a handler for a specific action type
func (r *downstreamActionRegistry) RegisterHandler(actionType string, handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {
	if actionType == "" {
		return
	}
	if handler == nil {
		return
	}

	r.handlers[actionType] = handler
}

// ExecuteAction executes downstream action
func (r *downstreamActionRegistry) ExecuteAction(ctx context.Context, actionType string, status *model.OperationCoordinationStatus) error {
	log := bilabllog.CreateContextLogger(ctx)

	if actionType == "" {
		log.Warn("Empty action type provided")
		return nil // No action to execute
	}

	handler, exists := r.handlers[actionType]
	if !exists {
		log.WithField("action_type", actionType).Warn("No handler registered for action type")
		return fmt.Errorf("no handler registered for action type: %s", actionType)
	}

	// Check if handler can execute
	if !handler.CanExecute(ctx, status) {
		log.WithField("action_type", actionType).Warn("Handler cannot execute action")
		return fmt.Errorf("handler cannot execute action type: %s", actionType)
	}

	log.WithField("action_type", actionType).Info("Executing downstream action")

	// Execute the action
	err := handler.HandleAction(ctx, status)
	if err != nil {
		log.WithError(err).WithField("action_type", actionType).Error("Failed to execute downstream action")
		return fmt.Errorf("failed to execute downstream action %s: %w", actionType, err)
	}

	log.WithField("action_type", actionType).Info("Successfully executed downstream action")
	return nil
}

// ListHandlers lists registered handlers
func (r *downstreamActionRegistry) ListHandlers() map[string]DownstreamActionHandler {
	// Return a copy to prevent external modification
	handlers := make(map[string]DownstreamActionHandler)
	for actionType, handler := range r.handlers {
		handlers[actionType] = handler
	}
	return handlers
}

// HasHandler checks if handler exists for action type
func (r *downstreamActionRegistry) HasHandler(actionType string) bool {
	_, exists := r.handlers[actionType]
	return exists
}

// NoOpDownstreamHandler is a no-op handler for testing or placeholder purposes
type NoOpDownstreamHandler struct {
	actionType string
}

// NewNoOpDownstreamHandler creates a new no-op downstream handler
func NewNoOpDownstreamHandler(actionType string) DownstreamActionHandler {
	return &NoOpDownstreamHandler{
		actionType: actionType,
	}
}

// HandleAction does nothing (no-op)
func (h *NoOpDownstreamHandler) HandleAction(ctx context.Context, status *model.OperationCoordinationStatus) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("action_type", h.actionType).Info("No-op downstream action executed")
	return nil
}

// GetActionType returns the action type
func (h *NoOpDownstreamHandler) GetActionType() string {
	return h.actionType
}

// CanExecute always returns true for no-op handler
func (h *NoOpDownstreamHandler) CanExecute(ctx context.Context, status *model.OperationCoordinationStatus) bool {
	return true
}
