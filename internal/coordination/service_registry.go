package coordination

import (
	"context"
	"fmt"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"
)

// EventRuleMatchingService interface for autodoc rule processing
type EventRuleMatchingService interface {
	ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error
}

// CoordinationServices holds all coordination-related services
type CoordinationServices struct {
	// Core coordination services
	OperationCoordinator   OperationCoordinator
	ProviderConfigService  ProviderConfigService
	DownstreamRegistry     DownstreamActionRegistry
	MatterEventCoordinator MatterEventCoordinator
	ClientEventCoordinator ClientEventCoordinator

	// Provider operations
	GDriveOperation     ProviderOperation
	SharePointOperation ProviderOperation
	InternalOperation   ProviderOperation

	// Repository
	StatusRepository model.OperationCoordinationStatusRepository
}

// CoordinationServiceConfig holds configuration for coordination services
type CoordinationServiceConfig struct {
	// Repositories
	StatusRepository model.OperationCoordinationStatusRepository
	DocSettingRepo   repositories.DocumentSettingRepository

	// Existing consumers
	GDriveConsumer     GDriveMatterEventConsumer
	SharePointConsumer SharePointMatterEventConsumer
	InternalConsumer   InternalMatterEventConsumer

	// Autodoc service
	EventRuleMatchingService EventRuleMatchingService

	// Optional: Pre-built downstream handlers
	AutodocHandler DownstreamActionHandler
}

// NewCoordinationServices creates and wires all coordination services
func NewCoordinationServices(config *CoordinationServiceConfig) *CoordinationServices {
	// Create provider configuration service
	providerConfigService := NewProviderConfigService(config.DocSettingRepo)

	// Create downstream action registry
	downstreamRegistry := NewDownstreamActionRegistry()

	// Register autodoc rules handler if provided
	if config.AutodocHandler != nil {
		downstreamRegistry.RegisterHandler(config.AutodocHandler.GetActionType(), config.AutodocHandler)
	}

	// Create operation coordinator
	operationCoordinator := NewOperationCoordinator(config.StatusRepository, downstreamRegistry)

	// Create provider operations
	gdriveOperation := NewGDriveProviderOperation(config.GDriveConsumer)
	sharepointOperation := NewSharePointProviderOperation(config.SharePointConsumer)
	internalOperation := NewInternalProviderOperation(config.InternalConsumer)

	// Create matter event coordinator
	matterEventCoordinator := NewMatterEventCoordinator(operationCoordinator, providerConfigService)

	// Register provider operations with matter event coordinator
	matterEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
	matterEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
	matterEventCoordinator.RegisterProviderOperation("internal", internalOperation)

	// Create client event coordinator
	clientEventCoordinator := NewClientEventCoordinator(operationCoordinator, providerConfigService)

	// Register provider operations with client event coordinator
	clientEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
	clientEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
	clientEventCoordinator.RegisterProviderOperation("internal", internalOperation)

	return &CoordinationServices{
		OperationCoordinator:   operationCoordinator,
		ProviderConfigService:  providerConfigService,
		DownstreamRegistry:     downstreamRegistry,
		MatterEventCoordinator: matterEventCoordinator,
		ClientEventCoordinator: clientEventCoordinator,
		GDriveOperation:        gdriveOperation,
		SharePointOperation:    sharepointOperation,
		InternalOperation:      internalOperation,
		StatusRepository:       config.StatusRepository,
	}
}

// CoordinationServiceBuilder helps build coordination services step by step
type CoordinationServiceBuilder struct {
	config *CoordinationServiceConfig
}

// NewCoordinationServiceBuilder creates a new service builder
func NewCoordinationServiceBuilder() *CoordinationServiceBuilder {
	return &CoordinationServiceBuilder{
		config: &CoordinationServiceConfig{},
	}
}

// WithStatusRepository sets the status repository
func (b *CoordinationServiceBuilder) WithStatusRepository(repo model.OperationCoordinationStatusRepository) *CoordinationServiceBuilder {
	b.config.StatusRepository = repo
	return b
}

// WithDocSettingRepository sets the document setting repository
func (b *CoordinationServiceBuilder) WithDocSettingRepository(repo repositories.DocumentSettingRepository) *CoordinationServiceBuilder {
	b.config.DocSettingRepo = repo
	return b
}

// WithGDriveConsumer sets the GDrive consumer
func (b *CoordinationServiceBuilder) WithGDriveConsumer(consumer GDriveMatterEventConsumer) *CoordinationServiceBuilder {
	b.config.GDriveConsumer = consumer
	return b
}

// WithSharePointConsumer sets the SharePoint consumer
func (b *CoordinationServiceBuilder) WithSharePointConsumer(consumer SharePointMatterEventConsumer) *CoordinationServiceBuilder {
	b.config.SharePointConsumer = consumer
	return b
}

// WithInternalConsumer sets the Internal consumer
func (b *CoordinationServiceBuilder) WithInternalConsumer(consumer InternalMatterEventConsumer) *CoordinationServiceBuilder {
	b.config.InternalConsumer = consumer
	return b
}

// WithEventRuleMatchingService sets the event rule matching service
func (b *CoordinationServiceBuilder) WithEventRuleMatchingService(service EventRuleMatchingService) *CoordinationServiceBuilder {
	b.config.EventRuleMatchingService = service
	return b
}

// WithAutodocHandler sets the pre-built autodoc handler
func (b *CoordinationServiceBuilder) WithAutodocHandler(handler DownstreamActionHandler) *CoordinationServiceBuilder {
	b.config.AutodocHandler = handler
	return b
}

// Build creates the coordination services
func (b *CoordinationServiceBuilder) Build() *CoordinationServices {
	return NewCoordinationServices(b.config)
}

// ValidateConfig validates the service configuration
func (b *CoordinationServiceBuilder) ValidateConfig() error {
	if b.config.StatusRepository == nil {
		return fmt.Errorf("status repository is required")
	}
	if b.config.DocSettingRepo == nil {
		return fmt.Errorf("document setting repository is required")
	}
	if b.config.EventRuleMatchingService == nil {
		return fmt.Errorf("event rule matching service is required")
	}
	// Note: Provider consumers are optional - they may not be available for all tenants
	return nil
}
