package integration

import (
	"context"
	"fmt"
	"time"

	"bilabl/docman/internal/coordination"
	"bilabl/docman/internal/handlers"
	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/bilabllog"
)

// CoordinatedEventRuleMatchingService wraps the original EventRuleMatchingService
// to coordinate folder creation before executing autodoc rules
type CoordinatedEventRuleMatchingService struct {
	// Original services
	originalService     autodoc.EventRuleMatchingService
	matterEventConsumer autodoc.MatterEventConsumer
	clientEventConsumer autodoc.ClientEventConsumer

	// Coordination services
	matterEventCoordinator coordination.MatterEventCoordinator
	clientEventCoordinator coordination.ClientEventCoordinator
}

// NewCoordinatedEventRuleMatchingService creates a new coordinated event rule matching service
func NewCoordinatedEventRuleMatchingService(
	originalService autodoc.EventRuleMatchingService,
	matterEventConsumer autodoc.MatterEventConsumer,
	clientEventConsumer autodoc.ClientEventConsumer,
	matterEventCoordinator coordination.MatterEventCoordinator,
	clientEventCoordinator coordination.ClientEventCoordinator,
) autodoc.EventRuleMatchingService {
	return &CoordinatedEventRuleMatchingService{
		originalService:        originalService,
		matterEventConsumer:    matterEventConsumer,
		clientEventConsumer:    clientEventConsumer,
		matterEventCoordinator: matterEventCoordinator,
		clientEventCoordinator: clientEventCoordinator,
	}
}

// ProcessEvent processes any event type with coordination support
func (c *CoordinatedEventRuleMatchingService) ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Info("Processing event with coordination")

	// Use coordinated processing for events that require folder operations
	switch eventType {
	case "matter.create":
		log.Info("Using coordinated processing for matter.create event")
		return c.processCoordinatedMatterCreate(ctx, tenantID, eventData)
	case "matter.update":
		log.Info("Using coordinated processing for matter.update event")
		return c.processCoordinatedMatterUpdate(ctx, tenantID, eventData)
	case "client.create":
		log.Info("Using coordinated processing for client.create event")
		return c.processCoordinatedClientCreate(ctx, tenantID, eventData)
	case "client.update":
		log.Info("Using coordinated processing for client.update event")
		return c.processCoordinatedClientUpdate(ctx, tenantID, eventData)
	default:
		// For other events, use original service directly
		log.WithField("event_type", eventType).Debug("Using original processing for non-coordinated event")
		return c.originalService.ProcessEvent(ctx, tenantID, eventType, eventData)
	}
}

// ProcessMatterEvent specifically handles matter events with coordination
func (c *CoordinatedEventRuleMatchingService) ProcessMatterEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Debug("Processing matter event with coordination")

	// Use coordinated processing for matter.create events
	if eventType == "matter.create" {
		log.Info("Using coordinated processing for matter.create event")
		return c.processCoordinatedMatterCreate(ctx, tenantID, eventData)
	}

	// Fall back to original processing
	log.Info("Using original matter event processing")
	return c.originalService.ProcessMatterEvent(ctx, tenantID, eventType, eventData)
}

// ProcessClientEvent delegates to original service (no coordination needed for client events)
func (c *CoordinatedEventRuleMatchingService) ProcessClientEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	return c.originalService.ProcessClientEvent(ctx, tenantID, eventType, eventData)
}

// processCoordinatedMatterCreate handles matter.create events with folder creation coordination
func (c *CoordinatedEventRuleMatchingService) processCoordinatedMatterCreate(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Extract matter information from event data
	matterID, ok := eventData["id"].(float64) // JSON unmarshaling converts numbers to float64
	if !ok {
		if matterIDInt, ok := eventData["id"].(uint64); ok {
			matterID = float64(matterIDInt)
		} else {
			return fmt.Errorf("invalid or missing matter ID in event data")
		}
	}

	clientID, ok := eventData["client_id"].(float64)
	if !ok {
		if clientIDInt, ok := eventData["client_id"].(uint64); ok {
			clientID = float64(clientIDInt)
		} else {
			return fmt.Errorf("invalid or missing client ID in event data")
		}
	}

	// Generate unique event ID for coordination
	eventID := fmt.Sprintf("matter_%d_create_%d", uint64(matterID), time.Now().Unix())

	log.Info("Initiating coordinated matter.create processing", map[string]interface{}{
		"event_id":  eventID,
		"matter_id": uint64(matterID),
		"client_id": uint64(clientID),
		"tenant_id": tenantID,
	})

	// Create coordination request
	req := &coordination.MatterCreateEventRequest{
		TenantID:  tenantID,
		MatterID:  uint64(matterID),
		ClientID:  uint64(clientID),
		EventID:   eventID,
		EventData: eventData,
	}

	// Process with coordination - this will:
	// 1. Initialize coordination tracking
	// 2. Trigger folder creation for all enabled providers
	// 3. Wait for all providers to complete
	// 4. Trigger autodoc rules only after all folders are created
	return c.matterEventCoordinator.ProcessMatterCreateEvent(ctx, req)
}

// processCoordinatedMatterUpdate handles matter.update events with coordination
func (c *CoordinatedEventRuleMatchingService) processCoordinatedMatterUpdate(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Initiating coordinated matter.update processing")

	// Extract matter data
	matterID, ok := eventData["id"].(float64)
	if !ok {
		return fmt.Errorf("invalid matter ID in event data")
	}

	clientID, ok := eventData["client_id"].(float64)
	if !ok {
		return fmt.Errorf("invalid client ID in event data")
	}

	// Generate event ID for tracking
	eventID := fmt.Sprintf("matter_update_%d_%d", uint64(matterID), time.Now().Unix())

	// Create request for matter update coordination
	req := &coordination.MatterUpdateEventRequest{
		TenantID:  tenantID,
		MatterID:  uint64(matterID),
		ClientID:  uint64(clientID),
		EventID:   eventID,
		EventData: eventData,
	}

	// Process with coordination for matter.update
	return c.matterEventCoordinator.ProcessMatterUpdateEvent(ctx, req)
}

// processCoordinatedClientCreate handles client.create events with coordination
func (c *CoordinatedEventRuleMatchingService) processCoordinatedClientCreate(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Initiating coordinated client.create processing")

	// Extract client data
	clientID, ok := eventData["id"].(float64)
	if !ok {
		return fmt.Errorf("invalid client ID in event data")
	}

	// Generate event ID for tracking
	eventID := fmt.Sprintf("client_create_%d_%d", uint64(clientID), time.Now().Unix())

	// Create request for client create coordination
	req := &coordination.ClientCreateEventRequest{
		TenantID:  tenantID,
		ClientID:  uint64(clientID),
		EventID:   eventID,
		EventData: eventData,
	}

	// Process with coordination for client.create
	return c.clientEventCoordinator.ProcessClientCreateEvent(ctx, req)
}

// processCoordinatedClientUpdate handles client.update events with coordination
func (c *CoordinatedEventRuleMatchingService) processCoordinatedClientUpdate(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Initiating coordinated client.update processing")

	// Extract client data
	clientID, ok := eventData["id"].(float64)
	if !ok {
		return fmt.Errorf("invalid client ID in event data")
	}

	// Generate event ID for tracking
	eventID := fmt.Sprintf("client_update_%d_%d", uint64(clientID), time.Now().Unix())

	// Create request for client update coordination
	req := &coordination.ClientUpdateEventRequest{
		TenantID:  tenantID,
		ClientID:  uint64(clientID),
		EventID:   eventID,
		EventData: eventData,
	}

	// Process with coordination for client.update
	return c.clientEventCoordinator.ProcessClientUpdateEvent(ctx, req)
}

// CoordinationIntegrationConfig holds configuration for coordination integration
type CoordinationIntegrationConfig struct {

	// Original services
	OriginalEventRuleMatchingService autodoc.EventRuleMatchingService
	MatterEventConsumer              autodoc.MatterEventConsumer
	ClientEventConsumer              autodoc.ClientEventConsumer

	// Coordination services
	CoordinationServices *coordination.CoordinationServices

	// Provider consumers for coordination (matter events)
	GDriveConsumer     *gdrive.MatterEventConsumer
	SharePointConsumer coordination.SharePointMatterEventConsumer
	InternalConsumer   coordination.InternalMatterEventConsumer

	// Provider consumers for client events
	GDriveClientConsumer     *gdrive.ClientEventConsumer
	SharePointClientConsumer coordination.SharePointMatterEventConsumer
	InternalClientConsumer   coordination.InternalMatterEventConsumer
}

// SetupCoordinationIntegration sets up the coordination integration
func SetupCoordinationIntegration(config *CoordinationIntegrationConfig) (autodoc.EventRuleMatchingService, error) {
	// Create autodoc handler
	autodocHandler := handlers.NewAutodocRulesHandler(config.OriginalEventRuleMatchingService)

	// Register autodoc handler with downstream registry
	config.CoordinationServices.DownstreamRegistry.RegisterHandler("autodoc_rules", autodocHandler)

	// Register provider operations with matter event coordinator
	if config.GDriveConsumer != nil {
		gdriveOperation := coordination.NewGDriveProviderOperation(config.GDriveConsumer)
		config.CoordinationServices.MatterEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
	}

	if config.SharePointConsumer != nil {
		sharepointOperation := coordination.NewSharePointProviderOperation(config.SharePointConsumer)
		config.CoordinationServices.MatterEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
	}

	if config.InternalConsumer != nil {
		internalOperation := coordination.NewInternalProviderOperation(config.InternalConsumer)
		config.CoordinationServices.MatterEventCoordinator.RegisterProviderOperation("internal", internalOperation)
	}

	// Register provider operations with client event coordinator
	// (reuse matter consumers for client operations for now)
	if config.GDriveClientConsumer != nil {
		// Use client-specific provider operation for client events
		gdriveClientOperation := coordination.NewGDriveClientProviderOperation(config.GDriveClientConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("gdrive", gdriveClientOperation)
	} else if config.GDriveConsumer != nil {
		// Fallback to matter consumer if client consumer not provided (not recommended)
		gdriveOperation := coordination.NewGDriveProviderOperation(config.GDriveConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("gdrive", gdriveOperation)
	}

	if config.SharePointClientConsumer != nil {
		sharepointClientOperation := coordination.NewSharePointProviderOperation(config.SharePointClientConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("sharepoint", sharepointClientOperation)
	} else if config.SharePointConsumer != nil {
		// Fallback to matter consumer if client consumer not provided
		sharepointOperation := coordination.NewSharePointProviderOperation(config.SharePointConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("sharepoint", sharepointOperation)
	}

	if config.InternalClientConsumer != nil {
		internalClientOperation := coordination.NewInternalProviderOperation(config.InternalClientConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("internal", internalClientOperation)
	} else if config.InternalConsumer != nil {
		// Fallback to matter consumer if client consumer not provided
		internalOperation := coordination.NewInternalProviderOperation(config.InternalConsumer)
		config.CoordinationServices.ClientEventCoordinator.RegisterProviderOperation("internal", internalOperation)
	}

	// Create coordinated event rule matching service
	coordinatedService := NewCoordinatedEventRuleMatchingService(
		config.OriginalEventRuleMatchingService,
		config.MatterEventConsumer,
		config.ClientEventConsumer,
		config.CoordinationServices.MatterEventCoordinator,
		config.CoordinationServices.ClientEventCoordinator,
	)

	return coordinatedService, nil
}

// EnableCoordinationForTenant enables coordination for a specific tenant (future feature)
func EnableCoordinationForTenant(tenantID uint64) {
	// TODO: Implement tenant-specific coordination enabling
	// This could be stored in database or configuration
}

// DisableCoordinationForTenant disables coordination for a specific tenant (future feature)
func DisableCoordinationForTenant(tenantID uint64) {
	// TODO: Implement tenant-specific coordination disabling
}

// IsCoordinationEnabledForTenant checks if coordination is enabled for a tenant (future feature)
func IsCoordinationEnabledForTenant(tenantID uint64) bool {
	// TODO: Implement tenant-specific coordination check
	// For now, return global setting
	return true
}
