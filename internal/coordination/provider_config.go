package coordination

import (
	"context"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// ProviderConfigService provides provider configuration for coordination
type ProviderConfigService interface {
	// GetEnabledProviders returns enabled providers for tenant and operation type
	GetEnabledProviders(ctx context.Context, tenantID uint64, operationType string) (required []string, optional []string, err error)

	// GetOperationTimeout returns timeout for operation type
	GetOperationTimeout(ctx context.Context, tenantID uint64, operationType string) time.Duration

	// GetCoordinationConfig returns full coordination config
	GetCoordinationConfig(ctx context.Context, tenantID uint64, operationType string) (*CoordinationConfig, error)
}

// CoordinationConfig represents coordination configuration for a tenant
type CoordinationConfig struct {
	RequiredProviders []string      `json:"required_providers"`
	OptionalProviders []string      `json:"optional_providers"`
	OperationTimeout  time.Duration `json:"operation_timeout"`
	RetryAttempts     int           `json:"retry_attempts"`
	RetryDelay        time.Duration `json:"retry_delay"`
	FailureStrategy   string        `json:"failure_strategy"` // "fail_fast", "continue_on_optional_failure"
}

// providerConfigService implements ProviderConfigService
type providerConfigService struct {
	docSettingRepo repositories.DocumentSettingRepository
}

// NewProviderConfigService creates a new provider configuration service
func NewProviderConfigService(docSettingRepo repositories.DocumentSettingRepository) ProviderConfigService {
	return &providerConfigService{
		docSettingRepo: docSettingRepo,
	}
}

// GetEnabledProviders returns enabled providers for tenant and operation type
func (s *providerConfigService) GetEnabledProviders(ctx context.Context, tenantID uint64, operationType string) (required []string, optional []string, err error) {
	log := logger.WithCtx(ctx, "[ProviderConfigService][GetEnabledProviders]")

	var requiredProviders []string
	var optionalProviders []string

	switch operationType {
	case "folder_creation":
		// Internal provider is always required for folder creation
		requiredProviders = append(requiredProviders, "internal")

		// Check Google Drive configuration
		if enabled, err := s.isGDriveEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "gdrive")
		} else if err != nil {
			log.WithError(err).Warn("Failed to check Google Drive status, assuming disabled")
		}

		// Check SharePoint configuration
		if enabled, err := s.isSharePointEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "sharepoint")
		} else if err != nil {
			log.WithError(err).Warn("Failed to check SharePoint status, assuming disabled")
		}

	case "client_folder_creation", "client_folder_update":
		// For client operations, external providers handle folder creation
		// AutoDoc rules execute AFTER folder creation completes

		// Check Google Drive configuration
		if enabled, err := s.isGDriveEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "gdrive")
		} else if err != nil {
			log.WithError(err).Warn("Failed to check Google Drive status, assuming disabled")
		}

		// Check SharePoint configuration
		if enabled, err := s.isSharePointEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "sharepoint")
		} else if err != nil {
			log.WithError(err).Warn("Failed to check SharePoint status, assuming disabled")
		}

		// If no external providers are enabled, use internal as fallback
		// This ensures coordination system works even without external providers
		if len(requiredProviders) == 0 {
			log.Info("No external providers enabled for client operations, using internal as fallback")
			requiredProviders = append(requiredProviders, "internal")
		}

		// Note: AutoDoc rules execute as downstream action AFTER folder creation

	case "permission_sync":
		// For permission sync, only external providers are relevant
		if enabled, err := s.isGDriveEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "gdrive")
		}

		if enabled, err := s.isSharePointEnabled(ctx, tenantID); err == nil && enabled {
			requiredProviders = append(requiredProviders, "sharepoint")
		}

	default:
		// For unknown operation types, default to internal only
		requiredProviders = append(requiredProviders, "internal")
	}

	log.Infof("Enabled providers for tenant %d, operation %s: required=%v, optional=%v",
		tenantID, operationType, requiredProviders, optionalProviders)

	return requiredProviders, optionalProviders, nil
}

// GetOperationTimeout returns timeout for operation type
func (s *providerConfigService) GetOperationTimeout(ctx context.Context, tenantID uint64, operationType string) time.Duration {
	switch operationType {
	case "folder_creation":
		// Folder creation can take longer due to multiple providers
		return 5 * time.Minute
	case "permission_sync":
		// Permission sync is usually faster
		return 2 * time.Minute
	default:
		// Default timeout
		return 3 * time.Minute
	}
}

// GetCoordinationConfig returns full coordination config
func (s *providerConfigService) GetCoordinationConfig(ctx context.Context, tenantID uint64, operationType string) (*CoordinationConfig, error) {
	requiredProviders, optionalProviders, err := s.GetEnabledProviders(ctx, tenantID, operationType)
	if err != nil {
		return nil, err
	}

	return &CoordinationConfig{
		RequiredProviders: requiredProviders,
		OptionalProviders: optionalProviders,
		OperationTimeout:  s.GetOperationTimeout(ctx, tenantID, operationType),
		RetryAttempts:     3,
		RetryDelay:        30 * time.Second,
		FailureStrategy:   "fail_fast", // Fail if any required provider fails
	}, nil
}

// isGDriveEnabled checks if Google Drive is enabled for a tenant
func (s *providerConfigService) isGDriveEnabled(ctx context.Context, tenantID uint64) (bool, error) {
	setting, err := s.docSettingRepo.GetValueByKey(ctx, tenantID, "gdrive_config")
	if err != nil {
		if model.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}

	config, err := setting.GetGDriveConfig()
	if err != nil {
		return false, err
	}

	if config == nil {
		return false, nil
	}

	return config.Enabled, nil
}

// isSharePointEnabled checks if SharePoint is enabled for a tenant
func (s *providerConfigService) isSharePointEnabled(ctx context.Context, tenantID uint64) (bool, error) {
	setting, err := s.docSettingRepo.GetValueByKey(ctx, tenantID, "sharepoint_config")
	if err != nil {
		if model.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}

	var config model.SharePointConfig
	if err := config.FromJSON(setting.Value); err != nil {
		return false, err
	}

	return config.Enabled, nil
}
