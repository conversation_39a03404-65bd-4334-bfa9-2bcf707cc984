package coordination

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// MatterEventCoordinator coordinates matter.create events across all providers
type MatterEventCoordinator interface {
	// ProcessMatterCreateEvent processes a matter.create event with coordination
	ProcessMatterCreateEvent(ctx context.Context, req *MatterCreateEventRequest) error

	// ProcessMatterUpdateEvent processes a matter.update event with coordination
	ProcessMatterUpdateEvent(ctx context.Context, req *MatterUpdateEventRequest) error

	// NotifyFolderCreationComplete notifies that folder creation is complete for a provider
	NotifyFolderCreationComplete(ctx context.Context, req *FolderCreationCompleteRequest) error

	// RegisterProviderOperation registers a provider operation
	RegisterProviderOperation(provider string, operation ProviderOperation)
}

// MatterCreateEventRequest represents a matter.create event request
type MatterCreateEventRequest struct {
	TenantID  uint64
	MatterID  uint64
	ClientID  uint64
	EventID   string
	EventData map[string]interface{}
}

// MatterUpdateEventRequest represents a matter.update event request
type MatterUpdateEventRequest struct {
	TenantID  uint64
	MatterID  uint64
	ClientID  uint64
	EventID   string
	EventData map[string]interface{}
}

// FolderCreationCompleteRequest represents a folder creation completion notification
type FolderCreationCompleteRequest struct {
	EventID  string
	Provider string
	Success  bool
	Error    error
}

// ProviderOperationCallback represents a callback function for provider operations
type ProviderOperationCallback func(ctx context.Context, operationID, provider string, success bool, err error)

// ProviderOperation represents a provider-specific operation
type ProviderOperation interface {
	// Execute the operation
	Execute(ctx context.Context, req *ProviderOperationRequest) error

	// GetProviderName returns the provider name
	GetProviderName() string

	// SetCompletionCallback sets the completion callback
	SetCompletionCallback(operationID string, callback ProviderOperationCallback)
}

// ProviderOperationRequest represents a request for provider operation
type ProviderOperationRequest struct {
	OperationID   string
	OperationType string
	EntityType    string
	EntityID      uint64
	TenantID      uint64
	PayloadJSON   string
	Metadata      map[string]interface{}
}

// matterEventCoordinator implements MatterEventCoordinator
type matterEventCoordinator struct {
	operationCoordinator  OperationCoordinator
	providerConfigService ProviderConfigService

	// Provider operations
	providerOperations map[string]ProviderOperation
}

// NewMatterEventCoordinator creates a new matter event coordinator
func NewMatterEventCoordinator(
	operationCoordinator OperationCoordinator,
	providerConfigService ProviderConfigService,
) MatterEventCoordinator {
	return &matterEventCoordinator{
		operationCoordinator:  operationCoordinator,
		providerConfigService: providerConfigService,
		providerOperations:    make(map[string]ProviderOperation),
	}
}

// RegisterProviderOperation registers a provider operation
func (c *matterEventCoordinator) RegisterProviderOperation(provider string, operation ProviderOperation) {
	c.providerOperations[provider] = operation
}

// ProcessMatterCreateEvent processes a matter.create event with coordination
func (c *matterEventCoordinator) ProcessMatterCreateEvent(ctx context.Context, req *MatterCreateEventRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing coordinated matter.create event", map[string]interface{}{
		"event_id":  req.EventID,
		"tenant_id": req.TenantID,
		"matter_id": req.MatterID,
		"client_id": req.ClientID,
	})

	// Get enabled providers for folder creation
	requiredProviders, optionalProviders, err := c.providerConfigService.GetEnabledProviders(ctx, req.TenantID, "folder_creation")
	if err != nil {
		log.WithError(err).Error("Failed to get enabled providers")
		return fmt.Errorf("failed to get enabled providers: %w", err)
	}

	log.Info("Enabled providers for tenant", map[string]interface{}{
		"tenant_id":          req.TenantID,
		"required_providers": requiredProviders,
		"optional_providers": optionalProviders,
	})

	// Generate unique operation ID if not provided
	operationID := req.EventID
	if operationID == "" {
		operationID = fmt.Sprintf("matter_%d_folder_creation_%d", req.MatterID, time.Now().Unix())
	}

	// Initialize coordination
	initReq := &InitializeOperationRequest{
		TenantID:          req.TenantID,
		OperationType:     "matter_folder_creation",
		OperationID:       operationID,
		EntityType:        "matter",
		EntityID:          req.MatterID,
		ParentEntityType:  "client",
		ParentEntityID:    req.ClientID,
		RequiredProviders: requiredProviders,
		OptionalProviders: optionalProviders,
		DownstreamAction:  "autodoc_rules",
		TimeoutDuration:   c.providerConfigService.GetOperationTimeout(ctx, req.TenantID, "matter_folder_creation"),
		Metadata:          req.EventData,
	}

	if err := c.operationCoordinator.InitializeOperation(ctx, initReq); err != nil {
		log.WithError(err).Error("Failed to initialize operation coordination")
		return fmt.Errorf("failed to initialize operation coordination: %w", err)
	}

	// Create completion callback
	completionCallback := func(ctx context.Context, operationID, provider string, success bool, err error) {
		status := model.ProviderStatusCompleted
		if !success {
			status = model.ProviderStatusFailed
		}

		completionReq := &ProviderCompletionRequest{
			OperationID: operationID,
			Provider:    provider,
			Status:      status,
			Error:       err,
		}

		if notifyErr := c.operationCoordinator.NotifyProviderCompletion(ctx, completionReq); notifyErr != nil {
			log.WithError(notifyErr).Error("Failed to notify provider completion")
		}
	}

	// Trigger provider operations
	if err := c.triggerProviderOperations(ctx, req, operationID, requiredProviders, completionCallback); err != nil {
		log.WithError(err).Error("Failed to trigger provider operations")
		return fmt.Errorf("failed to trigger provider operations: %w", err)
	}

	log.Info("Matter folder creation coordination initiated", map[string]interface{}{
		"operation_id":       operationID,
		"matter_id":          req.MatterID,
		"required_providers": requiredProviders,
	})

	return nil
}

// ProcessMatterUpdateEvent processes a matter.update event with coordination
func (c *matterEventCoordinator) ProcessMatterUpdateEvent(ctx context.Context, req *MatterUpdateEventRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing coordinated matter.update event", map[string]interface{}{
		"event_id":  req.EventID,
		"tenant_id": req.TenantID,
		"matter_id": req.MatterID,
		"client_id": req.ClientID,
	})

	// Get enabled providers for folder operations (matter.update may need folder updates)
	requiredProviders, optionalProviders, err := c.providerConfigService.GetEnabledProviders(ctx, req.TenantID, "matter_folder_update")
	if err != nil {
		log.WithError(err).Error("Failed to get enabled providers for matter.update")
		return fmt.Errorf("failed to get enabled providers: %w", err)
	}

	log.Info("Enabled providers for matter.update", map[string]interface{}{
		"tenant_id":          req.TenantID,
		"required_providers": requiredProviders,
		"optional_providers": optionalProviders,
	})

	// Generate unique operation ID if not provided
	operationID := req.EventID
	if operationID == "" {
		operationID = fmt.Sprintf("matter_%d_folder_update_%d", req.MatterID, time.Now().Unix())
	}

	// Initialize coordination for matter.update
	initReq := &InitializeOperationRequest{
		TenantID:          req.TenantID,
		OperationType:     "matter_folder_update",
		OperationID:       operationID,
		EntityType:        "matter",
		EntityID:          req.MatterID,
		ParentEntityType:  "client",
		ParentEntityID:    req.ClientID,
		RequiredProviders: requiredProviders,
		OptionalProviders: optionalProviders,
		DownstreamAction:  "autodoc_rules",
		TimeoutDuration:   c.providerConfigService.GetOperationTimeout(ctx, req.TenantID, "matter_folder_update"),
		Metadata:          req.EventData,
	}

	if err := c.operationCoordinator.InitializeOperation(ctx, initReq); err != nil {
		log.WithError(err).Error("Failed to initialize operation coordination for matter.update")
		return fmt.Errorf("failed to initialize operation coordination: %w", err)
	}

	// Create completion callback
	completionCallback := func(ctx context.Context, operationID, provider string, success bool, err error) {
		status := model.ProviderStatusCompleted
		if !success {
			status = model.ProviderStatusFailed
		}

		completionReq := &ProviderCompletionRequest{
			OperationID: operationID,
			Provider:    provider,
			Status:      status,
			Error:       err,
		}

		if notifyErr := c.operationCoordinator.NotifyProviderCompletion(ctx, completionReq); notifyErr != nil {
			log.WithError(notifyErr).Error("Failed to notify provider completion for matter.update")
		}
	}

	// Convert MatterUpdateEventRequest to MatterCreateEventRequest for provider operations
	// (providers handle both create and update similarly)
	createReq := &MatterCreateEventRequest{
		TenantID:  req.TenantID,
		MatterID:  req.MatterID,
		ClientID:  req.ClientID,
		EventID:   req.EventID,
		EventData: req.EventData,
	}

	// Trigger provider operations for matter.update (reuse create logic)
	if err := c.triggerProviderOperations(ctx, createReq, operationID, requiredProviders, completionCallback); err != nil {
		log.WithError(err).Error("Failed to trigger provider operations for matter.update")
		return fmt.Errorf("failed to trigger provider operations: %w", err)
	}

	log.Info("Matter update coordination initiated", map[string]interface{}{
		"operation_id":       operationID,
		"matter_id":          req.MatterID,
		"required_providers": requiredProviders,
	})

	return nil
}

// NotifyFolderCreationComplete notifies that folder creation is complete for a provider
func (c *matterEventCoordinator) NotifyFolderCreationComplete(ctx context.Context, req *FolderCreationCompleteRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Folder creation completion notification", map[string]interface{}{
		"event_id": req.EventID,
		"provider": req.Provider,
		"success":  req.Success,
		"error":    req.Error,
	})

	// Determine provider status
	status := model.ProviderStatusCompleted
	if !req.Success {
		status = model.ProviderStatusFailed
	}

	// Notify the operation coordinator
	completionReq := &ProviderCompletionRequest{
		OperationID: req.EventID,
		Provider:    req.Provider,
		Status:      status,
		Error:       req.Error,
	}

	return c.operationCoordinator.NotifyProviderCompletion(ctx, completionReq)
}

// triggerProviderOperations triggers operations for all required providers
func (c *matterEventCoordinator) triggerProviderOperations(ctx context.Context, req *MatterCreateEventRequest, operationID string, providers []string, callback ProviderOperationCallback) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Create payload JSON for provider operations
	payloadJSON, err := c.createProviderPayload(req)
	if err != nil {
		return fmt.Errorf("failed to create provider payload: %w", err)
	}

	// Trigger each provider
	for _, provider := range providers {
		log.Info("Triggering operation for provider", map[string]interface{}{
			"provider":     provider,
			"operation_id": operationID,
			"matter_id":    req.MatterID,
		})

		operation, exists := c.providerOperations[provider]
		if !exists {
			log.WithField("provider", provider).Warn("No operation registered for provider")
			// Notify failure for unregistered provider
			callback(ctx, operationID, provider, false, fmt.Errorf("no operation registered for provider: %s", provider))
			continue
		}

		// Set completion callback
		operation.SetCompletionCallback(operationID, callback)

		// Execute operation asynchronously
		go func(provider string, operation ProviderOperation) {
			operationReq := &ProviderOperationRequest{
				OperationID:   operationID,
				OperationType: "matter_folder_creation",
				EntityType:    "matter",
				EntityID:      req.MatterID,
				TenantID:      req.TenantID,
				PayloadJSON:   payloadJSON,
				Metadata:      req.EventData,
			}

			if err := operation.Execute(ctx, operationReq); err != nil {
				log.WithError(err).WithField("provider", provider).Error("Provider operation failed")
				// Callback will be called by the operation itself
			}
		}(provider, operation)
	}

	return nil
}

// createProviderPayload creates the JSON payload for provider operations
func (c *matterEventCoordinator) createProviderPayload(req *MatterCreateEventRequest) (string, error) {
	// Create payload structure expected by existing consumers
	payload := map[string]interface{}{
		"topic": "matter.create",
		"body": map[string]interface{}{
			"id":        req.MatterID,
			"client_id": req.ClientID,
			"tenant_id": req.TenantID,
		},
	}

	// Add additional fields from event data
	if req.EventData != nil {
		if body, ok := payload["body"].(map[string]interface{}); ok {
			for key, value := range req.EventData {
				if key != "id" && key != "client_id" && key != "tenant_id" {
					body[key] = value
				}
			}
		}
	}

	// Convert to JSON string
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	return string(payloadBytes), nil
}
