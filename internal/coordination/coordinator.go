package coordination

import (
	"context"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// OperationCoordinator coordinates multi-provider operations
type OperationCoordinator interface {
	// InitializeOperation initializes tracking for any multi-provider operation
	InitializeOperation(ctx context.Context, req *InitializeOperationRequest) error

	// NotifyProviderCompletion notifies that a provider has completed
	NotifyProviderCompletion(ctx context.Context, req *ProviderCompletionRequest) error

	// CheckCompletionStatus checks completion status
	CheckCompletionStatus(ctx context.Context, operationID string) (*model.OperationCoordinationStatus, error)

	// GetStatusByEntity gets status by entity
	GetStatusByEntity(ctx context.Context, tenantID uint64, entityType string, entityID uint64, operationType string) (*model.OperationCoordinationStatus, error)

	// CleanupOldRecords cleans up old records
	CleanupOldRecords(ctx context.Context, olderThan time.Duration) error
}

// InitializeOperationRequest represents a request to initialize operation tracking
type InitializeOperationRequest struct {
	TenantID          uint64
	OperationType     string // "folder_creation", "permission_sync", etc.
	OperationID       string // Unique identifier
	EntityType        string // "matter", "client", etc.
	EntityID          uint64
	ParentEntityType  string // Optional
	ParentEntityID    uint64 // Optional
	RequiredProviders []string
	OptionalProviders []string
	DownstreamAction  string // What to trigger after completion
	TimeoutDuration   time.Duration
	Metadata          map[string]interface{} // Operation-specific data
}

// ProviderCompletionRequest represents a provider completion notification
type ProviderCompletionRequest struct {
	OperationID string
	Provider    string
	Status      model.ProviderStatus
	Error       error
	Metadata    map[string]interface{} // Provider-specific completion data
}

// DownstreamActionRegistry manages downstream action handlers
type DownstreamActionRegistry interface {
	// RegisterHandler registers a handler for a specific action type
	RegisterHandler(actionType string, handler DownstreamActionHandler)

	// ExecuteAction executes a downstream action
	ExecuteAction(ctx context.Context, actionType string, status *model.OperationCoordinationStatus) error

	// ListHandlers lists registered handlers
	ListHandlers() map[string]DownstreamActionHandler

	// HasHandler checks if handler exists for action type
	HasHandler(actionType string) bool
}

// operationCoordinator implements OperationCoordinator
type operationCoordinator struct {
	statusRepo         model.OperationCoordinationStatusRepository
	downstreamRegistry DownstreamActionRegistry
}

// NewOperationCoordinator creates a new operation coordinator
func NewOperationCoordinator(
	statusRepo model.OperationCoordinationStatusRepository,
	downstreamRegistry DownstreamActionRegistry,
) OperationCoordinator {
	return &operationCoordinator{
		statusRepo:         statusRepo,
		downstreamRegistry: downstreamRegistry,
	}
}

// InitializeOperation initializes tracking for any multi-provider operation
func (c *operationCoordinator) InitializeOperation(ctx context.Context, req *InitializeOperationRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Initializing operation coordination", map[string]interface{}{
		"operation_id":       req.OperationID,
		"operation_type":     req.OperationType,
		"tenant_id":          req.TenantID,
		"entity_type":        req.EntityType,
		"entity_id":          req.EntityID,
		"required_providers": req.RequiredProviders,
		"optional_providers": req.OptionalProviders,
		"downstream_action":  req.DownstreamAction,
	})

	// Create operation coordination status record
	status := &model.OperationCoordinationStatus{
		TenantID:         req.TenantID,
		OperationType:    req.OperationType,
		OperationID:      req.OperationID,
		EntityType:       req.EntityType,
		EntityID:         req.EntityID,
		ParentEntityType: req.ParentEntityType,
		ParentEntityID:   req.ParentEntityID,
		OverallStatus:    model.CompletionStatusPending,
		DownstreamAction: req.DownstreamAction,
	}

	// Set timeout if specified
	if req.TimeoutDuration > 0 {
		timeoutAt := time.Now().Add(req.TimeoutDuration)
		status.TimeoutAt = &timeoutAt
	}

	// Set required providers
	if err := status.SetRequiredProviders(req.RequiredProviders); err != nil {
		log.WithError(err).Error("Failed to set required providers")
		return fmt.Errorf("failed to set required providers: %w", err)
	}

	// Set optional providers
	if err := status.SetOptionalProviders(req.OptionalProviders); err != nil {
		log.WithError(err).Error("Failed to set optional providers")
		return fmt.Errorf("failed to set optional providers: %w", err)
	}

	// Set metadata
	if req.Metadata != nil {
		if err := status.SetMetadata(req.Metadata); err != nil {
			log.WithError(err).Error("Failed to set metadata")
			return fmt.Errorf("failed to set metadata: %w", err)
		}
	}

	// Initialize provider statuses
	c.setInitialProviderStatuses(status, req.RequiredProviders, req.OptionalProviders)

	// Create the record
	if err := c.statusRepo.Create(status); err != nil {
		log.WithError(err).Error("Failed to create operation coordination status")
		return fmt.Errorf("failed to create operation coordination status: %w", err)
	}

	log.Info("Operation coordination initialized", map[string]interface{}{
		"operation_id":   req.OperationID,
		"overall_status": status.OverallStatus,
	})

	return nil
}

// NotifyProviderCompletion notifies that a provider has completed
func (c *operationCoordinator) NotifyProviderCompletion(ctx context.Context, req *ProviderCompletionRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Provider completion notification", map[string]interface{}{
		"operation_id": req.OperationID,
		"provider":     req.Provider,
		"status":       req.Status,
		"error":        req.Error,
	})

	// Update provider status
	err := c.statusRepo.UpdateProviderStatus(req.OperationID, req.Provider, req.Status)
	if err != nil {
		log.WithError(err).Error("Failed to update provider status")
		return fmt.Errorf("failed to update provider status: %w", err)
	}

	// Get updated status to check if all providers are complete
	status, err := c.statusRepo.GetByOperationID(req.OperationID)
	if err != nil {
		log.WithError(err).Error("Failed to get operation coordination status")
		return fmt.Errorf("failed to get operation coordination status: %w", err)
	}

	log.Info("Updated operation coordination status", map[string]interface{}{
		"operation_id":   req.OperationID,
		"overall_status": status.OverallStatus,
		"is_completed":   status.IsCompleted(),
	})

	// If all providers completed and downstream not yet triggered, trigger downstream action
	if status.IsCompleted() && !status.DownstreamTriggered {
		if err := c.triggerDownstreamAction(ctx, status); err != nil {
			log.WithError(err).Error("Failed to trigger downstream action")
			return fmt.Errorf("failed to trigger downstream action: %w", err)
		}
	}

	return nil
}

// CheckCompletionStatus checks completion status
func (c *operationCoordinator) CheckCompletionStatus(ctx context.Context, operationID string) (*model.OperationCoordinationStatus, error) {
	return c.statusRepo.GetByOperationID(operationID)
}

// GetStatusByEntity gets status by entity
func (c *operationCoordinator) GetStatusByEntity(ctx context.Context, tenantID uint64, entityType string, entityID uint64, operationType string) (*model.OperationCoordinationStatus, error) {
	return c.statusRepo.GetByEntity(tenantID, entityType, entityID, operationType)
}

// CleanupOldRecords cleans up old records
func (c *operationCoordinator) CleanupOldRecords(ctx context.Context, olderThan time.Duration) error {
	cutoff := time.Now().Add(-olderThan)
	return c.statusRepo.DeleteOldRecords(cutoff)
}

// setInitialProviderStatuses sets initial provider statuses based on enabled providers
func (c *operationCoordinator) setInitialProviderStatuses(status *model.OperationCoordinationStatus, requiredProviders, optionalProviders []string) {
	statuses := make(map[string]model.ProviderStatus)

	// Set required providers to pending
	for _, provider := range requiredProviders {
		statuses[provider] = model.ProviderStatusPending
	}

	// Set optional providers to pending
	for _, provider := range optionalProviders {
		statuses[provider] = model.ProviderStatusPending
	}

	// Set provider statuses
	status.SetProviderStatuses(statuses)

	// Recalculate overall status (will be pending if there are required providers)
	status.UpdateProviderStatus("", model.ProviderStatusPending) // Trigger recalculation
}

// triggerDownstreamAction triggers downstream action after all providers complete
func (c *operationCoordinator) triggerDownstreamAction(ctx context.Context, status *model.OperationCoordinationStatus) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Triggering downstream action after operation completion", map[string]interface{}{
		"operation_id":      status.OperationID,
		"operation_type":    status.OperationType,
		"downstream_action": status.DownstreamAction,
		"entity_type":       status.EntityType,
		"entity_id":         status.EntityID,
	})

	// Execute downstream action using registry
	if status.DownstreamAction != "" {
		// Create detached context for async operations to avoid HTTP request cancellation
		// Preserve important metadata from original context
		detachedCtx := context.Background()
		if tenantID := ctx.Value("tenant_id"); tenantID != nil {
			detachedCtx = context.WithValue(detachedCtx, "tenant_id", tenantID)
		}
		if userID := ctx.Value("user_id"); userID != nil {
			detachedCtx = context.WithValue(detachedCtx, "user_id", userID)
		}
		if requestID := ctx.Value("request_id"); requestID != nil {
			detachedCtx = context.WithValue(detachedCtx, "request_id", requestID)
		}

		// Also add tenant_id from status as fallback
		if detachedCtx.Value("tenant_id") == nil && status.TenantID > 0 {
			detachedCtx = context.WithValue(detachedCtx, "tenant_id", status.TenantID)
		}

		err := c.downstreamRegistry.ExecuteAction(detachedCtx, status.DownstreamAction, status)
		if err != nil {
			log.WithError(err).Error("Failed to execute downstream action")
			return err
		}
	}

	// Mark downstream as triggered
	now := time.Now()
	status.DownstreamTriggered = true
	status.DownstreamTriggeredAt = &now

	if err := c.statusRepo.Update(status); err != nil {
		log.WithError(err).Error("Failed to update downstream triggered status")
		return err
	}

	log.Info("Downstream action triggered successfully", map[string]interface{}{
		"operation_id":      status.OperationID,
		"downstream_action": status.DownstreamAction,
	})

	return nil
}
