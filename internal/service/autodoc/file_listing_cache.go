package autodoc

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"bilabl/docman/pkg/bilabllog"

	"github.com/redis/go-redis/v9"
)

// FileListingCache defines the interface for caching file listings
type FileListingCache interface {
	// GetCachedFiles retrieves cached file listing
	GetCachedFiles(ctx context.Context, req *ListFilesRequest, provider string) (*ListFilesResponse, bool)

	// CacheFiles stores file listing in cache
	CacheFiles(ctx context.Context, req *ListFilesRequest, resp *ListFilesResponse, provider string) error

	// InvalidateCache invalidates cache for specific path
	InvalidateCache(ctx context.Context, tenantID uint64, provider, path string) error

	// InvalidateTenantCache invalidates all cache for a tenant
	InvalidateTenantCache(ctx context.Context, tenantID uint64) error

	// InvalidateProviderCache invalidates all cache for a provider
	InvalidateProviderCache(ctx context.Context, tenantID uint64, provider string) error

	// WarmCache pre-loads frequently accessed paths
	WarmCache(ctx context.Context, tenantID uint64, provider string, paths []string) error

	// GetCacheStats returns cache hit/miss statistics
	GetCacheStats(ctx context.Context) (*CacheStats, error)

	// PublishInvalidation publishes cache invalidation event
	PublishInvalidation(ctx context.Context, event *CacheInvalidationEvent) error

	// SubscribeInvalidation subscribes to cache invalidation events
	SubscribeInvalidation(ctx context.Context, handler func(*CacheInvalidationEvent)) error
}

// CacheStats represents cache performance metrics
type CacheStats struct {
	Hits        int64   `json:"hits"`
	Misses      int64   `json:"misses"`
	HitRate     float64 `json:"hit_rate"`
	TotalKeys   int64   `json:"total_keys"`
	MemoryUsage int64   `json:"memory_usage"`
}

// CacheInvalidationEvent represents a cache invalidation event
type CacheInvalidationEvent struct {
	Type      string    `json:"type"` // "path", "tenant", "provider"
	TenantID  uint64    `json:"tenant_id"`
	Provider  string    `json:"provider,omitempty"`
	Path      string    `json:"path,omitempty"`
	Timestamp time.Time `json:"timestamp"`
	NodeID    string    `json:"node_id"`
}

// RedisFileListingCache implements FileListingCache using Redis
type RedisFileListingCache struct {
	client    *redis.Client
	pubsub    *redis.PubSub
	ttl       time.Duration
	keyPrefix string
	nodeID    string

	// Metrics
	hits   int64
	misses int64
}

// NewRedisFileListingCache creates a new Redis-based file listing cache
func NewRedisFileListingCache(client *redis.Client, ttl time.Duration) FileListingCache {
	cache := &RedisFileListingCache{
		client:    client,
		ttl:       ttl,
		keyPrefix: "autodoc:files:",
		nodeID:    generateNodeID(),
	}

	return cache
}

// GetCachedFiles retrieves cached file listing
func (c *RedisFileListingCache) GetCachedFiles(ctx context.Context, req *ListFilesRequest, provider string) (*ListFilesResponse, bool) {
	log := bilabllog.CreateContextLogger(ctx)

	key := c.buildCacheKey(req.TenantID, provider, req.Path, req)

	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			c.misses++
			log.WithField("cache_key", key).Debug("Cache miss for file listing")
			return nil, false
		}
		log.WithError(err).WithField("cache_key", key).Error("Failed to get cached file listing")
		c.misses++
		return nil, false
	}

	var resp ListFilesResponse
	if err := json.Unmarshal([]byte(data), &resp); err != nil {
		log.WithError(err).WithField("cache_key", key).Error("Failed to unmarshal cached file listing")
		c.misses++
		return nil, false
	}

	c.hits++
	log.WithFields(map[string]interface{}{
		"cache_key":  key,
		"file_count": len(resp.Data),
	}).Debug("Cache hit for file listing")

	return &resp, true
}

// CacheFiles stores file listing in cache
func (c *RedisFileListingCache) CacheFiles(ctx context.Context, req *ListFilesRequest, resp *ListFilesResponse, provider string) error {
	log := bilabllog.CreateContextLogger(ctx)

	key := c.buildCacheKey(req.TenantID, provider, req.Path, req)

	data, err := json.Marshal(resp)
	if err != nil {
		return fmt.Errorf("failed to marshal file listing response: %w", err)
	}

	err = c.client.Set(ctx, key, data, c.ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to cache file listing: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"cache_key":  key,
		"file_count": len(resp.Data),
		"ttl":        c.ttl,
	}).Debug("Cached file listing")

	return nil
}

// InvalidateCache invalidates cache for specific path
func (c *RedisFileListingCache) InvalidateCache(ctx context.Context, tenantID uint64, provider, path string) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Build pattern to match all cache keys for this path
	// Use wildcard pattern to match any parameters after the path
	sanitizedPath := c.sanitizePath(path)
	pattern := fmt.Sprintf("%s%d:%s:%s*", c.keyPrefix, tenantID, provider, sanitizedPath)

	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to find cache keys for pattern %s: %w", pattern, err)
	}

	if len(keys) > 0 {
		err = c.client.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete cache keys: %w", err)
		}

		log.WithFields(map[string]interface{}{
			"tenant_id":    tenantID,
			"provider":     provider,
			"path":         path,
			"pattern":      pattern,
			"deleted_keys": len(keys),
		}).Info("Invalidated cache for path")
	}

	// Publish invalidation event
	event := &CacheInvalidationEvent{
		Type:      "path",
		TenantID:  tenantID,
		Provider:  provider,
		Path:      path,
		Timestamp: time.Now(),
		NodeID:    c.nodeID,
	}

	return c.PublishInvalidation(ctx, event)
}

// InvalidateTenantCache invalidates all cache for a tenant
func (c *RedisFileListingCache) InvalidateTenantCache(ctx context.Context, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	pattern := fmt.Sprintf("%s%d:*", c.keyPrefix, tenantID)

	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to find cache keys for tenant %d: %w", tenantID, err)
	}

	if len(keys) > 0 {
		err = c.client.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete tenant cache keys: %w", err)
		}

		log.WithFields(map[string]interface{}{
			"tenant_id":    tenantID,
			"deleted_keys": len(keys),
		}).Info("Invalidated all cache for tenant")
	}

	// Publish invalidation event
	event := &CacheInvalidationEvent{
		Type:      "tenant",
		TenantID:  tenantID,
		Timestamp: time.Now(),
		NodeID:    c.nodeID,
	}

	return c.PublishInvalidation(ctx, event)
}

// InvalidateProviderCache invalidates all cache for a provider
func (c *RedisFileListingCache) InvalidateProviderCache(ctx context.Context, tenantID uint64, provider string) error {
	log := bilabllog.CreateContextLogger(ctx)

	pattern := fmt.Sprintf("%s%d:%s:*", c.keyPrefix, tenantID, provider)

	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to find cache keys for provider %s: %w", provider, err)
	}

	if len(keys) > 0 {
		err = c.client.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete provider cache keys: %w", err)
		}

		log.WithFields(map[string]interface{}{
			"tenant_id":    tenantID,
			"provider":     provider,
			"deleted_keys": len(keys),
		}).Info("Invalidated all cache for provider")
	}

	// Publish invalidation event
	event := &CacheInvalidationEvent{
		Type:      "provider",
		TenantID:  tenantID,
		Provider:  provider,
		Timestamp: time.Now(),
		NodeID:    c.nodeID,
	}

	return c.PublishInvalidation(ctx, event)
}

// buildCacheKey builds cache key with request parameters
func (c *RedisFileListingCache) buildCacheKey(tenantID uint64, provider, path string, req *ListFilesRequest) string {
	// Base key: autodoc:files:{tenantID}:{provider}:{path}
	baseKey := fmt.Sprintf("%s%d:%s:%s", c.keyPrefix, tenantID, provider, c.sanitizePath(path))

	// Add request parameters to make key unique
	params := []string{}
	if req.FileType != 0 {
		params = append(params, fmt.Sprintf("type:%d", req.FileType))
	}
	if req.Recursive {
		params = append(params, "recursive:true")
	}
	if req.SortBy != "" {
		params = append(params, fmt.Sprintf("sort:%s:%s", req.SortBy, req.SortOrder))
	}
	if req.Page > 0 && req.Limit > 0 {
		params = append(params, fmt.Sprintf("page:%d:%d", req.Page, req.Limit))
	}

	if len(params) > 0 {
		return fmt.Sprintf("%s:%s", baseKey, strings.Join(params, ":"))
	}

	return baseKey
}

// sanitizePath sanitizes path for use in cache key
func (c *RedisFileListingCache) sanitizePath(path string) string {
	if path == "" {
		return "root"
	}
	// Replace problematic characters for Redis keys
	path = strings.ReplaceAll(path, ":", "_")
	path = strings.ReplaceAll(path, " ", "_")
	path = strings.ReplaceAll(path, "/", "_")
	return path
}

// WarmCache pre-loads frequently accessed paths
func (c *RedisFileListingCache) WarmCache(ctx context.Context, tenantID uint64, provider string, paths []string) error {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithFields(map[string]interface{}{
		"tenant_id": tenantID,
		"provider":  provider,
		"paths":     paths,
	}).Info("Starting cache warming")

	// This would typically involve calling the actual file listing service
	// to populate cache for frequently accessed paths
	// For now, we'll just log the warming request

	for _, path := range paths {
		log.WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"provider":  provider,
			"path":      path,
		}).Debug("Cache warming for path (placeholder implementation)")
	}

	return nil
}

// GetCacheStats returns cache hit/miss statistics
func (c *RedisFileListingCache) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Get Redis memory usage
	_, err := c.client.Info(ctx, "memory").Result()
	if err != nil {
		log.WithError(err).Warn("Failed to get Redis memory info")
	}

	// Count total keys matching our pattern
	pattern := fmt.Sprintf("%s*", c.keyPrefix)
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		log.WithError(err).Warn("Failed to count cache keys")
	}

	totalRequests := c.hits + c.misses
	hitRate := float64(0)
	if totalRequests > 0 {
		hitRate = float64(c.hits) / float64(totalRequests)
	}

	stats := &CacheStats{
		Hits:        c.hits,
		Misses:      c.misses,
		HitRate:     hitRate,
		TotalKeys:   int64(len(keys)),
		MemoryUsage: 0, // Would parse from Redis info
	}

	log.WithFields(map[string]interface{}{
		"hits":       stats.Hits,
		"misses":     stats.Misses,
		"hit_rate":   stats.HitRate,
		"total_keys": stats.TotalKeys,
	}).Debug("Retrieved cache statistics")

	return stats, nil
}

// PublishInvalidation publishes cache invalidation event
func (c *RedisFileListingCache) PublishInvalidation(ctx context.Context, event *CacheInvalidationEvent) error {
	log := bilabllog.CreateContextLogger(ctx)

	channel := "autodoc:cache:invalidation"

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal invalidation event: %w", err)
	}

	err = c.client.Publish(ctx, channel, data).Err()
	if err != nil {
		return fmt.Errorf("failed to publish invalidation event: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"channel":    channel,
		"event_type": event.Type,
		"tenant_id":  event.TenantID,
		"provider":   event.Provider,
		"path":       event.Path,
		"node_id":    event.NodeID,
	}).Debug("Published cache invalidation event")

	return nil
}

// SubscribeInvalidation subscribes to cache invalidation events
func (c *RedisFileListingCache) SubscribeInvalidation(ctx context.Context, handler func(*CacheInvalidationEvent)) error {
	log := bilabllog.CreateContextLogger(ctx)

	channel := "autodoc:cache:invalidation"

	c.pubsub = c.client.Subscribe(ctx, channel)

	log.WithField("channel", channel).Info("Subscribed to cache invalidation events")

	// Start goroutine to handle messages
	go func() {
		defer c.pubsub.Close()

		ch := c.pubsub.Channel()
		for msg := range ch {
			var event CacheInvalidationEvent
			if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
				log.WithError(err).Error("Failed to unmarshal invalidation event")
				continue
			}

			// Skip events from same node to avoid self-invalidation
			if event.NodeID == c.nodeID {
				continue
			}

			log.WithFields(map[string]interface{}{
				"event_type": event.Type,
				"tenant_id":  event.TenantID,
				"provider":   event.Provider,
				"path":       event.Path,
				"node_id":    event.NodeID,
			}).Debug("Received cache invalidation event")

			// Call handler
			handler(&event)
		}
	}()

	return nil
}

// generateNodeID generates a unique node identifier
func generateNodeID() string {
	return fmt.Sprintf("node_%d", time.Now().UnixNano())
}

// NoOpFileListingCache is a no-op implementation for testing
type NoOpFileListingCache struct{}

// NewNoOpFileListingCache creates a new no-op cache
func NewNoOpFileListingCache() FileListingCache {
	return &NoOpFileListingCache{}
}

func (c *NoOpFileListingCache) GetCachedFiles(ctx context.Context, req *ListFilesRequest, provider string) (*ListFilesResponse, bool) {
	return nil, false
}

func (c *NoOpFileListingCache) CacheFiles(ctx context.Context, req *ListFilesRequest, resp *ListFilesResponse, provider string) error {
	return nil
}

func (c *NoOpFileListingCache) InvalidateCache(ctx context.Context, tenantID uint64, provider, path string) error {
	return nil
}

func (c *NoOpFileListingCache) InvalidateTenantCache(ctx context.Context, tenantID uint64) error {
	return nil
}

func (c *NoOpFileListingCache) InvalidateProviderCache(ctx context.Context, tenantID uint64, provider string) error {
	return nil
}

func (c *NoOpFileListingCache) WarmCache(ctx context.Context, tenantID uint64, provider string, paths []string) error {
	return nil
}

func (c *NoOpFileListingCache) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	return &CacheStats{}, nil
}

func (c *NoOpFileListingCache) PublishInvalidation(ctx context.Context, event *CacheInvalidationEvent) error {
	return nil
}

func (c *NoOpFileListingCache) SubscribeInvalidation(ctx context.Context, handler func(*CacheInvalidationEvent)) error {
	return nil
}
