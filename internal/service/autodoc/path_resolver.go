package autodoc

import (
	"context"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// PathResolver provides utilities for dynamic target path generation
type PathResolver struct {
	documentRepo repositories.DocumentRepository
}

// NewPathResolver creates a new PathResolver instance
func NewPathResolver(documentRepo repositories.DocumentRepository) *PathResolver {
	return &PathResolver{
		documentRepo: documentRepo,
	}
}

// ResolveTargetPath replaces placeholders in a path template with values from context
// Supports nested placeholders like {client.name}, {matter.id}, etc.
// Special placeholders: {client_folder}, {matter_folder} resolve to Document IDs
// Returns a clean, valid path string
func (pr *PathResolver) ResolveTargetPath(ctx context.Context, pathTemplate string, context map[string]interface{}) (string, error) {
	if pathTemplate == "" {
		return "", fmt.Errorf("path template cannot be empty")
	}

	if context == nil {
		context = make(map[string]interface{})
	}

	// Regular expression to match placeholders like {key} or {nested.key}
	placeholderRegex := regexp.MustCompile(`\{([^}]+)\}`)

	result := pathTemplate

	// Find all placeholders and replace them
	matches := placeholderRegex.FindAllStringSubmatch(pathTemplate, -1)
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		placeholder := match[0]            // Full match including {}
		key := strings.TrimSpace(match[1]) // Key without {}

		var valueStr string
		var err error

		// Handle special placeholders
		if key == "client_folder" || key == "matter_folder" {
			valueStr, err = pr.resolveSpecialPlaceholder(ctx, key, context)
			if err != nil {
				return "", fmt.Errorf("failed to resolve special placeholder '%s': %w", placeholder, err)
			}
		} else {
			// Get value from context using nested key support
			value, err := pr.getNestedValue(context, key)
			if err != nil {
				return "", fmt.Errorf("failed to resolve placeholder '%s': %w", placeholder, err)
			}

			// Convert value to string
			valueStr = pr.valueToString(value)
		}

		// Sanitize the value for use in file paths (except for special placeholders that return IDs)
		if key != "client_folder" && key != "matter_folder" {
			valueStr = pr.sanitizePathComponent(valueStr)
		}

		// Replace the placeholder with the value
		result = strings.ReplaceAll(result, placeholder, valueStr)
	}

	// Clean the final path
	result = filepath.Clean(result)

	// Ensure the path doesn't start with .. or contain .. components for security
	if strings.Contains(result, "..") {
		return "", fmt.Errorf("resolved path contains invalid '..' components: %s", result)
	}

	return result, nil
}

// getNestedValue retrieves a value from a nested map using dot notation
// Supports keys like "client.name", "matter.id", etc.
func (pr *PathResolver) getNestedValue(context map[string]interface{}, key string) (interface{}, error) {
	if key == "" {
		return "", fmt.Errorf("key cannot be empty")
	}

	// Split the key by dots to handle nested access
	parts := strings.Split(key, ".")
	current := context

	for i, part := range parts {
		if current == nil {
			return "", fmt.Errorf("context is nil at key part '%s'", part)
		}

		value, exists := current[part]
		if !exists {
			return "", fmt.Errorf("key '%s' not found in context", part)
		}

		// If this is the last part, return the value
		if i == len(parts)-1 {
			return value, nil
		}

		// Otherwise, the value should be a map for further nesting
		if nestedMap, ok := value.(map[string]interface{}); ok {
			current = nestedMap
		} else {
			return "", fmt.Errorf("key part '%s' is not a nested object", part)
		}
	}

	return "", fmt.Errorf("unexpected error resolving key '%s'", key)
}

// resolveSpecialPlaceholder resolves special placeholders like {client_folder} and {matter_folder}
// These placeholders represent the object context, not specific folders
// The actual folder resolution happens in ResolveTargetParentPath when processing the full path
func (pr *PathResolver) resolveSpecialPlaceholder(ctx context.Context, key string, context map[string]interface{}) (string, error) {
	log := logger.WithCtx(ctx, "resolveSpecialPlaceholder")

	// Get tenantID from context
	tenantIDValue, exists := context["tenant_id"]
	if !exists {
		return "", fmt.Errorf("tenant_id not found in context")
	}

	tenantID, err := pr.convertToUint64(tenantIDValue, "tenant_id")
	if err != nil {
		return "", err
	}

	var objectType int
	var objectIDKey string = "id"

	switch key {
	case "client_folder":
		objectType = 1 // DocumentObjectTypeClient
		// For matter events, get client_id instead of matter id
		// But only if client_id is valid (> 0) to avoid using invalid client_id from client events
		if clientIDValue, err := pr.getNestedValue(context, "client_id"); err == nil {
			if clientID, err := pr.convertToUint64(clientIDValue, "client_id"); err == nil && clientID > 0 {
				objectIDKey = "client_id"
			}
		}
	case "matter_folder":
		objectType = 3 // DocumentObjectTypeMatter
	default:
		return "", fmt.Errorf("unsupported special placeholder: %s", key)
	}

	// Get object ID from context
	objectIDValue, err := pr.getNestedValue(context, objectIDKey)
	if err != nil {
		return "", fmt.Errorf("failed to get %s from context: %w", objectIDKey, err)
	}

	objectID, err := pr.convertToUint64(objectIDValue, objectIDKey)
	if err != nil {
		return "", err
	}

	// For client_folder, validate that client data is available or warn about potential issues
	if key == "client_folder" {
		if clientData, exists := context["client"]; !exists || clientData == nil {
			log.Warnf("Client folder resolution without client enrichment data - using client_id=%d only", objectID)
		}
	}

	log.Infof("Resolved %s placeholder to object_type=%d object_id=%d tenant_id=%d",
		key, objectType, objectID, tenantID)

	// Return a special format that indicates this is an object context
	// Format: "object:{object_type}:{object_id}:{tenant_id}"
	return fmt.Sprintf("object:%d:%d:%d", objectType, objectID, tenantID), nil
}

// valueToString converts various types to string representation
func (pr *PathResolver) valueToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%.0f", v)
	case bool:
		return fmt.Sprintf("%t", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// sanitizePathComponent removes or replaces invalid characters for file system paths
func (pr *PathResolver) sanitizePathComponent(component string) string {
	if component == "" {
		return "unnamed"
	}

	// Replace common invalid characters with safe alternatives
	replacements := map[string]string{
		"/":  "_",
		"\\": "_",
		":":  "_",
		"*":  "_",
		"?":  "_",
		"\"": "_",
		"<":  "_",
		">":  "_",
		"|":  "_",
		"\n": "_",
		"\r": "_",
		"\t": "_",
	}

	result := component
	for invalid, replacement := range replacements {
		result = strings.ReplaceAll(result, invalid, replacement)
	}

	// Remove leading/trailing spaces and dots
	result = strings.Trim(result, " .")

	// If result is empty after sanitization, provide a default
	if result == "" {
		return "unnamed"
	}

	// Limit length to prevent overly long path components
	if len(result) > 100 {
		result = result[:100]
	}

	return result
}

// ValidatePathTemplate validates that a path template has valid syntax
func (pr *PathResolver) ValidatePathTemplate(pathTemplate string) error {
	if pathTemplate == "" {
		return fmt.Errorf("path template cannot be empty")
	}

	// Use regex to validate placeholder format
	placeholderRegex := regexp.MustCompile(`\{([^}]+)\}`)

	// Find all placeholders
	matches := placeholderRegex.FindAllStringSubmatch(pathTemplate, -1)

	// Check for empty placeholders
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}
		key := strings.TrimSpace(match[1])
		if key == "" {
			return fmt.Errorf("empty placeholders are not allowed")
		}
	}

	// Check for malformed braces (double braces or unmatched braces)
	// Remove all valid {...} patterns and check if any braces remain
	cleaned := placeholderRegex.ReplaceAllString(pathTemplate, "")
	if strings.Contains(cleaned, "{") || strings.Contains(cleaned, "}") {
		return fmt.Errorf("malformed braces in path template - use {placeholder} format")
	}

	// Check for double braces (not supported)
	if strings.Contains(pathTemplate, "{{") || strings.Contains(pathTemplate, "}}") {
		return fmt.Errorf("double braces are not supported - use {placeholder} format instead of {{placeholder}}")
	}

	return nil
}

// convertToUint64 converts various numeric types to uint64
// Handles the common case where JSON unmarshaling produces float64 instead of uint64
func (pr *PathResolver) convertToUint64(value interface{}, fieldName string) (uint64, error) {
	switch v := value.(type) {
	case uint64:
		return v, nil
	case float64:
		// Check if it's a whole number
		if v != float64(uint64(v)) {
			return 0, fmt.Errorf("%s must be a whole number, got %f", fieldName, v)
		}
		// Check if it's within uint64 range
		if v < 0 || v > float64(^uint64(0)) {
			return 0, fmt.Errorf("%s out of uint64 range: %f", fieldName, v)
		}
		return uint64(v), nil
	case int:
		if v < 0 {
			return 0, fmt.Errorf("%s must be non-negative, got %d", fieldName, v)
		}
		return uint64(v), nil
	case int64:
		if v < 0 {
			return 0, fmt.Errorf("%s must be non-negative, got %d", fieldName, v)
		}
		return uint64(v), nil
	case uint:
		return uint64(v), nil
	case uint32:
		return uint64(v), nil
	case int32:
		if v < 0 {
			return 0, fmt.Errorf("%s must be non-negative, got %d", fieldName, v)
		}
		return uint64(v), nil
	default:
		return 0, fmt.Errorf("%s must be a number, got %T", fieldName, value)
	}
}
