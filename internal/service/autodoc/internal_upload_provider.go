package autodoc

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/transport"

	"gitlab.com/goxp/cloud0/ginext"
)

// InternalUploadProvider handles uploads for internal storage
type InternalUploadProvider struct {
	documentService DocumentService
	globClient      transport.Glob // Glob client for presigned URLs
}

// NewInternalUploadProvider creates a new internal upload provider
func NewInternalUploadProvider(documentService DocumentService, globServiceURL string) UploadProvider {
	return &InternalUploadProvider{
		documentService: documentService,
		globClient:      transport.NewGlob(globServiceURL),
	}
}

// GetProviderName returns the provider name
func (p *InternalUploadProvider) GetProviderName() string {
	return "internal"
}

// CreateUploadSession creates an upload session for internal storage
func (p *InternalUploadProvider) CreateUploadSession(ctx context.Context, req *CreateUploadSessionRequest) (*CreateUploadSessionResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Creating upload session for internal provider")

	// Validate request
	if err := p.validateCreateUploadSessionRequest(req); err != nil {
		return nil, err
	}

	// Generate session token
	sessionToken := fmt.Sprintf("internal_%d_%d", req.TenantID, time.Now().Unix())

	// Call glob service to get presigned upload URL
	userID := "system" // Use system user for AutoDoc uploads
	tenantID := fmt.Sprintf("%d", req.TenantID)
	userRoles := "autodoc" // Role for AutoDoc operations

	presignResp, err := p.globClient.GetPresignedUploadURL(ctx, req.FileName, userID, tenantID, userRoles)
	if err != nil {
		log.WithError(err).Error("Failed to get presigned upload URL from glob service")
		return nil, fmt.Errorf("failed to get presigned upload URL: %w", err)
	}

	// Set expiration (1 hour from now)
	expiresAt := time.Now().Add(1 * time.Hour)

	response := &CreateUploadSessionResponse{
		UploadURL:    presignResp.UploadURL,
		SessionToken: sessionToken,
		Key:          presignResp.Key,
		ExpiresAt:    expiresAt,
		Provider:     p.GetProviderName(),
		Message:      "Upload file to the provided URL, then call register endpoint with the key",
	}

	log.WithField("session_token", sessionToken).WithField("upload_url", presignResp.UploadURL).WithField("key", presignResp.Key).Info("Upload session created for internal provider")
	return response, nil
}

// RegisterUpload registers the uploaded file for internal storage
func (p *InternalUploadProvider) RegisterUpload(ctx context.Context, req *RegisterUploadRequest) (*RegisterUploadResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Registering upload for internal provider")

	// Validate request
	if err := p.validateRegisterUploadRequest(req); err != nil {
		return nil, err
	}

	// For internal provider, create document record
	// The file content should already be uploaded to internal storage via glob service
	// Use the upload key to reference the uploaded file
	createReq := &CreateDocumentRequest{
		Name:        req.FileName,
		TenantID:    req.TenantID,
		ParentID:    req.ParentID,   // Use parent ID from request
		ObjectID:    req.ObjectID,   // Use object ID from request
		ObjectType:  req.ObjectType, // Use object type from request
		DocType:     2,              // File type
		CreatedUser: req.CreatedUser,
		Key:         req.Key, // Store glob key for file retrieval
	}

	document, err := p.documentService.CreateDocument(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("Failed to create document for internal upload")
		return nil, fmt.Errorf("failed to register internal upload: %w", err)
	}

	response := &RegisterUploadResponse{
		ID:       document.ID,
		Name:     document.Name,
		Size:     req.FileSize,
		Provider: p.GetProviderName(),
		Message:  "File registered successfully in internal storage",
	}

	log.WithField("document_id", document.ID).WithField("session_token", req.SessionToken).WithField("key", req.Key).Info("Upload registered for internal provider with key stored")
	return response, nil
}

// validateCreateUploadSessionRequest validates the create upload session request
func (p *InternalUploadProvider) validateCreateUploadSessionRequest(req *CreateUploadSessionRequest) error {
	if req == nil {
		return ginext.NewError(http.StatusBadRequest, "request cannot be nil")
	}
	if req.TenantID == 0 {
		return ginext.NewError(http.StatusBadRequest, "tenant_id is required")
	}
	if req.FileName == "" {
		return ginext.NewError(http.StatusBadRequest, "file_name is required")
	}
	if req.CreatedUser == 0 {
		return ginext.NewError(http.StatusBadRequest, "created_user is required")
	}

	// Validate file size (100MB limit for internal storage)
	const maxFileSize = 100 * 1024 * 1024 // 100MB
	if req.FileSize > maxFileSize {
		return ginext.NewError(http.StatusBadRequest, fmt.Sprintf("file size %d bytes exceeds limit of %d bytes", req.FileSize, maxFileSize))
	}

	return nil
}

// validateRegisterUploadRequest validates the register upload request
func (p *InternalUploadProvider) validateRegisterUploadRequest(req *RegisterUploadRequest) error {
	if req == nil {
		return ginext.NewError(http.StatusBadRequest, "request cannot be nil")
	}
	if req.TenantID == 0 {
		return ginext.NewError(http.StatusBadRequest, "tenant_id is required")
	}
	if req.SessionToken == "" {
		return ginext.NewError(http.StatusBadRequest, "session_token is required")
	}
	if req.Key == "" {
		return ginext.NewError(http.StatusBadRequest, "key is required")
	}
	if req.FileName == "" {
		return ginext.NewError(http.StatusBadRequest, "file_name is required")
	}
	if req.CreatedUser == 0 {
		return ginext.NewError(http.StatusBadRequest, "created_user is required")
	}

	// Validate session token format for internal provider
	if len(req.SessionToken) < 10 || req.SessionToken[:9] != "internal_" {
		return ginext.NewError(http.StatusBadRequest, "invalid session token format for internal provider")
	}

	return nil
}
