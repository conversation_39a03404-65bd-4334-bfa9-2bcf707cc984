package autodoc

import (
	"context"
	"fmt"

	"bilabl/docman/pkg/bilabllog"
)

// GDriveFileProvider handles file operations for Google Drive
type GDriveFileProvider struct {
	documentService DocumentService
}

// NewGDriveFileProvider creates a new Google Drive file provider
func NewGDriveFileProvider(documentService DocumentService) FileProvider {
	return &GDriveFileProvider{
		documentService: documentService,
	}
}

// GetProviderName returns the provider name
func (p *GDriveFileProvider) GetProviderName() string {
	return "gdrive"
}

// GetFile gets file information by Google Drive ID
func (p *GDriveFileProvider) GetFile(ctx context.Context, req *GetFileRequest) (*GetFileResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Getting file from Google Drive provider")

	// For Google Drive, req.ID is the external file ID
	externalID := req.ID

	// Get document from Google Drive service using external ID
	document, err := p.documentService.GetDocumentByExternalID(ctx, externalID, req.TenantID)
	if err != nil {
		log.WithError(err).Error("Failed to get document from Google Drive service")
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	response := &GetFileResponse{
		ID:         req.ID,
		Name:       document.Name,
		Size:       document.Size,
		ExternalID: externalID,
		Provider:   p.GetProviderName(),
		CreatedAt:  document.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:  document.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	log.WithField("external_id", externalID).WithField("file_name", document.Name).Info("File retrieved from Google Drive provider")
	return response, nil
}

// UpdateFile updates file information by Google Drive ID
func (p *GDriveFileProvider) UpdateFile(ctx context.Context, req *UpdateFileRequest) (*UpdateFileResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Updating file in Google Drive provider")

	// For Google Drive, req.ID is the external file ID
	externalID := req.ID

	// Update document in Google Drive service using external ID
	updateReq := &UpdateDocumentRequest{
		ExternalID: externalID,
		TenantID:   req.TenantID,
		Name:       req.Name,
	}

	document, err := p.documentService.UpdateDocument(ctx, updateReq)
	if err != nil {
		log.WithError(err).Error("Failed to update document in Google Drive service")
		return nil, fmt.Errorf("failed to update document: %w", err)
	}

	response := &UpdateFileResponse{
		ID:       req.ID,
		Name:     document.Name,
		Size:     document.Size,
		Provider: p.GetProviderName(),
		Message:  "File updated successfully in Google Drive",
	}

	log.WithField("external_id", externalID).WithField("file_name", document.Name).Info("File updated in Google Drive provider")
	return response, nil
}

// DeleteFile deletes file by Google Drive ID
func (p *GDriveFileProvider) DeleteFile(ctx context.Context, req *FileDeleteRequest) (*FileDeleteResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Deleting file from Google Drive provider")

	// For Google Drive, req.ID is the external file ID
	externalID := req.ID

	// Delete document from Google Drive service using external ID
	err := p.documentService.DeleteDocumentByExternalID(ctx, externalID, req.TenantID)
	if err != nil {
		log.WithError(err).Error("Failed to delete document from Google Drive service")
		return nil, fmt.Errorf("failed to delete document: %w", err)
	}

	response := &FileDeleteResponse{
		ID:       req.ID,
		Provider: p.GetProviderName(),
		Message:  "File deleted successfully from Google Drive",
	}

	log.WithField("external_id", externalID).Info("File deleted from Google Drive provider")
	return response, nil
}
