package autodoc

type MatterEventBody struct {
	ID       uint64 `json:"id"`
	ClientID uint64 `json:"client_id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	TenantID uint64 `json:"tenant_id"`
	ActorID  uint64 `json:"actor_id"`
	NoNotify bool   `json:"no_notify"`
	Extra    struct {
		Current map[string]interface{} `json:"current"`
		Old     map[string]interface{} `json:"old,omitempty"`
	} `json:"extra"`
}

type ClientEventBody struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	ShortName string `json:"short_name"`
	Code      string `json:"code"`
	TenantID  uint64 `json:"tenant_id"`
	Extra     struct {
		Current map[string]interface{} `json:"current"`
		Old     map[string]interface{} `json:"old,omitempty"`
	} `json:"extra"`
}
