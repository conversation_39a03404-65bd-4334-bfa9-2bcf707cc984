package autodoc

import (
	"bytes"
	"context"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"code.mybil.net/gophers/gokit/util/jsonutil"
)

// GenerateDocumentHandler handles generate_document actions
type GenerateDocumentHandler struct {
	*BaseActionHandler
	autoDocService   AutoDocService
	documentRepo     repositories.DocumentRepository
	documentRegistry DocumentServiceRegistry
	mappingService   MappingService
}

// NewGenerateDocumentHandler creates a new generate document handler
func <PERSON>GenerateDocument<PERSON>and<PERSON>(
	autoDocService AutoDocService,
	documentRepo repositories.DocumentRepository,
	documentRegistry DocumentServiceRegistry,
	mappingService MappingService,
) *GenerateDocumentHandler {
	return &GenerateDocumentHandler{
		BaseActionHandler: NewBaseActionHandler("generate_document"),
		autoDocService:    autoDocService,
		documentRepo:      documentRepo,
		documentRegistry:  documentRegistry,
		mappingService:    mappingService,
	}
}

// Execute handles the generate_document action
func (h *GenerateDocumentHandler) Execute(ctx context.Context, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.Execute")
	log.
		WithField("body", string(jsonutil.MustMarshal(params.PlaceholderData))).
		Infof("Executing generate_document action source_provider=%s target_provider=%s tenant_id=%d",
			params.Action.Provider, params.Action.TargetProvider, params.TenantID)

	// Validate action configuration
	if params.Action.SourcePath == "" {
		return fmt.Errorf("source_path is required for generate_document action")
	}
	if params.Action.TargetPath == "" {
		return fmt.Errorf("target_path is required for generate_document action")
	}

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	// Parse target path
	parentPath, filename := h.parseTargetPath(targetPath)
	filename = h.sanitizeFileName(filename)

	// Replace placeholders in filename using shared function that supports nested placeholders
	filename = ReplacePlaceholders(filename, params.PlaceholderData)
	filename = h.sanitizeFileName(filename) // Sanitize again after placeholder replacement

	// Resolve target parent path using autoDocService
	targetParentResult, err := h.autoDocService.ResolveTargetParentPath(ctx, params.TenantID, parentPath, params.PlaceholderData)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve target parent path=%s", parentPath)
		return fmt.Errorf("failed to resolve target parent path: %w", err)
	}

	// Resolve source path in AutoDocRoot
	sourceResult, err := h.resolvePathInAutoDocRoot(ctx, params.TenantID, sourcePath)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve source path=%s", sourcePath)
		return fmt.Errorf("failed to resolve source path: %w", err)
	}

	// Get source document
	sourceDoc, err := h.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", sourceResult.DocumentID),
			model.NewFilterE("tenant_id", params.TenantID),
		},
	})
	if err != nil {
		log.WithError(err).Errorf("Failed to find source document id=%d", sourceResult.DocumentID)
		return fmt.Errorf("failed to find source document: %w", err)
	}

	// Get source and target providers
	sourceProvider := params.Action.GetProvider()       // Default to "internal" if empty
	targetProvider := params.Action.GetTargetProvider() // May be empty, will be resolved by engine

	// If target provider is empty, use internal as fallback (should not happen after engine resolution)
	if targetProvider == "" {
		targetProvider = "internal"
		log.Warnf("Target provider is empty, using internal fallback")
	}

	log.Infof("Using providers: source=%s target=%s", sourceProvider, targetProvider)

	// Generate document based on target provider
	if targetProvider == "internal" {
		return h.generateToInternalProvider(ctx, sourceDoc, targetParentResult, filename, params)
	}

	// Generate to external provider (gdrive, sharepoint)
	return h.generateToExternalProvider(ctx, sourceDoc, targetParentResult, filename, targetProvider, params)
}

// Test validates the generate_document action configuration
func (h *GenerateDocumentHandler) Test(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error) {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.Test")
	log.Infof("Testing generate_document action tenant_id=%d", params.TenantID)

	result := &ActionTestResult{
		ActionType: "generate_document",
		SourcePath: params.Action.SourcePath,
		TargetPath: params.Action.TargetPath,
		Success:    true,
	}

	// Validate required fields
	if params.Action.SourcePath == "" {
		result.Success = false
		result.Error = "source_path is required"
		return result, nil
	}
	if params.Action.TargetPath == "" {
		result.Success = false
		result.Error = "target_path is required"
		return result, nil
	}

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	// Replace placeholders in filename using shared function that supports nested placeholders
	_, filename := h.parseTargetPath(targetPath)
	filename = ReplacePlaceholders(filename, params.PlaceholderData)
	filename = h.sanitizeFileName(filename)

	result.ResolvedPath = fmt.Sprintf("%s -> %s (filename: %s)", sourcePath, targetPath, filename)

	log.Infof("Testing generate_document action source=%s target=%s tenant_id=%d",
		sourcePath, targetPath, params.TenantID)

	return result, nil
}

// resolvePathInAutoDocRoot resolves a full path within the AutoDocRoot
func (h *GenerateDocumentHandler) resolvePathInAutoDocRoot(ctx context.Context, tenantID uint64, path string) (*PathResolutionResult, error) {
	// Create path traverser
	pathTraverser := NewPathTraverser(h.documentRepo)

	// Use PathTraverser to resolve the full path in AutoDocRoot
	return pathTraverser.TraversePathInAutoDocRoot(ctx, tenantID, path, h.autoDocService)
}

// parseTargetPath parses a target path into parent path and filename
func (h *GenerateDocumentHandler) parseTargetPath(targetPath string) (parentPath, filename string) {
	parts := strings.Split(targetPath, "/")
	if len(parts) == 1 {
		return "", parts[0]
	}
	return strings.Join(parts[:len(parts)-1], "/"), parts[len(parts)-1]
}

// sanitizeFileName removes invalid characters from filename
func (h *GenerateDocumentHandler) sanitizeFileName(filename string) string {
	if filename == "" {
		return "generated_document"
	}

	// Replace invalid characters
	replacements := map[string]string{
		"/":  "_",
		"\\": "_",
		":":  "_",
		"*":  "_",
		"?":  "_",
		"\"": "_",
		"<":  "_",
		">":  "_",
		"|":  "_",
	}

	result := filename
	for invalid, replacement := range replacements {
		result = strings.ReplaceAll(result, invalid, replacement)
	}

	return strings.TrimSpace(result)
}

// generateToInternalProvider generates document to internal storage (existing logic)
func (h *GenerateDocumentHandler) generateToInternalProvider(ctx context.Context, sourceDoc *model.Document, targetParent *TargetParentResult, filename string, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.generateToInternalProvider")

	// For internal provider, adjust target parent for object contexts
	// In internal provider, client/matter folders are virtual groupings
	// Documents should be created at root level (parent_id=0) with object context
	adjustedTargetParent := targetParent
	if targetParent.ObjectType > 0 && targetParent.ObjectID > 0 && targetParent.ParentID > 0 {
		// This is an object context (client_folder/matter_folder) with a folder parent
		// For internal provider, create documents at root level with object context
		adjustedTargetParent = &TargetParentResult{
			ParentID:   0, // Root level for internal provider
			ObjectType: targetParent.ObjectType,
			ObjectID:   targetParent.ObjectID,
			TenantID:   targetParent.TenantID,
		}
		log.Infof("Internal provider: adjusted object context to parent_id=0 for object_type=%d object_id=%d",
			targetParent.ObjectType, targetParent.ObjectID)
	}

	// Use existing internal generation logic
	generatedDoc, err := h.autoDocService.GenerateFromTemplate(
		ctx,
		sourceDoc,
		adjustedTargetParent,
		filename,
		params.PlaceholderData,
		params.Action.Override,
	)
	if err != nil {
		log.WithError(err).Error("Failed to generate document from template")
		return fmt.Errorf("failed to generate document from template: %w", err)
	}

	// Check if document was skipped due to override=false
	if generatedDoc == nil {
		log.WithFields(map[string]interface{}{
			"filename":    filename,
			"override":    params.Action.Override,
			"parent_id":   targetParent.ParentID,
		}).Info("Document generation skipped due to existing file and override=false")
		return nil
	}

	log.Infof("Successfully generated document internally document_id=%d filename=%s parent_id=%d",
		generatedDoc.ID, filename, targetParent.ParentID)

	return nil
}

// generateToExternalProvider generates document to external provider (gdrive, sharepoint)
func (h *GenerateDocumentHandler) generateToExternalProvider(ctx context.Context, sourceDoc *model.Document, targetParent *TargetParentResult, filename string, targetProvider string, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.generateToExternalProvider")

	log.Infof("Generating document to external provider=%s filename=%s", targetProvider, filename)

	// Get target document service (for future implementation)
	_, err := h.documentRegistry.GetProvider(targetProvider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get target document service provider=%s", targetProvider)
		return fmt.Errorf("failed to get target document service for provider %s: %w", targetProvider, err)
	}

	// Step 1: Get template content from source provider
	log.Infof("Getting template content from source provider document_id=%d", sourceDoc.ID)
	sourceDocService, err := h.documentRegistry.GetProvider("internal")
	if err != nil {
		log.WithError(err).Error("Failed to get source document service")
		return fmt.Errorf("failed to get source document service: %w", err)
	}

	templateContent, err := sourceDocService.GetDocumentContent(ctx, sourceDoc.ID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get template content document_id=%d", sourceDoc.ID)
		return fmt.Errorf("failed to get template content: %w", err)
	}

	log.Infof("Template content retrieved, size=%d bytes", len(templateContent))

	// Step 2: Process template with placeholders
	log.Infof("Processing template with placeholders")
	processedContent, err := h.autoDocService.ProcessDocxTemplate(ctx, templateContent, params.PlaceholderData)
	if err != nil {
		log.WithError(err).Errorf("Failed to process template document_id=%d", sourceDoc.ID)
		return fmt.Errorf("failed to process template: %w", err)
	}

	log.Infof("Template processed successfully, output size=%d bytes", len(processedContent))

	// Step 3: Upload to external provider
	log.Infof("Uploading processed document to provider=%s filename=%s content_size=%d",
		targetProvider, filename, len(processedContent))

	switch targetProvider {
	case "gdrive":
		return h.uploadToGoogleDrive(ctx, processedContent, filename, targetParent, params)
	case "sharepoint":
		return h.uploadToSharePoint(ctx, processedContent, filename, targetParent, params)
	default:
		return fmt.Errorf("unsupported target provider: %s", targetProvider)
	}
}

// uploadToGoogleDrive uploads processed document content to Google Drive
func (h *GenerateDocumentHandler) uploadToGoogleDrive(ctx context.Context, content []byte, filename string, targetParent *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.uploadToGoogleDrive")

	log.Infof("Uploading to Google Drive filename=%s content_size=%d", filename, len(content))

	// Get GDrive document service and cast to GDrive service type
	baseService, err := h.documentRegistry.GetProvider("gdrive")
	if err != nil {
		log.WithError(err).Error("Failed to get GDrive document service")
		return fmt.Errorf("failed to get GDrive document service: %w", err)
	}

	// Cast to GDrive adapter to access wrapped GDrive service
	gdriveAdapter, ok := baseService.(*gdriveAdapter)
	if !ok {
		log.Error("Failed to cast to GDrive adapter")
		return fmt.Errorf("failed to cast to GDrive adapter")
	}

	// Get the wrapped GDrive service from adapter
	gdriveService := gdriveAdapter.gdriveDocService

	// Resolve parent folder ID for GDrive
	parentDriveID, err := h.resolveGDriveParentID(ctx, targetParent, params.TenantID, gdriveService)
	if err != nil {
		log.WithError(err).Error("Failed to resolve GDrive parent folder ID")
		return fmt.Errorf("failed to resolve GDrive parent folder ID: %w", err)
	}

	log.Infof("Resolved GDrive parent folder parent_drive_id=%s", parentDriveID)

	// Use 2-step upload process (matching GDrive service pattern)
	// Step 1: Create upload session
	sessionReq := &gdrive.CreateUploadSessionRequest{
		TenantID:      params.TenantID,
		FileName:      filename,
		ParentDriveID: parentDriveID,
	}

	session, err := gdriveService.CreateUploadSession(ctx, sessionReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to create upload session filename=%s", filename)
		return fmt.Errorf("failed to create upload session: %w", err)
	}

	log.Infof("Upload session created session_token=%s", session.SessionToken)

	// Step 2: Upload file content
	contentReader := bytes.NewReader(content)
	contentType := "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	contentLength := int64(len(content))

	uploadResult, err := gdriveService.UploadFileContent(ctx, session.SessionToken, contentReader, contentType, contentLength)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload file content session_token=%s", session.SessionToken)
		return fmt.Errorf("failed to upload file content: %w", err)
	}

	log.Infof("Successfully uploaded to Google Drive drive_file_id=%s filename=%s file_size=%d",
		uploadResult.DriveFileID, uploadResult.FileName, uploadResult.FileSize)

	return nil
}

// resolveGDriveParentID resolves target parent to Google Drive folder ID using shared logic
func (h *GenerateDocumentHandler) resolveGDriveParentID(ctx context.Context, targetParent *TargetParentResult, tenantID uint64, gdriveService gdrive.DocumentService) (string, error) {
	return ResolveGDriveParentID(ctx, targetParent, tenantID, gdriveService, h.mappingService)
}

// uploadToSharePoint uploads processed document content to SharePoint (placeholder)
func (h *GenerateDocumentHandler) uploadToSharePoint(ctx context.Context, content []byte, filename string, targetParent *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "GenerateDocumentHandler.uploadToSharePoint")

	log.Infof("SharePoint upload not yet implemented filename=%s content_size=%d", filename, len(content))
	return fmt.Errorf("SharePoint upload not yet implemented")
}
