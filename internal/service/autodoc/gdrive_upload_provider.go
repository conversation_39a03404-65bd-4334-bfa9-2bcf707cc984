package autodoc

import (
	"context"
	"fmt"
	"net/http"

	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/bilabllog"

	"gitlab.com/goxp/cloud0/ginext"
)

// GDriveUploadProvider handles uploads for Google Drive
type GDriveUploadProvider struct {
	documentService DocumentService
	gdriveService   gdrive.DocumentService
	serviceURL      string // Base service URL for upload endpoints
}

// NewGDriveUploadProvider creates a new Google Drive upload provider
func NewGDriveUploadProvider(documentService DocumentService, gdriveService gdrive.DocumentService, serviceURL string) UploadProvider {
	return &GDriveUploadProvider{
		documentService: documentService,
		gdriveService:   gdriveService,
		serviceURL:      serviceURL,
	}
}

// GetProviderName returns the provider name
func (p *GDriveUploadProvider) GetProviderName() string {
	return "gdrive"
}

// CreateUploadSession creates an upload session for Google Drive
func (p *GDriveUploadProvider) CreateUploadSession(ctx context.Context, req *CreateUploadSessionRequest) (*CreateUploadSessionResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Creating upload session for Google Drive provider")

	// Validate request
	if err := p.validateCreateUploadSessionRequest(req); err != nil {
		return nil, err
	}

	// Resolve parent Drive ID
	parentDriveID, err := p.resolveParentDriveID(ctx, req)
	if err != nil {
		log.WithError(err).Error("Failed to resolve parent Drive ID")
		return nil, fmt.Errorf("failed to resolve parent Drive ID: %w", err)
	}

	// Create upload session using Google Drive service
	sessionReq := &gdrive.CreateUploadSessionRequest{
		TenantID:      req.TenantID,
		FileName:      req.FileName,
		ParentDriveID: parentDriveID,
	}

	sessionResult, err := p.gdriveService.CreateUploadSession(ctx, sessionReq)
	if err != nil {
		log.WithError(err).Error("Failed to create Google Drive upload session")
		return nil, fmt.Errorf("failed to create Google Drive upload session: %w", err)
	}

	// Build upload URL for our service (proxy to Google Drive)
	uploadURL := fmt.Sprintf("%s/v3/gdrive/upload/%s", p.serviceURL, sessionResult.SessionToken)

	response := &CreateUploadSessionResponse{
		UploadURL:    uploadURL,
		SessionToken: sessionResult.SessionToken,
		Key:          sessionResult.SessionToken, // For Google Drive, session token can be used as key
		ExpiresAt:    sessionResult.ExpiresAt,
		Provider:     p.GetProviderName(),
		Message:      "Upload file to the provided URL, then call register endpoint with the key",
	}

	log.WithField("session_token", sessionResult.SessionToken).WithField("upload_url", uploadURL).Info("Upload session created for Google Drive provider")
	return response, nil
}

// RegisterUpload registers the uploaded file for Google Drive
func (p *GDriveUploadProvider) RegisterUpload(ctx context.Context, req *RegisterUploadRequest) (*RegisterUploadResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Google Drive register upload - file already uploaded in step 2, no action needed")

	// For Google Drive, file is already uploaded in step 2
	// Register step doesn't need to do anything - just return success
	log.WithField("session_token", req.SessionToken).Info("Google Drive upload already completed - register step is no-op")

	// Return a simple success response
	return &RegisterUploadResponse{
		ID:       0, // No internal document created
		Name:     "Google Drive File",
		Size:     0,
		Provider: "gdrive",
		Message:  "Google Drive upload already completed",
	}, nil
}

// resolveParentDriveID resolves the parent Drive ID for upload
func (p *GDriveUploadProvider) resolveParentDriveID(ctx context.Context, req *CreateUploadSessionRequest) (string, error) {
	// If external parent ID is provided, use it directly
	if req.ExternalParentID != "" {
		return req.ExternalParentID, nil
	}

	// If internal parent ID is provided, resolve via mapping service
	if req.ParentID != 0 {
		// TODO: Implement mapping service call to resolve internal ID to external ID
		// For now, return error as this requires object type context
		return "", fmt.Errorf("resolving internal parent ID requires object type context - use ExternalParentID instead")
	}

	// No parent specified - use configured Google Drive root folder
	// Get tenant's Google Drive configuration to resolve root folder
	return p.getConfiguredRootFolder(ctx, req.TenantID)
}

// validateCreateUploadSessionRequest validates the create upload session request
func (p *GDriveUploadProvider) validateCreateUploadSessionRequest(req *CreateUploadSessionRequest) error {
	if req == nil {
		return ginext.NewError(http.StatusBadRequest, "request cannot be nil")
	}
	if req.TenantID == 0 {
		return ginext.NewError(http.StatusBadRequest, "tenant_id is required")
	}
	if req.FileName == "" {
		return ginext.NewError(http.StatusBadRequest, "file_name is required")
	}
	if req.CreatedUser == 0 {
		return ginext.NewError(http.StatusBadRequest, "created_user is required")
	}

	// For Google Drive, ExternalParentID is preferred but not required
	// If no parent is specified, upload will go to the configured root folder

	return nil
}



// getConfiguredRootFolder gets the configured Google Drive root folder for a tenant
func (p *GDriveUploadProvider) getConfiguredRootFolder(ctx context.Context, tenantID uint64) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Use the Google Drive document service to get tenant configuration
	config, err := p.gdriveService.GetTenantConfig(ctx, tenantID)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).Error("Failed to get Google Drive configuration")
		return "", fmt.Errorf("Google Drive configuration not found for tenant %d: %w", tenantID, err)
	}

	if config.RootID == "" {
		log.WithField("tenant_id", tenantID).Error("No root folder configured for tenant")
		return "", fmt.Errorf("Google Drive root folder not configured for tenant %d - please configure a Shared Drive", tenantID)
	}

	// Validate that configured root is suitable for Service Account uploads
	gdriveClient := p.gdriveService.GetGDriveClient()
	if err := gdriveClient.ValidateParentForServiceAccount(config.RootID); err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).WithField("root_id", config.RootID).Error("Configured root folder is not suitable for Service Account uploads")
		return "", fmt.Errorf("configured root folder for tenant %d is not suitable for Service Account uploads: %w", tenantID, err)
	}

	log.WithField("tenant_id", tenantID).WithField("root_id", config.RootID).Info("Using validated configured root folder")
	return config.RootID, nil
}
