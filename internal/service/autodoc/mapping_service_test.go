package autodoc

import (
	"context"
	"testing"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/mocks/repositories"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

func TestNewMappingService(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewNoOpMappingCache()

	service := NewMappingService(mockRepo, cache)
	assert.NotNil(t, service)
}

func TestMappingService_CreateMapping(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewInMemoryMappingCache(time.Hour)
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	req := &CreateMappingRequest{
		TenantID:       1,
		Type:           model.DocTypeClient,
		ObjectID:       123,
		ParentObjectID: 0,
		ExternalID:     "gdrive-file-123",
		ParentDriveID:  "gdrive-parent-123",
		Provider:       "gdrive",
	}

	expectedMapping := &model.DocumentMapping{
		TenantID:       1,
		Type:           model.DocTypeClient,
		ObjectID:       123,
		ParentObjectID: 0,
		DriveID:        "gdrive-file-123",
		ParentDriveID:  "gdrive-parent-123",
		Provider:       "gdrive",
	}

	// Mock repository calls for conflict checking
	// First call: GetExternalID (4 filters: object_id, type, provider, tenant_id)
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 4 // object_id, type, provider, tenant_id
	})).Return(nil, gorm.ErrRecordNotFound).Once()

	// Second call: GetInternalID (3 filters: external_id, provider, tenant_id)
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 3 // external_id, provider, tenant_id
	})).Return(nil, gorm.ErrRecordNotFound).Once()

	// Mock repository call for creation
	mockRepo.On("CreateOrUpdate", ctx, mock.MatchedBy(func(mapping *model.DocumentMapping) bool {
		return mapping.ObjectID == 123 && mapping.DriveID == "gdrive-file-123"
	})).Return(nil)

	// Execute
	result, err := service.CreateMapping(ctx, req)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedMapping.ObjectID, result.ObjectID)
	assert.Equal(t, expectedMapping.DriveID, result.DriveID)
	assert.Equal(t, expectedMapping.Provider, result.Provider)

	mockRepo.AssertExpectations(t)
}

func TestMappingService_CreateMapping_ValidationError(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewNoOpMappingCache()
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	tests := []struct {
		name string
		req  *CreateMappingRequest
	}{
		{
			name: "empty tenant_id",
			req: &CreateMappingRequest{
				TenantID:   0,
				Type:       model.DocTypeClient,
				ObjectID:   123,
				ExternalID: "gdrive-file-123",
				Provider:   "gdrive",
			},
		},
		{
			name: "empty type",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       "",
				ObjectID:   123,
				ExternalID: "gdrive-file-123",
				Provider:   "gdrive",
			},
		},
		{
			name: "empty object_id",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       model.DocTypeClient,
				ObjectID:   0,
				ExternalID: "gdrive-file-123",
				Provider:   "gdrive",
			},
		},
		{
			name: "empty external_id",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       model.DocTypeClient,
				ObjectID:   123,
				ExternalID: "",
				Provider:   "gdrive",
			},
		},
		{
			name: "empty provider",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       model.DocTypeClient,
				ObjectID:   123,
				ExternalID: "gdrive-file-123",
				Provider:   "",
			},
		},
		{
			name: "invalid provider",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       model.DocTypeClient,
				ObjectID:   123,
				ExternalID: "gdrive-file-123",
				Provider:   "invalid",
			},
		},
		{
			name: "invalid type",
			req: &CreateMappingRequest{
				TenantID:   1,
				Type:       "invalid",
				ObjectID:   123,
				ExternalID: "gdrive-file-123",
				Provider:   "gdrive",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.CreateMapping(ctx, tt.req)
			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), "invalid create mapping request")
		})
	}
}

func TestMappingService_GetInternalID(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewInMemoryMappingCache(time.Hour)
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	externalID := "gdrive-file-123"
	provider := "gdrive"
	tenantID := uint64(1)
	expectedInternalID := uint64(123)

	expectedMapping := &model.DocumentMapping{
		ObjectID: expectedInternalID,
		DriveID:  externalID,
		Provider: provider,
		TenantID: tenantID,
	}

	// Mock repository call
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 3 // drive_id, provider, tenant_id (GetMappingByExternalID doesn't use object_type)
	})).Return(expectedMapping, nil)

	// Execute
	result, err := service.GetInternalID(ctx, externalID, provider, tenantID)

	// Verify
	assert.NoError(t, err)
	assert.Equal(t, expectedInternalID, result)

	// Test cache hit
	result2, err2 := service.GetInternalID(ctx, externalID, provider, tenantID)
	assert.NoError(t, err2)
	assert.Equal(t, expectedInternalID, result2)

	mockRepo.AssertExpectations(t)
}

func TestMappingService_GetExternalID(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewInMemoryMappingCache(time.Hour)
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	internalID := uint64(123)
	provider := "gdrive"
	tenantID := uint64(1)
	expectedExternalID := "gdrive-file-123"

	expectedMapping := &model.DocumentMapping{
		ObjectID: internalID,
		DriveID:  expectedExternalID,
		Provider: provider,
		TenantID: tenantID,
	}

	// Mock repository call
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 4 // object_id, type, provider, tenant_id
	})).Return(expectedMapping, nil)

	// Execute
	result, err := service.GetExternalID(ctx, internalID, "client", provider, tenantID)

	// Verify
	assert.NoError(t, err)
	assert.Equal(t, expectedExternalID, result)

	// Test cache hit
	result2, err2 := service.GetExternalID(ctx, internalID, "client", provider, tenantID)
	assert.NoError(t, err2)
	assert.Equal(t, expectedExternalID, result2)

	mockRepo.AssertExpectations(t)
}

func TestMappingService_GetMapping(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewInMemoryMappingCache(time.Hour)
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	internalID := uint64(123)
	provider := "gdrive"
	tenantID := uint64(1)

	expectedMapping := &model.DocumentMapping{
		ObjectID: internalID,
		DriveID:  "gdrive-file-123",
		Provider: provider,
		TenantID: tenantID,
		Type:     model.DocTypeClient,
	}

	// Mock repository call
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 4 // object_id, type, provider, tenant_id
	})).Return(expectedMapping, nil)

	// Execute
	result, err := service.GetMapping(ctx, internalID, "client", provider, tenantID)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedMapping.ObjectID, result.ObjectID)
	assert.Equal(t, expectedMapping.DriveID, result.DriveID)
	assert.Equal(t, expectedMapping.Provider, result.Provider)

	mockRepo.AssertExpectations(t)
}

func TestMappingService_GetMappingByExternalID(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewInMemoryMappingCache(time.Hour)
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	externalID := "gdrive-file-123"
	provider := "gdrive"
	tenantID := uint64(1)

	expectedMapping := &model.DocumentMapping{
		ObjectID: 123,
		DriveID:  externalID,
		Provider: provider,
		TenantID: tenantID,
		Type:     model.DocTypeClient,
	}

	// Mock repository call
	mockRepo.On("FindOne", ctx, mock.MatchedBy(func(query *model.Query) bool {
		return len(query.Filters) == 3 // drive_id, provider, tenant_id (GetMappingByExternalID doesn't use object_type)
	})).Return(expectedMapping, nil)

	// Execute
	result, err := service.GetMappingByExternalID(ctx, externalID, provider, tenantID)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedMapping.ObjectID, result.ObjectID)
	assert.Equal(t, expectedMapping.DriveID, result.DriveID)
	assert.Equal(t, expectedMapping.Provider, result.Provider)

	mockRepo.AssertExpectations(t)
}

func TestMappingService_ValidateMapping(t *testing.T) {
	mockRepo := &repositories.MockDocumentMappingRepository{}
	cache := NewNoOpMappingCache()
	service := NewMappingService(mockRepo, cache)
	ctx := context.Background()

	tests := []struct {
		name    string
		mapping *model.DocumentMapping
		wantErr bool
	}{
		{
			name: "valid mapping",
			mapping: &model.DocumentMapping{
				TenantID: 1,
				ObjectID: 123,
				DriveID:  "gdrive-file-123",
				Provider: "gdrive",
				Type:     model.DocTypeClient,
			},
			wantErr: false,
		},
		{
			name: "missing tenant_id",
			mapping: &model.DocumentMapping{
				ObjectID: 123,
				DriveID:  "gdrive-file-123",
				Provider: "gdrive",
				Type:     model.DocTypeClient,
			},
			wantErr: true,
		},
		{
			name: "missing object_id",
			mapping: &model.DocumentMapping{
				TenantID: 1,
				DriveID:  "gdrive-file-123",
				Provider: "gdrive",
				Type:     model.DocTypeClient,
			},
			wantErr: true,
		},
		{
			name: "missing drive_id",
			mapping: &model.DocumentMapping{
				TenantID: 1,
				ObjectID: 123,
				Provider: "gdrive",
				Type:     model.DocTypeClient,
			},
			wantErr: true,
		},
		{
			name: "missing provider",
			mapping: &model.DocumentMapping{
				TenantID: 1,
				ObjectID: 123,
				DriveID:  "gdrive-file-123",
				Type:     model.DocTypeClient,
			},
			wantErr: true,
		},
		{
			name: "missing type",
			mapping: &model.DocumentMapping{
				TenantID: 1,
				ObjectID: 123,
				DriveID:  "gdrive-file-123",
				Provider: "gdrive",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock conflict checking calls for valid mappings
			if !tt.wantErr {
				mockRepo.On("FindOne", ctx, mock.Anything).Return(nil, gorm.ErrRecordNotFound).Twice()
			}

			err := service.ValidateMapping(ctx, tt.mapping)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
