package autodoc

import (
	"context"
	"testing"

	"code.mybil.net/gophers/gokit/components/entity"
	"github.com/stretchr/testify/assert"
)

// Test the variable definitions without external dependencies
func TestVariableDefinitions(t *testing.T) {
	t.Run("client variables", func(t *testing.T) {
		assert.NotEmpty(t, clientVariables, "Client variables should not be empty")

		// Check for required client variables
		var foundClientName, foundClientID bool
		for _, v := range clientVariables {
			assert.NotEmpty(t, v.Name, "Variable name should not be empty")
			assert.NotEmpty(t, v.Description, "Variable description should not be empty")
			assert.NotEmpty(t, v.Category, "Variable category should not be empty")

			switch v.Name {
			case "client.name":
				foundClientName = true
				assert.Equal(t, "client", v.Category)
			case "client.id":
				foundClientID = true
				assert.Equal(t, "client", v.Category)
			}
		}
		assert.True(t, foundClientName, "Should include client.name variable")
		assert.True(t, foundClientID, "Should include client.id variable")
	})

	t.Run("matter variables", func(t *testing.T) {
		assert.NotEmpty(t, matterVariables, "Matter variables should not be empty")

		// Check for required matter variables
		var foundMatterName, foundMatterID bool
		for _, v := range matterVariables {
			assert.NotEmpty(t, v.Name, "Variable name should not be empty")
			assert.NotEmpty(t, v.Description, "Variable description should not be empty")
			assert.NotEmpty(t, v.Category, "Variable category should not be empty")

			switch v.Name {
			case "matter.name":
				foundMatterName = true
				assert.Equal(t, "matter", v.Category)
			case "matter.id":
				foundMatterID = true
				assert.Equal(t, "matter", v.Category)
			}
		}
		assert.True(t, foundMatterName, "Should include matter.name variable")
		assert.True(t, foundMatterID, "Should include matter.id variable")
	})

	t.Run("special variables in client context", func(t *testing.T) {
		// Check for special variables within client variables
		var foundTimestamp, foundClientFolder bool
		for _, v := range clientVariables {
			switch v.Name {
			case "timestamp":
				foundTimestamp = true
				assert.Equal(t, "timestamp", v.Category)
			case "client_folder":
				foundClientFolder = true
				assert.Equal(t, "special", v.Category)
			}
		}
		assert.True(t, foundTimestamp, "Client variables should include timestamp variable")
		assert.True(t, foundClientFolder, "Client variables should include client_folder variable")
	})

	t.Run("special variables in matter context", func(t *testing.T) {
		// Check for special variables within matter variables
		var foundTimestamp, foundMatterFolder, foundClientFolder bool
		for _, v := range matterVariables {
			switch v.Name {
			case "timestamp":
				foundTimestamp = true
				assert.Equal(t, "timestamp", v.Category)
			case "matter_folder":
				foundMatterFolder = true
				assert.Equal(t, "special", v.Category)
			case "client_folder":
				foundClientFolder = true
				assert.Equal(t, "special", v.Category)
			}
		}
		assert.True(t, foundTimestamp, "Matter variables should include timestamp variable")
		assert.True(t, foundMatterFolder, "Matter variables should include matter_folder variable")
		assert.True(t, foundClientFolder, "Matter variables should include client_folder variable")
	})
}

// Test schema-only mode (when objectID is nil)
func TestVariableService_SchemaOnlyMode(t *testing.T) {
	// Create a simple mock entity fetcher for schema-only tests
	mockEntityFetcher := &mockEntityFetcher{}

	// Create service with nil clients since they won't be used in schema-only mode
	service := &variableService{
		clientClient:  nil,
		matterClient:  nil,
		entityFetcher: mockEntityFetcher,
	}

	t.Run("client variables schema-only", func(t *testing.T) {
		variables, err := service.GetAvailableVariables(context.Background(), 1, nil, 1)

		assert.NoError(t, err)
		assert.NotEmpty(t, variables)

		// All values should be nil in schema-only mode
		for _, v := range variables {
			assert.Nil(t, v.Value, "Variable %s should have nil value in schema-only mode", v.Name)
			assert.NotEmpty(t, v.Name)
			assert.NotEmpty(t, v.Description)
			assert.NotEmpty(t, v.Category)
		}

		// Check for specific client variables
		var foundClientName, foundClientFolder, foundTimestamp bool
		for _, v := range variables {
			switch v.Name {
			case "client.name":
				foundClientName = true
				assert.Equal(t, "client", v.Category)
			case "client_folder":
				foundClientFolder = true
				assert.Equal(t, "special", v.Category)
			case "timestamp":
				foundTimestamp = true
				assert.Equal(t, "timestamp", v.Category)
			}
		}
		assert.True(t, foundClientName, "Should include client.name variable")
		assert.True(t, foundClientFolder, "Should include client_folder variable")
		assert.True(t, foundTimestamp, "Should include timestamp variable")
	})

	t.Run("matter variables schema-only", func(t *testing.T) {
		variables, err := service.GetAvailableVariables(context.Background(), 3, nil, 1)

		assert.NoError(t, err)
		assert.NotEmpty(t, variables)

		// All values should be nil in schema-only mode
		for _, v := range variables {
			assert.Nil(t, v.Value, "Variable %s should have nil value in schema-only mode", v.Name)
		}

		// Check for specific matter variables
		var foundMatterName, foundClientName, foundMatterFolder bool
		for _, v := range variables {
			switch v.Name {
			case "matter.name":
				foundMatterName = true
				assert.Equal(t, "matter", v.Category)
			case "client.name":
				foundClientName = true
				assert.Equal(t, "client", v.Category)
			case "matter_folder":
				foundMatterFolder = true
				assert.Equal(t, "special", v.Category)
			}
		}
		assert.True(t, foundMatterName, "Should include matter.name variable")
		assert.True(t, foundClientName, "Should include client.name variable for matter context")
		assert.True(t, foundMatterFolder, "Should include matter_folder variable")
	})

	t.Run("invalid object type", func(t *testing.T) {
		variables, err := service.GetAvailableVariables(context.Background(), 999, nil, 1)

		assert.Error(t, err)
		assert.Empty(t, variables)
	})
}

// Simple mock entity fetcher for testing
type mockEntityFetcher struct{}

func (m *mockEntityFetcher) FetchCache(ctx context.Context, kind entity.Kind, id entity.ID) (*entity.Generic, error) {
	return nil, nil // Not used in schema-only mode
}

func (m *mockEntityFetcher) Fetch(ctx context.Context, kind entity.Kind, id entity.ID) (*entity.Generic, error) {
	return nil, nil // Not used in schema-only mode
}
