package autodoc

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"bilabl/docman/pkg/bilabllog"

	"code.mybil.net/gophers/gokit/clients"
	_ "code.mybil.net/gophers/gokit/clients/client" // Register
	"code.mybil.net/gophers/gokit/util/jsonutil"
)

// ClientEventConsumer handles client-related events for automation
type ClientEventConsumer interface {
	// ConsumeClientCreated processes client.create events
	ConsumeClientCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error

	// ConsumeClientUpdated processes client.update events
	ConsumeClientUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error
}

// clientEventConsumer implements ClientEventConsumer
type clientEventConsumer struct {
	ruleMatchingService RuleMatchingService
	ruleExecutionEngine RuleExecutionEngine
	clientClient        clients.ClientServiceClient
}

// NewClientEventConsumer creates a new client event consumer
func NewClientEventConsumer(
	ruleMatchingService RuleMatchingService,
	ruleExecutionEngine RuleExecutionEngine,
) ClientEventConsumer {
	return &clientEventConsumer{
		ruleMatchingService: ruleMatchingService,
		ruleExecutionEngine: ruleExecutionEngine,
		clientClient:        clients.GetClient(),
	}
}

// ConsumeClientCreated processes client.create events
func (c *clientEventConsumer) ConsumeClientCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_data": eventData,
	}).Info("Processing client.create event")

	return c.processEvent(ctx, tenantID, "client.create", eventData)
}

// ConsumeClientUpdated processes client.update events
func (c *clientEventConsumer) ConsumeClientUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_data": eventData,
	}).Info("Processing client.update event")

	return c.processEvent(ctx, tenantID, "client.update", eventData)
}

// processEvent handles the common event processing logic
func (c *clientEventConsumer) processEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Find matching rules
	log.WithFields(map[string]interface{}{
		"event_type": eventType,
		"tenant_id":  tenantID,
	}).Debug("Finding matching rules for client event")

	matchedRules, err := c.ruleMatchingService.MatchRules(ctx, tenantID, eventType, eventData)
	if err != nil {
		log.WithError(err).Error("Failed to match rules for client event")
		return fmt.Errorf("failed to match rules: %w", err)
	}

	if len(matchedRules) == 0 {
		log.WithFields(map[string]interface{}{
			"event_type": eventType,
			"tenant_id":  tenantID,
		}).Info("No matching rules found for client event")
		return nil
	}

	log.Info("Found matching rules for client event", map[string]interface{}{
		"rule_count": len(matchedRules),
		"event_type": eventType,
	})

	// enrich client data
	if err := c.enrichClientData(ctx, tenantID, eventData); err != nil {
		log.
			WithField("tenant_id", tenantID).
			WithField("event_type", eventType).
			WithError(err).
			Error("Failed to enrich client data")
	}

	log.WithField("body", string(jsonutil.MustMarshal(eventData))).Debug("Enriched client data")

	// Execute each matched rule
	var executionErrors []error
	successCount := 0

	for _, rule := range matchedRules {
		log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Debug("Executing client rule")

		err := c.ruleExecutionEngine.ExecuteRule(ctx, rule.ID, eventData)
		if err != nil {
			log.Error("Failed to execute client rule", map[string]interface{}{
				"rule_id":   rule.ID,
				"rule_name": rule.Name,
				"error":     err.Error(),
			})
			executionErrors = append(executionErrors, fmt.Errorf("rule %d (%s): %w", rule.ID, rule.Name, err))
			continue
		}

		log.Info("Client rule executed successfully", map[string]interface{}{
			"rule_id":   rule.ID,
			"rule_name": rule.Name,
		})
		successCount++
	}

	// Log final results
	log.Info("Client event processing completed", map[string]interface{}{
		"total_rules":      len(matchedRules),
		"successful":       successCount,
		"failed":           len(executionErrors),
		"execution_errors": len(executionErrors),
	})

	// Return error if any rule execution failed
	if len(executionErrors) > 0 {
		return fmt.Errorf("failed to execute %d out of %d client rules: %v", len(executionErrors), len(matchedRules), executionErrors)
	}

	return nil
}

func (c *clientEventConsumer) enrichClientData(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Extract client ID from eventData - try both direct access and nested body structure
	var clientID uint64

	// First try direct access (current event structure)
	if id, ok := eventData["id"]; ok {
		if idFloat, ok := id.(float64); ok {
			clientID = uint64(idFloat)
		} else if idUint, ok := id.(uint64); ok {
			clientID = idUint
		}
	}

	// Fallback: try nested body structure (legacy support)
	if clientID == 0 {
		if body, ok := eventData["body"].(map[string]interface{}); ok {
			if id, ok := body["id"]; ok {
				if idFloat, ok := id.(float64); ok {
					clientID = uint64(idFloat)
				} else if idUint, ok := id.(uint64); ok {
					clientID = idUint
				}
			}
		}
	}

	if clientID == 0 {
		log.WithField("event_data", eventData).Debug("Client ID extraction failed - event data structure")
		return fmt.Errorf("client ID not found in event data")
	}

	log.WithField("client_id", clientID).Debug("Successfully extracted client ID from event data")

	// Create detached context for external API call to avoid HTTP request cancellation
	// Use 10 second timeout for external service calls
	apiCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Preserve important metadata for the API call
	if requestID := ctx.Value("request_id"); requestID != nil {
		apiCtx = context.WithValue(apiCtx, "request_id", requestID)
	}
	if userID := ctx.Value("user_id"); userID != nil {
		apiCtx = context.WithValue(apiCtx, "user_id", userID)
	}
	if tenantIDCtx := ctx.Value("tenant_id"); tenantIDCtx != nil {
		apiCtx = context.WithValue(apiCtx, "tenant_id", tenantIDCtx)
	}

	clientObject, err := c.clientClient.GetClient(apiCtx, clientID)
	if err != nil {
		// Check if error is due to time parsing issues
		if strings.Contains(err.Error(), "parsing time") && strings.Contains(err.Error(), "cannot parse") {
			log.WithError(err).WithField("client_id", clientID).Warn("Client API returned invalid timestamp format, attempting to fix and retry")

			// Add minimal client data to prevent path resolution issues
			// This ensures {client_folder} can still resolve correctly
			eventData["client"] = map[string]interface{}{
				"id":   clientID,
				"name": fmt.Sprintf("Client_%d", clientID), // Fallback name
			}

			log.WithField("client_id", clientID).Info("Added minimal client data due to timestamp parsing error")
			return nil
		}
		return err
	}

	// Normalize client object to support both snake_case and PascalCase field access
	normalizedClient := c.normalizeClientObject(clientObject)
	eventData["client"] = normalizedClient

	return nil
}

// normalizeClientObject converts client object to support both snake_case and PascalCase field access
func (c *clientEventConsumer) normalizeClientObject(clientObject interface{}) map[string]interface{} {
	return normalizeClientObject(clientObject)
}

// normalizeClientObject is a shared utility function to normalize client objects
// It converts client object to map and adds PascalCase aliases for template compatibility
func normalizeClientObject(clientObject interface{}) map[string]interface{} {
	normalized := make(map[string]interface{})

	// Convert client object to map using JSON marshal/unmarshal
	jsonBytes, err := json.Marshal(clientObject)
	if err != nil {
		return normalized
	}

	var clientMap map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &clientMap); err != nil {
		return normalized
	}

	// Copy all original fields
	for key, value := range clientMap {
		normalized[key] = value
	}

	// Add PascalCase aliases for common fields (for template compatibility)
	if shortName, ok := clientMap["short_name"]; ok {
		normalized["ShortName"] = shortName
	}
	if name, ok := clientMap["name"]; ok {
		normalized["Name"] = name
	}
	if code, ok := clientMap["code"]; ok {
		normalized["Code"] = code
	}
	if id, ok := clientMap["id"]; ok {
		normalized["ID"] = id
	}
	if stageText, ok := clientMap["stage_text"]; ok {
		normalized["StageText"] = stageText
	}

	return normalized
}

// ClientEventData represents the structure of client event data
type ClientEventData struct {
	Client         ClientDetails  `json:"client"`
	PreviousClient *ClientDetails `json:"previous_client,omitempty"` // For update events
}

// ClientDetails represents detailed client information in events
type ClientDetails struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Stage       string `json:"stage"`
	CompanyName string `json:"company_name"`
	ContactName string `json:"contact_name"`
	Address     string `json:"address"`
	City        string `json:"city"`
	Country     string `json:"country"`
	Industry    string `json:"industry"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}
