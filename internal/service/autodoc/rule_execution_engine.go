package autodoc

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/clients"
)

// PathResolutionResult represents the result of resolving a path
type PathResolutionResult struct {
	DocumentID  uint64 `json:"document_id"`
	DriveFileID string `json:"drive_file_id"`
	ParentID    uint64 `json:"parent_id"`
	FileName    string `json:"file_name"`
	IsFile      bool   `json:"is_file"`
}

// TestResult represents the result of rule testing
type TestResult struct {
	Success         bool                   `json:"success"`
	ActionsCount    int                    `json:"actions_count"`
	Actions         []ActionTestResult     `json:"actions"`
	Errors          []string               `json:"errors,omitempty"`
	PlaceholderData map[string]interface{} `json:"placeholder_data"`
}

// ActionTestResult represents the result of testing a single action
type ActionTestResult struct {
	ActionType   string `json:"action_type"`
	SourcePath   string `json:"source_path"`
	TargetPath   string `json:"target_path"`
	ResolvedPath string `json:"resolved_path"`
	Success      bool   `json:"success"`
	Error        string `json:"error,omitempty"`
}

// RuleExecutionEngine interface defines operations for executing automation rules
type RuleExecutionEngine interface {
	// ExecuteRule executes a rule with the provided event data
	ExecuteRule(ctx context.Context, ruleID uint64, eventData map[string]interface{}) error

	// TestRule tests a rule with sample data without actually executing actions
	TestRule(ctx context.Context, ruleID uint64, sampleData map[string]interface{}) (*TestResult, error)

	// ExtractPlaceholderData extracts placeholder data from event data
	ExtractPlaceholderData(ctx context.Context, eventData map[string]interface{}) map[string]interface{}

	// ReplacePlaceholders replaces placeholders in a string with actual data
	ReplacePlaceholders(ctx context.Context, template string, placeholderData map[string]interface{}) string
}

// ruleExecutionEngine implements the RuleExecutionEngine interface
type ruleExecutionEngine struct {
	autoDocService    AutoDocService
	documentRepo      repositories.DocumentRepository
	actionRegistry    ActionHandlerRegistry
	documentRegistry  DocumentServiceRegistry
	defaultDMSService DefaultDMSService
	mappingService    MappingService
	uploadRegistry    UploadProviderRegistry
	clientClient      clients.ClientServiceClient
}

// NewRuleExecutionEngine creates a new instance of RuleExecutionEngine
func NewRuleExecutionEngine(
	autoDocService AutoDocService,
	documentRepo repositories.DocumentRepository,
	documentRegistry DocumentServiceRegistry,
	defaultDMSService DefaultDMSService,
	mappingService MappingService,
	uploadRegistry UploadProviderRegistry,
) RuleExecutionEngine {
	// Create action handler registry
	registry := NewActionHandlerRegistry()

	// Register all action handlers
	copyFileHandler := NewCopyFileHandler(autoDocService, documentRepo, documentRegistry, mappingService, uploadRegistry)
	copyFolderHandler := NewCopyFolderHandler(autoDocService, documentRepo, documentRegistry, mappingService, uploadRegistry)
	generateDocHandler := NewGenerateDocumentHandler(autoDocService, documentRepo, documentRegistry, mappingService)

	if err := registry.RegisterHandler(copyFileHandler); err != nil {
		panic(fmt.Errorf("failed to register copy file handler: %w", err))
	}
	if err := registry.RegisterHandler(copyFolderHandler); err != nil {
		panic(fmt.Errorf("failed to register copy folder handler: %w", err))
	}
	if err := registry.RegisterHandler(generateDocHandler); err != nil {
		panic(fmt.Errorf("failed to register generate document handler: %w", err))
	}

	return &ruleExecutionEngine{
		autoDocService:    autoDocService,
		documentRepo:      documentRepo,
		actionRegistry:    registry,
		documentRegistry:  documentRegistry,
		defaultDMSService: defaultDMSService,
		mappingService:    mappingService,
		uploadRegistry:    uploadRegistry,
		clientClient:      clients.GetClient(),
	}
}

// ExecuteRule executes a rule with the provided event data
func (e *ruleExecutionEngine) ExecuteRule(ctx context.Context, ruleID uint64, eventData map[string]interface{}) (err error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Add panic recovery with detailed logging
	defer func() {
		if r := recover(); r != nil {
			log.WithFields(map[string]interface{}{
				"rule_id":     ruleID,
				"event_data":  eventData,
				"panic_value": fmt.Sprintf("%v", r),
				"panic_type":  fmt.Sprintf("%T", r),
			}).Error("Panic recovered during rule execution")

			err = fmt.Errorf("panic during rule execution (rule_id=%d): %v", ruleID, r)
		}
	}()

	log.WithField("rule_id", ruleID).WithField("event_data", eventData).Info("Starting rule execution")

	// Extract tenant ID from event data as fallback
	var tenantID uint64
	if tid, ok := eventData["tenant_id"].(uint64); ok {
		tenantID = tid
	} else if tid, ok := eventData["tenant_id"].(float64); ok {
		tenantID = uint64(tid)
	} else if tid, ok := ctx.Value("tenant_id").(uint64); ok {
		tenantID = tid
	}

	// Get the rule with explicit tenant ID
	var rule *model.DocumentAutomationRule
	if tenantID > 0 {
		rule, err = e.autoDocService.GetRuleWithTenant(ctx, ruleID, tenantID)
	} else {
		rule, err = e.autoDocService.GetRule(ctx, ruleID)
	}
	if err != nil {
		log.WithError(err).WithField("rule_id", ruleID).Error("Failed to get rule")
		return fmt.Errorf("failed to get rule: %w", err)
	}

	if !rule.IsActive {
		log.WithField("rule_id", ruleID).WithField("rule_name", rule.Name).Warn("Rule is not active")
		return fmt.Errorf("rule is not active")
	}

	log.WithField("rule_id", ruleID).WithField("rule_name", rule.Name).WithField("trigger_type", rule.TriggerType).WithField("tenant_id", rule.TenantID).Info("Rule loaded successfully")

	// Extract placeholder data from event
	var placeholderData map[string]interface{} = eventData
	if rule.TriggerType == "client.update" || rule.TriggerType == "client.create" {
		placeholderData = e.ExtractPlaceholderData(ctx, eventData)
	}
	log.Infof("Extracted placeholder data rule_id=%d placeholders=%v", ruleID, placeholderData)

	// Parse rule configuration
	actions := []model.RuleAction(rule.RuleConfig)
	if len(actions) == 0 {
		log.Warnf("Rule has no actions rule_id=%d", ruleID)
		return fmt.Errorf("rule has no actions")
	}

	log.WithField("rule_id", ruleID).WithField("actions_count", len(actions)).WithField("rule_name", rule.Name).Info("Starting action execution")

	// Execute each action
	for i, action := range actions {
		log.WithField("rule_id", ruleID).WithField("action_num", i+1).WithField("total_actions", len(actions)).WithField("action_type", action.ActionType).WithField("source_path", action.SourcePath).WithField("target_path", action.TargetPath).Info("Executing action")

		if err := e.executeAction(ctx, rule.TenantID, action, placeholderData, ruleID); err != nil {
			log.WithError(err).WithField("rule_id", ruleID).WithField("action_num", i+1).WithField("total_actions", len(actions)).WithField("action_type", action.ActionType).WithField("source_path", action.SourcePath).WithField("target_path", action.TargetPath).Error("Failed to execute action")
			return fmt.Errorf("failed to execute action %d (%s): %w", i+1, action.ActionType, err)
		}

		log.WithField("rule_id", ruleID).WithField("action_num", i+1).WithField("total_actions", len(actions)).WithField("action_type", action.ActionType).Info("Successfully executed action")
	}

	log.WithField("rule_id", ruleID).WithField("rule_name", rule.Name).WithField("actions_count", len(actions)).Info("Successfully executed all actions for rule")
	return nil
}

// TestRule tests a rule with sample data without actually executing actions
func (e *ruleExecutionEngine) TestRule(ctx context.Context, ruleID uint64, sampleData map[string]interface{}) (*TestResult, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("rule_id", ruleID).WithField("sample_data", sampleData).Info("Starting rule test")

	result := &TestResult{
		Success: true,
		Actions: []ActionTestResult{},
		Errors:  []string{},
	}

	// Extract tenant ID from sample data as fallback
	var tenantID uint64
	if tid, ok := sampleData["tenant_id"].(uint64); ok {
		tenantID = tid
	} else if tid, ok := sampleData["tenant_id"].(float64); ok {
		tenantID = uint64(tid)
	} else if tid, ok := ctx.Value("tenant_id").(uint64); ok {
		tenantID = tid
	}

	// Get the rule with explicit tenant ID
	var rule *model.DocumentAutomationRule
	var err error
	if tenantID > 0 {
		rule, err = e.autoDocService.GetRuleWithTenant(ctx, ruleID, tenantID)
	} else {
		rule, err = e.autoDocService.GetRule(ctx, ruleID)
	}
	if err != nil {
		log.WithError(err).WithField("rule_id", ruleID).Error("Failed to get rule for testing")
		result.Success = false
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to get rule: %v", err))
		return result, nil
	}

	log.WithField("rule_id", ruleID).WithField("rule_name", rule.Name).WithField("trigger_type", rule.TriggerType).WithField("is_active", rule.IsActive).Info("Rule loaded for testing")

	// Extract placeholder data from sample data
	placeholderData := e.ExtractPlaceholderData(ctx, sampleData)
	result.PlaceholderData = placeholderData

	// Parse rule configuration
	actions := []model.RuleAction(rule.RuleConfig)
	if len(actions) == 0 {
		log.Warnf("Rule has no actions rule_id=%d", ruleID)
		result.Success = false
		result.Errors = append(result.Errors, "Rule has no actions")
		return result, nil
	}

	result.ActionsCount = len(actions)

	// Test each action using action handler registry
	for _, action := range actions {
		// Create action execution parameters
		params := &ActionExecutionParams{
			TenantID:        rule.TenantID,
			Action:          action,
			PlaceholderData: placeholderData,
			RuleID:          ruleID,
		}

		// Test action using registry
		actionResult, err := e.actionRegistry.TestAction(ctx, params)
		if err != nil {
			// If registry test fails, create a basic error result
			actionResult = &ActionTestResult{
				ActionType: action.ActionType,
				SourcePath: action.SourcePath,
				TargetPath: action.TargetPath,
				Success:    false,
				Error:      err.Error(),
			}
			result.Success = false
			result.Errors = append(result.Errors, err.Error())
		}

		// If action test failed, mark overall result as failed
		if !actionResult.Success {
			result.Success = false
			if actionResult.Error != "" {
				result.Errors = append(result.Errors, actionResult.Error)
			}
		}

		result.Actions = append(result.Actions, *actionResult)
	}

	log.WithField("rule_id", ruleID).WithField("rule_name", rule.Name).WithField("success", result.Success).WithField("actions_count", len(result.Actions)).WithField("errors_count", len(result.Errors)).Info("Rule test completed")
	return result, nil
}

// ExtractPlaceholderData prepares placeholder data from event data
// It keeps the original eventData and adds special folder placeholders
func (e *ruleExecutionEngine) ExtractPlaceholderData(ctx context.Context, eventData map[string]interface{}) map[string]interface{} {
	log := bilabllog.CreateContextLogger(ctx)

	// Start with the original event data as base placeholders
	placeholders := make(map[string]interface{})
	for key, value := range eventData {
		placeholders[key] = value
	}

	log.WithField("event_data", eventData).Debug("Preparing placeholder data")

	// Extract client ID from event data - convert to string first for simplicity
	var clientID uint64
	if idValue := eventData["id"]; idValue != nil {
		idStr := fmt.Sprintf("%v", idValue)
		if parsedID, err := strconv.ParseUint(idStr, 10, 64); err == nil {
			clientID = parsedID
			placeholders["id"] = clientID
			log.Debugf("Added client id: %v", clientID)
		} else {
			log.WithField("id_value", idValue).WithError(err).Debug("Could not parse client ID from event data")
		}
	}

	// Check if client data is already enriched by Client Event Consumer
	if existingClient, ok := eventData["client"]; ok && existingClient != nil {
		log.WithField("client_id", clientID).Debug("Using already enriched client data from event consumer")
		// Client data already enriched, use it directly
		placeholders["client"] = existingClient
	} else if clientID > 0 {
		// Fetch and add detailed client data if client ID is available and not already enriched
		log.WithField("client_id", clientID).Debug("Client data not enriched, fetching from client service")
		if err := e.enrichClientData(ctx, clientID, placeholders); err != nil {
			log.WithError(err).Warn("Failed to enrich client data, continuing with basic data")
		}
	}

	// Add object context data for PathResolver (legacy support)
	if body, ok := eventData["body"].(map[string]interface{}); ok {
		// For matter events - add both client and matter data
		if matterID, ok := body["id"]; ok {
			placeholders["id"] = matterID
			log.Debugf("Added matter id: %v", matterID)

			if clientID, ok := body["client_id"]; ok {
				placeholders["client_id"] = clientID
				log.Debugf("Added client_id: %v", clientID)
			}
		}
	}

	// Add current timestamp for dynamic file naming
	placeholders["timestamp"] = time.Now().Format("20060102_150405")
	placeholders["date"] = time.Now().Format("2006-01-02")

	log.WithField("placeholders_count", len(placeholders)).Debug("Placeholder data prepared")
	return placeholders
}

// enrichClientData fetches detailed client data and adds it to placeholders
func (e *ruleExecutionEngine) enrichClientData(ctx context.Context, clientID uint64, placeholders map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Create detached context for external API call to avoid HTTP request cancellation
	// Use 10 second timeout for external service calls
	apiCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Preserve important metadata for the API call
	if requestID := ctx.Value("request_id"); requestID != nil {
		apiCtx = context.WithValue(apiCtx, "request_id", requestID)
	}
	if userID := ctx.Value("user_id"); userID != nil {
		apiCtx = context.WithValue(apiCtx, "user_id", userID)
	}
	if tenantID := ctx.Value("tenant_id"); tenantID != nil {
		apiCtx = context.WithValue(apiCtx, "tenant_id", tenantID)
	}

	// Fetch client data from client service
	clientObject, err := e.clientClient.GetClient(apiCtx, clientID)
	if err != nil {
		// Check if error is due to time parsing issues
		if strings.Contains(err.Error(), "parsing time") && strings.Contains(err.Error(), "cannot parse") {
			log.WithError(err).WithField("client_id", clientID).Warn("Client API returned invalid timestamp format, skipping client enrichment")

			// Add minimal client data to prevent path resolution issues
			placeholders["client"] = map[string]interface{}{
				"id":   clientID,
				"name": fmt.Sprintf("Client_%d", clientID), // Fallback name
			}

			log.WithField("client_id", clientID).Info("Added minimal client data due to timestamp parsing error")
			return nil
		}

		log.WithError(err).Errorf("Failed to fetch client data for ID: %d", clientID)
		return err
	}

	log.WithField("client_object", clientObject).Debug("Raw client object from service")

	// Normalize client object to support both snake_case and PascalCase field access
	normalizedClient := e.normalizeClientObject(clientObject)
	placeholders["client"] = normalizedClient

	log.WithField("client_id", clientID).WithField("normalized_client", normalizedClient).Debug("Successfully enriched client data")
	return nil
}

// normalizeClientObject normalizes client object fields to support template access patterns
func (e *ruleExecutionEngine) normalizeClientObject(clientObject interface{}) map[string]interface{} {
	return normalizeClientObject(clientObject)
}

// ReplacePlaceholders replaces placeholders in a string with actual data
// Supports nested field access using dot notation (e.g., {body.name}, {body.extra.current.stage_text})
func (e *ruleExecutionEngine) ReplacePlaceholders(ctx context.Context, template string, placeholderData map[string]interface{}) string {
	result := template

	// Replace simple placeholders first
	for key, value := range placeholderData {
		placeholder := fmt.Sprintf("{%s}", key)
		if valueStr, ok := value.(string); ok {
			result = strings.ReplaceAll(result, placeholder, valueStr)
		} else {
			result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
		}
	}

	// Handle nested field access (e.g., {body.name}, {body.extra.current.stage_text})
	result = e.replaceNestedPlaceholders(result, placeholderData)

	return result
}

// replaceNestedPlaceholders handles nested field access in placeholders
func (e *ruleExecutionEngine) replaceNestedPlaceholders(template string, data map[string]interface{}) string {
	result := template

	// Find all {...} patterns that contain dots
	placeholderPattern := `\{([^}]+\.[^}]+)\}`
	re := regexp.MustCompile(placeholderPattern)

	matches := re.FindAllStringSubmatch(template, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			fullPlaceholder := match[0] // e.g., "{body.name}"
			fieldPath := match[1]       // e.g., "body.name"

			// Extract nested value
			if value := e.extractNestedValue(fieldPath, data); value != nil {
				if valueStr, ok := value.(string); ok {
					result = strings.ReplaceAll(result, fullPlaceholder, valueStr)
				} else {
					result = strings.ReplaceAll(result, fullPlaceholder, fmt.Sprintf("%v", value))
				}
			}
		}
	}

	return result
}

// extractNestedValue extracts value from nested map using dot notation
func (e *ruleExecutionEngine) extractNestedValue(fieldPath string, data map[string]interface{}) interface{} {
	parts := strings.Split(fieldPath, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			// Last part - return the value
			return current[part]
		}

		// Navigate deeper
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil // Path not found
		}
	}

	return nil
}

// executeAction executes a single action using the action handler registry
func (e *ruleExecutionEngine) executeAction(ctx context.Context, tenantID uint64, action model.RuleAction, placeholderData map[string]interface{}, ruleID uint64) (err error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Add panic recovery with detailed action context
	defer func() {
		if r := recover(); r != nil {
			log.WithFields(map[string]interface{}{
				"rule_id":         ruleID,
				"tenant_id":       tenantID,
				"action_type":     action.ActionType,
				"source_path":     action.SourcePath,
				"target_path":     action.TargetPath,
				"provider":        action.Provider,
				"target_provider": action.TargetProvider,
				"override":        action.Override,
				"panic_value":     fmt.Sprintf("%v", r),
				"panic_type":      fmt.Sprintf("%T", r),
			}).Error("Panic recovered during action execution")

			err = fmt.Errorf("panic during action execution (rule_id=%d, action_type=%s): %v", ruleID, action.ActionType, r)
		}
	}()

	log.WithField("rule_id", ruleID).WithField("tenant_id", tenantID).WithField("action_type", action.ActionType).WithField("source_path", action.SourcePath).WithField("target_path", action.TargetPath).WithField("provider", action.Provider).WithField("target_provider", action.TargetProvider).WithField("placeholder_data", placeholderData).Debug("Executing single action")

	// Resolve source provider for this action
	provider := e.resolveProvider(ctx, action)
	log.WithField("rule_id", ruleID).WithField("action_type", action.ActionType).WithField("resolved_provider", provider).Info("Source provider resolved for action")

	// Resolve target provider for this action
	targetProvider := e.resolveTargetProvider(ctx, action, tenantID)
	log.WithField("rule_id", ruleID).WithField("action_type", action.ActionType).WithField("resolved_target_provider", targetProvider).Info("Target provider resolved for action")

	// Provider validation removed - providers will be validated during actual usage
	log.WithField("rule_id", ruleID).WithField("provider", provider).WithField("target_provider", targetProvider).Debug("Providers resolved, proceeding with action execution")

	// Create action execution parameters with resolved providers
	// Note: Providers are stored in action after resolution
	action.Provider = provider
	action.TargetProvider = targetProvider
	params := &ActionExecutionParams{
		TenantID:        tenantID,
		Action:          action,
		PlaceholderData: placeholderData,
		RuleID:          ruleID,
	}

	// Execute action using registry
	err = e.actionRegistry.ExecuteAction(ctx, params)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"rule_id":         ruleID,
			"tenant_id":       tenantID,
			"action_type":     action.ActionType,
			"source_path":     action.SourcePath,
			"target_path":     action.TargetPath,
			"provider":        action.Provider,
			"target_provider": action.TargetProvider,
			"override":        action.Override,
		}).WithError(err).Error("Action execution failed")

		return fmt.Errorf("failed to execute action %s (rule_id=%d, source=%s, target=%s): %w",
			action.ActionType, ruleID, action.SourcePath, action.TargetPath, err)
	}

	log.WithField("rule_id", ruleID).WithField("action_type", action.ActionType).Debug("Action executed successfully")

	return nil
}

// resolveProvider determines the appropriate source provider for an action
func (e *ruleExecutionEngine) resolveProvider(ctx context.Context, action model.RuleAction) string {
	log := bilabllog.CreateContextLogger(ctx)

	// If action has explicit provider, use it
	if action.Provider != "" {
		log.WithField("action_provider", action.Provider).Debug("Using explicit source provider from action")
		return action.Provider
	}

	// Default source provider resolution logic
	defaultProvider := "internal"
	log.WithField("default_provider", defaultProvider).Debug("Using default source provider")

	return defaultProvider
}

// resolveTargetProvider determines the appropriate target provider for an action
func (e *ruleExecutionEngine) resolveTargetProvider(ctx context.Context, action model.RuleAction, tenantID uint64) string {
	log := bilabllog.CreateContextLogger(ctx)

	// If action has explicit target provider, use it
	if action.TargetProvider != "" {
		log.WithField("target_provider", action.TargetProvider).Debug("Using explicit target provider from action")
		return action.TargetProvider
	}

	// Get tenant default DMS setting
	defaultProvider, err := e.defaultDMSService.GetDefaultProvider(ctx, tenantID)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).Warn("Failed to get tenant default DMS, using internal")
		defaultProvider = "internal"
	}

	log.WithField("tenant_id", tenantID).WithField("default_target_provider", defaultProvider).Debug("Using tenant default target provider")
	return defaultProvider
}


