package autodoc

import (
	"bytes"
	"context"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// CopyDocumentService interface for providers that support native copy operations
type CopyDocumentService interface {
	// CopyDocument copies a document within the same provider
	CopyDocument(ctx context.Context, req *CopyDocumentRequest) (*model.Document, error)
}

// CopyFileHandler handles copy_file actions
type CopyFileHandler struct {
	*BaseActionHandler
	autoDocService   AutoDocService
	documentRepo     repositories.DocumentRepository
	pathTraverser    *PathTraverser
	documentRegistry DocumentServiceRegistry
	mappingService   MappingService
	uploadRegistry   UploadProviderRegistry
}

// NewCopyFileHandler creates a new copy file handler
func NewCopyFileHandler(
	autoDocService AutoDocService,
	documentRepo repositories.DocumentRepository,
	documentRegistry DocumentServiceRegistry,
	mappingService MappingService,
	uploadRegistry UploadProviderRegistry,
) *CopyFileHandler {
	return &CopyFileHandler{
		BaseActionHandler: NewBaseActionHandler("copy_file"),
		autoDocService:    autoDocService,
		documentRepo:      documentRepo,
		pathTraverser:     NewPathTraverser(documentRepo),
		documentRegistry:  documentRegistry,
		mappingService:    mappingService,
		uploadRegistry:    uploadRegistry,
	}
}

// Execute executes the copy file action
func (h *CopyFileHandler) Execute(ctx context.Context, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.Execute")

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	log.Infof("Executing copy_file action source=%s target=%s source_provider=%s target_provider=%s tenant_id=%d",
		sourcePath, targetPath, params.Action.Provider, params.Action.TargetProvider, params.TenantID)

	// Resolve source path in AutoDocRoot using PathTraverser
	sourceResult, err := h.pathTraverser.TraversePathInAutoDocRoot(ctx, params.TenantID, sourcePath, h.autoDocService)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve source path=%s", sourcePath)
		return fmt.Errorf("failed to resolve source path: %w", err)
	}

	// Parse target path
	targetParentPath, targetFileName := h.parseTargetPath(targetPath)

	// Resolve target parent path using autoDocService
	targetParentResult, err := h.autoDocService.ResolveTargetParentPath(ctx, params.TenantID, targetParentPath, params.PlaceholderData)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve target parent path=%s", targetParentPath)
		return fmt.Errorf("failed to resolve target parent path: %w", err)
	}

	// Use filename from target path
	newName := targetFileName

	// Replace placeholders in new name using shared function that supports nested placeholders
	newName = ReplacePlaceholders(newName, params.PlaceholderData)
	newName = h.sanitizeFileName(newName)

	// Get source and target providers
	sourceProvider := params.Action.GetProvider()       // Default to "internal" if empty
	targetProvider := params.Action.GetTargetProvider() // May be empty, will be resolved by engine

	// If target provider is empty, use internal as fallback (should not happen after engine resolution)
	if targetProvider == "" {
		targetProvider = "internal"
		log.Warnf("Target provider is empty, using internal fallback")
	}

	log.Infof("Using providers: source=%s target=%s", sourceProvider, targetProvider)

	// Check if this is a cross-provider copy
	if sourceProvider != targetProvider {
		return h.executeCrossProviderCopy(ctx, params, sourceResult, targetParentResult, newName)
	}

	// Same provider copy - use provider-specific logic
	if sourceProvider == "internal" {
		return h.executeInternalCopy(ctx, params.TenantID, sourceResult.DocumentID, targetParentResult, newName, params.Action.Override)
	}

	// External provider copy (gdrive to gdrive, sharepoint to sharepoint)
	return h.executeExternalProviderCopy(ctx, params, sourceResult, targetParentResult, newName, sourceProvider)
}

// Test validates the copy_file action configuration
func (h *CopyFileHandler) Test(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error) {
	log := logger.WithCtx(ctx, "CopyFileHandler.Test")

	result := &ActionTestResult{
		ActionType: "copy_file",
		SourcePath: params.Action.SourcePath,
		TargetPath: params.Action.TargetPath,
		Success:    true,
	}

	// Validate required fields
	if params.Action.SourcePath == "" {
		result.Success = false
		result.Error = "source_path is required"
		return result, nil
	}
	if params.Action.TargetPath == "" {
		result.Success = false
		result.Error = "target_path is required"
		return result, nil
	}

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	result.ResolvedPath = fmt.Sprintf("%s -> %s", sourcePath, targetPath)

	log.Infof("Testing copy_file action source=%s target=%s tenant_id=%d",
		sourcePath, targetPath, params.TenantID)

	return result, nil
}

// copyFileInternalWithOverride copies a file using internal document service with override support
func (h *CopyFileHandler) copyFileInternalWithOverride(ctx context.Context, tenantID uint64, sourceDocID uint64, targetParent *TargetParentResult, newName string, override bool) error {
	log := logger.WithCtx(ctx, "copyFileInternalWithContext")

	// Get source document
	sourceDoc, err := h.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", sourceDocID),
			model.NewFilterE("tenant_id", tenantID),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to find source document: %w", err)
	}

	// Check if file with same name already exists
	existingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", targetParent.ParentID),
			model.NewFilterE("name", newName),
			model.NewFilterE("doc_type", sourceDoc.DocType),
			model.NewFilterE("object_type", targetParent.ObjectType),
			model.NewFilterE("object_id", targetParent.ObjectID),
		},
	}

	log.Infof("Checking for existing file: tenant_id=%d parent_id=%d name=%s doc_type=%d object_type=%d object_id=%d",
		tenantID, targetParent.ParentID, newName, sourceDoc.DocType, targetParent.ObjectType, targetParent.ObjectID)

	// First check if document exists to avoid misleading error logs
	exists, err := h.documentRepo.Exists(ctx, existingQuery)
	if err != nil {
		log.WithError(err).Error("Failed to check file existence")
		return fmt.Errorf("failed to check file existence: %w", err)
	}

	if exists {
		// File exists, check override setting
		existingDoc, err := h.documentRepo.FindOne(ctx, existingQuery)
		if err != nil {
			log.WithError(err).Error("Failed to retrieve existing file")
			return fmt.Errorf("failed to retrieve existing file: %w", err)
		}

		if !override {
			// Override disabled, log warning and skip
			log.WithFields(map[string]interface{}{
				"existing_file_id":   existingDoc.ID,
				"existing_file_name": existingDoc.Name,
				"source_file_name":   newName,
				"override_setting":   override,
			}).Warn("File with same name already exists, skipping copy due to override=false")
			return nil
		}

		// Override enabled, proceed with override
		log.Infof("Found existing file to override: id=%d name=%s current_key=%s new_key=%s",
			existingDoc.ID, existingDoc.Name, existingDoc.Key, sourceDoc.Key)

		updateDoc := &model.Document{
			Key:    sourceDoc.Key,
			Status: 1, // Ensure it's active
		}

		updateQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", existingDoc.ID),
				model.NewFilterE("tenant_id", tenantID),
			},
		}

		err = h.documentRepo.UpdateOne(ctx, updateQuery, updateDoc)
		if err != nil {
			return fmt.Errorf("failed to override existing file: %w", err)
		}

		log.Infof("Successfully overrode existing file id=%d name=%s key=%s",
			existingDoc.ID, existingDoc.Name, sourceDoc.Key)

		return nil
	} else {
		log.Info("No existing file found, will create new one")
	}

	// File doesn't exist, create new one
	copyDoc := &model.Document{
		TenantID:    tenantID,
		ParentID:    targetParent.ParentID,
		Name:        newName,
		DocType:     sourceDoc.DocType,
		Key:         sourceDoc.Key,           // Copy the same key
		ObjectType:  targetParent.ObjectType, // Use target parent's object type
		ObjectID:    targetParent.ObjectID,   // Use target parent's object ID
		Status:      1,                       // Active
		CreatedUser: 1,                       // System user
	}

	// Create the copy using document repository
	err = h.documentRepo.Create(ctx, copyDoc)
	if err != nil {
		return fmt.Errorf("failed to create document copy: %w", err)
	}

	log.Infof("Successfully created new document copy source_id=%d parent_id=%d object_type=%d object_id=%d name=%s",
		sourceDocID, targetParent.ParentID, targetParent.ObjectType, targetParent.ObjectID, newName)

	return nil
}

// parseTargetPath parses a target path into parent path and filename
func (h *CopyFileHandler) parseTargetPath(targetPath string) (parentPath, filename string) {
	parts := strings.Split(targetPath, "/")
	if len(parts) == 1 {
		return "", parts[0]
	}
	return strings.Join(parts[:len(parts)-1], "/"), parts[len(parts)-1]
}

// sanitizeFileName removes invalid characters from filename
func (h *CopyFileHandler) sanitizeFileName(filename string) string {
	if filename == "" {
		return "copied_file"
	}

	// Replace invalid characters
	replacements := map[string]string{
		"/":  "_",
		"\\": "_",
		":":  "_",
		"*":  "_",
		"?":  "_",
		"\"": "_",
		"<":  "_",
		">":  "_",
		"|":  "_",
	}

	result := filename
	for invalid, replacement := range replacements {
		result = strings.ReplaceAll(result, invalid, replacement)
	}

	return strings.TrimSpace(result)
}

// executeInternalCopy handles internal-to-internal copy (existing logic)
func (h *CopyFileHandler) executeInternalCopy(ctx context.Context, tenantID uint64, sourceDocID uint64, targetParent *TargetParentResult, newName string, override bool) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.executeInternalCopy")

	// For internal provider, adjust target parent for object contexts
	// In internal provider, client/matter folders are virtual groupings
	// Files should be created at root level (parent_id=0) with object context
	adjustedTargetParent := targetParent
	if targetParent.ObjectType > 0 && targetParent.ObjectID > 0 && targetParent.ParentID > 0 {
		// This is an object context (client_folder/matter_folder) with a folder parent
		// For internal provider, create files at root level with object context
		adjustedTargetParent = &TargetParentResult{
			ParentID:   0, // Root level for internal provider
			ObjectType: targetParent.ObjectType,
			ObjectID:   targetParent.ObjectID,
			TenantID:   targetParent.TenantID,
		}
		log.Infof("Internal provider: adjusted object context to parent_id=0 for object_type=%d object_id=%d",
			targetParent.ObjectType, targetParent.ObjectID)
	}

	// Use existing internal copy logic
	err := h.copyFileInternalWithOverride(ctx, tenantID, sourceDocID, adjustedTargetParent, newName, override)
	if err != nil {
		log.WithError(err).Error("Failed to copy file internally")
		return fmt.Errorf("failed to copy file internally: %w", err)
	}

	log.Infof("Successfully copied file internally source_id=%d target_parent_id=%d new_name=%s",
		sourceDocID, targetParent.ParentID, newName)

	return nil
}

// executeCrossProviderCopy handles cross-provider copy (e.g., internal to gdrive)
func (h *CopyFileHandler) executeCrossProviderCopy(ctx context.Context, params *ActionExecutionParams, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.executeCrossProviderCopy")

	sourceProvider := params.Action.GetProvider()
	targetProvider := params.Action.GetTargetProvider()

	log.Infof("Cross-provider copy: %s -> %s source_doc_id=%d target_parent_id=%d new_name=%s",
		sourceProvider, targetProvider, sourceResult.DocumentID, targetParent.ParentID, newName)

	// Step 1: Get source document service
	sourceDocService, err := h.documentRegistry.GetProvider(sourceProvider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get source document service provider=%s", sourceProvider)
		return fmt.Errorf("failed to get source document service for provider %s: %w", sourceProvider, err)
	}

	// Step 2: Get target document service
	targetDocService, err := h.documentRegistry.GetProvider(targetProvider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get target document service provider=%s", targetProvider)
		return fmt.Errorf("failed to get target document service for provider %s: %w", targetProvider, err)
	}

	// Step 3: Read file content from source provider
	log.Infof("Reading file content from source provider=%s doc_id=%d", sourceProvider, sourceResult.DocumentID)
	fileContent, err := sourceDocService.GetDocumentContent(ctx, sourceResult.DocumentID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to read file content from source provider=%s doc_id=%d", sourceProvider, sourceResult.DocumentID)
		return fmt.Errorf("failed to read file content from source: %w", err)
	}

	log.Infof("Successfully read file content size=%d bytes from source", len(fileContent))

	// Step 4: Get source document metadata
	sourceDoc, err := sourceDocService.GetDocument(ctx, sourceResult.DocumentID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get source document metadata doc_id=%d", sourceResult.DocumentID)
		return fmt.Errorf("failed to get source document metadata: %w", err)
	}

	// Step 5: Create document in target provider
	return h.createDocumentInTargetProvider(ctx, targetDocService, targetProvider, targetParent, newName, fileContent, sourceDoc, params)
}

// executeExternalProviderCopy handles same external provider copy (e.g., gdrive to gdrive)
func (h *CopyFileHandler) executeExternalProviderCopy(ctx context.Context, params *ActionExecutionParams, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string, provider string) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.executeExternalProviderCopy")

	log.Infof("External provider copy: %s source_doc_id=%d target_parent_id=%d new_name=%s",
		provider, sourceResult.DocumentID, targetParent.ParentID, newName)

	// Get document service for the provider
	documentService, err := h.documentRegistry.GetProvider(provider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get document service for provider=%s", provider)
		return fmt.Errorf("failed to get document service for provider %s: %w", provider, err)
	}

	// Check if provider has native copy method
	if copyService, ok := documentService.(CopyDocumentService); ok {
		log.Infof("Using native copy method for provider=%s", provider)
		return h.executeNativeCopy(ctx, copyService, sourceResult, targetParent, newName, params)
	}

	// Fallback to read-then-write approach for same provider
	log.Infof("Using read-then-write approach for provider=%s", provider)
	return h.executeReadWriteCopy(ctx, documentService, provider, sourceResult, targetParent, newName, params)
}

// createDocumentInTargetProvider creates a document in the target provider with content
func (h *CopyFileHandler) createDocumentInTargetProvider(ctx context.Context, targetDocService DocumentService, targetProvider string, targetParent *TargetParentResult, newName string, fileContent []byte, sourceDoc *model.Document, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.createDocumentInTargetProvider")

	log.Infof("Creating document in target provider=%s name=%s content_size=%d", targetProvider, newName, len(fileContent))

	// Create document request based on target provider type
	if targetProvider == "internal" {
		return h.createInternalDocument(ctx, targetDocService, targetParent, newName, fileContent, sourceDoc, params)
	}

	// For external providers (gdrive, sharepoint), use external creation logic
	return h.createExternalDocument(ctx, targetDocService, targetProvider, targetParent, newName, fileContent, sourceDoc, params)
}

// createInternalDocument creates a document in internal storage
func (h *CopyFileHandler) createInternalDocument(ctx context.Context, targetDocService DocumentService, targetParent *TargetParentResult, newName string, fileContent []byte, sourceDoc *model.Document, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.createInternalDocument")

	log.Infof("Creating internal document name=%s parent_id=%d content_size=%d",
		newName, targetParent.ParentID, len(fileContent))

	// For internal storage, we need to upload content to glob service first
	// Use the upload provider pattern to handle file content upload

	// Step 1: Get internal upload provider
	uploadProvider, err := h.getInternalUploadProvider(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to get internal upload provider")
		return fmt.Errorf("failed to get internal upload provider: %w", err)
	}

	// Step 2: Create upload session
	sessionReq := &CreateUploadSessionRequest{
		TenantID:    params.TenantID,
		ParentID:    targetParent.ParentID,
		FileName:    newName,
		FileSize:    int64(len(fileContent)),
		MimeType:    h.getMimeType(newName),
		ObjectType:  int(targetParent.ObjectType),
		ObjectID:    targetParent.ObjectID,
		CreatedUser: 1, // System user
	}

	sessionResp, err := uploadProvider.CreateUploadSession(ctx, sessionReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to create upload session name=%s", newName)
		return fmt.Errorf("failed to create upload session: %w", err)
	}

	// Step 3: Upload file content to glob service
	err = h.uploadContentToGlob(ctx, sessionResp.UploadURL, fileContent)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload content to glob session=%s", sessionResp.SessionToken)
		return fmt.Errorf("failed to upload content to glob: %w", err)
	}

	// Step 4: Register upload completion
	registerReq := &RegisterUploadRequest{
		TenantID:     params.TenantID,
		SessionToken: sessionResp.SessionToken,
		Key:          sessionResp.Key,
		FileName:     newName,
		FileSize:     int64(len(fileContent)),
		MimeType:     h.getMimeType(newName),
		ObjectID:     targetParent.ObjectID,
		ObjectType:   int(targetParent.ObjectType),
		ParentID:     targetParent.ParentID,
		CreatedUser:  1, // System user
	}

	registerResp, err := uploadProvider.RegisterUpload(ctx, registerReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to register upload session=%s", sessionResp.SessionToken)
		return fmt.Errorf("failed to register upload: %w", err)
	}

	log.Infof("Successfully created internal document doc_id=%d name=%s key=%s",
		registerResp.ID, registerResp.Name, sessionResp.Key)

	return nil
}

// createExternalDocument creates a document in external provider (gdrive, sharepoint)
func (h *CopyFileHandler) createExternalDocument(ctx context.Context, targetDocService DocumentService, targetProvider string, targetParent *TargetParentResult, newName string, fileContent []byte, sourceDoc *model.Document, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.createExternalDocument")

	log.Infof("Creating external document provider=%s name=%s parent_id=%d content_size=%d",
		targetProvider, newName, targetParent.ParentID, len(fileContent))

	// For external providers, we need to:
	// 1. Get external parent folder ID from DocumentMapping
	// 2. Create upload session for external provider
	// 3. Upload file content to external provider
	// 4. Register upload completion

	switch targetProvider {
	case "gdrive":
		return h.createGoogleDriveDocument(ctx, targetDocService, targetParent, newName, fileContent, sourceDoc, params)
	case "sharepoint":
		return h.createSharePointDocument(ctx, targetDocService, targetParent, newName, fileContent, sourceDoc, params)
	default:
		log.Errorf("Unsupported external provider=%s", targetProvider)
		return fmt.Errorf("unsupported external provider: %s", targetProvider)
	}
}

// createGoogleDriveDocument creates a document in Google Drive
func (h *CopyFileHandler) createGoogleDriveDocument(ctx context.Context, targetDocService DocumentService, targetParent *TargetParentResult, newName string, fileContent []byte, sourceDoc *model.Document, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.createGoogleDriveDocument")

	log.Infof("Creating Google Drive document name=%s parent_id=%d content_size=%d",
		newName, targetParent.ParentID, len(fileContent))

	// For Google Drive, we need to use the upload provider pattern
	// since direct content upload requires complex session management

	// Step 1: Get external parent folder ID from DocumentMapping
	externalParentID, err := h.getExternalParentID(ctx, targetParent, "gdrive")
	if err != nil {
		log.WithError(err).Errorf("Failed to get external parent ID for parent_id=%d", targetParent.ParentID)
		return fmt.Errorf("failed to get external parent ID: %w", err)
	}

	// Step 2: Create upload session using AutoDoc upload provider pattern
	sessionReq := &CreateUploadSessionRequest{
		TenantID:         params.TenantID,
		ExternalParentID: externalParentID, // Use ExternalParentID for Google Drive
		FileName:         newName,
		FileSize:         int64(len(fileContent)),
		MimeType:         h.getMimeType(newName),
		ObjectType:       int(targetParent.ObjectType),
		ObjectID:         targetParent.ObjectID,
		CreatedUser:      1, // System user
	}

	// Get Google Drive upload provider
	uploadProvider, err := h.getGDriveUploadProvider(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to get Google Drive upload provider")
		return fmt.Errorf("failed to get Google Drive upload provider: %w", err)
	}

	sessionResp, err := uploadProvider.CreateUploadSession(ctx, sessionReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to create Google Drive upload session name=%s", newName)
		return fmt.Errorf("failed to create Google Drive upload session: %w", err)
	}

	// Step 3: Upload file content to Google Drive
	err = h.uploadContentToGoogleDrive(ctx, sessionResp.SessionToken, fileContent)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload content to Google Drive session=%s", sessionResp.SessionToken)
		return fmt.Errorf("failed to upload content to Google Drive: %w", err)
	}

	// Step 4: Register upload completion
	registerReq := &RegisterUploadRequest{
		TenantID:     params.TenantID,
		SessionToken: sessionResp.SessionToken,
		Key:          sessionResp.Key,
		FileName:     newName,
		FileSize:     int64(len(fileContent)),
		MimeType:     h.getMimeType(newName),
		ObjectID:     targetParent.ObjectID,
		ObjectType:   int(targetParent.ObjectType),
		CreatedUser:  1, // System user
	}

	registerResp, err := uploadProvider.RegisterUpload(ctx, registerReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to register Google Drive upload session=%s", sessionResp.SessionToken)
		return fmt.Errorf("failed to register Google Drive upload: %w", err)
	}

	log.Infof("Successfully created Google Drive document doc_id=%d name=%s external_id=%s",
		registerResp.ID, registerResp.Name, registerResp.ExternalID)

	return nil
}

// createSharePointDocument creates a document in SharePoint
func (h *CopyFileHandler) createSharePointDocument(ctx context.Context, targetDocService DocumentService, targetParent *TargetParentResult, newName string, fileContent []byte, sourceDoc *model.Document, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.createSharePointDocument")

	log.Infof("Creating SharePoint document name=%s parent_id=%d content_size=%d",
		newName, targetParent.ParentID, len(fileContent))

	// TODO: Implement SharePoint document creation
	// This would involve:
	// 1. Get external parent folder ID from DocumentMapping
	// 2. Create upload session for SharePoint
	// 3. Upload file content using SharePoint API
	// 4. Create DocumentMapping record

	return fmt.Errorf("sharepoint document creation not yet implemented")
}

// executeNativeCopy uses provider's native copy method
func (h *CopyFileHandler) executeNativeCopy(ctx context.Context, copyService CopyDocumentService, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.executeNativeCopy")

	// Create copy request
	copyReq := &CopyDocumentRequest{
		SourceID:       sourceResult.DocumentID,
		TargetParentID: targetParent.ParentID,
		NewName:        newName,
		TenantID:       params.TenantID,
		CreatedUser:    1, // TODO: Get from context or add to ActionExecutionParams
	}

	log.Infof("Executing native copy source_id=%d target_parent_id=%d new_name=%s",
		copyReq.SourceID, copyReq.TargetParentID, copyReq.NewName)

	// Execute native copy
	newDoc, err := copyService.CopyDocument(ctx, copyReq)
	if err != nil {
		log.WithError(err).Error("Native copy failed")
		return fmt.Errorf("native copy failed: %w", err)
	}

	log.Infof("Native copy successful new_doc_id=%d new_name=%s", newDoc.ID, newDoc.Name)
	return nil
}

// executeReadWriteCopy uses read-then-write approach for same provider
func (h *CopyFileHandler) executeReadWriteCopy(ctx context.Context, documentService DocumentService, provider string, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.executeReadWriteCopy")

	log.Infof("Executing read-write copy for provider=%s source_id=%d target_parent_id=%d",
		provider, sourceResult.DocumentID, targetParent.ParentID)

	// Step 1: Read file content from source
	fileContent, err := documentService.GetDocumentContent(ctx, sourceResult.DocumentID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to read file content source_id=%d", sourceResult.DocumentID)
		return fmt.Errorf("failed to read file content: %w", err)
	}

	// Step 2: Get source document metadata
	sourceDoc, err := documentService.GetDocument(ctx, sourceResult.DocumentID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get source document metadata source_id=%d", sourceResult.DocumentID)
		return fmt.Errorf("failed to get source document metadata: %w", err)
	}

	// Step 3: Create new document with content
	return h.createDocumentInTargetProvider(ctx, documentService, provider, targetParent, newName, fileContent, sourceDoc, params)
}

// getInternalUploadProvider gets the internal upload provider
func (h *CopyFileHandler) getInternalUploadProvider(ctx context.Context) (UploadProvider, error) {
	log := logger.WithCtx(ctx, "CopyFileHandler.getInternalUploadProvider")

	// TODO: Get internal upload provider from registry
	// For now, return error to indicate this needs implementation
	log.Error("Internal upload provider access not yet implemented")
	return nil, fmt.Errorf("internal upload provider access not yet implemented")
}

// uploadContentToGlob uploads content to glob service using presigned URL
func (h *CopyFileHandler) uploadContentToGlob(ctx context.Context, uploadURL string, content []byte) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.uploadContentToGlob")

	// TODO: Implement HTTP PUT to presigned URL
	// For now, return error to indicate this needs implementation
	log.Errorf("Glob content upload not yet implemented url=%s size=%d", uploadURL, len(content))
	return fmt.Errorf("glob content upload not yet implemented")
}

// getMimeType determines MIME type from filename
func (h *CopyFileHandler) getMimeType(filename string) string {
	// Simple MIME type detection based on file extension
	if strings.HasSuffix(strings.ToLower(filename), ".docx") {
		return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	}
	if strings.HasSuffix(strings.ToLower(filename), ".pdf") {
		return "application/pdf"
	}
	if strings.HasSuffix(strings.ToLower(filename), ".txt") {
		return "text/plain"
	}
	if strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
		return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	}

	// Default to binary
	return "application/octet-stream"
}

// getExternalParentID gets external parent folder ID from DocumentMapping or ExternalParentID
func (h *CopyFileHandler) getExternalParentID(ctx context.Context, targetParent *TargetParentResult, provider string) (string, error) {
	log := logger.WithCtx(ctx, "CopyFileHandler.getExternalParentID")

	log.Infof("Looking up external parent ID parent_id=%d object_type=%d object_id=%d provider=%s external_parent_id=%s",
		targetParent.ParentID, targetParent.ObjectType, targetParent.ObjectID, provider, targetParent.ExternalParentID)

	// If ExternalParentID is already set (from cross-provider folder creation), use it directly
	if targetParent.ExternalParentID != "" {
		log.Infof("Using existing external parent ID external_id=%s", targetParent.ExternalParentID)
		return targetParent.ExternalParentID, nil
	}

	// Convert provider name to model constant
	var providerConstant string
	switch provider {
	case "gdrive":
		providerConstant = model.DocProviderGoogle
	case "sharepoint":
		providerConstant = model.DocProviderSharepoint
	default:
		return "", fmt.Errorf("unsupported provider: %s", provider)
	}

	// Get external ID for the target parent object
	externalID, err := h.mappingService.GetExternalID(ctx, targetParent.ObjectID, getObjectTypeString(targetParent.ObjectType), providerConstant, targetParent.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get external ID for object_id=%d object_type=%d provider=%s",
			targetParent.ObjectID, targetParent.ObjectType, providerConstant)
		return "", fmt.Errorf("failed to get external ID for target parent: %w", err)
	}

	log.Infof("Successfully resolved external parent ID external_id=%s for object_id=%d provider=%s",
		externalID, targetParent.ObjectID, providerConstant)

	return externalID, nil
}

// getObjectTypeString converts object type int to string
func getObjectTypeString(objectType int) string {
	switch objectType {
	case 1:
		return model.DocTypeClient
	case 2:
		return model.DocTypeMatter
	case 3:
		return model.DocTypeAutoDocRoot
	default:
		return "unknown"
	}
}

// getGDriveUploadProvider gets Google Drive upload provider
func (h *CopyFileHandler) getGDriveUploadProvider(ctx context.Context) (UploadProvider, error) {
	log := logger.WithCtx(ctx, "CopyFileHandler.getGDriveUploadProvider")

	// Get Google Drive upload provider from registry
	uploadProvider, err := h.uploadRegistry.GetProvider("gdrive")
	if err != nil {
		log.WithError(err).Error("Failed to get Google Drive upload provider from registry")
		return nil, fmt.Errorf("failed to get Google Drive upload provider: %w", err)
	}

	log.Infof("Successfully retrieved Google Drive upload provider")
	return uploadProvider, nil
}

// uploadContentToGoogleDrive uploads content to Google Drive using session token
func (h *CopyFileHandler) uploadContentToGoogleDrive(ctx context.Context, sessionToken string, content []byte) error {
	log := logger.WithCtx(ctx, "CopyFileHandler.uploadContentToGoogleDrive")

	log.Infof("Uploading content to Google Drive session=%s size=%d", sessionToken, len(content))

	// Get Google Drive document service
	gdriveService, err := h.documentRegistry.GetProvider("gdrive")
	if err != nil {
		log.WithError(err).Error("Failed to get Google Drive document service")
		return fmt.Errorf("failed to get Google Drive document service: %w", err)
	}

	// Debug: log the actual type
	log.Infof("Google Drive service type: %T", gdriveService)

	// Cast to GDriveServiceProvider to access underlying gdrive.DocumentService
	type GDriveServiceProvider interface {
		GetGDriveService() gdrive.DocumentService
	}

	gdriveProvider, ok := gdriveService.(GDriveServiceProvider)
	if !ok {
		log.Errorf("Google Drive service does not implement GDriveServiceProvider interface, actual type: %T", gdriveService)
		return fmt.Errorf("Google Drive service does not support file content upload")
	}

	// Get the underlying gdrive.DocumentService
	gdriveDocService := gdriveProvider.GetGDriveService()

	// Create content reader from bytes
	contentReader := bytes.NewReader(content)
	contentType := h.getMimeType("") // Use default content type detection
	contentLength := int64(len(content))

	// Upload content to Google Drive
	uploadResult, err := gdriveDocService.UploadFileContent(ctx, sessionToken, contentReader, contentType, contentLength)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload content to Google Drive session=%s", sessionToken)
		return fmt.Errorf("failed to upload content to Google Drive: %w", err)
	}

	log.Infof("Successfully uploaded content to Google Drive session=%s drive_file_id=%s size=%d",
		sessionToken, uploadResult.DriveFileID, len(content))

	return nil
}
