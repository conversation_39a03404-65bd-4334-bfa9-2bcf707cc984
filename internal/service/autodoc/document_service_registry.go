package autodoc

import (
	"fmt"
	"sync"
)

// DocumentServiceRegistry manages multiple document service providers
type DocumentServiceRegistry interface {
	// RegisterProvider registers a document service provider
	RegisterProvider(provider string, service DocumentService) error

	// GetProvider retrieves a document service provider by name
	GetProvider(provider string) (DocumentService, error)

	// GetDefaultProvider returns the name of the default provider
	GetDefaultProvider() string

	// SetDefaultProvider sets the default provider
	SetDefaultProvider(provider string) error

	// ListProviders returns a list of all registered provider names
	ListProviders() []string

	// IsProviderRegistered checks if a provider is registered
	IsProviderRegistered(provider string) bool

	// UnregisterProvider removes a provider from the registry
	UnregisterProvider(provider string) error

	// GetProviderWithFallback gets a provider with fallback to default if not found
	GetProviderWithFallback(provider string) (DocumentService, string, error)


}



// documentServiceRegistry implements DocumentServiceRegistry
type documentServiceRegistry struct {
	providers       map[string]DocumentService
	defaultProvider string
	mu              sync.RWMutex
}

// NewDocumentServiceRegistry creates a new document service registry
func NewDocumentServiceRegistry() DocumentServiceRegistry {
	return &documentServiceRegistry{
		providers:       make(map[string]DocumentService),
		defaultProvider: "internal", // Default to internal provider
	}
}

// RegisterProvider registers a document service provider
func (r *documentServiceRegistry) RegisterProvider(provider string, service DocumentService) error {
	if provider == "" {
		return fmt.Errorf("provider name cannot be empty")
	}

	if service == nil {
		return fmt.Errorf("service cannot be nil")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.providers[provider]; exists {
		return fmt.Errorf("provider %s is already registered", provider)
	}

	r.providers[provider] = service
	return nil
}

// GetProvider retrieves a document service provider by name
func (r *documentServiceRegistry) GetProvider(provider string) (DocumentService, error) {
	if provider == "" {
		provider = r.GetDefaultProvider()
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	service, exists := r.providers[provider]
	if !exists {
		return nil, fmt.Errorf("provider %s is not registered", provider)
	}

	return service, nil
}

// GetDefaultProvider returns the name of the default provider
func (r *documentServiceRegistry) GetDefaultProvider() string {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.defaultProvider
}

// SetDefaultProvider sets the default provider
func (r *documentServiceRegistry) SetDefaultProvider(provider string) error {
	if provider == "" {
		return fmt.Errorf("provider name cannot be empty")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.providers[provider]; !exists {
		return fmt.Errorf("provider %s is not registered", provider)
	}

	r.defaultProvider = provider
	return nil
}

// ListProviders returns a list of all registered provider names
func (r *documentServiceRegistry) ListProviders() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	providers := make([]string, 0, len(r.providers))
	for provider := range r.providers {
		providers = append(providers, provider)
	}

	return providers
}

// IsProviderRegistered checks if a provider is registered
func (r *documentServiceRegistry) IsProviderRegistered(provider string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.providers[provider]
	return exists
}

// UnregisterProvider removes a provider from the registry
func (r *documentServiceRegistry) UnregisterProvider(provider string) error {
	if provider == "" {
		return fmt.Errorf("provider name cannot be empty")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.providers[provider]; !exists {
		return fmt.Errorf("provider %s is not registered", provider)
	}

	// Don't allow unregistering the default provider
	if provider == r.defaultProvider {
		return fmt.Errorf("cannot unregister default provider %s", provider)
	}

	delete(r.providers, provider)
	return nil
}

// GetProviderWithFallback gets a provider with fallback to default if not found
func (r *documentServiceRegistry) GetProviderWithFallback(provider string) (DocumentService, string, error) {
	// Try to get the requested provider first
	if provider != "" {
		if service, err := r.GetProvider(provider); err == nil {
			return service, provider, nil
		}
	}

	// Fallback to default provider
	defaultProvider := r.GetDefaultProvider()
	service, err := r.GetProvider(defaultProvider)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get fallback provider %s: %w", defaultProvider, err)
	}

	return service, defaultProvider, nil
}


