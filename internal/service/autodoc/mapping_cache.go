package autodoc

import (
	"context"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
)

// MappingCache defines the interface for caching ID mappings
type MappingCache interface {
	// GetInternalID retrieves internal ID from cache
	GetInternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (uint64, bool)

	// GetExternalID retrieves external ID from cache
	GetExternalID(ctx context.Context, internalID uint64, provider string, tenantID uint64) (string, bool)

	// GetMapping retrieves complete mapping from cache
	GetMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) (*model.DocumentMapping, bool)

	// GetMappingByExternalID retrieves complete mapping by external ID from cache
	GetMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (*model.DocumentMapping, bool)

	// SetMapping stores a mapping in cache
	SetMapping(ctx context.Context, mapping *model.DocumentMapping)

	// DeleteMapping removes a mapping from cache by internal ID
	DeleteMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64)

	// DeleteMappingByExternalID removes a mapping from cache by external ID
	DeleteMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64)

	// Clear removes all cached mappings for a tenant
	Clear(ctx context.Context, tenantID uint64)

	// ClearProvider removes all cached mappings for a provider
	ClearProvider(ctx context.Context, provider string, tenantID uint64)
}

// NoOpMappingCache is a no-operation cache implementation
type NoOpMappingCache struct{}

// NewNoOpMappingCache creates a new no-operation cache
func NewNoOpMappingCache() MappingCache {
	return &NoOpMappingCache{}
}

func (c *NoOpMappingCache) GetInternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (uint64, bool) {
	return 0, false
}

func (c *NoOpMappingCache) GetExternalID(ctx context.Context, internalID uint64, provider string, tenantID uint64) (string, bool) {
	return "", false
}

func (c *NoOpMappingCache) GetMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	return nil, false
}

func (c *NoOpMappingCache) GetMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	return nil, false
}

func (c *NoOpMappingCache) SetMapping(ctx context.Context, mapping *model.DocumentMapping) {
	// No-op
}

func (c *NoOpMappingCache) DeleteMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) {
	// No-op
}

func (c *NoOpMappingCache) DeleteMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) {
	// No-op
}

func (c *NoOpMappingCache) Clear(ctx context.Context, tenantID uint64) {
	// No-op
}

func (c *NoOpMappingCache) ClearProvider(ctx context.Context, provider string, tenantID uint64) {
	// No-op
}

// InMemoryMappingCache is an in-memory cache implementation for testing
type InMemoryMappingCache struct {
	mappings map[string]*model.DocumentMapping
	ttl      time.Duration
}

// NewInMemoryMappingCache creates a new in-memory cache
func NewInMemoryMappingCache(ttl time.Duration) MappingCache {
	return &InMemoryMappingCache{
		mappings: make(map[string]*model.DocumentMapping),
		ttl:      ttl,
	}
}

func (c *InMemoryMappingCache) GetInternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (uint64, bool) {
	key := c.externalKey(externalID, provider, tenantID)
	mapping, exists := c.mappings[key]
	if !exists {
		return 0, false
	}
	return mapping.ObjectID, true
}

func (c *InMemoryMappingCache) GetExternalID(ctx context.Context, internalID uint64, provider string, tenantID uint64) (string, bool) {
	key := c.internalKey(internalID, provider, tenantID)
	mapping, exists := c.mappings[key]
	if !exists {
		return "", false
	}
	return mapping.DriveID, true
}

func (c *InMemoryMappingCache) GetMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	key := c.internalKey(internalID, provider, tenantID)
	mapping, exists := c.mappings[key]
	if !exists {
		return nil, false
	}
	// Return a copy to prevent modification
	mappingCopy := *mapping
	return &mappingCopy, true
}

func (c *InMemoryMappingCache) GetMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	key := c.externalKey(externalID, provider, tenantID)
	mapping, exists := c.mappings[key]
	if !exists {
		return nil, false
	}
	// Return a copy to prevent modification
	mappingCopy := *mapping
	return &mappingCopy, true
}

func (c *InMemoryMappingCache) SetMapping(ctx context.Context, mapping *model.DocumentMapping) {
	internalKey := c.internalKey(mapping.ObjectID, mapping.Provider, mapping.TenantID)
	externalKey := c.externalKey(mapping.DriveID, mapping.Provider, mapping.TenantID)

	// Store mapping under both keys
	c.mappings[internalKey] = mapping
	c.mappings[externalKey] = mapping
}

func (c *InMemoryMappingCache) DeleteMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) {
	internalKey := c.internalKey(internalID, provider, tenantID)

	// Get mapping to find external key
	if mapping, exists := c.mappings[internalKey]; exists {
		externalKey := c.externalKey(mapping.DriveID, provider, tenantID)
		delete(c.mappings, externalKey)
	}

	delete(c.mappings, internalKey)
}

func (c *InMemoryMappingCache) DeleteMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) {
	externalKey := c.externalKey(externalID, provider, tenantID)

	// Get mapping to find internal key
	if mapping, exists := c.mappings[externalKey]; exists {
		internalKey := c.internalKey(mapping.ObjectID, provider, tenantID)
		delete(c.mappings, internalKey)
	}

	delete(c.mappings, externalKey)
}

func (c *InMemoryMappingCache) Clear(ctx context.Context, tenantID uint64) {
	// Remove all mappings for the tenant
	for key, mapping := range c.mappings {
		if mapping.TenantID == tenantID {
			delete(c.mappings, key)
		}
	}
}

func (c *InMemoryMappingCache) ClearProvider(ctx context.Context, provider string, tenantID uint64) {
	// Remove all mappings for the provider and tenant
	for key, mapping := range c.mappings {
		if mapping.Provider == provider && mapping.TenantID == tenantID {
			delete(c.mappings, key)
		}
	}
}

// internalKey generates cache key for internal ID lookups
func (c *InMemoryMappingCache) internalKey(internalID uint64, provider string, tenantID uint64) string {
	return fmt.Sprintf("internal:%d:%s:%d", internalID, provider, tenantID)
}

// externalKey generates cache key for external ID lookups
func (c *InMemoryMappingCache) externalKey(externalID string, provider string, tenantID uint64) string {
	return fmt.Sprintf("external:%s:%s:%d", externalID, provider, tenantID)
}

// RedisMappingCache would be implemented here for production use
// type RedisMappingCache struct {
//     client redis.Client
//     ttl    time.Duration
// }
//
// Implementation would use Redis with proper key patterns and TTL
// Keys would be: "mapping:internal:{tenantID}:{provider}:{internalID}"
//               "mapping:external:{tenantID}:{provider}:{externalID}"
