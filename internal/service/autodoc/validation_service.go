package autodoc

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"unicode"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// ValidationService provides comprehensive input validation and sanitization
type ValidationService interface {
	// ValidateEventData validates event data structure and content
	ValidateEventData(ctx context.Context, eventType string, eventData map[string]interface{}) error

	// ValidateRule validates automation rule configuration
	ValidateRule(ctx context.Context, rule *model.DocumentAutomationRule) error

	// ValidateTriggerRules validates trigger rule conditions
	ValidateTriggerRules(ctx context.Context, triggerRules model.TriggerRulesMap) error

	// ValidateRuleActions validates rule action configurations
	ValidateRuleActions(ctx context.Context, actions model.RuleConfigArray) error

	// SanitizeEventData sanitizes event data to prevent injection attacks
	SanitizeEventData(ctx context.Context, eventData map[string]interface{}) map[string]interface{}

	// SanitizeFilePath sanitizes file paths to prevent path traversal attacks
	SanitizeFilePath(ctx context.Context, filePath string) (string, error)

	// SanitizePlaceholderData sanitizes placeholder data
	SanitizePlaceholderData(ctx context.Context, data map[string]interface{}) map[string]interface{}
}

// validationService implements ValidationService
type validationService struct{}

// NewValidationService creates a new validation service
func NewValidationService() ValidationService {
	return &validationService{}
}

// ValidateEventData validates event data structure and content
func (v *validationService) ValidateEventData(ctx context.Context, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("event_type", eventType).Debug("Validating event data")

	if eventData == nil {
		return fmt.Errorf("event data cannot be nil")
	}

	// Validate event type
	if err := v.validateEventType(eventType); err != nil {
		return fmt.Errorf("invalid event type: %w", err)
	}

	// Validate based on event type
	switch {
	case isMatterEvent(eventType):
		return v.validateMatterEventData(ctx, eventData)
	case isClientEvent(eventType):
		return v.validateClientEventData(ctx, eventData)
	default:
		return fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// ValidateRule validates automation rule configuration
func (v *validationService) ValidateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	log := bilabllog.CreateContextLogger(ctx)
	if rule == nil {
		log.Debug("Validating automation rule - rule is nil")
		return fmt.Errorf("rule cannot be nil")
	}

	if rule == nil {
		return fmt.Errorf("rule cannot be nil")
	}

	log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Debug("Validating automation rule")

	// Validate required fields
	if rule.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if strings.TrimSpace(rule.Name) == "" {
		return fmt.Errorf("rule name is required")
	}

	if len(rule.Name) > 255 {
		return fmt.Errorf("rule name cannot exceed 255 characters")
	}

	// Validate trigger type
	if err := v.validateEventType(rule.TriggerType); err != nil {
		return fmt.Errorf("invalid trigger type: %w", err)
	}

	// Validate trigger rules
	if err := v.ValidateTriggerRules(ctx, rule.TriggerRules); err != nil {
		return fmt.Errorf("invalid trigger rules: %w", err)
	}

	// Validate rule actions
	if err := v.ValidateRuleActions(ctx, rule.RuleConfig); err != nil {
		return fmt.Errorf("invalid rule actions: %w", err)
	}

	return nil
}

// ValidateTriggerRules validates trigger rule conditions
func (v *validationService) ValidateTriggerRules(ctx context.Context, triggerRules model.TriggerRulesMap) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("rule_count", len(triggerRules)).Debug("Validating trigger rules")

	if triggerRules == nil {
		return nil // Empty trigger rules are allowed (match all)
	}

	// Validate each trigger rule
	for field, condition := range triggerRules {
		if err := v.validateTriggerRule(ctx, field, condition); err != nil {
			return fmt.Errorf("invalid trigger rule for field '%s': %w", field, err)
		}
	}

	return nil
}

// ValidateRuleActions validates rule action configurations
func (v *validationService) ValidateRuleActions(ctx context.Context, actions model.RuleConfigArray) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("action_count", len(actions)).Debug("Validating rule actions")

	if len(actions) == 0 {
		return fmt.Errorf("at least one action is required")
	}

	// Validate each action
	for i, action := range actions {
		if err := v.validateRuleAction(ctx, action); err != nil {
			return fmt.Errorf("invalid action at index %d: %w", i, err)
		}
	}

	return nil
}

// SanitizeEventData sanitizes event data to prevent injection attacks
func (v *validationService) SanitizeEventData(ctx context.Context, eventData map[string]interface{}) map[string]interface{} {
	log := bilabllog.CreateContextLogger(ctx)
	log.Debug("Sanitizing event data")

	if eventData == nil {
		return nil
	}

	sanitized := make(map[string]interface{})

	for key, value := range eventData {
		sanitizedKey := v.sanitizeString(key)
		sanitizedValue := v.sanitizeValue(value)
		sanitized[sanitizedKey] = sanitizedValue
	}

	return sanitized
}

// SanitizeFilePath sanitizes file paths to prevent path traversal attacks
func (v *validationService) SanitizeFilePath(ctx context.Context, filePath string) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("original_path", filePath).Debug("Sanitizing file path")

	if filePath == "" {
		return "", fmt.Errorf("file path cannot be empty")
	}

	// Remove dangerous patterns
	sanitized := strings.ReplaceAll(filePath, "..", "")
	sanitized = strings.ReplaceAll(sanitized, "//", "/")
	sanitized = strings.TrimPrefix(sanitized, "/")

	// Validate against dangerous patterns
	dangerousPatterns := []string{
		"../",
		"..\\",
		"/etc/",
		"/var/",
		"/usr/",
		"/bin/",
		"/sbin/",
		"C:\\",
		"D:\\",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(strings.ToLower(sanitized), strings.ToLower(pattern)) {
			return "", fmt.Errorf("file path contains dangerous pattern: %s", pattern)
		}
	}

	// Validate file extension
	if err := v.validateFileExtension(sanitized); err != nil {
		return "", fmt.Errorf("invalid file extension: %w", err)
	}

	log.WithField("original_path", filePath).WithField("sanitized_path", sanitized).Debug("File path sanitized")

	return sanitized, nil
}

// SanitizePlaceholderData sanitizes placeholder data
func (v *validationService) SanitizePlaceholderData(ctx context.Context, data map[string]interface{}) map[string]interface{} {
	log := bilabllog.CreateContextLogger(ctx)
	log.Debug("Sanitizing placeholder data")

	if data == nil {
		return nil
	}

	sanitized := make(map[string]interface{})

	for key, value := range data {
		sanitizedKey := v.sanitizeString(key)
		sanitizedValue := v.sanitizeValue(value)
		sanitized[sanitizedKey] = sanitizedValue
	}

	return sanitized
}

// validateEventType validates event type format
func (v *validationService) validateEventType(eventType string) error {
	if eventType == "" {
		return fmt.Errorf("event type cannot be empty")
	}

	// Valid event type pattern: entity.action (e.g., matter.create, client.update)
	validPattern := regexp.MustCompile(`^[a-z]+\.[a-z]+$`)
	if !validPattern.MatchString(eventType) {
		return fmt.Errorf("event type must match pattern 'entity.action': %s", eventType)
	}

	// Validate supported event types
	supportedTypes := []string{
		"matter.create", "matter.update", "matter.delete",
		"client.create", "client.update", "client.delete",
	}

	for _, supported := range supportedTypes {
		if eventType == supported {
			return nil
		}
	}

	return fmt.Errorf("unsupported event type: %s", eventType)
}

// validateMatterEventData validates matter event data structure
func (v *validationService) validateMatterEventData(ctx context.Context, eventData map[string]interface{}) error {
	// Check for required matter field
	matterData, exists := eventData["matter"]
	if !exists {
		return fmt.Errorf("matter field is required in matter events")
	}

	matterMap, ok := matterData.(map[string]interface{})
	if !ok {
		return fmt.Errorf("matter field must be an object")
	}

	// Validate required matter fields
	requiredFields := []string{"id", "name"}
	for _, field := range requiredFields {
		if _, exists := matterMap[field]; !exists {
			return fmt.Errorf("matter.%s is required", field)
		}
	}

	return nil
}

// validateClientEventData validates client event data structure
func (v *validationService) validateClientEventData(ctx context.Context, eventData map[string]interface{}) error {
	// Check for required client field
	clientData, exists := eventData["client"]
	if !exists {
		return fmt.Errorf("client field is required in client events")
	}

	clientMap, ok := clientData.(map[string]interface{})
	if !ok {
		return fmt.Errorf("client field must be an object")
	}

	// Validate required client fields
	requiredFields := []string{"id", "name"}
	for _, field := range requiredFields {
		if _, exists := clientMap[field]; !exists {
			return fmt.Errorf("client.%s is required", field)
		}
	}

	return nil
}

// validateTriggerRule validates a single trigger rule
func (v *validationService) validateTriggerRule(ctx context.Context, field string, condition interface{}) error {
	// Validate field name
	if field == "" {
		return fmt.Errorf("field name cannot be empty")
	}

	// Field name should use dot notation for nested fields
	validFieldPattern := regexp.MustCompile(`^[a-z_][a-z0-9_]*(\.[a-z_][a-z0-9_]*)*$`)
	if !validFieldPattern.MatchString(field) {
		return fmt.Errorf("invalid field name format: %s", field)
	}

	// Validate condition based on type
	switch cond := condition.(type) {
	case string:
		// Simple string condition
		if len(cond) > 1000 {
			return fmt.Errorf("condition value too long (max 1000 characters)")
		}
	case map[string]interface{}:
		// Complex condition with operators
		return v.validateComplexCondition(cond)
	case []interface{}:
		// Array condition
		if len(cond) > 100 {
			return fmt.Errorf("condition array too large (max 100 items)")
		}
	default:
		// Other types (numbers, booleans) are allowed
	}

	return nil
}

// validateComplexCondition validates complex trigger conditions
func (v *validationService) validateComplexCondition(condition map[string]interface{}) error {
	validOperators := []string{"eq", "equals", "ne", "not_equals", "in", "not_in", "contains", "not_contains"}

	for operator, value := range condition {
		// Validate operator
		isValid := false
		for _, validOp := range validOperators {
			if operator == validOp {
				isValid = true
				break
			}
		}

		if !isValid {
			return fmt.Errorf("invalid operator: %s", operator)
		}

		// Validate operator value
		if operator == "in" || operator == "not_in" {
			if _, ok := value.([]interface{}); !ok {
				return fmt.Errorf("operator '%s' requires array value", operator)
			}
		}
	}

	return nil
}

// validateRuleAction validates a single rule action
func (v *validationService) validateRuleAction(ctx context.Context, action model.RuleAction) error {
	// Validate action type
	validActionTypes := []string{"copy_file", "copy_folder", "generate_document"}
	isValid := false
	for _, validType := range validActionTypes {
		if action.ActionType == validType {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid action type: %s", action.ActionType)
	}

	// Validate source path
	if action.SourcePath == "" {
		return fmt.Errorf("source_path is required")
	}

	if _, err := v.SanitizeFilePath(ctx, action.SourcePath); err != nil {
		return fmt.Errorf("invalid source_path: %w", err)
	}

	// Validate target path
	if action.TargetPath == "" {
		return fmt.Errorf("target_path is required")
	}

	if _, err := v.SanitizeFilePath(ctx, action.TargetPath); err != nil {
		return fmt.Errorf("invalid target_path: %w", err)
	}

	return nil
}

// validateFileExtension validates file extensions
func (v *validationService) validateFileExtension(filePath string) error {
	// Extract file extension
	parts := strings.Split(filePath, ".")
	if len(parts) < 2 {
		return nil // No extension is allowed
	}

	extension := strings.ToLower(parts[len(parts)-1])

	// Allowed file extensions
	allowedExtensions := []string{
		"docx", "doc", "pdf", "txt", "rtf",
		"xlsx", "xls", "csv",
		"pptx", "ppt",
		"png", "jpg", "jpeg", "gif", "bmp",
		"zip", "rar", "7z",
	}

	for _, allowed := range allowedExtensions {
		if extension == allowed {
			return nil
		}
	}

	return fmt.Errorf("file extension '%s' is not allowed", extension)
}

// sanitizeString sanitizes string values
func (v *validationService) sanitizeString(s string) string {
	// Remove control characters
	cleaned := strings.Map(func(r rune) rune {
		if unicode.IsControl(r) && r != '\n' && r != '\r' && r != '\t' {
			return -1
		}
		return r
	}, s)

	// Trim whitespace
	cleaned = strings.TrimSpace(cleaned)

	// Limit length
	if len(cleaned) > 10000 {
		cleaned = cleaned[:10000]
	}

	return cleaned
}

// sanitizeValue sanitizes interface{} values recursively
func (v *validationService) sanitizeValue(value interface{}) interface{} {
	switch val := value.(type) {
	case string:
		return v.sanitizeString(val)
	case map[string]interface{}:
		sanitized := make(map[string]interface{})
		for key, mapVal := range val {
			sanitizedKey := v.sanitizeString(key)
			sanitizedValue := v.sanitizeValue(mapVal)
			sanitized[sanitizedKey] = sanitizedValue
		}
		return sanitized
	case []interface{}:
		sanitized := make([]interface{}, len(val))
		for i, arrVal := range val {
			sanitized[i] = v.sanitizeValue(arrVal)
		}
		return sanitized
	default:
		return value // Numbers, booleans, etc. are returned as-is
	}
}

// ValidationError represents a validation error with details
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// Error implements the error interface
func (e *ValidationError) Error() string {
	return e.Message
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors,omitempty"`
}
