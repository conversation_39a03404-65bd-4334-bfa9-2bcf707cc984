package autodoc

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCopyFolderHandler_parseTargetPath(t *testing.T) {
	// Create a minimal handler for testing
	handler := &CopyFolderHandler{}

	tests := []struct {
		name               string
		targetPath         string
		expectedParentPath string
		expectedFolderName string
	}{
		{
			name:               "placeholder only - client_folder",
			targetPath:         "{client_folder}",
			expectedParentPath: "{client_folder}",
			expectedFolderName: "",
		},
		{
			name:               "placeholder only - matter_folder",
			targetPath:         "{matter_folder}",
			expectedParentPath: "{matter_folder}",
			expectedFolderName: "",
		},
		{
			name:               "regular folder name only",
			targetPath:         "Documents",
			expectedParentPath: "",
			expectedFolderName: "Documents",
		},
		{
			name:               "nested path with placeholder parent",
			targetPath:         "{client_folder}/Documents",
			expectedParentPath: "{client_folder}",
			expectedFolderName: "Documents",
		},
		{
			name:               "nested path with regular parent",
			targetPath:         "clients/Documents",
			expectedParentPath: "clients",
			expectedFolderName: "Documents",
		},
		{
			name:               "deep nested path",
			targetPath:         "{client_folder}/matters/{matter.name}/Documents",
			expectedParentPath: "{client_folder}/matters/{matter.name}",
			expectedFolderName: "Documents",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parentPath, folderName := handler.parseTargetPath(tt.targetPath)
			assert.Equal(t, tt.expectedParentPath, parentPath, "Parent path mismatch")
			assert.Equal(t, tt.expectedFolderName, folderName, "Folder name mismatch")
		})
	}
}
