package autodoc

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
)

// RuleMatchingService handles rule matching logic for automation rules
type RuleMatchingService interface {
	// MatchRules finds all active rules that match the given event
	MatchRules(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) ([]*model.DocumentAutomationRule, error)

	// EvaluateRule checks if a single rule matches the event data
	EvaluateRule(ctx context.Context, rule *model.DocumentAutomationRule, eventData map[string]interface{}) (bool, error)

	// EvaluateTriggerConditions evaluates trigger rules against event data
	EvaluateTriggerConditions(ctx context.Context, triggerRules model.TriggerRulesMap, eventData map[string]interface{}) (bool, error)
}

// ruleMatchingService implements RuleMatchingService
type ruleMatchingService struct {
	autoDocService AutoDocService
}

// NewRuleMatchingService creates a new rule matching service
func NewRuleMatchingService(autoDocService AutoDocService) RuleMatchingService {
	return &ruleMatchingService{
		autoDocService: autoDocService,
	}
}

// MatchRules finds all active rules that match the given event
func (r *ruleMatchingService) MatchRules(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).WithField("event_data", eventData).Info("Starting rule matching")

	// Get all active rules for the tenant
	rules, err := r.autoDocService.GetActiveRules(ctx, tenantID)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).WithField("event_type", eventType).Error("Failed to get active rules")
		return nil, fmt.Errorf("failed to get active rules: %w", err)
	}

	log.WithField("tenant_id", tenantID).WithField("rules_count", len(rules)).WithField("event_type", eventType).Debug("Retrieved active rules")

	var matchedRules []*model.DocumentAutomationRule

	// Evaluate each rule
	for _, rule := range rules {
		log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("rule_trigger_type", rule.TriggerType).WithField("event_type", eventType).Debug("Evaluating rule")

		// Check if trigger type matches
		if rule.TriggerType != eventType {
			log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("rule_trigger_type", rule.TriggerType).WithField("event_type", eventType).Debug("Rule trigger type mismatch")
			continue
		}

		// Evaluate rule conditions
		matches, err := r.EvaluateRule(ctx, rule, eventData)
		if err != nil {
			log.WithError(err).WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Error("Failed to evaluate rule")
			continue
		}

		if matches {
			log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Info("Rule matched")
			matchedRules = append(matchedRules, rule)
		} else {
			log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Debug("Rule did not match")
		}
	}

	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).WithField("total_rules", len(rules)).WithField("matched_rules", len(matchedRules)).Info("Rule matching completed")

	return matchedRules, nil
}

// EvaluateRule checks if a single rule matches the event data
func (r *ruleMatchingService) EvaluateRule(ctx context.Context, rule *model.DocumentAutomationRule, eventData map[string]interface{}) (bool, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("trigger_type", rule.TriggerType).WithField("event_data", eventData).Debug("Starting rule evaluation")

	// If no trigger rules defined, match all events of this type
	if len(rule.TriggerRules) == 0 {
		log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Debug("No trigger rules defined, matching all events")
		return true, nil
	}

	log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("trigger_rules", rule.TriggerRules).Debug("Evaluating trigger conditions")

	// Evaluate trigger conditions
	matches, err := r.EvaluateTriggerConditions(ctx, rule.TriggerRules, eventData)
	if err != nil {
		log.WithError(err).WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).Error("Failed to evaluate trigger conditions")
		return false, fmt.Errorf("failed to evaluate trigger conditions: %w", err)
	}

	log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("matches", matches).Debug("Rule evaluation completed")

	return matches, nil
}

// EvaluateTriggerConditions evaluates trigger rules against event data
func (r *ruleMatchingService) EvaluateTriggerConditions(ctx context.Context, triggerRules model.TriggerRulesMap, eventData map[string]interface{}) (bool, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("trigger_rules", triggerRules).Debug("Evaluating trigger conditions")

	// Evaluate each condition in trigger rules
	for field, expectedValue := range triggerRules {

		// Standard field evaluation
		actualValue, err := r.extractFieldValue(eventData, field)
		if err != nil {
			log.
				WithField("field", field).
				WithField("expected_value", expectedValue).
				WithField("event_data_keys", r.getEventDataKeys(eventData)).
				WithField("field_path_analysis", r.analyzeFieldPath(eventData, field)).
				WithError(err).
				Debug("Failed to extract field value - detailed analysis")
			return false, nil // Field not found, condition not met
		}

		matches, err := r.evaluateCondition(ctx, eventData, field, expectedValue, actualValue)
		if err != nil {
			return false, fmt.Errorf("failed to evaluate condition for field %s: %w", field, err)
		}

		if !matches {
			log.WithField("field", field).WithField("expected_value", expectedValue).WithField("actual_value", actualValue).Debug("Condition not met")
			return false, nil
		}

		log.WithField("field", field).WithField("expected_value", expectedValue).WithField("actual_value", actualValue).Debug("Condition met")
	}

	log.Debug("All trigger conditions met")
	return true, nil
}

// extractFieldValue extracts field value from event data using dot notation
func (r *ruleMatchingService) extractFieldValue(eventData map[string]interface{}, field string) (interface{}, error) {
	// Support dot notation (e.g., "matter.category", "client.stage")
	parts := strings.Split(field, ".")
	current := eventData

	for i, part := range parts {
		value, exists := current[part]
		if !exists {
			availableKeys := r.getMapKeys(current)
			pathSoFar := strings.Join(parts[:i+1], ".")
			return nil, fmt.Errorf("field '%s' not found at path '%s'. Available keys at this level: %v",
				part, pathSoFar, availableKeys)
		}

		// If this is the last part, return the value
		if i == len(parts)-1 {
			return value, nil
		}

		// Otherwise, continue traversing
		if nestedMap, ok := value.(map[string]interface{}); ok {
			current = nestedMap
		} else {
			pathSoFar := strings.Join(parts[:i+1], ".")
			remainingPath := strings.Join(parts[i+1:], ".")
			return nil, fmt.Errorf("cannot traverse field '%s' at path '%s' (type: %T). Cannot access remaining path: '%s'",
				part, pathSoFar, value, remainingPath)
		}
	}

	return nil, fmt.Errorf("unexpected error extracting field %s", field)
}

// getEventDataKeys returns all top-level keys in event data for debugging
func (r *ruleMatchingService) getEventDataKeys(eventData map[string]interface{}) []string {
	keys := make([]string, 0, len(eventData))
	for key := range eventData {
		keys = append(keys, key)
	}
	return keys
}

// analyzeFieldPath provides detailed analysis of why field extraction failed
func (r *ruleMatchingService) analyzeFieldPath(eventData map[string]interface{}, field string) map[string]interface{} {
	analysis := map[string]interface{}{
		"requested_field": field,
		"field_parts":     strings.Split(field, "."),
		"path_analysis":   []map[string]interface{}{},
	}

	parts := strings.Split(field, ".")
	current := eventData
	pathSoFar := ""

	for i, part := range parts {
		if i > 0 {
			pathSoFar += "."
		}
		pathSoFar += part

		stepAnalysis := map[string]interface{}{
			"step":         i + 1,
			"part":         part,
			"path_so_far":  pathSoFar,
			"is_last_part": i == len(parts)-1,
		}

		if current == nil {
			stepAnalysis["status"] = "failed"
			stepAnalysis["reason"] = "current context is nil"
			analysis["path_analysis"] = append(analysis["path_analysis"].([]map[string]interface{}), stepAnalysis)
			break
		}

		value, exists := current[part]
		if !exists {
			stepAnalysis["status"] = "failed"
			stepAnalysis["reason"] = "key not found"
			stepAnalysis["available_keys"] = r.getMapKeys(current)
			analysis["path_analysis"] = append(analysis["path_analysis"].([]map[string]interface{}), stepAnalysis)
			break
		}

		stepAnalysis["status"] = "success"
		stepAnalysis["value_type"] = fmt.Sprintf("%T", value)

		// If this is the last part, we're done
		if i == len(parts)-1 {
			stepAnalysis["final_value"] = value
			analysis["path_analysis"] = append(analysis["path_analysis"].([]map[string]interface{}), stepAnalysis)
			break
		}

		// Otherwise, check if we can continue traversing
		if nestedMap, ok := value.(map[string]interface{}); ok {
			stepAnalysis["can_traverse"] = true
			stepAnalysis["nested_keys"] = r.getMapKeys(nestedMap)
			current = nestedMap
		} else {
			stepAnalysis["can_traverse"] = false
			stepAnalysis["reason"] = "value is not a nested object"
			stepAnalysis["actual_type"] = fmt.Sprintf("%T", value)
		}

		analysis["path_analysis"] = append(analysis["path_analysis"].([]map[string]interface{}), stepAnalysis)

		// If we can't traverse further, stop
		if !stepAnalysis["can_traverse"].(bool) {
			break
		}
	}

	return analysis
}

// getMapKeys returns keys of a map for debugging
func (r *ruleMatchingService) getMapKeys(m map[string]interface{}) []string {
	if m == nil {
		return []string{}
	}
	keys := make([]string, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// ExtractFieldValueForTesting exposes extractFieldValue for testing purposes
func (r *ruleMatchingService) ExtractFieldValueForTesting(eventData map[string]interface{}, field string) (interface{}, error) {
	return r.extractFieldValue(eventData, field)
}

// evaluateCondition evaluates a single condition
func (r *ruleMatchingService) evaluateCondition(ctx context.Context, eventData map[string]interface{}, field string, expectedValue, actualValue interface{}) (bool, error) {
	_ = bilabllog.CreateContextLogger(ctx)

	// Handle different condition types
	switch expected := expectedValue.(type) {
	case string:
		// Simple string equality
		if actual, ok := actualValue.(string); ok {
			return actual == expected, nil
		}
		return false, nil

	case map[string]interface{}:
		// Complex condition with operators
		return r.evaluateComplexCondition(ctx, eventData, field, expected, actualValue)

	case []interface{}:
		// Check if it's array of conditions or simple array for IN operator
		if len(expected) > 0 {
			// Check if first element is a condition object
			if firstItem, ok := expected[0].(map[string]interface{}); ok {
				if _, hasOperator := firstItem["operator"]; hasOperator {
					// Array of condition objects - evaluate all (AND logic)
					return r.evaluateConditionArray(ctx, eventData, field, expected, actualValue)
				}
			}
		}
		// Simple array for IN operator
		return r.evaluateArrayCondition(ctx, field, expected, actualValue)

	default:
		// Direct equality comparison
		return reflect.DeepEqual(expectedValue, actualValue), nil
	}
}

// evaluateConditionArray evaluates an array of condition objects (AND logic)
func (r *ruleMatchingService) evaluateConditionArray(ctx context.Context, eventData map[string]interface{}, field string, conditions []interface{}, actualValue interface{}) (bool, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("field", field).WithField("condition_count", len(conditions)).Debug("Evaluating array of conditions")

	// All conditions must be true (AND logic)
	for i, conditionInterface := range conditions {
		condition, ok := conditionInterface.(map[string]interface{})
		if !ok {
			return false, fmt.Errorf("condition at index %d is not a valid condition object", i)
		}

		// Evaluate this condition
		matches, err := r.evaluateComplexCondition(ctx, eventData, field, condition, actualValue)
		if err != nil {
			return false, fmt.Errorf("failed to evaluate condition at index %d: %w", i, err)
		}

		if !matches {
			log.WithField("field", field).WithField("condition_index", i).WithField("condition", condition).Debug("Condition in array not met")
			return false, nil
		}

		log.WithField("field", field).WithField("condition_index", i).WithField("condition", condition).Debug("Condition in array met")
	}

	log.WithField("field", field).Debug("All conditions in array met")
	return true, nil
}

// evaluateComplexCondition evaluates complex conditions with operators
func (r *ruleMatchingService) evaluateComplexCondition(ctx context.Context, eventData map[string]interface{}, field string, condition map[string]interface{}, actualValue interface{}) (bool, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Check for legacy format: {"value": "something"} without operator
	if len(condition) == 1 {
		if value, hasValue := condition["value"]; hasValue {
			// Legacy format - treat as equality check
			log.WithField("field", field).WithField("legacy_value", value).Debug("Using legacy trigger rule format (value-only), treating as equality check")
			return reflect.DeepEqual(actualValue, value), nil
		}
	}

	// Check for standard format: {"operator": "eq", "value": "something"}
	if operator, hasOperator := condition["operator"]; hasOperator {
		if operatorStr, ok := operator.(string); ok {
			operandValue := condition["value"]
			return r.evaluateOperatorCondition(ctx, eventData, field, operatorStr, operandValue, actualValue)
		}
	}

	// Support operators: eq, ne, in, not_in, contains, not_contains
	for operator, operandValue := range condition {
		return r.evaluateOperatorCondition(ctx, eventData, field, operator, operandValue, actualValue)
	}

	return false, nil
}

// evaluateOperatorCondition evaluates a condition with a specific operator
func (r *ruleMatchingService) evaluateOperatorCondition(ctx context.Context, eventData map[string]interface{}, field string, operator string, operandValue interface{}, actualValue interface{}) (bool, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Resolve placeholders in operandValue if it's a string
	resolvedOperandValue := r.resolvePlaceholders(ctx, eventData, operandValue)

	switch operator {
	case "eq", "equals":
		return reflect.DeepEqual(actualValue, resolvedOperandValue), nil

	case "ne", "not_equals":
		return !reflect.DeepEqual(actualValue, resolvedOperandValue), nil

	case "in":
		if operandArray, ok := resolvedOperandValue.([]interface{}); ok {
			return r.evaluateArrayCondition(ctx, field, operandArray, actualValue)
		}
		return false, fmt.Errorf("'in' operator requires array operand")

	case "not_in":
		if operandArray, ok := resolvedOperandValue.([]interface{}); ok {
			inResult, err := r.evaluateArrayCondition(ctx, field, operandArray, actualValue)
			return !inResult, err
		}
		return false, fmt.Errorf("'not_in' operator requires array operand")

	case "contains":
		if actualStr, ok := actualValue.(string); ok {
			if operandStr, ok := resolvedOperandValue.(string); ok {
				return strings.Contains(actualStr, operandStr), nil
			}
		}
		return false, nil

	case "not_contains":
		if actualStr, ok := actualValue.(string); ok {
			if operandStr, ok := resolvedOperandValue.(string); ok {
				return !strings.Contains(actualStr, operandStr), nil
			}
		}
		return false, nil

	case "prefix", "starts_with":
		if actualStr, ok := actualValue.(string); ok {
			if operandStr, ok := resolvedOperandValue.(string); ok {
				return strings.HasPrefix(actualStr, operandStr), nil
			}
		}
		return false, nil

	case "suffix", "ends_with":
		if actualStr, ok := actualValue.(string); ok {
			if operandStr, ok := resolvedOperandValue.(string); ok {
				return strings.HasSuffix(actualStr, operandStr), nil
			}
		}
		return false, nil

	default:
		log.WithField("operator", operator).WithField("field", field).Warn("Unknown operator")
		return false, fmt.Errorf("unknown operator: %s", operator)
	}
}

// resolvePlaceholders resolves placeholder values in operand values
func (r *ruleMatchingService) resolvePlaceholders(ctx context.Context, eventData map[string]interface{}, operandValue interface{}) interface{} {
	log := bilabllog.CreateContextLogger(ctx)

	// Only process string values that look like placeholders
	if operandStr, ok := operandValue.(string); ok {
		// Check if it's a placeholder (starts with { and ends with })
		if strings.HasPrefix(operandStr, "{") && strings.HasSuffix(operandStr, "}") {
			// Extract field path from placeholder
			fieldPath := strings.Trim(operandStr, "{}")

			// Extract value from event data
			resolvedValue, err := r.extractFieldValue(eventData, fieldPath)
			if err != nil {
				log.WithField("placeholder", operandStr).WithField("field_path", fieldPath).WithError(err).Debug("Failed to resolve placeholder")
				return operandValue // Return original value if resolution fails
			}

			log.WithField("placeholder", operandStr).WithField("field_path", fieldPath).WithField("resolved_value", resolvedValue).Debug("Resolved placeholder")
			return resolvedValue
		}
	}

	// Return original value if not a placeholder or not a string
	return operandValue
}

// evaluateArrayCondition evaluates array conditions (IN operator)
func (r *ruleMatchingService) evaluateArrayCondition(ctx context.Context, field string, expectedArray []interface{}, actualValue interface{}) (bool, error) {
	for _, expectedItem := range expectedArray {
		if reflect.DeepEqual(actualValue, expectedItem) {
			return true, nil
		}
	}
	return false, nil
}
