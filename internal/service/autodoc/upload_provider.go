package autodoc

import (
	"context"
	"fmt"
	"time"
)

// UploadProvider defines the interface for different upload providers
type UploadProvider interface {
	// CreateUploadSession creates an upload session and returns upload URL
	CreateUploadSession(ctx context.Context, req *CreateUploadSessionRequest) (*CreateUploadSessionResponse, error)

	// RegisterUpload registers the uploaded file after successful upload
	RegisterUpload(ctx context.Context, req *RegisterUploadRequest) (*RegisterUploadResponse, error)

	// GetProviderName returns the provider name
	GetProviderName() string
}

// FileProvider defines the interface for file operations with provider-specific ID handling
type FileProvider interface {
	// GetFile gets file information by ID (provider-specific format)
	GetFile(ctx context.Context, req *GetFileRequest) (*GetFileResponse, error)

	// UpdateFile updates file information by ID (provider-specific format)
	UpdateFile(ctx context.Context, req *UpdateFileRequest) (*UpdateFileResponse, error)

	// DeleteFile deletes file by ID (provider-specific format)
	DeleteFile(ctx context.Context, req *FileDeleteRequest) (*FileDeleteResponse, error)

	// GetProviderName returns the provider name
	GetProviderName() string
}

// CreateUploadSessionRequest represents the request for creating upload session
type CreateUploadSessionRequest struct {
	TenantID         uint64 `json:"tenant_id"`
	ParentID         uint64 `json:"parent_id,omitempty"`          // Internal parent ID
	ExternalParentID string `json:"external_parent_id,omitempty"` // External parent ID
	FileName         string `json:"file_name"`
	FileSize         int64  `json:"file_size"`
	MimeType         string `json:"mime_type,omitempty"`
	ObjectType       int    `json:"object_type,omitempty"`
	ObjectID         uint64 `json:"object_id,omitempty"`
	CreatedUser      uint64 `json:"created_user"`
}

// CreateUploadSessionResponse represents the response for creating upload session
type CreateUploadSessionResponse struct {
	UploadURL    string    `json:"upload_url"`
	SessionToken string    `json:"session_token"`
	Key          string    `json:"key"` // Key returned from glob service to identify uploaded file
	ExpiresAt    time.Time `json:"expires_at"`
	Provider     string    `json:"provider"`
	Message      string    `json:"message,omitempty"`
}

// RegisterUploadRequest represents the request for registering uploaded file
type RegisterUploadRequest struct {
	TenantID     uint64 `json:"tenant_id"`
	SessionToken string `json:"session_token"`
	Key          string `json:"key"` // Key from glob service (for internal) or external file ID
	FileName     string `json:"file_name"`
	FileSize     int64  `json:"file_size"`
	MimeType     string `json:"mime_type,omitempty"`
	// Fields for internal provider compatibility
	ObjectID    uint64 `json:"object_id,omitempty"`   // Business object ID (for internal provider)
	ObjectType  int    `json:"object_type,omitempty"` // Business object type (for internal provider)
	ParentID    uint64 `json:"parent_id,omitempty"`   // Parent folder ID (for internal provider)
	ExternalID  string `json:"external_id,omitempty"` // External file ID for external providers (deprecated, use Key)
	CreatedUser uint64 `json:"created_user"`
}

// RegisterUploadResponse represents the response for registering uploaded file
type RegisterUploadResponse struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	Size       int64  `json:"size"`
	ExternalID string `json:"external_id,omitempty"`
	Provider   string `json:"provider"`
	Message    string `json:"message,omitempty"`
}

// GetFileRequest represents the request for getting file information
type GetFileRequest struct {
	ID       string `json:"id"` // File ID (provider-specific format)
	TenantID uint64 `json:"tenant_id"`
}

// GetFileResponse represents the response for getting file information
type GetFileResponse struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Size       int64  `json:"size"`
	MimeType   string `json:"mime_type,omitempty"`
	ExternalID string `json:"external_id,omitempty"`
	Provider   string `json:"provider"`
	CreatedAt  string `json:"created_at,omitempty"`
	UpdatedAt  string `json:"updated_at,omitempty"`
}

// UpdateFileRequest represents the request for updating file information
type UpdateFileRequest struct {
	ID       string `json:"id"` // File ID (provider-specific format)
	TenantID uint64 `json:"tenant_id"`
	Name     string `json:"name,omitempty"`
	// Add other updatable fields as needed
}

// UpdateFileResponse represents the response for updating file information
type UpdateFileResponse struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	Provider string `json:"provider"`
	Message  string `json:"message,omitempty"`
}

// FileDeleteRequest represents the request for deleting a file
type FileDeleteRequest struct {
	ID       string `json:"id"` // File ID (provider-specific format)
	TenantID uint64 `json:"tenant_id"`
}

// FileDeleteResponse represents the response for deleting a file
type FileDeleteResponse struct {
	ID       string `json:"id"`
	Provider string `json:"provider"`
	Message  string `json:"message,omitempty"`
}

// UploadProviderRegistry manages upload providers
type UploadProviderRegistry interface {
	// RegisterProvider registers an upload provider
	RegisterProvider(name string, provider UploadProvider)

	// GetProvider gets an upload provider by name
	GetProvider(name string) (UploadProvider, error)

	// GetDefaultProvider gets the default upload provider
	GetDefaultProvider() UploadProvider

	// ListProviders lists all registered providers
	ListProviders() []string
}

// FileProviderRegistry manages file providers
type FileProviderRegistry interface {
	// RegisterProvider registers a file provider
	RegisterProvider(name string, provider FileProvider)

	// GetProvider gets a file provider by name
	GetProvider(name string) (FileProvider, error)

	// GetDefaultProvider gets the default file provider
	GetDefaultProvider() FileProvider

	// ListProviders lists all registered providers
	ListProviders() []string
}

// uploadProviderRegistry implements UploadProviderRegistry
type uploadProviderRegistry struct {
	providers       map[string]UploadProvider
	defaultProvider string
}

// NewUploadProviderRegistry creates a new upload provider registry
func NewUploadProviderRegistry(defaultProvider string) UploadProviderRegistry {
	return &uploadProviderRegistry{
		providers:       make(map[string]UploadProvider),
		defaultProvider: defaultProvider,
	}
}

// RegisterProvider registers an upload provider
func (r *uploadProviderRegistry) RegisterProvider(name string, provider UploadProvider) {
	r.providers[name] = provider
}

// GetProvider gets an upload provider by name
func (r *uploadProviderRegistry) GetProvider(name string) (UploadProvider, error) {
	provider, exists := r.providers[name]
	if !exists {
		return nil, fmt.Errorf("upload provider not found: %s", name)
	}
	return provider, nil
}

// GetDefaultProvider gets the default upload provider
func (r *uploadProviderRegistry) GetDefaultProvider() UploadProvider {
	if r.defaultProvider != "" {
		if provider, exists := r.providers[r.defaultProvider]; exists {
			return provider
		}
	}

	// Fallback to first available provider
	for _, provider := range r.providers {
		return provider
	}

	return nil
}

// ListProviders lists all registered providers
func (r *uploadProviderRegistry) ListProviders() []string {
	var names []string
	for name := range r.providers {
		names = append(names, name)
	}
	return names
}

// fileProviderRegistry implements FileProviderRegistry
type fileProviderRegistry struct {
	providers       map[string]FileProvider
	defaultProvider string
}

// NewFileProviderRegistry creates a new file provider registry
func NewFileProviderRegistry(defaultProvider string) FileProviderRegistry {
	return &fileProviderRegistry{
		providers:       make(map[string]FileProvider),
		defaultProvider: defaultProvider,
	}
}

// RegisterProvider registers a file provider
func (r *fileProviderRegistry) RegisterProvider(name string, provider FileProvider) {
	r.providers[name] = provider
}

// GetProvider gets a file provider by name
func (r *fileProviderRegistry) GetProvider(name string) (FileProvider, error) {
	provider, exists := r.providers[name]
	if !exists {
		return nil, fmt.Errorf("file provider not found: %s", name)
	}
	return provider, nil
}

// GetDefaultProvider gets the default file provider
func (r *fileProviderRegistry) GetDefaultProvider() FileProvider {
	if r.defaultProvider != "" {
		if provider, exists := r.providers[r.defaultProvider]; exists {
			return provider
		}
	}

	// Fallback to first available provider
	for _, provider := range r.providers {
		return provider
	}

	return nil
}

// ListProviders lists all registered providers
func (r *fileProviderRegistry) ListProviders() []string {
	var names []string
	for name := range r.providers {
		names = append(names, name)
	}
	return names
}
