package autodoc_test

import (
	"context"
	"testing"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/internal/service/gdrive"
	mockrepos "bilabl/docman/mocks/repositories"
	mockautodoc "bilabl/docman/mocks/service/autodoc"
	mockgdrive "bilabl/docman/mocks/service/gdrive"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestCrossProviderOperationsFramework tests the cross-provider operations framework
func TestCrossProviderOperationsFramework(t *testing.T) {
	ctx := context.Background()

	t.Run("CopyFileHandler_CrossProvider_Framework", func(t *testing.T) {
		// Create mock dependencies
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		// Mock AutoDocRoot for path traversal
		autoDocRoot := &model.Document{
			Model: model.Model{
				ID: 1,
			},
			Name:     "AutoDoc",
			TenantID: 123,
			DocType:  model.DocTypeDir,
		}
		mockAutoDocService.On("GetAutoDocRoot", mock.Anything, uint64(123)).Return(autoDocRoot, nil)

		// Mock document repository for path traversal
		templatesFolder := &model.Document{
			Model:    model.Model{ID: 2},
			Name:     "templates",
			TenantID: 123,
			DocType:  model.DocTypeDir,
			ParentID: 1,
		}
		sourceFile := &model.Document{
			Model:    model.Model{ID: 3},
			Name:     "test.docx",
			TenantID: 123,
			DocType:  model.DocTypeFile,
			ParentID: 2,
		}

		// Mock FindOne calls for path traversal
		mockDocumentRepo.On("FindOne", mock.Anything, mock.MatchedBy(func(q *model.Query) bool {
			// Check if this is looking for "templates" folder
			for _, filter := range q.Filters {
				if filter.Key == "name" && filter.Value == "templates" {
					return true
				}
			}
			return false
		})).Return(templatesFolder, nil)

		mockDocumentRepo.On("FindOne", mock.Anything, mock.MatchedBy(func(q *model.Query) bool {
			// Check if this is looking for "test.docx" file
			for _, filter := range q.Filters {
				if filter.Key == "name" && filter.Value == "test.docx" {
					return true
				}
			}
			return false
		})).Return(sourceFile, nil)

		// Mock target parent path resolution
		targetParentResult := &autodoc.TargetParentResult{
			ParentID:   100,
			ObjectType: 1, // Client
			ObjectID:   123,
			TenantID:   123,
		}
		mockAutoDocService.On("ResolveTargetParentPath", mock.Anything, uint64(123), "client-123", mock.Anything).Return(targetParentResult, nil)

		// Mock document service registry
		mockInternalService := &mockautodoc.MockDocumentService{}

		// Create a mock GDrive service that implements GDriveServiceProvider interface
		mockGDriveDocumentService := &mockgdrive.MockDocumentService{}
		mockGDriveService := &mockGDriveServiceProvider{
			documentService: &mockautodoc.MockDocumentService{},
			gdriveService:   mockGDriveDocumentService,
		}

		mockDocumentRegistry.On("GetProvider", "internal").Return(mockInternalService, nil)
		mockDocumentRegistry.On("GetProvider", "gdrive").Return(mockGDriveService, nil)

		// Mock internal service file download
		fileContent := []byte("test file content")
		mockInternalService.On("GetDocumentContent", mock.Anything, uint64(3), uint64(123)).Return(fileContent, nil)
		mockInternalService.On("GetDocument", mock.Anything, uint64(3), uint64(123)).Return(sourceFile, nil)

		// Mock mapping service for external ID lookup
		mockMappingService := &mockautodoc.MockMappingService{}
		mockMappingService.On("GetExternalID", mock.Anything, uint64(123), "client", "google", uint64(123)).Return("gdrive-client-123", nil)

		// Mock upload registry and provider
		mockUploadRegistry := &mockautodoc.MockUploadProviderRegistry{}
		mockUploadProvider := &mockautodoc.MockUploadProvider{}

		mockUploadRegistry.On("GetProvider", "gdrive").Return(mockUploadProvider, nil)

		// Mock upload session creation and file upload
		sessionResp := &autodoc.CreateUploadSessionResponse{
			SessionToken: "test-session-token",
			Key:          "test-upload-key",
		}
		mockUploadProvider.On("CreateUploadSession", mock.Anything, mock.Anything).Return(sessionResp, nil)

		registerResp := &autodoc.RegisterUploadResponse{
			ID:         999,
			Name:       "test.docx",
			ExternalID: "gdrive-file-999",
		}
		mockUploadProvider.On("RegisterUpload", mock.Anything, mock.Anything).Return(registerResp, nil)

		// Mock Google Drive service for file content upload
		uploadResult := &gdrive.UploadContentResponse{
			Status:       "completed",
			DriveFileID:  "gdrive-file-999",
			FileName:     "test.docx",
			FileSize:     int64(len(fileContent)),
			ContentType:  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			SessionToken: "test-session-token",
		}
		mockGDriveDocumentService.On("UploadFileContent", mock.Anything, "test-session-token", mock.Anything, mock.Anything, mock.Anything).Return(uploadResult, nil)

		// Create handler
		handler := autodoc.NewCopyFileHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, mockMappingService, mockUploadRegistry)

		// Create test parameters for cross-provider copy
		params := &autodoc.ActionExecutionParams{
			TenantID: 123,
			Action: model.RuleAction{
				ActionType:     "copy_file",
				SourcePath:     "templates/test.docx",
				TargetPath:     "{client_folder}/test.docx",
				Provider:       "internal",
				TargetProvider: "gdrive",
			},
			PlaceholderData: map[string]interface{}{
				"client_folder": "client-123",
			},
		}

		// Execute - should succeed with cross-provider copy
		err := handler.Execute(ctx, params)

		// Should succeed with cross-provider copy
		assert.NoError(t, err) // Cross-provider copy should work now

		// Verify all mock expectations were met
		mockAutoDocService.AssertExpectations(t)
		mockDocumentRepo.AssertExpectations(t)
		mockDocumentRegistry.AssertExpectations(t)
		mockMappingService.AssertExpectations(t)
		mockUploadRegistry.AssertExpectations(t)
		mockUploadProvider.AssertExpectations(t)
	})

	t.Run("CopyFileHandler_SameProvider_Internal", func(t *testing.T) {
		// Create mock dependencies
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		// Create handler
		handler := autodoc.NewCopyFileHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil, nil)

		// Create test parameters for same provider (internal) copy
		params := &autodoc.ActionExecutionParams{
			TenantID: 123,
			Action: model.RuleAction{
				ActionType:     "copy_file",
				SourcePath:     "templates/test.docx",
				TargetPath:     "{client_folder}/test.docx",
				Provider:       "internal",
				TargetProvider: "internal",
			},
			PlaceholderData: map[string]interface{}{
				"client_folder": "client-123",
			},
		}

		// Test same-provider detection without full execution
		sourceProvider := params.Action.GetProvider()
		targetProvider := params.Action.GetTargetProvider()
		isCrossProvider := sourceProvider != targetProvider

		assert.Equal(t, "internal", sourceProvider)
		assert.Equal(t, "internal", targetProvider)
		assert.False(t, isCrossProvider) // Same provider

		// Use handler to avoid unused variable warning
		_ = handler
	})

	t.Run("CopyFolderHandler_Framework", func(t *testing.T) {
		// Create mock dependencies
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		// Create handler
		handler := autodoc.NewCopyFolderHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil, nil)

		// Create test parameters for cross-provider folder copy
		params := &autodoc.ActionExecutionParams{
			TenantID: 123,
			Action: model.RuleAction{
				ActionType:     "copy_folder",
				SourcePath:     "templates/folder",
				TargetPath:     "{client_folder}/folder",
				Provider:       "internal",
				TargetProvider: "gdrive",
			},
			PlaceholderData: map[string]interface{}{
				"client_folder": "client-123",
			},
		}

		// Test copy folder handler creation and basic functionality
		assert.NotNil(t, handler)
		assert.Equal(t, "copy_folder", handler.GetActionType())

		// Verify action configuration
		assert.Equal(t, "internal", params.Action.GetProvider())
		assert.Equal(t, "gdrive", params.Action.GetTargetProvider())
		assert.False(t, params.Action.IsInternalTargetProvider())
	})

	t.Run("GenerateDocumentHandler_Framework", func(t *testing.T) {
		// Create mock dependencies
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		// Create handler
		handler := autodoc.NewGenerateDocumentHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil)

		// Create test parameters for external provider generation
		params := &autodoc.ActionExecutionParams{
			TenantID: 123,
			Action: model.RuleAction{
				ActionType:     "generate_document",
				SourcePath:     "templates/contract.docx",
				TargetPath:     "{client_folder}/contract.docx",
				Provider:       "internal",
				TargetProvider: "gdrive",
			},
			PlaceholderData: map[string]interface{}{
				"client_folder": "client-123",
			},
		}

		// Test generate document handler creation and basic functionality
		assert.NotNil(t, handler)
		assert.Equal(t, "generate_document", handler.GetActionType())

		// Verify action configuration
		assert.Equal(t, "internal", params.Action.GetProvider())
		assert.Equal(t, "gdrive", params.Action.GetTargetProvider())
		assert.False(t, params.Action.IsInternalTargetProvider())
	})
}

// TestProviderOperationsStructures tests the data structures and interfaces
func TestProviderOperationsStructures(t *testing.T) {
	t.Run("CopyDocumentRequest_Structure", func(t *testing.T) {
		// Test CopyDocumentRequest structure
		req := &autodoc.CopyDocumentRequest{
			SourceID:       123,
			TargetParentID: 456,
			NewName:        "copied-file.docx",
			TenantID:       789,
			CreatedUser:    1,
		}

		assert.Equal(t, uint64(123), req.SourceID)
		assert.Equal(t, uint64(456), req.TargetParentID)
		assert.Equal(t, "copied-file.docx", req.NewName)
		assert.Equal(t, uint64(789), req.TenantID)
		assert.Equal(t, uint64(1), req.CreatedUser)
	})

	t.Run("ActionExecutionParams_Structure", func(t *testing.T) {
		// Test ActionExecutionParams structure with target provider
		params := &autodoc.ActionExecutionParams{
			TenantID: 123,
			Action: model.RuleAction{
				ActionType:     "copy_file",
				SourcePath:     "templates/test.docx",
				TargetPath:     "{client_folder}/test.docx",
				Provider:       "internal",
				TargetProvider: "gdrive",
			},
			PlaceholderData: map[string]interface{}{
				"client_folder": "client-123",
			},
			RuleID: 456,
		}

		assert.Equal(t, uint64(123), params.TenantID)
		assert.Equal(t, "copy_file", params.Action.ActionType)
		assert.Equal(t, "internal", params.Action.Provider)
		assert.Equal(t, "gdrive", params.Action.TargetProvider)
		assert.Equal(t, uint64(456), params.RuleID)
	})

	t.Run("RuleAction_TargetProvider_Methods", func(t *testing.T) {
		// Test RuleAction target provider methods
		action := model.RuleAction{
			ActionType:     "copy_file",
			Provider:       "internal",
			TargetProvider: "gdrive",
		}

		// Test GetProvider method
		assert.Equal(t, "internal", action.GetProvider())

		// Test GetTargetProvider method
		assert.Equal(t, "gdrive", action.GetTargetProvider())

		// Test SetTargetProvider method
		action.SetTargetProvider("sharepoint")
		assert.Equal(t, "sharepoint", action.GetTargetProvider())

		// Test IsInternalTargetProvider method
		action.SetTargetProvider("internal")
		assert.True(t, action.IsInternalTargetProvider())

		action.SetTargetProvider("gdrive")
		assert.False(t, action.IsInternalTargetProvider())
	})
}

// TestProviderCombinations tests different provider combinations
func TestProviderCombinations(t *testing.T) {
	testCases := []struct {
		name           string
		sourceProvider string
		targetProvider string
		expectedType   string
	}{
		{
			name:           "Internal_to_Internal",
			sourceProvider: "internal",
			targetProvider: "internal",
			expectedType:   "same_provider",
		},
		{
			name:           "Internal_to_GDrive",
			sourceProvider: "internal",
			targetProvider: "gdrive",
			expectedType:   "cross_provider",
		},
		{
			name:           "GDrive_to_SharePoint",
			sourceProvider: "gdrive",
			targetProvider: "sharepoint",
			expectedType:   "cross_provider",
		},
		{
			name:           "GDrive_to_GDrive",
			sourceProvider: "gdrive",
			targetProvider: "gdrive",
			expectedType:   "same_provider",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			action := model.RuleAction{
				Provider:       tc.sourceProvider,
				TargetProvider: tc.targetProvider,
			}

			sourceProvider := action.GetProvider()
			targetProvider := action.GetTargetProvider()

			assert.Equal(t, tc.sourceProvider, sourceProvider)
			assert.Equal(t, tc.targetProvider, targetProvider)

			// Determine operation type
			var operationType string
			if sourceProvider == targetProvider {
				operationType = "same_provider"
			} else {
				operationType = "cross_provider"
			}

			assert.Equal(t, tc.expectedType, operationType)
		})
	}
}

// mockGDriveServiceProvider implements both DocumentService and GDriveServiceProvider interfaces
type mockGDriveServiceProvider struct {
	documentService autodoc.DocumentService
	gdriveService   gdrive.DocumentService
}

// Implement DocumentService interface by delegating to embedded service
func (m *mockGDriveServiceProvider) CreateDocument(ctx context.Context, req *autodoc.CreateDocumentRequest) (*model.Document, error) {
	return m.documentService.CreateDocument(ctx, req)
}

func (m *mockGDriveServiceProvider) GetDocument(ctx context.Context, id uint64, tenantID uint64) (*model.Document, error) {
	return m.documentService.GetDocument(ctx, id, tenantID)
}

func (m *mockGDriveServiceProvider) GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error) {
	return m.documentService.GetDocumentByExternalID(ctx, externalID, tenantID)
}

func (m *mockGDriveServiceProvider) UpdateDocument(ctx context.Context, req *autodoc.UpdateDocumentRequest) (*model.Document, error) {
	return m.documentService.UpdateDocument(ctx, req)
}

func (m *mockGDriveServiceProvider) DeleteDocument(ctx context.Context, id uint64, tenantID uint64) error {
	return m.documentService.DeleteDocument(ctx, id, tenantID)
}

func (m *mockGDriveServiceProvider) DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error {
	return m.documentService.DeleteDocumentByExternalID(ctx, externalID, tenantID)
}

func (m *mockGDriveServiceProvider) CopyDocument(ctx context.Context, req *autodoc.CopyDocumentRequest) (*model.Document, error) {
	return m.documentService.CopyDocument(ctx, req)
}

func (m *mockGDriveServiceProvider) GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error) {
	return m.documentService.GetDocumentContent(ctx, documentID, tenantID)
}

func (m *mockGDriveServiceProvider) CreateFolder(ctx context.Context, req *autodoc.CreateFolderRequest) (*model.Document, error) {
	return m.documentService.CreateFolder(ctx, req)
}

func (m *mockGDriveServiceProvider) CreateFolderWithResponse(ctx context.Context, req *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error) {
	return m.documentService.CreateFolderWithResponse(ctx, req)
}

func (m *mockGDriveServiceProvider) MoveFile(ctx context.Context, req *autodoc.MoveFileRequest) (*model.Document, error) {
	return m.documentService.MoveFile(ctx, req)
}

func (m *mockGDriveServiceProvider) CopyFile(ctx context.Context, req *autodoc.CopyFileRequest) (*model.Document, error) {
	return m.documentService.CopyFile(ctx, req)
}

func (m *mockGDriveServiceProvider) GetFileMetadata(ctx context.Context, req *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error) {
	return m.documentService.GetFileMetadata(ctx, req)
}

func (m *mockGDriveServiceProvider) SetFilePermissions(ctx context.Context, req *autodoc.SetFilePermissionsRequest) error {
	return m.documentService.SetFilePermissions(ctx, req)
}

func (m *mockGDriveServiceProvider) ListFiles(ctx context.Context, req *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error) {
	return m.documentService.ListFiles(ctx, req)
}

func (m *mockGDriveServiceProvider) SearchFiles(ctx context.Context, req *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error) {
	return m.documentService.SearchFiles(ctx, req)
}

func (m *mockGDriveServiceProvider) GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error) {
	return m.documentService.GetFilePath(ctx, fileID, tenantID)
}

func (m *mockGDriveServiceProvider) ValidatePath(ctx context.Context, path string, tenantID uint64) error {
	return m.documentService.ValidatePath(ctx, path, tenantID)
}

func (m *mockGDriveServiceProvider) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	return m.documentService.EnsureAutoDocRoot(ctx, tenantID)
}

func (m *mockGDriveServiceProvider) GetProviderName() string {
	return "gdrive"
}

// Implement GDriveServiceProvider interface
func (m *mockGDriveServiceProvider) GetGDriveService() gdrive.DocumentService {
	return m.gdriveService
}

// TestHandlerCreation tests that handlers can be created with new signature
func TestHandlerCreation(t *testing.T) {
	t.Run("CopyFileHandler_Creation", func(t *testing.T) {
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		handler := autodoc.NewCopyFileHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "copy_file", handler.GetActionType())
	})

	t.Run("CopyFolderHandler_Creation", func(t *testing.T) {
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		handler := autodoc.NewCopyFolderHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "copy_folder", handler.GetActionType())
	})

	t.Run("GenerateDocumentHandler_Creation", func(t *testing.T) {
		mockAutoDocService := &mockautodoc.MockAutoDocService{}
		mockDocumentRepo := &mockrepos.MockDocumentRepository{}
		mockDocumentRegistry := &mockautodoc.MockDocumentServiceRegistry{}

		handler := autodoc.NewGenerateDocumentHandler(mockAutoDocService, mockDocumentRepo, mockDocumentRegistry, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "generate_document", handler.GetActionType())
	})
}
