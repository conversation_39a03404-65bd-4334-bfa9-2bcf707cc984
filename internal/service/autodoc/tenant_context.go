package autodoc

import (
	"context"
	"fmt"
)

// extractTenantIDFromContext extracts tenant ID from authenticated context
// This is a security-critical method that ensures tenant isolation
func (s *autoDocService) extractTenantIDFromContext(ctx context.Context) (uint64, error) {
	// TODO: Replace with actual context extraction logic based on your authentication system
	// This is a placeholder implementation - in production, you would extract from JWT token, session, etc.

	// Example implementations (choose based on your auth system):

	// Option 1: From context value set by authentication middleware
	if tenantID, ok := ctx.Value("tenant_id").(uint64); ok && tenantID > 0 {
		return tenantID, nil
	}

	// Option 2: From user context (if you have user info in context)
	if userCtx, ok := ctx.Value("user").(map[string]interface{}); ok {
		if tenantID, ok := userCtx["tenant_id"].(uint64); ok && tenantID > 0 {
			return tenantID, nil
		}
	}

	// Option 3: From JWT claims (if you store JWT in context)
	if claims, ok := ctx.Value("jwt_claims").(map[string]interface{}); ok {
		if tenantID, ok := claims["tenant_id"].(float64); ok && tenantID > 0 {
			return uint64(tenantID), nil
		}
	}

	return 0, fmt.Errorf("tenant ID not found in context - authentication required")
}
