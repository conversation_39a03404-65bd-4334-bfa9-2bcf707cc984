package autodoc

import (
	"context"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"gitlab.com/goxp/cloud0/ginext"
)

// CopyFolderHandler handles copy_folder actions
type CopyFolderHandler struct {
	*BaseActionHandler
	autoDocService   AutoDocService
	documentRepo     repositories.DocumentRepository
	pathTraverser    *PathTraverser
	documentRegistry DocumentServiceRegistry
	mappingService   MappingService
	uploadRegistry   UploadProviderRegistry
}

// NewCopyFolderHandler creates a new copy folder handler
func NewCopyFolderHandler(
	autoDocService AutoDocService,
	documentRepo repositories.DocumentRepository,
	documentRegistry DocumentServiceRegistry,
	mappingService MappingService,
	uploadRegistry UploadProviderRegistry,
) *CopyFolderHandler {
	return &CopyFolderHandler{
		BaseActionHandler: NewBaseActionHandler("copy_folder"),
		autoDocService:    autoDocService,
		documentRepo:      documentRepo,
		pathTraverser:     NewPathTraverser(documentRepo),
		documentRegistry:  documentRegistry,
		mappingService:    mappingService,
		uploadRegistry:    uploadRegistry,
	}
}

// Execute executes the copy folder action
func (h *CopyFolderHandler) Execute(ctx context.Context, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.Execute")

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	log.Infof("Executing copy_folder action source=%s target=%s source_provider=%s target_provider=%s tenant_id=%d",
		sourcePath, targetPath, params.Action.Provider, params.Action.TargetProvider, params.TenantID)

	// Resolve source path in AutoDocRoot using PathTraverser
	sourceResult, err := h.pathTraverser.TraversePathInAutoDocRoot(ctx, params.TenantID, sourcePath, h.autoDocService)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve source path=%s", sourcePath)
		return fmt.Errorf("failed to resolve source path: %w", err)
	}

	// Parse target path
	targetParentPath, targetFolderName := h.parseTargetPath(targetPath)

	// Resolve target parent path using autoDocService
	targetParentResult, err := h.autoDocService.ResolveTargetParentPath(ctx, params.TenantID, targetParentPath, params.PlaceholderData)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve target parent path=%s", targetParentPath)
		return fmt.Errorf("failed to resolve target parent path: %w", err)
	}

	// Use folder name from target path, or source folder name if target is just a placeholder
	newName := targetFolderName
	if newName == "" {
		// Target path was just a placeholder (e.g., {client_folder})
		// Use the source folder name
		newName = sourceResult.FileName
		log.Infof("Target path is placeholder only, using source folder name: %s", newName)
	}

	// Replace placeholders in new name using shared function that supports nested placeholders
	newName = ReplacePlaceholders(newName, params.PlaceholderData)
	newName = h.sanitizeFolderName(newName)

	// Get source and target providers
	sourceProvider := params.Action.GetProvider()       // Default to "internal" if empty
	targetProvider := params.Action.GetTargetProvider() // May be empty, will be resolved by engine

	// If target provider is empty, use internal as fallback (should not happen after engine resolution)
	if targetProvider == "" {
		targetProvider = "internal"
		log.Warnf("Target provider is empty, using internal fallback")
	}

	log.Infof("Using providers: source=%s target=%s", sourceProvider, targetProvider)

	// Check if this is a cross-provider copy
	if sourceProvider != targetProvider {
		return h.executeCrossProviderFolderCopy(ctx, params, sourceResult, targetParentResult, newName)
	}

	// Same provider copy - use provider-specific logic
	if sourceProvider == "internal" {
		return h.executeInternalFolderCopy(ctx, params.TenantID, sourceResult.DocumentID, targetParentResult, newName, params.Action.Override)
	}

	// External provider copy (gdrive to gdrive, sharepoint to sharepoint)
	return h.executeExternalProviderFolderCopy(ctx, params, sourceResult, targetParentResult, newName, sourceProvider)
}

// Test validates the copy_folder action configuration
func (h *CopyFolderHandler) Test(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.Test")

	result := &ActionTestResult{
		ActionType: "copy_folder",
		SourcePath: params.Action.SourcePath,
		TargetPath: params.Action.TargetPath,
		Success:    true,
	}

	// Validate required fields
	if params.Action.SourcePath == "" {
		result.Success = false
		result.Error = "source_path is required"
		return result, nil
	}
	if params.Action.TargetPath == "" {
		result.Success = false
		result.Error = "target_path is required"
		return result, nil
	}

	// Replace placeholders in paths using shared function that supports nested placeholders
	sourcePath := ReplacePlaceholders(params.Action.SourcePath, params.PlaceholderData)
	targetPath := ReplacePlaceholders(params.Action.TargetPath, params.PlaceholderData)

	result.ResolvedPath = fmt.Sprintf("%s -> %s", sourcePath, targetPath)

	log.Infof("Testing copy_folder action source=%s target=%s tenant_id=%d",
		sourcePath, targetPath, params.TenantID)

	return result, nil
}

// copyFolderInternalWithOverride copies a folder using internal document service with override support
func (h *CopyFolderHandler) copyFolderInternalWithOverride(ctx context.Context, tenantID uint64, sourceFolderID uint64, targetParent *TargetParentResult, newName string, override bool) error {
	log := logger.WithCtx(ctx, "copyFolderInternalWithContext")

	// Validate source folder exists
	_, err := h.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", sourceFolderID),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("doc_type", model.DocTypeDir),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to find source folder: %w", err)
	}

	// Check if folder with same name already exists
	existingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", targetParent.ParentID),
			model.NewFilterE("name", newName),
			model.NewFilterE("doc_type", model.DocTypeDir),
			model.NewFilterE("object_type", targetParent.ObjectType),
			model.NewFilterE("object_id", targetParent.ObjectID),
		},
	}

	log.Infof("Checking for existing folder: tenant_id=%d parent_id=%d name=%s object_type=%d object_id=%d",
		tenantID, targetParent.ParentID, newName, targetParent.ObjectType, targetParent.ObjectID)

	// First check if folder exists to avoid misleading error logs
	exists, err := h.documentRepo.Exists(ctx, existingQuery)
	if err != nil {
		log.WithError(err).Error("Failed to check folder existence")
		return fmt.Errorf("failed to check folder existence: %w", err)
	}

	var targetFolderID uint64

	if exists {
		// Folder exists, check override setting
		existingFolder, err := h.documentRepo.FindOne(ctx, existingQuery)
		if err != nil {
			log.WithError(err).Error("Failed to retrieve existing folder")
			return fmt.Errorf("failed to retrieve existing folder: %w", err)
		}

		if !override {
			// Override disabled, log warning and skip
			log.WithFields(map[string]interface{}{
				"existing_folder_id":   existingFolder.ID,
				"existing_folder_name": existingFolder.Name,
				"source_folder_name":   newName,
				"override_setting":     override,
			}).Warn("Folder with same name already exists, skipping copy due to override=false")
			return nil
		}

		// Override enabled, use existing folder for copying children
		targetFolderID = existingFolder.ID
		log.Infof("Found existing folder, will copy children into it source_id=%d existing_folder_id=%d name=%s",
			sourceFolderID, existingFolder.ID, newName)
	} else {
		log.Info("No existing folder found, will create new one")
		// Folder doesn't exist, create new one
		copyDoc := &model.Document{
			TenantID:    tenantID,
			ParentID:    targetParent.ParentID,
			Name:        newName,
			DocType:     model.DocTypeDir,
			ObjectType:  targetParent.ObjectType, // Use target parent's object type
			ObjectID:    targetParent.ObjectID,   // Use target parent's object ID
			Status:      1,                       // Active
			CreatedUser: 1,                       // System user
		}

		// Create the folder copy using document repository
		err = h.documentRepo.Create(ctx, copyDoc)
		if err != nil {
			return fmt.Errorf("failed to create folder copy: %w", err)
		}

		targetFolderID = copyDoc.ID
		log.Infof("Successfully created new folder copy source_id=%d parent_id=%d object_type=%d object_id=%d name=%s new_id=%d",
			sourceFolderID, targetParent.ParentID, targetParent.ObjectType, targetParent.ObjectID, newName, copyDoc.ID)
	}

	// Copy children (level 1) from source folder
	// Note: Children will inherit the same object_type and object_id from the parent folder
	err = h.copyFolderChildrenWithOverride(ctx, tenantID, sourceFolderID, targetFolderID)
	if err != nil {
		log.WithError(err).Errorf("Failed to copy children from source_id=%d to target_id=%d",
			sourceFolderID, targetFolderID)
		// Don't fail the entire operation if children copy fails
		// Log the error and continue
	}

	return nil
}

// copyFolderChildrenWithOverride copies all direct children with override support
func (h *CopyFolderHandler) copyFolderChildrenWithOverride(ctx context.Context, tenantID uint64, sourceFolderID, targetFolderID uint64) error {
	log := logger.WithCtx(ctx, "copyFolderChildrenWithOverride")

	// Find all direct children of source folder
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", sourceFolderID),
		},
	}

	children, err := h.documentRepo.List(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to find children of source folder: %w", err)
	}

	log.Infof("Found %d children to copy with override from source_id=%d to target_id=%d",
		len(children), sourceFolderID, targetFolderID)

	// Copy each child with override support
	for _, child := range children {
		err := h.copyChildDocumentWithOverride(ctx, tenantID, child, targetFolderID)
		if err != nil {
			log.WithError(err).Errorf("Failed to copy child document with override id=%d name=%s",
				child.ID, child.Name)
			// Continue with other children even if one fails
			continue
		}

		log.Debugf("Successfully copied child with override id=%d name=%s type=%d",
			child.ID, child.Name, child.DocType)
	}

	return nil
}

// copyChildDocumentWithOverride copies a single child document with override support
func (h *CopyFolderHandler) copyChildDocumentWithOverride(ctx context.Context, tenantID uint64, sourceDoc *model.Document, targetParentID uint64) error {
	log := logger.WithCtx(ctx, "copyChildDocumentWithOverride")

	// Check if document with same name already exists in target location
	existingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", targetParentID),
			model.NewFilterE("name", sourceDoc.Name),
			model.NewFilterE("doc_type", sourceDoc.DocType),
			model.NewFilterE("object_type", sourceDoc.ObjectType),
			model.NewFilterE("object_id", sourceDoc.ObjectID),
		},
	}

	existingDoc, err := h.documentRepo.FindOne(ctx, existingQuery)

	if err == nil && existingDoc != nil {
		// Document exists, override it (update key and timestamp)
		updateDoc := &model.Document{
			Key:    sourceDoc.Key,
			Status: 1, // Ensure it's active
		}

		updateQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", existingDoc.ID),
				model.NewFilterE("tenant_id", tenantID),
			},
		}

		err = h.documentRepo.UpdateOne(ctx, updateQuery, updateDoc)
		if err != nil {
			return fmt.Errorf("failed to override existing document: %w", err)
		}

		log.Infof("Successfully overrode existing document id=%d name=%s key=%s",
			existingDoc.ID, existingDoc.Name, sourceDoc.Key)

		return nil
	}

	// Document doesn't exist, create new one
	copyDoc := &model.Document{
		TenantID:    tenantID,
		ParentID:    targetParentID,
		Name:        sourceDoc.Name, // Keep original name
		DocType:     sourceDoc.DocType,
		Key:         sourceDoc.Key, // Copy the same key
		ObjectType:  sourceDoc.ObjectType,
		ObjectID:    sourceDoc.ObjectID,
		Status:      1, // Active
		CreatedUser: 1, // System user
	}

	// Create the copy using document repository
	err = h.documentRepo.Create(ctx, copyDoc)
	if err != nil {
		return fmt.Errorf("failed to create child document copy: %w", err)
	}

	log.Infof("Successfully created new child document id=%d name=%s key=%s",
		copyDoc.ID, copyDoc.Name, copyDoc.Key)

	return nil
}

// parseTargetPath parses a target path into parent path and folder name
func (h *CopyFolderHandler) parseTargetPath(targetPath string) (parentPath, folderName string) {
	parts := strings.Split(targetPath, "/")
	if len(parts) == 1 {
		// Single component - check if it's a placeholder like {client_folder}
		singlePart := parts[0]
		if strings.HasPrefix(singlePart, "{") && strings.HasSuffix(singlePart, "}") {
			// This is a placeholder that should be resolved as parent path
			// Return the placeholder as parent path and empty folder name
			return singlePart, ""
		}
		// Regular folder name without parent path
		return "", singlePart
	}
	return strings.Join(parts[:len(parts)-1], "/"), parts[len(parts)-1]
}

// sanitizeFolderName removes invalid characters from folder name
func (h *CopyFolderHandler) sanitizeFolderName(folderName string) string {
	if folderName == "" {
		return "copied_folder"
	}

	// Replace invalid characters
	replacements := map[string]string{
		"/":  "_",
		"\\": "_",
		":":  "_",
		"*":  "_",
		"?":  "_",
		"\"": "_",
		"<":  "_",
		">":  "_",
		"|":  "_",
	}

	result := folderName
	for invalid, replacement := range replacements {
		result = strings.ReplaceAll(result, invalid, replacement)
	}

	return strings.TrimSpace(result)
}

// executeInternalFolderCopy handles internal-to-internal folder copy (existing logic)
func (h *CopyFolderHandler) executeInternalFolderCopy(ctx context.Context, tenantID uint64, sourceDocID uint64, targetParent *TargetParentResult, newName string, override bool) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.executeInternalFolderCopy")

	// For internal provider, adjust target parent for object contexts
	// In internal provider, client/matter folders are virtual groupings
	// Folders should be created at root level (parent_id=0) with object context
	adjustedTargetParent := targetParent
	if targetParent.ObjectType > 0 && targetParent.ObjectID > 0 && targetParent.ParentID > 0 {
		// This is an object context (client_folder/matter_folder) with a folder parent
		// For internal provider, create folders at root level with object context
		adjustedTargetParent = &TargetParentResult{
			ParentID:   0, // Root level for internal provider
			ObjectType: targetParent.ObjectType,
			ObjectID:   targetParent.ObjectID,
			TenantID:   targetParent.TenantID,
		}
		log.Infof("Internal provider: adjusted object context to parent_id=0 for object_type=%d object_id=%d",
			targetParent.ObjectType, targetParent.ObjectID)
	}

	// Use existing internal copy logic
	err := h.copyFolderInternalWithOverride(ctx, tenantID, sourceDocID, adjustedTargetParent, newName, override)
	if err != nil {
		log.WithError(err).Error("Failed to copy folder internally")
		return fmt.Errorf("failed to copy folder internally: %w", err)
	}

	log.Infof("Successfully copied folder internally source_id=%d target_parent_id=%d new_name=%s",
		sourceDocID, targetParent.ParentID, newName)

	return nil
}

// executeCrossProviderFolderCopy handles cross-provider folder copy
func (h *CopyFolderHandler) executeCrossProviderFolderCopy(ctx context.Context, params *ActionExecutionParams, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.executeCrossProviderFolderCopy")

	sourceProvider := params.Action.GetProvider()
	targetProvider := params.Action.GetTargetProvider()

	log.Infof("Cross-provider folder copy: %s -> %s source_doc_id=%d target_parent_id=%d new_name=%s",
		sourceProvider, targetProvider, sourceResult.DocumentID, targetParent.ParentID, newName)

	// Step 1: Get source document service
	sourceDocService, err := h.documentRegistry.GetProvider(sourceProvider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get source document service for provider=%s", sourceProvider)
		return fmt.Errorf("failed to get source document service for provider %s: %w", sourceProvider, err)
	}

	// Step 2: Get target document service
	targetDocService, err := h.documentRegistry.GetProvider(targetProvider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get target document service for provider=%s", targetProvider)
		return fmt.Errorf("failed to get target document service for provider %s: %w", targetProvider, err)
	}

	// Step 3: Create target folder in target provider
	targetFolderResult, err := h.createFolderInTargetProvider(ctx, targetDocService, targetProvider, targetParent, newName, params)
	if err != nil {
		log.WithError(err).Errorf("Failed to create target folder in provider=%s", targetProvider)
		return fmt.Errorf("failed to create target folder in provider %s: %w", targetProvider, err)
	}

	log.Infof("Created target folder: provider=%s name=%s", targetProvider, newName)

	// Step 4: Recursively copy all contents from source to target
	err = h.copyFolderContentsRecursively(ctx, sourceDocService, targetDocService, sourceProvider, targetProvider, sourceResult.DocumentID, targetFolderResult, params)
	if err != nil {
		log.WithError(err).Errorf("Failed to copy folder contents recursively")
		return fmt.Errorf("failed to copy folder contents recursively: %w", err)
	}

	log.Infof("Successfully completed cross-provider folder copy: %s -> %s", sourceProvider, targetProvider)
	return nil
}

// executeExternalProviderFolderCopy handles same external provider folder copy
func (h *CopyFolderHandler) executeExternalProviderFolderCopy(ctx context.Context, params *ActionExecutionParams, sourceResult *PathResolutionResult, targetParent *TargetParentResult, newName string, provider string) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.executeExternalProviderFolderCopy")

	log.Infof("External provider folder copy: %s source_doc_id=%d target_parent_id=%d new_name=%s",
		provider, sourceResult.DocumentID, targetParent.ParentID, newName)

	// Get document service for the provider
	documentService, err := h.documentRegistry.GetProvider(provider)
	if err != nil {
		log.WithError(err).Errorf("Failed to get document service for provider=%s", provider)
		return fmt.Errorf("failed to get document service for provider %s: %w", provider, err)
	}

	// TODO: Implement external provider folder copy logic
	// This would use the document service to copy folder within the same provider

	_ = documentService // Use the service
	return fmt.Errorf("external provider folder copy not yet implemented for provider: %s", provider)
}

// createFolderInTargetProvider creates a folder in the target provider
func (h *CopyFolderHandler) createFolderInTargetProvider(ctx context.Context, targetDocService DocumentService, targetProvider string, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.createFolderInTargetProvider")

	log.Infof("Creating folder in target provider=%s name=%s parent_id=%d", targetProvider, newName, targetParent.ParentID)

	switch targetProvider {
	case "internal":
		return h.createFolderInInternal(ctx, targetParent, newName, params)
	case "gdrive":
		return h.createFolderInGoogleDrive(ctx, targetDocService, targetParent, newName, params)
	case "sharepoint":
		return h.createFolderInSharePoint(ctx, targetDocService, targetParent, newName, params)
	default:
		return nil, fmt.Errorf("unsupported target provider: %s", targetProvider)
	}
}

// createFolderInInternal creates a folder in internal storage
func (h *CopyFolderHandler) createFolderInInternal(ctx context.Context, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.createFolderInInternal")

	// Create folder document in internal storage
	folderDoc := &model.Document{
		TenantID:    params.TenantID,
		ParentID:    targetParent.ParentID,
		Name:        newName,
		DocType:     model.DocTypeDir,
		ObjectType:  targetParent.ObjectType,
		ObjectID:    targetParent.ObjectID,
		Status:      1, // Active
		CreatedUser: 1, // System user
	}

	err := h.documentRepo.Create(ctx, folderDoc)
	if err != nil {
		log.WithError(err).Errorf("Failed to create internal folder name=%s", newName)
		return nil, fmt.Errorf("failed to create internal folder: %w", err)
	}

	log.Infof("Created internal folder: id=%d name=%s", folderDoc.ID, folderDoc.Name)

	// Return target parent result for the newly created folder
	return &TargetParentResult{
		ParentID:   folderDoc.ID,
		ObjectType: targetParent.ObjectType,
		ObjectID:   targetParent.ObjectID,
	}, nil
}

// createFolderInGoogleDrive creates a folder in Google Drive
func (h *CopyFolderHandler) createFolderInGoogleDrive(ctx context.Context, targetDocService DocumentService, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.createFolderInGoogleDrive")

	log.Infof("Creating Google Drive folder name=%s parent_id=%d", newName, targetParent.ParentID)

	// Step 1: Cast to GDrive adapter to access wrapped service (pattern from generate_document_handler)
	gdriveAdapter, ok := targetDocService.(*gdriveAdapter)
	if !ok {
		log.Errorf("Failed to cast to GDrive adapter, got type: %T", targetDocService)
		return nil, fmt.Errorf("failed to cast to GDrive document service interface")
	}

	gdriveService := gdriveAdapter.gdriveDocService

	// Step 2: Resolve parent folder ID using tenant config (pattern from generate_document_handler)
	parentDriveID, err := h.resolveGDriveParentID(ctx, targetParent, params.TenantID, gdriveService)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve Google Drive parent ID for parent_id=%d", targetParent.ParentID)
		return nil, fmt.Errorf("failed to resolve Google Drive parent ID: %w", err)
	}

	// Step 3: Create folder in Google Drive using the service
	// Note: We need to access the underlying Google Drive client
	gdriveClient := gdriveService.GetGDriveClient()

	// Create folder using Google Drive API
	driveFile, err := gdriveClient.CreateFolder(newName, parentDriveID)
	if err != nil {
		log.WithError(err).Errorf("Failed to create folder in Google Drive name=%s parent_id=%s", newName, parentDriveID)
		return nil, fmt.Errorf("failed to create folder in Google Drive: %w", err)
	}

	log.Infof("Created Google Drive folder: name=%s drive_id=%s", driveFile.Name, driveFile.Id)

	// Note: For copy operations, we don't create DocumentMapping
	// DocumentMapping is only for sync operations (client/matter folders)
	// Copy operations create independent copies without business object relationships

	// Step 5: Return target parent result for the newly created Google Drive folder
	// For cross-provider copy, we create a temporary TargetParentResult to continue recursive copy
	// The external folder ID (driveFile.Id) will be used for subsequent child operations
	return &TargetParentResult{
		ParentID:         0, // No internal parent ID for external folders
		ObjectType:       targetParent.ObjectType,
		ObjectID:         targetParent.ObjectID,
		TenantID:         params.TenantID,
		ExternalParentID: driveFile.Id, // Store Google Drive folder ID for child operations
	}, nil
}

// resolveGDriveParentID resolves Google Drive parent folder ID using shared logic from generate_document_handler
func (h *CopyFolderHandler) resolveGDriveParentID(ctx context.Context, targetParent *TargetParentResult, tenantID uint64, gdriveService gdrive.DocumentService) (string, error) {
	return ResolveGDriveParentID(ctx, targetParent, tenantID, gdriveService, h.mappingService)
}

// createFolderInSharePoint creates a folder in SharePoint (placeholder implementation)
func (h *CopyFolderHandler) createFolderInSharePoint(ctx context.Context, targetDocService DocumentService, targetParent *TargetParentResult, newName string, params *ActionExecutionParams) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.createFolderInSharePoint")

	log.Infof("Creating SharePoint folder name=%s parent_id=%d", newName, targetParent.ParentID)

	// TODO: Implement SharePoint folder creation
	// This would involve:
	// 1. Cast to SharePoint adapter
	// 2. Resolve parent folder ID using tenant config
	// 3. Create folder using SharePoint API
	// 4. Create DocumentMapping for the new folder

	return nil, fmt.Errorf("sharepoint folder creation not yet implemented")
}

// copyFolderContentsRecursively recursively copies all contents from source folder to target folder
func (h *CopyFolderHandler) copyFolderContentsRecursively(ctx context.Context, sourceDocService DocumentService, targetDocService DocumentService, sourceProvider string, targetProvider string, sourceFolderID uint64, targetFolderResult *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.copyFolderContentsRecursively")

	log.Infof("Recursively copying folder contents: source_provider=%s target_provider=%s source_folder_id=%d target_parent_id=%d",
		sourceProvider, targetProvider, sourceFolderID, targetFolderResult.ParentID)

	// Step 1: List all children in source folder
	children, err := h.listFolderChildren(ctx, sourceDocService, sourceFolderID, params.TenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to list children of source folder source_folder_id=%d", sourceFolderID)
		return fmt.Errorf("failed to list children of source folder: %w", err)
	}

	log.Infof("Found %d children in source folder", len(children))

	// Step 2: Copy each child (file or folder)
	for _, child := range children {
		err := h.copyChildItem(ctx, sourceDocService, targetDocService, sourceProvider, targetProvider, child, targetFolderResult, params)
		if err != nil {
			log.WithError(err).Errorf("Failed to copy child item child_id=%d child_name=%s", child.ID, child.Name)
			// Continue with other children instead of failing the entire operation
			log.Warnf("Skipping failed child copy: child_id=%d child_name=%s", child.ID, child.Name)
			continue
		}
		log.Infof("Successfully copied child item: child_id=%d child_name=%s", child.ID, child.Name)
	}

	log.Infof("Completed recursive folder copy for source_folder_id=%d", sourceFolderID)
	return nil
}

// listFolderChildren lists all children (files and folders) in a folder
func (h *CopyFolderHandler) listFolderChildren(ctx context.Context, documentService DocumentService, folderID uint64, tenantID uint64) ([]*model.Document, error) {
	log := logger.WithCtx(ctx, "CopyFolderHandler.listFolderChildren")

	log.Infof("Listing children of folder folder_id=%d tenant_id=%d", folderID, tenantID)

	// Use document repository to find children
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("parent_id", folderID),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("status", 1), // Active documents only
		},
	}

	// Use a large page size to get all children
	pager := &ginext.Pager{
		Page:     1,
		PageSize: 1000, // Large page size to get all children
	}

	children, err := h.documentRepo.Find(ctx, query, pager)
	if err != nil {
		log.WithError(err).Errorf("Failed to find children of folder folder_id=%d", folderID)
		return nil, fmt.Errorf("failed to find children of folder: %w", err)
	}

	log.Infof("Found %d children in folder folder_id=%d", len(children), folderID)
	return children, nil
}

// copyChildItem copies a single child item (file or folder) from source to target
func (h *CopyFolderHandler) copyChildItem(ctx context.Context, sourceDocService DocumentService, targetDocService DocumentService, sourceProvider string, targetProvider string, child *model.Document, targetParent *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.copyChildItem")

	log.Infof("Copying child item: child_id=%d child_name=%s child_type=%d source_provider=%s target_provider=%s",
		child.ID, child.Name, child.DocType, sourceProvider, targetProvider)

	if child.DocType == model.DocTypeDir {
		// Child is a folder - recursively copy it
		return h.copyChildFolder(ctx, sourceDocService, targetDocService, sourceProvider, targetProvider, child, targetParent, params)
	} else {
		// Child is a file - use cross-provider file copy logic
		return h.copyChildFile(ctx, sourceDocService, targetDocService, sourceProvider, targetProvider, child, targetParent, params)
	}
}

// copyChildFile copies a file from source to target using cross-provider file copy logic
func (h *CopyFolderHandler) copyChildFile(ctx context.Context, sourceDocService DocumentService, targetDocService DocumentService, sourceProvider string, targetProvider string, child *model.Document, targetParent *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.copyChildFile")

	log.Infof("Copying child file: file_id=%d file_name=%s source_provider=%s target_provider=%s",
		child.ID, child.Name, sourceProvider, targetProvider)

	// Create a copy file handler to handle the file copy
	copyFileHandler := NewCopyFileHandler(
		h.autoDocService,
		h.documentRepo,
		h.documentRegistry,
		h.mappingService,
		h.uploadRegistry, // Pass uploadRegistry from folder handler
	)

	// Create path resolution result for the source file
	sourceResult := &PathResolutionResult{
		DocumentID: child.ID,
		ParentID:   child.ParentID,
		FileName:   child.Name,
		IsFile:     child.DocType == model.DocTypeFile,
	}

	// Execute cross-provider file copy
	return copyFileHandler.executeCrossProviderCopy(ctx, params, sourceResult, targetParent, child.Name)
}

// copyChildFolder recursively copies a child folder from source to target
func (h *CopyFolderHandler) copyChildFolder(ctx context.Context, sourceDocService DocumentService, targetDocService DocumentService, sourceProvider string, targetProvider string, child *model.Document, targetParent *TargetParentResult, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "CopyFolderHandler.copyChildFolder")

	log.Infof("Recursively copying child folder: folder_id=%d folder_name=%s source_provider=%s target_provider=%s",
		child.ID, child.Name, sourceProvider, targetProvider)

	// Step 1: Create the child folder in target provider
	childTargetResult, err := h.createFolderInTargetProvider(ctx, targetDocService, targetProvider, targetParent, child.Name, params)
	if err != nil {
		log.WithError(err).Errorf("Failed to create child folder in target provider folder_name=%s", child.Name)
		return fmt.Errorf("failed to create child folder in target provider: %w", err)
	}

	log.Infof("Created child folder in target provider: folder_name=%s", child.Name)

	// Step 2: Recursively copy all contents of the child folder
	err = h.copyFolderContentsRecursively(ctx, sourceDocService, targetDocService, sourceProvider, targetProvider, child.ID, childTargetResult, params)
	if err != nil {
		log.WithError(err).Errorf("Failed to recursively copy contents of child folder folder_id=%d", child.ID)
		return fmt.Errorf("failed to recursively copy contents of child folder: %w", err)
	}

	log.Infof("Successfully completed recursive copy of child folder: folder_id=%d folder_name=%s", child.ID, child.Name)
	return nil
}
