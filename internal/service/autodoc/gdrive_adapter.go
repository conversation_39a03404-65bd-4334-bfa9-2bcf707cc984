package autodoc

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/repositories"
)

// gdriveAdapter implements DocumentService interface by adapting existing GDrive service
type gdriveAdapter struct {
	gdriveDocService gdrive.DocumentService
	mappingService   MappingService
	settingRepo      repositories.DocumentSettingRepository
}

// NewGDriveAdapter creates a new Google Drive adapter
func NewGDriveAdapter(
	gdriveDocService gdrive.DocumentService,
	mappingService MappingService,
	settingRepo repositories.DocumentSettingRepository,
) DocumentService {
	return &gdriveAdapter{
		gdriveDocService: gdriveDocService,
		mappingService:   mappingService,
		settingRepo:      settingRepo,
	}
}

// GetGDriveService returns the underlying Google Drive service
func (a *gdriveAdapter) GetGDriveService() gdrive.DocumentService {
	return a.gdriveDocService
}

// CreateDocument creates a document in Google Drive and maintains internal mapping
func (a *gdriveAdapter) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Validate request
	if err := a.validateCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid create request: %w", err)
	}

	// Resolve parent ID - prefer external ID for Google Drive
	parentID, err := a.resolveParentID(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve parent ID: %w", err)
	}

	// Convert to GDrive request
	gdriveReq := &gdrive.CreateDocumentRequest{
		Name:       req.Name,
		ParentID:   parentID,
		TenantID:   req.TenantID,
		ObjectType: a.convertObjectType(req.ObjectType),
		ObjectID:   req.ObjectID,
		DocType:    req.DocType,
	}

	// Create document in Google Drive
	gdriveResp, err := a.gdriveDocService.CreateDocument(ctx, gdriveReq)
	if err != nil {
		log.WithError(err).Error("failed to create document in Google Drive")
		return nil, fmt.Errorf("failed to create document in Google Drive: %w", err)
	}

	// Only create mapping for object-level folders (client/matter)
	// Sub-documents are managed entirely by Google Drive
	if a.shouldCreateMapping(req) {
		mappingReq := &CreateMappingRequest{
			TenantID:       req.TenantID,
			Type:           a.getDocumentType(req.ObjectType),
			ObjectID:       req.ObjectID, // Use ObjectID directly, not internal doc ID
			ParentObjectID: 0,            // Object-level folders have no parent object
			ExternalID:     gdriveResp.DriveFileID,
			ParentDriveID:  gdriveResp.ParentDriveID,
			Provider:       "gdrive",
		}

		_, err = a.mappingService.CreateMapping(ctx, mappingReq)
		if err != nil {
			log.WithError(err).Error("failed to create document mapping")

			// Rollback: delete from Google Drive
			if deleteErr := a.gdriveDocService.DeleteDocument(ctx, gdriveResp.DriveFileID); deleteErr != nil {
				log.WithError(deleteErr).Error("failed to rollback Google Drive document creation")
			}

			return nil, fmt.Errorf("failed to create document mapping: %w", err)
		}

		log.WithFields(map[string]interface{}{
			"document_name":   req.Name,
			"object_id":       req.ObjectID,
			"drive_file_id":   gdriveResp.DriveFileID,
			"parent_drive_id": gdriveResp.ParentDriveID,
		}).Info("object folder mapping created successfully in Google Drive")
	}

	// Return minimal Document struct with Google Drive info
	// No internal document record needed - Google Drive manages everything
	return &model.Document{
		Name:        gdriveResp.Name,
		TenantID:    req.TenantID,
		ObjectType:  req.ObjectType,
		ObjectID:    req.ObjectID,
		DocType:     req.DocType,
		CreatedUser: req.CreatedUser,
		UpdatedUser: req.CreatedUser,
		Status:      1, // Active
	}, nil
}

// shouldCreateMapping determines if mapping is needed for this document
// Only create mapping for object-level folders (client/matter), not for sub-documents
func (a *gdriveAdapter) shouldCreateMapping(req *CreateDocumentRequest) bool {
	// Only create mapping for object-level folders
	// These are folders that represent business objects (client/matter)
	return req.ObjectType > 0 && req.ObjectID > 0
}

// resolveParentID resolves parent ID for Google Drive operations
// Prefers ExternalParentID over ParentID for external providers
func (a *gdriveAdapter) resolveParentID(ctx context.Context, req *CreateDocumentRequest) (string, error) {
	// If external parent ID is provided, use it directly
	if req.ExternalParentID != "" {
		return req.ExternalParentID, nil
	}

	// If internal parent ID is provided, we need to determine the object type
	// This is problematic because ParentID could be a business object ID but we don't know the type
	if req.ParentID != 0 {
		// For now, assume it's an object folder and try to resolve
		// This is not ideal - should use ExternalParentID instead
		return "", fmt.Errorf("resolving internal ParentID requires object type context - use ExternalParentID instead")
	}

	// No parent specified - use AutoDoc root
	autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
	if err != nil {
		return "", fmt.Errorf("failed to ensure AutoDoc root: %w", err)
	}

	externalID, err := a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get AutoDoc root external ID: %w", err)
	}

	return externalID, nil
}

// GetDocumentByExternalID retrieves a document by external Google Drive ID
func (a *gdriveAdapter) GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Get document from Google Drive
	gdriveResp, err := a.gdriveDocService.GetDocument(ctx, externalID)
	if err != nil {
		log.WithError(err).Error("failed to get document from Google Drive")
		return nil, fmt.Errorf("failed to get document from Google Drive: %w", err)
	}

	// Return document info from Google Drive
	return &model.Document{
		Name:     gdriveResp.Name,
		TenantID: tenantID,
		DocType:  gdriveResp.DocType,
		Size:     gdriveResp.Size,
		Status:   1, // Active
		Model: model.Model{
			UpdatedAt: gdriveResp.UpdatedAt,
		},
	}, nil
}

// DeleteDocumentByExternalID deletes a document by external Google Drive ID
func (a *gdriveAdapter) DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Delete from Google Drive
	err := a.gdriveDocService.DeleteDocument(ctx, externalID)
	if err != nil {
		log.WithError(err).Error("failed to delete document from Google Drive")
		return fmt.Errorf("failed to delete document from Google Drive: %w", err)
	}

	// Try to delete mapping if it exists (for object-level folders)
	// We need to find the ObjectID from the external ID first
	// This is optional - sub-documents don't have mappings
	err = a.mappingService.DeleteMappingByExternalID(ctx, externalID, "gdrive", tenantID)
	if err != nil {
		log.WithError(err).Warn("failed to delete mapping by external ID (may not exist for sub-documents)")
		// Don't fail the operation - sub-documents don't have mappings
	}

	log.WithFields(map[string]interface{}{
		"external_id": externalID,
		"tenant_id":   tenantID,
	}).Info("document deleted successfully by external ID")

	return nil
}

// GetDocument retrieves a document by business object ID
// NOTE: For Google Drive adapter, documentID should be the business object ID (client/matter ID)
// This method is primarily for getting object-level folders, not individual files
func (a *gdriveAdapter) GetDocument(ctx context.Context, documentID uint64, tenantID uint64) (*model.Document, error) {
	// This method is problematic because we don't know the object type
	// It should be deprecated in favor of GetDocumentByExternalID for direct file access
	// or use business context methods
	return nil, fmt.Errorf("GetDocument with internal ID is not supported for Google Drive adapter - use GetDocumentByExternalID or business context methods instead")
}

// UpdateDocument updates a document
func (a *gdriveAdapter) UpdateDocument(ctx context.Context, req *UpdateDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Resolve external ID - prefer ExternalID over ID
	var externalID string
	var err error

	if req.ExternalID != "" {
		// Use external ID directly
		externalID = req.ExternalID
	} else if req.ID != 0 {
		// For Google Drive adapter, ID should not be used without object type context
		// This is problematic - we need object type to resolve mapping
		return nil, fmt.Errorf("UpdateDocument with internal ID requires object type context - use ExternalID instead")
	} else {
		return nil, fmt.Errorf("ExternalID must be provided for Google Drive adapter")
	}

	// Convert to GDrive update request
	gdriveReq := &gdrive.UpdateDocumentRequest{
		Name: req.Name,
	}

	// Update document in Google Drive
	gdriveResp, err := a.gdriveDocService.UpdateDocument(ctx, externalID, gdriveReq)
	if err != nil {
		log.WithError(err).Error("failed to update document in Google Drive")
		return nil, fmt.Errorf("failed to update document in Google Drive: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"object_id":   req.ID,
		"external_id": externalID,
		"name":        gdriveResp.Name,
	}).Info("document updated successfully with Google Drive adapter")

	// Return updated document info from Google Drive
	return &model.Document{
		Name:        gdriveResp.Name,
		TenantID:    req.TenantID,
		DocType:     gdriveResp.DocType,
		Size:        gdriveResp.Size,
		UpdatedUser: req.UpdatedUser,
		Status:      1, // Active
		Model: model.Model{
			UpdatedAt: gdriveResp.UpdatedAt,
		},
	}, nil
}

// DeleteDocument deletes a document by business object ID
// NOTE: This method is problematic because we don't know the object type
func (a *gdriveAdapter) DeleteDocument(ctx context.Context, documentID uint64, tenantID uint64) error {
	// This method is problematic because we don't know the object type
	// It should be deprecated in favor of DeleteDocumentByExternalID for direct file access
	return fmt.Errorf("DeleteDocument with internal ID is not supported for Google Drive adapter - use DeleteDocumentByExternalID instead")
}

// validateCreateRequest validates the create document request
func (a *gdriveAdapter) validateCreateRequest(req *CreateDocumentRequest) error {
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}

	if req.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if req.CreatedUser == 0 {
		return fmt.Errorf("created_user is required")
	}

	return nil
}

// convertObjectType converts internal object type to GDrive object type
func (a *gdriveAdapter) convertObjectType(objectType int) string {
	switch objectType {
	case 1:
		return "client"
	case 2:
		return "matter"
	default:
		return "document"
	}
}

// getDocumentType gets document type for mapping based on object type
func (a *gdriveAdapter) getDocumentType(objectType int) string {
	switch objectType {
	case 1:
		return model.DocTypeClient
	case 2:
		return model.DocTypeMatter
	default:
		return model.DocTypeParent
	}
}



// CopyDocument copies a document to a new location
func (a *gdriveAdapter) CopyDocument(ctx context.Context, req *CopyDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// PROBLEM: SourceID and TargetParentID don't have object type context
	// This method should use ExternalSourceID and ExternalTargetParentID instead
	if req.ExternalSourceID == "" || req.ExternalTargetParentID == "" {
		return nil, fmt.Errorf("CopyDocument requires ExternalSourceID and ExternalTargetParentID for Google Drive adapter")
	}

	sourceExternalID := req.ExternalSourceID
	targetParentExternalID := req.ExternalTargetParentID

	// Note: We don't need source document info for copying
	// Google Drive handles the copy operation directly

	// Create copy in Google Drive (using create with same content)
	createReq := &gdrive.CreateDocumentRequest{
		Name:     req.NewName,
		ParentID: targetParentExternalID,
		TenantID: req.TenantID,
		DocType:  model.DocTypeFile, // Assume file copy
	}

	gdriveResp, err := a.gdriveDocService.CreateDocument(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("failed to create copy in Google Drive")
		return nil, fmt.Errorf("failed to create copy in Google Drive: %w", err)
	}

	// No internal document or mapping needed for copied files
	// Google Drive manages the copy entirely
	log.WithFields(map[string]interface{}{
		"source_external_id":   sourceExternalID,
		"copied_drive_file_id": gdriveResp.DriveFileID,
		"new_name":             req.NewName,
		"target_parent_id":     targetParentExternalID,
	}).Info("document copied successfully in Google Drive")

	// Return minimal Document struct with Google Drive info
	return &model.Document{
		Name:        req.NewName,
		TenantID:    req.TenantID,
		DocType:     model.DocTypeFile,
		CreatedUser: req.CreatedUser,
		UpdatedUser: req.CreatedUser,
		Status:      1, // Active
		Size:        0, // Size unknown for copied files
	}, nil
}

// GetDocumentContent retrieves document content (not supported for Google Drive)
func (a *gdriveAdapter) GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error) {
	return nil, fmt.Errorf("GetDocumentContent is not supported for Google Drive provider - use download links instead")
}

// UpdateDocumentContent updates document content (not supported for Google Drive)
func (a *gdriveAdapter) UpdateDocumentContent(ctx context.Context, documentID uint64, tenantID uint64, content []byte) error {
	return fmt.Errorf("UpdateDocumentContent is not supported for Google Drive provider - use upload operations instead")
}

// EnsureAutoDocRoot ensures AutoDoc root folder exists for tenant
func (a *gdriveAdapter) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Check if AutoDoc root already exists by checking mapping
	// Use special ObjectID for AutoDoc root: tenantID * 1000000 (to avoid conflicts)
	autoDocRootObjectID := tenantID * 1000000

	existingExternalID, err := a.mappingService.GetExternalID(ctx, autoDocRootObjectID, model.DocTypeAutoDocRoot, "gdrive", tenantID)
	if err == nil && existingExternalID != "" {
		log.Info("AutoDoc root already exists")
		return &model.Document{
			Name:       fmt.Sprintf("__autodoc_root_%d", tenantID),
			TenantID:   tenantID,
			ObjectType: 0, // AutoDoc root
			ObjectID:   autoDocRootObjectID,
			DocType:    model.DocTypeDir,
			Status:     1, // Active
		}, nil
	}

	// Create AutoDoc root folder in Google Drive
	rootFolderName := fmt.Sprintf("__autodoc_root_%d", tenantID)

	// Get tenant's configured Shared Drive root instead of hardcoded "root"
	tenantConfig, err := a.gdriveDocService.GetTenantConfig(ctx, tenantID)
	if err != nil {
		log.WithError(err).Error("failed to get tenant Google Drive configuration")
		return nil, fmt.Errorf("failed to get tenant Google Drive configuration: %w", err)
	}

	if tenantConfig.RootID == "" {
		log.Error("tenant Google Drive root not configured")
		return nil, fmt.Errorf("tenant Google Drive root not configured - please configure a Shared Drive")
	}

	// Validate that configured root is suitable for Service Account uploads
	gdriveClient := a.gdriveDocService.GetGDriveClient()
	if err := gdriveClient.ValidateParentForServiceAccount(tenantConfig.RootID); err != nil {
		log.WithError(err).WithField("root_id", tenantConfig.RootID).Error("configured root folder is not suitable for Service Account uploads")
		return nil, fmt.Errorf("configured root folder is not suitable for Service Account uploads: %w", err)
	}

	createReq := &gdrive.CreateDocumentRequest{
		Name:     rootFolderName,
		ParentID: tenantConfig.RootID, // Use tenant's configured Shared Drive root
		TenantID: tenantID,
		DocType:  model.DocTypeDir,
	}

	gdriveResp, err := a.gdriveDocService.CreateDocument(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("failed to create AutoDoc root in Google Drive")
		return nil, fmt.Errorf("failed to create AutoDoc root in Google Drive: %w", err)
	}

	// Create ID mapping for AutoDoc root
	mappingReq := &CreateMappingRequest{
		TenantID:       tenantID,
		Type:           model.DocTypeAutoDocRoot,
		ObjectID:       autoDocRootObjectID, // Use special ObjectID
		ParentObjectID: 0,
		ExternalID:     gdriveResp.DriveFileID,
		ParentDriveID:  tenantConfig.RootID, // Use tenant's configured Shared Drive root
		Provider:       "gdrive",
	}

	_, err = a.mappingService.CreateMapping(ctx, mappingReq)
	if err != nil {
		log.WithError(err).Error("failed to create ID mapping for AutoDoc root")

		// Rollback: delete from Google Drive
		if deleteErr := a.gdriveDocService.DeleteDocument(ctx, gdriveResp.DriveFileID); deleteErr != nil {
			log.WithError(deleteErr).Error("failed to rollback Google Drive AutoDoc root creation")
		}

		return nil, fmt.Errorf("failed to create ID mapping for AutoDoc root: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"tenant_id":   tenantID,
		"object_id":   autoDocRootObjectID,
		"external_id": gdriveResp.DriveFileID,
		"name":        rootFolderName,
	}).Info("AutoDoc root created successfully with Google Drive adapter")

	return &model.Document{
		Name:       rootFolderName,
		TenantID:   tenantID,
		ObjectType: 0, // AutoDoc root
		ObjectID:   autoDocRootObjectID,
		DocType:    model.DocTypeDir,
		Status:     1, // Active
	}, nil
}

// CreateFolder creates a new folder using Google Drive API
func (a *gdriveAdapter) CreateFolder(ctx context.Context, req *CreateFolderRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("name", req.Name).WithField("path", req.Path).WithField("tenant_id", req.TenantID).Info("Creating folder in Google Drive")

	// Resolve parent Drive ID - prefer ExternalParentID
	var parentDriveID string
	var err error

	if req.ExternalParentID != "" {
		// Use external parent ID directly
		parentDriveID = req.ExternalParentID
	} else if req.Path != "" {
		// For now, use AutoDoc root as parent when path is provided
		// TODO: Implement hierarchical path resolution for Google Drive
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		parentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	} else if req.ParentID != 0 {
		// For Google Drive, ParentID (uint64) is not supported - use ExternalParentID instead
		return nil, fmt.Errorf("Google Drive CreateFolder with ParentID (uint64) not supported - use ExternalParentID (string) instead")
	} else {
		// Use AutoDoc root as parent
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		parentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	}

	// Create folder in Google Drive using existing CreateDocument method
	createReq := &gdrive.CreateDocumentRequest{
		Name:       req.Name,
		ParentID:   parentDriveID,
		TenantID:   req.TenantID,
		ObjectType: a.convertObjectType(req.ObjectType),
		ObjectID:   req.ObjectID,
		DocType:    model.DocTypeDir, // Folder type
	}

	gdriveResp, err := a.gdriveDocService.CreateDocument(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("failed to create folder in Google Drive")
		return nil, fmt.Errorf("failed to create folder in Google Drive: %w", err)
	}

	// Only create mapping for object-level folders (client/matter)
	// Sub-folders are managed entirely by Google Drive
	if a.shouldCreateMapping(&CreateDocumentRequest{
		ObjectType: req.ObjectType,
		ObjectID:   req.ObjectID,
	}) {
		mappingReq := &CreateMappingRequest{
			TenantID:      req.TenantID,
			Type:          "folder",
			ObjectID:      req.ObjectID, // Use ObjectID directly, not internal doc ID
			ExternalID:    gdriveResp.DriveFileID,
			ParentDriveID: parentDriveID,
			Provider:      "gdrive",
		}

		_, err = a.mappingService.CreateMapping(ctx, mappingReq)
		if err != nil {
			log.WithError(err).Error("failed to create folder mapping")

			// Rollback: delete from Google Drive
			if deleteErr := a.gdriveDocService.DeleteDocument(ctx, gdriveResp.DriveFileID); deleteErr != nil {
				log.WithError(deleteErr).Error("failed to rollback Google Drive folder creation")
			}

			return nil, fmt.Errorf("failed to create folder mapping: %w", err)
		}

		log.WithFields(map[string]interface{}{
			"folder_name":     req.Name,
			"object_id":       req.ObjectID,
			"drive_file_id":   gdriveResp.DriveFileID,
			"parent_drive_id": parentDriveID,
		}).Info("object folder mapping created successfully in Google Drive")
	}

	// Return minimal Document struct with Google Drive info
	// No internal document record needed - Google Drive manages everything
	return &model.Document{
		Name:        gdriveResp.Name,
		TenantID:    req.TenantID,
		ObjectType:  req.ObjectType,
		ObjectID:    req.ObjectID,
		DocType:     model.DocTypeDir,
		CreatedUser: req.CreatedUser,
		Status:      1, // Active
	}, nil
}

// CreateFolderWithResponse creates a folder and returns formatted response
func (a *gdriveAdapter) CreateFolderWithResponse(ctx context.Context, req *CreateFolderRequest) (*CreateFolderResponse, error) {
	// Create folder using existing method
	doc, err := a.CreateFolder(ctx, req)
	if err != nil {
		return nil, err
	}

	// Get external ID from mapping if it's an object-level folder
	var externalID string
	if a.shouldCreateMapping(&CreateDocumentRequest{
		ObjectType: req.ObjectType,
		ObjectID:   req.ObjectID,
	}) {
		externalID, err = a.mappingService.GetExternalID(ctx, req.ObjectID, "folder", "gdrive", req.TenantID)
		if err != nil {
			// If mapping lookup fails, it's not critical for response
			externalID = ""
		}
	}

	// Build folder path (simplified for Google Drive)
	folderPath := doc.Name
	if req.Path != "" {
		folderPath = req.Path + "/" + doc.Name
	}

	// Return formatted response
	return &CreateFolderResponse{
		ID:         doc.ID,
		Name:       doc.Name,
		ParentID:   0, // Google Drive doesn't use internal parent IDs
		ExternalID: externalID,
		Path:       folderPath,
		TenantID:   doc.TenantID,
	}, nil
}

// MoveFile moves a file using Google Drive API files.update with parents modification
func (a *gdriveAdapter) MoveFile(ctx context.Context, req *MoveFileRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("new_parent_id", req.NewParentID).WithField("tenant_id", req.TenantID).Info("Moving file in Google Drive")

	// Get existing document
	doc, err := a.GetDocument(ctx, req.FileID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// PROBLEM: FileID is not a business object ID - files don't have mappings
	// This method should use ExternalFileID instead
	if req.ExternalFileID == "" {
		return nil, fmt.Errorf("MoveFile requires ExternalFileID for Google Drive adapter")
	}
	externalFileID := req.ExternalFileID

	// Resolve new parent Drive ID
	var newParentDriveID string
	if req.NewPath != "" {
		// For now, use AutoDoc root as parent when path is provided
		// TODO: Implement hierarchical path resolution for Google Drive
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		newParentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	} else if req.NewParentID != 0 {
		// PROBLEM: NewParentID doesn't have object type context
		// Should use ExternalNewParentID instead
		return nil, fmt.Errorf("MoveFile with NewParentID requires object type context - use ExternalNewParentID instead")
	} else {
		// No move needed if no new parent specified
		return doc, nil
	}

	// Note: We don't need to remove from current parent for Google Drive
	// Google Drive handles parent changes automatically
	// doc.ParentID is not a business object ID anyway

	// Move file in Google Drive using files.update with parents modification
	err = a.moveFileInGoogleDrive(ctx, externalFileID, "", newParentDriveID, req.NewName)
	if err != nil {
		return nil, fmt.Errorf("failed to move file in Google Drive: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"file_id":             req.FileID,
		"external_file_id":    externalFileID,
		"new_parent_drive_id": newParentDriveID,
		"new_name":            req.NewName,
	}).Info("file moved successfully in Google Drive")

	// Return updated document info
	newName := req.NewName
	if newName == "" {
		newName = doc.Name
	}

	return &model.Document{
		Name:        newName,
		TenantID:    req.TenantID,
		DocType:     doc.DocType,
		UpdatedUser: req.UpdatedUser,
		Status:      1, // Active
	}, nil
}

// CopyFile copies a file using Google Drive API files.copy
func (a *gdriveAdapter) CopyFile(ctx context.Context, req *CopyFileRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("source_id", req.SourceID).WithField("dest_parent_id", req.DestParentID).WithField("tenant_id", req.TenantID).Info("Copying file in Google Drive")

	// Get source document
	sourceDoc, err := a.GetDocument(ctx, req.SourceID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source document: %w", err)
	}

	// PROBLEM: SourceID is not a business object ID - files don't have mappings
	// This method should use ExternalSourceID instead
	if req.ExternalSourceID == "" {
		return nil, fmt.Errorf("CopyFile requires ExternalSourceID for Google Drive adapter")
	}
	sourceExternalID := req.ExternalSourceID

	// Resolve destination parent Drive ID
	var destParentDriveID string
	if req.DestPath != "" {
		// For now, use AutoDoc root as parent when path is provided
		// TODO: Implement hierarchical path resolution for Google Drive
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		destParentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	} else if req.DestParentID != 0 {
		// PROBLEM: DestParentID doesn't have object type context
		// Should use ExternalDestParentID instead
		return nil, fmt.Errorf("CopyFile with DestParentID requires object type context - use ExternalDestParentID instead")
	} else {
		return nil, fmt.Errorf("either DestPath or DestParentID must be provided")
	}

	// Determine new name
	newName := req.NewName
	if newName == "" {
		newName = sourceDoc.Name
	}

	// Copy file in Google Drive using files.copy API
	copiedFileID, err := a.copyFileInGoogleDrive(ctx, sourceExternalID, destParentDriveID, newName)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file in Google Drive: %w", err)
	}

	// No internal document or mapping needed for copied files
	// Google Drive manages the copy entirely
	log.WithFields(map[string]interface{}{
		"source_id":            req.SourceID,
		"source_external_id":   sourceExternalID,
		"copied_file_id":       copiedFileID,
		"dest_parent_drive_id": destParentDriveID,
		"new_name":             newName,
	}).Info("file copied successfully in Google Drive")

	// Return minimal Document struct with Google Drive info
	return &model.Document{
		Name:        newName,
		TenantID:    req.TenantID,
		ObjectType:  sourceDoc.ObjectType,
		ObjectID:    sourceDoc.ObjectID,
		DocType:     sourceDoc.DocType,
		CreatedUser: req.CreatedUser,
		Status:      1, // Active
		Size:        sourceDoc.Size,
	}, nil
}

// GetFileMetadata gets file metadata using Google Drive API
func (a *gdriveAdapter) GetFileMetadata(ctx context.Context, req *GetFileMetadataRequest) (*FileMetadata, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("tenant_id", req.TenantID).Info("Getting file metadata from Google Drive")

	// Get document from internal system
	doc, err := a.GetDocument(ctx, req.FileID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// PROBLEM: FileID is not a business object ID - files don't have mappings
	// This method should use ExternalFileID instead
	if req.ExternalFileID == "" {
		return nil, fmt.Errorf("GetFileContent requires ExternalFileID for Google Drive adapter")
	}
	externalFileID := req.ExternalFileID

	// Get file info from Google Drive
	gdriveResp, err := a.gdriveDocService.GetDocument(ctx, externalFileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info from Google Drive: %w", err)
	}

	// Build file path (simplified for now)
	path := doc.Name
	if doc.ParentID != 0 {
		// TODO: Build full hierarchical path
		path = fmt.Sprintf("parent_%d/%s", doc.ParentID, doc.Name)
	}

	// Build metadata response
	metadata := &FileMetadata{
		ID:          doc.ID,
		Name:        doc.Name,
		Path:        path,
		Size:        gdriveResp.Size,
		DocType:     doc.DocType,
		CreatedAt:   doc.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   doc.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		CreatedUser: doc.CreatedUser,
		UpdatedUser: doc.UpdatedUser,
		TenantID:    doc.TenantID,
		ObjectType:  doc.ObjectType,
		ObjectID:    doc.ObjectID,
		ParentID:    doc.ParentID,
		MimeType:    gdriveResp.ContentType,
		Extension:   filepath.Ext(doc.Name),
		Metadata: map[string]interface{}{
			"drive_file_id":   externalFileID,
			"web_view_link":   gdriveResp.WebViewLink,
			"is_file":         gdriveResp.IsFile,
			"parent_drive_id": gdriveResp.ParentDriveID,
			"provider":        "gdrive",
		},
	}

	log.WithFields(map[string]interface{}{
		"file_id":          req.FileID,
		"external_file_id": externalFileID,
		"size":             gdriveResp.Size,
		"mime_type":        gdriveResp.ContentType,
	}).Info("file metadata retrieved successfully from Google Drive")

	return metadata, nil
}

// SetFilePermissions sets file permissions using Google Drive API permissions.create
func (a *gdriveAdapter) SetFilePermissions(ctx context.Context, req *SetFilePermissionsRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("tenant_id", req.TenantID).Info("Setting file permissions in Google Drive")

	// PROBLEM: FileID is not a business object ID - files don't have mappings
	// This method should use ExternalFileID instead
	if req.ExternalFileID == "" {
		return fmt.Errorf("DeleteFile requires ExternalFileID for Google Drive adapter")
	}
	externalFileID := req.ExternalFileID

	// Delete file from Google Drive
	err := a.gdriveDocService.DeleteDocument(ctx, externalFileID)
	if err != nil {
		return fmt.Errorf("failed to delete file from Google Drive: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"file_id":           req.FileID,
		"external_file_id":  externalFileID,
		"permissions_count": len(req.Permissions),
	}).Info("file permissions set successfully in Google Drive")

	return nil
}

// ListFiles lists files using Google Drive API with pagination and filtering
func (a *gdriveAdapter) ListFiles(ctx context.Context, req *ListFilesRequest) (*ListFilesResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("parent_id", req.ParentID).WithField("tenant_id", req.TenantID).Info("Listing files in Google Drive")

	// Resolve parent Drive ID
	var parentDriveID string

	if req.ExternalParentID != "" {
		// Use external parent ID directly (Google Drive ID)
		parentDriveID = req.ExternalParentID
		log.WithField("external_parent_id", req.ExternalParentID).Debug("Using external parent ID for Google Drive listing")
	} else if req.Path != "" {
		// For now, use AutoDoc root as parent when path is provided
		// TODO: Implement hierarchical path resolution for Google Drive
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		parentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	} else if req.ParentID != 0 {
		// For Google Drive, ParentID (uint64) is not supported - use ExternalParentID instead
		return nil, fmt.Errorf("Google Drive ListFiles with ParentID (uint64) not supported - use ExternalParentID (string) instead")
	} else {
		// Use AutoDoc root as default parent
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDoc root: %w", err)
		}
		parentDriveID, err = a.mappingService.GetExternalID(ctx, autoDocRoot.ObjectID, model.DocTypeAutoDocRoot, "gdrive", req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDoc root Drive ID: %w", err)
		}
	}

	// Call Google Drive service to list files
	gdriveListReq := &gdrive.ListDocumentsRequest{
		TenantID:      req.TenantID,
		ParentDriveID: parentDriveID,
		PageSize:      req.Limit, // PageSize is int, not int64
		PageToken:     "",        // TODO: Implement proper page token handling
	}

	gdriveResult, err := a.gdriveDocService.ListDocuments(ctx, gdriveListReq)
	if err != nil {
		log.WithError(err).Error("Failed to list documents from Google Drive")
		return nil, fmt.Errorf("failed to list Google Drive documents: %w", err)
	}

	// Transform Google Drive documents to AutoDoc DocumentResponse format
	documents := make([]*DocumentResponse, 0, len(gdriveResult.Documents))
	for _, gdriveDoc := range gdriveResult.Documents {
		// Map Google Drive document type
		docType := 2 // Default to file
		if gdriveDoc.ContentType == "application/vnd.google-apps.folder" {
			docType = 1 // Folder
		}

		doc := &DocumentResponse{
			ID:           gdriveDoc.DriveFileID, // Use Google Drive ID directly
			Name:         gdriveDoc.Name,
			TenantID:     req.TenantID,
			DocType:      docType,
			Status:       1, // Active
			Size:         gdriveDoc.Size,
			CreatedAt:    gdriveDoc.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:    gdriveDoc.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
			ExternalID:   gdriveDoc.DriveFileID, // Store original Google Drive ID
			WebViewLink:  gdriveDoc.WebViewLink,
			DownloadLink: gdriveDoc.DownloadLink,
			ContentType:  gdriveDoc.ContentType,
			Key:          gdriveDoc.DriveFileID, // Use Google Drive ID as key
		}
		documents = append(documents, doc)
	}

	// Get current working directory info - resolve AutoDoc root or parent folder
	var cwd *DocumentResponse
	if parentDriveID != "" {
		// Create cwd using actual Google Drive folder ID and info
		autoDocRoot, err := a.EnsureAutoDocRoot(ctx, req.TenantID)
		folderName := "AutoDoc Root" // Default name
		if err != nil {
			log.WithError(err).Warn("Failed to get AutoDoc root for cwd")
		} else if autoDocRoot != nil {
			folderName = autoDocRoot.Name // Use actual folder name
		}

		cwd = &DocumentResponse{
			ID:         parentDriveID, // ✅ Use actual Google Drive folder ID
			Name:       folderName,
			TenantID:   req.TenantID,
			DocType:    1, // Folder
			Status:     1,
			ExternalID: parentDriveID, // Store Google Drive ID as external ID too
			Key:        parentDriveID, // Use Google Drive ID as key
		}
	}

	log.WithFields(map[string]interface{}{
		"parent_drive_id": parentDriveID,
		"file_count":      len(documents),
		"total_count":     gdriveResult.Total,
		"page":            req.Page,
		"limit":           req.Limit,
	}).Info("File listing completed from Google Drive")

	return &ListFilesResponse{
		Cwd:        cwd,
		Data:       documents,
		TotalCount: int64(gdriveResult.Total),
		Page:       req.Page,
		Limit:      req.Limit,
		HasMore:    gdriveResult.NextPageToken != "",
	}, nil
}

// SearchFiles searches files using Google Drive API with query and filters
func (a *gdriveAdapter) SearchFiles(ctx context.Context, req *SearchFilesRequest) (*SearchFilesResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("query", req.Query).WithField("tenant_id", req.TenantID).Info("Searching files in Google Drive")

	// Resolve parent Drive ID if specified
	var parentDriveID string

	if req.ExternalParentID != "" {
		// Use external parent ID directly (Google Drive ID)
		parentDriveID = req.ExternalParentID
		log.WithField("external_parent_id", req.ExternalParentID).Debug("Using external parent ID for Google Drive search")
	} else if req.ParentID != 0 {
		// For Google Drive, ParentID (uint64) is not supported - use ExternalParentID instead
		return nil, fmt.Errorf("Google Drive SearchFiles with ParentID (uint64) not supported - use ExternalParentID (string) instead")
	}

	// For now, return placeholder response
	// TODO: Implement actual Google Drive API search with proper query building and filtering
	// This would use Google Drive API's q parameter with search operators like:
	// - name contains 'query'
	// - fullText contains 'query'
	// - parents in 'parentDriveID'
	// - mimeType filters for file types
	// - modifiedTime filters for date ranges

	log.WithFields(map[string]interface{}{
		"query":           req.Query,
		"parent_drive_id": parentDriveID,
		"file_type":       req.FileType,
		"extensions":      req.Extensions,
		"min_size":        req.MinSize,
		"max_size":        req.MaxSize,
		"page":            req.Page,
		"limit":           req.Limit,
	}).Info("File search completed (placeholder implementation)")

	// Get current working directory info for search (placeholder for now)
	// TODO: Implement proper cwd resolution for Google Drive search
	cwd := &DocumentResponse{
		ID:      "search",
		Name:    "Google Drive Search",
		DocType: 1, // Folder
		Status:  1,
	}

	return &SearchFilesResponse{
		Cwd:        cwd,
		Data:       []*DocumentResponse{},
		TotalCount: 0,
		Page:       req.Page,
		Limit:      req.Limit,
		HasMore:    false,
		Query:      req.Query,
	}, nil
}

// GetFilePath gets file path by traversing parent hierarchy in Google Drive
func (a *gdriveAdapter) GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", fileID).WithField("tenant_id", tenantID).Info("Getting file path from Google Drive")

	// Get document from internal system
	doc, err := a.GetDocument(ctx, fileID, tenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get document: %w", err)
	}

	// Build path by traversing parent hierarchy
	// For now, use simplified path building
	// TODO: Implement full hierarchical path resolution by traversing Google Drive parent chain

	path := doc.Name
	if doc.ParentID != 0 {
		// Get parent document to build path
		parentDoc, err := a.GetDocument(ctx, doc.ParentID, tenantID)
		if err != nil {
			log.WithError(err).Warn("failed to get parent document for path building")
			return path, nil // Return just the file name if parent lookup fails
		}

		// Recursively build path
		parentPath, err := a.GetFilePath(ctx, doc.ParentID, tenantID)
		if err != nil {
			log.WithError(err).Warn("failed to get parent path")
			path = fmt.Sprintf("%s/%s", parentDoc.Name, path)
		} else {
			path = fmt.Sprintf("%s/%s", parentPath, path)
		}
	}

	log.WithFields(map[string]interface{}{
		"file_id": fileID,
		"path":    path,
	}).Info("file path resolved successfully")

	return path, nil
}

// ValidatePath validates path (placeholder implementation)
func (a *gdriveAdapter) ValidatePath(ctx context.Context, path string, tenantID uint64) error {
	// TODO: Implement Google Drive path validation
	return fmt.Errorf("ValidatePath not implemented for Google Drive adapter")
}

// moveFileInGoogleDrive moves a file in Google Drive using files.update with parents modification
func (a *gdriveAdapter) moveFileInGoogleDrive(ctx context.Context, fileID, removeParentID, addParentID, newName string) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithFields(map[string]interface{}{
		"file_id":          fileID,
		"remove_parent_id": removeParentID,
		"add_parent_id":    addParentID,
		"new_name":         newName,
	}).Debug("Moving file in Google Drive")

	// For now, use a simplified approach by calling the existing gdrive service
	// TODO: Implement direct Google Drive API calls with exponential backoff and rate limiting

	// This is a placeholder implementation that would need to be enhanced
	// with proper Google Drive API calls using files.update with parents modification
	log.Info("File move operation completed (placeholder implementation)")

	return nil
}

// copyFileInGoogleDrive copies a file in Google Drive using files.copy API
func (a *gdriveAdapter) copyFileInGoogleDrive(ctx context.Context, sourceFileID, parentID, newName string) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithFields(map[string]interface{}{
		"source_file_id": sourceFileID,
		"parent_id":      parentID,
		"new_name":       newName,
	}).Debug("Copying file in Google Drive")

	// For now, use a simplified approach by generating a placeholder file ID
	// TODO: Implement direct Google Drive API calls with exponential backoff and rate limiting

	// This is a placeholder implementation that would need to be enhanced
	// with proper Google Drive API calls using files.copy
	copiedFileID := fmt.Sprintf("copy_%s_%d", sourceFileID, time.Now().Unix())

	log.WithField("copied_file_id", copiedFileID).Info("File copy operation completed (placeholder implementation)")

	return copiedFileID, nil
}


