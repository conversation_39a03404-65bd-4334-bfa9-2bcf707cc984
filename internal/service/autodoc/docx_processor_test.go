package autodoc

import (
	"context"
	"io"
	"testing"

	"bilabl/docman/pkg/transport"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockGlobClient is a mock implementation of transport.Glob
type MockGlobClient struct {
	mock.Mock
}

func (m *MockGlobClient) EmitDownload(ctx context.Context, key string, userID string) (*transport.DownloadResp, error) {
	args := m.Called(ctx, key, userID)
	return args.Get(0).(*transport.DownloadResp), args.Error(1)
}

func (m *MockGlobClient) GetObjectSize(ctx context.Context, key string, userID string) (transport.GlobSizeResponse, error) {
	args := m.Called(ctx, key, userID)
	return args.Get(0).(transport.GlobSizeResponse), args.Error(1)
}

func (m *MockGlobClient) UploadFile(ctx context.Context, filename string, fileContent io.Reader, userID string, tenantID string, userRoles string) (*transport.UploadResult, error) {
	args := m.Called(ctx, filename, fileContent, userID, tenantID, userRoles)
	return args.Get(0).(*transport.UploadResult), args.Error(1)
}

func (m *MockGlobClient) GetFileContent(ctx context.Context, key string, userID string) ([]byte, error) {
	args := m.Called(ctx, key, userID)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockGlobClient) GetPresignedUploadURL(ctx context.Context, filename string, userID string, tenantID string, userRoles string) (*transport.PresignUploadResp, error) {
	args := m.Called(ctx, filename, userID, tenantID, userRoles)
	return args.Get(0).(*transport.PresignUploadResp), args.Error(1)
}

func TestDocxProcessor_ProcessTemplate(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	ctx := context.Background()

	t.Run("should handle empty template content", func(t *testing.T) {
		placeholders := map[string]interface{}{
			"client.name": "John Doe",
			"matter.id":   "12345",
		}

		// Empty template content should return error
		result, err := processor.ProcessTemplate(ctx, []byte{}, placeholders)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "template content is empty")
	})

	t.Run("should handle invalid DOCX content", func(t *testing.T) {
		placeholders := map[string]interface{}{
			"client.name": "John Doe",
		}

		// Invalid DOCX content should return error
		invalidContent := []byte("not a valid docx file")
		result, err := processor.ProcessTemplate(ctx, invalidContent, placeholders)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "not a valid DOCX file")
	})
}

func TestDocxProcessor_ValidateTemplate(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	ctx := context.Background()

	t.Run("should reject invalid template", func(t *testing.T) {
		invalidContent := []byte("not a valid docx file")
		err := processor.ValidateTemplate(ctx, invalidContent)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not a valid DOCX file")
	})

	t.Run("should reject empty template", func(t *testing.T) {
		err := processor.ValidateTemplate(ctx, []byte{})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "template content is empty")
	})
}

func TestDocxProcessor_ExtractPlaceholders(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	ctx := context.Background()

	t.Run("should return common placeholders for invalid template", func(t *testing.T) {
		// Even with invalid template, should return error
		invalidContent := []byte("not a valid docx file")
		placeholders, err := processor.ExtractPlaceholders(ctx, invalidContent)
		assert.Error(t, err)
		assert.Nil(t, placeholders)
		assert.Contains(t, err.Error(), "failed to parse DOCX template")
	})

	t.Run("should handle empty template", func(t *testing.T) {
		placeholders, err := processor.ExtractPlaceholders(ctx, []byte{})
		assert.Error(t, err)
		assert.Nil(t, placeholders)
	})
}

func TestDocxProcessor_flattenPlaceholders(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	tests := []struct {
		name     string
		prefix   string
		input    map[string]interface{}
		expected map[string]interface{}
	}{
		{
			name:   "simple flat map",
			prefix: "",
			input: map[string]interface{}{
				"name":  "John Doe",
				"email": "<EMAIL>",
			},
			expected: map[string]interface{}{
				"name":  "John Doe",
				"email": "<EMAIL>",
			},
		},
		{
			name:   "nested client object",
			prefix: "",
			input: map[string]interface{}{
				"client": map[string]interface{}{
					"name":       "ABC Company",
					"short_name": "ABC",
					"email":      "<EMAIL>",
					"phone":      "+1234567890",
				},
			},
			expected: map[string]interface{}{
				"client.name":       "ABC Company",
				"client.short_name": "ABC",
				"client.email":      "<EMAIL>",
				"client.phone":      "+1234567890",
			},
		},
		{
			name:   "nested matter object",
			prefix: "",
			input: map[string]interface{}{
				"matter": map[string]interface{}{
					"id":    12345,
					"title": "Contract Review",
					"type":  "Legal",
				},
			},
			expected: map[string]interface{}{
				"matter.id":    12345,
				"matter.title": "Contract Review",
				"matter.type":  "Legal",
			},
		},
		{
			name:   "mixed flat and nested",
			prefix: "",
			input: map[string]interface{}{
				"date": "2024-01-15",
				"client": map[string]interface{}{
					"name":  "XYZ Corp",
					"email": "<EMAIL>",
				},
				"matter": map[string]interface{}{
					"id":    67890,
					"title": "Merger Agreement",
				},
			},
			expected: map[string]interface{}{
				"date":         "2024-01-15",
				"client.name":  "XYZ Corp",
				"client.email": "<EMAIL>",
				"matter.id":    67890,
				"matter.title": "Merger Agreement",
			},
		},
		{
			name:   "deeply nested object",
			prefix: "",
			input: map[string]interface{}{
				"client": map[string]interface{}{
					"contact": map[string]interface{}{
						"primary": map[string]interface{}{
							"name":  "John Primary",
							"email": "<EMAIL>",
						},
					},
				},
			},
			expected: map[string]interface{}{
				"client.contact.primary.name":  "John Primary",
				"client.contact.primary.email": "<EMAIL>",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.flattenPlaceholders(tt.prefix, tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDocxProcessor_convertToString(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	tests := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{"nil value", nil, ""},
		{"string value", "hello", "hello"},
		{"int value", 42, "42"},
		{"uint value", uint(42), "42"},
		{"float value", 3.14, "3.14"},
		{"bool true", true, "Yes"},
		{"bool false", false, "No"},
		{"other type", []string{"a", "b"}, "[a b]"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.convertToString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewDocxProcessor(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)

	require.NotNil(t, processor)
	require.NotNil(t, processor.globClient)
}

func TestDocxProcessor_DownloadTemplate(t *testing.T) {
	ctx := context.Background()
	templateKey := "test-template-key"
	userID := "test-user"
	expectedContent := []byte("mock template content")

	t.Run("should download template successfully", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return(expectedContent, nil)

		content, err := processor.DownloadTemplate(ctx, templateKey, userID)

		assert.NoError(t, err)
		assert.Equal(t, expectedContent, content)
		mockGlob.AssertExpectations(t)
	})

	t.Run("should handle download error", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return([]byte(nil), assert.AnError)

		content, err := processor.DownloadTemplate(ctx, templateKey, userID)

		assert.Error(t, err)
		assert.Nil(t, content)
		assert.Contains(t, err.Error(), "failed to download template")
		mockGlob.AssertExpectations(t)
	})
}

func TestDocxProcessor_UploadProcessedDocument(t *testing.T) {
	ctx := context.Background()
	filename := "test-document.docx"
	content := []byte("processed document content")
	userID := "test-user"
	tenantID := "test-tenant"
	userRoles := "admin"

	t.Run("should upload document successfully", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		expectedResult := &transport.UploadResult{
			Key: "uploaded-key",
		}

		mockGlob.On("UploadFile", ctx, filename, mock.AnythingOfType("*bytes.Reader"), userID, tenantID, userRoles).Return(expectedResult, nil)

		result, err := processor.UploadProcessedDocument(ctx, filename, content, userID, tenantID, userRoles)

		assert.NoError(t, err)
		assert.Equal(t, expectedResult, result)
		mockGlob.AssertExpectations(t)
	})

	t.Run("should handle upload error", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("UploadFile", ctx, filename, mock.AnythingOfType("*bytes.Reader"), userID, tenantID, userRoles).Return((*transport.UploadResult)(nil), assert.AnError)

		result, err := processor.UploadProcessedDocument(ctx, filename, content, userID, tenantID, userRoles)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to upload document")
		mockGlob.AssertExpectations(t)
	})
}

func TestDocxProcessor_ValidateTemplateFromKey(t *testing.T) {
	ctx := context.Background()
	templateKey := "test-template-key"
	userID := "test-user"

	t.Run("should handle download error", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return([]byte(nil), assert.AnError)

		err := processor.ValidateTemplateFromKey(ctx, templateKey, userID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to download template for validation")
		mockGlob.AssertExpectations(t)
	})

	t.Run("should handle invalid template", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		invalidContent := []byte("invalid docx content")
		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return(invalidContent, nil)

		err := processor.ValidateTemplateFromKey(ctx, templateKey, userID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "template validation failed")
		mockGlob.AssertExpectations(t)
	})
}

func TestDocxProcessor_ExtractPlaceholdersFromKey(t *testing.T) {
	ctx := context.Background()
	templateKey := "test-template-key"
	userID := "test-user"

	t.Run("should handle download error", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return([]byte(nil), assert.AnError)

		placeholders, err := processor.ExtractPlaceholdersFromKey(ctx, templateKey, userID)

		assert.Error(t, err)
		assert.Nil(t, placeholders)
		assert.Contains(t, err.Error(), "failed to download template for placeholder extraction")
		mockGlob.AssertExpectations(t)
	})

	t.Run("should handle invalid template", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		invalidContent := []byte("invalid docx content")
		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return(invalidContent, nil)

		placeholders, err := processor.ExtractPlaceholdersFromKey(ctx, templateKey, userID)

		assert.Error(t, err)
		assert.Nil(t, placeholders)
		assert.Contains(t, err.Error(), "failed to extract placeholders")
		mockGlob.AssertExpectations(t)
	})
}

func TestDocxProcessor_ProcessTemplateFromKey(t *testing.T) {
	ctx := context.Background()
	templateKey := "test-template-key"
	userID := "test-user"
	tenantID := "test-tenant"
	userRoles := "admin"
	placeholders := map[string]interface{}{
		"client.name": "John Doe",
		"matter.id":   "12345",
	}

	t.Run("should handle download error", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return([]byte(nil), assert.AnError)

		result, err := processor.ProcessTemplateFromKey(ctx, templateKey, placeholders, userID, tenantID, userRoles)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to download template")
		mockGlob.AssertExpectations(t)
	})

	t.Run("should handle invalid template content", func(t *testing.T) {
		mockGlob := &MockGlobClient{}
		processor := NewDocxProcessor(mockGlob)

		invalidContent := []byte("invalid docx content")
		mockGlob.On("GetFileContent", ctx, templateKey, userID).Return(invalidContent, nil)

		result, err := processor.ProcessTemplateFromKey(ctx, templateKey, placeholders, userID, tenantID, userRoles)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to process template")
		mockGlob.AssertExpectations(t)
	})
}

// Note: Full integration tests for loadDocumentContent and saveProcessedDocument
// would require complex mocking of document service and repositories.
// These methods are tested indirectly through the GenerateFromTemplate method
// and through integration tests with real services.

// TestImprovedDOCXErrorAnalysis tests the improved DOCX error analysis
func TestImprovedDOCXErrorAnalysis(t *testing.T) {
	mockGlob := &MockGlobClient{}
	processor := NewDocxProcessor(mockGlob)
	ctx := context.Background()

	t.Run("should provide detailed analysis for invalid DOCX", func(t *testing.T) {
		// Test with content that looks like text but not zip
		invalidContent := []byte("This is not a DOCX file, just plain text")

		// This should trigger detailed analysis
		_, err := processor.ProcessTemplate(ctx, invalidContent, map[string]interface{}{})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not a valid DOCX file")

		// The detailed analysis should be logged (visible in debug logs)
		t.Log("✅ Detailed DOCX analysis should show:")
		t.Log("  - size: content length")
		t.Log("  - is_zip: false")
		t.Log("  - error: 'not a zip file'")
		t.Log("  - content_header: hex dump of first bytes")
	})

	t.Run("should analyze zip structure for corrupted DOCX", func(t *testing.T) {
		// Create a valid zip header but invalid DOCX structure
		zipHeader := []byte("PK\x03\x04")
		corruptedZip := append(zipHeader, []byte("corrupted zip content")...)

		_, err := processor.ProcessTemplate(ctx, corruptedZip, map[string]interface{}{})

		assert.Error(t, err)

		t.Log("✅ For corrupted zip files, analysis should show:")
		t.Log("  - size: content length")
		t.Log("  - is_zip: true (has PK header)")
		t.Log("  - error: 'failed to read zip: ...'")
		t.Log("  - zip_files: [] (empty due to corruption)")
		t.Log("  - has_document_xml: false")
	})

	t.Run("documentation for real DOCX analysis", func(t *testing.T) {
		t.Log("=== DOCX Error Analysis Documentation ===")
		t.Log("")
		t.Log("When 'invalid docx archive, word/document.xml is missing' occurs:")
		t.Log("")
		t.Log("1. Check template_size - should be > 0")
		t.Log("2. Check content_header - should start with 504b0304 (PK header)")
		t.Log("3. Check docx_analysis.is_zip - should be true")
		t.Log("4. Check docx_analysis.zip_files - should contain DOCX structure:")
		t.Log("   - [Content_Types].xml")
		t.Log("   - _rels/.rels")
		t.Log("   - word/document.xml ← THIS IS CRITICAL")
		t.Log("   - word/_rels/document.xml.rels")
		t.Log("   - docProps/app.xml")
		t.Log("   - docProps/core.xml")
		t.Log("5. Check docx_analysis.has_document_xml - should be true")
		t.Log("")
		t.Log("Common issues:")
		t.Log("- File is not actually DOCX (renamed .doc or other format)")
		t.Log("- DOCX file is corrupted during download/upload")
		t.Log("- Template content is empty or truncated")
		t.Log("- Zip structure is damaged")
	})
}
