package autodoc

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"

	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/transport"

	"github.com/lukasjarosch/go-docx"
)

// DocxProcessor handles DOCX template processing with placeholder replacement
type DocxProcessor struct {
	globClient transport.Glob
}

// NewDocxProcessor creates a new DocxProcessor instance
func NewDocxProcessor(globClient transport.Glob) *DocxProcessor {
	return &DocxProcessor{
		globClient: globClient,
	}
}

// ProcessTemplateFromKey downloads template from glob storage, processes it, and uploads the result
func (p *DocxProcessor) ProcessTemplateFromKey(ctx context.Context, templateKey string, placeholders map[string]interface{}, userID, tenantID, userRoles string) (*transport.UploadResult, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Download template content from glob storage
	templateContent, err := p.globClient.GetFileContent(ctx, templateKey, userID)
	if err != nil {
		log.WithError(err).Error("Failed to download template from glob storage")
		return nil, fmt.Errorf("failed to download template: %w", err)
	}

	// Process the template
	processedContent, err := p.ProcessTemplate(ctx, templateContent, placeholders)
	if err != nil {
		return nil, fmt.Errorf("failed to process template: %w", err)
	}

	// Generate filename for processed document
	filename := fmt.Sprintf("processed_document_%d.docx", ctx.Value("request_id"))
	if filename == "processed_document_<nil>.docx" {
		filename = "processed_document.docx"
	}

	// Upload processed content to glob storage
	uploadResult, err := p.UploadProcessedDocument(ctx, filename, processedContent, userID, tenantID, userRoles)
	if err != nil {
		return nil, fmt.Errorf("failed to upload processed document: %w", err)
	}

	log.Info("Successfully processed template from key and uploaded result", map[string]interface{}{
		"template_key":   templateKey,
		"processed_size": len(processedContent),
		"upload_key":     uploadResult.Key,
	})

	return uploadResult, nil
}

// UploadProcessedDocument uploads processed document content to glob storage
func (p *DocxProcessor) UploadProcessedDocument(ctx context.Context, filename string, content []byte, userID, tenantID, userRoles string) (*transport.UploadResult, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Create reader from content
	contentReader := bytes.NewReader(content)

	// Upload to glob storage
	uploadResult, err := p.globClient.UploadFile(ctx, filename, contentReader, userID, tenantID, userRoles)
	if err != nil {
		log.WithError(err).Error("Failed to upload processed document")
		return nil, fmt.Errorf("failed to upload document: %w", err)
	}

	log.Info("Successfully uploaded processed document", map[string]interface{}{
		"filename":   filename,
		"size":       len(content),
		"upload_key": uploadResult.Key,
	})

	return uploadResult, nil
}

// DownloadTemplate downloads template content from glob storage by key
func (p *DocxProcessor) DownloadTemplate(ctx context.Context, templateKey, userID string) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)

	content, err := p.globClient.GetFileContent(ctx, templateKey, userID)
	if err != nil {
		log.WithError(err).Error("Failed to download template")
		return nil, fmt.Errorf("failed to download template: %w", err)
	}

	log.Info("Successfully downloaded template", map[string]interface{}{
		"template_key": templateKey,
		"size":         len(content),
	})

	return content, nil
}

// ValidateTemplateFromKey downloads and validates a DOCX template from glob storage
func (p *DocxProcessor) ValidateTemplateFromKey(ctx context.Context, templateKey, userID string) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Download template content
	templateContent, err := p.DownloadTemplate(ctx, templateKey, userID)
	if err != nil {
		return fmt.Errorf("failed to download template for validation: %w", err)
	}

	// Validate the template
	if err := p.ValidateTemplate(ctx, templateContent); err != nil {
		return fmt.Errorf("template validation failed: %w", err)
	}

	log.Info("Template validation successful", map[string]interface{}{
		"template_key": templateKey,
	})

	return nil
}

// ExtractPlaceholdersFromKey downloads template and extracts placeholders from glob storage
func (p *DocxProcessor) ExtractPlaceholdersFromKey(ctx context.Context, templateKey, userID string) ([]string, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Download template content
	templateContent, err := p.DownloadTemplate(ctx, templateKey, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to download template for placeholder extraction: %w", err)
	}

	// Extract placeholders
	placeholders, err := p.ExtractPlaceholders(ctx, templateContent)
	if err != nil {
		return nil, fmt.Errorf("failed to extract placeholders: %w", err)
	}

	log.Info("Successfully extracted placeholders from template", map[string]interface{}{
		"template_key":       templateKey,
		"placeholders_count": len(placeholders),
	})

	return placeholders, nil
}

// ProcessTemplate processes a DOCX template with placeholder replacement
// Supports {variable_name} syntax for placeholders
func (p *DocxProcessor) ProcessTemplate(ctx context.Context, templateContent []byte, placeholders map[string]interface{}) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Add detailed logging for debugging DOCX issues
	log.WithField("template_size", len(templateContent)).
		WithField("placeholders_count", len(placeholders)).
		Debug("Starting DOCX template processing")

	// Validate template content before processing
	if len(templateContent) == 0 {
		log.Error("Template content is empty")
		return nil, fmt.Errorf("template content is empty")
	}

	// Check if content looks like a zip file (DOCX is a zip archive)
	if len(templateContent) < 4 || string(templateContent[:4]) != "PK\x03\x04" {
		log.WithField("content_header", fmt.Sprintf("%x", templateContent[:min(len(templateContent), 10)])).
			Error("Template content does not appear to be a valid zip/DOCX file")
		return nil, fmt.Errorf("template content is not a valid DOCX file (missing zip header)")
	}

	// Open DOCX document from bytes
	doc, err := docx.OpenBytes(templateContent)
	if err != nil {
		// Perform detailed analysis for debugging
		analysis := p.analyzeDOCXStructure(ctx, templateContent)

		log.WithError(err).
			WithField("template_size", len(templateContent)).
			WithField("content_preview", fmt.Sprintf("%x", templateContent[:min(len(templateContent), 20)])).
			WithField("docx_analysis", analysis).
			Error("Failed to parse DOCX template - detailed analysis")
		return nil, fmt.Errorf("failed to parse DOCX template: %w", err)
	}
	defer doc.Close()

	// Convert placeholders to PlaceholderMap with nested key support
	placeholderMap := make(docx.PlaceholderMap)
	flattenedPlaceholders := p.flattenPlaceholders("", placeholders)
	for key, value := range flattenedPlaceholders {
		placeholderMap[key] = p.convertToString(value)
	}

	// Replace placeholders in the document
	if err := doc.ReplaceAll(placeholderMap); err != nil {
		log.WithError(err).Error("Failed to replace placeholders")
		return nil, fmt.Errorf("failed to replace placeholders: %w", err)
	}

	// Write the processed document to a buffer
	var buf bytes.Buffer
	if err := doc.Write(&buf); err != nil {
		log.WithError(err).Error("Failed to write processed DOCX")
		return nil, fmt.Errorf("failed to write processed DOCX: %w", err)
	}

	log.Info("Successfully processed DOCX template", map[string]interface{}{
		"placeholders_count": len(placeholders),
		"output_size":        buf.Len(),
	})

	return buf.Bytes(), nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// analyzeDOCXStructure analyzes DOCX zip structure for debugging
func (p *DocxProcessor) analyzeDOCXStructure(ctx context.Context, templateContent []byte) map[string]interface{} {
	log := bilabllog.CreateContextLogger(ctx)

	analysis := map[string]interface{}{
		"size":             len(templateContent),
		"is_zip":           false,
		"zip_files":        []string{},
		"has_document_xml": false,
		"error":            nil,
	}

	if len(templateContent) < 4 {
		analysis["error"] = "content too small"
		return analysis
	}

	// Check zip signature
	if string(templateContent[:4]) == "PK\x03\x04" {
		analysis["is_zip"] = true

		// Try to read zip structure
		reader := bytes.NewReader(templateContent)
		zipReader, err := zip.NewReader(reader, int64(len(templateContent)))
		if err != nil {
			analysis["error"] = fmt.Sprintf("failed to read zip: %v", err)
			return analysis
		}

		// List all files in zip
		files := make([]string, 0, len(zipReader.File))
		for _, file := range zipReader.File {
			files = append(files, file.Name)
			if file.Name == "word/document.xml" {
				analysis["has_document_xml"] = true
			}
		}
		analysis["zip_files"] = files
	} else {
		analysis["error"] = "not a zip file"
	}

	log.WithField("docx_analysis", analysis).Debug("DOCX structure analysis completed")
	return analysis
}

// flattenPlaceholders recursively flattens nested maps/structs into dot-notation keys
// e.g., {"client": {"name": "John", "email": "<EMAIL>"}} becomes {"client.name": "John", "client.email": "<EMAIL>"}
func (p *DocxProcessor) flattenPlaceholders(prefix string, data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	for key, value := range data {
		var newKey string
		if prefix == "" {
			newKey = key
		} else {
			newKey = prefix + "." + key
		}

		// Handle nested maps
		if nestedMap, ok := value.(map[string]interface{}); ok {
			// Recursively flatten nested maps
			nestedResult := p.flattenPlaceholders(newKey, nestedMap)
			for nestedKey, nestedValue := range nestedResult {
				result[nestedKey] = nestedValue
			}
		} else {
			// Add the value directly
			result[newKey] = value
		}
	}

	return result
}

// convertToString converts various types to string representation
func (p *DocxProcessor) convertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "Yes"
		}
		return "No"
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ValidateTemplate validates that a DOCX template is properly formatted
func (p *DocxProcessor) ValidateTemplate(ctx context.Context, templateContent []byte) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Basic content validation
	if len(templateContent) == 0 {
		log.Error("Template content is empty")
		return fmt.Errorf("template content is empty")
	}

	// Check zip file signature
	if len(templateContent) < 4 || string(templateContent[:4]) != "PK\x03\x04" {
		log.WithField("content_size", len(templateContent)).
			WithField("content_header", fmt.Sprintf("%x", templateContent[:min(len(templateContent), 10)])).
			Error("Template is not a valid zip/DOCX file")
		return fmt.Errorf("template is not a valid DOCX file (invalid zip signature)")
	}

	// Try to open with go-docx library
	doc, err := docx.OpenBytes(templateContent)
	if err != nil {
		log.WithError(err).
			WithField("template_size", len(templateContent)).
			Error("Failed to validate DOCX template structure")
		return fmt.Errorf("invalid DOCX template structure: %w", err)
	}
	defer doc.Close()

	log.WithField("template_size", len(templateContent)).
		Info("DOCX template validation successful")
	return nil
}

// ExtractPlaceholders extracts all placeholders from a DOCX template
func (p *DocxProcessor) ExtractPlaceholders(ctx context.Context, templateContent []byte) ([]string, error) {
	log := bilabllog.CreateContextLogger(ctx)

	doc, err := docx.OpenBytes(templateContent)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DOCX template: %w", err)
	}
	defer doc.Close()

	// This is a simplified implementation
	// In a full implementation, you'd iterate through all document elements
	placeholders := make(map[string]bool)

	// For now, return common placeholders that might be used
	// In a real implementation, you'd extract from actual document content
	commonPlaceholders := []string{
		"client.name", "client.email", "client.phone",
		"matter.id", "matter.title", "matter.type",
		"user.name", "user.email",
		"date", "time", "datetime",
	}

	for _, placeholder := range commonPlaceholders {
		placeholders[placeholder] = true
	}

	// Convert map to slice
	result := make([]string, 0, len(placeholders))
	for placeholder := range placeholders {
		result = append(result, placeholder)
	}

	log.Info("Extracted placeholders from DOCX template", map[string]interface{}{
		"count":        len(result),
		"placeholders": result,
	})

	return result, nil
}
