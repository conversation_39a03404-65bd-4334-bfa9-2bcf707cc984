# Nested Placeholders Support in DOCX Template Processing

## Overview

The `ProcessTemplate` function now supports nested placeholders using dot notation. This allows you to access nested object properties in your DOCX templates.

## How It Works

### Before (Level 1 only)
```go
placeholders := map[string]interface{}{
    "client_name": "ABC Company",
    "matter_id":   12345,
}
// Template: {client_name}, {matter_id}
```

### After (Nested Support)
```go
placeholders := map[string]interface{}{
    "client": map[string]interface{}{
        "name":       "ABC Company",
        "short_name": "ABC",
        "email":      "<EMAIL>",
        "phone":      "+1234567890",
    },
    "matter": map[string]interface{}{
        "id":    12345,
        "title": "Contract Review",
        "type":  "Legal",
    },
    "date": "2024-01-15",
}
// Template: {client.name}, {client.short_name}, {matter.title}, {date}
```

## Flattening Process

The `flattenPlaceholders` function converts nested objects into dot-notation keys:

**Input:**
```go
{
    "client": {
        "name": "ABC Company",
        "contact": {
            "email": "<EMAIL>"
        }
    },
    "date": "2024-01-15"
}
```

**Output:**
```go
{
    "client.name": "ABC Company",
    "client.contact.email": "<EMAIL>",
    "date": "2024-01-15"
}
```

## Template Usage Examples

### Basic Client Information
```
Dear {client.name},

Your matter "{matter.title}" (ID: {matter.id}) has been created.

Contact: {client.email}
Phone: {client.phone}
```

### Complex Nested Data
```
Client: {client.name} ({client.short_name})
Matter: {matter.title} - {matter.type}
Primary Contact: {client.contact.primary.name}
Email: {client.contact.primary.email}
```

### Mixed Simple and Nested
```
Date: {date}
Client: {client.name}
Matter ID: {matter.id}
Status: {status}
```

## Supported Data Types

The system handles various data types in nested structures:

- **Strings**: `{client.name}` → "ABC Company"
- **Numbers**: `{matter.id}` → "12345"
- **Booleans**: `{client.active}` → "Yes" or "No"
- **Nested Objects**: Automatically flattened with dot notation

## Implementation Details

### Key Functions

1. **`flattenPlaceholders(prefix string, data map[string]interface{}) map[string]interface{}`**
   - Recursively flattens nested maps
   - Creates dot-notation keys
   - Handles deep nesting

2. **`convertToString(value interface{}) string`**
   - Converts various types to string representation
   - Handles nil values gracefully
   - Formats numbers and booleans appropriately

### Usage in ProcessTemplate

```go
// Convert placeholders to PlaceholderMap with nested key support
placeholderMap := make(docx.PlaceholderMap)
flattenedPlaceholders := p.flattenPlaceholders("", placeholders)
for key, value := range flattenedPlaceholders {
    placeholderMap[key] = p.convertToString(value)
}
```

## Benefits

1. **Cleaner Data Structure**: Organize related data in nested objects
2. **Template Clarity**: Use descriptive dot-notation in templates
3. **Backward Compatibility**: Existing flat placeholders still work
4. **Flexible Nesting**: Support for arbitrary nesting levels
5. **Type Safety**: Proper handling of different data types

## Migration Guide

### From Flat Structure
```go
// Old way
placeholders := map[string]interface{}{
    "client_name":       "ABC Company",
    "client_short_name": "ABC",
    "client_email":      "<EMAIL>",
    "matter_id":         12345,
    "matter_title":      "Contract Review",
}
```

### To Nested Structure
```go
// New way
placeholders := map[string]interface{}{
    "client": map[string]interface{}{
        "name":       "ABC Company",
        "short_name": "ABC",
        "email":      "<EMAIL>",
    },
    "matter": map[string]interface{}{
        "id":    12345,
        "title": "Contract Review",
    },
}
```

### Template Updates
```
// Old template
{client_name}, {client_short_name}
{matter_id}: {matter_title}

// New template  
{client.name}, {client.short_name}
{matter.id}: {matter.title}
```

## Testing

The implementation includes comprehensive tests covering:
- Simple flat maps
- Nested client objects
- Nested matter objects
- Mixed flat and nested data
- Deeply nested objects
- Type conversion for various data types

Run tests with:
```bash
go test -v ./internal/service/autodoc -run "TestDocxProcessor_flattenPlaceholders"
```
