package autodoc

import (
	"context"
	"fmt"

	"bilabl/docman/pkg/bilabllog"
)

// contextKey is a custom type for context keys to avoid collisions
type contextKey string

const (
	tenantIDKey contextKey = "tenant_id"
)

// EventRuleMatchingService orchestrates event processing and rule matching
type EventRuleMatchingService interface {
	// ProcessEvent processes any event type and routes to appropriate consumers
	ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error

	// ProcessMatterEvent specifically handles matter events
	ProcessMatterEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error

	// ProcessClientEvent specifically handles client events
	ProcessClientEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error
}

// eventRuleMatchingService implements EventRuleMatchingService
type eventRuleMatchingService struct {
	matterEventConsumer MatterEventConsumer
	clientEventConsumer ClientEventConsumer
	ruleMatchingService RuleMatchingService
}

// NewEventRuleMatchingService creates a new event-rule matching service
func NewEventRuleMatchingService(
	matterEventConsumer MatterEventConsumer,
	clientEventConsumer ClientEventConsumer,
	ruleMatchingService RuleMatchingService,
) EventRuleMatchingService {
	return &eventRuleMatchingService{
		matterEventConsumer: matterEventConsumer,
		clientEventConsumer: clientEventConsumer,
		ruleMatchingService: ruleMatchingService,
	}
}

// ProcessEvent processes any event type and routes to appropriate consumers
func (e *eventRuleMatchingService) ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Info("Processing event")

	// we use tenant_id in ctx in some rule matching
	ctx = context.WithValue(ctx, tenantIDKey, tenantID)

	// Route to appropriate event processor based on event type
	switch {
	case isMatterEvent(eventType):
		log.WithField("event_type", eventType).Debug("Routing to matter event processor")
		return e.ProcessMatterEvent(ctx, tenantID, eventType, eventData)

	case isClientEvent(eventType):
		log.WithField("event_type", eventType).Debug("Routing to client event processor")
		return e.ProcessClientEvent(ctx, tenantID, eventType, eventData)

	default:
		log.Warn("Unknown event type", map[string]interface{}{
			"event_type": eventType,
		})
		return fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// ProcessMatterEvent specifically handles matter events
func (e *eventRuleMatchingService) ProcessMatterEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Debug("Processing matter event")

	// Validate event type
	if !isMatterEvent(eventType) {
		return fmt.Errorf("invalid matter event type: %s", eventType)
	}

	// Route to specific matter event handler
	switch eventType {
	case "matter.create":
		return e.matterEventConsumer.ConsumeMatterCreated(ctx, tenantID, eventData)
	case "matter.update":
		return e.matterEventConsumer.ConsumeMatterUpdated(ctx, tenantID, eventData)
	default:
		log.Warn("Unsupported matter event type", map[string]interface{}{
			"event_type": eventType,
		})
		return fmt.Errorf("unsupported matter event type: %s", eventType)
	}
}

// ProcessClientEvent specifically handles client events
func (e *eventRuleMatchingService) ProcessClientEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Debug("Processing client event")

	// Validate event type
	if !isClientEvent(eventType) {
		return fmt.Errorf("invalid client event type: %s", eventType)
	}

	// Route to specific client event handler
	switch eventType {
	case "client.create":
		return e.clientEventConsumer.ConsumeClientCreated(ctx, tenantID, eventData)
	case "client.update":
		return e.clientEventConsumer.ConsumeClientUpdated(ctx, tenantID, eventData)
	default:
		log.Warn("Unsupported client event type", map[string]interface{}{
			"event_type": eventType,
		})
		return fmt.Errorf("unsupported client event type: %s", eventType)
	}
}

// isMatterEvent checks if the event type is a matter event
func isMatterEvent(eventType string) bool {
	switch eventType {
	case "matter.create", "matter.update", "matter.delete":
		return true
	default:
		return false
	}
}

// isClientEvent checks if the event type is a client event
func isClientEvent(eventType string) bool {
	switch eventType {
	case "client.create", "client.update", "client.delete":
		return true
	default:
		return false
	}
}

// EventProcessingResult represents the result of event processing
type EventProcessingResult struct {
	EventType        string `json:"event_type"`
	TenantID         uint64 `json:"tenant_id"`
	RulesMatched     int    `json:"rules_matched"`
	RulesExecuted    int    `json:"rules_executed"`
	RulesFailed      int    `json:"rules_failed"`
	ProcessingTimeMs int64  `json:"processing_time_ms"`
	Success          bool   `json:"success"`
	ErrorMessage     string `json:"error_message,omitempty"`
}

// EventProcessingStats represents statistics for event processing
type EventProcessingStats struct {
	TotalEvents      int64 `json:"total_events"`
	SuccessfulEvents int64 `json:"successful_events"`
	FailedEvents     int64 `json:"failed_events"`
	TotalRulesRun    int64 `json:"total_rules_run"`
	AverageTimeMs    int64 `json:"average_time_ms"`
}
