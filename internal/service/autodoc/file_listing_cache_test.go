package autodoc

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRedisFileListingCache_GetCachedFiles(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("cache miss", func(t *testing.T) {
		req := &ListFilesRequest{
			TenantID: 1,
			Path:     "/test",
		}

		resp, found := cache.GetCachedFiles(ctx, req, "internal")
		assert.False(t, found)
		assert.Nil(t, resp)
	})

	t.Run("cache hit", func(t *testing.T) {
		req := &ListFilesRequest{
			TenantID: 1,
			Path:     "/test",
		}

		// First cache the response
		originalResp := &ListFilesResponse{
			Data: []*DocumentResponse{
				{ID: "1", Name: "test.txt", TenantID: 1},
				{ID: "2", Name: "test2.txt", TenantID: 1},
			},
			TotalCount: 2,
		}

		err := cache.CacheFiles(ctx, req, originalResp, "internal")
		require.NoError(t, err)

		// Now retrieve from cache
		resp, found := cache.GetCachedFiles(ctx, req, "internal")
		assert.True(t, found)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Data, 2)
		assert.Equal(t, int64(2), resp.TotalCount)
		assert.Equal(t, "test.txt", resp.Data[0].Name)
	})
}

func TestRedisFileListingCache_CacheFiles(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("successful caching", func(t *testing.T) {
		req := &ListFilesRequest{
			TenantID: 1,
			Path:     "/documents",
			Page:     1,
			Limit:    10,
		}

		resp := &ListFilesResponse{
			Data: []*DocumentResponse{
				{ID: "1", Name: "doc1.pdf", TenantID: 1},
			},
			TotalCount: 1,
		}

		err := cache.CacheFiles(ctx, req, resp, "gdrive")
		assert.NoError(t, err)

		// Verify it's cached
		cached, found := cache.GetCachedFiles(ctx, req, "gdrive")
		assert.True(t, found)
		assert.Equal(t, resp.TotalCount, cached.TotalCount)
	})
}

func TestRedisFileListingCache_InvalidateCache(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("invalidate specific path", func(t *testing.T) {
		// Cache some files
		req := &ListFilesRequest{
			TenantID: 1,
			Path:     "/test",
		}

		resp := &ListFilesResponse{
			Data:       []*DocumentResponse{{ID: "1", Name: "test.txt"}},
			TotalCount: 1,
		}

		err := cache.CacheFiles(ctx, req, resp, "internal")
		require.NoError(t, err)

		// Verify cached
		cached, found := cache.GetCachedFiles(ctx, req, "internal")
		assert.True(t, found)
		assert.NotNil(t, cached)

		// Invalidate cache
		err = cache.InvalidateCache(ctx, 1, "internal", "/test")
		assert.NoError(t, err)

		// Verify cache is invalidated
		cached, found = cache.GetCachedFiles(ctx, req, "internal")
		assert.False(t, found)
		assert.Nil(t, cached)
	})
}

func TestRedisFileListingCache_InvalidateTenantCache(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("invalidate all tenant cache", func(t *testing.T) {
		// Cache files for tenant 1
		req1 := &ListFilesRequest{TenantID: 1, Path: "/path1"}
		req2 := &ListFilesRequest{TenantID: 1, Path: "/path2"}
		req3 := &ListFilesRequest{TenantID: 2, Path: "/path1"} // Different tenant

		resp := &ListFilesResponse{Data: []*DocumentResponse{{ID: "1", Name: "test.txt"}}, TotalCount: 1}

		err := cache.CacheFiles(ctx, req1, resp, "internal")
		require.NoError(t, err)
		err = cache.CacheFiles(ctx, req2, resp, "internal")
		require.NoError(t, err)
		err = cache.CacheFiles(ctx, req3, resp, "internal")
		require.NoError(t, err)

		// Invalidate tenant 1 cache
		err = cache.InvalidateTenantCache(ctx, 1)
		assert.NoError(t, err)

		// Verify tenant 1 cache is invalidated
		_, found1 := cache.GetCachedFiles(ctx, req1, "internal")
		_, found2 := cache.GetCachedFiles(ctx, req2, "internal")
		assert.False(t, found1)
		assert.False(t, found2)

		// Verify tenant 2 cache is still there
		_, found3 := cache.GetCachedFiles(ctx, req3, "internal")
		assert.True(t, found3)
	})
}

func TestRedisFileListingCache_InvalidateProviderCache(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("invalidate provider cache", func(t *testing.T) {
		// Cache files for different providers
		req := &ListFilesRequest{TenantID: 1, Path: "/test"}
		resp := &ListFilesResponse{Data: []*DocumentResponse{{ID: "1", Name: "test.txt"}}, TotalCount: 1}

		err := cache.CacheFiles(ctx, req, resp, "internal")
		require.NoError(t, err)
		err = cache.CacheFiles(ctx, req, resp, "gdrive")
		require.NoError(t, err)

		// Invalidate internal provider cache
		err = cache.InvalidateProviderCache(ctx, 1, "internal")
		assert.NoError(t, err)

		// Verify internal cache is invalidated
		_, found1 := cache.GetCachedFiles(ctx, req, "internal")
		assert.False(t, found1)

		// Verify gdrive cache is still there
		_, found2 := cache.GetCachedFiles(ctx, req, "gdrive")
		assert.True(t, found2)
	})
}

func TestRedisFileListingCache_GetCacheStats(t *testing.T) {
	// Setup mini Redis
	mr, err := miniredis.Run()
	require.NoError(t, err)
	defer mr.Close()

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	cache := NewRedisFileListingCache(client, 15*time.Minute)
	ctx := context.Background()

	t.Run("get cache statistics", func(t *testing.T) {
		req := &ListFilesRequest{TenantID: 1, Path: "/test"}
		resp := &ListFilesResponse{Data: []*DocumentResponse{{ID: "1", Name: "test.txt"}}, TotalCount: 1}

		// Generate some hits and misses
		_, found := cache.GetCachedFiles(ctx, req, "internal") // Miss
		assert.False(t, found)

		err := cache.CacheFiles(ctx, req, resp, "internal")
		require.NoError(t, err)

		_, found = cache.GetCachedFiles(ctx, req, "internal") // Hit
		assert.True(t, found)

		// Get stats
		stats, err := cache.GetCacheStats(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.Equal(t, int64(1), stats.Hits)
		assert.Equal(t, int64(1), stats.Misses)
		assert.Equal(t, float64(0.5), stats.HitRate)
		assert.True(t, stats.TotalKeys >= 1)
	})
}

func TestRedisFileListingCache_BuildCacheKey(t *testing.T) {
	cache := &RedisFileListingCache{
		keyPrefix: "autodoc:files:",
	}

	t.Run("basic cache key", func(t *testing.T) {
		req := &ListFilesRequest{
			TenantID: 123,
			Path:     "/documents",
		}

		key := cache.buildCacheKey(123, "internal", "/documents", req)
		expected := "autodoc:files:123:internal:_documents"
		assert.Equal(t, expected, key)
	})

	t.Run("cache key with parameters", func(t *testing.T) {
		req := &ListFilesRequest{
			TenantID:  123,
			Path:      "/test",
			FileType:  1,
			Recursive: true,
			Page:      2,
			Limit:     50,
			SortBy:    "name",
			SortOrder: "desc",
		}

		key := cache.buildCacheKey(123, "gdrive", "/test", req)
		assert.Contains(t, key, "autodoc:files:123:gdrive:_test")
		assert.Contains(t, key, "type:1")
		assert.Contains(t, key, "recursive:true")
		assert.Contains(t, key, "sort:name:desc")
		assert.Contains(t, key, "page:2:50")
	})
}

func TestNoOpFileListingCache(t *testing.T) {
	cache := NewNoOpFileListingCache()
	ctx := context.Background()

	t.Run("no-op cache operations", func(t *testing.T) {
		req := &ListFilesRequest{TenantID: 1, Path: "/test"}
		resp := &ListFilesResponse{Data: []*DocumentResponse{}, TotalCount: 0}

		// All operations should be no-op
		cached, found := cache.GetCachedFiles(ctx, req, "internal")
		assert.False(t, found)
		assert.Nil(t, cached)

		err := cache.CacheFiles(ctx, req, resp, "internal")
		assert.NoError(t, err)

		err = cache.InvalidateCache(ctx, 1, "internal", "/test")
		assert.NoError(t, err)

		err = cache.InvalidateTenantCache(ctx, 1)
		assert.NoError(t, err)

		err = cache.InvalidateProviderCache(ctx, 1, "internal")
		assert.NoError(t, err)

		err = cache.WarmCache(ctx, 1, "internal", []string{"/test"})
		assert.NoError(t, err)

		stats, err := cache.GetCacheStats(ctx)
		assert.NoError(t, err)
		assert.NotNil(t, stats)

		err = cache.PublishInvalidation(ctx, &CacheInvalidationEvent{})
		assert.NoError(t, err)

		err = cache.SubscribeInvalidation(ctx, func(*CacheInvalidationEvent) {})
		assert.NoError(t, err)
	})
}
