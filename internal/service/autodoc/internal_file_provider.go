package autodoc

import (
	"context"
	"fmt"
	"strconv"

	"bilabl/docman/pkg/bilabllog"
)

// InternalFileProvider handles file operations for internal storage
type InternalFileProvider struct {
	documentService DocumentService
}

// NewInternalFileProvider creates a new internal file provider
func NewInternalFileProvider(documentService DocumentService) FileProvider {
	return &InternalFileProvider{
		documentService: documentService,
	}
}

// GetProviderName returns the provider name
func (p *InternalFileProvider) GetProviderName() string {
	return "internal"
}

// GetFile gets file information by internal ID
func (p *InternalFileProvider) GetFile(ctx context.Context, req *GetFileRequest) (*GetFileResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Getting file from internal provider")

	// Parse internal ID
	fileID, err := strconv.ParseUint(req.ID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid internal file ID: %s", req.ID)
	}

	// Get document from internal service
	document, err := p.documentService.GetDocument(ctx, fileID, req.TenantID)
	if err != nil {
		log.WithError(err).Error("Failed to get document from internal service")
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	response := &GetFileResponse{
		ID:        req.ID,
		Name:      document.Name,
		Size:      document.Size,
		Provider:  p.GetProviderName(),
		CreatedAt: document.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt: document.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	log.WithField("file_id", fileID).WithField("file_name", document.Name).Info("File retrieved from internal provider")
	return response, nil
}

// UpdateFile updates file information by internal ID
func (p *InternalFileProvider) UpdateFile(ctx context.Context, req *UpdateFileRequest) (*UpdateFileResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Updating file in internal provider")

	// Parse internal ID
	fileID, err := strconv.ParseUint(req.ID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid internal file ID: %s", req.ID)
	}

	// Update document in internal service
	updateReq := &UpdateDocumentRequest{
		ID:       fileID,
		TenantID: req.TenantID,
		Name:     req.Name,
	}

	document, err := p.documentService.UpdateDocument(ctx, updateReq)
	if err != nil {
		log.WithError(err).Error("Failed to update document in internal service")
		return nil, fmt.Errorf("failed to update document: %w", err)
	}

	response := &UpdateFileResponse{
		ID:       req.ID,
		Name:     document.Name,
		Size:     document.Size,
		Provider: p.GetProviderName(),
		Message:  "File updated successfully in internal storage",
	}

	log.WithField("file_id", fileID).WithField("file_name", document.Name).Info("File updated in internal provider")
	return response, nil
}

// DeleteFile deletes file by internal ID
func (p *InternalFileProvider) DeleteFile(ctx context.Context, req *FileDeleteRequest) (*FileDeleteResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Deleting file from internal provider")

	// Parse internal ID
	fileID, err := strconv.ParseUint(req.ID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid internal file ID: %s", req.ID)
	}

	// Delete document from internal service
	err = p.documentService.DeleteDocument(ctx, fileID, req.TenantID)
	if err != nil {
		log.WithError(err).Error("Failed to delete document from internal service")
		return nil, fmt.Errorf("failed to delete document: %w", err)
	}

	response := &FileDeleteResponse{
		ID:       req.ID,
		Provider: p.GetProviderName(),
		Message:  "File deleted successfully from internal storage",
	}

	log.WithField("file_id", fileID).Info("File deleted from internal provider")
	return response, nil
}
