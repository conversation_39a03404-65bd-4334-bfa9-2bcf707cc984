package autodoc

import (
	"testing"

	"bilabl/docman/domain/model"

	"github.com/stretchr/testify/assert"
)

func TestRuleAction_OverrideField(t *testing.T) {
	t.Run("should have override field with default false", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "copy_file",
			SourcePath: "/templates/test.docx",
			TargetPath: "{client_folder}/test.docx",
		}

		// Override should default to false
		assert.False(t, action.Override)
	})

	t.Run("should allow setting override to true", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "generate_document",
			SourcePath: "/templates/contract.docx",
			TargetPath: "{client_folder}/contract.docx",
			Override:   true,
		}

		assert.True(t, action.Override)
	})

	t.Run("should work with copy_folder action", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "copy_folder",
			SourcePath: "/templates/client-folder",
			TargetPath: "{client_folder}",
			Override:   true,
		}

		assert.True(t, action.Override)
		assert.Equal(t, "copy_folder", action.ActionType)
	})
}

func TestRuleAction_JSONSerialization(t *testing.T) {
	t.Run("should serialize override field to JSON", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "copy_file",
			SourcePath: "/templates/test.docx",
			TargetPath: "{client_folder}/test.docx",
			Override:   true,
		}

		// This would be used in actual JSON serialization
		assert.True(t, action.Override)
		assert.Equal(t, "copy_file", action.ActionType)
	})

	t.Run("should handle override false in JSON", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "generate_document",
			SourcePath: "/templates/contract.docx",
			TargetPath: "{client_folder}/contract.docx",
			Override:   false,
		}

		assert.False(t, action.Override)
	})
}

func TestActionExecutionParams_OverrideAccess(t *testing.T) {
	t.Run("should access override from action in params", func(t *testing.T) {
		action := model.RuleAction{
			ActionType: "copy_file",
			SourcePath: "/templates/test.docx",
			TargetPath: "{client_folder}/test.docx",
			Override:   true,
		}

		params := &ActionExecutionParams{
			TenantID:        1,
			Action:          action,
			PlaceholderData: map[string]interface{}{},
			RuleID:          123,
		}

		// Handlers can access override setting via params.Action.Override
		assert.True(t, params.Action.Override)
	})
}
