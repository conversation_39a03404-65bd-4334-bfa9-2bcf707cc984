package autodoc

import (
	"context"
	"encoding/json"
	"fmt"

	"bilabl/docman/pkg/bilabllog"

	"code.mybil.net/gophers/gokit/clients"
	_ "code.mybil.net/gophers/gokit/clients/matter" // Register matter client
	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"code.mybil.net/gophers/gokit/util/jsonutil"
)

// MatterEventConsumer handles matter-related events for automation
type MatterEventConsumer interface {
	// ConsumeMatterCreated processes matter.create events
	ConsumeMatterCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error

	// ConsumeMatterUpdated processes matter.update events
	ConsumeMatterUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error
}

// matterEventConsumer implements MatterEventConsumer
type matterEventConsumer struct {
	ruleMatchingService RuleMatchingService
	ruleExecutionEngine RuleExecutionEngine
	matterClient        clients.MatterClient
}

// NewMatterEventConsumer creates a new matter event consumer
func NewMatterEventConsumer(
	ruleMatchingService RuleMatchingService,
	ruleExecutionEngine RuleExecutionEngine,
) MatterEventConsumer {
	return &matterEventConsumer{
		ruleMatchingService: ruleMatchingService,
		ruleExecutionEngine: ruleExecutionEngine,
		matterClient:        clients.GetMatter(),
	}
}

// ConsumeMatterCreated processes matter.create events
func (m *matterEventConsumer) ConsumeMatterCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing matter.create event", map[string]interface{}{
		"tenant_id":  tenantID,
		"event_data": eventData,
	})

	return m.processEvent(ctx, tenantID, "matter.create", eventData)
}

// ConsumeMatterUpdated processes matter.update events
func (m *matterEventConsumer) ConsumeMatterUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Processing matter.update event", map[string]interface{}{
		"tenant_id":  tenantID,
		"event_data": eventData,
	})

	return m.processEvent(ctx, tenantID, "matter.update", eventData)
}

// processEvent handles the common event processing logic
func (m *matterEventConsumer) processEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).WithField("event_data", eventData).Info("Starting event processing")

	// Find matching rules
	log.WithField("tenant_id", tenantID).WithField("event_type", eventType).Debug("Finding matching rules")

	matchedRules, err := m.ruleMatchingService.MatchRules(ctx, tenantID, eventType, eventData)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).WithField("event_type", eventType).Error("Failed to match rules")
		return fmt.Errorf("failed to match rules: %w", err)
	}

	if len(matchedRules) == 0 {
		log.WithField("event_type", eventType).WithField("tenant_id", tenantID).Info("No matching rules found")
		return nil
	}

	log.WithField("rule_count", len(matchedRules)).WithField("event_type", eventType).Info("Found matching rules")

	// enrich matter data
	if err := m.enrichMatterData(ctx, tenantID, eventData); err != nil {
		log.
			WithError(err).
			WithField("tenant_id", tenantID).
			WithField("event_type", eventType).Error("Failed to enrich matter data")
	}

	// Execute each matched rule
	var executionErrors []error
	successCount := 0

	for _, rule := range matchedRules {
		log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("tenant_id", tenantID).Info("Executing matched rule")

		err := m.ruleExecutionEngine.ExecuteRule(ctx, rule.ID, eventData)
		if err != nil {
			log.WithError(err).WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("tenant_id", tenantID).Error("Failed to execute rule")
			executionErrors = append(executionErrors, fmt.Errorf("rule %d (%s): %w", rule.ID, rule.Name, err))
			continue
		}

		log.WithField("rule_id", rule.ID).WithField("rule_name", rule.Name).WithField("tenant_id", tenantID).Info("Rule executed successfully")
		successCount++
	}

	// Log final results
	log.Info("Event processing completed", map[string]interface{}{
		"tenant_id":   tenantID,
		"event_type":  eventType,
		"total_rules": len(matchedRules),
		"successful":  successCount,
		"failed":      len(executionErrors),
	})

	// Return error if any rule execution failed
	if len(executionErrors) > 0 {
		return fmt.Errorf("failed to execute %d out of %d rules: %v", len(executionErrors), len(matchedRules), executionErrors)
	}

	return nil
}

func (m *matterEventConsumer) enrichMatterData(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	var matterEventBody MatterEventBody
	bodyBytes := jsonutil.MustMarshal(eventData)
	if err := json.Unmarshal(bodyBytes, &matterEventBody); err != nil {
		return err
	}

	actorCtx := actor.NewCtxWithActor(ctx, actor.Actor{
		ID:       matterEventBody.ActorID,
		TenantID: tenantID,
		Roles:    userrole.RoleAdmin,
	})

	matterObject, err := m.matterClient.GetMatter(actorCtx, matterEventBody.ID)
	if err != nil {
		return err
	}

	// map normalize matter object
	var mapData map[string]interface{}
	jsonBytes, err := json.Marshal(matterObject)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(jsonBytes, &mapData); err != nil {
		return err
	}

	eventData["matter"] = mapData
	return nil
}

// MatterEventData represents the structure of matter event data
type MatterEventData struct {
	Matter MatterInfo `json:"matter"`
	Client ClientInfo `json:"client"`
}

// MatterInfo represents matter information in events
type MatterInfo struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Category    string `json:"category"`
	Description string `json:"description"`
	OwnerID     uint64 `json:"owner_id"`
	OwnerName   string `json:"owner_name"`
	Status      string `json:"status"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// ClientInfo represents client information in events
type ClientInfo struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Stage       string `json:"stage"`
	CompanyName string `json:"company_name"`
	ContactName string `json:"contact_name"`
}
