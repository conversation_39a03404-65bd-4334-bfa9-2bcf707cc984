package autodoc

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPathResolver_ResolveTargetPath(t *testing.T) {
	// Create a mock resolver without repository for basic placeholder tests
	resolver := &PathResolver{}
	ctx := context.Background()

	tests := []struct {
		name         string
		pathTemplate string
		context      map[string]interface{}
		expected     string
		expectError  bool
	}{
		{
			name:         "simple placeholder replacement",
			pathTemplate: "/clients/{client_name}/documents",
			context: map[string]interface{}{
				"client_name": "ACME Corp",
			},
			expected:    "/clients/ACME Corp/documents",
			expectError: false,
		},
		{
			name:         "nested placeholder replacement",
			pathTemplate: "/clients/{client.name}/matters/{matter.id}",
			context: map[string]interface{}{
				"client": map[string]interface{}{
					"name": "ACME Corp",
				},
				"matter": map[string]interface{}{
					"id": "12345",
				},
			},
			expected:    "/clients/ACME Corp/matters/12345",
			expectError: false,
		},
		{
			name:         "multiple placeholders same level",
			pathTemplate: "/{year}/{month}/{client_name}",
			context: map[string]interface{}{
				"year":        2024,
				"month":       "January",
				"client_name": "Test Client",
			},
			expected:    "/2024/January/Test Client",
			expectError: false,
		},
		{
			name:         "no placeholders",
			pathTemplate: "/static/path/documents",
			context:      map[string]interface{}{},
			expected:     "/static/path/documents",
			expectError:  false,
		},
		{
			name:         "empty template",
			pathTemplate: "",
			context:      map[string]interface{}{},
			expected:     "",
			expectError:  true,
		},
		{
			name:         "missing placeholder value",
			pathTemplate: "/clients/{{missing_value}}/documents",
			context:      map[string]interface{}{},
			expected:     "",
			expectError:  true,
		},
		{
			name:         "special characters in values",
			pathTemplate: "/clients/{client_name}/documents",
			context: map[string]interface{}{
				"client_name": "ACME & Co. (Ltd.)",
			},
			expected:    "/clients/ACME & Co. (Ltd.)/documents",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := resolver.ResolveTargetPath(ctx, tt.pathTemplate, tt.context)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestPathResolver_ValidatePathTemplate(t *testing.T) {
	resolver := &PathResolver{}

	tests := []struct {
		name         string
		pathTemplate string
		expectError  bool
	}{
		{
			name:         "valid template with placeholders",
			pathTemplate: "/clients/{client.name}/matters/{matter.id}",
			expectError:  false,
		},
		{
			name:         "valid template without placeholders",
			pathTemplate: "/static/path/documents",
			expectError:  false,
		},
		{
			name:         "empty template",
			pathTemplate: "",
			expectError:  true,
		},
		{
			name:         "malformed placeholder - missing closing",
			pathTemplate: "/clients/{client.name/documents",
			expectError:  true, // ValidatePathTemplate should detect unbalanced braces
		},
		{
			name:         "malformed placeholder - missing opening",
			pathTemplate: "/clients/client.name}/documents",
			expectError:  true, // ValidatePathTemplate should detect unbalanced braces
		},
		{
			name:         "double braces not supported",
			pathTemplate: "/clients/{{client.name}}/documents",
			expectError:  true, // Should reject double braces
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := resolver.ValidatePathTemplate(tt.pathTemplate)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPathResolver_getNestedValue(t *testing.T) {
	resolver := &PathResolver{}

	context := map[string]interface{}{
		"client": map[string]interface{}{
			"name": "ACME Corp",
			"id":   12345,
			"contact": map[string]interface{}{
				"email": "<EMAIL>",
				"phone": "555-1234",
			},
		},
		"matter": map[string]interface{}{
			"id":   "matter-123",
			"type": "litigation",
		},
		"simple_value": "test",
		"number_value": 42,
	}

	tests := []struct {
		name        string
		key         string
		expected    interface{}
		expectError bool
	}{
		{
			name:        "simple key",
			key:         "simple_value",
			expected:    "test",
			expectError: false,
		},
		{
			name:        "nested key - level 1",
			key:         "client.name",
			expected:    "ACME Corp",
			expectError: false,
		},
		{
			name:        "nested key - level 2",
			key:         "client.contact.email",
			expected:    "<EMAIL>",
			expectError: false,
		},
		{
			name:        "number value",
			key:         "client.id",
			expected:    12345,
			expectError: false,
		},
		{
			name:        "missing key",
			key:         "nonexistent",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "missing nested key",
			key:         "client.nonexistent",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid nested path",
			key:         "simple_value.nested",
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := resolver.getNestedValue(context, tt.key)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestPathResolver_valueToString(t *testing.T) {
	resolver := &PathResolver{}

	tests := []struct {
		name     string
		value    interface{}
		expected string
	}{
		{
			name:     "string value",
			value:    "test string",
			expected: "test string",
		},
		{
			name:     "integer value",
			value:    42,
			expected: "42",
		},
		{
			name:     "float value",
			value:    3.14,
			expected: "3",
		},
		{
			name:     "boolean true",
			value:    true,
			expected: "true",
		},
		{
			name:     "boolean false",
			value:    false,
			expected: "false",
		},
		{
			name:     "nil value",
			value:    nil,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := resolver.valueToString(tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPathResolver_sanitizePathComponent(t *testing.T) {
	resolver := &PathResolver{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "clean string",
			input:    "clean_string",
			expected: "clean_string",
		},
		{
			name:     "string with spaces",
			input:    "string with spaces",
			expected: "string with spaces",
		},
		{
			name:     "string with special characters",
			input:    "ACME & Co. (Ltd.)",
			expected: "ACME & Co. (Ltd.)",
		},
		{
			name:     "string with path separators",
			input:    "path/with\\separators",
			expected: "path_with_separators",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "unnamed",
		},
		{
			name:     "string with only invalid characters",
			input:    "/\\:*?\"<>|",
			expected: "_________",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := resolver.sanitizePathComponent(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
