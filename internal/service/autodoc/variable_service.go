package autodoc

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"bilabl/docman/pkg/bilabllog"

	"code.mybil.net/gophers/gokit/clients"
	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"github.com/sirupsen/logrus"
)

// VariableInfo represents information about a template variable
type VariableInfo struct {
	Name        string      `json:"name"`
	Value       interface{} `json:"value"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
}

// VariableDefinition represents a static variable definition
type VariableDefinition struct {
	Name        string
	Description string
	Category    string
}

// VariableService provides template variable information
type VariableService interface {
	GetAvailableVariables(ctx context.Context, objectType int, objectID *uint64, tenantID uint64) ([]VariableInfo, error)
}

// variableService implements VariableService
type variableService struct {
	clientClient  clients.ClientServiceClient
	matterClient  clients.MatterClient
	entityFetcher entity.GenericFetcher
}

// NewVariableService creates a new VariableService
func NewVariableService(
	clientClient clients.ClientServiceClient,
	matterClient clients.MatterClient,
	entityFetcher entity.GenericFetcher,
) VariableService {
	return &variableService{
		clientClient:  clientClient,
		matterClient:  matterClient,
		entityFetcher: entityFetcher,
	}
}

// Most important client variables for template usage (filtered list)
var importantClientFields = []string{
	"id", "name", "short_name", "code", "email", "phone",
	"website", "address", "city", "state", "country", "postal_code",
	"tax_id", "registration_number", "industry", "stage", "stage_text",
	"account_manager_email", "created_at", "updated_at",
}

// Most important matter variables for template usage (filtered list)
var importantMatterFields = []string{
	"id", "name", "code", "client_id", "category", "description",
	"owner_id", "owner_name", "status", "created_at", "updated_at",
}

// Client variables (object_type=1)
var clientVariables = []VariableDefinition{
	// Basic client fields (template format)
	{Name: "{client.id}", Description: "Client ID", Category: "client"},
	{Name: "{client.name}", Description: "Client company name", Category: "client"},
	{Name: "{client.short_name}", Description: "Client short name", Category: "client"},
	{Name: "{client.code}", Description: "Client code", Category: "client"},
	{Name: "{client.email}", Description: "Client email address", Category: "client"},
	{Name: "{client.phone}", Description: "Client phone number", Category: "client"},
	{Name: "{client.website}", Description: "Client website", Category: "client"},
	{Name: "{client.address}", Description: "Client address", Category: "client"},
	{Name: "{client.city}", Description: "Client city", Category: "client"},
	{Name: "{client.state}", Description: "Client state", Category: "client"},
	{Name: "{client.country}", Description: "Client country", Category: "client"},
	{Name: "{client.postal_code}", Description: "Client postal code", Category: "client"},
	{Name: "{client.tax_id}", Description: "Client tax ID", Category: "client"},
	{Name: "{client.registration_number}", Description: "Client registration number", Category: "client"},
	{Name: "{client.industry}", Description: "Client industry", Category: "client"},
	{Name: "{client.stage}", Description: "Client stage", Category: "client"},
	{Name: "{client.stage_text}", Description: "Client stage description", Category: "client"},
	{Name: "{client.account_manager_email}", Description: "Client account manager email", Category: "client"},
	{Name: "{client.created_at}", Description: "Client creation date", Category: "client"},
	{Name: "{client.updated_at}", Description: "Client last update date", Category: "client"},

	// Special placeholders
	{Name: "{client_folder}", Description: "Special placeholder that resolves to client's root folder", Category: "special"},

	// Timestamp placeholders
	{Name: "{timestamp}", Description: "Current timestamp (YYYYMMDD_HHMMSS)", Category: "timestamp"},
	{Name: "{date}", Description: "Current date (YYYY-MM-DD)", Category: "timestamp"},
}

// Matter variables (object_type=3)
var matterVariables = []VariableDefinition{
	// Basic matter fields (template format)
	{Name: "{matter.id}", Description: "Matter ID", Category: "matter"},
	{Name: "{matter.name}", Description: "Matter name", Category: "matter"},
	{Name: "{matter.code}", Description: "Matter code", Category: "matter"},
	{Name: "{matter.client_id}", Description: "Associated client ID", Category: "matter"},
	{Name: "{matter.category}", Description: "Matter category", Category: "matter"},
	{Name: "{matter.description}", Description: "Matter description", Category: "matter"},
	{Name: "{matter.owner_id}", Description: "Matter owner ID", Category: "matter"},
	{Name: "{matter.owner_name}", Description: "Matter owner name", Category: "matter"},
	{Name: "{matter.status}", Description: "Matter status", Category: "matter"},
	{Name: "{matter.created_at}", Description: "Matter creation date", Category: "matter"},
	{Name: "{matter.updated_at}", Description: "Matter last update date", Category: "matter"},

	// Client fields (inherited from matter's client)
	{Name: "{client.name}", Description: "Client company name", Category: "client"},
	{Name: "{client.short_name}", Description: "Client short name", Category: "client"},
	{Name: "{client.code}", Description: "Client code", Category: "client"},
	{Name: "{client.email}", Description: "Client email address", Category: "client"},
	{Name: "{client.phone}", Description: "Client phone number", Category: "client"},

	// Special placeholders
	{Name: "{matter_folder}", Description: "Special placeholder that resolves to matter's root folder", Category: "special"},
	{Name: "{client_folder}", Description: "Special placeholder that resolves to client's root folder", Category: "special"},

	// Timestamp placeholders
	{Name: "{timestamp}", Description: "Current timestamp (YYYYMMDD_HHMMSS)", Category: "timestamp"},
	{Name: "{date}", Description: "Current date (YYYY-MM-DD)", Category: "timestamp"},
}

// GetAvailableVariables returns all available variables for the given object type
func (s *variableService) GetAvailableVariables(ctx context.Context, objectType int, objectID *uint64, tenantID uint64) ([]VariableInfo, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("object_type", objectType).WithField("object_id", objectID).WithField("tenant_id", tenantID).Info("Getting available variables")

	// Validate object type
	if objectType != 1 && objectType != 3 {
		log.WithField("object_type", objectType).Error("Unsupported object type")
		return nil, fmt.Errorf("unsupported object_type: %d (supported: 1=client, 3=matter)", objectType)
	}

	// If no objectID provided, return static schema definitions
	if objectID == nil {
		return s.getStaticVariableDefinitions(objectType, log), nil
	}

	// If objectID provided, fetch real data and generate dynamic variables
	return s.getDynamicVariablesWithData(ctx, objectType, *objectID, tenantID, log)
}

// getStaticVariableDefinitions returns static variable definitions for schema-only mode
func (s *variableService) getStaticVariableDefinitions(objectType int, log *logrus.Entry) []VariableInfo {
	var definitions []VariableDefinition

	switch objectType {
	case 1: // Client
		definitions = clientVariables
		log.WithField("variable_count", len(clientVariables)).Debug("Using client variable definitions")
	case 3: // Matter
		definitions = matterVariables
		log.WithField("variable_count", len(matterVariables)).Debug("Using matter variable definitions")
	}

	// Convert definitions to VariableInfo with null values
	variables := make([]VariableInfo, len(definitions))
	for i, def := range definitions {
		variables[i] = VariableInfo{
			Name:        def.Name,
			Description: def.Description,
			Category:    def.Category,
			Value:       nil, // Schema-only mode
		}
	}

	log.WithField("total_variables", len(variables)).Info("Variables returned in schema-only mode")
	return variables
}

// getDynamicVariablesWithData fetches object data and generates dynamic variables
func (s *variableService) getDynamicVariablesWithData(ctx context.Context, objectType int, objectID uint64, tenantID uint64, log *logrus.Entry) ([]VariableInfo, error) {
	switch objectType {
	case 1: // Client
		return s.getClientVariablesWithData(ctx, objectID, tenantID, log)
	case 3: // Matter
		return s.getMatterVariablesWithData(ctx, objectID, tenantID, log)
	default:
		return nil, fmt.Errorf("unsupported object_type: %d", objectType)
	}
}

// getClientVariablesWithData fetches client data once and generates all variables dynamically
func (s *variableService) getClientVariablesWithData(ctx context.Context, clientID uint64, tenantID uint64, log *logrus.Entry) ([]VariableInfo, error) {
	log.WithField("client_id", clientID).Debug("Fetching client data for dynamic variable generation")

	// Create actor context for service calls
	actorCtx := actor.NewCtxWithActor(ctx, actor.Actor{
		ID:       1, // System actor
		TenantID: tenantID,
		Roles:    userrole.RoleAdmin,
	})

	// Try to get client data from service client first (preferred)
	clientObject, err := s.clientClient.GetClient(actorCtx, clientID)
	if err != nil {
		log.WithError(err).WithField("client_id", clientID).Warn("Failed to fetch client from service client, trying entity fetcher")

		// Fallback to entity fetcher if service client fails
		entityClient, fetchErr := s.entityFetcher.FetchCache(ctx, entity.KindClient, entity.ID(clientID))
		if fetchErr != nil {
			log.WithError(fetchErr).WithField("client_id", clientID).Error("Failed to fetch client from entity fetcher")
			return nil, fmt.Errorf("failed to fetch client from both service and entity: %v, %v", err, fetchErr)
		}
		log.WithField("client_id", clientID).Info("Successfully used entity fetcher as fallback")
		return s.generateClientVariablesFromEntity(entityClient, clientID, log), nil
	}

	log.WithField("client_id", clientID).Debug("Successfully fetched client from service client")

	// Normalize client object using existing function
	normalizedClient := normalizeClientObject(clientObject)

	// Generate variables dynamically from all fields in the response
	return s.generateClientVariablesFromServiceData(normalizedClient, clientID, log), nil
}

// getMatterVariablesWithData fetches matter data once and generates all variables dynamically
func (s *variableService) getMatterVariablesWithData(ctx context.Context, matterID uint64, tenantID uint64, log *logrus.Entry) ([]VariableInfo, error) {
	log.WithField("matter_id", matterID).Debug("Fetching matter data for dynamic variable generation")

	// Create actor context for service calls
	actorCtx := actor.NewCtxWithActor(ctx, actor.Actor{
		ID:       1, // System actor
		TenantID: tenantID,
		Roles:    userrole.RoleAdmin,
	})

	// Try to get matter data from service client first (preferred)
	matterObject, err := s.matterClient.GetMatter(actorCtx, matterID)
	if err != nil {
		log.WithError(err).WithField("matter_id", matterID).Warn("Failed to fetch matter from service client, trying entity fetcher")

		// Fallback to entity fetcher if service client fails
		entityMatter, fetchErr := s.entityFetcher.FetchCache(ctx, entity.KindMatter, entity.ID(matterID))
		if fetchErr != nil {
			log.WithError(fetchErr).WithField("matter_id", matterID).Error("Failed to fetch matter from entity fetcher")
			return nil, fmt.Errorf("failed to fetch matter from both service and entity: %v, %v", err, fetchErr)
		}
		log.WithField("matter_id", matterID).Info("Successfully used entity fetcher as fallback")
		return s.generateMatterVariablesFromEntity(entityMatter, matterID, log), nil
	}

	log.WithField("matter_id", matterID).Debug("Successfully fetched matter from service client")

	// Normalize matter object (convert to map)
	normalizedMatter := s.normalizeMatterObject(matterObject)

	// Generate variables dynamically from all fields in the response
	return s.generateMatterVariablesFromServiceData(ctx, normalizedMatter, matterID, tenantID, log)
}

// generateClientVariablesFromServiceData creates variables from important client fields only
func (s *variableService) generateClientVariablesFromServiceData(clientData map[string]interface{}, clientID uint64, log *logrus.Entry) []VariableInfo {
	variables := make([]VariableInfo, 0)

	// Generate variables only for important fields
	for _, fieldName := range importantClientFields {
		if fieldName == "" {
			continue
		}

		// Get value from client data (case-insensitive lookup)
		fieldValue := s.getFieldValue(clientData, fieldName)

		// Format as template variable
		variableName := fmt.Sprintf("{client.%s}", fieldName)
		description := s.generateFieldDescription(fieldName, "client")

		variables = append(variables, VariableInfo{
			Name:        variableName,
			Value:       fieldValue,
			Description: description,
			Category:    "client",
		})
	}

	// Add special placeholders
	variables = append(variables, VariableInfo{
		Name:        "{client_folder}",
		Value:       fmt.Sprintf("object:1:%d:1", clientID),
		Description: "Special placeholder that resolves to client's root folder",
		Category:    "special",
	})

	// Add timestamp placeholders
	variables = append(variables, s.getTimestampVariables()...)

	log.WithField("total_variables", len(variables)).WithField("important_fields", len(importantClientFields)).Info("Generated filtered client variables from service data")
	return variables
}

// getFieldValue performs case-insensitive lookup for field values
func (s *variableService) getFieldValue(data map[string]interface{}, fieldName string) interface{} {
	// Try exact match first
	if value, exists := data[fieldName]; exists {
		return value
	}

	// Try case-insensitive lookup
	for key, value := range data {
		if strings.EqualFold(key, fieldName) {
			return value
		}
	}

	// Return empty string if not found
	return ""
}

// generateMatterVariablesFromServiceData creates variables from important matter fields only
func (s *variableService) generateMatterVariablesFromServiceData(ctx context.Context, matterData map[string]interface{}, matterID uint64, tenantID uint64, log *logrus.Entry) ([]VariableInfo, error) {
	variables := make([]VariableInfo, 0)

	// Generate variables only for important matter fields
	for _, fieldName := range importantMatterFields {
		if fieldName == "" {
			continue
		}

		// Get value from matter data (case-insensitive lookup)
		fieldValue := s.getFieldValue(matterData, fieldName)

		// Format as template variable
		variableName := fmt.Sprintf("{matter.%s}", fieldName)
		description := s.generateFieldDescription(fieldName, "matter")

		variables = append(variables, VariableInfo{
			Name:        variableName,
			Value:       fieldValue,
			Description: description,
			Category:    "matter",
		})
	}

	// Extract client ID and fetch client data for client context variables
	clientID := s.extractClientIDFromMatter(matterData, log)
	if clientID > 0 {
		clientVariables, err := s.getClientVariablesForMatterContext(ctx, clientID, tenantID, log)
		if err != nil {
			log.WithError(err).WithField("client_id", clientID).Warn("Failed to fetch client variables for matter context")
		} else {
			variables = append(variables, clientVariables...)
		}
	}

	// Add special placeholders
	variables = append(variables, VariableInfo{
		Name:        "{matter_folder}",
		Value:       fmt.Sprintf("object:3:%d:1", matterID),
		Description: "Special placeholder that resolves to matter's root folder",
		Category:    "special",
	})

	variables = append(variables, VariableInfo{
		Name:        "{client_folder}",
		Value:       fmt.Sprintf("object:1:%d:1", clientID),
		Description: "Special placeholder that resolves to client's root folder",
		Category:    "special",
	})

	// Add timestamp placeholders
	variables = append(variables, s.getTimestampVariables()...)

	log.WithField("total_variables", len(variables)).WithField("dynamic_matter_fields", len(matterData)).Info("Generated dynamic matter variables from service data")
	return variables, nil
}

// generateFieldDescription creates a human-readable description for a field
func (s *variableService) generateFieldDescription(fieldName, objectType string) string {
	// Use predefined descriptions for known fields, generate generic ones for others
	knownDescriptions := map[string]string{
		"id":           fmt.Sprintf("%s ID", strings.Title(objectType)),
		"name":         fmt.Sprintf("%s name", strings.Title(objectType)),
		"code":         fmt.Sprintf("%s code", strings.Title(objectType)),
		"email":        "Email address",
		"phone":        "Phone number",
		"address":      "Address",
		"city":         "City",
		"country":      "Country",
		"industry":     "Industry",
		"stage":        "Stage",
		"stage_text":   "Stage text",
		"company_name": "Company name",
		"contact_name": "Contact name",
		"short_name":   "Short name",
		"client_id":    "Associated client ID",
		"category":     "Category",
		"description":  "Description",
		"owner_id":     "Owner ID",
		"owner_name":   "Owner name",
		"status":       "Status",
		"created_at":   "Creation date",
		"updated_at":   "Last update date",
	}

	if desc, exists := knownDescriptions[fieldName]; exists {
		return desc
	}

	// Generate generic description for unknown fields
	return fmt.Sprintf("%s %s", strings.Title(objectType), strings.ReplaceAll(fieldName, "_", " "))
}

// getTimestampVariables returns timestamp-related variables
func (s *variableService) getTimestampVariables() []VariableInfo {
	now := time.Now()
	return []VariableInfo{
		{
			Name:        "{timestamp}",
			Value:       now.Format("20060102_150405"),
			Description: "Current timestamp (YYYYMMDD_HHMMSS)",
			Category:    "timestamp",
		},
		{
			Name:        "{date}",
			Value:       now.Format("2006-01-02"),
			Description: "Current date (YYYY-MM-DD)",
			Category:    "timestamp",
		},
	}
}

// extractClientIDFromMatter extracts client ID from matter data
func (s *variableService) extractClientIDFromMatter(matterData map[string]interface{}, log *logrus.Entry) uint64 {
	if clientID, ok := matterData["client_id"].(uint64); ok {
		return clientID
	}

	// Try different type conversions
	if clientIDFloat, ok := matterData["client_id"].(float64); ok {
		return uint64(clientIDFloat)
	}

	if clientIDInt, ok := matterData["client_id"].(int64); ok {
		return uint64(clientIDInt)
	}

	if clientIDInt, ok := matterData["client_id"].(int); ok {
		return uint64(clientIDInt)
	}

	log.WithField("matter_data", matterData).Warn("client_id not found or not convertible to uint64")
	return 0
}

// getClientVariablesForMatterContext fetches client data for matter context
func (s *variableService) getClientVariablesForMatterContext(ctx context.Context, clientID uint64, tenantID uint64, log *logrus.Entry) ([]VariableInfo, error) {
	log.WithField("client_id", clientID).Debug("Fetching client data for matter context")

	// Create actor context for service calls
	actorCtx := actor.NewCtxWithActor(ctx, actor.Actor{
		ID:       1, // System actor
		TenantID: tenantID,
		Roles:    userrole.RoleAdmin,
	})

	// Try to get client data from service client
	clientObject, err := s.clientClient.GetClient(actorCtx, clientID)
	if err != nil {
		log.WithError(err).WithField("client_id", clientID).Debug("Failed to fetch client for matter context, trying entity fetcher")

		// Fallback to entity fetcher
		entityClient, fetchErr := s.entityFetcher.FetchCache(ctx, entity.KindClient, entity.ID(clientID))
		if fetchErr != nil {
			return nil, fmt.Errorf("failed to fetch client for matter context: %v", fetchErr)
		}
		return s.generateClientVariablesFromEntityForMatterContext(entityClient), nil
	}

	// Normalize client object and generate variables
	normalizedClient := normalizeClientObject(clientObject)

	variables := make([]VariableInfo, 0)
	// Generate variables only for important client fields
	for _, fieldName := range importantClientFields {
		if fieldName == "" {
			continue
		}

		// Get value from client data (case-insensitive lookup)
		fieldValue := s.getFieldValue(normalizedClient, fieldName)

		// Format as template variable
		variableName := fmt.Sprintf("{client.%s}", fieldName)
		description := s.generateFieldDescription(fieldName, "client")

		variables = append(variables, VariableInfo{
			Name:        variableName,
			Value:       fieldValue,
			Description: description,
			Category:    "client",
		})
	}

	log.WithField("client_variables_count", len(variables)).Debug("Generated client variables for matter context")
	return variables, nil
}

// generateClientVariablesFromEntity creates variables from entity.Generic client data (fallback)
func (s *variableService) generateClientVariablesFromEntity(entityClient *entity.Generic, clientID uint64, log *logrus.Entry) []VariableInfo {
	variables := []VariableInfo{
		{
			Name:        "client.id",
			Value:       uint64(entityClient.ID),
			Description: "Client ID",
			Category:    "client",
		},
		{
			Name:        "client.name",
			Value:       entityClient.Name,
			Description: "Client name",
			Category:    "client",
		},
		{
			Name:        "client.short_name",
			Value:       entityClient.ShortName,
			Description: "Client short name",
			Category:    "client",
		},
		{
			Name:        "client.code",
			Value:       entityClient.Code,
			Description: "Client code",
			Category:    "client",
		},
	}

	// Add special placeholders
	variables = append(variables, VariableInfo{
		Name:        "client_folder",
		Value:       fmt.Sprintf("object:1:%d:1", clientID),
		Description: "Special placeholder that resolves to client's root folder",
		Category:    "special",
	})

	// Add timestamp placeholders
	variables = append(variables, s.getTimestampVariables()...)

	log.WithField("total_variables", len(variables)).Info("Generated client variables from entity fallback")
	return variables
}

// generateMatterVariablesFromEntity creates variables from entity.Generic matter data (fallback)
func (s *variableService) generateMatterVariablesFromEntity(entityMatter *entity.Generic, matterID uint64, log *logrus.Entry) []VariableInfo {
	variables := []VariableInfo{
		{
			Name:        "matter.id",
			Value:       uint64(entityMatter.ID),
			Description: "Matter ID",
			Category:    "matter",
		},
		{
			Name:        "matter.name",
			Value:       entityMatter.Name,
			Description: "Matter name",
			Category:    "matter",
		},
		{
			Name:        "matter.code",
			Value:       entityMatter.Code,
			Description: "Matter code",
			Category:    "matter",
		},
	}

	// Add special placeholders
	variables = append(variables, VariableInfo{
		Name:        "matter_folder",
		Value:       fmt.Sprintf("object:3:%d:1", matterID),
		Description: "Special placeholder that resolves to matter's root folder",
		Category:    "special",
	})

	variables = append(variables, VariableInfo{
		Name:        "client_folder",
		Value:       fmt.Sprintf("object:1:%d:1", matterID), // Fallback - use matter ID
		Description: "Special placeholder that resolves to client's root folder",
		Category:    "special",
	})

	// Add timestamp placeholders
	variables = append(variables, s.getTimestampVariables()...)

	log.WithField("total_variables", len(variables)).Info("Generated matter variables from entity fallback")
	return variables
}

// generateClientVariablesFromEntityForMatterContext creates client variables from entity for matter context
func (s *variableService) generateClientVariablesFromEntityForMatterContext(entityClient *entity.Generic) []VariableInfo {
	return []VariableInfo{
		{
			Name:        "client.id",
			Value:       uint64(entityClient.ID),
			Description: "Client ID",
			Category:    "client",
		},
		{
			Name:        "client.name",
			Value:       entityClient.Name,
			Description: "Client name",
			Category:    "client",
		},
		{
			Name:        "client.short_name",
			Value:       entityClient.ShortName,
			Description: "Client short name",
			Category:    "client",
		},
		{
			Name:        "client.code",
			Value:       entityClient.Code,
			Description: "Client code",
			Category:    "client",
		},
	}
}

// normalizeMatterObject converts matter object to map for field access
func (s *variableService) normalizeMatterObject(matterObject interface{}) map[string]interface{} {
	normalized := make(map[string]interface{})

	// Convert matter object to map using JSON marshal/unmarshal (same pattern as client)
	jsonBytes, err := json.Marshal(matterObject)
	if err != nil {
		return normalized
	}

	var matterMap map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &matterMap); err != nil {
		return normalized
	}

	// Copy all original fields
	for key, value := range matterMap {
		normalized[key] = value
	}

	return normalized
}
