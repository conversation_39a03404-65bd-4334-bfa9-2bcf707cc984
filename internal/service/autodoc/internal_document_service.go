package autodoc

import (
	"context"
	"fmt"
	"mime"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/transport"

	cloud0ginext "gitlab.com/goxp/cloud0/ginext"
)

// internalDocumentService implements DocumentService using internal file management
type internalDocumentService struct {
	documentRepo repositories.DocumentRepository
	glob         transport.Glob
}

// NewInternalDocumentService creates a new internal document service
func NewInternalDocumentService(documentRepo repositories.DocumentRepository, glob transport.Glob) DocumentService {
	return &internalDocumentService{
		documentRepo: documentRepo,
		glob:         glob,
	}
}

// CreateDocument creates a new document in the internal system
func (s *internalDocumentService) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Creating document via internal service", map[string]interface{}{
		"name":      req.Name,
		"parent_id": req.ParentID,
		"tenant_id": req.TenantID,
		"doc_type":  req.DocType,
	})

	// Create document model
	document := &model.Document{
		ParentID:    req.ParentID,
		Name:        req.Name,
		TenantID:    req.TenantID,
		ObjectType:  req.ObjectType,
		ObjectID:    req.ObjectID,
		DocType:     req.DocType,
		CreatedUser: req.CreatedUser,
		UpdatedUser: req.CreatedUser,
		Key:         req.Key, // Store file content key if provided
		Status:      1,       // active
		Model: model.Model{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// Save document to database
	err := s.documentRepo.CreateDoc(ctx, document)
	if err != nil {
		log.WithError(err).Errorf("Failed to create document name=%s", req.Name)
		return nil, fmt.Errorf("failed to create document: %w", err)
	}

	log.Info("Document created successfully", map[string]interface{}{
		"document_id": document.ID,
		"name":        document.Name,
	})

	return document, nil
}

// GetDocument retrieves a document by ID
func (s *internalDocumentService) GetDocument(ctx context.Context, documentID uint64, tenantID uint64) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("document_id", documentID).WithField("tenant_id", tenantID).Debug("Getting document")

	// Build query with tenant isolation
	filters := []*model.Filter{
		{
			Key:    "id",
			Method: "=",
			Value:  documentID,
		},
		{
			Key:    "tenant_id",
			Method: "=",
			Value:  tenantID,
		},
	}

	query := &model.Query{
		Filters: filters,
	}
	document, err := s.documentRepo.FindOne(ctx, query)
	if err != nil {
		log.WithError(err).Errorf("Failed to get document document_id=%d tenant_id=%d", documentID, tenantID)
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	return document, nil
}

// UpdateDocument updates an existing document
func (s *internalDocumentService) UpdateDocument(ctx context.Context, req *UpdateDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Updating document", map[string]interface{}{
		"document_id": req.ID,
		"tenant_id":   req.TenantID,
		"name":        req.Name,
	})

	// Get existing document with tenant isolation
	document, err := s.GetDocument(ctx, req.ID, req.TenantID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Name != "" {
		document.Name = req.Name
	}
	document.UpdatedUser = req.UpdatedUser
	document.UpdatedAt = time.Now()

	// Update document in database
	query := &model.Query{
		Filters: []*model.Filter{
			{Key: "id", Method: "=", Value: req.ID},
			{Key: "tenant_id", Method: "=", Value: req.TenantID},
		},
	}
	err = s.documentRepo.UpdateOne(ctx, query, document)
	if err != nil {
		log.WithError(err).Errorf("Failed to update document document_id=%d", req.ID)
		return nil, fmt.Errorf("failed to update document: %w", err)
	}

	log.Info("Document updated successfully", map[string]interface{}{
		"document_id": document.ID,
		"name":        document.Name,
	})

	return document, nil
}

// DeleteDocument deletes a document
func (s *internalDocumentService) DeleteDocument(ctx context.Context, documentID uint64, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Deleting document", map[string]interface{}{
		"document_id": documentID,
		"tenant_id":   tenantID,
	})

	// Get document first to check tenant
	_, err := s.GetDocument(ctx, documentID, tenantID)
	if err != nil {
		return err
	}

	// Delete document from database
	query := &model.Query{
		Filters: []*model.Filter{
			{Key: "id", Method: "=", Value: documentID},
			{Key: "tenant_id", Method: "=", Value: tenantID},
		},
	}
	err = s.documentRepo.DeleteOne(ctx, query)
	if err != nil {
		log.WithError(err).Errorf("Failed to delete document document_id=%d", documentID)
		return fmt.Errorf("failed to delete document: %w", err)
	}

	log.Info("Document deleted successfully", map[string]interface{}{
		"document_id": documentID,
	})

	return nil
}

// CopyDocument copies a document to a new location
func (s *internalDocumentService) CopyDocument(ctx context.Context, req *CopyDocumentRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Info("Copying document", map[string]interface{}{
		"source_id":        req.SourceID,
		"target_parent_id": req.TargetParentID,
		"new_name":         req.NewName,
		"tenant_id":        req.TenantID,
	})

	// Get source document with tenant isolation
	sourceDoc, err := s.GetDocument(ctx, req.SourceID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source document: %w", err)
	}

	// Create new document
	createReq := &CreateDocumentRequest{
		Name:        req.NewName,
		ParentID:    req.TargetParentID,
		TenantID:    req.TenantID,
		ObjectType:  sourceDoc.ObjectType,
		ObjectID:    sourceDoc.ObjectID,
		DocType:     sourceDoc.DocType,
		CreatedUser: req.CreatedUser,
	}

	newDoc, err := s.CreateDocument(ctx, createReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create copied document: %w", err)
	}

	log.Info("Document copied successfully", map[string]interface{}{
		"source_id": req.SourceID,
		"new_id":    newDoc.ID,
		"new_name":  newDoc.Name,
	})

	return newDoc, nil
}

// GetDocumentContent retrieves the content of a document
func (s *internalDocumentService) GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("document_id", documentID).WithField("tenant_id", tenantID).Debug("Getting document content")

	// Get document to get key
	document, err := s.GetDocument(ctx, documentID, tenantID)
	if err != nil {
		return nil, err
	}

	if document.Key == "" {
		return nil, fmt.Errorf("document has no content key")
	}

	// Get content from glob
	content, err := s.getDocumentContent(ctx, document.Key, tenantID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get document content key=%s", document.Key)
		return nil, fmt.Errorf("failed to get document content: %w", err)
	}

	return content, nil
}

// getDocumentContent retrieves content from glob storage
func (s *internalDocumentService) getDocumentContent(ctx context.Context, key string, tenantID uint64) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("key", key).WithField("tenant_id", tenantID).Debug("Getting document content from glob")

	// Use tenantID as string for userID since this is internal system operation
	userID := fmt.Sprintf("system_%d", tenantID)

	// Get content from glob storage
	content, err := s.glob.GetFileContent(ctx, key, userID)
	if err != nil {
		log.WithError(err).Error("Failed to get content from glob storage")
		return nil, fmt.Errorf("failed to get content from glob storage: %w", err)
	}

	log.WithField("key", key).WithField("tenant_id", tenantID).WithField("content_size", len(content)).Debug("Successfully retrieved document content from glob")

	return content, nil
}

// EnsureAutoDocRoot ensures AutoDoc root folder exists for tenant
func (s *internalDocumentService) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Search for existing AutoDocRoot
	filters := []*model.Filter{
		{Key: "tenant_id", Method: "=", Value: tenantID},
		{Key: "name", Method: "=", Value: model.AutoDocRootFolderName},
		{Key: "doc_type", Method: "=", Value: model.DocTypeDir},
		{Key: "parent_id", Method: "=", Value: 0},
	}

	query := &model.Query{Filters: filters}
	existingRoot, err := s.documentRepo.FindOne(ctx, query)
	if err == nil && existingRoot != nil {
		log.WithField("document_id", existingRoot.ID).Debug("Found existing AutoDocRoot")
		return existingRoot, nil
	}

	// Create AutoDocRoot if not exists
	autoDocRoot := &model.Document{
		ParentID:    0,
		Name:        model.AutoDocRootFolderName,
		TenantID:    tenantID,
		ObjectType:  0,
		ObjectID:    0,
		DocType:     model.DocTypeDir,
		CreatedUser: 1, // System user
		UpdatedUser: 1, // System user
		Status:      1, // Active
		Model: model.Model{
			CreatedAt: time.Now(),
		},
	}

	if err := s.documentRepo.CreateDoc(ctx, autoDocRoot); err != nil {
		log.WithError(err).Errorf("Failed to create AutoDocRoot tenant_id=%d", tenantID)
		return nil, fmt.Errorf("failed to create AutoDocRoot: %w", err)
	}

	log.WithField("document_id", autoDocRoot.ID).Info("Created AutoDocRoot")
	return autoDocRoot, nil
}

// CreateFolder creates a new folder at the specified path
func (s *internalDocumentService) CreateFolder(ctx context.Context, req *CreateFolderRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("name", req.Name).WithField("path", req.Path).WithField("tenant_id", req.TenantID).Info("Creating folder")

	// If path is provided, create nested folders using os.MkdirAll
	if req.Path != "" {
		err := s.createFolderPath(ctx, req.Path, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to create folder path: %w", err)
		}

		// Resolve parent ID from path after creation
		parentID, err := s.resolvePathToParentID(ctx, req.Path, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve path: %w", err)
		}
		req.ParentID = parentID
	}

	// Create document request for folder
	createReq := &CreateDocumentRequest{
		Name:        req.Name,
		ParentID:    req.ParentID,
		TenantID:    req.TenantID,
		ObjectType:  req.ObjectType,
		ObjectID:    req.ObjectID,
		DocType:     model.DocTypeDir, // Folder type
		CreatedUser: req.CreatedUser,
	}

	// Create folder document in database
	doc, err := s.CreateDocument(ctx, createReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create folder document: %w", err)
	}

	// Create physical folder if needed (for file system operations)
	physicalPath := s.getPhysicalPath(ctx, doc.ID, req.TenantID)
	err = os.MkdirAll(physicalPath, 0755)
	if err != nil {
		log.WithError(err).WithField("physical_path", physicalPath).Warn("Failed to create physical folder")
		// Don't fail the operation as the database record is already created
	} else {
		log.WithField("physical_path", physicalPath).Info("Created physical folder")
	}

	return doc, nil
}

// CreateFolderWithResponse creates a folder and returns formatted response
func (s *internalDocumentService) CreateFolderWithResponse(ctx context.Context, req *CreateFolderRequest) (*CreateFolderResponse, error) {
	// Create folder using existing method
	doc, err := s.CreateFolder(ctx, req)
	if err != nil {
		return nil, err
	}

	// Build folder path
	folderPath, err := s.GetFilePath(ctx, doc.ID, req.TenantID)
	if err != nil {
		// If path resolution fails, use folder name as fallback
		folderPath = doc.Name
	}

	// Return formatted response
	return &CreateFolderResponse{
		ID:         doc.ID,
		Name:       doc.Name,
		ParentID:   doc.ParentID,
		ExternalID: "", // Internal provider doesn't have external ID
		Path:       folderPath,
		TenantID:   doc.TenantID,
	}, nil
}

// MoveFile moves a file to a new location using atomic operations
func (s *internalDocumentService) MoveFile(ctx context.Context, req *MoveFileRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("new_parent_id", req.NewParentID).WithField("tenant_id", req.TenantID).Info("Moving file")

	// Get existing document
	doc, err := s.GetDocument(ctx, req.FileID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Resolve new parent ID if path is provided
	newParentID := req.NewParentID
	if req.NewPath != "" {
		parentID, err := s.resolvePathToParentID(ctx, req.NewPath, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve new path: %w", err)
		}
		newParentID = parentID
	}

	// Determine new name
	newName := req.NewName
	if newName == "" {
		newName = doc.Name
	}

	// If moving to different parent or renaming, use atomic move operation
	if (newParentID != 0 && newParentID != doc.ParentID) || newName != doc.Name {
		return s.atomicMoveFile(ctx, doc, newParentID, newName, req.UpdatedUser, req.PreserveLinks)
	}

	// No changes needed
	return doc, nil
}

// CopyFile copies a file to a destination path with proper error handling
func (s *internalDocumentService) CopyFile(ctx context.Context, req *CopyFileRequest) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("source_id", req.SourceID).WithField("dest_parent_id", req.DestParentID).WithField("tenant_id", req.TenantID).Info("Copying file")

	// Get source document
	sourceDoc, err := s.GetDocument(ctx, req.SourceID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source document: %w", err)
	}

	// Resolve destination parent ID if path is provided
	destParentID := req.DestParentID
	if req.DestPath != "" {
		parentID, err := s.resolvePathToParentID(ctx, req.DestPath, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve destination path: %w", err)
		}
		destParentID = parentID
	}

	// Perform atomic copy operation
	return s.atomicCopyFile(ctx, sourceDoc, destParentID, req.NewName, req.CreatedUser, req.CopyContent, req.CopyMetadata)
}

// GetFileMetadata retrieves detailed metadata for a file including file system info
func (s *internalDocumentService) GetFileMetadata(ctx context.Context, req *GetFileMetadataRequest) (*FileMetadata, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("tenant_id", req.TenantID).Info("Getting file metadata")

	// Get document
	doc, err := s.GetDocument(ctx, req.FileID, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Get file path
	path, err := s.GetFilePath(ctx, req.FileID, req.TenantID)
	if err != nil {
		log.WithError(err).Warn("Failed to get file path")
		path = doc.Name // Fallback to just the name
	}

	// Build metadata
	metadata := &FileMetadata{
		ID:          doc.ID,
		Name:        doc.Name,
		Path:        path,
		Size:        doc.Size,
		DocType:     doc.DocType,
		CreatedAt:   doc.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   doc.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		CreatedUser: doc.CreatedUser,
		UpdatedUser: doc.UpdatedUser,
		TenantID:    doc.TenantID,
		ObjectType:  doc.ObjectType,
		ObjectID:    doc.ObjectID,
		ParentID:    doc.ParentID,
	}

	// Add file extension and mime type for files
	if doc.DocType == model.DocTypeFile {
		metadata.Extension = filepath.Ext(doc.Name)
		metadata.MimeType = mime.TypeByExtension(metadata.Extension)

		// Get file system metadata if physical file exists
		if doc.Key != "" {
			physicalPath := s.getPhysicalPathFromKey(ctx, doc.Key, req.TenantID)
			if fileInfo, err := os.Stat(physicalPath); err == nil {
				metadata.Size = fileInfo.Size()
				metadata.Metadata = map[string]interface{}{
					"file_mode":     fileInfo.Mode().String(),
					"is_dir":        fileInfo.IsDir(),
					"mod_time":      fileInfo.ModTime().Format("2006-01-02T15:04:05Z07:00"),
					"physical_path": physicalPath,
				}

				// Calculate checksum for files
				if checksum, err := s.calculateFileChecksum(ctx, physicalPath); err == nil {
					metadata.Checksum = checksum
				}
			}
		}
	}

	return metadata, nil
}

// SetFilePermissions sets permissions for a file
func (s *internalDocumentService) SetFilePermissions(ctx context.Context, req *SetFilePermissionsRequest) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", req.FileID).WithField("tenant_id", req.TenantID).Info("Setting file permissions")

	// For internal document service, permissions are not directly supported
	// This would need to be implemented based on the specific permission system
	// For now, we'll just log and return success
	log.WithField("permissions", req.Permissions).Info("File permissions set (internal implementation)")

	return nil
}

// ListFiles lists files in a directory with filtering options
func (s *internalDocumentService) ListFiles(ctx context.Context, req *ListFilesRequest) (*ListFilesResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("parent_id", req.ParentID).WithField("tenant_id", req.TenantID).Info("Listing files")

	// Build filters
	filters := []*model.Filter{
		{Key: "tenant_id", Method: "=", Value: req.TenantID},
	}

	// Add parent ID filter
	if req.ParentID != 0 {
		filters = append(filters, &model.Filter{Key: "parent_id", Method: "=", Value: req.ParentID})
	} else if req.Path != "" {
		// Resolve path to parent ID
		parentID, err := s.resolvePathToParentID(ctx, req.Path, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve path: %w", err)
		}
		filters = append(filters, &model.Filter{Key: "parent_id", Method: "=", Value: parentID})
	} else {
		// No parent ID or path specified - use AutoDocRoot as default
		autoDocRoot, err := s.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure AutoDocRoot: %w", err)
		}
		filters = append(filters, &model.Filter{Key: "parent_id", Method: "=", Value: autoDocRoot.ID})
		log.WithField("autodoc_root_id", autoDocRoot.ID).Info("Using AutoDocRoot as default parent for listing")
	}

	// Add file type filter
	if req.FileType != 0 {
		filters = append(filters, &model.Filter{Key: "doc_type", Method: "=", Value: req.FileType})
	}

	// Set pagination defaults
	page := req.Page
	if page <= 0 {
		page = 1
	}
	limit := req.Limit
	if limit <= 0 {
		limit = 50
	}

	// Build query with pagination
	query := &model.Query{
		Filters: filters,
		Pagination: &model.Pagination{
			Page:   page,
			Limit:  limit,
			Offset: (page - 1) * limit,
		},
	}

	// Build pager for sorting
	pager := &cloud0ginext.Pager{
		Page: page,
	}

	// Add sorting
	if req.SortBy != "" {
		sortOrder := ""
		if req.SortOrder == "desc" {
			sortOrder = "-"
		}
		pager.Sort = sortOrder + req.SortBy
	} else {
		pager.Sort = "name" // Default sort by name
	}

	// Get documents
	documents, err := s.documentRepo.Find(ctx, query, pager)
	if err != nil {
		log.WithError(err).Error("Failed to list files")
		return nil, fmt.Errorf("failed to list files: %w", err)
	}

	// Get total count for pagination using List method (no pagination)
	countQuery := &model.Query{Filters: filters}
	allDocuments, err := s.documentRepo.List(ctx, countQuery)
	totalCount := int64(0)
	if err != nil {
		log.WithError(err).Warn("Failed to get total count")
		totalCount = int64(len(documents))
	} else {
		totalCount = int64(len(allDocuments))
	}

	// Handle recursive listing if requested
	if req.Recursive && len(documents) > 0 {
		documents, err = s.getRecursiveFiles(ctx, documents, req)
		if err != nil {
			log.WithError(err).Warn("Failed to get recursive files")
		}
	}

	// Get current working directory info
	var cwd *model.Document
	if req.ParentID != 0 {
		// Get parent folder info
		parentQuery := &model.Query{
			Filters: []*model.Filter{
				{Key: "id", Method: "=", Value: req.ParentID},
				{Key: "tenant_id", Method: "=", Value: req.TenantID},
			},
		}
		parentDoc, err := s.documentRepo.FindOne(ctx, parentQuery)
		if err == nil {
			cwd = parentDoc
		}
	} else {
		// Use AutoDocRoot as cwd
		autoDocRoot, err := s.EnsureAutoDocRoot(ctx, req.TenantID)
		if err == nil {
			cwd = autoDocRoot
		}
	}

	return &ListFilesResponse{
		Cwd:        ConvertModelToResponse(cwd),
		Data:       ConvertModelsToResponses(documents),
		TotalCount: totalCount,
		Page:       page,
		Limit:      limit,
		HasMore:    totalCount > int64(page*limit),
	}, nil
}

// SearchFiles searches for files based on criteria
func (s *internalDocumentService) SearchFiles(ctx context.Context, req *SearchFilesRequest) (*SearchFilesResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("query", req.Query).WithField("tenant_id", req.TenantID).Info("Searching files")

	// Build search filters
	filters := []*model.Filter{
		{Key: "tenant_id", Method: "=", Value: req.TenantID},
	}

	// Add parent ID filter if specified
	if req.ParentID != 0 {
		filters = append(filters, &model.Filter{Key: "parent_id", Method: "=", Value: req.ParentID})
	}

	// Add file type filter if specified
	if req.FileType != 0 {
		filters = append(filters, &model.Filter{Key: "doc_type", Method: "=", Value: req.FileType})
	}

	// Add size filters if specified
	if req.MinSize > 0 {
		filters = append(filters, &model.Filter{Key: "size", Method: ">=", Value: req.MinSize})
	}
	if req.MaxSize > 0 {
		filters = append(filters, &model.Filter{Key: "size", Method: "<=", Value: req.MaxSize})
	}

	// Set pagination defaults
	page := req.Page
	if page <= 0 {
		page = 1
	}
	limit := req.Limit
	if limit <= 0 {
		limit = 50
	}

	// Build query with search term and pagination
	query := &model.Query{
		Q:       req.Query, // Search term
		Filters: filters,
		Pagination: &model.Pagination{
			Page:   page,
			Limit:  limit,
			Offset: (page - 1) * limit,
		},
	}

	// Build pager
	pager := &cloud0ginext.Pager{
		Page: page,
		Sort: "name", // Default sort by name
	}

	// Get documents
	documents, err := s.documentRepo.Find(ctx, query, pager)
	if err != nil {
		log.WithError(err).Error("Failed to search files")
		return nil, fmt.Errorf("failed to search files: %w", err)
	}

	// Get total count
	countQuery := &model.Query{Q: req.Query, Filters: filters}
	allDocuments, err := s.documentRepo.List(ctx, countQuery)
	totalCount := int64(0)
	if err != nil {
		log.WithError(err).Warn("Failed to get search total count")
		totalCount = int64(len(documents))
	} else {
		totalCount = int64(len(allDocuments))
	}

	// Get current working directory info for search
	var cwd *model.Document
	if req.ParentID != 0 {
		// Get parent folder info
		parentQuery := &model.Query{
			Filters: []*model.Filter{
				{Key: "id", Method: "=", Value: req.ParentID},
				{Key: "tenant_id", Method: "=", Value: req.TenantID},
			},
		}
		parentDoc, err := s.documentRepo.FindOne(ctx, parentQuery)
		if err == nil {
			cwd = parentDoc
		}
	} else {
		// Use AutoDocRoot as cwd for global search
		autoDocRoot, err := s.EnsureAutoDocRoot(ctx, req.TenantID)
		if err == nil {
			cwd = autoDocRoot
		}
	}

	return &SearchFilesResponse{
		Cwd:        ConvertModelToResponse(cwd),
		Data:       ConvertModelsToResponses(documents),
		TotalCount: totalCount,
		Page:       page,
		Limit:      limit,
		HasMore:    totalCount > int64(page*limit),
		Query:      req.Query,
	}, nil
}

// GetFilePath gets the full path of a file
func (s *internalDocumentService) GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("file_id", fileID).WithField("tenant_id", tenantID).Debug("Getting file path")

	// Get document
	doc, err := s.GetDocument(ctx, fileID, tenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get document: %w", err)
	}

	// Build path by traversing up the parent hierarchy
	path := doc.Name
	currentDoc := doc

	for currentDoc.ParentID != 0 {
		parent, err := s.GetDocument(ctx, currentDoc.ParentID, tenantID)
		if err != nil {
			log.WithError(err).WithField("parent_id", currentDoc.ParentID).Warn("Failed to get parent document")
			break
		}
		path = parent.Name + "/" + path
		currentDoc = parent
	}

	return "/" + path, nil
}

// ValidatePath validates if a path is valid and accessible
func (s *internalDocumentService) ValidatePath(ctx context.Context, path string, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("path", path).WithField("tenant_id", tenantID).Debug("Validating path")

	// Basic path validation
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// Try to resolve path to see if it exists
	_, err := s.resolvePathToParentID(ctx, path, tenantID)
	if err != nil {
		return fmt.Errorf("invalid path: %w", err)
	}

	return nil
}

// Helper method to resolve path to parent ID
func (s *internalDocumentService) resolvePathToParentID(ctx context.Context, path string, tenantID uint64) (uint64, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("path", path).WithField("tenant_id", tenantID).Debug("Resolving path to parent ID")

	// Clean and split path
	path = strings.Trim(path, "/")
	if path == "" {
		return 0, nil // Root path
	}

	parts := strings.Split(path, "/")
	currentParentID := uint64(0)

	// Traverse path parts to find the final parent
	for _, part := range parts {
		if part == "" {
			continue
		}

		// Find document with this name under current parent
		filters := []*model.Filter{
			{Key: "tenant_id", Method: "=", Value: tenantID},
			{Key: "name", Method: "=", Value: part},
			{Key: "parent_id", Method: "=", Value: currentParentID},
			{Key: "doc_type", Method: "=", Value: model.DocTypeDir}, // Only folders
		}

		query := &model.Query{Filters: filters}
		doc, err := s.documentRepo.FindOne(ctx, query)
		if err != nil {
			return 0, fmt.Errorf("path part '%s' not found: %w", part, err)
		}
		if doc == nil {
			return 0, fmt.Errorf("path part '%s' not found", part)
		}

		currentParentID = doc.ID
	}

	return currentParentID, nil
}



// createFolderPath creates nested folders from path using os.MkdirAll
func (s *internalDocumentService) createFolderPath(ctx context.Context, path string, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("path", path).WithField("tenant_id", tenantID).Debug("Creating folder path")

	// Clean and split path
	path = strings.Trim(path, "/")
	if path == "" {
		return nil // Root path, nothing to create
	}

	parts := strings.Split(path, "/")
	currentParentID := uint64(0)

	// Create each folder in the path if it doesn't exist
	for _, part := range parts {
		if part == "" {
			continue
		}

		// Check if folder already exists
		filters := []*model.Filter{
			{Key: "tenant_id", Method: "=", Value: tenantID},
			{Key: "name", Method: "=", Value: part},
			{Key: "parent_id", Method: "=", Value: currentParentID},
			{Key: "doc_type", Method: "=", Value: model.DocTypeDir},
		}

		query := &model.Query{Filters: filters}
		existingDoc, err := s.documentRepo.FindOne(ctx, query)
		if err != nil {
			return fmt.Errorf("failed to check existing folder '%s': %w", part, err)
		}

		if existingDoc != nil {
			// Folder exists, use its ID as parent for next level
			currentParentID = existingDoc.ID
		} else {
			// Create new folder
			createReq := &CreateDocumentRequest{
				Name:        part,
				ParentID:    currentParentID,
				TenantID:    tenantID,
				DocType:     model.DocTypeDir,
				CreatedUser: 1, // System user
			}

			newDoc, err := s.CreateDocument(ctx, createReq)
			if err != nil {
				return fmt.Errorf("failed to create folder '%s': %w", part, err)
			}

			currentParentID = newDoc.ID
		}
	}

	return nil
}

// getPhysicalPath returns the physical file system path for a document
func (s *internalDocumentService) getPhysicalPath(ctx context.Context, documentID uint64, tenantID uint64) string {
	// Create tenant-specific directory structure
	return filepath.Join("/tmp", "docman", fmt.Sprintf("tenant_%d", tenantID), fmt.Sprintf("doc_%d", documentID))
}

// getPhysicalPathFromKey returns the physical path from a glob key
func (s *internalDocumentService) getPhysicalPathFromKey(ctx context.Context, key string, tenantID uint64) string {
	// For glob keys, we might need to map them to physical paths
	// This is a simplified implementation
	return filepath.Join("/tmp", "docman", fmt.Sprintf("tenant_%d", tenantID), "files", key)
}

// calculateFileChecksum calculates SHA256 checksum of a file
func (s *internalDocumentService) calculateFileChecksum(ctx context.Context, filePath string) (string, error) {
	// This would implement SHA256 checksum calculation
	// For now, return a placeholder
	return "sha256:placeholder", nil
}

// atomicMoveFile performs atomic file move operation
func (s *internalDocumentService) atomicMoveFile(ctx context.Context, doc *model.Document, newParentID uint64, newName string, updatedUser uint64, preserveLinks bool) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("document_id", doc.ID).WithField("new_parent_id", newParentID).WithField("new_name", newName).Info("Performing atomic file move")

	// Get current physical path
	oldPhysicalPath := s.getPhysicalPath(ctx, doc.ID, doc.TenantID)

	// Create new document with new location
	createReq := &CreateDocumentRequest{
		Name:        newName,
		ParentID:    newParentID,
		TenantID:    doc.TenantID,
		ObjectType:  doc.ObjectType,
		ObjectID:    doc.ObjectID,
		DocType:     doc.DocType,
		CreatedUser: updatedUser,
	}

	// Create new document
	newDoc, err := s.CreateDocument(ctx, createReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create document at new location: %w", err)
	}

	// Move physical file if it exists
	newPhysicalPath := s.getPhysicalPath(ctx, newDoc.ID, doc.TenantID)
	if _, err := os.Stat(oldPhysicalPath); err == nil {
		// Ensure destination directory exists
		if err := os.MkdirAll(filepath.Dir(newPhysicalPath), 0755); err != nil {
			log.WithError(err).Warn("Failed to create destination directory")
		} else {
			// Attempt atomic move using os.Rename
			if err := os.Rename(oldPhysicalPath, newPhysicalPath); err != nil {
				log.WithError(err).Warn("Failed to move physical file")
			}
		}
	}

	// Delete old document record
	err = s.DeleteDocument(ctx, doc.ID, doc.TenantID)
	if err != nil {
		log.WithError(err).Warn("Failed to delete old document record after move")
		// Don't fail the operation as new document is already created
	}

	return newDoc, nil
}

// atomicCopyFile performs atomic file copy operation
func (s *internalDocumentService) atomicCopyFile(ctx context.Context, sourceDoc *model.Document, destParentID uint64, newName string, createdUser uint64, copyContent, copyMetadata bool) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.WithField("source_id", sourceDoc.ID).WithField("dest_parent_id", destParentID).WithField("new_name", newName).Info("Performing atomic file copy")

	// Create new document
	createReq := &CreateDocumentRequest{
		Name:        newName,
		ParentID:    destParentID,
		TenantID:    sourceDoc.TenantID,
		ObjectType:  sourceDoc.ObjectType,
		ObjectID:    sourceDoc.ObjectID,
		DocType:     sourceDoc.DocType,
		CreatedUser: createdUser,
	}

	// Create new document
	newDoc, err := s.CreateDocument(ctx, createReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create copied document: %w", err)
	}

	return newDoc, nil
}

// getRecursiveFiles recursively gets all files in subdirectories
func (s *internalDocumentService) getRecursiveFiles(ctx context.Context, documents []*model.Document, req *ListFilesRequest) ([]*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)
	log.Debug("Getting recursive files")

	allFiles := make([]*model.Document, 0, len(documents))
	allFiles = append(allFiles, documents...)

	// Find all folders in the current list
	for _, doc := range documents {
		if doc.DocType == model.DocTypeDir {
			// Recursively get files from this folder
			subReq := &ListFilesRequest{
				ParentID:  doc.ID,
				TenantID:  req.TenantID,
				FileType:  req.FileType,
				Recursive: true, // Keep recursive flag
				Page:      1,
				Limit:     1000, // Large limit for recursive calls
				SortBy:    req.SortBy,
				SortOrder: req.SortOrder,
			}

			subResponse, err := s.ListFiles(ctx, subReq)
			if err != nil {
				log.WithError(err).WithField("folder_id", doc.ID).Warn("Failed to get files from subfolder")
				continue
			}

			// Convert DocumentResponse back to model.Document for internal processing
			for _, docResp := range subResponse.Data {
				if modelDoc := s.convertResponseToModel(docResp); modelDoc != nil {
					allFiles = append(allFiles, modelDoc)
				}
			}
		}
	}

	return allFiles, nil
}

// convertResponseToModel converts DocumentResponse back to model.Document (for internal use)
func (s *internalDocumentService) convertResponseToModel(resp *DocumentResponse) *model.Document {
	if resp == nil {
		return nil
	}

	// Parse ID back to uint64
	var id uint64
	if resp.ID != "" {
		if parsedID, err := strconv.ParseUint(resp.ID, 10, 64); err == nil {
			id = parsedID
		}
	}

	// Parse ParentID back to uint64
	var parentID uint64
	if resp.ParentID != "" {
		if parsedParentID, err := strconv.ParseUint(resp.ParentID, 10, 64); err == nil {
			parentID = parsedParentID
		}
	}

	return &model.Document{
		Model: model.Model{
			ID: id,
		},
		ParentID:    parentID,
		Name:        resp.Name,
		TenantID:    resp.TenantID,
		ObjectType:  resp.ObjectType,
		ObjectID:    resp.ObjectID,
		Key:         resp.Key,
		SubObjectID: resp.SubObjectID,
		Type:        resp.Type,
		DocType:     resp.DocType,
		Status:      resp.Status,
		Note:        resp.Note,
		CreatedUser: resp.CreatedUser,
		UpdatedUser: resp.UpdatedUser,
		Size:        resp.Size,
	}
}

// GetDocumentByExternalID retrieves a document by external ID (not supported for internal provider)
func (s *internalDocumentService) GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error) {
	return nil, fmt.Errorf("GetDocumentByExternalID not supported for internal document service - use GetDocument with internal ID instead")
}

// DeleteDocumentByExternalID deletes a document by external ID (not supported for internal provider)
func (s *internalDocumentService) DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error {
	return fmt.Errorf("DeleteDocumentByExternalID not supported for internal document service - use DeleteDocument with internal ID instead")
}
