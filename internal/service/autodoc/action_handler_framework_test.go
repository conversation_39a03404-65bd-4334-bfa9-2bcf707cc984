package autodoc_test

import (
	"context"
	"testing"

	"bilabl/docman/internal/service/autodoc"

	"github.com/stretchr/testify/assert"
)

// TestActionHandlerFramework verifies the action handler framework implementation
func TestActionHandlerFramework(t *testing.T) {
	t.Run("ActionHandlerRegistry_Creation", func(t *testing.T) {
		// Verify ActionHandlerRegistry can be created
		registry := autodoc.NewActionHandlerRegistry()
		assert.NotNil(t, registry)

		// Verify supported actions initially empty
		actions := registry.GetSupportedActions()
		assert.NotNil(t, actions)
		assert.Len(t, actions, 0)
	})

	t.Run("RuleExecutionEngine_WithActionHandlers", func(t *testing.T) {
		// Verify RuleExecutionEngine can be created with action handler registry
		engine := autodoc.NewRuleExecutionEngine(nil, nil, nil, nil, nil, nil)
		assert.NotNil(t, engine)

		// This verifies the constructor properly initializes action handlers
		// and registry without panicking
	})

	t.Run("PlaceholderExtraction_Basic", func(t *testing.T) {
		engine := autodoc.NewRuleExecutionEngine(nil, nil, nil, nil, nil, nil)

		// Test basic placeholder extraction with actual event structure
		eventData := map[string]interface{}{
			"topic": "client.update",
			"body": map[string]interface{}{
				"id":        float64(123),
				"name":      "Test Client",
				"tenant_id": float64(1),
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"stage_text": "initial",
					},
				},
			},
		}

		result := engine.ExtractPlaceholderData(context.Background(), eventData)

		// Verify original event data is preserved
		assert.Equal(t, "client.update", result["topic"])
		assert.NotNil(t, result["body"])

		// Verify client id is added for PathResolver to use
		assert.Equal(t, float64(123), result["id"])
		assert.NotEmpty(t, result["timestamp"])
		assert.NotEmpty(t, result["date"])
	})

	t.Run("PlaceholderReplacement_Basic", func(t *testing.T) {
		engine := autodoc.NewRuleExecutionEngine(nil, nil, nil, nil, nil, nil)

		// Test both simple and nested placeholder replacement
		template := "{client_folder}/Documents/{body.name} - {body.extra.current.stage_text} - {date}"
		placeholderData := map[string]interface{}{
			"client_folder": "id:123",
			"date":          "2024-01-15",
			"body": map[string]interface{}{
				"name": "ABC Corp",
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"stage_text": "initial",
					},
				},
			},
		}

		result := engine.ReplacePlaceholders(context.Background(), template, placeholderData)
		assert.Equal(t, "id:123/Documents/ABC Corp - initial - 2024-01-15", result)
	})
}

// TestActionHandlerTypes verifies all action handler types are implemented
func TestActionHandlerTypes(t *testing.T) {
	t.Run("CopyFileHandler_Creation", func(t *testing.T) {
		handler := autodoc.NewCopyFileHandler(nil, nil, nil, nil, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "copy_file", handler.GetActionType())
	})

	t.Run("CopyFolderHandler_Creation", func(t *testing.T) {
		handler := autodoc.NewCopyFolderHandler(nil, nil, nil, nil, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "copy_folder", handler.GetActionType())
	})

	t.Run("GenerateDocumentHandler_Creation", func(t *testing.T) {
		handler := autodoc.NewGenerateDocumentHandler(nil, nil, nil, nil)
		assert.NotNil(t, handler)
		assert.Equal(t, "generate_document", handler.GetActionType())
	})
}

// TestActionHandlerExtensibility verifies the extensibility framework
func TestActionHandlerExtensibility(t *testing.T) {
	t.Run("BaseActionHandler_Functionality", func(t *testing.T) {
		base := autodoc.NewBaseActionHandler("test_action")
		assert.NotNil(t, base)
		assert.Equal(t, "test_action", base.GetActionType())
	})

	t.Run("ActionExecutionParams_Structure", func(t *testing.T) {
		// Verify ActionExecutionParams struct exists and can be created
		params := &autodoc.ActionExecutionParams{
			TenantID:        123,
			PlaceholderData: map[string]interface{}{},
			RuleID:          1,
		}
		assert.NotNil(t, params)
		assert.Equal(t, uint64(123), params.TenantID)
		assert.Equal(t, uint64(1), params.RuleID)
	})
}
