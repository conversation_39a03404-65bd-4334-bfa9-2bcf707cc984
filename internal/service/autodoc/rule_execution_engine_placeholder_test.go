package autodoc

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExtractPlaceholderData(t *testing.T) {
	engine := &ruleExecutionEngine{}
	ctx := context.Background()

	t.Run("client update event", func(t *testing.T) {
		eventData := map[string]interface{}{
			"topic": "client.update",
			"body": map[string]interface{}{
				"id":         float64(10219),
				"name":       "acclime Vietnam",
				"short_name": "ACCLIME",
				"code":       "C06855",
				"tenant_id":  float64(1),
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"name":       "acclime Vietnam",
						"stage_text": "initial",
						"owners":     []interface{}{float64(23)},
					},
					"old": map[string]interface{}{
						"stage_text": "lead",
					},
				},
			},
		}

		placeholders := engine.ExtractPlaceholderData(ctx, eventData)

		// Should contain original event data
		assert.Equal(t, "client.update", placeholders["topic"])
		assert.NotNil(t, placeholders["body"])

		// Should contain client id for PathResolver to use
		assert.Equal(t, float64(10219), placeholders["id"])

		// Should contain timestamp placeholders
		assert.NotEmpty(t, placeholders["timestamp"])
		assert.NotEmpty(t, placeholders["date"])
	})

	t.Run("matter create event", func(t *testing.T) {
		eventData := map[string]interface{}{
			"topic": "matter.create",
			"body": map[string]interface{}{
				"id":        float64(123),
				"client_id": float64(456),
				"name":      "Business Registration",
				"code":      "M00123",
				"tenant_id": float64(1),
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"name":   "Business Registration",
						"owners": []interface{}{float64(101), float64(102)},
					},
				},
			},
		}

		placeholders := engine.ExtractPlaceholderData(ctx, eventData)

		// Should contain original event data
		assert.Equal(t, "matter.create", placeholders["topic"])
		assert.NotNil(t, placeholders["body"])

		// Should contain matter and client ids for PathResolver to use
		assert.Equal(t, float64(123), placeholders["id"])        // matter id (from body.id)
		assert.Equal(t, float64(456), placeholders["client_id"]) // client id (from body.client_id)

		// Should contain timestamp placeholders
		assert.NotEmpty(t, placeholders["timestamp"])
		assert.NotEmpty(t, placeholders["date"])
	})
}

func TestReplacePlaceholders(t *testing.T) {
	engine := &ruleExecutionEngine{}
	ctx := context.Background()

	placeholderData := map[string]interface{}{
		"topic": "client.update",
		"body": map[string]interface{}{
			"id":         float64(10219),
			"name":       "acclime Vietnam",
			"short_name": "ACCLIME",
			"code":       "C06855",
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name":       "acclime Vietnam",
					"stage_text": "initial",
				},
			},
		},
		"client_folder": "id:10219",
		"timestamp":     "20240115_143022",
		"date":          "2024-01-15",
	}

	t.Run("simple placeholders", func(t *testing.T) {
		template := "{client_folder}/Welcome/{timestamp}.docx"
		result := engine.ReplacePlaceholders(ctx, template, placeholderData)
		expected := "id:10219/Welcome/20240115_143022.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("nested field access", func(t *testing.T) {
		template := "{client_folder}/Documents/{body.name} - {body.code} - {date}.docx"
		result := engine.ReplacePlaceholders(ctx, template, placeholderData)
		expected := "id:10219/Documents/acclime Vietnam - C06855 - 2024-01-15.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("deep nested access", func(t *testing.T) {
		template := "{client_folder}/Stage_{body.extra.current.stage_text}/{body.extra.current.name}.docx"
		result := engine.ReplacePlaceholders(ctx, template, placeholderData)
		expected := "id:10219/Stage_initial/acclime Vietnam.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("mixed placeholders", func(t *testing.T) {
		template := "{client_folder}/{body.code}/{body.extra.current.stage_text}_{timestamp}.docx"
		result := engine.ReplacePlaceholders(ctx, template, placeholderData)
		expected := "id:10219/C06855/initial_20240115_143022.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("non-existent nested field", func(t *testing.T) {
		template := "{client_folder}/{body.nonexistent.field}/document.docx"
		result := engine.ReplacePlaceholders(ctx, template, placeholderData)
		// Should leave placeholder unchanged if field doesn't exist
		expected := "id:10219/{body.nonexistent.field}/document.docx"
		assert.Equal(t, expected, result)
	})
}

func TestExtractNestedValue(t *testing.T) {
	engine := &ruleExecutionEngine{}

	data := map[string]interface{}{
		"body": map[string]interface{}{
			"name": "Test Client",
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"stage_text": "initial",
					"owners":     []interface{}{float64(1), float64(2)},
				},
			},
		},
	}

	t.Run("simple field", func(t *testing.T) {
		value := engine.extractNestedValue("body.name", data)
		assert.Equal(t, "Test Client", value)
	})

	t.Run("nested field", func(t *testing.T) {
		value := engine.extractNestedValue("body.extra.current.stage_text", data)
		assert.Equal(t, "initial", value)
	})

	t.Run("array field", func(t *testing.T) {
		value := engine.extractNestedValue("body.extra.current.owners", data)
		expected := []interface{}{float64(1), float64(2)}
		assert.Equal(t, expected, value)
	})

	t.Run("non-existent field", func(t *testing.T) {
		value := engine.extractNestedValue("body.nonexistent.field", data)
		assert.Nil(t, value)
	})

	t.Run("invalid path", func(t *testing.T) {
		value := engine.extractNestedValue("body.name.invalid", data)
		assert.Nil(t, value)
	})
}
