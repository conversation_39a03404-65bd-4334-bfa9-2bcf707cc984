package autodoc

import (
	"context"
	"fmt"
	"time"

	"bilabl/docman/pkg/bilabllog"
)

// ErrorHandlingService provides comprehensive error handling and recovery mechanisms
type ErrorHandlingService interface {
	// HandleRuleExecutionError handles errors during rule execution with retry logic
	HandleRuleExecutionError(ctx context.Context, ruleID uint64, eventData map[string]interface{}, err error) error

	// HandleEventProcessingError handles errors during event processing
	HandleEventProcessingError(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}, err error) error

	// RetryRuleExecution retries rule execution with exponential backoff
	RetryRuleExecution(ctx context.Context, ruleID uint64, eventData map[string]interface{}, maxRetries int) error

	// LogExecutionError logs detailed error information for debugging
	LogExecutionError(ctx context.Context, operation string, details map[string]interface{}, err error)

	// RecoverFromPanic recovers from panics during rule execution
	RecoverFromPanic(ctx context.Context, operation string, details map[string]interface{}) error
}

// errorHandlingService implements ErrorHandlingService
type errorHandlingService struct {
	ruleExecutionEngine RuleExecutionEngine
	autoDocService      AutoDocService
}

// NewErrorHandlingService creates a new error handling service
func NewErrorHandlingService(
	ruleExecutionEngine RuleExecutionEngine,
	autoDocService AutoDocService,
) ErrorHandlingService {
	return &errorHandlingService{
		ruleExecutionEngine: ruleExecutionEngine,
		autoDocService:      autoDocService,
	}
}

// HandleRuleExecutionError handles errors during rule execution with retry logic
func (e *errorHandlingService) HandleRuleExecutionError(ctx context.Context, ruleID uint64, eventData map[string]interface{}, err error) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Log the error with context
	e.LogExecutionError(ctx, "rule_execution", map[string]interface{}{
		"rule_id":    ruleID,
		"event_data": eventData,
	}, err)

	// Determine if error is retryable
	if isRetryableError(err) {
		log.WithField("rule_id", ruleID).WithError(err).Info("Error is retryable, attempting retry")

		// Retry with exponential backoff
		return e.RetryRuleExecution(ctx, ruleID, eventData, 3)
	}

	// For non-retryable errors, log and return
	log.WithField("rule_id", ruleID).WithError(err).Error("Non-retryable error encountered")

	return fmt.Errorf("rule execution failed permanently: %w", err)
}

// HandleEventProcessingError handles errors during event processing
func (e *errorHandlingService) HandleEventProcessingError(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}, err error) error {
	log := bilabllog.CreateContextLogger(ctx)

	// Log the error with full context
	e.LogExecutionError(ctx, "event_processing", map[string]interface{}{
		"tenant_id":  tenantID,
		"event_type": eventType,
		"event_data": eventData,
	}, err)

	// Check if this is a critical error that requires immediate attention
	if isCriticalError(err) {
		log.WithFields(map[string]interface{}{
			"tenant_id":  tenantID,
			"event_type": eventType,
		}).WithError(err).Error("Critical error encountered during event processing")

		// For critical errors, we might want to notify administrators
		// This could be extended to send alerts, create tickets, etc.
		return fmt.Errorf("critical event processing error: %w", err)
	}

	// For non-critical errors, log and continue
	log.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_type": eventType,
	}).WithError(err).Warn("Non-critical error during event processing")

	return nil // Don't fail the entire process for non-critical errors
}

// RetryRuleExecution retries rule execution with exponential backoff
func (e *errorHandlingService) RetryRuleExecution(ctx context.Context, ruleID uint64, eventData map[string]interface{}, maxRetries int) error {
	log := bilabllog.CreateContextLogger(ctx)

	var lastErr error
	backoffDuration := time.Second // Start with 1 second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.WithFields(map[string]interface{}{
			"rule_id":          ruleID,
			"attempt":          attempt,
			"max_retries":      maxRetries,
			"backoff_duration": backoffDuration.String(),
		}).Debug("Retrying rule execution")

		// Wait before retry (except for first attempt)
		if attempt > 1 {
			select {
			case <-ctx.Done():
				return fmt.Errorf("retry cancelled due to context cancellation: %w", ctx.Err())
			case <-time.After(backoffDuration):
				// Continue with retry
			}
		}

		// Attempt rule execution
		err := e.ruleExecutionEngine.ExecuteRule(ctx, ruleID, eventData)
		if err == nil {
			log.WithFields(map[string]interface{}{
				"rule_id": ruleID,
				"attempt": attempt,
			}).Info("Rule execution succeeded after retry")
			return nil
		}

		lastErr = err

		// Check if error is still retryable
		if !isRetryableError(err) {
			log.WithFields(map[string]interface{}{
				"rule_id": ruleID,
				"attempt": attempt,
			}).WithError(err).Error("Error became non-retryable")
			break
		}

		log.WithFields(map[string]interface{}{
			"rule_id": ruleID,
			"attempt": attempt,
		}).WithError(err).Warn("Retry attempt failed")

		// Exponential backoff: double the duration for next attempt
		backoffDuration *= 2
		if backoffDuration > time.Minute {
			backoffDuration = time.Minute // Cap at 1 minute
		}
	}

	log.WithFields(map[string]interface{}{
		"rule_id":     ruleID,
		"max_retries": maxRetries,
	}).WithError(lastErr).Error("All retry attempts exhausted")

	return fmt.Errorf("rule execution failed after %d retries: %w", maxRetries, lastErr)
}

// LogExecutionError logs detailed error information for debugging
func (e *errorHandlingService) LogExecutionError(ctx context.Context, operation string, details map[string]interface{}, err error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Create comprehensive error log entry
	errorDetails := map[string]interface{}{
		"operation":  operation,
		"error":      err.Error(),
		"error_type": fmt.Sprintf("%T", err),
		"timestamp":  time.Now().UTC().Format(time.RFC3339),
	}

	// Add provided details
	for key, value := range details {
		errorDetails[key] = value
	}

	// Add stack trace information if available
	if stackTracer, ok := err.(interface{ StackTrace() []uintptr }); ok {
		errorDetails["has_stack_trace"] = true
		_ = stackTracer // Use the stack trace if needed
	}

	log.WithFields(errorDetails).Error("Detailed error information")
}

// RecoverFromPanic recovers from panics during rule execution
func (e *errorHandlingService) RecoverFromPanic(ctx context.Context, operation string, details map[string]interface{}) error {
	if r := recover(); r != nil {
		log := bilabllog.CreateContextLogger(ctx)

		// Create panic recovery log entry
		panicDetails := map[string]interface{}{
			"operation":   operation,
			"panic_value": fmt.Sprintf("%v", r),
			"panic_type":  fmt.Sprintf("%T", r),
			"timestamp":   time.Now().UTC().Format(time.RFC3339),
			"recovered":   true,
		}

		// Add provided details
		for key, value := range details {
			panicDetails[key] = value
		}

		log.WithFields(panicDetails).Error("Panic recovered")

		// Convert panic to error
		return fmt.Errorf("panic recovered during %s: %v", operation, r)
	}

	return nil
}

// isRetryableError determines if an error is retryable
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Network-related errors are usually retryable
	retryablePatterns := []string{
		"connection refused",
		"connection timeout",
		"timeout",
		"temporary failure",
		"service unavailable",
		"internal server error",
		"bad gateway",
		"gateway timeout",
		"too many requests",
	}

	for _, pattern := range retryablePatterns {
		if contains(errorMsg, pattern) {
			return true
		}
	}

	return false
}

// isCriticalError determines if an error is critical and requires immediate attention
func isCriticalError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// Critical error patterns
	criticalPatterns := []string{
		"database connection lost",
		"authentication failed",
		"authorization denied",
		"disk full",
		"out of memory",
		"security violation",
		"data corruption",
	}

	for _, pattern := range criticalPatterns {
		if contains(errorMsg, pattern) {
			return true
		}
	}

	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsInMiddle(s, substr)))
}

// containsInMiddle checks if substring exists in the middle of string
func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// ErrorType represents different types of errors in the system
type ErrorType string

const (
	ErrorTypeRetryable    ErrorType = "retryable"
	ErrorTypeNonRetryable ErrorType = "non_retryable"
	ErrorTypeCritical     ErrorType = "critical"
	ErrorTypeWarning      ErrorType = "warning"
)

// ErrorContext provides additional context for error handling
type ErrorContext struct {
	Operation  string                 `json:"operation"`
	TenantID   uint64                 `json:"tenant_id,omitempty"`
	RuleID     uint64                 `json:"rule_id,omitempty"`
	EventType  string                 `json:"event_type,omitempty"`
	EventData  map[string]interface{} `json:"event_data,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	ErrorType  ErrorType              `json:"error_type"`
	RetryCount int                    `json:"retry_count,omitempty"`
}
