package autodoc

import (
	"context"
	"testing"
	"time"

	"code.mybil.net/gophers/gokit/clients/settings"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockSettingsClient is a mock implementation of settings.Client
type MockSettingsClient struct {
	mock.Mock
}

func (m *MockSettingsClient) Fetch(ctx context.Context, key string, tenantID uint64, userID uint64) (*settings.SettingItem, error) {
	args := m.Called(ctx, key, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*settings.SettingItem), args.Error(1)
}

func (m *MockSettingsClient) LoadTenantLocation(ctx context.Context, tenantID uint64) (*time.Location, error) {
	args := m.Called(ctx, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*time.Location), args.Error(1)
}

func (m *MockSettingsClient) LoadIgnoreClients(ctx context.Context, tenantID uint64) (settings.IgnoreClients, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(settings.IgnoreClients), args.Error(1)
}

func TestDefaultDMSService_GetDefaultProvider(t *testing.T) {
	ctx := context.Background()

	t.Run("Success_WithValidSetting", func(t *testing.T) {
		mockClient := &MockSettingsClient{}
		service := NewDefaultDMSService(mockClient)

		// Mock successful fetch
		mockSetting := &settings.SettingItem{
			Key:   "default_dms",
			Value: "gdrive",
		}
		mockClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(mockSetting, nil)

		// Test
		provider, err := service.GetDefaultProvider(ctx, 123)

		// Verify
		assert.NoError(t, err)
		assert.Equal(t, "gdrive", provider)
		mockClient.AssertExpectations(t)
	})

	t.Run("Success_WithEmptyValue_UsesFallback", func(t *testing.T) {
		mockClient := &MockSettingsClient{}
		service := NewDefaultDMSService(mockClient)

		// Mock fetch with empty value
		mockSetting := &settings.SettingItem{
			Key:   "default_dms",
			Value: "",
		}
		mockClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(mockSetting, nil)

		// Test
		provider, err := service.GetDefaultProvider(ctx, 123)

		// Verify
		assert.NoError(t, err)
		assert.Equal(t, "internal", provider) // Should use fallback
		mockClient.AssertExpectations(t)
	})

	t.Run("Error_FetchFails_UsesFallback", func(t *testing.T) {
		mockClient := &MockSettingsClient{}
		service := NewDefaultDMSService(mockClient)

		// Mock fetch error
		mockClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(nil, assert.AnError)

		// Test
		provider, err := service.GetDefaultProvider(ctx, 123)

		// Verify
		assert.NoError(t, err)                // Should not return error, just use fallback
		assert.Equal(t, "internal", provider) // Should use fallback
		mockClient.AssertExpectations(t)
	})

	t.Run("Success_AlwaysFetchesFresh", func(t *testing.T) {
		mockClient := &MockSettingsClient{}
		service := NewDefaultDMSService(mockClient)

		// Mock successful fetch (should be called twice - no caching)
		mockSetting := &settings.SettingItem{
			Key:   "default_dms",
			Value: "sharepoint",
		}
		mockClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(mockSetting, nil).Twice()

		// Test first call
		provider1, err1 := service.GetDefaultProvider(ctx, 123)
		assert.NoError(t, err1)
		assert.Equal(t, "sharepoint", provider1)

		// Test second call (should fetch fresh data, not use cache)
		provider2, err2 := service.GetDefaultProvider(ctx, 123)
		assert.NoError(t, err2)
		assert.Equal(t, "sharepoint", provider2)

		// Verify mock was called twice (no caching)
		mockClient.AssertExpectations(t)
	})

	t.Run("Success_WithJSONEncodedValue", func(t *testing.T) {
		mockClient := &MockSettingsClient{}
		service := NewDefaultDMSService(mockClient)

		// Mock setting with JSON-encoded value
		mockSetting := &settings.SettingItem{
			Key:   "default_dms",
			Value: "\"gdrive\"", // JSON-encoded string
		}
		mockClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(mockSetting, nil)

		// Test
		provider, err := service.GetDefaultProvider(ctx, 123)

		// Verify - should decode JSON and return clean value
		assert.NoError(t, err)
		assert.Equal(t, "gdrive", provider)
		mockClient.AssertExpectations(t)
	})
}

func TestCleanProviderValue(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Plain string",
			input:    "gdrive",
			expected: "gdrive",
		},
		{
			name:     "JSON-encoded string",
			input:    "\"gdrive\"",
			expected: "gdrive",
		},
		{
			name:     "JSON-encoded string with spaces",
			input:    " \"sharepoint\" ",
			expected: "sharepoint",
		},
		{
			name:     "String with whitespace",
			input:    " internal ",
			expected: "internal",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Invalid JSON",
			input:    "\"unclosed",
			expected: "\"unclosed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cleanProviderValue(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
