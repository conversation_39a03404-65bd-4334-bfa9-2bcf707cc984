package autodoc

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/transport"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// TargetParentResult represents the result of resolving target parent path
type TargetParentResult struct {
	ParentID         uint64 `json:"parent_id"`          // Parent folder ID (0 if no specific parent)
	ObjectType       int    `json:"object_type"`        // Object type for document association
	ObjectID         uint64 `json:"object_id"`          // Object ID for document association
	TenantID         uint64 `json:"tenant_id"`          // Tenant ID
	ExternalParentID string `json:"external_parent_id"` // External parent folder ID (for cross-provider operations)
}

// AutoDocService interface defines operations for document automation
type AutoDocService interface {
	// AutoDocRoot Management
	EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error)
	GetAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error)

	// File Operations
	CopyFile(ctx context.Context, tenantID uint64, sourceFileID uint64, targetParentID uint64, newName string) (*model.Document, error)
	CopyFolder(ctx context.Context, tenantID uint64, sourceFolderID uint64, targetParentID uint64, newName string) (*model.Document, error)

	// Rule Management
	CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error
	GetRule(ctx context.Context, ruleID uint64) (*model.DocumentAutomationRule, error)
	GetRuleWithTenant(ctx context.Context, ruleID uint64, tenantID uint64) (*model.DocumentAutomationRule, error)
	GetRulesByTenant(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error)
	UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule) error
	DeleteRule(ctx context.Context, ruleID uint64) error
	GetActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error)

	// Document Generation
	GenerateFromTemplate(ctx context.Context, templateDoc *model.Document, targetParent *TargetParentResult, fileName string, placeholders map[string]interface{}, override bool) (*model.Document, error)
	ProcessDocxTemplate(ctx context.Context, templateContent []byte, placeholders map[string]interface{}) ([]byte, error)
	ValidateTemplate(ctx context.Context, templateContent []byte) error
	ExtractPlaceholders(ctx context.Context, templateContent []byte) ([]string, error)

	// Path Resolution
	ResolveTargetParentPath(ctx context.Context, tenantID uint64, parentPath string, placeholderData map[string]interface{}) (*TargetParentResult, error)
}

// autoDocService implements the AutoDocService interface
type autoDocService struct {
	documentRepo            repositories.DocumentRepository
	documentServiceRegistry DocumentServiceRegistry
	ruleRepo                repositories.DocumentAutomationRuleRepository
	docxProcessor           *DocxProcessor
}

// NewAutoDocService creates a new instance of AutoDocService
func NewAutoDocService(
	documentRepo repositories.DocumentRepository,
	documentServiceRegistry DocumentServiceRegistry,
	ruleRepo repositories.DocumentAutomationRuleRepository,
	globClient transport.Glob,
) AutoDocService {
	return &autoDocService{
		documentRepo:            documentRepo,
		documentServiceRegistry: documentServiceRegistry,
		ruleRepo:                ruleRepo,
		docxProcessor:           NewDocxProcessor(globClient),
	}
}

// getDocumentService gets the appropriate document service for a provider
func (s *autoDocService) getDocumentService(provider string) (DocumentService, error) {
	if provider == "" {
		provider = s.documentServiceRegistry.GetDefaultProvider()
	}

	return s.documentServiceRegistry.GetProvider(provider)
}

// EnsureAutoDocRoot creates AutoDocRoot folder if it doesn't exist, returns existing one if it does
func (s *autoDocService) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	// Get default document service
	documentService, err := s.getDocumentService("")
	if err != nil {
		return nil, fmt.Errorf("failed to get document service: %w", err)
	}

	// Delegate to document service to avoid duplicate logic
	// The document service already handles all the complexity of checking existence,
	// concurrent creation, error handling, etc.
	return documentService.EnsureAutoDocRoot(ctx, tenantID)
}

// GetAutoDocRoot retrieves the AutoDocRoot folder for a tenant
// NOTE: For optimal performance, ensure there's a database index on (tenant_id, name, doc_type, parent_id)
func (s *autoDocService) GetAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	log := logger.WithCtx(ctx, "GetAutoDocRoot")
	log.Debugf("Searching for AutoDocRoot tenant_id=%d", tenantID)

	// Search for AutoDocRoot folder by name and tenant
	filters := []*model.Filter{
		model.NewFilterE("tenant_id", tenantID),
		model.NewFilterE("name", model.AutoDocRootFolderName),
		model.NewFilterE("doc_type", model.DocTypeDir), // Only folders
		model.NewFilterE("parent_id", 0),               // Root level only
	}

	query := &model.Query{
		Filters: filters,
	}

	documents, err := s.documentRepo.List(ctx, query)
	if err != nil {
		log.WithError(err).Errorf("Failed to search for AutoDocRoot tenant_id=%d", tenantID)
		return nil, fmt.Errorf("failed to search for AutoDocRoot: %w", err)
	}

	if len(documents) == 0 {
		log.Debugf("AutoDocRoot not found tenant_id=%d", tenantID)
		return nil, nil // Not found, but not an error
	}

	if len(documents) > 1 {
		log.Warnf("Multiple AutoDocRoot folders found tenant_id=%d count=%d", tenantID, len(documents))
		// Return the first one, but log a warning
	}

	autoDocRoot := documents[0]
	log.Debugf("Found AutoDocRoot tenant_id=%d document_id=%d", tenantID, autoDocRoot.ID)

	return autoDocRoot, nil
}

// CopyFile copies a file to a target parent folder with optional new name
// Note: This is a placeholder implementation. Full copy functionality requires
// extending DocumentService with copy operations or using Google Drive API directly.
func (s *autoDocService) CopyFile(ctx context.Context, tenantID uint64, sourceFileID uint64, targetParentID uint64, newName string) (*model.Document, error) {
	log := logger.WithCtx(ctx, "CopyFile")
	log.Infof("Copying file source_id=%d target_parent_id=%d new_name=%s tenant_id=%d", sourceFileID, targetParentID, newName, tenantID)

	// Validate tenant isolation - check source file belongs to tenant
	sourceFile, err := s.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", sourceFileID),
			model.NewFilterE("tenant_id", tenantID),
		},
	})
	if err != nil {
		log.WithError(err).Errorf("Failed to find source file source_id=%d tenant_id=%d", sourceFileID, tenantID)
		return nil, fmt.Errorf("failed to find source file: %w", err)
	}
	if sourceFile == nil {
		log.Errorf("Source file not found or access denied source_id=%d tenant_id=%d", sourceFileID, tenantID)
		return nil, fmt.Errorf("source file not found or access denied")
	}

	// Validate target parent belongs to tenant
	targetParent, err := s.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", targetParentID),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("doc_type", model.DocTypeDir), // Must be a folder
		},
	})
	if err != nil {
		log.WithError(err).Errorf("Failed to find target parent target_parent_id=%d tenant_id=%d", targetParentID, tenantID)
		return nil, fmt.Errorf("failed to find target parent: %w", err)
	}
	if targetParent == nil {
		log.Errorf("Target parent not found or access denied target_parent_id=%d tenant_id=%d", targetParentID, tenantID)
		return nil, fmt.Errorf("target parent not found or access denied")
	}

	// Use original name if newName is empty
	fileName := newName
	if fileName == "" {
		_ = sourceFile.Name // TODO: Use this when implementing actual copy
	}

	// TODO: Implement actual file copy using Google Drive API
	// For now, return error indicating feature not yet implemented
	log.Errorf("File copy operation not yet implemented - requires Google Drive API integration")
	return nil, fmt.Errorf("file copy operation not yet implemented")
}

// CopyFolder copies a folder and its contents to a target parent folder with optional new name
// Note: This is a placeholder implementation. Full copy functionality requires
// extending DocumentService with copy operations or using Google Drive API directly.
func (s *autoDocService) CopyFolder(ctx context.Context, tenantID uint64, sourceFolderID uint64, targetParentID uint64, newName string) (*model.Document, error) {
	log := logger.WithCtx(ctx, "CopyFolder")
	log.Infof("Copying folder source_id=%d target_parent_id=%d new_name=%s tenant_id=%d", sourceFolderID, targetParentID, newName, tenantID)

	// Validate tenant isolation - check source folder belongs to tenant
	sourceFolder, err := s.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", sourceFolderID),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("doc_type", model.DocTypeDir), // Must be a folder
		},
	})
	if err != nil {
		log.WithError(err).Errorf("Failed to find source folder source_id=%d tenant_id=%d", sourceFolderID, tenantID)
		return nil, fmt.Errorf("failed to find source folder: %w", err)
	}
	if sourceFolder == nil {
		log.Errorf("Source folder not found or access denied source_id=%d tenant_id=%d", sourceFolderID, tenantID)
		return nil, fmt.Errorf("source folder not found or access denied")
	}

	// Validate target parent belongs to tenant
	targetParent, err := s.documentRepo.FindOne(ctx, &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("id", targetParentID),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("doc_type", model.DocTypeDir), // Must be a folder
		},
	})
	if err != nil {
		log.WithError(err).Errorf("Failed to find target parent target_parent_id=%d tenant_id=%d", targetParentID, tenantID)
		return nil, fmt.Errorf("failed to find target parent: %w", err)
	}
	if targetParent == nil {
		log.Errorf("Target parent not found or access denied target_parent_id=%d tenant_id=%d", targetParentID, tenantID)
		return nil, fmt.Errorf("target parent not found or access denied")
	}

	// Use original name if newName is empty
	folderName := newName
	if folderName == "" {
		_ = sourceFolder.Name // TODO: Use this when implementing actual copy
	}

	// TODO: Implement actual folder copy using Google Drive API
	// For now, return error indicating feature not yet implemented
	log.Errorf("Folder copy operation not yet implemented - requires Google Drive API integration")
	return nil, fmt.Errorf("folder copy operation not yet implemented")
}

// Rule Management Methods

// CreateRule creates a new document automation rule
func (s *autoDocService) CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithFields(map[string]interface{}{
		"tenant_id":    rule.TenantID,
		"rule_name":    rule.Name,
		"trigger_type": rule.TriggerType,
	}).Info("Creating automation rule")

	if err := s.ruleRepo.CreateRule(ctx, rule); err != nil {
		log.WithError(err).Error("Failed to create automation rule")
		return fmt.Errorf("failed to create automation rule: %w", err)
	}

	log.WithField("rule_id", rule.ID).Info("Successfully created automation rule")
	return nil
}

// GetRule retrieves a document automation rule by ID with tenant isolation
func (s *autoDocService) GetRule(ctx context.Context, ruleID uint64) (*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// SECURITY: Extract tenantID from authenticated context
	tenantID, err := s.extractTenantIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to extract tenant ID from context")
		return nil, fmt.Errorf("authentication required: %w", err)
	}

	return s.GetRuleWithTenant(ctx, ruleID, tenantID)
}

// GetRuleWithTenant retrieves a document automation rule by ID with explicit tenant ID
func (s *autoDocService) GetRuleWithTenant(ctx context.Context, ruleID uint64, tenantID uint64) (*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("rule_id", ruleID).WithField("tenant_id", tenantID).Info("Retrieving automation rule")

	// SECURITY: Pass tenantID for tenant isolation
	rule, err := s.ruleRepo.FindByID(ctx, ruleID, tenantID)
	if err != nil {
		log.WithError(err).Error("Failed to retrieve automation rule")
		return nil, fmt.Errorf("failed to retrieve automation rule: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"rule_id":      rule.ID,
		"tenant_id":    rule.TenantID,
		"rule_name":    rule.Name,
		"trigger_type": rule.TriggerType,
	}).Info("Successfully retrieved automation rule")

	return rule, nil
}

// GetRulesByTenant retrieves all automation rules for a tenant
func (s *autoDocService) GetRulesByTenant(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("tenant_id", tenantID).Info("Retrieving automation rules for tenant")

	rules, err := s.ruleRepo.FindByTenantID(ctx, tenantID)
	if err != nil {
		log.WithError(err).Error("Failed to retrieve automation rules for tenant")
		return nil, fmt.Errorf("failed to retrieve automation rules for tenant: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"tenant_id":   tenantID,
		"rules_count": len(rules),
	}).Info("Successfully retrieved automation rules for tenant")

	return rules, nil
}

// UpdateRule updates an existing document automation rule with tenant isolation
func (s *autoDocService) UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	log := bilabllog.CreateContextLogger(ctx)

	// SECURITY: Extract tenantID from authenticated context
	tenantID, err := s.extractTenantIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to extract tenant ID from context")
		return fmt.Errorf("authentication required: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"rule_id":        rule.ID,
		"tenant_id":      rule.TenantID,
		"auth_tenant_id": tenantID,
		"rule_name":      rule.Name,
		"trigger_type":   rule.TriggerType,
	}).Info("Updating automation rule")

	// SECURITY: Pass tenantID for tenant isolation
	if err := s.ruleRepo.UpdateRule(ctx, rule, tenantID); err != nil {
		log.WithError(err).Error("Failed to update automation rule")
		return fmt.Errorf("failed to update automation rule: %w", err)
	}

	log.WithField("rule_id", rule.ID).Info("Successfully updated automation rule")
	return nil
}

// DeleteRule deletes a document automation rule by ID with tenant isolation
func (s *autoDocService) DeleteRule(ctx context.Context, ruleID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	// SECURITY: Extract tenantID from authenticated context
	tenantID, err := s.extractTenantIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to extract tenant ID from context")
		return fmt.Errorf("authentication required: %w", err)
	}

	log.WithField("rule_id", ruleID).WithField("tenant_id", tenantID).Info("Deleting automation rule")

	// SECURITY: Pass tenantID for tenant isolation
	if err := s.ruleRepo.DeleteRule(ctx, ruleID, tenantID); err != nil {
		log.WithError(err).Error("Failed to delete automation rule")
		return fmt.Errorf("failed to delete automation rule: %w", err)
	}

	log.WithField("rule_id", ruleID).Info("Successfully deleted automation rule")
	return nil
}

// GetActiveRules retrieves all active automation rules for a tenant
func (s *autoDocService) GetActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithField("tenant_id", tenantID).Info("Retrieving active automation rules for tenant")

	rules, err := s.ruleRepo.FindActiveRules(ctx, tenantID)
	if err != nil {
		log.WithError(err).Error("Failed to retrieve active automation rules for tenant")
		return nil, fmt.Errorf("failed to retrieve active automation rules for tenant: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"tenant_id":          tenantID,
		"active_rules_count": len(rules),
	}).Info("Successfully retrieved active automation rules for tenant")

	return rules, nil
}

// GenerateFromTemplate generates a document from a template with placeholder replacement
func (s *autoDocService) GenerateFromTemplate(ctx context.Context, templateDoc *model.Document, targetParent *TargetParentResult, fileName string, placeholders map[string]interface{}, override bool) (*model.Document, error) {
	log := logger.WithCtx(ctx, "GenerateFromTemplate")
	log.Infof("Generating document from template template_id=%d target_parent_id=%d object_type=%d object_id=%d filename=%s",
		templateDoc.ID, targetParent.ParentID, targetParent.ObjectType, targetParent.ObjectID, fileName)

	// Load template content from document
	templateContent, err := s.loadDocumentContent(ctx, templateDoc)
	if err != nil {
		log.WithError(err).Error("Failed to load template content")
		return nil, fmt.Errorf("failed to load template content: %w", err)
	}

	// Process template with placeholders
	processedContent, err := s.docxProcessor.ProcessTemplate(ctx, templateContent, placeholders)
	if err != nil {
		log.WithError(err).Error("Failed to process template")
		return nil, fmt.Errorf("failed to process template: %w", err)
	}

	// Save processed document with target parent context
	generatedDoc, err := s.saveProcessedDocumentWithContext(ctx, processedContent, targetParent, fileName, templateDoc.TenantID, override)
	if err != nil {
		log.WithError(err).Error("Failed to save processed document")
		return nil, fmt.Errorf("failed to save processed document: %w", err)
	}

	// Check if document was skipped due to override=false
	if generatedDoc == nil {
		log.WithFields(map[string]interface{}{
			"file_name":      fileName,
			"override":       override,
			"template_id":    templateDoc.ID,
		}).Info("Document generation skipped due to existing file and override=false")
		return nil, nil
	}

	log.Infof("Successfully generated document from template generated_doc_id=%d", generatedDoc.ID)
	return generatedDoc, nil
}

// ProcessDocxTemplate processes DOCX template content with placeholder replacement
func (s *autoDocService) ProcessDocxTemplate(ctx context.Context, templateContent []byte, placeholders map[string]interface{}) ([]byte, error) {
	return s.docxProcessor.ProcessTemplate(ctx, templateContent, placeholders)
}

// ValidateTemplate validates that a template is properly formatted
func (s *autoDocService) ValidateTemplate(ctx context.Context, templateContent []byte) error {
	return s.docxProcessor.ValidateTemplate(ctx, templateContent)
}

// ExtractPlaceholders extracts all placeholders from a template
func (s *autoDocService) ExtractPlaceholders(ctx context.Context, templateContent []byte) ([]string, error) {
	return s.docxProcessor.ExtractPlaceholders(ctx, templateContent)
}

// loadDocumentContent loads content from a document using the document service
func (s *autoDocService) loadDocumentContent(ctx context.Context, doc *model.Document) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)

	if doc == nil {
		return nil, fmt.Errorf("document is nil")
	}

	log.WithField("document_id", doc.ID).WithField("tenant_id", doc.TenantID).WithField("doc_type", doc.DocType).WithField("key", doc.Key).Info("Loading document content")

	// Only files (DocType == 2) have content
	if doc.DocType != 2 {
		return nil, fmt.Errorf("document is not a file (doc_type=%d)", doc.DocType)
	}

	// Get document service (use default provider for content retrieval)
	documentService, err := s.getDocumentService("")
	if err != nil {
		log.WithError(err).Error("Failed to get document service")
		return nil, fmt.Errorf("failed to get document service: %w", err)
	}

	// Use document service to get content
	content, err := documentService.GetDocumentContent(ctx, doc.ID, doc.TenantID)
	if err != nil {
		log.WithError(err).Error("Failed to load document content")
		return nil, fmt.Errorf("failed to load document content: %w", err)
	}

	log.WithField("document_id", doc.ID).WithField("content_size", len(content)).Info("Successfully loaded document content")

	return content, nil
}

// saveProcessedDocumentWithContext saves processed document content with target parent context and override support
func (s *autoDocService) saveProcessedDocumentWithContext(ctx context.Context, content []byte, targetParent *TargetParentResult, fileName string, tenantID uint64, override bool) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	log.WithFields(map[string]interface{}{
		"parent_id":    targetParent.ParentID,
		"object_type":  targetParent.ObjectType,
		"object_id":    targetParent.ObjectID,
		"file_name":    fileName,
		"tenant_id":    tenantID,
		"content_size": len(content),
	}).Info("Saving processed document with context and override support")

	// Check if file with same name already exists
	existingQuery := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", targetParent.ParentID),
			model.NewFilterE("name", fileName),
			model.NewFilterE("doc_type", model.DocTypeFile),
			model.NewFilterE("object_type", targetParent.ObjectType),
			model.NewFilterE("object_id", targetParent.ObjectID),
		},
	}

	log.WithFields(map[string]interface{}{
		"tenant_id":   tenantID,
		"parent_id":   targetParent.ParentID,
		"file_name":   fileName,
		"object_type": targetParent.ObjectType,
		"object_id":   targetParent.ObjectID,
	}).Info("Checking for existing generated document")

	// First check if document exists to avoid misleading error logs
	exists, err := s.documentRepo.Exists(ctx, existingQuery)
	if err != nil {
		log.WithError(err).Error("Failed to check document existence")
		return nil, fmt.Errorf("failed to check document existence: %w", err)
	}

	if exists {
		// Document exists, check override setting
		existingDoc, err := s.documentRepo.FindOne(ctx, existingQuery)
		if err != nil {
			log.WithError(err).Error("Failed to retrieve existing document")
			return nil, fmt.Errorf("failed to retrieve existing document: %w", err)
		}

		if !override {
			// Override disabled, log warning and skip
			log.WithFields(map[string]interface{}{
				"existing_doc_id":   existingDoc.ID,
				"existing_doc_name": existingDoc.Name,
				"new_file_name":     fileName,
				"override_setting":  override,
			}).Warn("Document with same name already exists, skipping generation due to override=false")
			return nil, nil
		}

		// Override enabled, proceed with override
		log.WithFields(map[string]interface{}{
			"existing_doc_id": existingDoc.ID,
			"existing_key":    existingDoc.Key,
			"file_name":       fileName,
		}).Info("Found existing document to override")

		// First, create new document content
		createReq := &CreateDocumentRequest{
			Name:        fileName,
			ParentID:    targetParent.ParentID,
			TenantID:    tenantID,
			ObjectType:  targetParent.ObjectType,
			ObjectID:    targetParent.ObjectID,
			DocType:     model.DocTypeFile,
			CreatedUser: 1, // System user
		}

		// Get document service
		documentService, err := s.getDocumentService("")
		if err != nil {
			log.WithError(err).Error("Failed to get document service")
			return nil, fmt.Errorf("failed to get document service: %w", err)
		}

		// Create new document to get new key
		newDoc, err := documentService.CreateDocument(ctx, createReq)
		if err != nil {
			log.WithError(err).Error("Failed to create new document for override")
			return nil, fmt.Errorf("failed to create new document for override: %w", err)
		}

		// Update existing document with new key
		updateDoc := &model.Document{
			Key:    newDoc.Key,
			Status: 1, // Ensure it's active
		}

		updateQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", existingDoc.ID),
				model.NewFilterE("tenant_id", tenantID),
			},
		}

		err = s.documentRepo.UpdateOne(ctx, updateQuery, updateDoc)
		if err != nil {
			log.WithError(err).Error("Failed to override existing document")
			return nil, fmt.Errorf("failed to override existing document: %w", err)
		}

		// Delete the temporary new document since we only needed its key
		deleteQuery := &model.Query{
			Filters: []*model.Filter{
				model.NewFilterE("id", newDoc.ID),
				model.NewFilterE("tenant_id", tenantID),
			},
		}
		_ = s.documentRepo.DeleteOne(ctx, deleteQuery) // Ignore error for cleanup

		// Update existing document with new key for return
		existingDoc.Key = newDoc.Key

		log.WithFields(map[string]interface{}{
			"document_id":  existingDoc.ID,
			"document_key": existingDoc.Key,
			"file_name":    fileName,
			"object_type":  existingDoc.ObjectType,
			"object_id":    existingDoc.ObjectID,
		}).Info("Successfully overrode existing document with context")

		return existingDoc, nil
	} else {
		log.Info("No existing document found, will create new one")
	}

	// File doesn't exist, create new one
	createReq := &CreateDocumentRequest{
		Name:        fileName,
		ParentID:    targetParent.ParentID,
		TenantID:    tenantID,
		ObjectType:  targetParent.ObjectType, // Use object type from target parent
		ObjectID:    targetParent.ObjectID,   // Use object ID from target parent
		DocType:     model.DocTypeFile,       // File type
		CreatedUser: 1,                       // System user for auto-generated documents
	}

	// Get document service
	documentService, err := s.getDocumentService("")
	if err != nil {
		log.WithError(err).Error("Failed to get document service")
		return nil, fmt.Errorf("failed to get document service: %w", err)
	}

	// Create document using document service
	document, err := documentService.CreateDocument(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("Failed to create processed document with context")
		return nil, fmt.Errorf("failed to create processed document: %w", err)
	}

	// Upload the processed content to storage
	if len(content) > 0 {
		// Generate filename for upload
		uploadFilename := fmt.Sprintf("autodoc_%d_%s", document.ID, fileName)

		// Use system user for internal operations
		userID := fmt.Sprintf("system_%d", tenantID)
		userRoles := "8" // System admin role

		// Upload content using docx processor
		uploadResult, err := s.docxProcessor.UploadProcessedDocument(ctx, uploadFilename, content, userID, fmt.Sprintf("%d", tenantID), userRoles)
		if err != nil {
			log.WithError(err).Error("Failed to upload processed document content")
			// Don't fail the operation - document record is already created
			// But log the issue for investigation
			log.WithField("document_id", document.ID).Warn("Document created but content upload failed - document will be invalid")
		} else {
			// Update document with the upload key
			document.Key = uploadResult.Key
			log.WithField("document_id", document.ID).WithField("upload_key", uploadResult.Key).Info("Successfully uploaded processed document content")
		}
	}

	log.WithFields(map[string]interface{}{
		"document_id":  document.ID,
		"document_key": document.Key,
		"file_name":    fileName,
		"object_type":  document.ObjectType,
		"object_id":    document.ObjectID,
	}).Info("Successfully saved new processed document with context")

	return document, nil
}

// ResolveTargetParentPath resolves target parent path with placeholder support
func (s *autoDocService) ResolveTargetParentPath(ctx context.Context, tenantID uint64, parentPath string, placeholderData map[string]interface{}) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "ResolveTargetParentPath")
	log.Infof("Resolving target parent path path=%s tenant_id=%d", parentPath, tenantID)

	if parentPath == "" {
		// No parent path specified, use AutoDocRoot
		autoDocRoot, err := s.GetAutoDocRoot(ctx, tenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get AutoDocRoot: %w", err)
		}
		return &TargetParentResult{
			ParentID:   autoDocRoot.ID,
			ObjectType: 0,
			ObjectID:   0,
			TenantID:   tenantID,
		}, nil
	}

	// Create path resolver with document repository
	pathResolver := NewPathResolver(s.documentRepo)

	// Add tenant_id to placeholder data if not present
	if placeholderData == nil {
		placeholderData = make(map[string]interface{})
	}
	placeholderData["tenant_id"] = tenantID

	// Resolve path with placeholders
	resolvedPath, err := pathResolver.ResolveTargetPath(ctx, parentPath, placeholderData)
	if err != nil {
		log.WithError(err).Errorf("Failed to resolve path with placeholders path=%s", parentPath)
		return nil, fmt.Errorf("failed to resolve path with placeholders: %w", err)
	}

	log.Infof("Resolved path with placeholders: %s -> %s", parentPath, resolvedPath)

	// Always resolve as hierarchical path with context (handles both regular paths and object contexts)
	return s.resolveHierarchicalPathWithContext(ctx, tenantID, resolvedPath)
}

// resolveHierarchicalPathWithContext resolves a hierarchical path and returns context information
func (s *autoDocService) resolveHierarchicalPathWithContext(ctx context.Context, tenantID uint64, path string) (*TargetParentResult, error) {
	log := logger.WithCtx(ctx, "resolveHierarchicalPathWithContext")

	// Split path into components
	components := strings.Split(strings.Trim(path, "/"), "/")
	if len(components) == 0 || (len(components) == 1 && components[0] == "") {
		// Empty path, return root context
		return &TargetParentResult{
			ParentID:   0,
			ObjectType: 0,
			ObjectID:   0,
			TenantID:   tenantID,
		}, nil
	}

	// Check if first component is a special format
	var currentParentID uint64
	var objectType int
	var objectID uint64
	startIndex := 0

	if strings.HasPrefix(components[0], "object:") {
		// First component is object context: "object:{object_type}:{object_id}:{tenant_id}"
		parts := strings.Split(components[0], ":")
		if len(parts) != 4 {
			return nil, fmt.Errorf("invalid object format: %s", components[0])
		}

		var err error
		if objectType, err = strconv.Atoi(parts[1]); err != nil {
			return nil, fmt.Errorf("invalid object_type in: %s", components[0])
		}
		if objectID, err = strconv.ParseUint(parts[2], 10, 64); err != nil {
			return nil, fmt.Errorf("invalid object_id in: %s", components[0])
		}

		// For object context, find the corresponding folder
		// Look for existing folder with this object_type and object_id
		objectFolder, err := s.findObjectFolder(ctx, tenantID, objectType, objectID)
		if err != nil {
			log.WithError(err).Warnf("Failed to find object folder for object_type=%d object_id=%d, will use root", objectType, objectID)
			currentParentID = 0
		} else if objectFolder != nil {
			currentParentID = objectFolder.ID
			log.Infof("Found existing object folder: folder_id=%d object_type=%d object_id=%d", objectFolder.ID, objectType, objectID)
		} else {
			currentParentID = 0
			log.Infof("No existing object folder found for object_type=%d object_id=%d, will use root", objectType, objectID)
		}
		startIndex = 1
		log.Infof("Starting from object context: object_type=%d object_id=%d parent_id=%d", objectType, objectID, currentParentID)

	} else if strings.HasPrefix(components[0], "id:") {
		// First component is a folder ID, extract and use it as starting parent
		idStr := strings.TrimPrefix(components[0], "id:")
		if firstID, err := strconv.ParseUint(idStr, 10, 64); err == nil {
			currentParentID = firstID
			startIndex = 1
			log.Infof("Starting from folder ID: %d", firstID)
		} else {
			log.WithError(err).Errorf("Failed to parse folder ID from: %s", components[0])
			return nil, fmt.Errorf("invalid folder ID format: %s", components[0])
		}
	} else {
		// First component is not an ID, start from root (parent_id = 0)
		currentParentID = 0
		log.Infof("Starting from root (parent_id = 0)")
	}

	// If there are no more components after the first one, return current context
	if startIndex >= len(components) {
		return &TargetParentResult{
			ParentID:   currentParentID,
			ObjectType: objectType,
			ObjectID:   objectID,
			TenantID:   tenantID,
		}, nil
	}

	// Create each folder in the path if it doesn't exist
	for i := startIndex; i < len(components); i++ {
		component := components[i]
		if component == "" {
			continue
		}

		// Build query filters for finding existing folder
		filters := []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", currentParentID),
			model.NewFilterE("name", component),
			model.NewFilterE("doc_type", model.DocTypeDir),
		}

		// Add object filters if we're in object context
		if objectType != 0 && objectID != 0 {
			filters = append(filters,
				model.NewFilterE("object_type", objectType),
				model.NewFilterE("object_id", objectID),
			)
		}

		query := &model.Query{
			Filters: filters,
		}

		existingDoc, err := s.documentRepo.FindOne(ctx, query)
		if err == nil && existingDoc != nil {
			// Folder exists, use it as parent for next component
			currentParentID = existingDoc.ID
			log.Infof("Found existing folder: %s (ID: %d) with object_type=%d object_id=%d",
				component, existingDoc.ID, existingDoc.ObjectType, existingDoc.ObjectID)
			continue
		}

		// Folder doesn't exist, create it
		createReq := &CreateDocumentRequest{
			TenantID:    tenantID,
			ParentID:    currentParentID,
			Name:        component,
			DocType:     model.DocTypeDir,
			ObjectType:  objectType, // Use object context if available
			ObjectID:    objectID,
			CreatedUser: 1, // System user
		}

		// Get document service
		documentService, err := s.getDocumentService("")
		if err != nil {
			log.WithError(err).Error("Failed to get document service")
			return nil, fmt.Errorf("failed to get document service: %w", err)
		}

		newDoc, err := documentService.CreateDocument(ctx, createReq)
		if err != nil {
			log.WithError(err).Errorf("Failed to create folder: %s", component)
			return nil, fmt.Errorf("failed to create folder '%s': %w", component, err)
		}

		currentParentID = newDoc.ID
		log.Infof("Created new folder: %s (ID: %d) with object_type=%d object_id=%d",
			component, newDoc.ID, objectType, objectID)
	}

	log.Infof("Resolved hierarchical path: %s -> ParentID: %d, ObjectType: %d, ObjectID: %d",
		path, currentParentID, objectType, objectID)

	return &TargetParentResult{
		ParentID:   currentParentID,
		ObjectType: objectType,
		ObjectID:   objectID,
		TenantID:   tenantID,
	}, nil
}

// findObjectFolder finds an existing folder for the given object_type and object_id
func (s *autoDocService) findObjectFolder(ctx context.Context, tenantID uint64, objectType int, objectID uint64) (*model.Document, error) {
	log := logger.WithCtx(ctx, "findObjectFolder")

	// Query for folder with matching object_type and object_id
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("object_type", objectType),
			model.NewFilterE("object_id", objectID),
			model.NewFilterE("doc_type", model.DocTypeDir), // Only folders
			model.NewFilterE("status", 1),                  // Active only
		},
	}

	folder, err := s.documentRepo.FindOne(ctx, query)
	if err != nil {
		log.WithError(err).Errorf("Failed to query object folder object_type=%d object_id=%d", objectType, objectID)
		return nil, err
	}

	if folder == nil {
		log.Infof("No object folder found for object_type=%d object_id=%d", objectType, objectID)
		return nil, nil
	}

	log.Infof("Found object folder: folder_id=%d name=%s object_type=%d object_id=%d",
		folder.ID, folder.Name, folder.ObjectType, folder.ObjectID)

	return folder, nil
}

// ResolveProviderParentID is a shared utility function to resolve parent folder ID for any provider
// Used by both copy_folder and generate_document handlers
func ResolveProviderParentID(ctx context.Context, targetParent *TargetParentResult, tenantID uint64, provider string, gdriveService gdrive.DocumentService, mappingService MappingService) (string, error) {
	log := logger.WithCtx(ctx, "ResolveProviderParentID")

	log.Infof("Resolving %s parent folder object_type=%d object_id=%d parent_id=%d",
		provider, targetParent.ObjectType, targetParent.ObjectID, targetParent.ParentID)

	// Convert provider name to model constant
	var providerConstant string
	switch provider {
	case "gdrive":
		providerConstant = model.DocProviderGoogle
	case "sharepoint":
		providerConstant = model.DocProviderSharepoint
	default:
		return "", fmt.Errorf("unsupported provider: %s", provider)
	}

	// Provider-specific configuration and validation
	var rootID string
	if provider == "gdrive" {
		// Get tenant configuration for Google Drive
		tenantConfig, err := gdriveService.GetTenantConfig(ctx, tenantID)
		if err != nil {
			log.WithError(err).Errorf("Failed to get tenant Google Drive config tenant_id=%d", tenantID)
			return "", fmt.Errorf("failed to get tenant Google Drive config: %w", err)
		}

		// Validate Service Account compatibility
		gdriveClient := gdriveService.GetGDriveClient()
		err = gdriveClient.ValidateParentForServiceAccount(tenantConfig.RootID)
		if err != nil {
			log.WithError(err).Errorf("Parent folder validation failed for Service Account root_id=%s", tenantConfig.RootID)
			return "", fmt.Errorf("parent folder validation failed for Service Account: %w", err)
		}

		rootID = tenantConfig.RootID
	} else {
		// For SharePoint and other providers, we'll need to implement config lookup
		// For now, return error as SharePoint config is not implemented in this function
		return "", fmt.Errorf("provider %s configuration not implemented in ResolveProviderParentID", provider)
	}

	// If we have business object context (ObjectType and ObjectID), try to resolve via mapping
	if targetParent.ObjectType > 0 && targetParent.ObjectID > 0 {
		var docType string
		switch targetParent.ObjectType {
		case 1:
			docType = model.DocTypeClient
		case 3:
			docType = model.DocTypeMatter
		default:
			log.Warnf("Unsupported object type for folder resolution object_type=%d", targetParent.ObjectType)
			docType = ""
		}

		if docType != "" {
			// Look up DocumentMapping for the business entity using correct provider
			externalID, err := mappingService.GetExternalID(ctx, targetParent.ObjectID, docType, providerConstant, tenantID)
			if err != nil {
				log.WithError(err).Warnf("Failed to get external ID for %s %d in %s, falling back to root folder", docType, targetParent.ObjectID, provider)
			} else {
				log.Infof("Successfully resolved business context folder %s %d to %s external_id=%s", docType, targetParent.ObjectID, provider, externalID)
				return externalID, nil
			}
		}
	}

	// Fallback: use tenant's configured root folder
	log.Infof("Using tenant configured root folder for %s root_id=%s", provider, rootID)
	return rootID, nil
}

// ResolveGDriveParentID is a backward compatibility wrapper for existing callers
// Deprecated: Use ResolveProviderParentID with provider="gdrive" instead
func ResolveGDriveParentID(ctx context.Context, targetParent *TargetParentResult, tenantID uint64, gdriveService gdrive.DocumentService, mappingService MappingService) (string, error) {
	return ResolveProviderParentID(ctx, targetParent, tenantID, "gdrive", gdriveService, mappingService)
}
