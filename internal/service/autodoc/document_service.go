package autodoc

import (
	"context"
	"fmt"

	"bilabl/docman/domain/model"
)

// DocumentService defines the interface for document operations used by AutoDoc
type DocumentService interface {
	// BasicDocumentOperations Basic document operations
	BasicDocumentOperations

	// AdvancedFileOperations Advanced file operations
	AdvancedFileOperations
}

// BasicDocumentOperations defines core document CRUD operations
type BasicDocumentOperations interface {
	// CreateDocument creates a new document in the internal system
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*model.Document, error)

	// GetDocument retrieves a document by ID (supports both internal and external IDs)
	GetDocument(ctx context.Context, documentID uint64, tenantID uint64) (*model.Document, error)
	GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error)

	// UpdateDocument updates an existing document
	UpdateDocument(ctx context.Context, req *UpdateDocumentRequest) (*model.Document, error)

	// DeleteDocument deletes a document (supports both internal and external IDs)
	DeleteDocument(ctx context.Context, documentID uint64, tenantID uint64) error
	DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error

	// CopyDocument copies a document to a new location
	CopyDocument(ctx context.Context, req *CopyDocumentRequest) (*model.Document, error)

	// GetDocumentContent retrieves the content of a document
	GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error)

	// EnsureAutoDocRoot ensures AutoDoc root folder exists for tenant
	EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error)
}

// AdvancedFileOperations defines advanced file management operations
type AdvancedFileOperations interface {
	// CreateFolder creates a new folder at the specified path
	CreateFolder(ctx context.Context, req *CreateFolderRequest) (*model.Document, error)

	// CreateFolderWithResponse creates a folder and returns formatted response
	CreateFolderWithResponse(ctx context.Context, req *CreateFolderRequest) (*CreateFolderResponse, error)

	// MoveFile moves a file to a new location
	MoveFile(ctx context.Context, req *MoveFileRequest) (*model.Document, error)

	// CopyFile copies a file to a destination path
	CopyFile(ctx context.Context, req *CopyFileRequest) (*model.Document, error)

	// GetFileMetadata retrieves detailed metadata for a file
	GetFileMetadata(ctx context.Context, req *GetFileMetadataRequest) (*FileMetadata, error)

	// SetFilePermissions sets permissions for a file
	SetFilePermissions(ctx context.Context, req *SetFilePermissionsRequest) error

	// ListFiles lists files in a directory with filtering options
	ListFiles(ctx context.Context, req *ListFilesRequest) (*ListFilesResponse, error)

	// SearchFiles searches for files based on criteria
	SearchFiles(ctx context.Context, req *SearchFilesRequest) (*SearchFilesResponse, error)

	// GetFilePath gets the full path of a file
	GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error)

	// ValidatePath validates if a path is valid and accessible
	ValidatePath(ctx context.Context, path string, tenantID uint64) error
}

// CreateDocumentRequest represents a request to create a document
type CreateDocumentRequest struct {
	Name             string `json:"name" validate:"required"`
	ParentID         uint64 `json:"parent_id"`          // Internal parent ID (for internal provider)
	ExternalParentID string `json:"external_parent_id"` // External parent ID (for external providers like GDrive)
	TenantID         uint64 `json:"tenant_id" validate:"required"`
	ObjectType       int    `json:"object_type"`
	ObjectID         uint64 `json:"object_id"`
	DocType          int    `json:"doc_type"` // 1 = folder, 2 = file
	CreatedUser      uint64 `json:"created_user" validate:"required"`
	Key              string `json:"key,omitempty"` // File content key (for internal provider with content)
}

// UpdateDocumentRequest represents a request to update a document
type UpdateDocumentRequest struct {
	ID          uint64 `json:"id" validate:"required"` // Internal ID (for internal provider)
	ExternalID  string `json:"external_id"`            // External ID (for external providers like GDrive)
	Name        string `json:"name,omitempty"`
	TenantID    uint64 `json:"tenant_id" validate:"required"`
	UpdatedUser uint64 `json:"updated_user" validate:"required"`
}

// CopyDocumentRequest represents a request to copy a document
type CopyDocumentRequest struct {
	SourceID               uint64 `json:"source_id" validate:"required"`        // Internal source ID
	ExternalSourceID       string `json:"external_source_id"`                   // External source ID (for external providers)
	TargetParentID         uint64 `json:"target_parent_id" validate:"required"` // Internal target parent ID
	ExternalTargetParentID string `json:"external_target_parent_id"`            // External target parent ID
	NewName                string `json:"new_name" validate:"required"`
	TenantID               uint64 `json:"tenant_id" validate:"required"`
	CreatedUser            uint64 `json:"created_user" validate:"required"`
}

// CreateFolderRequest represents a request to create a folder
type CreateFolderRequest struct {
	Name             string `json:"name" validate:"required"`
	Path             string `json:"path,omitempty"`               // Optional path, if provided will create nested folders
	ParentID         uint64 `json:"parent_id,omitempty"`          // Internal parent folder ID
	ExternalParentID string `json:"external_parent_id,omitempty"` // External parent folder ID (for external providers)
	TenantID         uint64 `json:"tenant_id" validate:"required"`
	ObjectType       int    `json:"object_type,omitempty"` // Associated object type
	ObjectID         uint64 `json:"object_id,omitempty"`   // Associated object ID
	CreatedUser      uint64 `json:"created_user" validate:"required"`
}

// CreateFolderResponse represents the response for creating a folder
type CreateFolderResponse struct {
	ID         uint64 `json:"id"`          // Internal folder ID
	Name       string `json:"name"`        // Folder name
	ParentID   uint64 `json:"parent_id"`   // Internal parent folder ID
	ExternalID string `json:"external_id"` // External folder ID (for external providers)
	Path       string `json:"path"`        // Full folder path
	TenantID   uint64 `json:"tenant_id"`   // Tenant ID
}

// MoveFileRequest represents a request to move a file
type MoveFileRequest struct {
	FileID              uint64 `json:"file_id" validate:"required"`      // Internal file ID
	ExternalFileID      string `json:"external_file_id"`                 // External file ID (for external providers)
	NewParentID         uint64 `json:"new_parent_id,omitempty"`          // New internal parent folder ID
	ExternalNewParentID string `json:"external_new_parent_id,omitempty"` // New external parent folder ID
	NewPath             string `json:"new_path,omitempty"`               // New path (alternative to parent ID)
	NewName             string `json:"new_name,omitempty"`               // Optional new name
	TenantID            uint64 `json:"tenant_id" validate:"required"`
	UpdatedUser         uint64 `json:"updated_user" validate:"required"`
	PreserveLinks       bool   `json:"preserve_links,omitempty"` // Whether to preserve existing links
}

// CopyFileRequest represents a request to copy a file
type CopyFileRequest struct {
	SourceID             uint64 `json:"source_id" validate:"required"`     // Internal source ID
	ExternalSourceID     string `json:"external_source_id"`                // External source ID
	DestParentID         uint64 `json:"dest_parent_id,omitempty"`          // Internal destination parent folder ID
	ExternalDestParentID string `json:"external_dest_parent_id,omitempty"` // External destination parent folder ID
	DestPath             string `json:"dest_path,omitempty"`               // Destination path (alternative to parent ID)
	NewName              string `json:"new_name" validate:"required"`
	TenantID             uint64 `json:"tenant_id" validate:"required"`
	CreatedUser          uint64 `json:"created_user" validate:"required"`
	CopyContent          bool   `json:"copy_content,omitempty"`  // Whether to copy file content
	CopyMetadata         bool   `json:"copy_metadata,omitempty"` // Whether to copy metadata
}

// GetFileMetadataRequest represents a request to get file metadata
type GetFileMetadataRequest struct {
	FileID         uint64 `json:"file_id" validate:"required"` // Internal file ID
	ExternalFileID string `json:"external_file_id"`            // External file ID (for external providers)
	TenantID       uint64 `json:"tenant_id" validate:"required"`
}

// SetFilePermissionsRequest represents a request to set file permissions
type SetFilePermissionsRequest struct {
	FileID         uint64                 `json:"file_id" validate:"required"` // Internal file ID
	ExternalFileID string                 `json:"external_file_id"`            // External file ID (for external providers)
	TenantID       uint64                 `json:"tenant_id" validate:"required"`
	Permissions    map[string]interface{} `json:"permissions" validate:"required"`
	UpdatedUser    uint64                 `json:"updated_user" validate:"required"`
}

// ListFilesRequest represents a request to list files
type ListFilesRequest struct {
	ParentID         uint64 `json:"parent_id,omitempty"`          // Internal parent ID
	ExternalParentID string `json:"external_parent_id,omitempty"` // External parent ID (for external providers)
	Path             string `json:"path,omitempty"`
	TenantID         uint64 `json:"tenant_id" validate:"required"`
	FileType         int    `json:"file_type,omitempty"`  // Filter by file type (1=folder, 2=file)
	Recursive        bool   `json:"recursive,omitempty"`  // Whether to list recursively
	Page             int    `json:"page,omitempty"`       // Page number for pagination
	Limit            int    `json:"limit,omitempty"`      // Items per page
	SortBy           string `json:"sort_by,omitempty"`    // Sort field (name, created_at, size)
	SortOrder        string `json:"sort_order,omitempty"` // Sort order (asc, desc)
}

// SearchFilesRequest represents a request to search files
type SearchFilesRequest struct {
	Query            string   `json:"query" validate:"required"` // Search query
	TenantID         uint64   `json:"tenant_id" validate:"required"`
	ParentID         uint64   `json:"parent_id,omitempty"`          // Internal parent ID - limit search to specific folder
	ExternalParentID string   `json:"external_parent_id,omitempty"` // External parent ID (for external providers)
	FileType         int      `json:"file_type,omitempty"`          // Filter by file type
	Extensions       []string `json:"extensions,omitempty"`         // Filter by file extensions
	MinSize          int64    `json:"min_size,omitempty"`           // Minimum file size
	MaxSize          int64    `json:"max_size,omitempty"`           // Maximum file size
	CreatedFrom      string   `json:"created_from,omitempty"`       // Created date range start
	CreatedTo        string   `json:"created_to,omitempty"`         // Created date range end
	Page             int      `json:"page,omitempty"`               // Page number for pagination
	Limit            int      `json:"limit,omitempty"`              // Items per page
}

// GetFileContentRequest represents a request to get file content
type GetFileContentRequest struct {
	FileID         uint64 `json:"file_id" validate:"required"` // Internal file ID
	ExternalFileID string `json:"external_file_id"`            // External file ID (for external providers)
	TenantID       uint64 `json:"tenant_id" validate:"required"`
}

// DeleteFileRequest represents a request to delete a file
type DeleteFileRequest struct {
	FileID         uint64 `json:"file_id" validate:"required"` // Internal file ID
	ExternalFileID string `json:"external_file_id"`            // External file ID (for external providers)
	TenantID       uint64 `json:"tenant_id" validate:"required"`
	Force          bool   `json:"force,omitempty"` // Force deletion without confirmation
}

// UploadFileRequest represents a request to upload a file
type UploadFileRequest struct {
	Name             string `json:"name" validate:"required"`     // File name
	ParentID         uint64 `json:"parent_id,omitempty"`          // Internal parent folder ID
	ExternalParentID string `json:"external_parent_id,omitempty"` // External parent folder ID (for external providers)
	TenantID         uint64 `json:"tenant_id" validate:"required"`
	Content          []byte `json:"content,omitempty"`   // File content
	MimeType         string `json:"mime_type,omitempty"` // MIME type
	CreatedUser      uint64 `json:"created_user" validate:"required"`
	Overwrite        bool   `json:"overwrite,omitempty"` // Whether to overwrite existing file
}

// DownloadFileRequest represents a request to download a file
type DownloadFileRequest struct {
	FileID           uint64 `json:"file_id" validate:"required"`  // Internal file ID
	ExternalFileID   string `json:"external_file_id"`             // External file ID (for external providers)
	ParentID         uint64 `json:"parent_id,omitempty"`          // Internal parent folder ID
	ExternalParentID string `json:"external_parent_id,omitempty"` // External parent folder ID (for external providers)
	TenantID         uint64 `json:"tenant_id" validate:"required"`
}

// FileMetadata represents detailed file metadata
type FileMetadata struct {
	ID          uint64                 `json:"id"`
	Name        string                 `json:"name"`
	Path        string                 `json:"path"`
	Size        int64                  `json:"size"`
	DocType     int                    `json:"doc_type"`
	MimeType    string                 `json:"mime_type,omitempty"`
	Extension   string                 `json:"extension,omitempty"`
	Checksum    string                 `json:"checksum,omitempty"`
	CreatedAt   string                 `json:"created_at"`
	UpdatedAt   string                 `json:"updated_at"`
	CreatedUser uint64                 `json:"created_user"`
	UpdatedUser uint64                 `json:"updated_user"`
	TenantID    uint64                 `json:"tenant_id"`
	ObjectType  int                    `json:"object_type"`
	ObjectID    uint64                 `json:"object_id"`
	ParentID    uint64                 `json:"parent_id"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// DocumentResponse represents a document in API responses (supports both internal and external providers)
type DocumentResponse struct {
	ID          string `json:"id"` // String ID (uint64 for internal, string for external providers)
	Name        string `json:"name"`
	ParentID    string `json:"parent_id,omitempty"` // String parent ID
	TenantID    uint64 `json:"tenant_id"`
	ObjectType  int    `json:"object_type"`
	ObjectID    uint64 `json:"object_id"`
	Key         string `json:"key,omitempty"` // External key/reference
	SubObjectID uint64 `json:"sub_object_id"`
	Type        int    `json:"type"`
	DocType     int    `json:"doc_type"` // 1=folder, 2=file
	Status      int    `json:"status"`
	Note        string `json:"note,omitempty"`
	Size        int64  `json:"size"`
	CreatedAt   string `json:"created_at,omitempty"`
	UpdatedAt   string `json:"updated_at,omitempty"`
	CreatedUser uint64 `json:"created_user"`
	UpdatedUser uint64 `json:"updated_user"`
	// External provider specific fields
	ExternalID   string `json:"external_id,omitempty"`   // Original external ID (Google Drive ID, etc.)
	WebViewLink  string `json:"web_view_link,omitempty"` // Web view URL
	DownloadLink string `json:"download_link,omitempty"` // Download URL
	ContentType  string `json:"content_type,omitempty"`  // MIME type
}

// ListFilesResponse represents the response for listing files
type ListFilesResponse struct {
	Cwd        *DocumentResponse   `json:"cwd"` // Current working directory info
	Data       []*DocumentResponse `json:"data"`
	TotalCount int64               `json:"total_count"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	HasMore    bool                `json:"has_more"`
}

// SearchFilesResponse represents the response for searching files
type SearchFilesResponse struct {
	Cwd        *DocumentResponse   `json:"cwd"` // Current working directory info
	Data       []*DocumentResponse `json:"data"`
	TotalCount int64               `json:"total_count"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	HasMore    bool                `json:"has_more"`
	Query      string              `json:"query"`
}

// Converter functions

// ConvertModelToResponse converts model.Document to DocumentResponse
func ConvertModelToResponse(doc *model.Document) *DocumentResponse {
	if doc == nil {
		return nil
	}

	return &DocumentResponse{
		ID:          fmt.Sprintf("%d", doc.ID), // Convert uint64 to string
		Name:        doc.Name,
		ParentID:    fmt.Sprintf("%d", doc.ParentID),
		TenantID:    doc.TenantID,
		ObjectType:  doc.ObjectType,
		ObjectID:    doc.ObjectID,
		Key:         doc.Key,
		SubObjectID: doc.SubObjectID,
		Type:        doc.Type,
		DocType:     doc.DocType,
		Status:      doc.Status,
		Note:        doc.Note,
		Size:        doc.Size,
		CreatedAt:   doc.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   doc.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		CreatedUser: doc.CreatedUser,
		UpdatedUser: doc.UpdatedUser,
		ExternalID:  doc.Key, // Use Key as external ID for compatibility
	}
}

// ConvertModelsToResponses converts slice of model.Document to slice of DocumentResponse
func ConvertModelsToResponses(docs []*model.Document) []*DocumentResponse {
	if docs == nil {
		return nil
	}

	responses := make([]*DocumentResponse, len(docs))
	for i, doc := range docs {
		responses[i] = ConvertModelToResponse(doc)
	}
	return responses
}

// ConvertGDriveToResponse converts Google Drive DocumentResponse to AutoDoc DocumentResponse
func ConvertGDriveToResponse(gdriveDoc interface{}, tenantID uint64) *DocumentResponse {
	// Handle different Google Drive response types
	switch doc := gdriveDoc.(type) {
	case map[string]interface{}:
		// Handle generic map response
		return convertMapToDocumentResponse(doc, tenantID)
	default:
		// Handle other types if needed
		return nil
	}
}

// convertMapToDocumentResponse converts map[string]interface{} to DocumentResponse
func convertMapToDocumentResponse(doc map[string]interface{}, tenantID uint64) *DocumentResponse {
	if doc == nil {
		return nil
	}

	// Helper function to safely get string value
	getString := func(key string) string {
		if val, ok := doc[key]; ok {
			if str, ok := val.(string); ok {
				return str
			}
		}
		return ""
	}

	// Helper function to safely get int64 value
	getInt64 := func(key string) int64 {
		if val, ok := doc[key]; ok {
			switch v := val.(type) {
			case int64:
				return v
			case int:
				return int64(v)
			case float64:
				return int64(v)
			}
		}
		return 0
	}

	// Helper function to safely get bool value
	getBool := func(key string) bool {
		if val, ok := doc[key]; ok {
			if b, ok := val.(bool); ok {
				return b
			}
		}
		return false
	}

	// Determine document type
	docType := 2 // Default to file
	contentType := getString("content_type")
	if contentType == "application/vnd.google-apps.folder" || !getBool("is_file") {
		docType = 1 // Folder
	}

	return &DocumentResponse{
		ID:           getString("drive_file_id"), // Use Google Drive ID directly
		Name:         getString("name"),
		TenantID:     tenantID,
		DocType:      docType,
		Status:       1, // Active
		Size:         getInt64("size"),
		CreatedAt:    getString("created_at"),
		UpdatedAt:    getString("updated_at"),
		ExternalID:   getString("drive_file_id"), // Store original Google Drive ID
		WebViewLink:  getString("web_view_link"),
		DownloadLink: getString("download_link"),
		ContentType:  contentType,
		Key:          getString("drive_file_id"), // Use Google Drive ID as key
	}
}
