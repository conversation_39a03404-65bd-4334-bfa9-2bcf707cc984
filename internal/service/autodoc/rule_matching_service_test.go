package autodoc_test

import (
	"context"
	"testing"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/autodoc"
	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// RuleMatchingServiceTestSuite tests rule matching functionality with mockery-generated mocks
type RuleMatchingServiceTestSuite struct {
	suite.Suite
	mockAutoDocService  *autodocmocks.MockAutoDocService
	ruleMatchingService autodoc.RuleMatchingService
	ctx                 context.Context
}

func (suite *RuleMatchingServiceTestSuite) SetupTest() {
	suite.mockAutoDocService = autodocmocks.NewMockAutoDocService(suite.T())
	suite.ruleMatchingService = autodoc.NewRuleMatchingService(suite.mockAutoDocService)
	suite.ctx = context.Background()
}

func TestRuleMatchingServiceTestSuite(t *testing.T) {
	suite.Run(t, new(RuleMatchingServiceTestSuite))
}

// TestImprovedErrorMessages tests the improved error messages for field extraction
func TestImprovedErrorMessages(t *testing.T) {
	service := autodoc.NewRuleMatchingService(nil)

	testCases := []struct {
		name         string
		eventData    map[string]interface{}
		triggerRules model.TriggerRulesMap
		expectLog    string // We'll check if detailed logging is working
	}{
		{
			name: "field not found at root level",
			eventData: map[string]interface{}{
				"id":   123,
				"name": "Test",
			},
			triggerRules: model.TriggerRulesMap{
				"missing_field": "some_value",
			},
			expectLog: "Failed to extract field value - detailed analysis",
		},
		{
			name: "nested field not found",
			eventData: map[string]interface{}{
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"stage_text": "initial-1",
					},
				},
			},
			triggerRules: model.TriggerRulesMap{
				"extra.current.missing_field": "some_value",
			},
			expectLog: "Failed to extract field value - detailed analysis",
		},
		{
			name: "cannot traverse non-object",
			eventData: map[string]interface{}{
				"extra": map[string]interface{}{
					"stage_text": "initial-1",
				},
			},
			triggerRules: model.TriggerRulesMap{
				"extra.stage_text.something": "some_value",
			},
			expectLog: "Failed to extract field value - detailed analysis",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// This will trigger the improved error logging
			matches, err := service.EvaluateTriggerConditions(context.Background(), tc.triggerRules, tc.eventData)
			assert.NoError(t, err)   // Should not return error, just log and return false
			assert.False(t, matches) // Should not match due to field extraction failure

			// The detailed error logging will be visible in debug logs
			// In a real scenario, you would capture logs to verify the improved messages
			t.Logf("✅ Test case '%s' completed - improved error logging should show detailed field analysis", tc.name)
		})
	}
}

// TestMatchRules_Success tests successful rule matching
func (suite *RuleMatchingServiceTestSuite) TestMatchRules_Success() {
	// Arrange
	tenantID := uint64(123)
	eventType := "matter.create"
	eventData := map[string]interface{}{
		"matter_id": "12345",
		"client_id": "67890",
	}

	expectedRules := []*model.DocumentAutomationRule{
		{
			Model:       model.Model{ID: 1},
			TenantID:    tenantID,
			Name:        "Test Rule",
			TriggerType: eventType,
			IsActive:    true,
		},
	}

	// Mock: AutoDocService should return active rules
	suite.mockAutoDocService.On("GetActiveRules", mock.Anything, tenantID).Return(expectedRules, nil)

	// Act
	matchedRules, err := suite.ruleMatchingService.MatchRules(suite.ctx, tenantID, eventType, eventData)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), matchedRules, 1)
	assert.Equal(suite.T(), expectedRules[0].ID, matchedRules[0].ID)

	suite.mockAutoDocService.AssertExpectations(suite.T())
}

// TestMatchRules_NoActiveRules tests when no active rules exist
func (suite *RuleMatchingServiceTestSuite) TestMatchRules_NoActiveRules() {
	// Arrange
	tenantID := uint64(123)
	eventType := "matter.create"
	eventData := map[string]interface{}{}

	// Mock: AutoDocService should return empty rules
	suite.mockAutoDocService.On("GetActiveRules", mock.Anything, tenantID).Return([]*model.DocumentAutomationRule{}, nil)

	// Act
	matchedRules, err := suite.ruleMatchingService.MatchRules(suite.ctx, tenantID, eventType, eventData)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Empty(suite.T(), matchedRules)

	suite.mockAutoDocService.AssertExpectations(suite.T())
}
