package autodoc

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"bilabl/docman/domain/model"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// ActionHandler defines the interface for executing different types of rule actions
type ActionHandler interface {
	// Execute executes the action with the given context and parameters
	Execute(ctx context.Context, params *ActionExecutionParams) error

	// Test tests the action without actually executing it
	Test(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error)

	// GetActionType returns the action type this handler supports
	GetActionType() string

	// Validate validates the action parameters
	Validate(ctx context.Context, params *ActionExecutionParams) error
}

// ActionExecutionParams contains parameters for action execution
type ActionExecutionParams struct {
	TenantID        uint64                 `json:"tenant_id"`
	Action          model.RuleAction       `json:"action"`
	PlaceholderData map[string]interface{} `json:"placeholder_data"`
	RuleID          uint64                 `json:"rule_id"`
}

// ActionHandlerRegistry manages action handlers
type ActionHandlerRegistry interface {
	// <PERSON><PERSON><PERSON><PERSON> registers an action handler
	<PERSON><PERSON><PERSON><PERSON>(handler ActionHandler) error

	// <PERSON><PERSON><PERSON><PERSON> gets an action handler by action type
	GetHandler(actionType string) (ActionHandler, error)

	// GetSupportedActions returns list of supported action types
	GetSupportedActions() []string

	// ExecuteAction executes an action using the appropriate handler
	ExecuteAction(ctx context.Context, params *ActionExecutionParams) error

	// TestAction tests an action using the appropriate handler
	TestAction(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error)
}

// actionHandlerRegistry implements ActionHandlerRegistry
type actionHandlerRegistry struct {
	handlers map[string]ActionHandler
}

// NewActionHandlerRegistry creates a new action handler registry
func NewActionHandlerRegistry() ActionHandlerRegistry {
	return &actionHandlerRegistry{
		handlers: make(map[string]ActionHandler),
	}
}

// RegisterHandler registers an action handler
func (r *actionHandlerRegistry) RegisterHandler(handler ActionHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}

	actionType := handler.GetActionType()
	if actionType == "" {
		return fmt.Errorf("action type cannot be empty")
	}

	if _, exists := r.handlers[actionType]; exists {
		return fmt.Errorf("handler for action type %s already registered", actionType)
	}

	r.handlers[actionType] = handler
	return nil
}

// GetHandler gets an action handler by action type
func (r *actionHandlerRegistry) GetHandler(actionType string) (ActionHandler, error) {
	handler, exists := r.handlers[actionType]
	if !exists {
		return nil, fmt.Errorf("no handler registered for action type: %s", actionType)
	}
	return handler, nil
}

// GetSupportedActions returns list of supported action types
func (r *actionHandlerRegistry) GetSupportedActions() []string {
	actions := make([]string, 0, len(r.handlers))
	for actionType := range r.handlers {
		actions = append(actions, actionType)
	}
	return actions
}

// ExecuteAction executes an action using the appropriate handler
func (r *actionHandlerRegistry) ExecuteAction(ctx context.Context, params *ActionExecutionParams) error {
	log := logger.WithCtx(ctx, "ExecuteAction")

	if params == nil {
		return fmt.Errorf("action execution parameters cannot be nil")
	}

	handler, err := r.GetHandler(params.Action.ActionType)
	if err != nil {
		log.WithError(err).Errorf("Failed to get handler for action type=%s", params.Action.ActionType)
		return fmt.Errorf("failed to get handler: %w", err)
	}

	// Validate parameters
	if err := handler.Validate(ctx, params); err != nil {
		log.WithError(err).Errorf("Action validation failed type=%s", params.Action.ActionType)
		return fmt.Errorf("action validation failed: %w", err)
	}

	// Execute action
	log.Infof("Executing action type=%s rule_id=%d tenant_id=%d",
		params.Action.ActionType, params.RuleID, params.TenantID)

	if err := handler.Execute(ctx, params); err != nil {
		log.WithError(err).Errorf("Action execution failed type=%s rule_id=%d",
			params.Action.ActionType, params.RuleID)
		return fmt.Errorf("action execution failed: %w", err)
	}

	log.Infof("Action executed successfully type=%s rule_id=%d",
		params.Action.ActionType, params.RuleID)
	return nil
}

// TestAction tests an action using the appropriate handler
func (r *actionHandlerRegistry) TestAction(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error) {
	log := logger.WithCtx(ctx, "TestAction")

	if params == nil {
		return nil, fmt.Errorf("action execution parameters cannot be nil")
	}

	handler, err := r.GetHandler(params.Action.ActionType)
	if err != nil {
		log.WithError(err).Errorf("Failed to get handler for action type=%s", params.Action.ActionType)
		return &ActionTestResult{
			ActionType: params.Action.ActionType,
			SourcePath: params.Action.SourcePath,
			TargetPath: params.Action.TargetPath,
			Success:    false,
			Error:      err.Error(),
		}, nil
	}

	// Test action
	log.Infof("Testing action type=%s rule_id=%d tenant_id=%d",
		params.Action.ActionType, params.RuleID, params.TenantID)

	result, err := handler.Test(ctx, params)
	if err != nil {
		log.WithError(err).Errorf("Action test failed type=%s rule_id=%d",
			params.Action.ActionType, params.RuleID)
		return &ActionTestResult{
			ActionType: params.Action.ActionType,
			SourcePath: params.Action.SourcePath,
			TargetPath: params.Action.TargetPath,
			Success:    false,
			Error:      err.Error(),
		}, nil
	}

	log.Infof("Action test completed type=%s rule_id=%d success=%t",
		params.Action.ActionType, params.RuleID, result.Success)
	return result, nil
}

// BaseActionHandler provides common functionality for action handlers
type BaseActionHandler struct {
	actionType string
}

// NewBaseActionHandler creates a new base action handler
func NewBaseActionHandler(actionType string) *BaseActionHandler {
	return &BaseActionHandler{
		actionType: actionType,
	}
}

// GetActionType returns the action type
func (h *BaseActionHandler) GetActionType() string {
	return h.actionType
}

// Validate provides basic validation for action parameters
func (h *BaseActionHandler) Validate(ctx context.Context, params *ActionExecutionParams) error {
	if params == nil {
		return fmt.Errorf("action execution parameters cannot be nil")
	}

	if params.TenantID == 0 {
		return fmt.Errorf("tenant ID cannot be zero")
	}

	if params.Action.ActionType != h.actionType {
		return fmt.Errorf("action type mismatch: expected %s, got %s",
			h.actionType, params.Action.ActionType)
	}

	if params.Action.SourcePath == "" {
		return fmt.Errorf("source path cannot be empty")
	}

	if params.Action.TargetPath == "" {
		return fmt.Errorf("target path cannot be empty")
	}

	return nil
}

// ReplacePlaceholders replaces placeholders in a string with actual data
// Supports nested field access using dot notation (e.g., {body.name}, {client.ShortName})
func ReplacePlaceholders(template string, placeholderData map[string]interface{}) string {
	if placeholderData == nil {
		return template
	}

	result := template

	// Replace simple placeholders first
	for key, value := range placeholderData {
		placeholder := fmt.Sprintf("{%s}", key)
		if valueStr, ok := value.(string); ok {
			result = strings.ReplaceAll(result, placeholder, valueStr)
		} else {
			result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
		}
	}

	// Handle nested field access (e.g., {body.name}, {client.ShortName})
	result = replaceNestedPlaceholders(result, placeholderData)

	return result
}

// replaceNestedPlaceholders handles nested field access in placeholders
func replaceNestedPlaceholders(template string, data map[string]interface{}) string {
	result := template

	// Find all {...} patterns that contain dots
	placeholderPattern := `\{([^}]+\.[^}]+)\}`
	re := regexp.MustCompile(placeholderPattern)

	matches := re.FindAllStringSubmatch(template, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			fullPlaceholder := match[0] // e.g., "{client.ShortName}"
			fieldPath := match[1]       // e.g., "client.ShortName"

			// Extract nested value
			if value := extractNestedValue(fieldPath, data); value != nil {
				if valueStr, ok := value.(string); ok {
					result = strings.ReplaceAll(result, fullPlaceholder, valueStr)
				} else {
					result = strings.ReplaceAll(result, fullPlaceholder, fmt.Sprintf("%v", value))
				}
			}
		}
	}

	return result
}

// extractNestedValue extracts a nested value from data using dot notation
func extractNestedValue(fieldPath string, data map[string]interface{}) interface{} {
	parts := strings.Split(fieldPath, ".")
	var current interface{} = data

	for _, part := range parts {
		if current == nil {
			return nil
		}

		if currentMap, ok := current.(map[string]interface{}); ok {
			if value, exists := currentMap[part]; exists {
				current = value
			} else {
				return nil
			}
		} else {
			return nil
		}
	}

	return current
}
