package autodoc

import (
	"context"
	"testing"

	"bilabl/docman/domain/model"

	"code.mybil.net/gophers/gokit/clients/settings"
	"github.com/stretchr/testify/assert"
)

// TestTargetProviderResolution tests the target provider resolution logic
func TestTargetProviderResolution(t *testing.T) {
	ctx := context.Background()

	t.Run("ExplicitTargetProvider_ShouldUseExplicitValue", func(t *testing.T) {
		// Create mock settings client
		mockSettingsClient := &MockSettingsClient{}
		defaultDMSService := NewDefaultDMSService(mockSettingsClient)

		// Create rule execution engine with mock
		engine := &ruleExecutionEngine{
			defaultDMSService: defaultDMSService,
		}

		// Create action with explicit target provider
		action := model.RuleAction{
			ActionType:     "copy_file",
			SourcePath:     "templates/test.docx",
			TargetPath:     "{client_folder}/test.docx",
			Provider:       "internal",
			TargetProvider: "gdrive", // Explicit target provider
		}

		// Test target provider resolution
		targetProvider := engine.resolveTargetProvider(ctx, action, 123)

		// Should use explicit value, not call settings service
		assert.Equal(t, "gdrive", targetProvider)
		mockSettingsClient.AssertNotCalled(t, "Fetch")
	})

	t.Run("EmptyTargetProvider_ShouldUseTenantDefault", func(t *testing.T) {
		// Create mock settings client
		mockSettingsClient := &MockSettingsClient{}
		defaultDMSService := NewDefaultDMSService(mockSettingsClient)

		// Mock successful fetch of tenant setting
		mockSetting := &settings.SettingItem{
			Key:   "default_dms",
			Value: "sharepoint",
		}
		mockSettingsClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(mockSetting, nil)

		// Create rule execution engine with mock
		engine := &ruleExecutionEngine{
			defaultDMSService: defaultDMSService,
		}

		// Create action without target provider
		action := model.RuleAction{
			ActionType:     "copy_file",
			SourcePath:     "templates/test.docx",
			TargetPath:     "{client_folder}/test.docx",
			Provider:       "internal",
			TargetProvider: "", // Empty target provider
		}

		// Test target provider resolution
		targetProvider := engine.resolveTargetProvider(ctx, action, 123)

		// Should use tenant default from settings
		assert.Equal(t, "sharepoint", targetProvider)
		mockSettingsClient.AssertExpectations(t)
	})

	t.Run("EmptyTargetProvider_SettingsFail_ShouldUseFallback", func(t *testing.T) {
		// Create mock settings client
		mockSettingsClient := &MockSettingsClient{}
		defaultDMSService := NewDefaultDMSService(mockSettingsClient)

		// Mock failed fetch of tenant setting
		mockSettingsClient.On("Fetch", ctx, "default_dms", uint64(123), uint64(0)).Return(nil, assert.AnError)

		// Create rule execution engine with mock
		engine := &ruleExecutionEngine{
			defaultDMSService: defaultDMSService,
		}

		// Create action without target provider
		action := model.RuleAction{
			ActionType:     "copy_file",
			SourcePath:     "templates/test.docx",
			TargetPath:     "{client_folder}/test.docx",
			Provider:       "internal",
			TargetProvider: "", // Empty target provider
		}

		// Test target provider resolution
		targetProvider := engine.resolveTargetProvider(ctx, action, 123)

		// Should use fallback when settings fail
		assert.Equal(t, "internal", targetProvider)
		mockSettingsClient.AssertExpectations(t)
	})
}

// TestRuleActionTargetProviderMethods tests the new methods on RuleAction
func TestRuleActionTargetProviderMethods(t *testing.T) {
	t.Run("GetTargetProvider_WithValue", func(t *testing.T) {
		action := model.RuleAction{
			TargetProvider: "gdrive",
		}

		assert.Equal(t, "gdrive", action.GetTargetProvider())
	})

	t.Run("GetTargetProvider_Empty", func(t *testing.T) {
		action := model.RuleAction{
			TargetProvider: "",
		}

		assert.Equal(t, "", action.GetTargetProvider())
	})

	t.Run("SetTargetProvider", func(t *testing.T) {
		action := model.RuleAction{}
		action.SetTargetProvider("sharepoint")

		assert.Equal(t, "sharepoint", action.TargetProvider)
		assert.Equal(t, "sharepoint", action.GetTargetProvider())
	})

	t.Run("IsInternalTargetProvider", func(t *testing.T) {
		// Test with internal target provider
		action1 := model.RuleAction{TargetProvider: "internal"}
		assert.True(t, action1.IsInternalTargetProvider())

		// Test with external target provider
		action2 := model.RuleAction{TargetProvider: "gdrive"}
		assert.False(t, action2.IsInternalTargetProvider())

		// Test with empty target provider
		action3 := model.RuleAction{TargetProvider: ""}
		assert.False(t, action3.IsInternalTargetProvider())
	})
}

// TestProviderCombinations tests different provider combinations
func TestProviderCombinations(t *testing.T) {
	testCases := []struct {
		name           string
		sourceProvider string
		targetProvider string
		expectedType   string
	}{
		{
			name:           "Internal_to_Internal",
			sourceProvider: "internal",
			targetProvider: "internal",
			expectedType:   "same_provider",
		},
		{
			name:           "Internal_to_GDrive",
			sourceProvider: "internal",
			targetProvider: "gdrive",
			expectedType:   "cross_provider",
		},
		{
			name:           "GDrive_to_SharePoint",
			sourceProvider: "gdrive",
			targetProvider: "sharepoint",
			expectedType:   "cross_provider",
		},
		{
			name:           "GDrive_to_GDrive",
			sourceProvider: "gdrive",
			targetProvider: "gdrive",
			expectedType:   "same_provider",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			action := model.RuleAction{
				Provider:       tc.sourceProvider,
				TargetProvider: tc.targetProvider,
			}

			sourceProvider := action.GetProvider()
			targetProvider := action.GetTargetProvider()

			assert.Equal(t, tc.sourceProvider, sourceProvider)
			assert.Equal(t, tc.targetProvider, targetProvider)

			// Determine operation type
			var operationType string
			if sourceProvider == targetProvider {
				operationType = "same_provider"
			} else {
				operationType = "cross_provider"
			}

			assert.Equal(t, tc.expectedType, operationType)
		})
	}
}
