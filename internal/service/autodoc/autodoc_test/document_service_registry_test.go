package autodoc_test

import (
	"fmt"
	"testing"
	"time"

	"bilabl/docman/internal/service/autodoc"
	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/stretchr/testify/assert"
)

func TestNewDocumentServiceRegistry(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	assert.NotNil(t, registry)
	assert.Equal(t, "internal", registry.GetDefaultProvider())
	assert.Empty(t, registry.ListProviders())
}

func TestDocumentServiceRegistry_RegisterProvider(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService := autodocmocks.NewMockDocumentService(t)

	// Test successful registration
	err := registry.RegisterProvider("test", mockService)
	assert.NoError(t, err)
	assert.True(t, registry.IsProviderRegistered("test"))

	// Test duplicate registration
	err = registry.RegisterProvider("test", mockService)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already registered")

	// Test empty provider name
	err = registry.RegisterProvider("", mockService)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot be empty")

	// Test nil service
	err = registry.RegisterProvider("nil-service", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot be nil")
}

func TestDocumentServiceRegistry_GetProvider(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService := autodocmocks.NewMockDocumentService(t)

	// Register a provider
	err := registry.RegisterProvider("test", mockService)
	assert.NoError(t, err)

	// Test getting existing provider
	service, err := registry.GetProvider("test")
	assert.NoError(t, err)
	assert.Equal(t, mockService, service)

	// Test getting non-existent provider
	_, err = registry.GetProvider("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not registered")

	// Test getting provider with empty name (should return default)
	// First register internal provider
	internalService := autodocmocks.NewMockDocumentService(t)
	err = registry.RegisterProvider("internal", internalService)
	assert.NoError(t, err)

	service, err = registry.GetProvider("")
	assert.NoError(t, err)
	assert.Equal(t, internalService, service)
}

func TestDocumentServiceRegistry_SetDefaultProvider(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService := autodocmocks.NewMockDocumentService(t)

	// Test setting default to non-existent provider
	err := registry.SetDefaultProvider("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not registered")

	// Register a provider and set as default
	err = registry.RegisterProvider("test", mockService)
	assert.NoError(t, err)

	err = registry.SetDefaultProvider("test")
	assert.NoError(t, err)
	assert.Equal(t, "test", registry.GetDefaultProvider())

	// Test empty provider name
	err = registry.SetDefaultProvider("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot be empty")
}

func TestDocumentServiceRegistry_ListProviders(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService1 := autodocmocks.NewMockDocumentService(t)
	mockService2 := autodocmocks.NewMockDocumentService(t)

	// Initially empty
	providers := registry.ListProviders()
	assert.Empty(t, providers)

	// Register providers
	err := registry.RegisterProvider("provider1", mockService1)
	assert.NoError(t, err)

	err = registry.RegisterProvider("provider2", mockService2)
	assert.NoError(t, err)

	// Check list
	providers = registry.ListProviders()
	assert.Len(t, providers, 2)
	assert.Contains(t, providers, "provider1")
	assert.Contains(t, providers, "provider2")
}

func TestDocumentServiceRegistry_UnregisterProvider(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService := autodocmocks.NewMockDocumentService(t)

	// Test unregistering non-existent provider
	err := registry.UnregisterProvider("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not registered")

	// Register and unregister provider
	err = registry.RegisterProvider("test", mockService)
	assert.NoError(t, err)

	err = registry.UnregisterProvider("test")
	assert.NoError(t, err)
	assert.False(t, registry.IsProviderRegistered("test"))

	// Test unregistering default provider
	err = registry.RegisterProvider("internal", mockService)
	assert.NoError(t, err)

	err = registry.UnregisterProvider("internal")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot unregister default provider")

	// Test empty provider name
	err = registry.UnregisterProvider("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot be empty")
}

func TestDocumentServiceRegistry_GetProviderWithFallback(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	internalService := autodocmocks.NewMockDocumentService(t)
	testService := autodocmocks.NewMockDocumentService(t)

	// Register providers
	err := registry.RegisterProvider("internal", internalService)
	assert.NoError(t, err)

	err = registry.RegisterProvider("test", testService)
	assert.NoError(t, err)

	// Test getting existing provider
	service, providerName, err := registry.GetProviderWithFallback("test")
	assert.NoError(t, err)
	assert.Equal(t, testService, service)
	assert.Equal(t, "test", providerName)

	// Test fallback to default
	service, providerName, err = registry.GetProviderWithFallback("nonexistent")
	assert.NoError(t, err)
	assert.Equal(t, internalService, service)
	assert.Equal(t, "internal", providerName)

	// Test empty provider name (should use default)
	service, providerName, err = registry.GetProviderWithFallback("")
	assert.NoError(t, err)
	assert.Equal(t, internalService, service)
	assert.Equal(t, "internal", providerName)
}



func TestDocumentServiceRegistry_ConcurrentAccess(t *testing.T) {
	registry := autodoc.NewDocumentServiceRegistry()
	mockService := autodocmocks.NewMockDocumentService(t)

	// Test concurrent registration and access
	done := make(chan bool, 10)

	// Concurrent registrations
	for i := 0; i < 5; i++ {
		go func(id int) {
			err := registry.RegisterProvider(fmt.Sprintf("provider%d", id), mockService)
			assert.NoError(t, err)
			done <- true
		}(i)
	}

	// Concurrent reads
	for i := 0; i < 5; i++ {
		go func() {
			providers := registry.ListProviders()
			assert.True(t, len(providers) >= 0) // Should not panic
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		select {
		case <-done:
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for concurrent operations")
		}
	}

	// Verify final state
	providers := registry.ListProviders()
	assert.Len(t, providers, 5)
}
