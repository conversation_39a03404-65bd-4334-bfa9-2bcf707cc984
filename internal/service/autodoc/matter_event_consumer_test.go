package autodoc_test

import (
	"context"
	"errors"
	"testing"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/autodoc"
	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// MatterEventConsumerTestSuite tests matter event consumer functionality with mockery-generated mocks
type MatterEventConsumerTestSuite struct {
	suite.Suite
	mockRuleMatchingService *autodocmocks.MockRuleMatchingService
	mockRuleExecutionEngine *autodocmocks.MockRuleExecutionEngine
	matterEventConsumer     autodoc.MatterEventConsumer
	ctx                     context.Context
}

func (suite *MatterEventConsumerTestSuite) SetupTest() {
	suite.mockRuleMatchingService = autodocmocks.NewMockRuleMatchingService(suite.T())
	suite.mockRuleExecutionEngine = autodocmocks.NewMockRuleExecutionEngine(suite.T())
	suite.matterEventConsumer = autodoc.NewMatterEventConsumer(
		suite.mockRuleMatchingService,
		suite.mockRuleExecutionEngine,
	)
	suite.ctx = context.Background()
}

func TestMatterEventConsumerTestSuite(t *testing.T) {
	suite.Run(t, new(MatterEventConsumerTestSuite))
}

// TestConsumeMatterCreated_Success tests successful matter creation event processing
func (suite *MatterEventConsumerTestSuite) TestConsumeMatterCreated_Success() {
	// Arrange
	tenantID := uint64(123)
	eventData := map[string]interface{}{
		"matter_id": "12345",
		"client_id": "67890",
	}

	matchedRules := []*model.DocumentAutomationRule{
		{
			Model:       model.Model{ID: 1},
			TenantID:    tenantID,
			Name:        "Test Rule",
			TriggerType: "matter.create",
			IsActive:    true,
		},
	}

	// Mock: RuleMatchingService should return matched rules
	suite.mockRuleMatchingService.On("MatchRules", mock.Anything, tenantID, "matter.create", eventData).Return(matchedRules, nil)

	// Mock: RuleExecutionEngine should execute the rule successfully
	suite.mockRuleExecutionEngine.On("ExecuteRule", mock.Anything, uint64(1), eventData).Return(nil)

	// Act
	err := suite.matterEventConsumer.ConsumeMatterCreated(suite.ctx, tenantID, eventData)

	// Assert
	assert.NoError(suite.T(), err)

	suite.mockRuleMatchingService.AssertExpectations(suite.T())
	suite.mockRuleExecutionEngine.AssertExpectations(suite.T())
}

// TestConsumeMatterCreated_NoMatchingRules tests when no rules match the event
func (suite *MatterEventConsumerTestSuite) TestConsumeMatterCreated_NoMatchingRules() {
	// Arrange
	tenantID := uint64(123)
	eventData := map[string]interface{}{}

	// Mock: RuleMatchingService should return no matched rules
	suite.mockRuleMatchingService.On("MatchRules", mock.Anything, tenantID, "matter.create", eventData).Return([]*model.DocumentAutomationRule{}, nil)

	// Act
	err := suite.matterEventConsumer.ConsumeMatterCreated(suite.ctx, tenantID, eventData)

	// Assert
	assert.NoError(suite.T(), err)

	suite.mockRuleMatchingService.AssertExpectations(suite.T())
	// RuleExecutionEngine should not be called when no rules match
	suite.mockRuleExecutionEngine.AssertNotCalled(suite.T(), "ExecuteRule")
}

// TestConsumeMatterCreated_RuleMatchingError tests error handling in rule matching
func (suite *MatterEventConsumerTestSuite) TestConsumeMatterCreated_RuleMatchingError() {
	// Arrange
	tenantID := uint64(123)
	eventData := map[string]interface{}{}

	// Mock: RuleMatchingService should return error
	suite.mockRuleMatchingService.On("MatchRules", mock.Anything, tenantID, "matter.create", eventData).Return(nil, errors.New("rule matching failed"))

	// Act
	err := suite.matterEventConsumer.ConsumeMatterCreated(suite.ctx, tenantID, eventData)

	// Assert
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "rule matching failed")

	suite.mockRuleMatchingService.AssertExpectations(suite.T())
	// RuleExecutionEngine should not be called when rule matching fails
	suite.mockRuleExecutionEngine.AssertNotCalled(suite.T(), "ExecuteRule")
}

// TestConsumeMatterCreated_RuleExecutionError tests error handling in rule execution
func (suite *MatterEventConsumerTestSuite) TestConsumeMatterCreated_RuleExecutionError() {
	// Arrange
	tenantID := uint64(123)
	eventData := map[string]interface{}{}

	matchedRules := []*model.DocumentAutomationRule{
		{
			Model:       model.Model{ID: 1},
			TenantID:    tenantID,
			Name:        "Test Rule",
			TriggerType: "matter.create",
			IsActive:    true,
		},
	}

	// Mock: RuleMatchingService should return matched rules
	suite.mockRuleMatchingService.On("MatchRules", mock.Anything, tenantID, "matter.create", eventData).Return(matchedRules, nil)

	// Mock: RuleExecutionEngine should fail to execute the rule
	suite.mockRuleExecutionEngine.On("ExecuteRule", mock.Anything, uint64(1), eventData).Return(errors.New("rule execution failed"))

	// Act
	err := suite.matterEventConsumer.ConsumeMatterCreated(suite.ctx, tenantID, eventData)

	// Assert
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "rule execution failed")

	suite.mockRuleMatchingService.AssertExpectations(suite.T())
	suite.mockRuleExecutionEngine.AssertExpectations(suite.T())
}
