package autodoc

import (
	"context"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// PathTraverser provides utilities for traversing document hierarchy paths
type PathTraverser struct {
	documentRepo repositories.DocumentRepository
}

// NewPathTraverser creates a new PathTraverser instance
func NewPathTraverser(documentRepo repositories.DocumentRepository) *PathTraverser {
	return &PathTraverser{
		documentRepo: documentRepo,
	}
}

// TraversePath traverses a hierarchical path starting from rootID
// Supports paths like "folder1/folder2/file.txt" or "folder1/subfolder"
// Returns PathResolutionResult with the final document found
func (pt *PathTraverser) TraversePath(ctx context.Context, tenantID uint64, rootID uint64, path string) (*PathResolutionResult, error) {
	log := logger.WithCtx(ctx, "PathTraverser.TraversePath")

	if path == "" {
		return nil, fmt.Errorf("path cannot be empty")
	}

	// Clean and normalize the path
	path = strings.Trim(path, "/")
	if path == "" {
		// Empty path after cleaning means root
		rootDoc, err := pt.getDocumentByID(ctx, tenantID, rootID)
		if err != nil {
			return nil, fmt.Errorf("failed to get root document: %w", err)
		}
		return &PathResolutionResult{
			DocumentID: rootDoc.ID,
			ParentID:   rootDoc.ParentID,
			FileName:   rootDoc.Name,
			IsFile:     rootDoc.DocType == model.DocTypeFile,
		}, nil
	}

	// Split path into segments
	segments := strings.Split(path, "/")
	currentParentID := rootID

	log.Infof("Traversing path=%s segments=%v starting_from_root_id=%d tenant_id=%d",
		path, segments, rootID, tenantID)

	var finalDoc *model.Document
	var err error

	// Traverse each segment
	for i, segment := range segments {
		if segment == "" {
			continue // Skip empty segments
		}

		log.Debugf("Traversing segment[%d]=%s parent_id=%d", i, segment, currentParentID)

		// Find document with this name under current parent
		finalDoc, err = pt.findDocumentByNameAndParent(ctx, tenantID, currentParentID, segment)
		if err != nil {
			return nil, fmt.Errorf("failed to find segment '%s' at path position %d: %w", segment, i, err)
		}

		// For all segments except the last one, ensure it's a directory
		if i < len(segments)-1 && finalDoc.DocType != model.DocTypeDir {
			return nil, fmt.Errorf("segment '%s' at position %d is not a directory, cannot traverse further", segment, i)
		}

		// Update current parent for next iteration
		currentParentID = finalDoc.ID

		log.Debugf("Found segment[%d]=%s document_id=%d doc_type=%d",
			i, segment, finalDoc.ID, finalDoc.DocType)
	}

	if finalDoc == nil {
		return nil, fmt.Errorf("no document found for path: %s", path)
	}

	result := &PathResolutionResult{
		DocumentID: finalDoc.ID,
		ParentID:   finalDoc.ParentID,
		FileName:   finalDoc.Name,
		IsFile:     finalDoc.DocType == model.DocTypeFile,
	}

	log.Infof("Successfully traversed path=%s to document_id=%d is_file=%t",
		path, result.DocumentID, result.IsFile)

	return result, nil
}

// TraversePathInAutoDocRoot is a convenience method that gets AutoDocRoot first
// then traverses the path relative to it
func (pt *PathTraverser) TraversePathInAutoDocRoot(ctx context.Context, tenantID uint64, path string, autoDocService AutoDocService) (*PathResolutionResult, error) {
	// Get AutoDocRoot
	autoDocRoot, err := autoDocService.GetAutoDocRoot(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get AutoDocRoot: %w", err)
	}

	// Traverse path relative to AutoDocRoot
	return pt.TraversePath(ctx, tenantID, autoDocRoot.ID, path)
}

// findDocumentByNameAndParent finds a document by name under a specific parent
func (pt *PathTraverser) findDocumentByNameAndParent(ctx context.Context, tenantID uint64, parentID uint64, name string) (*model.Document, error) {
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("parent_id", parentID),
			model.NewFilterE("name", name),
		},
	}

	doc, err := pt.documentRepo.FindOne(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("document not found: name=%s parent_id=%d tenant_id=%d: %w",
			name, parentID, tenantID, err)
	}

	return doc, nil
}

// getDocumentByID retrieves a document by its ID
func (pt *PathTraverser) getDocumentByID(ctx context.Context, tenantID uint64, documentID uint64) (*model.Document, error) {
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("id", documentID),
		},
	}

	doc, err := pt.documentRepo.FindOne(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("document not found: id=%d tenant_id=%d: %w",
			documentID, tenantID, err)
	}

	return doc, nil
}

// ValidatePath validates that a path has valid syntax for traversal
func (pt *PathTraverser) ValidatePath(path string) error {
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// Check for invalid characters
	invalidChars := []string{"\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range invalidChars {
		if strings.Contains(path, char) {
			return fmt.Errorf("path contains invalid character '%s': %s", char, path)
		}
	}

	// Check for relative path components
	if strings.Contains(path, "..") {
		return fmt.Errorf("path contains invalid '..' components: %s", path)
	}

	// Check for double slashes
	if strings.Contains(path, "//") {
		return fmt.Errorf("path contains double slashes: %s", path)
	}

	return nil
}
