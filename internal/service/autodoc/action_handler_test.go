package autodoc

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSharedReplacePlaceholders(t *testing.T) {
	t.Run("should replace simple placeholders", func(t *testing.T) {
		template := "Hello {name}, welcome to {company}!"
		data := map[string]interface{}{
			"name":    "<PERSON>",
			"company": "Bilabl",
		}

		result := ReplacePlaceholders(template, data)
		expected := "Hello John Do<PERSON>, welcome to Bilabl!"
		assert.Equal(t, expected, result)
	})

	t.Run("should replace nested placeholders", func(t *testing.T) {
		template := "Welcome Letter - {client.ShortName}.docx"
		data := map[string]interface{}{
			"client": map[string]interface{}{
				"ShortName": "ACCLIME",
				"Name":      "Acclime Vietnam",
			},
		}

		result := ReplacePlaceholders(template, data)
		expected := "Welcome Letter - ACCLIME.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("should replace mixed simple and nested placeholders", func(t *testing.T) {
		template := "{client_folder}/Documents/{client.ShortName} - {date}.docx"
		data := map[string]interface{}{
			"client_folder": "id:10219",
			"date":          "2024-01-15",
			"client": map[string]interface{}{
				"ShortName": "ACCLIME",
				"Name":      "Acclime Vietnam",
			},
		}

		result := ReplacePlaceholders(template, data)
		expected := "id:10219/Documents/ACCLIME - 2024-01-15.docx"
		assert.Equal(t, expected, result)
	})

	t.Run("should handle missing nested placeholders", func(t *testing.T) {
		template := "Welcome Letter - {client.MissingField}.docx"
		data := map[string]interface{}{
			"client": map[string]interface{}{
				"ShortName": "ACCLIME",
			},
		}

		result := ReplacePlaceholders(template, data)
		expected := "Welcome Letter - {client.MissingField}.docx" // Should remain unchanged
		assert.Equal(t, expected, result)
	})

	t.Run("should handle deep nested placeholders", func(t *testing.T) {
		template := "Stage: {body.extra.current.stage_text}"
		data := map[string]interface{}{
			"body": map[string]interface{}{
				"extra": map[string]interface{}{
					"current": map[string]interface{}{
						"stage_text": "initial",
					},
				},
			},
		}

		result := ReplacePlaceholders(template, data)
		expected := "Stage: initial"
		assert.Equal(t, expected, result)
	})

	t.Run("should handle nil data", func(t *testing.T) {
		template := "Hello {name}"
		result := ReplacePlaceholders(template, nil)
		assert.Equal(t, template, result)
	})

	t.Run("should handle empty template", func(t *testing.T) {
		template := ""
		data := map[string]interface{}{
			"name": "John",
		}
		result := ReplacePlaceholders(template, data)
		assert.Equal(t, "", result)
	})
}

func TestSharedExtractNestedValue(t *testing.T) {
	data := map[string]interface{}{
		"client": map[string]interface{}{
			"ShortName": "ACCLIME",
			"Name":      "Acclime Vietnam",
			"ID":        10219,
		},
		"body": map[string]interface{}{
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"stage_text": "initial",
				},
			},
		},
	}

	t.Run("should extract simple nested value", func(t *testing.T) {
		result := extractNestedValue("client.ShortName", data)
		assert.Equal(t, "ACCLIME", result)
	})

	t.Run("should extract deep nested value", func(t *testing.T) {
		result := extractNestedValue("body.extra.current.stage_text", data)
		assert.Equal(t, "initial", result)
	})

	t.Run("should return nil for missing path", func(t *testing.T) {
		result := extractNestedValue("client.MissingField", data)
		assert.Nil(t, result)
	})

	t.Run("should return nil for invalid path", func(t *testing.T) {
		result := extractNestedValue("client.ShortName.invalid", data)
		assert.Nil(t, result)
	})

	t.Run("should handle numeric values", func(t *testing.T) {
		result := extractNestedValue("client.ID", data)
		assert.Equal(t, 10219, result)
	})
}

func TestClientObjectStructure(t *testing.T) {
	t.Run("should handle client object from enrichClientData", func(t *testing.T) {
		// Simulate client object structure as returned by entity.Generic
		clientObject := map[string]interface{}{
			"id":         uint64(10219),
			"kind":       "client",
			"tenant_id":  uint64(1),
			"name":       "Acclime Vietnam",
			"short_name": "ACCLIME", // Note: this is snake_case from JSON
			"code":       "C06855",
			"stage":      "client",
		}

		// Test data structure as it would appear in eventData after enrichClientData
		eventData := map[string]interface{}{
			"topic": "client.update",
			"body": map[string]interface{}{
				"id":         uint64(10219),
				"name":       "Acclime Vietnam",
				"short_name": "ACCLIME",
			},
			"client": clientObject, // This is added by enrichClientData
		}

		// Test nested placeholder replacement
		template := "Welcome Letter - {client.short_name}.docx"
		result := ReplacePlaceholders(template, eventData)
		expected := "Welcome Letter - ACCLIME.docx"
		assert.Equal(t, expected, result)

		// Also test with ShortName (PascalCase) in case the JSON unmarshaling preserves it
		clientObjectPascal := map[string]interface{}{
			"id":        uint64(10219),
			"kind":      "client",
			"tenant_id": uint64(1),
			"name":      "Acclime Vietnam",
			"ShortName": "ACCLIME", // PascalCase
			"code":      "C06855",
			"stage":     "client",
		}

		eventDataPascal := map[string]interface{}{
			"topic":  "client.update",
			"client": clientObjectPascal,
		}

		templatePascal := "Welcome Letter - {client.ShortName}.docx"
		resultPascal := ReplacePlaceholders(templatePascal, eventDataPascal)
		expectedPascal := "Welcome Letter - ACCLIME.docx"
		assert.Equal(t, expectedPascal, resultPascal)
	})

	t.Run("should handle normalized client object with both formats", func(t *testing.T) {
		// Simulate normalized client object (both snake_case and PascalCase)
		normalizedClient := map[string]interface{}{
			"id":         uint64(534),
			"ID":         uint64(534), // PascalCase alias
			"name":       "bilabl engineering team 002",
			"Name":       "bilabl engineering team 002", // PascalCase alias
			"short_name": "MYBIL",                       // Original snake_case
			"ShortName":  "MYBIL",                       // PascalCase alias
			"code":       "C00037",
			"Code":       "C00037", // PascalCase alias
			"stage_text": "client",
			"StageText":  "client", // PascalCase alias
		}

		eventData := map[string]interface{}{
			"client": normalizedClient,
		}

		// Test both formats work
		templateSnake := "Welcome Letter - {client.short_name}.docx"
		resultSnake := ReplacePlaceholders(templateSnake, eventData)
		expectedSnake := "Welcome Letter - MYBIL.docx"
		assert.Equal(t, expectedSnake, resultSnake)

		templatePascal := "Welcome Letter - {client.ShortName}.docx"
		resultPascal := ReplacePlaceholders(templatePascal, eventData)
		expectedPascal := "Welcome Letter - MYBIL.docx"
		assert.Equal(t, expectedPascal, resultPascal)
	})
}
