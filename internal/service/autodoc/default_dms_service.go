package autodoc

import (
	"context"
	"encoding/json"
	"strings"

	"bilabl/docman/pkg/bilabllog"

	"code.mybil.net/gophers/gokit/clients/settings"
)

// DefaultDMSService handles tenant default DMS provider resolution
type DefaultDMSService interface {
	GetDefaultProvider(ctx context.Context, tenantID uint64) (string, error)
}

// defaultDMSService implements DefaultDMSService
type defaultDMSService struct {
	settingsClient settings.Client
}

// NewDefaultDMSService creates a new DefaultDMSService
func NewDefaultDMSService(settingsClient settings.Client) DefaultDMSService {
	return &defaultDMSService{
		settingsClient: settingsClient,
	}
}

// GetDefaultProvider gets the default DMS provider for a tenant
// Always fetches fresh data from settings service to ensure up-to-date values
func (s *defaultDMSService) GetDefaultProvider(ctx context.Context, tenantID uint64) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Always fetch fresh data from settings service (no caching)
	// This ensures we get the latest default_dms setting if it changes
	setting, err := s.settingsClient.Fetch(ctx, "default_dms", tenantID, 0)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).Warn("Failed to fetch default_dms setting, using fallback")
		return "internal", nil
	}

	provider := "internal" // Default fallback
	if setting != nil && setting.Value != "" {
		provider = cleanProviderValue(setting.Value)
	}

	log.WithField("tenant_id", tenantID).WithField("provider", provider).Debug("Fetched default DMS provider from settings")
	return provider, nil
}

// cleanProviderValue handles JSON-encoded strings and cleans up provider values
// Handles cases where setting value might be "\"gdrive\"" instead of "gdrive"
func cleanProviderValue(value string) string {
	// Remove leading/trailing whitespace
	cleaned := strings.TrimSpace(value)

	// If value looks like JSON-encoded string, try to decode it
	if strings.HasPrefix(cleaned, "\"") && strings.HasSuffix(cleaned, "\"") {
		var decoded string
		if err := json.Unmarshal([]byte(cleaned), &decoded); err == nil {
			return decoded
		}
	}

	// Return original value if not JSON-encoded
	return cleaned
}
