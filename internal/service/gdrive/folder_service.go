package gdrive

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"bilabl/docman/domain/model"
	gdriveSvc "bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"google.golang.org/api/drive/v3"
)

// FolderService defines the interface for Google Drive folder operations
type FolderService interface {
	// CreateClientFolder creates a folder for a client in Google Drive
	CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*CreateClientFolderResponse, error)
	CreateMatterParentFolder(ctx context.Context, req *CreateMatterParentFolderRequest) (*CreateMatterParentFolderResponse, error)
	CreateMatterFolder(ctx context.Context, req *CreateMatterFolderRequest) (*CreateMatterFolderResponse, error)
	// RenameClientFolder renames a client folder in Google Drive
	RenameClientFolder(ctx context.Context, req *RenameClientFolderRequest) (*RenameClientFolderResponse, error)
	// RenameMatterFolder renames a matter folder in Google Drive
	RenameMatterFolder(ctx context.Context, req *RenameMatterFolderRequest) (*RenameMatterFolderResponse, error)
}

// folderService implements FolderService interface
type folderService struct {
	gdriveService  gdriveSvc.DriveClient
	docSettingRepo repositories.DocumentSettingRepository
	docMappingRepo repositories.DocumentMappingRepository
}

// NewFolderService creates a new folder service instance
func NewFolderService(
	gdriveService gdriveSvc.DriveClient,
	docSettingRepo repositories.DocumentSettingRepository,
	docMappingRepo repositories.DocumentMappingRepository,
) FolderService {
	return &folderService{
		gdriveService:  gdriveService,
		docSettingRepo: docSettingRepo,
		docMappingRepo: docMappingRepo,
	}
}

// CreateClientFolder creates a folder for a new client
func (s *folderService) CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*CreateClientFolderResponse, error) {
	log := logger.WithCtx(ctx, "[FolderService][CreateClientFolder]")
	log.Infof("creating client folder for client_id=%d, tenant_id=%d", req.ClientID, req.TenantID)

	// Check if client folder already exists
	existingMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeClient,
		model.DocProviderGoogle,
		req.TenantID,
		req.ClientID,
		0,
	)
	if err == nil {
		log.Infof("client folder already exists with drive_id=%s", existingMapping.DriveID)

		// Get folder info from Google Drive for existing folder
		folderInfo, err := s.getFolderInfo(ctx, existingMapping.DriveID, req.Config)
		if err != nil {
			log.WithError(err).Warn("failed to get folder info from Google Drive, returning with empty name and link")
			return &CreateClientFolderResponse{
				FolderID:        existingMapping.DriveID,
				FolderName:      "",
				WebViewLink:     "",
				DocumentMapping: existingMapping,
			}, nil
		}

		return &CreateClientFolderResponse{
			FolderID:        existingMapping.DriveID,
			FolderName:      folderInfo.Name,
			WebViewLink:     folderInfo.WebViewLink,
			DocumentMapping: existingMapping,
		}, nil
	}

	if !model.IsNotFound(err) {
		log.WithError(err).Error("failed to check existing client folder mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to check existing folder mapping",
			Details: err.Error(),
		}
	}

	// Validate path configuration
	if err := s.validatePathConfig(req.Config); err != nil {
		log.WithError(err).Error("invalid path configuration")
		return nil, &FolderCreationError{
			Code:    ErrCodeConfigMissing,
			Message: "Invalid Google Drive path configuration",
			Details: err.Error(),
		}
	}

	// Generate folder name using naming service
	clientData := &ClientData{
		Name:      req.ClientName,
		ShortName: req.ShortName,
		Code:      req.ClientCode,
		ID:        req.ClientID,
	}

	folderName, err := s.generateClientFolderName(ctx, req.TenantID, clientData, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to generate client folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeTemplateError,
			Message: "Failed to generate folder name",
			Details: err.Error(),
		}
	}

	// Check if folder already exists in Google Drive
	existingFolder, err := s.findExistingFolder(ctx, folderName, req.Config.RootID, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to search for existing folder")
		// Continue with creation - this is not a fatal error
	}

	var driveFile *drive.File
	if existingFolder != nil {
		log.Infof("found existing folder with same name: %s", existingFolder.Id)
		driveFile = existingFolder
	} else {
		// Create new folder in Google Drive
		driveFile, err = s.createFolderInGoogleDrive(ctx, folderName, req.Config.RootID, req.Config)
		if err != nil {
			log.WithError(err).Error("failed to create folder in google drive")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to create folder in Google Drive",
				Details: err.Error(),
			}
		}
		log.Infof("created new client folder: %s (id: %s)", folderName, driveFile.Id)
	}

	// Create document mapping
	documentMapping := &model.DocumentMapping{
		TenantID:       req.TenantID,
		Type:           model.DocTypeClient,
		ObjectID:       req.ClientID,
		ParentObjectID: 0,
		DriveID:        driveFile.Id,
		ParentDriveID:  req.Config.RootID,
		Provider:       model.DocProviderGoogle,
	}

	err = s.docMappingRepo.CreateOrUpdate(ctx, documentMapping)
	if err != nil {
		log.WithError(err).Error("failed to create document mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to save folder mapping",
			Details: err.Error(),
		}
	}

	log.Infof("successfully created client folder mapping for client_id=%d", req.ClientID)

	return &CreateClientFolderResponse{
		FolderID:        driveFile.Id,
		FolderName:      driveFile.Name,
		WebViewLink:     driveFile.WebViewLink,
		DocumentMapping: documentMapping,
	}, nil
}

// CreateMatterParentFolder creates the "Matters" parent folder under a client folder
func (s *folderService) CreateMatterParentFolder(ctx context.Context, req *CreateMatterParentFolderRequest) (*CreateMatterParentFolderResponse, error) {
	log := logger.WithCtx(ctx, "[FolderService][CreateMatterParentFolder]")
	log.Infof("creating matter parent folder for client_id=%d, tenant_id=%d", req.ClientID, req.TenantID)

	// Check if matter parent folder already exists
	existingMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeParent,
		model.DocProviderGoogle,
		req.TenantID,
		0,            // ObjectID is 0 for parent folders
		req.ClientID, // ParentObjectID is the client ID
	)
	if err == nil {
		log.Infof("matter parent folder already exists with drive_id=%s", existingMapping.DriveID)

		// Get folder info from Google Drive for existing folder
		folderInfo, err := s.getFolderInfo(ctx, existingMapping.DriveID, req.Config)
		if err != nil {
			log.WithError(err).Warn("failed to get folder info from Google Drive, returning with empty name and link")
			return &CreateMatterParentFolderResponse{
				FolderID:        existingMapping.DriveID,
				FolderName:      "",
				WebViewLink:     "",
				DocumentMapping: existingMapping,
			}, nil
		}

		return &CreateMatterParentFolderResponse{
			FolderID:        existingMapping.DriveID,
			FolderName:      folderInfo.Name,
			WebViewLink:     folderInfo.WebViewLink,
			DocumentMapping: existingMapping,
		}, nil
	}

	if !model.IsNotFound(err) {
		log.WithError(err).Error("failed to check existing matter parent folder mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to check existing parent folder mapping",
			Details: err.Error(),
		}
	}

	// Get client folder mapping
	clientMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeClient,
		model.DocProviderGoogle,
		req.TenantID,
		req.ClientID,
		0,
	)
	if err != nil {
		if model.IsNotFound(err) {
			log.Infof("Client folder mapping not found client_id=%d tenant_id=%d", req.ClientID, req.TenantID)
			return nil, &FolderCreationError{
				Code:    ErrCodeNotFound,
				Message: "Client folder not found. Create client folder first.",
			}
		}

		log.WithError(err).Error("Database error while getting client folder mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to query client folder mapping",
			Details: err.Error(),
		}
	}

	// Get matter parent folder name from config
	folderName, err := s.getMatterParentFolderName(req.Config)
	if err != nil {
		log.WithError(err).Error("failed to get matter parent folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeConfigMissing,
			Message: "Matter parent folder configuration error",
			Details: err.Error(),
		}
	}

	// Check if folder already exists in Google Drive
	existingFolder, err := s.findExistingFolder(ctx, folderName, clientMapping.DriveID, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to search for existing matter parent folder")
		// Continue with creation - this is not a fatal error
	}

	var driveFile *drive.File
	if existingFolder != nil {
		log.Infof("found existing matter parent folder with same name: (name=%s, id=%s)",
			existingFolder.Name, existingFolder.Id)
		driveFile = existingFolder
	} else {
		// Create new folder in Google Drive under client folder
		driveFile, err = s.createFolderInGoogleDrive(ctx, folderName, clientMapping.DriveID, req.Config)
		if err != nil {
			log.WithError(err).Error("failed to create matter parent folder in google drive")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to create matter parent folder in Google Drive",
				Details: err.Error(),
			}
		}
		log.Infof("created new matter parent folder: %s (id: %s)", folderName, driveFile.Id)
	}

	// Create document mapping
	documentMapping := &model.DocumentMapping{
		TenantID:       req.TenantID,
		Type:           model.DocTypeParent,
		ObjectID:       0,            // ObjectID is 0 for parent folders
		ParentObjectID: req.ClientID, // ParentObjectID is the client ID
		DriveID:        driveFile.Id,
		ParentDriveID:  clientMapping.DriveID,
		Provider:       model.DocProviderGoogle,
	}

	err = s.docMappingRepo.CreateOrUpdate(ctx, documentMapping)
	if err != nil {
		log.WithError(err).Error("failed to create matter parent document mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to save matter parent folder mapping",
			Details: err.Error(),
		}
	}

	log.Infof("successfully created matter parent folder mapping for client_id=%d", req.ClientID)

	return &CreateMatterParentFolderResponse{
		FolderID:        driveFile.Id,
		FolderName:      driveFile.Name,
		WebViewLink:     driveFile.WebViewLink,
		DocumentMapping: documentMapping,
	}, nil
}

// CreateMatterFolder creates a folder for a specific matter
func (s *folderService) CreateMatterFolder(ctx context.Context, req *CreateMatterFolderRequest) (*CreateMatterFolderResponse, error) {
	log := logger.WithCtx(ctx, "[FolderService][CreateMatterFolder]")
	log.Infof("creating matter folder for matter_id=%d, client_id=%d, tenant_id=%d", req.MatterID, req.ClientID, req.TenantID)

	// Check if matter folder already exists
	existingMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeMatter,
		model.DocProviderGoogle,
		req.TenantID,
		req.MatterID,
		0,
	)
	if err == nil {
		log.Infof("matter folder already exists with drive_id=%s", existingMapping.DriveID)

		// Get folder info from Google Drive for existing folder
		folderInfo, err := s.getFolderInfo(ctx, existingMapping.DriveID, req.Config)
		if err != nil {
			log.WithError(err).Warn("failed to get folder info from Google Drive, returning with empty name and link")
			return &CreateMatterFolderResponse{
				FolderID:        existingMapping.DriveID,
				FolderName:      "",
				WebViewLink:     "",
				DocumentMapping: existingMapping,
			}, nil
		}

		return &CreateMatterFolderResponse{
			FolderID:        existingMapping.DriveID,
			FolderName:      folderInfo.Name,
			WebViewLink:     folderInfo.WebViewLink,
			DocumentMapping: existingMapping,
		}, nil
	}

	if !model.IsNotFound(err) {
		log.WithError(err).Error("failed to check existing matter folder mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to check existing matter folder mapping",
			Details: err.Error(),
		}
	}

	// Validate path configuration
	if err := s.validatePathConfig(req.Config); err != nil {
		log.WithError(err).Error("invalid path configuration")
		return nil, &FolderCreationError{
			Code:    ErrCodeConfigMissing,
			Message: "Invalid Google Drive path configuration",
			Details: err.Error(),
		}
	}

	// Determine parent folder ID based on configuration
	var parentFolderID string

	// Check if matter folder should be created directly under client folder
	parentFolderName, err := s.getMatterParentFolderName(req.Config)
	if err != nil {
		log.WithError(err).Error("failed to get matter parent folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeConfigMissing,
			Message: "Matter parent folder configuration error",
			Details: err.Error(),
		}
	}

	if parentFolderName == "" {
		// Matter folder should be created directly under client folder
		clientMapping, err := s.docMappingRepo.FirstObjectMapping(
			ctx,
			model.DocTypeClient,
			model.DocProviderGoogle,
			req.TenantID,
			req.ClientID,
			0,
		)
		if err != nil {
			if model.IsNotFound(err) {
				log.Infof("Client folder mapping not found client_id=%d tenant_id=%d", req.ClientID, req.TenantID)
				return nil, &FolderCreationError{
					Code:    ErrCodeNotFound,
					Message: "Client folder not found. Create client folder first.",
				}
			}

			log.WithError(err).Error("Database error while getting client folder mapping")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to query client folder mapping",
				Details: err.Error(),
			}
		}
		parentFolderID = clientMapping.DriveID
		log.Info("creating matter folder directly under client folder")
	} else {
		// Matter folder should be created under matter parent folder
		// Try to get existing matter parent folder first
		parentMapping, err := s.docMappingRepo.FirstObjectMapping(
			ctx,
			model.DocTypeParent,
			model.DocProviderGoogle,
			req.TenantID,
			0,            // ObjectID is 0 for parent folders
			req.ClientID, // ParentObjectID is the client ID
		)

		if err != nil && !model.IsNotFound(err) {
			log.WithError(err).Error("failed to check matter parent folder mapping")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to check matter parent folder mapping",
				Details: err.Error(),
			}
		}

		if model.IsNotFound(err) {
			// Matter parent folder doesn't exist, create it automatically
			log.Infof("matter parent folder not found, creating it automatically: %s", parentFolderName)

			parentReq := &CreateMatterParentFolderRequest{
				TenantID: req.TenantID,
				ClientID: req.ClientID,
				Config:   req.Config,
			}

			parentResp, err := s.CreateMatterParentFolder(ctx, parentReq)
			if err != nil {
				log.WithError(err).Error("failed to create matter parent folder automatically")
				return nil, &FolderCreationError{
					Code:    ErrCodeAPIError,
					Message: "Failed to create matter parent folder automatically",
					Details: err.Error(),
				}
			}

			parentFolderID = parentResp.FolderID
			log.Infof("automatically created matter parent folder: %s (id: %s)", parentFolderName, parentFolderID)
		} else {
			// Use existing matter parent folder
			parentFolderID = parentMapping.DriveID
			log.Infof("using existing matter parent folder: %s", parentFolderName)
		}
	}

	// Generate folder name using naming service
	matterData := &MatterData{
		Name:     req.MatterName,
		Code:     req.MatterCode,
		ID:       req.MatterID,
		ClientID: req.ClientID,
	}

	folderName, err := s.generateMatterFolderName(ctx, req.TenantID, matterData, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to generate matter folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeTemplateError,
			Message: "Failed to generate matter folder name",
			Details: err.Error(),
		}
	}

	// Check if folder already exists in Google Drive
	existingFolder, err := s.findExistingFolder(ctx, folderName, parentFolderID, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to search for existing matter folder")
		// Continue with creation - this is not a fatal error
	}

	var driveFile *drive.File
	if existingFolder != nil {
		log.Infof("found existing matter folder with same name: %s", existingFolder.Id)
		driveFile = existingFolder
	} else {
		// Create new folder in Google Drive under determined parent folder
		driveFile, err = s.createFolderInGoogleDrive(ctx, folderName, parentFolderID, req.Config)
		if err != nil {
			log.WithError(err).Error("failed to create matter folder in google drive")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to create matter folder in Google Drive",
				Details: err.Error(),
			}
		}
		log.Infof("created new matter folder: %s (id: %s)", folderName, driveFile.Id)
	}

	// Create document mapping with appropriate parent relationship
	documentMapping := &model.DocumentMapping{
		TenantID:       req.TenantID,
		Type:           model.DocTypeMatter,
		ObjectID:       req.MatterID,
		ParentObjectID: 0, // use 0 with matter to consist with sharepoint
		DriveID:        driveFile.Id,
		ParentDriveID:  parentFolderID,
		Provider:       model.DocProviderGoogle,
	}

	err = s.docMappingRepo.CreateOrUpdate(ctx, documentMapping)
	if err != nil {
		log.WithError(err).Error("failed to create matter document mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to save matter folder mapping",
			Details: err.Error(),
		}
	}

	log.Infof("successfully created matter folder mapping for matter_id=%d", req.MatterID)

	return &CreateMatterFolderResponse{
		FolderID:        driveFile.Id,
		FolderName:      driveFile.Name,
		WebViewLink:     driveFile.WebViewLink,
		DocumentMapping: documentMapping,
	}, nil
}

// RenameClientFolderRequest contains parameters for renaming a client folder
type RenameClientFolderRequest struct {
	TenantID   uint64
	ClientID   uint64
	ClientName string
	ClientCode string
	ShortName  string
	Config     *model.GDriveConfig // Pass config from handler instead of loading inside service
}

// RenameClientFolderResponse contains the result of a client folder rename operation
type RenameClientFolderResponse struct {
	FolderID    string
	OldName     string
	NewName     string
	WebViewLink string
}

// RenameClientFolder renames a client folder in Google Drive
// If the folder mapping doesn't exist, it creates the folder first before renaming
func (s *folderService) RenameClientFolder(ctx context.Context, req *RenameClientFolderRequest) (*RenameClientFolderResponse, error) {
	log := logger.WithCtx(ctx, "[FolderService][RenameClientFolder]")

	// Validate request parameters first
	if req == nil {
		log.Error("Request cannot be nil")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Request cannot be nil",
		}
	}

	log.Infof("renaming client folder for client_id=%d, tenant_id=%d", req.ClientID, req.TenantID)

	if req.ClientID == 0 {
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Client ID is required",
		}
	}

	if req.TenantID == 0 {
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Tenant ID is required",
		}
	}

	if req.Config == nil {
		return nil, &FolderCreationError{
			Code:    ErrCodeConfigMissing,
			Message: "Google Drive configuration is required",
		}
	}

	// Find existing folder mapping
	existingMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeClient,
		model.DocProviderGoogle,
		req.TenantID,
		req.ClientID,
		0,
	)
	// If mapping doesn't exist, create the folder first
	if err != nil {
		if model.IsNotFound(err) {
			log.WithFields(map[string]interface{}{
				"client_id": req.ClientID,
				"tenant_id": req.TenantID,
			}).Info("Client folder mapping not found, creating folder first")

			// Create the client folder first
			createReq := &CreateClientFolderRequest{
				TenantID:   req.TenantID,
				ClientID:   req.ClientID,
				ClientName: req.ClientName,
				ClientCode: req.ClientCode,
				ShortName:  req.ShortName,
				Config:     req.Config,
			}

			createResp, createErr := s.CreateClientFolder(ctx, createReq)
			if createErr != nil {
				log.WithError(createErr).Error("failed to create client folder before rename")
				return nil, &FolderCreationError{
					Code:    ErrCodeAPIError,
					Message: "Failed to create client folder before rename",
					Details: createErr.Error(),
				}
			}

			log.WithFields(map[string]interface{}{
				"client_id":   req.ClientID,
				"tenant_id":   req.TenantID,
				"folder_id":   createResp.FolderID,
				"folder_name": createResp.FolderName,
			}).Info("Successfully created client folder, proceeding with rename")

			// Use the newly created mapping for rename operation
			existingMapping = createResp.DocumentMapping
		} else {
			log.WithError(err).Error("failed to check existing client folder mapping")
			return nil, &FolderCreationError{
				Code:    ErrCodeAPIError,
				Message: "Failed to check existing client folder mapping",
				Details: err.Error(),
			}
		}
	}

	// Get current folder info
	currentFolder, err := s.getFolderInfo(ctx, existingMapping.DriveID, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to get current folder info from Google Drive")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to get current folder information",
			Details: err.Error(),
		}
	}

	// Generate new folder name
	clientData := &ClientData{
		Name:      req.ClientName,
		ShortName: req.ShortName,
		Code:      req.ClientCode,
		ID:        req.ClientID,
	}

	newFolderName, err := s.generateClientFolderName(ctx, req.TenantID, clientData, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to generate new client folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeTemplateError,
			Message: "Failed to generate new folder name",
			Details: err.Error(),
		}
	}

	// If name hasn't changed, no need to rename
	if currentFolder.Name == newFolderName {
		log.Infof("folder name unchanged, skipping rename operation")
		return &RenameClientFolderResponse{
			FolderID:    existingMapping.DriveID,
			OldName:     currentFolder.Name,
			NewName:     newFolderName,
			WebViewLink: currentFolder.WebViewLink,
		}, nil
	}

	// Rename folder in Google Drive
	log.WithFields(map[string]interface{}{
		"folder_id": existingMapping.DriveID,
		"old_name":  currentFolder.Name,
		"new_name":  newFolderName,
		"client_id": req.ClientID,
		"tenant_id": req.TenantID,
	}).Info("Attempting to rename folder in Google Drive")

	updatedFolder, err := s.gdriveService.RenameFile(existingMapping.DriveID, newFolderName)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"folder_id": existingMapping.DriveID,
			"old_name":  currentFolder.Name,
			"new_name":  newFolderName,
			"client_id": req.ClientID,
			"tenant_id": req.TenantID,
		}).WithError(err).Error("failed to rename folder in Google Drive")

		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to rename folder in Google Drive",
			Details: fmt.Sprintf("Folder ID: %s, Old Name: %s, New Name: %s, Error: %s",
				existingMapping.DriveID, currentFolder.Name, newFolderName, err.Error()),
		}
	}

	log.Infof("successfully renamed client folder from '%s' to '%s'", currentFolder.Name, newFolderName)

	return &RenameClientFolderResponse{
		FolderID:    updatedFolder.Id,
		OldName:     currentFolder.Name,
		NewName:     updatedFolder.Name,
		WebViewLink: updatedFolder.WebViewLink,
	}, nil
}

// RenameMatterFolder renames a matter folder in Google Drive
func (s *folderService) RenameMatterFolder(ctx context.Context, req *RenameMatterFolderRequest) (*RenameMatterFolderResponse, error) {
	log := logger.WithCtx(ctx, "[FolderService][RenameMatterFolder]")
	log.Infof("renaming matter folder for matter_id=%d, client_id=%d, tenant_id=%d", req.MatterID, req.ClientID, req.TenantID)

	// Find existing folder mapping
	existingMapping, err := s.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeMatter,
		model.DocProviderGoogle,
		req.TenantID,
		req.MatterID,
		0,
	)
	if err != nil {
		if model.IsNotFound(err) {
			log.Infof("matter folder mapping not found, nothing to rename")
			return nil, &FolderCreationError{
				Code:    ErrCodeNotFound,
				Message: "Matter folder mapping not found",
				Details: "No Google Drive folder exists for this matter",
			}
		}
		log.WithError(err).Error("failed to get matter folder mapping")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to get matter folder mapping",
			Details: err.Error(),
		}
	}

	// Get current folder info
	currentFolder, err := s.getFolderInfo(ctx, existingMapping.DriveID, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to get current folder info from Google Drive")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to get current folder information",
			Details: err.Error(),
		}
	}

	// Generate new folder name
	matterData := &MatterData{
		Name:     req.MatterName,
		Code:     req.MatterCode,
		ID:       req.MatterID,
		ClientID: req.ClientID,
	}

	newFolderName, err := s.generateMatterFolderName(ctx, req.TenantID, matterData, req.Config)
	if err != nil {
		log.WithError(err).Error("failed to generate new matter folder name")
		return nil, &FolderCreationError{
			Code:    ErrCodeTemplateError,
			Message: "Failed to generate new folder name",
			Details: err.Error(),
		}
	}

	// If name hasn't changed, no need to rename
	if currentFolder.Name == newFolderName {
		log.Infof("folder name unchanged, skipping rename operation")
		return &RenameMatterFolderResponse{
			FolderID:    existingMapping.DriveID,
			OldName:     currentFolder.Name,
			NewName:     newFolderName,
			WebViewLink: currentFolder.WebViewLink,
		}, nil
	}

	// Rename folder in Google Drive
	updatedFolder, err := s.gdriveService.RenameFile(existingMapping.DriveID, newFolderName)
	if err != nil {
		log.WithError(err).Error("failed to rename folder in Google Drive")
		return nil, &FolderCreationError{
			Code:    ErrCodeAPIError,
			Message: "Failed to rename folder in Google Drive",
			Details: err.Error(),
		}
	}

	log.Infof("successfully renamed matter folder from '%s' to '%s'", currentFolder.Name, newFolderName)

	return &RenameMatterFolderResponse{
		FolderID:    updatedFolder.Id,
		OldName:     currentFolder.Name,
		NewName:     updatedFolder.Name,
		WebViewLink: updatedFolder.WebViewLink,
	}, nil
}

// Helper Methods

// PathConfigError represents configuration validation errors
type PathConfigError struct {
	Field   string
	Message string
	Path    string
}

func (e *PathConfigError) Error() string {
	return fmt.Sprintf("invalid path config for %s: %s (path: %s)", e.Field, e.Message, e.Path)
}

// validatePathConfig validates folder path configurations
func (s *folderService) validatePathConfig(config *model.GDriveConfig) error {
	if config == nil {
		return &PathConfigError{
			Field:   "Config",
			Message: "configuration is required",
			Path:    "",
		}
	}

	if config.PathConfig == nil {
		return nil // No path config is valid, will use defaults
	}

	// Validate client folder path
	if config.PathConfig.ClientFolderPath != "" {
		if err := s.validateClientFolderPath(config.PathConfig.ClientFolderPath); err != nil {
			return err
		}
	}

	// Validate matter folder path
	if config.PathConfig.MatterFolderPath != "" {
		if err := s.validateMatterFolderPath(config.PathConfig.MatterFolderPath); err != nil {
			return err
		}
	}

	return nil
}

// validateClientFolderPath validates client folder path template
func (s *folderService) validateClientFolderPath(path string) error {
	if strings.TrimSpace(path) == "" {
		return &PathConfigError{
			Field:   "ClientFolderPath",
			Message: "path cannot be empty",
			Path:    path,
		}
	}

	segments := s.parseFolderPath(path)
	if len(segments) == 0 {
		return &PathConfigError{
			Field:   "ClientFolderPath",
			Message: "path must contain at least one segment",
			Path:    path,
		}
	}

	// Client folder path should only have 1 segment (the client folder itself)
	if len(segments) > 1 {
		return &PathConfigError{
			Field:   "ClientFolderPath",
			Message: "client folder path should only contain one segment",
			Path:    path,
		}
	}

	// Validate template variables in client path
	validClientVars := map[string]bool{
		"name":       true,
		"short_name": true,
		"code":       true,
		"id":         true,
	}

	if err := s.validateTemplateVariables(segments[0].Name, validClientVars, "client"); err != nil {
		return &PathConfigError{
			Field:   "ClientFolderPath",
			Message: err.Error(),
			Path:    path,
		}
	}

	return nil
}

// validateMatterFolderPath validates matter folder path template
func (s *folderService) validateMatterFolderPath(path string) error {
	if strings.TrimSpace(path) == "" {
		return &PathConfigError{
			Field:   "MatterFolderPath",
			Message: "path cannot be empty",
			Path:    path,
		}
	}

	segments := s.parseFolderPath(path)
	if len(segments) == 0 {
		return &PathConfigError{
			Field:   "MatterFolderPath",
			Message: "path must contain at least one segment",
			Path:    path,
		}
	}

	// Currently support max 3 levels: {client_folder}/{parent}/{matter}
	if len(segments) > 3 {
		return &PathConfigError{
			Field:   "MatterFolderPath",
			Message: "matter folder path supports maximum 3 levels: {client_folder}/{parent}/{matter}",
			Path:    path,
		}
	}

	// First segment should contain client_folder variable
	if !strings.Contains(segments[0].Name, "{client_folder}") {
		return &PathConfigError{
			Field:   "MatterFolderPath",
			Message: "first segment must contain {client_folder} variable",
			Path:    path,
		}
	}

	// Validate template variables in each segment
	validClientVars := map[string]bool{
		"client_folder": true,
		"name":          true,
		"short_name":    true,
		"code":          true,
		"id":            true,
	}

	validMatterVars := map[string]bool{
		"name":      true,
		"code":      true,
		"id":        true,
		"client_id": true,
	}

	for i, segment := range segments {
		var validVars map[string]bool
		var segmentType string

		switch i {
		case 0:
			// Client folder segment
			validVars = validClientVars
			segmentType = "client folder"
		case len(segments) - 1:
			// Last segment is matter folder
			validVars = validMatterVars
			segmentType = "matter folder"
		default:
			// Middle segments are parent folders (fixed names only for now)
			if segment.HasVariables {
				return &PathConfigError{
					Field:   "MatterFolderPath",
					Message: fmt.Sprintf("parent folder segment at position %d cannot contain variables (only fixed names supported)", i+1),
					Path:    path,
				}
			}
			continue
		}

		if err := s.validateTemplateVariables(segment.Name, validVars, segmentType); err != nil {
			return &PathConfigError{
				Field:   "MatterFolderPath",
				Message: fmt.Sprintf("segment %d (%s): %s", i+1, segmentType, err.Error()),
				Path:    path,
			}
		}
	}

	return nil
}

// validateTemplateVariables validates template variables in a segment
func (s *folderService) validateTemplateVariables(template string, validVars map[string]bool, segmentType string) error {
	// Find all variables in template
	varPattern := `\{([^}]+)\}`
	re := regexp.MustCompile(varPattern)
	matches := re.FindAllStringSubmatch(template, -1)

	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		varName := match[1]

		// Handle fallback syntax like {short_name|name}
		if strings.Contains(varName, "|") {
			fallbackVars := strings.Split(varName, "|")
			allValid := true
			for _, fVar := range fallbackVars {
				fVar = strings.TrimSpace(fVar)
				if !validVars[fVar] {
					allValid = false
					break
				}
			}
			if !allValid {
				return fmt.Errorf("invalid fallback variable '%s' in %s template", varName, segmentType)
			}
		} else if !validVars[varName] {
			return fmt.Errorf("invalid variable '%s' in %s template", varName, segmentType)
		}
	}

	return nil
}

// PathSegment represents a parsed segment of a folder path template
type PathSegment struct {
	Name         string
	HasVariables bool
	IsFixed      bool // true when segment contains no template variables
}

// getMatterParentFolderName gets the configurable matter parent folder name from path config
func (s *folderService) getMatterParentFolderName(config *model.GDriveConfig) (string, error) {
	// Priority: use PathConfig.MatterFolderPath to determine folder structure
	if config != nil && config.PathConfig != nil && config.PathConfig.MatterFolderPath != "" {
		// Validate path config first
		if err := s.validateMatterFolderPath(config.PathConfig.MatterFolderPath); err != nil {
			return "", err
		}

		segments := s.parseFolderPath(config.PathConfig.MatterFolderPath)
		parentName := s.extractParentFolderName(segments)

		if parentName != "" {
			return parentName, nil
		}

		// Check if matter folder should be created directly under client folder
		if s.isMatterUnderClientFolder(segments) {
			return "", nil // Empty string indicates no parent folder needed
		}
	}

	// Fallback: use default "Matters" parent folder
	return "Matters", nil
}

// generateMatterFolderName generates a folder name for a matter using path templates
func (s *folderService) generateMatterFolderName(ctx context.Context, tenantID uint64, matterData *MatterData, config *model.GDriveConfig) (string, error) {
	// Priority: use PathConfig.MatterFolderPath for template-based naming
	if config.PathConfig != nil && config.PathConfig.MatterFolderPath != "" {
		// Validate matter folder path before using it
		if err := s.validateMatterFolderPath(config.PathConfig.MatterFolderPath); err != nil {
			return "", fmt.Errorf("invalid matter folder path configuration: %w", err)
		}

		segments := s.parseFolderPath(config.PathConfig.MatterFolderPath)
		template := s.extractMatterFolderTemplate(segments)
		return s.processTemplate(template, s.buildMatterVariables(matterData)), nil
	}

	// Fallback: use SharePoint-compatible naming convention
	return fmt.Sprintf("%s - %s", matterData.Name, matterData.Code), nil
}

// parseFolderPath parses a folder path template into segments for analysis
func (s *folderService) parseFolderPath(pathTemplate string) []PathSegment {
	parts := strings.Split(pathTemplate, "/")
	segments := make([]PathSegment, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		hasVariables := strings.Contains(part, "{") && strings.Contains(part, "}")
		segment := PathSegment{
			Name:         part,
			HasVariables: hasVariables,
			IsFixed:      !hasVariables,
		}
		segments = append(segments, segment)
	}

	return segments
}

// extractParentFolderName finds the parent folder name from path segments
// Returns empty string if matter folder should be created directly under client folder
func (s *folderService) extractParentFolderName(segments []PathSegment) string {
	// Need at least 2 segments to have a parent: {client_folder}/{parent}/{matter}
	if len(segments) < 3 {
		return ""
	}

	// Look for fixed segments between client folder and matter folder
	// Skip first segment (client folder) and last segment (matter folder)
	for i := 1; i < len(segments)-1; i++ {
		if segments[i].IsFixed {
			return segments[i].Name
		}
	}

	return ""
}

// extractMatterFolderTemplate gets the template for matter folder naming
func (s *folderService) extractMatterFolderTemplate(segments []PathSegment) string {
	if len(segments) == 0 {
		return "{name} - {code}" // Default template
	}

	// Use the last segment as matter folder template
	return segments[len(segments)-1].Name
}

// isMatterUnderClientFolder checks if matter folder should be created directly under client folder
// Returns true for patterns like "{client_folder}/{name} - {code}"
func (s *folderService) isMatterUnderClientFolder(segments []PathSegment) bool {
	// Pattern: exactly 2 segments where first is client_folder variable and second is matter template
	return len(segments) == 2 &&
		segments[0].HasVariables &&
		strings.Contains(segments[0].Name, "client_folder")
}

// findExistingFolder searches for an existing folder by name in a parent folder
func (s *folderService) findExistingFolder(ctx context.Context, folderName, parentID string, config *model.GDriveConfig) (*drive.File, error) {
	log := logger.WithCtx(ctx, "[FolderService][findExistingFolder]")
	log.Infof("searching for folder '%s' in parent '%s'", folderName, parentID)

	// Escape single quotes in folder name for the query
	escapedFolderName := strings.ReplaceAll(folderName, "'", "\\'")

	// Build search query
	query := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false and '%s' in parents",
		escapedFolderName, parentID)

	// Search in regular drive using the gdrive service's search capability
	log.Infof("searching in regular drive with query: %s", query)
	files, err := s.gdriveService.ListFilesWithQuery(query, "")
	if err != nil {
		return nil, fmt.Errorf("failed to search for folder: %w", err)
	}

	if len(files) == 0 {
		log.Infof("folder '%s' not found in parent '%s'", folderName, parentID)
		return nil, nil // Not found, but not an error
	}

	// Return the first match
	log.Infof("found existing folder '%s' with id: %s", folderName, files[0].Id)
	return files[0], nil
}

// createFolderInGoogleDrive creates a new folder in Google Drive
func (s *folderService) createFolderInGoogleDrive(ctx context.Context, folderName, parentID string, config *model.GDriveConfig) (*drive.File, error) {
	log := logger.WithCtx(ctx, "[FolderService][createFolderInGoogleDrive]")

	if config.DriveID != "" {
		// Create folder in shared drive
		log.Infof("creating folder '%s' in shared drive %s under parent %s", folderName, config.DriveID, parentID)
		return s.gdriveService.CreateFolderInSharedDriveFolder(folderName, config.DriveID, parentID)
	} else {
		// Create folder in regular drive
		log.Infof("creating folder '%s' under parent %s", folderName, parentID)
		return s.gdriveService.CreateFolder(folderName, parentID)
	}
}

// getFolderInfo retrieves folder information from Google Drive by folder ID
func (s *folderService) getFolderInfo(ctx context.Context, folderID string, config *model.GDriveConfig) (*drive.File, error) {
	log := logger.WithCtx(ctx, "[FolderService][getFolderInfo]")
	log.Infof("getting folder info for folder_id=%s", folderID)

	// Use the GetFileInfo method from DriveClient interface
	file, err := s.gdriveService.GetFileInfo(folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder info from Google Drive: %w", err)
	}

	log.Infof("retrieved folder info: name='%s', webViewLink='%s'", file.Name, file.WebViewLink)
	return file, nil
}

// generateClientFolderName generates a folder name for a client using templates
func (s *folderService) generateClientFolderName(ctx context.Context, tenantID uint64, clientData *ClientData, config *model.GDriveConfig) (string, error) {
	// Priority: use PathConfig.ClientFolderPath for template-based naming
	if config.PathConfig != nil && config.PathConfig.ClientFolderPath != "" {
		// Validate client folder path before using it
		if err := s.validateClientFolderPath(config.PathConfig.ClientFolderPath); err != nil {
			return "", fmt.Errorf("invalid client folder path configuration: %w", err)
		}

		segments := s.parseFolderPath(config.PathConfig.ClientFolderPath)
		template := s.extractMatterFolderTemplate(segments) // Use last segment as folder name
		return s.processTemplate(template, s.buildClientVariables(clientData)), nil
	}

	// Fallback: use SharePoint-compatible naming convention
	var folderName string
	if clientData.ShortName != "" {
		folderName = clientData.ShortName
	} else {
		folderName = clientData.Name
	}
	return fmt.Sprintf("%s - %s", folderName, clientData.Code), nil
}

// processTemplate processes a template string with variables
func (s *folderService) processTemplate(template string, variables map[string]string) string {
	result := template
	for key, value := range variables {
		placeholder := fmt.Sprintf("{%s}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}

	// Handle fallback syntax {var1|var2} - prioritize first option if available
	if strings.Contains(result, "{short_name|name}") {
		if variables["short_name"] != "" {
			result = strings.ReplaceAll(result, "{short_name|name}", variables["short_name"])
		} else {
			result = strings.ReplaceAll(result, "{short_name|name}", variables["name"])
		}
	}

	return result
}

// buildClientVariables builds template variables for client data
func (s *folderService) buildClientVariables(clientData *ClientData) map[string]string {
	return map[string]string{
		"name":       clientData.Name,
		"short_name": clientData.ShortName,
		"code":       clientData.Code,
		"id":         fmt.Sprintf("%d", clientData.ID),
	}
}

// buildMatterVariables builds template variables for matter data
func (s *folderService) buildMatterVariables(matterData *MatterData) map[string]string {
	return map[string]string{
		"name":      matterData.Name,
		"code":      matterData.Code,
		"id":        fmt.Sprintf("%d", matterData.ID),
		"client_id": fmt.Sprintf("%d", matterData.ClientID),
	}
}
