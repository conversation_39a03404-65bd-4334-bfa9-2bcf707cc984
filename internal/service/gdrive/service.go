package gdrive

import (
	"context"

	"bilabl/docman/domain/model"
	gdriveSvc "bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/errors"
	"code.mybil.net/gophers/gokit/pkg/logger"
)

// CompleteSetupRequest contains all parameters for completing Google Drive setup
type CompleteSetupRequest struct {
	URLOrID    string
	TenantID   uint64
	Enabled    bool
	PathConfig *model.PathConfig // Optional path configuration
}

// Service defines the interface for the GDrive business logic.
type Service interface {
	TestSetup(ctx context.Context, urlOrID string) (*gdriveSvc.DriveInfo, error)
	CompleteSetup(ctx context.Context, req *CompleteSetupRequest) (*CompleteSetupResponse, error)

	// Folder creation operations
	CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*CreateClientFolderResponse, error)
	CreateMatterParentFolder(ctx context.Context, req *CreateMatterParentFolderRequest) (*CreateMatterParentFolderResponse, error)
	CreateMatterFolder(ctx context.Context, req *CreateMatterFolderRequest) (*CreateMatterFolderResponse, error)

	// Getter methods
	GetFolderService() FolderService
	GetDocumentSettingRepo() repositories.DocumentSettingRepository
	GetDocumentMappingRepo() repositories.DocumentMappingRepository
	GetMatterEventConsumer() *MatterEventConsumer
	GetClientEventConsumer() *ClientEventConsumer

	// SetPermissionSyncer sets the permission syncer for event consumers
	SetPermissionSyncer(syncer PermissionSyncer)
}

// service implements the Google Drive Service interface
type service struct {
	gdriveService  gdriveSvc.DriveClient
	docSettingRepo repositories.DocumentSettingRepository
	docMappingRepo repositories.DocumentMappingRepository
	folderService  FolderService
	clientConsumer *ClientEventConsumer
	matterConsumer *MatterEventConsumer
}

// New creates a new GDrive service instance.
func New(
	gdriveService gdriveSvc.DriveClient,
	docSettingRepo repositories.DocumentSettingRepository,
	docMappingRepo repositories.DocumentMappingRepository,
	docPermissionRepo repositories.DocumentPermissionMappingRepository,
	entityFetcher entity.GenericFetcher,
) Service {
	// Create folder service instance
	folderService := NewFolderService(gdriveService, docSettingRepo, docMappingRepo)

	// Create permission handler (will be set later via SetPermissionSyncer)
	var permissionSyncer PermissionSyncer = nil

	// Create event consumers
	clientConsumer := NewClientEventConsumer(folderService, docSettingRepo, permissionSyncer)
	matterConsumer := NewMatterEventConsumer(folderService, docSettingRepo, docMappingRepo, entityFetcher, permissionSyncer)

	return &service{
		gdriveService:  gdriveService,
		docSettingRepo: docSettingRepo,
		docMappingRepo: docMappingRepo,
		folderService:  folderService,
		clientConsumer: clientConsumer,
		matterConsumer: matterConsumer,
	}
}

// TestSetup validates the provided Google Drive URL/ID and checks for access.
func (s *service) TestSetup(ctx context.Context, urlOrID string) (*gdriveSvc.DriveInfo, error) {
	log := logger.WithCtx(ctx, "[GdriveService][TestSetup]")

	info, err := s.gdriveService.ParseSharedURL(urlOrID)
	if err != nil {
		log.WithError(err).Errorf("failed to parse shared url: %s", urlOrID)
		return nil, errors.NewBadDataErr("Invalid Google Drive URL or ID. Please provide a valid URL or the resource ID.")
	}

	// A simple check to confirm access by trying to list files.
	switch info.Type {
	case gdriveSvc.ResourceTypeSharedDrive:
		// Use pagination options for the new interface - just test with minimal page size
		opts := &gdriveSvc.PaginationOptions{PageSize: 1}
		_, err = s.gdriveService.ListFilesInSharedDrive(info.ID, opts)
	case gdriveSvc.ResourceTypeFolder:
		// For folders, test using ListFilesInFolder with pagination
		opts := &gdriveSvc.PaginationOptions{PageSize: 1}
		_, err = s.gdriveService.ListFilesInFolder(info.ID, opts)
	case gdriveSvc.ResourceTypeFile:
		// If GetDriveInfo succeeded for a file, we can assume access.
		err = nil
	}

	if err != nil {
		log.WithError(err).Errorf("failed to list files for resource %s: %v", info.ID, err)
		return nil, errors.ErrUnauthorized
	}

	return info, nil
}

// CompleteSetupResponse contains the response from CompleteSetup
type CompleteSetupResponse struct {
	AppliedPathConfig *model.PathConfig
}

// CompleteSetup saves the Google Drive configuration settings for a tenant using JSON config.
func (s *service) CompleteSetup(ctx context.Context, req *CompleteSetupRequest) (*CompleteSetupResponse, error) {
	log := logger.WithCtx(ctx, "[GdriveService][CompleteSetup]")

	info, err := s.gdriveService.ParseSharedURL(req.URLOrID)
	if err != nil {
		log.WithError(err).Errorf("failed to parse or get info from url: %s", req.URLOrID)
		return nil, errors.NewBadDataErr("Invalid Google Drive URL or ID. Please provide a valid URL or the resource ID.")
	}

	if req.TenantID == 0 {
		return nil, errors.ErrUnauthorized
	}

	// Test access to the resource before saving configuration
	if req.Enabled {
		switch info.Type {
		case gdriveSvc.ResourceTypeSharedDrive:
			// Use pagination options for the new interface - just test with minimal page size
			opts := &gdriveSvc.PaginationOptions{PageSize: 1}
			_, err = s.gdriveService.ListFilesInSharedDrive(info.ID, opts)
		case gdriveSvc.ResourceTypeFolder:
			// For folders, test using ListFilesInFolder with pagination
			opts := &gdriveSvc.PaginationOptions{PageSize: 1}
			_, err = s.gdriveService.ListFilesInFolder(info.ID, opts)
		case gdriveSvc.ResourceTypeFile:
			// If ParseSharedURL succeeded for a file, we can assume access.
			err = nil
		}

		if err != nil {
			log.WithError(err).Errorf("failed to access resource %s during setup: %v", info.ID, err)
			return nil, errors.NewBadDataErr("Invalid Google Drive URL or ID. Please provide a valid URL or the resource ID.")
		}
	}

	// Create GDrive configuration
	config := &model.GDriveConfig{
		Enabled:      req.Enabled,
		RootID:       info.ID,
		ResourceType: string(info.Type),
	}

	// Set DriveID if applicable
	if info.Type == gdriveSvc.ResourceTypeSharedDrive {
		// For shared drives, the drive ID is the resource ID itself
		config.DriveID = info.ID
	} else if info.DriveID != "" {
		// For folders/files within shared drives, use the DriveID from info
		config.DriveID = info.DriveID
	}

	// Set PathConfig with defaults if provided or use defaults
	config.PathConfig = s.mergePathConfigWithDefaults(req.PathConfig)

	// Convert config to JSON
	configJSON, err := config.ToJSON()
	if err != nil {
		log.WithError(err).Error("failed to marshal gdrive config to JSON")
		return nil, errors.NewInternalErr("failed to process gdrive configuration")
	}

	// Upsert Google Drive configuration as single JSON setting
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("tenant_id", req.TenantID),
			model.NewFilterE("key", model.KeyGdriveConfig),
		},
	}
	setting := &model.DocumentSetting{
		TenantID: req.TenantID,
		Key:      model.KeyGdriveConfig,
		Value:    configJSON,
	}

	if err := s.docSettingRepo.CreateOrUpdate(ctx, query, setting); err != nil {
		log.WithError(err).Error("failed to upsert gdrive_config setting")
		return nil, errors.NewInternalErr("failed to save gdrive configuration")
	}

	log.Infof("completed setup for drive/folder: %s (config: %s)", req.URLOrID, configJSON)

	// Return response with applied PathConfig
	response := &CompleteSetupResponse{
		AppliedPathConfig: config.PathConfig,
	}

	return response, nil
}

// getDefaultPathConfig returns default PathConfig values
func (s *service) getDefaultPathConfig() *model.PathConfig {
	return &model.PathConfig{
		ClientFolderPath:   "{short_name|name} - {code}",              // SharePoint-compatible
		MatterFolderPath:   "{client_folder}/matters/{name} - {code}", // Nested under client
		CaseFormat:         "original",                                // No case transformation
		MaxLength:          255,                                       // Google Drive limit
		InvalidCharReplace: "_",                                       // Safe replacement character
	}
}

// mergePathConfigWithDefaults merges provided PathConfig with defaults
func (s *service) mergePathConfigWithDefaults(provided *model.PathConfig) *model.PathConfig {
	defaults := s.getDefaultPathConfig()

	// If no PathConfig provided, return defaults
	if provided == nil {
		return defaults
	}

	// Merge provided values with defaults
	result := &model.PathConfig{
		ClientFolderPath:   defaults.ClientFolderPath,
		MatterFolderPath:   defaults.MatterFolderPath,
		CaseFormat:         defaults.CaseFormat,
		MaxLength:          defaults.MaxLength,
		InvalidCharReplace: defaults.InvalidCharReplace,
	}

	// Override with provided values if not empty
	if provided.ClientFolderPath != "" {
		result.ClientFolderPath = provided.ClientFolderPath
	}
	if provided.MatterFolderPath != "" {
		result.MatterFolderPath = provided.MatterFolderPath
	}
	if provided.CaseFormat != "" {
		result.CaseFormat = provided.CaseFormat
	}
	if provided.MaxLength > 0 {
		result.MaxLength = provided.MaxLength
	}
	if provided.InvalidCharReplace != "" {
		result.InvalidCharReplace = provided.InvalidCharReplace
	}

	return result
}

// CreateClientFolder creates a folder for a new client
func (s *service) CreateClientFolder(ctx context.Context, req *CreateClientFolderRequest) (*CreateClientFolderResponse, error) {
	return s.folderService.CreateClientFolder(ctx, req)
}

// CreateMatterParentFolder creates the "Matters" parent folder under a client folder
func (s *service) CreateMatterParentFolder(ctx context.Context, req *CreateMatterParentFolderRequest) (*CreateMatterParentFolderResponse, error) {
	return s.folderService.CreateMatterParentFolder(ctx, req)
}

// CreateMatterFolder creates a folder for a specific matter
func (s *service) CreateMatterFolder(ctx context.Context, req *CreateMatterFolderRequest) (*CreateMatterFolderResponse, error) {
	return s.folderService.CreateMatterFolder(ctx, req)
}

// GetFolderService returns the folder service instance for event consumers
func (s *service) GetFolderService() FolderService {
	return s.folderService
}

// GetDocumentSettingRepo returns the document setting repository for event consumers
func (s *service) GetDocumentSettingRepo() repositories.DocumentSettingRepository {
	return s.docSettingRepo
}

// GetDocumentMappingRepo returns the document mapping repository for event consumers
func (s *service) GetDocumentMappingRepo() repositories.DocumentMappingRepository {
	return s.docMappingRepo
}

// GetMatterEventConsumer returns the matter event consumer instance for event handlers
func (s *service) GetMatterEventConsumer() *MatterEventConsumer {
	return s.matterConsumer
}

// SetPermissionSyncer sets the permission syncer for event consumers
func (s *service) SetPermissionSyncer(syncer PermissionSyncer) {
	// Update permission syncer in event consumers
	s.clientConsumer.permissionSyncer = syncer
	s.matterConsumer.permissionSyncer = syncer
}

// GetClientEventConsumer returns the client event consumer instance for event handlers
func (s *service) GetClientEventConsumer() *ClientEventConsumer {
	return s.clientConsumer
}
