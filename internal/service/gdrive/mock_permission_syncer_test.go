package gdrive_test

import (
	"context"

	"github.com/stretchr/testify/mock"
)

// MockPermissionSyncer is a mock implementation of PermissionSyncer
type MockPermissionSyncer struct {
	mock.Mock
}

func (m *MockPermissionSyncer) SyncGoogleDrivePermissions(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string) error {
	args := m.Called(ctx, tenantID, objectType, objectID, ownerEmails)
	return args.Error(0)
}
