package gdrive

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
)

// PermissionSyncer interface for syncing Google Drive permissions
type PermissionSyncer interface {
	SyncGoogleDrivePermissions(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string) error
}

// ClientEventData represents the client data from event payload
type ClientEventData struct {
	ID         uint64            `json:"id"`
	Name       string            `json:"name"`
	ShortName  string            `json:"short_name"`
	Code       string            `json:"code"`
	TenantID   uint64            `json:"tenant_id"`
	Owners     []uint64          `json:"owners"`
	OwnerUsers []model.OwnerUser `json:"owner_users"`
}

// ClientEventExtra represents the extra data in event payload
type ClientEventExtra struct {
	Current *ClientEventData `json:"current"`
	Old     *ClientEventData `json:"old"`
}

// ClientEventBody represents the full event body structure
type ClientEventBody struct {
	ID         uint64            `json:"id"`
	Name       string            `json:"name"`
	ShortName  string            `json:"short_name"`
	Code       string            `json:"code"`
	TenantID   uint64            `json:"tenant_id"`
	Owners     []uint64          `json:"owners"`
	OwnerUsers []model.OwnerUser `json:"owner_users"`
	Extra      *ClientEventExtra `json:"extra"`
}

// ClientEventPayload represents the complete event payload
type ClientEventPayload struct {
	Topic string           `json:"topic"`
	Body  *ClientEventBody `json:"body"`
}

// ClientEventConsumer handles client-related events for Google Drive folder creation
type ClientEventConsumer struct {
	folderService    FolderService
	docSettingRepo   repositories.DocumentSettingRepository
	permissionSyncer PermissionSyncer // Interface for permission sync
}

// NewClientEventConsumer creates a new client event consumer
func NewClientEventConsumer(
	folderService FolderService,
	docSettingRepo repositories.DocumentSettingRepository,
	permissionSyncer PermissionSyncer,
) *ClientEventConsumer {
	return &ClientEventConsumer{
		folderService:    folderService,
		docSettingRepo:   docSettingRepo,
		permissionSyncer: permissionSyncer,
	}
}

// HandleClientCreated processes client_created events and triggers folder creation
func (c *ClientEventConsumer) HandleClientCreated(ctx context.Context, payloadJSON string) error {
	log := logger.WithCtx(ctx, "HandleClientCreated")

	// Parse event payload
	var payload ClientEventPayload
	if err := json.Unmarshal([]byte(payloadJSON), &payload); err != nil {
		log.WithField("payload", payloadJSON).
			WithError(err).
			Error("Failed to parse client event payload")
		return fmt.Errorf("failed to parse event payload: %w", err)
	}

	// Validate payload
	if payload.Body == nil {
		log.WithField("payload", payloadJSON).
			Error("Event body is missing")
		return fmt.Errorf("event body is missing")
	}

	body := payload.Body
	log.WithFields(map[string]interface{}{
		"client_id":   body.ID,
		"tenant_id":   body.TenantID,
		"client_name": body.Name,
		"topic":       payload.Topic,
	}).Info("Received client event")

	// Check if Google Drive is enabled for this tenant
	config, err := c.getGDriveConfig(ctx, body.TenantID)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"tenant_id": body.TenantID,
		}).WithError(err).Error("Failed to get Google Drive config")
		return fmt.Errorf("failed to get Google Drive config: %w", err)
	}

	if config == nil || !config.Enabled {
		log.WithFields(map[string]interface{}{
			"tenant_id": body.TenantID,
			"operation": "HandleClientCreated",
		}).Info("Google Drive operation skipped due to disabled integration")
		return nil // Return gracefully, don't error
	}

	// Create folder creation request
	req := &CreateClientFolderRequest{
		TenantID:   body.TenantID,
		ClientID:   body.ID,
		ClientName: body.Name,
		ClientCode: body.Code,
		ShortName:  body.ShortName,
		Config:     config,
	}

	// Call folder service to create client folder
	resp, err := c.folderService.CreateClientFolder(ctx, req)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"client_id": body.ID,
			"tenant_id": body.TenantID,
		}).WithError(err).Error("Failed to create client folder")

		// Record failure for retry mechanism
		if recordErr := c.recordFailure(ctx, "client_folder_creation", payloadJSON, err.Error()); recordErr != nil {
			log.WithError(recordErr).Error("Failed to record failure for retry")
		}

		return fmt.Errorf("failed to create client folder: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"client_id":   body.ID,
		"tenant_id":   body.TenantID,
		"folder_id":   resp.FolderID,
		"folder_name": resp.FolderName,
	}).Info("Successfully created client folder")

	// Sync permissions if permission syncer is available and owner users exist
	if c.permissionSyncer != nil && len(body.OwnerUsers) > 0 {
		ownerEmails := make([]string, 0, len(body.OwnerUsers))
		for _, owner := range body.OwnerUsers {
			email := strings.TrimSpace(owner.Email)
			if email != "" {
				ownerEmails = append(ownerEmails, email)
			}
		}

		if len(ownerEmails) > 0 {
			log.WithFields(map[string]interface{}{
				"client_id":    body.ID,
				"tenant_id":    body.TenantID,
				"owner_emails": ownerEmails,
			}).Info("Syncing permissions for created client folder")

			err = c.permissionSyncer.SyncGoogleDrivePermissions(
				ctx,
				body.TenantID,
				"client",
				body.ID,
				ownerEmails,
			)
			if err != nil {
				log.WithFields(map[string]interface{}{
					"client_id": body.ID,
					"tenant_id": body.TenantID,
				}).WithError(err).Error("Failed to sync permissions for client folder")
				// Don't fail the entire operation if permission sync fails
				// Log the error and continue
			} else {
				log.WithFields(map[string]interface{}{
					"client_id": body.ID,
					"tenant_id": body.TenantID,
				}).Info("Successfully synced permissions for client folder")
			}
		} else {
			log.WithFields(map[string]interface{}{
				"client_id": body.ID,
				"tenant_id": body.TenantID,
			}).Warn("No valid owner emails found for permission sync")
		}
	}

	return nil
}

// HandleClientUpdated processes client_updated events and triggers folder updates
func (c *ClientEventConsumer) HandleClientUpdated(ctx context.Context, payloadJSON string) error {
	log := logger.WithCtx(ctx, "HandleClientUpdated")

	// Parse event payload
	var payload ClientEventPayload
	if err := json.Unmarshal([]byte(payloadJSON), &payload); err != nil {
		log.WithField("payload", payloadJSON).
			WithError(err).
			Error("Failed to parse client update event payload")
		return fmt.Errorf("failed to parse event payload: %w", err)
	}

	// Validate payload
	if payload.Body == nil {
		log.WithField("payload", payloadJSON).
			Error("Event body is missing")
		return fmt.Errorf("event body is missing")
	}

	body := payload.Body
	log.WithFields(map[string]interface{}{
		"client_id":   body.ID,
		"tenant_id":   body.TenantID,
		"client_name": body.Name,
		"topic":       payload.Topic,
	}).Info("Received client update event")

	// Check if Google Drive is enabled for this tenant
	config, err := c.getGDriveConfig(ctx, body.TenantID)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"tenant_id": body.TenantID,
		}).WithError(err).Error("Failed to get Google Drive config")
		return fmt.Errorf("failed to get Google Drive config: %w", err)
	}

	if config == nil || !config.Enabled {
		log.WithFields(map[string]interface{}{
			"tenant_id": body.TenantID,
			"operation": "HandleClientUpdated",
		}).Info("Google Drive operation skipped due to disabled integration")
		return nil // Return gracefully, don't error
	}

	// Create folder rename request
	req := &RenameClientFolderRequest{
		TenantID:   body.TenantID,
		ClientID:   body.ID,
		ClientName: body.Name,
		ClientCode: body.Code,
		ShortName:  body.ShortName,
		Config:     config,
	}

	// Call folder service to rename client folder (will create if not exists)
	log.WithFields(map[string]interface{}{
		"client_id":   body.ID,
		"tenant_id":   body.TenantID,
		"client_name": body.Name,
		"client_code": body.Code,
	}).Info("Attempting to rename/create client folder")

	resp, err := c.folderService.RenameClientFolder(ctx, req)
	if err != nil {
		log.WithFields(map[string]interface{}{
			"client_id": body.ID,
			"tenant_id": body.TenantID,
		}).WithError(err).Error("Failed to rename/create client folder")

		// Record failure for retry mechanism
		if recordErr := c.recordFailure(ctx, "client_folder_rename", payloadJSON, err.Error()); recordErr != nil {
			log.WithError(recordErr).Error("Failed to record failure for retry")
		}

		return fmt.Errorf("failed to rename/create client folder: %w", err)
	}

	// Log success based on what actually happened
	if resp != nil {
		if resp.OldName != resp.NewName {
			log.WithFields(map[string]interface{}{
				"client_id":     body.ID,
				"tenant_id":     body.TenantID,
				"folder_id":     resp.FolderID,
				"old_name":      resp.OldName,
				"new_name":      resp.NewName,
				"web_view_link": resp.WebViewLink,
			}).Info("Successfully renamed client folder")
		} else {
			log.WithFields(map[string]interface{}{
				"client_id":     body.ID,
				"tenant_id":     body.TenantID,
				"folder_id":     resp.FolderID,
				"folder_name":   resp.NewName,
				"web_view_link": resp.WebViewLink,
			}).Info("Client folder name unchanged, no rename needed")
		}
	} else {
		log.WithFields(map[string]interface{}{
			"client_id": body.ID,
			"tenant_id": body.TenantID,
		}).Warn("Rename operation completed but no response received")
	}

	// Sync permissions if permission syncer is available
	if c.permissionSyncer != nil {
		ownerEmails := make([]string, 0, len(body.OwnerUsers))
		for _, owner := range body.OwnerUsers {
			email := strings.TrimSpace(owner.Email)
			if email != "" {
				ownerEmails = append(ownerEmails, email)
			}
		}

		if len(ownerEmails) == 0 {
			log.WithFields(map[string]interface{}{
				"client_id": body.ID,
				"tenant_id": body.TenantID,
			}).Warn("No owner emails found - will remove all existing permissions")
		}

		log.WithFields(map[string]interface{}{
			"client_id":    body.ID,
			"tenant_id":    body.TenantID,
			"owner_emails": ownerEmails,
		}).Info("Syncing permissions for updated client folder")

		err = c.permissionSyncer.SyncGoogleDrivePermissions(
			ctx,
			body.TenantID,
			"client",
			body.ID,
			ownerEmails,
		)
		if err != nil {
			log.WithFields(map[string]interface{}{
				"client_id": body.ID,
				"tenant_id": body.TenantID,
			}).WithError(err).Error("Failed to sync permissions for updated client folder")
			// Don't fail the entire operation if permission sync fails
			// Log the error and continue
		} else {
			log.WithFields(map[string]interface{}{
				"client_id": body.ID,
				"tenant_id": body.TenantID,
			}).Info("Successfully synced permissions for updated client folder")
		}
	}

	return nil
}

// getGDriveConfig retrieves the Google Drive configuration for a tenant
func (c *ClientEventConsumer) getGDriveConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	log := logger.WithCtx(ctx, "getGDriveConfig")

	setting, err := c.docSettingRepo.GetValueByKey(ctx, tenantID, model.KeyGdriveConfig)
	if err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).
			Error("Failed to get Google Drive config from database")
		return nil, err
	}

	config := &model.GDriveConfig{}
	if err := config.FromJSON(setting.Value); err != nil {
		log.WithError(err).WithField("tenant_id", tenantID).
			Error("Failed to parse Google Drive config")
		return nil, err
	}

	return config, nil
}

// recordFailure records failed events for retry mechanism
// TODO: Implement this in subtask 13 (Retry mechanism)
func (c *ClientEventConsumer) recordFailure(ctx context.Context, eventType, payload, errorMsg string) error {
	log := logger.WithCtx(ctx, "recordFailure")

	log.WithFields(map[string]interface{}{
		"event_type": eventType,
		"error":      errorMsg,
	}).Error("Recording event failure for retry (not implemented yet)")

	// For now, just log the failure
	// This will be implemented in subtask 13
	return nil
}
