package gdrive

import (
	"context"
	"testing"

	"bilabl/docman/domain/model"
	mock_gdrive "bilabl/docman/mocks/gdrive"
	mock_repositories "bilabl/docman/mocks/repositories"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
)

func TestSearchDocuments_ResolvesRootToSharedDriveID(t *testing.T) {
	// Setup mocks
	mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := mock_gdrive.NewMockDriveClient(t)

	// Test data
	testTenantID := uint64(123)
	testSharedDriveID := "0ABCdefG1hIjKlMnOpQrStUvWxYz"
	testSearchKeyword := "test document"

	// Setup mock for DocumentSetting query
	mockDocumentSetting := &model.DocumentSetting{
		TenantID: testTenantID,
		Key:      model.KeyGdriveConfig,
		Value:    `{"enabled":true,"root_id":"` + testSharedDriveID + `","drive_id":"` + testSharedDriveID + `","resource_type":"shared_drive"}`,
	}

	mockDocSettingRepo.On("GetValueByKey", mock.Anything, testTenantID, model.KeyGdriveConfig).
		Return(mockDocumentSetting, nil).Once()

	// Setup mock for Google Drive API call - verify it uses native drive scoping
	mockDriveClient.On("ListFilesWithQuery",
		mock.MatchedBy(func(query string) bool {
			// For shared drive search, should search entire drive without parent constraint
			expectedQuery := "fullText contains 'test document' and trashed=false"
			return query == expectedQuery
		}),
		testSharedDriveID, // Should pass the shared drive ID for native API scoping
	).Return([]*drive.File{
		{
			Id:      "test-file-1",
			Name:    "test document.pdf",
			DriveId: testSharedDriveID, // File belongs to target shared drive
		},
	}, nil).Once()

	// Create service
	service := NewDocumentService(
		mockDocRepo,
		mockMappingRepo,
		mockUploadSessionRepo,
		mockDocSettingRepo,
		mockDriveClient,
	)

	// Execute search with "root" as ParentDriveID
	req := &SearchDocumentsRequest{
		TenantID:      testTenantID,
		ParentDriveID: "root", // This should be resolved to shared drive ID
		Query:         testSearchKeyword,
		PageSize:      100,
	}

	ctx := context.Background()
	result, err := service.SearchDocuments(ctx, req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Documents)) // Should return 1 file (native API scoping)
	assert.Equal(t, "test-file-1", result.Documents[0].DriveFileID)

	// Verify all mocks were called as expected
	mockDocSettingRepo.AssertExpectations(t)
	mockDriveClient.AssertExpectations(t)
}

func TestSearchDocuments_EmptyParentDriveID_ResolvesToSharedDrive(t *testing.T) {
	// Setup mocks
	mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := mock_gdrive.NewMockDriveClient(t)

	// Test data
	testTenantID := uint64(123)
	testSharedDriveID := "0ABCdefG1hIjKlMnOpQrStUvWxYz"
	testSearchKeyword := "test document"

	// Setup mock for DocumentSetting query
	mockDocumentSetting := &model.DocumentSetting{
		TenantID: testTenantID,
		Key:      model.KeyGdriveConfig,
		Value:    `{"enabled":true,"root_id":"` + testSharedDriveID + `","drive_id":"` + testSharedDriveID + `","resource_type":"shared_drive"}`,
	}

	mockDocSettingRepo.On("GetValueByKey", mock.Anything, testTenantID, model.KeyGdriveConfig).
		Return(mockDocumentSetting, nil).Once()

	// Setup mock for Google Drive API call
	mockDriveClient.On("ListFilesWithQuery",
		mock.MatchedBy(func(query string) bool {
			expectedQuery := "fullText contains 'test document' and trashed=false"
			return query == expectedQuery
		}),
		testSharedDriveID, // Should pass shared drive ID for native scoping
	).Return([]*drive.File{}, nil).Once()

	// Create service
	service := NewDocumentService(
		mockDocRepo,
		mockMappingRepo,
		mockUploadSessionRepo,
		mockDocSettingRepo,
		mockDriveClient,
	)

	// Execute search with empty ParentDriveID
	req := &SearchDocumentsRequest{
		TenantID:      testTenantID,
		ParentDriveID: "", // Empty should also be resolved to shared drive ID
		Query:         testSearchKeyword,
		PageSize:      100,
	}

	ctx := context.Background()
	result, err := service.SearchDocuments(ctx, req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify all mocks were called as expected
	mockDocSettingRepo.AssertExpectations(t)
	mockDriveClient.AssertExpectations(t)
}

func TestSearchDocuments_SpecificParentID_DoesNotResolve(t *testing.T) {
	// Setup mocks
	mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := mock_gdrive.NewMockDriveClient(t)

	// Test data
	testTenantID := uint64(123)
	testSpecificFolderID := "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
	testSearchKeyword := "test document"

	// Setup mock for Google Drive API call - should use specific folder ID directly
	mockDriveClient.On("ListFilesWithQuery",
		mock.MatchedBy(func(query string) bool {
			expectedQuery := "fullText contains 'test document' and '" + testSpecificFolderID + "' in parents and trashed=false"
			return query == expectedQuery
		}),
		"", // No drive scoping for specific folder searches
	).Return([]*drive.File{}, nil).Once()

	// Create service
	service := NewDocumentService(
		mockDocRepo,
		mockMappingRepo,
		mockUploadSessionRepo,
		mockDocSettingRepo,
		mockDriveClient,
	)

	// Execute search with specific ParentDriveID (should NOT query DocumentSetting)
	req := &SearchDocumentsRequest{
		TenantID:      testTenantID,
		ParentDriveID: testSpecificFolderID, // Specific ID should be used directly
		Query:         testSearchKeyword,
		PageSize:      100,
	}

	ctx := context.Background()
	result, err := service.SearchDocuments(ctx, req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify DocumentSetting was NOT queried (since we have specific folder ID)
	mockDocSettingRepo.AssertNotCalled(t, "GetValueByKey")
	mockDriveClient.AssertExpectations(t)
}

func TestSearchDocuments_ConfigNotFound_ReturnsError(t *testing.T) {
	// Setup mocks
	mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := mock_gdrive.NewMockDriveClient(t)

	// Test data
	testTenantID := uint64(123)
	testSearchKeyword := "test document"

	// Setup mock for DocumentSetting query to return error
	mockDocSettingRepo.On("GetValueByKey", mock.Anything, testTenantID, model.KeyGdriveConfig).
		Return(nil, assert.AnError).Once()

	// Create service
	service := NewDocumentService(
		mockDocRepo,
		mockMappingRepo,
		mockUploadSessionRepo,
		mockDocSettingRepo,
		mockDriveClient,
	)

	// Execute search with "root" as ParentDriveID
	req := &SearchDocumentsRequest{
		TenantID:      testTenantID,
		ParentDriveID: "root",
		Query:         testSearchKeyword,
		PageSize:      100,
	}

	ctx := context.Background()
	result, err := service.SearchDocuments(ctx, req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "Failed to get Google Drive configuration for tenant")

	// Verify mocks
	mockDocSettingRepo.AssertExpectations(t)
	mockDriveClient.AssertNotCalled(t, "ListFilesWithQuery")
}
