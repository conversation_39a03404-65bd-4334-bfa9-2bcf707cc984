package gdrive

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"strings"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"google.golang.org/api/drive/v3"
)

// Validation constants
const (
	MaxDocumentNameLength = 255
	MinDocumentNameLength = 1
	MaxPathDepth          = 10
)

// Invalid characters for document names (Google Drive restrictions)
var invalidChars = []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", "\x00"}

// Path traversal patterns
var pathTraversalPatterns = []string{"../", "..\\", "..", "~/"}

// Import model constants for document types
// DocTypeDir = 1 (Directory/Folder) from model package
// DocTypeFile = 2 (File) from model package

// DocumentService interface defines operations for managing Google Drive documents
type DocumentService interface {
	// Core CRUD operations (using string IDs for Google Drive file IDs)
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error)
	GetDocument(ctx context.Context, driveFileID string) (*DocumentResponse, error)
	UpdateDocument(ctx context.Context, driveFileID string, req *UpdateDocumentRequest) (*DocumentResponse, error)
	DeleteDocument(ctx context.Context, driveFileID string) error

	// File operations (SharePoint-compatible)
	UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error)

	// Upload session operations (2-step upload proxy)
	CreateUploadSession(ctx context.Context, req *CreateUploadSessionRequest) (*UploadSessionResponse, error)
	UploadFileContent(ctx context.Context, sessionToken string, content io.Reader, contentType string, contentLength int64) (*UploadContentResponse, error)
	GetUploadSession(ctx context.Context, sessionToken string) (*UploadSessionResponse, error)

	// Hierarchical path operations
	ResolveHierarchicalPath(ctx context.Context, req *ResolveHierarchicalPathRequest) (*ResolveHierarchicalPathResponse, error)

	// List and search operations
	ListDocuments(ctx context.Context, req *ListDocumentsRequest) (*DocumentListResponse, error)
	SearchDocuments(ctx context.Context, req *SearchDocumentsRequest) (*DocumentListResponse, error)

	// Getter methods for testing/events
	GetDocumentRepository() repositories.DocumentRepository
	GetDocumentMappingRepository() repositories.DocumentMappingRepository
	GetGDriveClient() gdrive.DriveClient

	// Configuration methods
	GetTenantConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error)
}

// documentService implements the DocumentService interface
type documentService struct {
	documentRepo        repositories.DocumentRepository
	mappingRepo         repositories.DocumentMappingRepository
	uploadSessionRepo   repositories.UploadSessionRepository
	documentSettingRepo repositories.DocumentSettingRepository
	driveClient         gdrive.DriveClient
}

// NewDocumentService creates a new instance of DocumentService
func NewDocumentService(
	documentRepo repositories.DocumentRepository,
	mappingRepo repositories.DocumentMappingRepository,
	uploadSessionRepo repositories.UploadSessionRepository,
	documentSettingRepo repositories.DocumentSettingRepository,
	driveClient gdrive.DriveClient,
) DocumentService {
	return &documentService{
		documentRepo:        documentRepo,
		mappingRepo:         mappingRepo,
		uploadSessionRepo:   uploadSessionRepo,
		documentSettingRepo: documentSettingRepo,
		driveClient:         driveClient,
	}
}

// GetDocumentRepository returns the document repository for testing/events
func (s *documentService) GetDocumentRepository() repositories.DocumentRepository {
	return s.documentRepo
}

// GetDocumentMappingRepository returns the document mapping repository for testing/events
func (s *documentService) GetDocumentMappingRepository() repositories.DocumentMappingRepository {
	return s.mappingRepo
}

// GetGDriveClient returns the Google Drive client for testing/events
func (s *documentService) GetGDriveClient() gdrive.DriveClient {
	return s.driveClient
}

// validateDocumentName validates document name for security and business rules
func validateDocumentName(name string) error {
	// Check length constraints
	if len(name) < MinDocumentNameLength {
		return fmt.Errorf("document name cannot be empty")
	}
	if len(name) > MaxDocumentNameLength {
		return fmt.Errorf("document name exceeds maximum length of %d characters", MaxDocumentNameLength)
	}

	// Check for path traversal attempts first (more specific)
	lowerName := strings.ToLower(name)
	for _, pattern := range pathTraversalPatterns {
		if strings.Contains(lowerName, pattern) {
			return fmt.Errorf("document name contains invalid path traversal pattern: %s", pattern)
		}
	}

	// Check for invalid characters
	for _, char := range invalidChars {
		if strings.Contains(name, char) {
			return fmt.Errorf("document name contains invalid character: %s", char)
		}
	}

	// Check for leading/trailing whitespace
	if strings.TrimSpace(name) != name {
		return fmt.Errorf("document name cannot have leading or trailing whitespace")
	}

	return nil
}

// validateCreateDocumentRequest validates the entire request
func validateCreateDocumentRequest(req *CreateDocumentRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}

	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}

	if err := validateDocumentName(req.Name); err != nil {
		return fmt.Errorf("invalid document name: %w", err)
	}

	return nil
}

// validateUploadDocumentRequest validates upload request parameters
func validateUploadDocumentRequest(req *UploadDocumentRequest) error {
	if req == nil {
		return fmt.Errorf("upload request is required")
	}

	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}

	if req.FileName == "" {
		return fmt.Errorf("file name is required")
	}

	if req.ParentDriveID == "" {
		return fmt.Errorf("parent drive ID is required")
	}

	// Validate file name (reuse existing validation)
	if err := validateDocumentName(req.FileName); err != nil {
		return fmt.Errorf("invalid file name: %w", err)
	}

	return nil
}

// CreateDocument creates a new document in Google Drive only (no database tracking)
func (s *documentService) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "CreateDocument")
	log.Info("Creating new document in Google Drive")

	// Enhanced input validation
	if err := validateCreateDocumentRequest(req); err != nil {
		log.WithError(err).Error("Request validation failed")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Invalid request: %v", err),
		}
	}

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Determine parent ID - fail fast for invalid parent info
	var parentID string
	var err error

	if req.ParentID == "" {
		// If no parent_id, then object_type and object_id are required
		if req.ObjectType == "" || req.ObjectID == 0 {
			log.Error("Either parent_id or both object_type and object_id must be provided")
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Either parent_id or both object_type and object_id must be provided",
			}
		}

		// Query parent folder ID from object type and ID
		parentID, err = s.getParentFolderIDFromObject(ctx, req.ObjectType, req.ObjectID, req.TenantID)
		if err != nil {
			return nil, err // Error already logged in getParentFolderIDFromObject
		}
	} else {
		// Use provided parent ID directly
		parentID = req.ParentID
		log.Infof("Using provided parent ID parent_id=%s name=%s", parentID, req.Name)
	}

	// Handle unsupported document types - fail fast
	if req.DocType != model.DocTypeDir {
		log.Error("Creating files with content is not supported yet")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Creating files with content is not supported yet. Use UploadDocument instead.",
		}
	}

	// Check for duplicate folders in Google Drive
	if err := s.checkDuplicateFolder(ctx, req.Name, parentID); err != nil {
		log.WithError(err).Error("Duplicate folder check failed")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to check for duplicate folders",
			Details: err.Error(),
		}
	}

	// Create folder in Google Drive
	driveFolder, err := s.driveClient.CreateFolder(req.Name, parentID)
	if err != nil {
		log.WithError(err).Error("Failed to create folder in Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to create folder in Google Drive: %v", err),
		}
	}

	log.Infof("Successfully created folder in Google Drive drive_file_id=%s name=%s", driveFolder.Id, driveFolder.Name)

	// Return response with Google Drive information only
	return &DocumentResponse{
		DriveFileID:   driveFolder.Id,
		Name:          driveFolder.Name,
		TenantID:      req.TenantID,
		ParentDriveID: parentID,
		WebViewLink:   driveFolder.WebViewLink,
		IsFile:        false,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// checkDuplicateFolder checks if a folder with the same name already exists in the parent
func (s *documentService) checkDuplicateFolder(ctx context.Context, folderName, parentID string) error {
	log := logger.WithCtx(ctx, "checkDuplicateFolder")

	// Use Google Drive API to search for existing folders
	query := fmt.Sprintf("name='%s' and parents in '%s' and mimeType='application/vnd.google-apps.folder' and trashed=false",
		strings.ReplaceAll(folderName, "'", "\\'"), parentID)

	files, err := s.driveClient.ListFilesWithQuery(query, "")
	if err != nil {
		return fmt.Errorf("failed to search for existing folders: %w", err)
	}

	if len(files) > 0 {
		log.Warnf("Folder with same name already exists existing_folder_id=%s folder_name=%s parent_id=%s", files[0].Id, folderName, parentID)
		return fmt.Errorf("folder with name '%s' already exists in parent folder", folderName)
	}

	return nil
}

// getParentFolderIDFromObject queries the document mapping repository to find the parent folder ID
// for a given object type and object ID
func (s *documentService) getParentFolderIDFromObject(ctx context.Context, objectType string, objectID uint64, tenantID uint64) (string, error) {
	log := logger.WithCtx(ctx, "getParentFolderIDFromObject")
	log.Infof("Finding parent folder using object mapping object_type=%s object_id=%d tenant_id=%d", objectType, objectID, tenantID)

	// Validate object type - fail fast

	var docType string
	switch objectType {
	case "client":
		docType = model.DocTypeClient
	case "matter":
		docType = model.DocTypeMatter
	default:
		log.Errorf("Unsupported object type object_type=%s", objectType)
		return "", &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Unsupported object type: " + objectType,
		}
	}

	// Build query to find the mapping
	query := &model.Query{
		Filters: []*model.Filter{
			model.NewFilterE("type", docType),
			model.NewFilterE("tenant_id", tenantID),
			model.NewFilterE("object_id", objectID),
			model.NewFilterE("provider", model.DocProviderGoogle),
		},
	}

	// Find parent folder mapping
	mapping, err := s.mappingRepo.FindOne(ctx, query)
	if err != nil {
		// Check if this is a "record not found" error vs actual database error
		if model.IsNotFound(err) {
			log.Infof("Document mapping not found object_type=%s object_id=%d tenant_id=%d", objectType, objectID, tenantID)
			return "", &DocumentServiceError{
				Code:    ErrCodeDocumentNotFound,
				Message: fmt.Sprintf("No document mapping found for %s with ID %d", objectType, objectID),
			}
		}

		// This is an actual database/system error
		log.WithError(err).Errorf("Database error while finding document mapping object_type=%s object_id=%d", objectType, objectID)
		return "", &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Failed to query document mapping for %s with ID %d", objectType, objectID),
			Details: err.Error(),
		}
	}

	if mapping == nil {
		log.Errorf("Document mapping query returned nil object_type=%s object_id=%d", objectType, objectID)
		return "", &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: fmt.Sprintf("No document mapping found for %s with ID %d", objectType, objectID),
		}
	}

	// Success path - return the Drive ID
	log.Infof("Found parent folder drive ID from mapping object_type=%s object_id=%d parent_drive_id=%s", objectType, objectID, mapping.DriveID)

	return mapping.DriveID, nil
}

// GetDocument retrieves a document by its Google Drive file ID
func (s *documentService) GetDocument(ctx context.Context, driveFileID string) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "GetDocument")
	log.Infof("Retrieving document drive_file_id=%s", driveFileID)

	// Validate input
	if driveFileID == "" {
		log.Error("Missing drive file ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Drive file ID is required",
		}
	}

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Get file information from Google Drive
	driveFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get file info from Google Drive drive_file_id=%s", driveFileID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to retrieve document from Google Drive: %v", err),
		}
	}

	if driveFile == nil {
		log.Errorf("Document not found in Google Drive drive_file_id=%s", driveFileID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: fmt.Sprintf("Document with ID %s not found", driveFileID),
		}
	}

	// Determine if it's a file or folder
	isFile := driveFile.MimeType != "application/vnd.google-apps.folder"

	// Get parent folder ID
	var parentDriveID string
	if len(driveFile.Parents) > 0 {
		parentDriveID = driveFile.Parents[0]
	}

	log.Infof("Successfully retrieved document drive_file_id=%s name=%s is_file=%t", driveFileID, driveFile.Name, isFile)

	// Build response
	return &DocumentResponse{
		DriveFileID:   driveFile.Id,
		Name:          driveFile.Name,
		ParentDriveID: parentDriveID,
		Size:          driveFile.Size,
		ContentType:   driveFile.MimeType,
		WebViewLink:   driveFile.WebViewLink,
		IsFile:        isFile,
		CreatedAt:     parseGoogleDriveTime(driveFile.CreatedTime),
		UpdatedAt:     parseGoogleDriveTime(driveFile.ModifiedTime),
	}, nil
}

// parseGoogleDriveTime parses Google Drive timestamp format
func parseGoogleDriveTime(timeStr string) time.Time {
	if timeStr == "" {
		return time.Time{}
	}

	// Google Drive uses RFC3339 format
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		// Fallback to current time if parsing fails
		return time.Now()
	}
	return t
}

// UpdateDocument updates a document in Google Drive
func (s *documentService) UpdateDocument(ctx context.Context, driveFileID string, req *UpdateDocumentRequest) (*DocumentResponse, error) {
	log := logger.WithCtx(ctx, "UpdateDocument")
	log.Infof("Updating document drive_file_id=%s", driveFileID)

	// Validate input
	if driveFileID == "" {
		log.Error("Missing drive file ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Drive file ID is required",
		}
	}

	if req == nil {
		log.Error("Missing update request")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Update request is required",
		}
	}

	// Validate document name if provided
	if req.Name != "" {
		if err := validateDocumentName(req.Name); err != nil {
			log.WithError(err).Error("Invalid document name in update request")
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: fmt.Sprintf("Invalid document name: %v", err),
			}
		}
	}

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// First, verify the document exists
	existingFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get existing file info drive_file_id=%s", driveFileID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to retrieve existing document: %v", err),
		}
	}

	if existingFile == nil {
		log.Errorf("Document not found for update drive_file_id=%s", driveFileID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: fmt.Sprintf("Document with ID %s not found", driveFileID),
		}
	}

	// Check for duplicate name if name is being changed
	if req.Name != "" && req.Name != existingFile.Name {
		// Get parent folder ID for duplicate check
		var parentDriveID string
		if len(existingFile.Parents) > 0 {
			parentDriveID = existingFile.Parents[0]
		}

		if err := s.checkDuplicateFolder(ctx, req.Name, parentDriveID); err != nil {
			log.WithError(err).Error("Duplicate name check failed during update")
			return nil, &DocumentServiceError{
				Code:    ErrCodeDriveAPIError,
				Message: "Failed to check for duplicate names",
				Details: err.Error(),
			}
		}
	}

	// Update the document in Google Drive
	updatedFile, err := s.driveClient.RenameFile(driveFileID, req.Name)
	if err != nil {
		log.WithError(err).Errorf("Failed to update document in Google Drive drive_file_id=%s", driveFileID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to update document in Google Drive: %v", err),
		}
	}

	log.Infof("Successfully updated document drive_file_id=%s name=%s", driveFileID, updatedFile.Name)

	// Determine if it's a file or folder
	isFile := updatedFile.MimeType != "application/vnd.google-apps.folder"

	// Get parent folder ID
	var parentDriveID string
	if len(updatedFile.Parents) > 0 {
		parentDriveID = updatedFile.Parents[0]
	}

	// Build response
	return &DocumentResponse{
		DriveFileID:   updatedFile.Id,
		Name:          updatedFile.Name,
		ParentDriveID: parentDriveID,
		Size:          updatedFile.Size,
		ContentType:   updatedFile.MimeType,
		WebViewLink:   updatedFile.WebViewLink,
		IsFile:        isFile,
		CreatedAt:     parseGoogleDriveTime(updatedFile.CreatedTime),
		UpdatedAt:     parseGoogleDriveTime(updatedFile.ModifiedTime),
	}, nil
}

// DeleteDocument deletes a document from Google Drive
func (s *documentService) DeleteDocument(ctx context.Context, driveFileID string) error {
	log := logger.WithCtx(ctx, "DeleteDocument")
	log.Infof("Deleting document drive_file_id=%s", driveFileID)

	// Validate input
	if driveFileID == "" {
		log.Error("Missing drive file ID")
		return &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Drive file ID is required",
		}
	}

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// First, verify the document exists
	existingFile, err := s.driveClient.GetFileInfo(driveFileID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get file info before deletion drive_file_id=%s", driveFileID)
		return &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to retrieve document for deletion: %v", err),
		}
	}

	if existingFile == nil {
		log.Errorf("Document not found for deletion drive_file_id=%s", driveFileID)
		return &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: fmt.Sprintf("Document with ID %s not found", driveFileID),
		}
	}

	// Check if it's a folder and if it has children
	if existingFile.MimeType == "application/vnd.google-apps.folder" {
		// List files in the folder to check if it's empty
		files, err := s.driveClient.ListFilesInFolder(driveFileID, &gdrive.PaginationOptions{
			PageSize: 1, // Just check if there's at least one file
		})
		if err != nil {
			log.WithError(err).Errorf("Failed to check folder contents drive_file_id=%s", driveFileID)
			return &DocumentServiceError{
				Code:    ErrCodeDriveAPIError,
				Message: "Failed to check folder contents before deletion",
				Details: err.Error(),
			}
		}

		if len(files.Files) > 0 {
			log.Errorf("Cannot delete non-empty folder drive_file_id=%s", driveFileID)
			return &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Cannot delete folder that contains files or subfolders",
			}
		}
	}

	// Delete the document from Google Drive
	err = s.driveClient.DeleteFile(driveFileID)
	if err != nil {
		log.WithError(err).Errorf("Failed to delete document from Google Drive drive_file_id=%s", driveFileID)
		return &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to delete document from Google Drive: %v", err),
		}
	}

	log.Infof("Successfully deleted document drive_file_id=%s name=%s", driveFileID, existingFile.Name)
	return nil
}

// UploadDocument generates upload URL for client-side file upload (SharePoint-compatible)
func (s *documentService) UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error) {
	log := logger.WithCtx(ctx, "UploadDocument")

	// Validate input first (before logging to avoid nil pointer dereference)
	if err := validateUploadDocumentRequest(req); err != nil {
		log.WithError(err).Error("Upload request validation failed")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Invalid upload request: %v", err),
		}
	}

	log.Infof("Generating upload URL file_name=%s parent_drive_id=%s", req.FileName, req.ParentDriveID)

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Generate upload URL using Google Drive API (matching SharePoint pattern)
	// Note: Using a placeholder file size since we don't have actual file content
	uploadInfo, err := s.driveClient.CreateResumableUploadURL(req.FileName, req.ParentDriveID, 0)
	if err != nil {
		log.WithError(err).Errorf("Failed to generate upload URL file_name=%s parent_drive_id=%s", req.FileName, req.ParentDriveID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to generate upload URL: %v", err),
		}
	}

	log.Infof("Successfully generated upload URL file_name=%s drive_file_id=%s", req.FileName, uploadInfo.FileID)

	return &UploadDocumentResponse{
		UploadURL:   uploadInfo.UploadURL,
		DriveFileID: uploadInfo.FileID,
		FileName:    req.FileName,
	}, nil
}

// generateSessionToken creates a globally unique cryptographically secure session token
func generateSessionToken() (string, error) {
	// Generate 32 random bytes for better uniqueness across tenants
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to hex and add timestamp and nanosecond precision for global uniqueness
	hexStr := hex.EncodeToString(bytes)
	timestamp := time.Now().UnixNano()

	return fmt.Sprintf("upload_%s_%d", hexStr, timestamp), nil
}

// validateCreateUploadSessionRequest validates the create upload session request
func validateCreateUploadSessionRequest(req *CreateUploadSessionRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}
	if req.FileName == "" {
		return fmt.Errorf("file_name is required")
	}
	if req.ParentDriveID == "" {
		return fmt.Errorf("parent_drive_id is required")
	}
	if len(req.FileName) > MaxDocumentNameLength {
		return fmt.Errorf("file_name exceeds maximum length of %d characters", MaxDocumentNameLength)
	}
	return nil
}

// CreateUploadSession creates a new upload session for 2-step upload process
func (s *documentService) CreateUploadSession(ctx context.Context, req *CreateUploadSessionRequest) (*UploadSessionResponse, error) {
	log := logger.WithCtx(ctx, "CreateUploadSession")

	// Validate input
	if err := validateCreateUploadSessionRequest(req); err != nil {
		log.WithError(err).Error("Upload session request validation failed")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Invalid upload session request: %v", err),
		}
	}

	log.Infof("Creating upload session file_name=%s parent_drive_id=%s tenant_id=%d", req.FileName, req.ParentDriveID, req.TenantID)

	// Set context timeout for external API calls
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Generate session token
	sessionToken, err := generateSessionToken()
	if err != nil {
		log.WithError(err).Error("Failed to generate session token")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to generate session token",
		}
	}

	// Generate Google Drive resumable upload URL
	uploadInfo, err := s.driveClient.CreateResumableUploadURL(req.FileName, req.ParentDriveID, 0)
	if err != nil {
		log.WithError(err).Errorf("Failed to generate Google Drive upload URL file_name=%s parent_drive_id=%s", req.FileName, req.ParentDriveID)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to generate upload URL: %v", err),
		}
	}

	// Create upload session record
	expiresAt := time.Now().Add(1 * time.Hour) // Default 1 hour expiration
	uploadSession := &model.UploadSession{
		SessionToken:    sessionToken,
		TenantID:        req.TenantID,
		FileName:        req.FileName,
		ParentDriveID:   req.ParentDriveID,
		GoogleUploadURL: uploadInfo.UploadURL,
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       expiresAt,
	}

	err = s.uploadSessionRepo.Create(ctx, uploadSession)
	if err != nil {
		log.WithError(err).Errorf("Failed to create upload session session_token=%s", sessionToken)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDatabaseError,
			Message: "Failed to create upload session",
		}
	}

	log.Infof("Successfully created upload session session_token=%s file_name=%s", sessionToken, req.FileName)

	return &UploadSessionResponse{
		SessionToken:    sessionToken,
		UploadURL:       sessionToken, // Return session token as upload URL for compatibility
		FileName:        req.FileName,
		Status:          string(model.UploadSessionStatusPending),
		ExpiresAt:       expiresAt,
		GoogleUploadURL: uploadInfo.UploadURL,
	}, nil
}

// GetUploadSession retrieves upload session information using globally unique session token
func (s *documentService) GetUploadSession(ctx context.Context, sessionToken string) (*UploadSessionResponse, error) {
	log := logger.WithCtx(ctx, "GetUploadSession")

	// Validate input
	if sessionToken == "" {
		log.Error("Missing session token")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Session token is required",
		}
	}

	log.Infof("Getting upload session session_token=%s", sessionToken)

	// Get session from database using globally unique token (no tenant filtering needed)
	session, err := s.uploadSessionRepo.GetByToken(ctx, sessionToken)
	if err != nil {
		log.WithError(err).Errorf("Failed to get upload session session_token=%s", sessionToken)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: "Upload session not found",
		}
	}

	log.Infof("Successfully retrieved upload session session_token=%s status=%s tenant_id=%d", sessionToken, session.Status, session.TenantID)

	return &UploadSessionResponse{
		SessionToken:    session.SessionToken,
		UploadURL:       session.SessionToken, // Return session token as upload URL for compatibility
		FileName:        session.FileName,
		Status:          string(session.Status),
		ExpiresAt:       session.ExpiresAt,
		GoogleUploadURL: session.GoogleUploadURL,
	}, nil
}

// UploadFileContent handles the actual file upload to Google Drive
func (s *documentService) UploadFileContent(ctx context.Context, sessionToken string, content io.Reader, contentType string, contentLength int64) (*UploadContentResponse, error) {
	log := logger.WithCtx(ctx, "UploadFileContent")

	// Validate input
	if sessionToken == "" {
		log.Error("Missing session token")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Session token is required",
		}
	}

	if content == nil {
		log.Error("Missing file content")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "File content is required",
		}
	}

	log.Infof("Uploading file content session_token=%s content_type=%s content_length=%d", sessionToken, contentType, contentLength)

	// Get session from database
	session, err := s.uploadSessionRepo.GetByToken(ctx, sessionToken)
	if err != nil {
		log.WithError(err).Errorf("Failed to get upload session session_token=%s", sessionToken)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDocumentNotFound,
			Message: "Upload session not found",
		}
	}

	// Validate session can accept upload
	if !session.CanUpload() {
		log.Errorf("Upload session cannot accept upload session_token=%s status=%s expired=%v", sessionToken, session.Status, session.IsExpired())
		if session.IsExpired() {
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Upload session has expired",
			}
		}
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: fmt.Sprintf("Upload session is not in pending state (current: %s)", session.Status),
		}
	}

	// Update session status to uploading
	err = s.uploadSessionRepo.UpdateStatus(ctx, sessionToken, model.UploadSessionStatusUploading)
	if err != nil {
		log.WithError(err).Errorf("Failed to update session status to uploading session_token=%s", sessionToken)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDatabaseError,
			Message: "Failed to update upload session status",
		}
	}

	// Update file info if provided
	if contentLength > 0 || contentType != "" {
		err = s.uploadSessionRepo.UpdateFileInfo(ctx, sessionToken, contentLength, contentType)
		if err != nil {
			log.WithError(err).Errorf("Failed to update file info session_token=%s", sessionToken)
			// Don't fail the upload for this, just log the error
		}
	}

	// Upload to Google Drive using resumable upload URL
	driveFile, err := s.driveClient.UploadToResumableURL(session.GoogleUploadURL, content, contentType)
	if err != nil {
		log.WithError(err).Errorf("Failed to upload to Google Drive session_token=%s", sessionToken)

		// Update session status to failed
		updateErr := s.uploadSessionRepo.UpdateStatus(ctx, sessionToken, model.UploadSessionStatusFailed)
		if updateErr != nil {
			log.WithError(updateErr).Errorf("Failed to update session status to failed session_token=%s", sessionToken)
		}

		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: fmt.Sprintf("Failed to upload file to Google Drive: %v", err),
		}
	}

	// Update session status to completed with Google file ID
	err = s.uploadSessionRepo.UpdateStatusAndFileID(ctx, sessionToken, model.UploadSessionStatusCompleted, driveFile.Id)
	if err != nil {
		log.WithError(err).Errorf("Failed to update session status to completed session_token=%s file_id=%s", sessionToken, driveFile.Id)
		return nil, &DocumentServiceError{
			Code:    ErrCodeDatabaseError,
			Message: "Failed to update upload session status",
		}
	}

	log.Infof("Successfully uploaded file session_token=%s drive_file_id=%s file_name=%s", sessionToken, driveFile.Id, driveFile.Name)

	return &UploadContentResponse{
		Status:       string(model.UploadSessionStatusCompleted),
		DriveFileID:  driveFile.Id,
		FileName:     driveFile.Name,
		FileSize:     driveFile.Size,
		ContentType:  driveFile.MimeType,
		SessionToken: sessionToken,
	}, nil
}

// ListDocuments lists documents in a folder
func (s *documentService) ListDocuments(ctx context.Context, req *ListDocumentsRequest) (*DocumentListResponse, error) {
	log := logger.WithCtx(ctx, "ListDocuments")
	log.WithField("parentDriveID", req.ParentDriveID).Info("Listing documents")

	// Validate request
	if req.TenantID == 0 {
		log.Error("Missing tenant ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing tenant ID",
		}
	}

	// Use "root" if no parent folder ID is specified
	parentID := req.ParentDriveID
	if parentID == "" {
		parentID = "root"
	}

	// Set up pagination options with Google Drive API limits
	pageSize := int64(req.PageSize)
	if pageSize <= 0 {
		pageSize = gdrive.DefaultPageSize // 100
	} else if pageSize > gdrive.MaxPageSize {
		pageSize = gdrive.MaxPageSize // 1000 - Google Drive API maximum
	}

	// List files from Google Drive
	paginationOpts := &gdrive.PaginationOptions{
		PageSize:  pageSize,
		PageToken: req.PageToken,
	}

	var result *gdrive.PaginatedResult
	var err error

	// Use the appropriate method based on the parent ID
	if parentID == "root" {
		result, err = s.driveClient.ListFiles(paginationOpts)
	} else {
		result, err = s.driveClient.ListFilesInFolder(parentID, paginationOpts)
	}

	if err != nil {
		log.WithError(err).Error("Failed to list files from Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to list files from Google Drive",
			Details: err.Error(),
		}
	}

	// Build response
	documents := make([]*DocumentResponse, 0, len(result.Files))
	for _, file := range result.Files {
		doc := &DocumentResponse{
			DriveFileID:   file.Id,
			Name:          file.Name,
			TenantID:      req.TenantID,
			ContentType:   file.MimeType,
			WebViewLink:   file.WebViewLink,
			DownloadLink:  file.WebContentLink,
			ParentDriveID: parentID,
			IsFile:        file.MimeType != gdrive.MimeTypeFolder,
			Size:          file.Size,
			CreatedAt:     parseGoogleDriveTime(file.CreatedTime),
			UpdatedAt:     parseGoogleDriveTime(file.ModifiedTime),
		}
		documents = append(documents, doc)
	}

	log.WithField("count", len(documents)).Info("Documents listed successfully")
	return &DocumentListResponse{
		Documents:     documents,
		NextPageToken: result.NextPageToken,
		Total:         len(documents),
	}, nil
}

// SearchDocuments searches for documents
func (s *documentService) SearchDocuments(ctx context.Context, req *SearchDocumentsRequest) (*DocumentListResponse, error) {
	log := logger.WithCtx(ctx, "SearchDocuments")
	log.WithField("query", req.Query).Info("Searching documents")

	// Validate request
	if req.TenantID == 0 {
		log.Error("Missing tenant ID")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing tenant ID",
		}
	}

	if req.Query == "" {
		log.Error("Missing search query")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Missing search query",
		}
	}

	// Resolve actual parent drive ID for search and get config
	actualParentID := req.ParentDriveID
	var config *model.GDriveConfig

	if req.ParentDriveID == "root" || req.ParentDriveID == "" {
		// Query DocumentSetting to get tenant's root shared drive ID
		log.Info("Resolving root shared drive ID for tenant")
		setting, err := s.documentSettingRepo.GetValueByKey(ctx, req.TenantID, model.KeyGdriveConfig)
		if err != nil {
			log.WithError(err).Error("Failed to get document setting for tenant")
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Failed to get Google Drive configuration for tenant",
				Details: err.Error(),
			}
		}

		config, err = setting.GetGDriveConfig()
		if err != nil {
			log.WithError(err).Error("Failed to parse Google Drive configuration")
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Invalid Google Drive configuration for tenant",
				Details: err.Error(),
			}
		}

		if config.IsEmpty() {
			log.Error("Google Drive not configured for tenant")
			return nil, &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Google Drive not configured for tenant",
			}
		}

		actualParentID = config.RootID
		log.WithField("resolved_parent_id", actualParentID).Info("Resolved root shared drive ID")
	}

	// Build proper Google Drive search query and determine drive scope
	var searchQuery string
	var driveID string

	if actualParentID != "" {
		// Check if we have a shared drive configuration
		if config != nil && config.DriveID != "" && config.DriveID == actualParentID {
			// This is a shared drive root - search entire shared drive recursively
			searchQuery = fmt.Sprintf("fullText contains '%s' and trashed=false", req.Query)
			driveID = config.DriveID // Use native API drive scoping
			log.WithField("drive_id", config.DriveID).Info("Searching within entire shared drive recursively using native API scoping")
		} else {
			// This is a specific folder - search within folder only
			searchQuery = fmt.Sprintf("fullText contains '%s' and '%s' in parents and trashed=false", req.Query, actualParentID)
			driveID = "" // No drive scoping for folder searches
			log.WithField("folder_id", actualParentID).Info("Searching within specific folder only")
		}
	} else {
		// Fallback to global search (should not happen with proper config)
		searchQuery = fmt.Sprintf("fullText contains '%s' and trashed=false", req.Query)
		driveID = ""
	}
	log.WithField("formatted_query", searchQuery).WithField("drive_id", driveID).Info("Formatted search query for Google Drive API with content search")

	// Search files in Google Drive using ListFilesWithQuery with native drive scoping
	files, err := s.driveClient.ListFilesWithQuery(searchQuery, driveID)
	if err != nil {
		log.WithError(err).Error("Failed to search files in Google Drive")
		return nil, &DocumentServiceError{
			Code:    ErrCodeDriveAPIError,
			Message: "Failed to search files in Google Drive",
			Details: err.Error(),
		}
	}

	log.WithField("results_count", len(files)).WithField("drive_id", driveID).Info("Search completed using native API drive scoping")

	// Build response - no client-side filtering needed since API handles drive scoping
	documents := make([]*DocumentResponse, 0, len(files))
	for _, file := range files {
		doc := &DocumentResponse{
			DriveFileID:  file.Id,
			Name:         file.Name,
			TenantID:     req.TenantID,
			ContentType:  file.MimeType,
			WebViewLink:  file.WebViewLink,
			DownloadLink: file.WebContentLink,
			IsFile:       file.MimeType != gdrive.MimeTypeFolder,
			Size:         file.Size,
			CreatedAt:    parseGoogleDriveTime(file.CreatedTime),
			UpdatedAt:    parseGoogleDriveTime(file.ModifiedTime),
		}
		documents = append(documents, doc)
	}

	log.WithField("count", len(documents)).Info("Documents searched successfully")
	return &DocumentListResponse{
		Documents:     documents,
		NextPageToken: "", // ListFilesWithQuery doesn't support pagination yet
		Total:         len(documents),
	}, nil
}

// ResolveHierarchicalPath resolves a hierarchical folder path by traversing/creating folders as needed
func (s *documentService) ResolveHierarchicalPath(ctx context.Context, req *ResolveHierarchicalPathRequest) (*ResolveHierarchicalPathResponse, error) {
	log := logger.WithCtx(ctx, "ResolveHierarchicalPath")
	log.Infof("Resolving hierarchical path parent_path=%s tenant_id=%d", req.ParentPath, req.TenantID)

	// Validate request
	if err := validateResolveHierarchicalPathRequest(req); err != nil {
		log.WithError(err).Error("Invalid request")
		return nil, &DocumentServiceError{
			Code:    ErrCodeInvalidRequest,
			Message: "Invalid hierarchical path request",
			Details: err.Error(),
		}
	}

	// 1. Resolve the root drive ID
	rootDriveID, err := s.resolveRootDriveID(ctx, req)
	if err != nil {
		log.WithError(err).Error("Failed to resolve root drive ID")
		return nil, err
	}

	// 2. Parse the path into segments
	pathSegments := parsePathSegments(req.ParentPath)
	log.Infof("Parsed path into %d segments: %v", len(pathSegments), pathSegments)

	// 3. Traverse/create folder hierarchy
	finalParentID, createdFolders, err := s.traverseFolderHierarchy(ctx, rootDriveID, pathSegments)
	if err != nil {
		log.WithError(err).Error("Failed to traverse folder hierarchy")
		return nil, err
	}

	log.Infof("Successfully resolved hierarchical path final_parent_id=%s created_folders=%d", finalParentID, len(createdFolders))

	return &ResolveHierarchicalPathResponse{
		FinalParentID:  finalParentID,
		CreatedFolders: createdFolders,
		ResolvedPath:   strings.Join(pathSegments, "/"),
	}, nil
}

// validateResolveHierarchicalPathRequest validates the hierarchical path request
func validateResolveHierarchicalPathRequest(req *ResolveHierarchicalPathRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	// Must have at least one way to resolve root
	hasRootDriveID := req.RootDriveID != ""
	hasObjectMapping := req.ObjectType != "" && req.ObjectID != 0

	if !hasRootDriveID && !hasObjectMapping {
		return fmt.Errorf("must provide root_drive_id or both object_type and object_id")
	}

	return nil
}

// parsePathSegments parses a hierarchical path into clean segments
func parsePathSegments(parentPath string) []string {
	// Remove leading and trailing slashes
	cleanPath := strings.Trim(parentPath, "/")
	if cleanPath == "" {
		return []string{}
	}

	// Split by slash and clean each segment
	segments := strings.Split(cleanPath, "/")
	cleanSegments := make([]string, 0, len(segments))

	for _, segment := range segments {
		segment = strings.TrimSpace(segment)
		if segment != "" {
			cleanSegments = append(cleanSegments, segment)
		}
	}

	return cleanSegments
}

// resolveRootDriveID resolves the root drive ID from the request parameters
func (s *documentService) resolveRootDriveID(ctx context.Context, req *ResolveHierarchicalPathRequest) (string, error) {
	log := logger.WithCtx(ctx, "resolveRootDriveID")

	// If root drive ID is provided directly, use it
	if req.RootDriveID != "" {
		log.Infof("Using provided root drive ID root_drive_id=%s", req.RootDriveID)
		return req.RootDriveID, nil
	}

	// Otherwise, resolve from object mapping
	if req.ObjectType != "" && req.ObjectID != 0 {
		log.Infof("Resolving root drive ID from object mapping object_type=%s object_id=%d", req.ObjectType, req.ObjectID)
		return s.getParentFolderIDFromObject(ctx, req.ObjectType, req.ObjectID, req.TenantID)
	}

	return "", &DocumentServiceError{
		Code:    ErrCodeInvalidRequest,
		Message: "No valid root resolution method provided",
	}
}

// traverseFolderHierarchy traverses the folder hierarchy, creating folders as needed
func (s *documentService) traverseFolderHierarchy(ctx context.Context, rootDriveID string, pathSegments []string) (string, []string, error) {
	log := logger.WithCtx(ctx, "traverseFolderHierarchy")
	log.Infof("Traversing folder hierarchy root_drive_id=%s segments=%v", rootDriveID, pathSegments)

	currentParentID := rootDriveID
	createdFolders := make([]string, 0)

	// Traverse each path segment
	for i, segment := range pathSegments {
		log.Infof("Processing segment %d: %s under parent %s", i+1, segment, currentParentID)

		// Search for existing folder with this name under current parent
		existingFolder, err := s.findFolderByName(ctx, segment, currentParentID)
		if err != nil {
			log.WithError(err).Errorf("Failed to search for existing folder segment=%s parent=%s", segment, currentParentID)
			return "", nil, &DocumentServiceError{
				Code:    ErrCodeDriveAPIError,
				Message: fmt.Sprintf("Failed to search for folder '%s'", segment),
				Details: err.Error(),
			}
		}

		if existingFolder != nil {
			// Folder exists, use its ID as the new parent
			log.Infof("Found existing folder segment=%s folder_id=%s", segment, existingFolder.Id)
			currentParentID = existingFolder.Id
		} else {
			// Folder doesn't exist, create it
			log.Infof("Creating new folder segment=%s under parent=%s", segment, currentParentID)
			newFolder, err := s.driveClient.CreateFolder(segment, currentParentID)
			if err != nil {
				log.WithError(err).Errorf("Failed to create folder segment=%s parent=%s", segment, currentParentID)
				return "", nil, &DocumentServiceError{
					Code:    ErrCodeDriveAPIError,
					Message: fmt.Sprintf("Failed to create folder '%s'", segment),
					Details: err.Error(),
				}
			}

			log.Infof("Created new folder segment=%s folder_id=%s", segment, newFolder.Id)
			currentParentID = newFolder.Id
			createdFolders = append(createdFolders, newFolder.Id)
		}
	}

	log.Infof("Completed folder hierarchy traversal final_parent_id=%s created_count=%d", currentParentID, len(createdFolders))
	return currentParentID, createdFolders, nil
}

// findFolderByName searches for a folder by name under a specific parent
func (s *documentService) findFolderByName(ctx context.Context, folderName, parentID string) (*drive.File, error) {
	log := logger.WithCtx(ctx, "findFolderByName")
	log.Infof("Searching for folder name=%s under parent=%s", folderName, parentID)

	// Escape single quotes in folder name for the query
	escapedFolderName := strings.ReplaceAll(folderName, "'", "\\'")

	// Build search query for folders only
	query := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false and '%s' in parents",
		escapedFolderName, parentID)

	// Search using Google Drive API
	files, err := s.driveClient.ListFilesWithQuery(query, "")
	if err != nil {
		return nil, fmt.Errorf("failed to search for folder: %w", err)
	}

	if len(files) == 0 {
		log.Infof("Folder not found name=%s parent=%s", folderName, parentID)
		return nil, nil // Not found, but not an error
	}

	// Return the first match (there should only be one with exact name match)
	log.Infof("Found existing folder name=%s folder_id=%s", folderName, files[0].Id)
	return files[0], nil
}

// GetTenantConfig retrieves Google Drive configuration for a tenant
func (s *documentService) GetTenantConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	log := logger.WithCtx(ctx, "GetTenantConfig").WithField("tenant_id", tenantID)

	setting, err := s.documentSettingRepo.GetValueByKey(ctx, tenantID, model.KeyGdriveConfig)
	if err != nil {
		log.WithError(err).Error("Failed to get Google Drive configuration from database")
		return nil, fmt.Errorf("failed to get Google Drive configuration: %w", err)
	}

	config, err := setting.GetGDriveConfig()
	if err != nil {
		log.WithError(err).Error("Failed to parse Google Drive configuration")
		return nil, fmt.Errorf("failed to parse Google Drive configuration: %w", err)
	}

	if config.IsEmpty() {
		log.Error("Google Drive not configured for tenant")
		return nil, fmt.Errorf("google Drive not configured for tenant %d", tenantID)
	}

	log.WithField("root_id", config.RootID).Info("Retrieved Google Drive configuration")
	return config, nil
}
