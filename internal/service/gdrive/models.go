package gdrive

import (
	"bilabl/docman/domain/model"
	"fmt"
	"time"
)

// Request Models for Folder Creation

// CreateClientFolderRequest contains parameters for creating a client folder
type CreateClientFolderRequest struct {
	TenantID   uint64              `json:"tenant_id" validate:"required"`
	ClientID   uint64              `json:"client_id" validate:"required"`
	ClientName string              `json:"client_name" validate:"required"`
	ClientCode string              `json:"client_code" validate:"required"`
	ShortName  string              `json:"short_name,omitempty"` // Optional short name for client
	Config     *model.GDriveConfig `json:"-"`                    // Pass config from handler instead of loading inside service
}

// CreateMatterParentFolderRequest contains parameters for creating a matter parent folder
type CreateMatterParentFolderRequest struct {
	TenantID uint64
	ClientID uint64
	Config   *model.GDriveConfig // Pass config from handler instead of loading inside service
}

// CreateMatterFolderRequest contains parameters for creating a matter folder
type CreateMatterFolderRequest struct {
	TenantID   uint64
	ClientID   uint64
	MatterID   uint64
	MatterName string
	MatterCode string
	Config     *model.GDriveConfig // Pass config from handler instead of loading inside service
}

// RenameMatterFolderRequest contains parameters for renaming a matter folder
type RenameMatterFolderRequest struct {
	TenantID   uint64
	MatterID   uint64
	ClientID   uint64
	MatterName string
	MatterCode string
	Config     *model.GDriveConfig // Pass config from handler instead of loading inside service
}

// RenameMatterFolderResponse contains the result of a matter folder rename operation
type RenameMatterFolderResponse struct {
	FolderID    string
	OldName     string
	NewName     string
	WebViewLink string
}

// Response Models for Folder Creation

// CreateClientFolderResponse contains the result of client folder creation
type CreateClientFolderResponse struct {
	FolderID        string                 `json:"folder_id"`
	FolderName      string                 `json:"folder_name"`
	WebViewLink     string                 `json:"web_view_link"`
	DocumentMapping *model.DocumentMapping `json:"document_mapping"`
}

// CreateMatterParentFolderResponse contains the result of matter parent folder creation
type CreateMatterParentFolderResponse struct {
	FolderID        string                 `json:"folder_id"`
	FolderName      string                 `json:"folder_name"`
	WebViewLink     string                 `json:"web_view_link"`
	DocumentMapping *model.DocumentMapping `json:"document_mapping"`
}

// CreateMatterFolderResponse contains the result of matter folder creation
type CreateMatterFolderResponse struct {
	FolderID        string                 `json:"folder_id"`
	FolderName      string                 `json:"folder_name"`
	WebViewLink     string                 `json:"web_view_link"`
	DocumentMapping *model.DocumentMapping `json:"document_mapping"`
}

// ClientData contains client information for folder naming
type ClientData struct {
	Name      string
	ShortName string
	Code      string
	ID        uint64
}

// MatterData contains matter information for folder naming
type MatterData struct {
	Name     string
	Code     string
	ID       uint64
	ClientID uint64
}

// FolderCreationError represents errors that can occur during folder creation
type FolderCreationError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error implements the error interface
func (e *FolderCreationError) Error() string {
	return e.Message
}

// Error codes for folder creation
const (
	ErrCodeConfigMissing    = "CONFIG_MISSING"
	ErrCodePermissionDenied = "PERMISSION_DENIED"
	ErrCodeFolderExists     = "FOLDER_EXISTS"
	ErrCodeAPIError         = "API_ERROR"
	ErrCodeInvalidPath      = "INVALID_PATH"
	ErrCodeTemplateError    = "TEMPLATE_ERROR"
	ErrCodeNotFound         = "NOT_FOUND"
)

// Document Service Models

// CreateDocumentRequest contains parameters for creating a document
type CreateDocumentRequest struct {
	TenantID    uint64 `json:"tenant_id" binding:"required"` // Tenant ID
	Name        string `json:"name" binding:"required"`      // Document name
	FolderPath  string `json:"folder_path,omitempty"`        // Optional path within the drive
	ParentID    string `json:"parent_id,omitempty"`          // Google Drive parent folder ID
	DocType     int    `json:"doc_type,omitempty"`           // Document type (1=folder, 2=file)
	ObjectType  string `json:"object_type,omitempty"`        // Object type (client, matter, etc.)
	ObjectID    uint64 `json:"object_id,omitempty"`          // Object ID (client_id, matter_id)
	Status      string `json:"status,omitempty"`             // Document status
	SubObjectID uint64 `json:"sub_object_id,omitempty"`      // Sub object ID
	Type        string `json:"type,omitempty"`               // Document type string
}

// UpdateDocumentRequest contains parameters for updating a document
type UpdateDocumentRequest struct {
	Name string `json:"name,omitempty"` // New name for the document
	Size int64  `json:"size,omitempty"` // New size for the document
}

// DocumentResponse contains the result of document operations
type DocumentResponse struct {
	ID            uint64    `json:"id,omitempty"`            // Internal database ID
	DriveFileID   string    `json:"drive_file_id"`           // Google Drive file ID
	Name          string    `json:"name"`                    // File or folder name
	TenantID      uint64    `json:"tenant_id"`               // Tenant ID
	ParentID      uint64    `json:"parent_id,omitempty"`     // Parent document ID (internal)
	ParentDriveID string    `json:"parent_drive_id"`         // Parent folder ID in Google Drive
	Size          int64     `json:"size,omitempty"`          // File size in bytes
	ContentType   string    `json:"content_type"`            // MIME type of the file
	WebViewLink   string    `json:"web_view_link"`           // Google Drive web view URL
	DownloadLink  string    `json:"download_link,omitempty"` // Direct download URL
	IsFile        bool      `json:"is_file"`                 // True if it's a file, false if it's a folder
	CreatedAt     time.Time `json:"created_at,omitempty"`    // Creation timestamp
	UpdatedAt     time.Time `json:"updated_at,omitempty"`    // Last update timestamp
	CreatedBy     uint64    `json:"created_by,omitempty"`    // User ID who created the document
	UpdatedBy     uint64    `json:"updated_by,omitempty"`    // User ID who last updated the document
	ObjectType    string    `json:"object_type,omitempty"`   // Object type (client, matter, etc.)
	ObjectID      uint64    `json:"object_id,omitempty"`     // Object ID (client_id, matter_id)
	DocType       int       `json:"doc_type,omitempty"`      // Document type (1=folder, 2=file)
	Status        string    `json:"status,omitempty"`        // Document status
	SubObjectID   uint64    `json:"sub_object_id,omitempty"` // Sub object ID
	Type          string    `json:"type,omitempty"`          // Document type string
}

// UploadDocumentRequest contains parameters for generating upload URL (SharePoint-compatible)
type UploadDocumentRequest struct {
	TenantID      uint64 `json:"tenant_id" binding:"required"`       // Tenant ID
	FileName      string `json:"file_name" binding:"required"`       // File name
	ParentDriveID string `json:"parent_drive_id" binding:"required"` // Parent folder ID in Google Drive
	// REMOVED: Content, ContentType, Size - no server-side file handling (matches SharePoint pattern)
}

// UploadDocumentResponse contains the result of upload URL generation
type UploadDocumentResponse struct {
	UploadURL   string `json:"upload_url"`    // Generated upload URL for client-side upload
	DriveFileID string `json:"drive_file_id"` // Placeholder file ID from Google Drive
	FileName    string `json:"file_name"`     // Original file name
}

// CreateUploadSessionRequest contains parameters for creating upload session
type CreateUploadSessionRequest struct {
	TenantID      uint64 `json:"tenant_id" binding:"required"`       // Tenant ID
	FileName      string `json:"file_name" binding:"required"`       // File name
	ParentDriveID string `json:"parent_drive_id" binding:"required"` // Parent folder ID in Google Drive
}

// UploadSessionResponse contains upload session information
type UploadSessionResponse struct {
	SessionToken    string    `json:"session_token"` // Unique session token
	UploadURL       string    `json:"upload_url"`    // Upload URL (same as session token for compatibility)
	FileName        string    `json:"file_name"`     // File name
	Status          string    `json:"status"`        // Session status
	ExpiresAt       time.Time `json:"expires_at"`    // Session expiration time
	GoogleUploadURL string    `json:"-"`             // Internal Google Drive upload URL
}

// UploadContentResponse contains the result of file content upload
type UploadContentResponse struct {
	Status       string `json:"status"`        // Upload status (completed, failed)
	DriveFileID  string `json:"drive_file_id"` // Google Drive file ID
	FileName     string `json:"file_name"`     // File name
	FileSize     int64  `json:"file_size"`     // File size in bytes
	ContentType  string `json:"content_type"`  // MIME type
	SessionToken string `json:"session_token"` // Session token used
}

// ResolveHierarchicalPathRequest contains parameters for resolving hierarchical folder paths
type ResolveHierarchicalPathRequest struct {
	TenantID   uint64 `json:"tenant_id" validate:"required"`   // Tenant ID
	ParentPath string `json:"parent_path" validate:"required"` // Hierarchical folder path (e.g., "parent/child1/child2")
	// Root resolution parameters (one of these must be provided)
	RootDriveID string `json:"root_drive_id,omitempty"` // Direct root drive ID
	ObjectType  string `json:"object_type,omitempty"`   // Object type for mapping lookup
	ObjectID    uint64 `json:"object_id,omitempty"`     // Object ID for mapping lookup
}

// ResolveHierarchicalPathResponse contains the result of hierarchical path resolution
type ResolveHierarchicalPathResponse struct {
	FinalParentID  string   `json:"final_parent_id"` // Final parent folder ID for creation/upload
	CreatedFolders []string `json:"created_folders"` // List of folder IDs that were created during traversal
	ResolvedPath   string   `json:"resolved_path"`   // The actual path that was resolved
}

// SearchDocumentsRequest contains parameters for searching documents
type SearchDocumentsRequest struct {
	TenantID      uint64 `json:"tenant_id" binding:"required"` // Tenant ID
	ParentDriveID string `json:"parent_drive_id"`              // Parent folder ID to search within
	Query         string `json:"query" binding:"required"`     // Search query
	PageToken     string `json:"page_token,omitempty"`         // Token for pagination
	PageSize      int    `json:"page_size,omitempty"`          // Number of items per page
}

// ListDocumentsRequest contains parameters for listing documents
type ListDocumentsRequest struct {
	TenantID      uint64 `json:"tenant_id" binding:"required"` // Tenant ID
	ParentDriveID string `json:"parent_drive_id"`              // Parent folder ID in Google Drive
	PageToken     string `json:"page_token,omitempty"`         // Token for pagination
	PageSize      int    `json:"page_size,omitempty"`          // Number of items per page
}

// DocumentListResponse contains the result of document list operations
type DocumentListResponse struct {
	Documents     []*DocumentResponse `json:"documents"`       // List of documents
	NextPageToken string              `json:"next_page_token"` // Token for getting the next page
	Total         int                 `json:"total"`           // Total number of documents returned
}

// DocumentServiceError represents errors that can occur during document operations
type DocumentServiceError struct {
	Code    string `json:"code"`              // Error code
	Message string `json:"message"`           // Error message
	Details string `json:"details,omitempty"` // Optional error details
}

// Error implements the error interface
func (e *DocumentServiceError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Error codes for document operations
const (
	ErrCodeInvalidRequest   = "INVALID_REQUEST"
	ErrCodeDriveAPIError    = "DRIVE_API_ERROR"
	ErrCodeDatabaseError    = "DATABASE_ERROR"
	ErrCodeDocumentNotFound = "DOCUMENT_NOT_FOUND"
	ErrCodeInternalError    = "INTERNAL_ERROR"
)
