package gdrive_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/mocks/repositories"
	mockGdrive "bilabl/docman/mocks/service/gdrive"
)

func TestClientEventConsumer_HandleClientCreated(t *testing.T) {
	tests := []struct {
		name          string
		payloadJSON   string
		setupMocks    func(*mockGdrive.MockFolderService, *repositories.MockDocumentSettingRepository)
		expectedError bool
	}{
		{
			name: "Success_CreateNewClientFolder",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder creation
				expectedReq := &gdrive.CreateClientFolderRequest{
					TenantID:   1,
					ClientID:   123,
					ClientName: "Test Client",
					ClientCode: "TC001",
					ShortName:  "",
					Config: &model.GDriveConfig{
						Enabled: true,
						RootID:  "root123",
					},
				}

				createResp := &gdrive.CreateClientFolderResponse{
					FolderID:    "folder123",
					FolderName:  "Test Client - TC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}

				folderSvc.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == expectedReq.TenantID &&
						req.ClientID == expectedReq.ClientID &&
						req.ClientName == expectedReq.ClientName &&
						req.ClientCode == expectedReq.ClientCode &&
						req.Config != nil &&
						req.Config.Enabled == true &&
						req.Config.RootID == "root123"
				})).Return(createResp, nil)
			},
			expectedError: false,
		},
		{
			name: "Success_GoogleDriveDisabled",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config - disabled
				setting := &model.DocumentSetting{
					Value: `{"enabled": false, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// No folder service calls should be made when Google Drive is disabled
			},
			expectedError: false,
		},
		{
			name: "Error_InvalidJSON",
			payloadJSON: `{
				"topic": "client.create",
				"body": invalid json
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// No mocks needed for JSON parsing error
			},
			expectedError: true,
		},
		{
			name: "Error_MissingBody",
			payloadJSON: `{
				"topic": "client.create",
				"body": null
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// No mocks needed for validation error
			},
			expectedError: true,
		},
		{
			name: "Error_ConfigMissing",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock config retrieval error
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(nil, errors.New("config not found"))
			},
			expectedError: true,
		},
		{
			name: "Error_FolderCreationFailed",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder creation failure - expect config to be passed
				folderSvc.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == 1 &&
						req.ClientID == 123 &&
						req.ClientName == "Test Client" &&
						req.ClientCode == "TC001" &&
						req.Config != nil &&
						req.Config.Enabled == true &&
						req.Config.RootID == "root123"
				})).Return(nil, errors.New("Google Drive API error"))
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			folderSvc := mockGdrive.NewMockFolderService(t)
			settingRepo := repositories.NewMockDocumentSettingRepository(t)

			// Setup mocks
			tt.setupMocks(folderSvc, settingRepo)

			// Create consumer
			consumer := gdrive.NewClientEventConsumer(folderSvc, settingRepo, nil)

			// Execute test
			err := consumer.HandleClientCreated(context.Background(), tt.payloadJSON)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			folderSvc.AssertExpectations(t)
			settingRepo.AssertExpectations(t)
		})
	}
}

func TestClientEventConsumer_HandleClientUpdated(t *testing.T) {
	tests := []struct {
		name          string
		payloadJSON   string
		setupMocks    func(*mockGdrive.MockFolderService, *repositories.MockDocumentSettingRepository)
		expectedError bool
	}{
		{
			name: "Success_GoogleDriveEnabled_FolderRenamed",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename operation
				expectedReq := &gdrive.RenameClientFolderRequest{
					TenantID:   1,
					ClientID:   123,
					ClientName: "Updated Client",
					ClientCode: "UC001",
					ShortName:  "",
					Config: &model.GDriveConfig{
						Enabled: true,
						RootID:  "root123",
					},
				}

				renameResp := &gdrive.RenameClientFolderResponse{
					FolderID:    "folder123",
					OldName:     "Old Client - UC001",
					NewName:     "Updated Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}

				folderSvc.On("RenameClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.RenameClientFolderRequest) bool {
					return req.TenantID == expectedReq.TenantID &&
						req.ClientID == expectedReq.ClientID &&
						req.ClientName == expectedReq.ClientName &&
						req.ClientCode == expectedReq.ClientCode &&
						req.Config != nil &&
						req.Config.Enabled == true &&
						req.Config.RootID == "root123"
				})).Return(renameResp, nil)
			},
			expectedError: false,
		},
		{
			name: "Success_GoogleDriveDisabled",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config - disabled
				setting := &model.DocumentSetting{
					Value: `{"enabled": false, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// No folder service calls should be made when Google Drive is disabled
			},
			expectedError: false,
		},
		{
			name: "Error_InvalidJSON",
			payloadJSON: `{
				"topic": "client.update",
				"body": invalid json
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// No mocks needed for JSON parsing error
			},
			expectedError: true,
		},
		{
			name: "Error_RenameFailure",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename failure
				folderSvc.On("RenameClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.RenameClientFolderRequest) bool {
					return req.TenantID == 1 &&
						req.ClientID == 123 &&
						req.ClientName == "Updated Client" &&
						req.ClientCode == "UC001" &&
						req.Config != nil &&
						req.Config.Enabled == true &&
						req.Config.RootID == "root123"
				})).
					Return(nil, errors.New("rename failed"))
			},
			expectedError: true,
		},
		{
			name: "Success_FolderCreatedThenRenamed",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 456,
					"name": "New Client",
					"code": "NC001",
					"tenant_id": 2
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root456"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(2), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename operation that creates folder first
				expectedReq := &gdrive.RenameClientFolderRequest{
					TenantID:   2,
					ClientID:   456,
					ClientName: "New Client",
					ClientCode: "NC001",
					ShortName:  "",
					Config: &model.GDriveConfig{
						Enabled: true,
						RootID:  "root456",
					},
				}

				renameResp := &gdrive.RenameClientFolderResponse{
					FolderID:    "folder456",
					OldName:     "New Client - NC001", // Same name indicates folder was just created
					NewName:     "New Client - NC001",
					WebViewLink: "https://drive.google.com/folder/folder456",
				}

				folderSvc.On("RenameClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.RenameClientFolderRequest) bool {
					return req.TenantID == expectedReq.TenantID &&
						req.ClientID == expectedReq.ClientID &&
						req.ClientName == expectedReq.ClientName &&
						req.ClientCode == expectedReq.ClientCode &&
						req.Config != nil &&
						req.Config.Enabled == true &&
						req.Config.RootID == "root456"
				})).Return(renameResp, nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			folderSvc := mockGdrive.NewMockFolderService(t)
			settingRepo := repositories.NewMockDocumentSettingRepository(t)

			// Setup mocks
			tt.setupMocks(folderSvc, settingRepo)

			// Create consumer
			consumer := gdrive.NewClientEventConsumer(folderSvc, settingRepo, nil)

			// Execute test
			err := consumer.HandleClientUpdated(context.Background(), tt.payloadJSON)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			folderSvc.AssertExpectations(t)
			settingRepo.AssertExpectations(t)
		})
	}
}

// getTestDocumentMapping creates a test document mapping for testing
func getTestDocumentMapping(docType string, objectID uint64) *model.DocumentMapping {
	return &model.DocumentMapping{
		TenantID:       1,
		Type:           docType,
		ObjectID:       objectID,
		ParentObjectID: 0,
		DriveID:        "folder123",
		ParentDriveID:  "root123",
		Provider:       model.DocProviderGoogle,
	}
}

func TestClientEventConsumer_HandleClientCreated_WithPermissionSync(t *testing.T) {
	tests := []struct {
		name          string
		payloadJSON   string
		setupMocks    func(*mockGdrive.MockFolderService, *repositories.MockDocumentSettingRepository, *MockPermissionSyncer)
		expectedError bool
	}{
		{
			name: "Success_WithPermissionSync",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1,
					"owner_users": [
						{"email": "<EMAIL>"},
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder creation
				createResp := &gdrive.CreateClientFolderResponse{
					FolderID:    "folder123",
					FolderName:  "Test Client - TC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("CreateClientFolder", mock.Anything, mock.Anything).Return(createResp, nil)

				// Mock permission sync
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"client",
					uint64(123),
					[]string{"<EMAIL>", "<EMAIL>"}).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "Success_WithPermissionSyncFailure_DoesNotFailOperation",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1,
					"owner_users": [
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder creation
				createResp := &gdrive.CreateClientFolderResponse{
					FolderID:    "folder123",
					FolderName:  "Test Client - TC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("CreateClientFolder", mock.Anything, mock.Anything).Return(createResp, nil)

				// Mock permission sync failure
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"client",
					uint64(123),
					[]string{"<EMAIL>"}).Return(errors.New("permission sync failed"))
			},
			expectedError: false, // Should not fail the entire operation
		},
		{
			name: "Success_WithEmptyOwnerEmails_SkipsPermissionSync",
			payloadJSON: `{
				"topic": "client.create",
				"body": {
					"id": 123,
					"name": "Test Client",
					"code": "TC001",
					"tenant_id": 1,
					"owner_users": [
						{"email": ""},
						{"email": "   "}
					]
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder creation
				createResp := &gdrive.CreateClientFolderResponse{
					FolderID:    "folder123",
					FolderName:  "Test Client - TC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("CreateClientFolder", mock.Anything, mock.Anything).Return(createResp, nil)

				// No permission sync calls should be made since emails are empty/whitespace
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			folderSvc := mockGdrive.NewMockFolderService(t)
			settingRepo := repositories.NewMockDocumentSettingRepository(t)
			permSyncer := &MockPermissionSyncer{}

			// Setup mocks
			tt.setupMocks(folderSvc, settingRepo, permSyncer)

			// Create consumer with permission syncer
			consumer := gdrive.NewClientEventConsumer(folderSvc, settingRepo, permSyncer)

			// Execute test
			err := consumer.HandleClientCreated(context.Background(), tt.payloadJSON)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			folderSvc.AssertExpectations(t)
			settingRepo.AssertExpectations(t)
			permSyncer.AssertExpectations(t)
		})
	}
}

func TestClientEventConsumer_HandleClientUpdated_WithPermissionSync(t *testing.T) {
	tests := []struct {
		name          string
		payloadJSON   string
		setupMocks    func(*mockGdrive.MockFolderService, *repositories.MockDocumentSettingRepository, *MockPermissionSyncer)
		expectedError bool
	}{
		{
			name: "Success_WithPermissionSync",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1,
					"owner_users": [
						{"email": "<EMAIL>"},
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename operation
				renameResp := &gdrive.RenameClientFolderResponse{
					FolderID:    "folder123",
					OldName:     "Old Client - UC001",
					NewName:     "Updated Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("RenameClientFolder", mock.Anything, mock.Anything).Return(renameResp, nil)

				// Mock permission sync
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"client",
					uint64(123),
					[]string{"<EMAIL>", "<EMAIL>"}).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "Success_WithPermissionSyncFailure_DoesNotFailOperation",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1,
					"owner_users": [
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename operation
				renameResp := &gdrive.RenameClientFolderResponse{
					FolderID:    "folder123",
					OldName:     "Old Client - UC001",
					NewName:     "Updated Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("RenameClientFolder", mock.Anything, mock.Anything).Return(renameResp, nil)

				// Mock permission sync failure
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"client",
					uint64(123),
					[]string{"<EMAIL>"}).Return(errors.New("permission sync failed"))
			},
			expectedError: false, // Should not fail the entire operation
		},
		{
			name: "Success_WithEmptyOwnerEmails_RemovesAllPermissions",
			payloadJSON: `{
				"topic": "client.update",
				"body": {
					"id": 123,
					"name": "Updated Client",
					"code": "UC001",
					"tenant_id": 1,
					"owner_users": []
				}
			}`,
			setupMocks: func(folderSvc *mockGdrive.MockFolderService, settingRepo *repositories.MockDocumentSettingRepository, permSyncer *MockPermissionSyncer) {
				// Mock Google Drive config retrieval
				setting := &model.DocumentSetting{
					Value: `{"enabled": true, "root_id": "root123"}`,
				}
				settingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).Return(setting, nil)

				// Mock folder rename operation
				renameResp := &gdrive.RenameClientFolderResponse{
					FolderID:    "folder123",
					OldName:     "Old Client - UC001",
					NewName:     "Updated Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}
				folderSvc.On("RenameClientFolder", mock.Anything, mock.Anything).Return(renameResp, nil)

				// Mock permission sync with empty emails (should remove all permissions)
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"client",
					uint64(123),
					[]string{}).Return(nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			folderSvc := mockGdrive.NewMockFolderService(t)
			settingRepo := repositories.NewMockDocumentSettingRepository(t)
			permSyncer := &MockPermissionSyncer{}

			// Setup mocks
			tt.setupMocks(folderSvc, settingRepo, permSyncer)

			// Create consumer with permission syncer
			consumer := gdrive.NewClientEventConsumer(folderSvc, settingRepo, permSyncer)

			// Execute test
			err := consumer.HandleClientUpdated(context.Background(), tt.payloadJSON)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			folderSvc.AssertExpectations(t)
			settingRepo.AssertExpectations(t)
			permSyncer.AssertExpectations(t)
		})
	}
}
