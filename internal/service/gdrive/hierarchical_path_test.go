package gdrive

import (
	"context"
	"testing"

	"bilabl/docman/mocks/gdrive"
	"bilabl/docman/mocks/repositories"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/drive/v3"
)

func TestParsePathSegments(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected []string
	}{
		{
			name:     "Simple path",
			path:     "parent",
			expected: []string{"parent"},
		},
		{
			name:     "Nested path",
			path:     "parent/child1/child2",
			expected: []string{"parent", "child1", "child2"},
		},
		{
			name:     "Path with leading slash",
			path:     "/parent/child1",
			expected: []string{"parent", "child1"},
		},
		{
			name:     "Path with trailing slash",
			path:     "parent/child1/",
			expected: []string{"parent", "child1"},
		},
		{
			name:     "Path with both leading and trailing slashes",
			path:     "/parent/child1/",
			expected: []string{"parent", "child1"},
		},
		{
			name:     "Path with extra spaces",
			path:     " parent / child1 / child2 ",
			expected: []string{"parent", "child1", "child2"},
		},
		{
			name:     "Empty path",
			path:     "",
			expected: []string{},
		},
		{
			name:     "Only slashes",
			path:     "///",
			expected: []string{},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := parsePathSegments(tc.path)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestValidateResolveHierarchicalPathRequest(t *testing.T) {
	t.Run("Valid_with_root_drive_ID", func(t *testing.T) {
		req := &ResolveHierarchicalPathRequest{
			TenantID:    1,
			ParentPath:  "folderA/folderB",
			RootDriveID: "root123",
		}
		err := validateResolveHierarchicalPathRequest(req)
		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
	})

	t.Run("Valid_with_object_mapping", func(t *testing.T) {
		req := &ResolveHierarchicalPathRequest{
			TenantID:   1,
			ParentPath: "folderA/folderB",
			ObjectType: "client",
			ObjectID:   123,
		}
		err := validateResolveHierarchicalPathRequest(req)
		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
	})

	t.Run("Invalid_-_nil_request", func(t *testing.T) {
		err := validateResolveHierarchicalPathRequest(nil)
		if err == nil {
			t.Errorf("Expected error for nil request, got nil")
		}
	})

	t.Run("Invalid_-_missing_tenant_ID", func(t *testing.T) {
		req := &ResolveHierarchicalPathRequest{
			ParentPath:  "folderA/folderB",
			RootDriveID: "root123",
		}
		err := validateResolveHierarchicalPathRequest(req)
		if err == nil {
			t.Errorf("Expected error for missing tenant_id, got nil")
		}
	})

	// Removed: Invalid_-_missing_parent_path, since missing parent_path is now allowed by logic
}

func TestResolveHierarchicalPath_Success(t *testing.T) {
	// Setup mocks
	mockDocRepo := repositories.NewMockDocumentRepository(t)
	mockMappingRepo := repositories.NewMockDocumentMappingRepository(t)
	mockUploadRepo := repositories.NewMockUploadSessionRepository(t)
	mockSettingRepo := repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := gdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo:        mockDocRepo,
		mappingRepo:         mockMappingRepo,
		uploadSessionRepo:   mockUploadRepo,
		documentSettingRepo: mockSettingRepo,
		driveClient:         mockDriveClient,
	}

	ctx := context.Background()
	req := &ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "parent/child1/child2",
		RootDriveID: "root123",
	}

	// Mock expectations for folder traversal
	// 1. Search for "parent" under root123 - not found
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='parent' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root123' in parents", "").
		Return([]*drive.File{}, nil).
		Once()

	// 2. Create "parent" folder
	parentFolder := &drive.File{Id: "parent123", Name: "parent"}
	mockDriveClient.EXPECT().
		CreateFolder("parent", "root123").
		Return(parentFolder, nil).
		Once()

	// 3. Search for "child1" under parent123 - found existing
	existingChild1 := &drive.File{Id: "child1_123", Name: "child1"}
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='child1' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'parent123' in parents", "").
		Return([]*drive.File{existingChild1}, nil).
		Once()

	// 4. Search for "child2" under child1_123 - not found
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='child2' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'child1_123' in parents", "").
		Return([]*drive.File{}, nil).
		Once()

	// 5. Create "child2" folder
	child2Folder := &drive.File{Id: "child2_123", Name: "child2"}
	mockDriveClient.EXPECT().
		CreateFolder("child2", "child1_123").
		Return(child2Folder, nil).
		Once()

	// Execute
	result, err := service.ResolveHierarchicalPath(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "child2_123", result.FinalParentID)
	assert.Equal(t, []string{"parent123", "child2_123"}, result.CreatedFolders)
	assert.Equal(t, "parent/child1/child2", result.ResolvedPath)

	mockDriveClient.AssertExpectations(t)
}

func TestResolveHierarchicalPath_AllFoldersExist(t *testing.T) {
	// Setup mocks
	mockDocRepo := repositories.NewMockDocumentRepository(t)
	mockMappingRepo := repositories.NewMockDocumentMappingRepository(t)
	mockUploadRepo := repositories.NewMockUploadSessionRepository(t)
	mockSettingRepo := repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := gdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo:        mockDocRepo,
		mappingRepo:         mockMappingRepo,
		uploadSessionRepo:   mockUploadRepo,
		documentSettingRepo: mockSettingRepo,
		driveClient:         mockDriveClient,
	}

	ctx := context.Background()
	req := &ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "parent/child1",
		RootDriveID: "root123",
	}

	// Mock expectations - all folders exist
	parentFolder := &drive.File{Id: "parent123", Name: "parent"}
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='parent' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root123' in parents", "").
		Return([]*drive.File{parentFolder}, nil).
		Once()

	child1Folder := &drive.File{Id: "child1_123", Name: "child1"}
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='child1' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'parent123' in parents", "").
		Return([]*drive.File{child1Folder}, nil).
		Once()

	// Execute
	result, err := service.ResolveHierarchicalPath(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "child1_123", result.FinalParentID)
	assert.Empty(t, result.CreatedFolders) // No folders were created
	assert.Equal(t, "parent/child1", result.ResolvedPath)

	mockDriveClient.AssertExpectations(t)
}

func TestResolveHierarchicalPath_CreateFolderError(t *testing.T) {
	// Setup mocks
	mockDocRepo := repositories.NewMockDocumentRepository(t)
	mockMappingRepo := repositories.NewMockDocumentMappingRepository(t)
	mockUploadRepo := repositories.NewMockUploadSessionRepository(t)
	mockSettingRepo := repositories.NewMockDocumentSettingRepository(t)
	mockDriveClient := gdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo:        mockDocRepo,
		mappingRepo:         mockMappingRepo,
		uploadSessionRepo:   mockUploadRepo,
		documentSettingRepo: mockSettingRepo,
		driveClient:         mockDriveClient,
	}

	ctx := context.Background()
	req := &ResolveHierarchicalPathRequest{
		TenantID:    1,
		ParentPath:  "parent",
		RootDriveID: "root123",
	}

	// Mock expectations - folder not found, creation fails
	mockDriveClient.EXPECT().
		ListFilesWithQuery("name='parent' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root123' in parents", "").
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("parent", "root123").
		Return(nil, assert.AnError).
		Once()

	// Execute
	result, err := service.ResolveHierarchicalPath(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to create folder 'parent'")

	mockDriveClient.AssertExpectations(t)
}
