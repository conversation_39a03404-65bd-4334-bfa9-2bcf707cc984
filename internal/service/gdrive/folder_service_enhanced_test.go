package gdrive

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
	"gorm.io/gorm"

	"bilabl/docman/domain/model"
	mockGdrive "bilabl/docman/mocks/gdrive"
	mockRepository "bilabl/docman/mocks/repositories"
)

func TestFolderService_RenameClientFolder_Enhanced(t *testing.T) {
	tests := []struct {
		name          string
		request       *RenameClientFolderRequest
		setupMocks    func(*mockGdrive.MockDriveClient, *mockRepository.MockDocumentMappingRepository, *mockRepository.MockDocumentSettingRepository)
		expectedError bool
		errorCode     string
	}{
		{
			name: "Success_FolderExists_Renamed",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   123,
				ClientName: "Updated Client",
				ClientCode: "UC001",
				ShortName:  "",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// Mock existing folder mapping
				existingMapping := &model.DocumentMapping{
					TenantID:       1,
					Type:           model.DocTypeClient,
					ObjectID:       123,
					ParentObjectID: 0,
					DriveID:        "folder123",
					ParentDriveID:  "root123",
					Provider:       model.DocProviderGoogle,
				}
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(existingMapping, nil)

				// Mock current folder info
				driveClient.On("GetFileInfo", "folder123").Return(&drive.File{
					Id:          "folder123",
					Name:        "Old Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}, nil)

				// Mock folder rename
				driveClient.On("RenameFile", "folder123", "Updated Client - UC001").Return(&drive.File{
					Id:          "folder123",
					Name:        "Updated Client - UC001",
					WebViewLink: "https://drive.google.com/folder/folder123",
				}, nil)
			},
			expectedError: false,
		},
		{
			name: "Success_FolderNotExists_CreatedThenRenamed",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   456,
				ClientName: "New Client",
				ClientCode: "NC001",
				ShortName:  "",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// Mock folder mapping not found (first call in RenameClientFolder)
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(456), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound).Once()

				// Mock folder mapping not found (second call in CreateClientFolder)
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(456), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound).Once()

				// Mock ListFilesWithQuery for checking existing folder
				driveClient.On("ListFilesWithQuery", mock.MatchedBy(func(query string) bool {
					return query == "name='New Client - NC001' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root123' in parents"
				}), "").Return([]*drive.File{}, nil)

				// Mock folder creation in Google Drive
				driveClient.On("CreateFolder", "New Client - NC001", "root123").Return(&drive.File{
					Id:          "folder456",
					Name:        "New Client - NC001",
					WebViewLink: "https://drive.google.com/folder/folder456",
				}, nil)

				// Mock document mapping creation
				mappingRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentMapping) bool {
					return mapping.TenantID == 1 &&
						mapping.Type == model.DocTypeClient &&
						mapping.ObjectID == 456 &&
						mapping.DriveID == "folder456" &&
						mapping.ParentDriveID == "root123" &&
						mapping.Provider == model.DocProviderGoogle
				})).Return(nil)

				// Mock getting folder info after creation
				driveClient.On("GetFileInfo", "folder456").Return(&drive.File{
					Id:          "folder456",
					Name:        "New Client - NC001",
					WebViewLink: "https://drive.google.com/folder/folder456",
				}, nil)

				// Since the name is the same, no rename should occur
			},
			expectedError: false,
		},
		{
			name:    "Error_InvalidRequest_NilRequest",
			request: nil,
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// No mocks needed for validation error
			},
			expectedError: true,
			errorCode:     ErrCodeAPIError,
		},
		{
			name: "Error_InvalidRequest_ZeroClientID",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   0, // Invalid
				ClientName: "Test Client",
				ClientCode: "TC001",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// No mocks needed for validation error
			},
			expectedError: true,
			errorCode:     ErrCodeAPIError,
		},
		{
			name: "Error_InvalidRequest_ZeroTenantID",
			request: &RenameClientFolderRequest{
				TenantID:   0, // Invalid
				ClientID:   123,
				ClientName: "Test Client",
				ClientCode: "TC001",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// No mocks needed for validation error
			},
			expectedError: true,
			errorCode:     ErrCodeAPIError,
		},
		{
			name: "Error_InvalidRequest_NilConfig",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   123,
				ClientName: "Test Client",
				ClientCode: "TC001",
				Config:     nil, // Invalid
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// No mocks needed for validation error
			},
			expectedError: true,
			errorCode:     ErrCodeConfigMissing,
		},
		{
			name: "Error_DatabaseError_MappingCheck",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   123,
				ClientName: "Test Client",
				ClientCode: "TC001",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// Mock database error
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, errors.New("database connection failed"))
			},
			expectedError: true,
			errorCode:     ErrCodeAPIError,
		},
		{
			name: "Error_FolderCreationFailed",
			request: &RenameClientFolderRequest{
				TenantID:   1,
				ClientID:   789,
				ClientName: "Failed Client",
				ClientCode: "FC001",
				Config: &model.GDriveConfig{
					Enabled: true,
					RootID:  "root123",
				},
			},
			setupMocks: func(driveClient *mockGdrive.MockDriveClient, mappingRepo *mockRepository.MockDocumentMappingRepository, settingRepo *mockRepository.MockDocumentSettingRepository) {
				// Mock folder mapping not found (first call in RenameClientFolder)
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(789), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound).Once()

				// Mock folder mapping not found (second call in CreateClientFolder)
				mappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(789), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound).Once()

				// Mock ListFilesWithQuery for checking existing folder
				driveClient.On("ListFilesWithQuery", mock.MatchedBy(func(query string) bool {
					return query == "name='Failed Client - FC001' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root123' in parents"
				}), "").Return([]*drive.File{}, nil)

				// Mock folder creation failure
				driveClient.On("CreateFolder", "Failed Client - FC001", "root123").Return(nil, errors.New("Google Drive API error"))
			},
			expectedError: true,
			errorCode:     ErrCodeAPIError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			driveClient := mockGdrive.NewMockDriveClient(t)
			mappingRepo := mockRepository.NewMockDocumentMappingRepository(t)
			settingRepo := mockRepository.NewMockDocumentSettingRepository(t)

			// Setup mocks
			tt.setupMocks(driveClient, mappingRepo, settingRepo)

			// Create folder service
			folderService := NewFolderService(driveClient, settingRepo, mappingRepo)

			// Execute test
			resp, err := folderService.RenameClientFolder(context.Background(), tt.request)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, resp)

				if tt.errorCode != "" {
					folderErr, ok := err.(*FolderCreationError)
					assert.True(t, ok, "Expected FolderCreationError")
					assert.Equal(t, tt.errorCode, folderErr.Code)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.FolderID)
			}

			// Verify all mock expectations
			driveClient.AssertExpectations(t)
			mappingRepo.AssertExpectations(t)
			settingRepo.AssertExpectations(t)
		})
	}
}
