package gdrive

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
	"gorm.io/gorm"

	"bilabl/docman/domain/model"
	mockGdrive "bilabl/docman/mocks/gdrive"
	mockRepository "bilabl/docman/mocks/repositories"
)

// Test data constants
var (
	testTenantID = uint64(123)
	testClientID = uint64(456)
	testMatterID = uint64(789)
	testFolderID = "1DxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
	testRootID   = "root-folder-id"

	testWebLink = "https://drive.google.com/drive/folders/" + testFolderID
)

// Test data helper functions

func getTestDocumentMapping(objectType string, objectID uint64) *model.DocumentMapping {
	return &model.DocumentMapping{
		TenantID: testTenantID,
		Type:     objectType,
		ObjectID: objectID,
		DriveID:  testFolderID,
		Provider: "gdrive",
	}
}

func getTestDriveFile() *drive.File {
	return &drive.File{
		Id:          testFolderID,
		Name:        "Test Folder",
		WebViewLink: testWebLink,
		MimeType:    "application/vnd.google-apps.folder",
	}
}

// Mock setup helper
func setupMocks(t *testing.T) (*mockGdrive.MockDriveClient, *mockRepository.MockDocumentMappingRepository, *mockRepository.MockDocumentSettingRepository) {
	mockDriveClient := mockGdrive.NewMockDriveClient(t)
	mockDocMappingRepo := mockRepository.NewMockDocumentMappingRepository(t)
	mockDocSettingRepo := mockRepository.NewMockDocumentSettingRepository(t)
	return mockDriveClient, mockDocMappingRepo, mockDocSettingRepo
}

// Service setup helper
func setupFolderService(driveClient *mockGdrive.MockDriveClient, docMappingRepo *mockRepository.MockDocumentMappingRepository, docSettingRepo *mockRepository.MockDocumentSettingRepository) FolderService {
	return &folderService{
		gdriveService:  driveClient,
		docMappingRepo: docMappingRepo,
		docSettingRepo: docSettingRepo,
	}
}

// Test CreateClientFolder - Success with new folder
func TestCreateClientFolder_Success_NewFolder(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}
	driveFile := getTestDriveFile()
	driveFile.Name = "Test Client - TC001"

	req := &CreateClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Test Client",
		ClientCode: "TC001",
		Config:     config,
	}

	// Mock expectations
	// 1. Check for existing mapping - not found (6 parameters)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// 2. Check for existing folder in Google Drive - not found
	mockDriveClient.On("ListFilesWithQuery", mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil)

	// 3. Create folder in Google Drive
	mockDriveClient.On("CreateFolder", "Test Client - TC001", testRootID).
		Return(driveFile, nil)

	// 4. Create document mapping
	mockDocMappingRepo.On("CreateOrUpdate", ctx, mock.AnythingOfType("*model.DocumentMapping")).
		Return(nil)

	// Execute
	result, err := service.CreateClientFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, "Test Client - TC001", result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)

	// Verify all mocks were called
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateClientFolder - Success with existing mapping
func TestCreateClientFolder_Success_ExistingMapping(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}
	existingMapping := getTestDocumentMapping("client", testClientID)
	driveFile := getTestDriveFile()

	req := &CreateClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Test Client",
		ClientCode: "TC001",
		Config:     config,
	}

	// Mock expectations
	// 1. Check for existing mapping - found (6 parameters)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(existingMapping, nil)

	// 2. Get folder info from Google Drive
	mockDriveClient.On("GetFileInfo", testFolderID).
		Return(driveFile, nil)

	// Execute
	result, err := service.CreateClientFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, driveFile.Name, result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)
	assert.Equal(t, existingMapping, result.DocumentMapping)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateClientFolder - Error when config missing
func TestCreateClientFolder_Error_ConfigMissing(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()

	req := &CreateClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Test Client",
		ClientCode: "TC001",
		Config:     nil, // No config provided
	}

	// Mock expectations
	// 1. Check for existing mapping - not found (6 parameters)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// Execute
	result, err := service.CreateClientFolder(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	// Since config validation will fail with nil config
	var folderErr *FolderCreationError
	assert.True(t, errors.As(err, &folderErr))
	assert.Equal(t, ErrCodeConfigMissing, folderErr.Code)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateClientFolder - Error when Google Drive API fails
func TestCreateClientFolder_Error_GoogleDriveAPI(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}

	req := &CreateClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Test Client",
		ClientCode: "TC001",
		Config:     config, // Pass config directly
	}

	// Mock expectations
	// 1. Check for existing mapping - not found (6 parameters)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// 2. Check for existing folder in Google Drive - not found
	mockDriveClient.On("ListFilesWithQuery", mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil)

	// 3. Create folder in Google Drive - error
	mockDriveClient.On("CreateFolder", "Test Client - TC001", testRootID).
		Return(nil, errors.New("Google Drive API error"))

	// Execute
	result, err := service.CreateClientFolder(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	var folderErr *FolderCreationError
	assert.True(t, errors.As(err, &folderErr))
	assert.Equal(t, ErrCodeAPIError, folderErr.Code)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// ============ CreateMatterParentFolder Tests ============

// Test CreateMatterParentFolder - Success with new folder
func TestCreateMatterParentFolder_Success_NewFolder(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	driveFile := getTestDriveFile()

	req := &CreateMatterParentFolderRequest{
		TenantID: testTenantID,
		ClientID: testClientID,
		Config:   &model.GDriveConfig{RootID: testRootID, DriveID: ""},
	}

	// Mock expectations
	// 1. Check for existing mapping - not found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "parent", "google", testTenantID, uint64(0), testClientID).
		Return(nil, gorm.ErrRecordNotFound)

	// 2. Get client folder mapping
	clientMapping := getTestDocumentMapping("client", testClientID)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(clientMapping, nil)

	// 3. Check for existing folder - not found
	mockDriveClient.On("ListFilesWithQuery", mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil)

	// 4. Create folder in Google Drive
	mockDriveClient.On("CreateFolder", "Matters", testFolderID).
		Return(driveFile, nil)

	// 5. Create document mapping
	mockDocMappingRepo.On("CreateOrUpdate", ctx, mock.AnythingOfType("*model.DocumentMapping")).
		Return(nil)

	// Execute
	result, err := service.CreateMatterParentFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, "Test Folder", result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateMatterParentFolder - Success with existing mapping
func TestCreateMatterParentFolder_Success_ExistingMapping(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	existingMapping := getTestDocumentMapping("parent", testClientID)
	driveFile := getTestDriveFile()

	req := &CreateMatterParentFolderRequest{
		TenantID: testTenantID,
		ClientID: testClientID,
		Config:   &model.GDriveConfig{RootID: testRootID, DriveID: ""},
	}

	// Mock expectations
	// 1. Check for existing mapping - found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "parent", "google", testTenantID, uint64(0), testClientID).
		Return(existingMapping, nil)

	// 2. Get folder info
	mockDriveClient.On("GetFileInfo", testFolderID).
		Return(driveFile, nil)

	// Execute
	result, err := service.CreateMatterParentFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, driveFile.Name, result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// ============ CreateMatterFolder Tests ============

// Test CreateMatterFolder - Success with new folder
func TestCreateMatterFolder_Success_NewFolder(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	driveFile := getTestDriveFile()
	driveFile.Name = "2024-001 - Test Matter"

	req := &CreateMatterFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		MatterID:   testMatterID,
		MatterName: "Test Matter",
		MatterCode: "2024-001",
		Config:     &model.GDriveConfig{RootID: testRootID, DriveID: ""},
	}

	// Mock expectations
	// 1. Check for existing mapping - not found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "matter", "google", testTenantID, testMatterID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// 2. Check for existing matter parent folder mapping - not found (will auto-create)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "parent", "google", testTenantID, uint64(0), testClientID).
		Return(nil, gorm.ErrRecordNotFound)

	// 3. Auto-create matter parent folder - get client mapping first
	clientMapping := getTestDocumentMapping("client", testClientID)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(clientMapping, nil)

	// 4. Search for existing parent folder - not found
	mockDriveClient.On("ListFilesWithQuery", mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).Once()

	// 5. Create parent folder in Google Drive
	parentDriveFile := getTestDriveFile()
	parentDriveFile.Name = "Matters"
	mockDriveClient.On("CreateFolder", "Matters", testFolderID).
		Return(parentDriveFile, nil)

	// 6. Create parent document mapping
	mockDocMappingRepo.On("CreateOrUpdate", ctx, mock.AnythingOfType("*model.DocumentMapping")).
		Return(nil).Once()

	// 7. Check for existing matter folder - not found
	mockDriveClient.On("ListFilesWithQuery", mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).Once()

	// 8. Create matter folder in Google Drive (using parent folder ID)
	mockDriveClient.On("CreateFolder", "Test Matter - 2024-001", testFolderID).
		Return(driveFile, nil)

	// 9. Create matter document mapping
	mockDocMappingRepo.On("CreateOrUpdate", ctx, mock.AnythingOfType("*model.DocumentMapping")).
		Return(nil).Once()

	// Execute
	result, err := service.CreateMatterFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, "2024-001 - Test Matter", result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateMatterFolder - Success with existing mapping
func TestCreateMatterFolder_Success_ExistingMapping(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	existingMapping := getTestDocumentMapping("matter", testMatterID)
	driveFile := getTestDriveFile()

	req := &CreateMatterFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		MatterID:   testMatterID,
		MatterName: "Test Matter",
		MatterCode: "2024-001",
		Config:     &model.GDriveConfig{RootID: testRootID, DriveID: ""},
	}

	// Mock expectations
	// 1. Check for existing mapping - found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "matter", "google", testTenantID, testMatterID, uint64(0)).
		Return(existingMapping, nil)

	// 2. Get folder info
	mockDriveClient.On("GetFileInfo", testFolderID).
		Return(driveFile, nil)

	// Execute
	result, err := service.CreateMatterFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testFolderID, result.FolderID)
	assert.Equal(t, driveFile.Name, result.FolderName)
	assert.Equal(t, testWebLink, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// ============ processTemplate Tests ============

// Test processTemplate function
func TestProcessTemplate_Success(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	tests := []struct {
		name     string
		template string
		data     map[string]string
		expected string
	}{
		{
			name:     "Client template",
			template: "{ClientName} - {ClientCode}",
			data: map[string]string{
				"ClientName": "Test Client",
				"ClientCode": "TC001",
			},
			expected: "Test Client - TC001",
		},
		{
			name:     "Matter template",
			template: "{MatterName} - {MatterCode}",
			data: map[string]string{
				"MatterName": "Test Matter",
				"MatterCode": "2024-001",
			},
			expected: "Test Matter - 2024-001",
		},
		{
			name:     "Complex template",
			template: "{Year}/{ClientCode}/{MatterCode} - {MatterName}",
			data: map[string]string{
				"Year":       "2024",
				"ClientCode": "TC",
				"MatterCode": "001",
				"MatterName": "Complex Matter",
			},
			expected: "2024/TC/001 - Complex Matter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result := service.(*folderService).processTemplate(tt.template, tt.data)

			// Assert
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test processTemplate - Edge cases
func TestProcessTemplate_EdgeCases(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	tests := []struct {
		name     string
		template string
		data     map[string]string
		expected string
	}{
		{
			name:     "No placeholders",
			template: "Static Folder Name",
			data: map[string]string{
				"ClientName": "Test Client",
			},
			expected: "Static Folder Name",
		},
		{
			name:     "Empty template",
			template: "",
			data: map[string]string{
				"ClientName": "Test Client",
			},
			expected: "",
		},
		{
			name:     "Missing variables",
			template: "{ClientName} - {MissingVar}",
			data: map[string]string{
				"ClientName": "Test Client",
			},
			expected: "Test Client - {MissingVar}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result := service.(*folderService).processTemplate(tt.template, tt.data)

			// Assert
			assert.Equal(t, tt.expected, result)
		})
	}
}

// ============ Helper Functions Tests ============

// Test buildClientVariables
func TestBuildClientVariables(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	// Test data
	clientData := &ClientData{
		Name:      "Test Client",
		ShortName: "TC",
		Code:      "TC001",
		ID:        456,
	}

	// Execute
	variables := service.(*folderService).buildClientVariables(clientData)

	// Assert
	expected := map[string]string{
		"name":       "Test Client",
		"short_name": "TC",
		"code":       "TC001",
		"id":         "456",
	}
	assert.Equal(t, expected, variables)
}

// Test buildMatterVariables
func TestBuildMatterVariables(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	// Test data
	matterData := &MatterData{
		Name:     "Test Matter",
		Code:     "2024-001",
		ID:       789,
		ClientID: 456,
	}

	// Execute
	variables := service.(*folderService).buildMatterVariables(matterData)

	// Assert
	expected := map[string]string{
		"name":      "Test Matter",
		"code":      "2024-001",
		"id":        "789",
		"client_id": "456",
	}
	assert.Equal(t, expected, variables)
}

// Test generateClientFolderName
func TestGenerateClientFolderName(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	testTenantID := uint64(123)

	tests := []struct {
		name       string
		clientData *ClientData
		config     *model.GDriveConfig
		expected   string
	}{
		{
			name: "Default naming with short name",
			clientData: &ClientData{
				Name:      "Test Client Full Name",
				ShortName: "TC",
				Code:      "TC001",
				ID:        456,
			},
			config:   &model.GDriveConfig{},
			expected: "TC - TC001",
		},
		{
			name: "Default naming without short name",
			clientData: &ClientData{
				Name:      "Test Client",
				ShortName: "",
				Code:      "TC001",
				ID:        456,
			},
			config:   &model.GDriveConfig{},
			expected: "Test Client - TC001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result, err := service.(*folderService).generateClientFolderName(ctx, testTenantID, tt.clientData, tt.config)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test generateMatterFolderName
func TestGenerateMatterFolderName(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	testTenantID := uint64(123)

	tests := []struct {
		name       string
		matterData *MatterData
		config     *model.GDriveConfig
		expected   string
	}{
		{
			name: "Default naming",
			matterData: &MatterData{
				Name:     "Important Matter",
				Code:     "2024-001",
				ID:       789,
				ClientID: 456,
			},
			config:   &model.GDriveConfig{},
			expected: "Important Matter - 2024-001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result, err := service.(*folderService).generateMatterFolderName(ctx, testTenantID, tt.matterData, tt.config)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// ============ RenameClientFolder Tests ============

// Test RenameClientFolder - Success with name change
func TestRenameClientFolder_Success_WithNameChange(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}
	folderID := "folder123"

	// Create test mapping
	mapping := &model.DocumentMapping{
		TenantID:       testTenantID,
		Type:           model.DocTypeClient,
		ObjectID:       testClientID,
		ParentObjectID: 0,
		DriveID:        folderID,
		ParentDriveID:  testRootID,
		Provider:       model.DocProviderGoogle,
	}

	// Create test folder info
	oldFolder := &drive.File{
		Id:          folderID,
		Name:        "Old Client - TC001",
		WebViewLink: "https://drive.google.com/folder/" + folderID,
	}

	// Create test updated folder
	updatedFolder := &drive.File{
		Id:          folderID,
		Name:        "New Client - TC001",
		WebViewLink: "https://drive.google.com/folder/" + folderID,
	}

	req := &RenameClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "New Client",
		ClientCode: "TC001",
		Config:     config, // Pass config directly
	}

	// Mock expectations
	// 1. Get existing mapping
	mockDocMappingRepo.On("FirstObjectMapping", ctx, model.DocTypeClient, model.DocProviderGoogle, testTenantID, testClientID, uint64(0)).
		Return(mapping, nil)

	// 2. Get current folder info
	mockDriveClient.On("GetFileInfo", folderID).
		Return(oldFolder, nil)

	// 3. Rename folder
	mockDriveClient.On("RenameFile", folderID, "New Client - TC001").
		Return(updatedFolder, nil)

	// Execute
	result, err := service.RenameClientFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, folderID, result.FolderID)
	assert.Equal(t, "Old Client - TC001", result.OldName)
	assert.Equal(t, "New Client - TC001", result.NewName)
	assert.Equal(t, "https://drive.google.com/folder/"+folderID, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test RenameClientFolder - Success with no name change
func TestRenameClientFolder_Success_NoNameChange(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}
	folderID := "folder123"

	// Create test mapping
	mapping := &model.DocumentMapping{
		TenantID:       testTenantID,
		Type:           model.DocTypeClient,
		ObjectID:       testClientID,
		ParentObjectID: 0,
		DriveID:        folderID,
		ParentDriveID:  testRootID,
		Provider:       model.DocProviderGoogle,
	}

	// Create test folder info (same name as would be generated)
	folder := &drive.File{
		Id:          folderID,
		Name:        "Client Name - TC001",
		WebViewLink: "https://drive.google.com/folder/" + folderID,
	}

	req := &RenameClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Client Name",
		ClientCode: "TC001",
		Config:     config,
	}

	// Mock expectations
	// 1. Get existing mapping
	mockDocMappingRepo.On("FirstObjectMapping", ctx, model.DocTypeClient, model.DocProviderGoogle, testTenantID, testClientID, uint64(0)).
		Return(mapping, nil)

	// 2. Get current folder info
	mockDriveClient.On("GetFileInfo", folderID).
		Return(folder, nil)

	// No rename call should be made since names match

	// Execute
	result, err := service.RenameClientFolder(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, folderID, result.FolderID)
	assert.Equal(t, "Client Name - TC001", result.OldName)
	assert.Equal(t, "Client Name - TC001", result.NewName)
	assert.Equal(t, "https://drive.google.com/folder/"+folderID, result.WebViewLink)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test RenameClientFolder - Error when folder mapping not found
func TestRenameClientFolder_Error_MappingNotFound(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()

	req := &RenameClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Client Name",
		ClientCode: "TC001",
		Config:     &model.GDriveConfig{RootID: testRootID, DriveID: ""},
	}

	// Mock expectations
	// 1. Get existing mapping - not found (first call in RenameClientFolder)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, model.DocTypeClient, model.DocProviderGoogle, testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound).Once()

	// 2. Get existing mapping - not found (second call in CreateClientFolder)
	mockDocMappingRepo.On("FirstObjectMapping", ctx, model.DocTypeClient, model.DocProviderGoogle, testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound).Once()

	// 3. Mock ListFilesWithQuery for checking existing folder
	mockDriveClient.On("ListFilesWithQuery", mock.MatchedBy(func(query string) bool {
		return query == "name='Client Name - TC001' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root-folder-id' in parents"
	}), "").Return([]*drive.File{}, nil)

	// 4. Mock folder creation in Google Drive
	mockDriveClient.On("CreateFolder", "Client Name - TC001", testRootID).Return(&drive.File{
		Id:          "new-folder-123",
		Name:        "Client Name - TC001",
		WebViewLink: "https://drive.google.com/folder/new-folder-123",
	}, nil)

	// 5. Mock document mapping creation
	mockDocMappingRepo.On("CreateOrUpdate", ctx, mock.MatchedBy(func(mapping *model.DocumentMapping) bool {
		return mapping.TenantID == testTenantID &&
			mapping.Type == model.DocTypeClient &&
			mapping.ObjectID == testClientID &&
			mapping.DriveID == "new-folder-123" &&
			mapping.ParentDriveID == testRootID &&
			mapping.Provider == model.DocProviderGoogle
	})).Return(nil)

	// 6. Mock getting folder info after creation
	mockDriveClient.On("GetFileInfo", "new-folder-123").Return(&drive.File{
		Id:          "new-folder-123",
		Name:        "Client Name - TC001",
		WebViewLink: "https://drive.google.com/folder/new-folder-123",
	}, nil)

	// Execute
	result, err := service.RenameClientFolder(ctx, req)

	// Assert - should succeed now because folder is created
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "new-folder-123", result.FolderID)
	assert.Equal(t, "Client Name - TC001", result.NewName)
	assert.Equal(t, "Client Name - TC001", result.OldName) // Same name since just created

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test RenameClientFolder - Error when Google Drive API fails during rename
func TestRenameClientFolder_Error_RenameFailure(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	config := &model.GDriveConfig{RootID: testRootID, DriveID: ""}
	folderID := "folder123"

	// Create test mapping
	mapping := &model.DocumentMapping{
		TenantID:       testTenantID,
		Type:           model.DocTypeClient,
		ObjectID:       testClientID,
		ParentObjectID: 0,
		DriveID:        folderID,
		ParentDriveID:  testRootID,
		Provider:       model.DocProviderGoogle,
	}

	// Create test folder info
	oldFolder := &drive.File{
		Id:          folderID,
		Name:        "Old Client - TC001",
		WebViewLink: "https://drive.google.com/folder/" + folderID,
	}

	req := &RenameClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "New Client",
		ClientCode: "TC001",
		Config:     config,
	}

	// Mock expectations
	// 1. Get existing mapping
	mockDocMappingRepo.On("FirstObjectMapping", ctx, model.DocTypeClient, model.DocProviderGoogle, testTenantID, testClientID, uint64(0)).
		Return(mapping, nil)

	// 2. Get current folder info
	mockDriveClient.On("GetFileInfo", folderID).
		Return(oldFolder, nil)

	// 4. Rename folder - error
	mockDriveClient.On("RenameFile", folderID, "New Client - TC001").
		Return(nil, errors.New("Google Drive API error"))

	// Execute
	result, err := service.RenameClientFolder(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	var folderErr *FolderCreationError
	assert.True(t, errors.As(err, &folderErr))
	assert.Equal(t, ErrCodeAPIError, folderErr.Code)

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// ============ Path Config Validation Tests ============

// Test validateClientFolderPath
func TestValidateClientFolderPath(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	tests := []struct {
		name        string
		path        string
		expectError bool
		errorField  string
	}{
		{
			name:        "Valid client path",
			path:        "{name} - {code}",
			expectError: false,
		},
		{
			name:        "Valid client path with short_name",
			path:        "{short_name|name} - {code}",
			expectError: false,
		},
		{
			name:        "Empty path",
			path:        "",
			expectError: true,
			errorField:  "ClientFolderPath",
		},
		{
			name:        "Multiple segments",
			path:        "{name}/{code}",
			expectError: true,
			errorField:  "ClientFolderPath",
		},
		{
			name:        "Invalid variable",
			path:        "{invalid_var} - {code}",
			expectError: true,
			errorField:  "ClientFolderPath",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			err := service.(*folderService).validateClientFolderPath(tt.path)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				var pathErr *PathConfigError
				assert.True(t, errors.As(err, &pathErr))
				assert.Equal(t, tt.errorField, pathErr.Field)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test validateMatterFolderPath
func TestValidateMatterFolderPath(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	tests := []struct {
		name        string
		path        string
		expectError bool
		errorField  string
	}{
		{
			name:        "Valid 2-level path",
			path:        "{client_folder}/{name} - {code}",
			expectError: false,
		},
		{
			name:        "Valid 3-level path",
			path:        "{client_folder}/Matters/{name} - {code}",
			expectError: false,
		},
		{
			name:        "Empty path",
			path:        "",
			expectError: true,
			errorField:  "MatterFolderPath",
		},
		{
			name:        "Too many levels",
			path:        "{client_folder}/parent1/parent2/parent3/{name} - {code}",
			expectError: true,
			errorField:  "MatterFolderPath",
		},
		{
			name:        "Missing client_folder",
			path:        "SomeFolder/{name} - {code}",
			expectError: true,
			errorField:  "MatterFolderPath",
		},
		{
			name:        "Invalid variable in matter segment",
			path:        "{client_folder}/{invalid_var} - {code}",
			expectError: true,
			errorField:  "MatterFolderPath",
		},
		{
			name:        "Variable in parent segment",
			path:        "{client_folder}/{parent_var}/{name} - {code}",
			expectError: true,
			errorField:  "MatterFolderPath",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			err := service.(*folderService).validateMatterFolderPath(tt.path)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				var pathErr *PathConfigError
				assert.True(t, errors.As(err, &pathErr))
				assert.Equal(t, tt.errorField, pathErr.Field)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test CreateClientFolder with invalid path config
func TestCreateClientFolder_Error_InvalidPathConfig(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	invalidConfig := &model.GDriveConfig{
		RootID:  testRootID,
		DriveID: "",
		PathConfig: &model.PathConfig{
			ClientFolderPath: "{invalid_var} - {code}",
		},
	}

	req := &CreateClientFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		ClientName: "Test Client",
		ClientCode: "TC001",
		Config:     invalidConfig, // Pass invalid config directly
	}

	// Mock expectations
	// 1. Check for existing mapping - not found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "client", "google", testTenantID, testClientID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// Execute
	result, err := service.CreateClientFolder(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	var folderErr *FolderCreationError
	assert.True(t, errors.As(err, &folderErr))
	assert.Equal(t, ErrCodeConfigMissing, folderErr.Code)
	assert.Contains(t, folderErr.Message, "Invalid Google Drive path configuration")

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}

// Test CreateMatterFolder with invalid path config
func TestCreateMatterFolder_Error_InvalidPathConfig(t *testing.T) {
	// Setup
	mockDriveClient, mockDocMappingRepo, mockDocSettingRepo := setupMocks(t)
	service := setupFolderService(mockDriveClient, mockDocMappingRepo, mockDocSettingRepo)

	ctx := context.Background()
	invalidConfig := &model.GDriveConfig{
		RootID:  testRootID,
		DriveID: "",
		PathConfig: &model.PathConfig{
			MatterFolderPath: "InvalidPath/{name} - {code}", // Missing {client_folder}
		},
	}

	req := &CreateMatterFolderRequest{
		TenantID:   testTenantID,
		ClientID:   testClientID,
		MatterID:   testMatterID,
		MatterName: "Test Matter",
		MatterCode: "2024-001",
		Config:     invalidConfig,
	}

	// Mock expectations
	// 1. Check for existing mapping - not found
	mockDocMappingRepo.On("FirstObjectMapping", ctx, "matter", "google", testTenantID, testMatterID, uint64(0)).
		Return(nil, gorm.ErrRecordNotFound)

	// Execute
	result, err := service.CreateMatterFolder(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	var folderErr *FolderCreationError
	assert.True(t, errors.As(err, &folderErr))
	assert.Equal(t, ErrCodeConfigMissing, folderErr.Code)
	assert.Contains(t, folderErr.Message, "Invalid Google Drive path configuration")

	// Verify mocks
	mockDriveClient.AssertExpectations(t)
	mockDocMappingRepo.AssertExpectations(t)
	mockDocSettingRepo.AssertExpectations(t)
}
