package gdrive

import (
	"context"
	"testing"

	"bilabl/docman/domain/model"
	mockGdrive "bilabl/docman/mocks/gdrive"
	mock_repositories "bilabl/docman/mocks/repositories"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// TestGetParentFolderIDFromObject_ErrorHandling tests the error handling for DocumentMapping lookups
func TestGetParentFolderIDFromObject_ErrorHandling(t *testing.T) {
	tests := []struct {
		name          string
		objectType    string
		objectID      uint64
		tenantID      uint64
		setupMocks    func(*mock_repositories.MockDocumentMappingRepository)
		expectedError string
		expectedCode  string
	}{
		{
			name:       "Record not found should return 404",
			objectType: "matter",
			objectID:   12088,
			tenantID:   1,
			setupMocks: func(mockRepo *mock_repositories.MockDocumentMappingRepository) {
				mockRepo.On("FindOne", mock.Anything, mock.Anything).
					Return(nil, gorm.ErrRecordNotFound)
			},
			expectedError: "No document mapping found for matter with ID 12088",
			expectedCode:  ErrCodeDocumentNotFound,
		},
		{
			name:       "Database error should return 400",
			objectType: "matter",
			objectID:   12088,
			tenantID:   1,
			setupMocks: func(mockRepo *mock_repositories.MockDocumentMappingRepository) {
				mockRepo.On("FindOne", mock.Anything, mock.Anything).
					Return(nil, assert.AnError) // Generic database error
			},
			expectedError: "Failed to query document mapping for matter with ID 12088",
			expectedCode:  ErrCodeInvalidRequest,
		},
		{
			name:       "Nil mapping should return 404",
			objectType: "client",
			objectID:   5000,
			tenantID:   1,
			setupMocks: func(mockRepo *mock_repositories.MockDocumentMappingRepository) {
				mockRepo.On("FindOne", mock.Anything, mock.Anything).
					Return(nil, nil) // No error but nil mapping
			},
			expectedError: "No document mapping found for client with ID 5000",
			expectedCode:  ErrCodeDocumentNotFound,
		},
		{
			name:       "Unsupported object type should return 400",
			objectType: "invalid_type",
			objectID:   1000,
			tenantID:   1,
			setupMocks: func(mockRepo *mock_repositories.MockDocumentMappingRepository) {
				// No mock setup needed as validation happens before DB call
			},
			expectedError: "Unsupported object type: invalid_type",
			expectedCode:  ErrCodeInvalidRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
			mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
			mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
			mockDriveClient := mockGdrive.NewMockDriveClient(t)

			tt.setupMocks(mockMappingRepo)

			// Create service
			mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
			service := NewDocumentService(
				mockDocRepo,
				mockMappingRepo,
				mockUploadSessionRepo,
				mockDocSettingRepo,
				mockDriveClient,
			)

			// Execute test
			ctx := context.Background()
			result, err := service.(*documentService).getParentFolderIDFromObject(
				ctx,
				tt.objectType,
				tt.objectID,
				tt.tenantID,
			)

			// Verify results
			assert.Empty(t, result)
			assert.Error(t, err)

			// Check error type and code
			docErr, ok := err.(*DocumentServiceError)
			assert.True(t, ok, "Expected DocumentServiceError")
			assert.Equal(t, tt.expectedCode, docErr.Code)
			assert.Contains(t, docErr.Message, tt.expectedError)
		})
	}
}

// TestGetParentFolderIDFromObject_Success tests the success case
func TestGetParentFolderIDFromObject_Success(t *testing.T) {
	// Setup mocks
	mockMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockDocRepo := mock_repositories.NewMockDocumentRepository(t)
	mockUploadSessionRepo := mock_repositories.NewMockUploadSessionRepository(t)
	mockDriveClient := mockGdrive.NewMockDriveClient(t)

	expectedMapping := &model.DocumentMapping{
		TenantID: 1,
		Type:     model.DocTypeMatter,
		ObjectID: 12088,
		DriveID:  "test-drive-id-123",
		Provider: model.DocProviderGoogle,
	}

	mockMappingRepo.On("FindOne", mock.Anything, mock.Anything).
		Return(expectedMapping, nil)

	// Create service
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	service := NewDocumentService(
		mockDocRepo,
		mockMappingRepo,
		mockUploadSessionRepo,
		mockDocSettingRepo,
		mockDriveClient,
	)

	// Execute test
	ctx := context.Background()
	result, err := service.(*documentService).getParentFolderIDFromObject(
		ctx,
		"matter",
		12088,
		1,
	)

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, "test-drive-id-123", result)
}
