package gdrive

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"

	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/pkg/logger"
)

// MatterEventConsumer handles matter-related events for Google Drive operations
type MatterEventConsumer struct {
	folderService    FolderService
	docSettingRepo   repositories.DocumentSettingRepository
	docMappingRepo   repositories.DocumentMappingRepository
	entityFetcher    entity.GenericFetcher
	permissionSyncer PermissionSyncer // Interface for permission sync
}

// NewMatterEventConsumer creates a new MatterEventConsumer
func NewMatterEventConsumer(folderService FolderService, docSettingRepo repositories.DocumentSettingRepository, docMappingRepo repositories.DocumentMappingRepository, entityFetcher entity.GenericFetcher, permissionSyncer PermissionSyncer) *MatterEventConsumer {
	return &MatterEventConsumer{
		folderService:    folderService,
		docSettingRepo:   docSettingRepo,
		docMappingRepo:   docMappingRepo,
		entityFetcher:    entityFetcher,
		permissionSyncer: permissionSyncer,
	}
}

// MatterEvent represents the general structure of matter events
type MatterEvent struct {
	Topic string      `json:"topic"`
	Body  interface{} `json:"body"`
}

// MatterCreatePayload represents the payload for matter.create events
type MatterCreatePayload struct {
	ID         uint64            `json:"id"`
	ClientID   uint64            `json:"client_id"`
	Name       string            `json:"name"`
	Code       string            `json:"code"`
	TenantID   uint64            `json:"tenant_id"`
	ActorID    uint64            `json:"actor_id"`
	NoNotify   bool              `json:"no_notify"`
	Owners     []uint64          `json:"owners"`
	OwnerUsers []model.OwnerUser `json:"owner_users"`
	Extra      *MatterExtra      `json:"extra,omitempty"`
}

// MatterExtra contains additional matter information
type MatterExtra struct {
	Current MatterExtraDetails `json:"current"`
	Old     MatterExtraDetails `json:"old,omitempty"`
}

// MatterExtraDetails contains detailed matter information
type MatterExtraDetails struct {
	Name       string            `json:"name"`
	Owners     []uint64          `json:"owners"`
	OwnerUsers []model.OwnerUser `json:"owner_users"`
}

// MatterUpdatePayload represents the payload for matter.update events
type MatterUpdatePayload struct {
	ID         uint64            `json:"id"`
	ClientID   uint64            `json:"client_id"`
	Name       string            `json:"name"`
	Code       string            `json:"code"`
	TenantID   uint64            `json:"tenant_id"`
	ActorID    uint64            `json:"actor_id"`
	NoNotify   bool              `json:"no_notify"`
	Owners     []uint64          `json:"owners"`
	OwnerUsers []model.OwnerUser `json:"owner_users"`
	Extra      *MatterExtra      `json:"extra,omitempty"`
}

// HandleMatterCreated processes matter.create events
func (c *MatterEventConsumer) HandleMatterCreated(ctx context.Context, payloadJSON string) error {
	log := logger.WithCtx(ctx, "HandleMatterCreated")

	// Parse the event payload
	var event MatterEvent
	if err := json.Unmarshal([]byte(payloadJSON), &event); err != nil {
		log.WithError(err).WithField("payload", payloadJSON).Error("Failed to parse matter event payload")
		return err
	}

	// Extract the body as matter create payload
	bodyBytes, err := json.Marshal(event.Body)
	if err != nil {
		log.WithError(err).WithField("body", event.Body).Error("Failed to marshal event body")
		return err
	}

	var payload MatterCreatePayload
	if err := json.Unmarshal(bodyBytes, &payload); err != nil {
		log.WithError(err).WithField("body", string(bodyBytes)).Error("Failed to parse matter create payload")
		return err
	}

	// Validate payload
	if event.Body == nil {
		log.WithField("payload", payloadJSON).Error("Event body is missing")
		return fmt.Errorf("event body is missing")
	}

	if payload.TenantID == 0 || payload.ID == 0 || payload.ClientID == 0 {
		log.WithFields(map[string]interface{}{
			"tenant_id": payload.TenantID,
			"matter_id": payload.ID,
			"client_id": payload.ClientID,
		}).Error("Invalid matter event payload - missing required fields")
		return fmt.Errorf("invalid matter event payload")
	}

	log.WithFields(map[string]interface{}{
		"tenant_id":   payload.TenantID,
		"matter_id":   payload.ID,
		"client_id":   payload.ClientID,
		"matter_name": payload.Name,
		"topic":       event.Topic,
	}).Info("Received matter event")

	// Check if Google Drive is enabled for this tenant
	config, err := c.getGDriveConfig(ctx, payload.TenantID)
	if err != nil {
		log.WithError(err).WithField("tenant_id", payload.TenantID).Error("Failed to get Google Drive config")
		return err
	}

	if !config.Enabled {
		log.WithField("tenant_id", payload.TenantID).WithField("operation", "HandleMatterCreated").Info("Google Drive operation skipped due to disabled integration")
		return nil
	}

	// Ensure client folder exists before creating matter folder
	_, err = c.ensureClientFolderExists(ctx, payload.ClientID, payload.TenantID, config)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"client_id": payload.ClientID,
			"tenant_id": payload.TenantID,
		}).Error("Failed to ensure client folder exists")
		return fmt.Errorf("failed to ensure client folder exists: %w", err)
	}

	// Create the matter folder (client folder now guaranteed to exist)
	req := &CreateMatterFolderRequest{
		TenantID:   payload.TenantID,
		ClientID:   payload.ClientID,
		MatterID:   payload.ID,
		MatterName: payload.Name,
		MatterCode: payload.Code,
		Config:     config,
	}

	// Call folder service to create matter folder
	resp, err := c.folderService.CreateMatterFolder(ctx, req)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"matter_id": payload.ID,
			"client_id": payload.ClientID,
			"tenant_id": payload.TenantID,
		}).Error("Failed to create matter folder")

		// Record failure for potential retry
		if recordErr := c.recordFailure(ctx, "matter_folder_creation", err); recordErr != nil {
			log.WithError(recordErr).Error("Recording event failure for retry (not implemented yet)")
		}

		return err
	}

	log.WithFields(map[string]interface{}{
		"matter_id":   payload.ID,
		"folder_id":   resp.FolderID,
		"folder_name": resp.FolderName,
		"tenant_id":   payload.TenantID,
	}).Info("Successfully created matter folder")

	// Sync permissions if permission syncer is available and owner users exist
	if c.permissionSyncer != nil && len(payload.OwnerUsers) > 0 {
		ownerEmails := make([]string, 0, len(payload.OwnerUsers))
		for _, owner := range payload.OwnerUsers {
			email := strings.TrimSpace(owner.Email)
			if email != "" {
				ownerEmails = append(ownerEmails, email)
			}
		}

		if len(ownerEmails) > 0 {
			log.WithFields(map[string]interface{}{
				"matter_id":    payload.ID,
				"tenant_id":    payload.TenantID,
				"owner_emails": ownerEmails,
			}).Info("Syncing permissions for created matter folder")

			err = c.permissionSyncer.SyncGoogleDrivePermissions(
				ctx,
				payload.TenantID,
				"matter",
				payload.ID,
				ownerEmails,
			)
			if err != nil {
				log.WithFields(map[string]interface{}{
					"matter_id": payload.ID,
					"tenant_id": payload.TenantID,
				}).WithError(err).Error("Failed to sync permissions for matter folder")
				// Don't fail the entire operation if permission sync fails
				// Log the error and continue
			} else {
				log.WithFields(map[string]interface{}{
					"matter_id": payload.ID,
					"tenant_id": payload.TenantID,
				}).Info("Successfully synced permissions for matter folder")
			}
		} else {
			log.WithFields(map[string]interface{}{
				"matter_id": payload.ID,
				"tenant_id": payload.TenantID,
			}).Warn("No valid owner emails found for permission sync")
		}
	}

	return nil
}

// HandleMatterUpdated handles matter.update events
func (c *MatterEventConsumer) HandleMatterUpdated(ctx context.Context, payloadJSON string) error {
	log := logger.WithCtx(ctx, "[MatterEventConsumer][HandleMatterUpdated]")
	log.Infof("handling matter.update event")

	// Parse the payload
	var payload MatterEvent
	if err := json.Unmarshal([]byte(payloadJSON), &payload); err != nil {
		log.WithError(err).Error("failed to parse event payload")
		return err
	}

	// Validate event type
	if payload.Topic != "matter.update" {
		log.Warnf("unexpected event topic: %s", payload.Topic)
		return nil
	}

	// Parse matter data from body
	var body MatterUpdatePayload
	bodyBytes, err := json.Marshal(payload.Body)
	if err != nil {
		log.WithError(err).Error("failed to marshal event body")
		return err
	}

	if err := json.Unmarshal(bodyBytes, &body); err != nil {
		log.WithError(err).Error("failed to parse matter update payload")
		return err
	}

	log.Infof("processing matter update: matter_id=%d, name=%s, code=%s, tenant_id=%d",
		body.ID, body.Name, body.Code, body.TenantID)

	// Get Google Drive configuration
	config, err := c.getGDriveConfig(ctx, body.TenantID)
	if err != nil {
		log.WithError(err).Error("failed to get Google Drive configuration")
		return err
	}

	if !config.Enabled {
		log.Infof("Google Drive integration disabled for tenant_id=%d", body.TenantID)
		return nil
	}

	// Ensure matter folder exists (will create client folder if needed)
	_, err = c.ensureMatterFolderExists(ctx, body.ID, body.ClientID, body.TenantID, body.Name, body.Code, config)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"matter_id": body.ID,
			"client_id": body.ClientID,
			"tenant_id": body.TenantID,
		}).Error("Failed to ensure matter folder exists")
		return fmt.Errorf("failed to ensure matter folder exists: %w", err)
	}

	// Rename matter folder (matter folder now guaranteed to exist)
	req := &RenameMatterFolderRequest{
		TenantID:   body.TenantID,
		MatterID:   body.ID,
		ClientID:   body.ClientID,
		MatterName: body.Name,
		MatterCode: body.Code,
		Config:     config,
	}

	_, err = c.folderService.RenameMatterFolder(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to rename matter folder")
		return err
	}

	log.Infof("successfully processed matter update event for matter_id=%d", body.ID)
	return nil
}

// getGDriveConfig retrieves Google Drive configuration for a tenant
func (c *MatterEventConsumer) getGDriveConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	log := logger.WithCtx(ctx, "getGDriveConfig").WithField("tenant_id", tenantID)

	setting, err := c.docSettingRepo.GetValueByKey(ctx, tenantID, "gdrive_config")
	if err != nil {
		log.WithError(err).Error("Failed to get Google Drive config from database")
		return nil, fmt.Errorf("config not found")
	}

	config, err := setting.GetGDriveConfig()
	if err != nil {
		log.WithError(err).Error("Failed to parse Google Drive config")
		return nil, fmt.Errorf("invalid config format")
	}

	return config, nil
}

// convertEntityToClientData converts entity.Generic to ClientData for folder creation
func (c *MatterEventConsumer) convertEntityToClientData(clientEntity *entity.Generic) *ClientData {
	return &ClientData{
		ID:        uint64(clientEntity.ID),
		Name:      clientEntity.Name,
		ShortName: clientEntity.ShortName,
		Code:      clientEntity.Code,
	}
}

// ensureClientFolderExists checks if client folder exists and creates it if missing
func (c *MatterEventConsumer) ensureClientFolderExists(ctx context.Context, clientID, tenantID uint64, config *model.GDriveConfig) (*model.DocumentMapping, error) {
	log := logger.WithCtx(ctx, "ensureClientFolderExists").WithField("client_id", clientID)

	// Check if client folder mapping exists
	clientMapping, err := c.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeClient,
		model.DocProviderGoogle,
		tenantID,
		clientID,
		0,
	)
	if err == nil {
		log.Infof("client folder already exists with drive_id=%s", clientMapping.DriveID)
		return clientMapping, nil
	}

	if !model.IsNotFound(err) {
		log.WithError(err).Error("failed to check existing client folder mapping")
		return nil, fmt.Errorf("failed to check client folder mapping: %w", err)
	}

	// Client folder doesn't exist, fetch client information
	log.Info("client folder not found, fetching client information")
	clientEntity, err := c.entityFetcher.FetchCache(ctx, entity.KindClient, entity.ID(clientID))
	if err != nil {
		log.WithError(err).Error("failed to fetch client information")
		return nil, fmt.Errorf("failed to fetch client information: %w", err)
	}

	// Convert entity to ClientData
	clientData := c.convertEntityToClientData(clientEntity)

	// Create client folder
	log.Infof("creating client folder for client: %s", clientData.Name)
	createReq := &CreateClientFolderRequest{
		TenantID:   tenantID,
		ClientID:   clientID,
		ClientName: clientData.Name,
		ShortName:  clientData.ShortName,
		ClientCode: clientData.Code,
		Config:     config,
	}

	createResp, err := c.folderService.CreateClientFolder(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("failed to create client folder")
		return nil, fmt.Errorf("failed to create client folder: %w", err)
	}

	log.Infof("successfully created client folder with drive_id=%s", createResp.FolderID)
	return createResp.DocumentMapping, nil
}

// ensureMatterFolderExists checks if matter folder exists and creates it if missing
func (c *MatterEventConsumer) ensureMatterFolderExists(ctx context.Context, matterID, clientID, tenantID uint64, matterName, matterCode string, config *model.GDriveConfig) (*model.DocumentMapping, error) {
	log := logger.WithCtx(ctx, "ensureMatterFolderExists").WithField("matter_id", matterID)

	// Check if matter folder mapping exists
	matterMapping, err := c.docMappingRepo.FirstObjectMapping(
		ctx,
		model.DocTypeMatter,
		model.DocProviderGoogle,
		tenantID,
		matterID,
		clientID,
	)
	if err == nil {
		log.Infof("matter folder already exists with drive_id=%s", matterMapping.DriveID)
		return matterMapping, nil
	}

	if !model.IsNotFound(err) {
		log.WithError(err).Error("failed to check existing matter folder mapping")
		return nil, fmt.Errorf("failed to check matter folder mapping: %w", err)
	}

	// Matter folder doesn't exist, ensure client folder exists first
	log.Info("matter folder not found, ensuring client folder exists")
	_, err = c.ensureClientFolderExists(ctx, clientID, tenantID, config)
	if err != nil {
		log.WithError(err).Error("failed to ensure client folder exists")
		return nil, fmt.Errorf("failed to ensure client folder exists: %w", err)
	}

	// Create matter folder
	log.Infof("creating matter folder for matter: %s", matterName)
	createReq := &CreateMatterFolderRequest{
		TenantID:   tenantID,
		ClientID:   clientID,
		MatterID:   matterID,
		MatterName: matterName,
		MatterCode: matterCode,
		Config:     config,
	}

	createResp, err := c.folderService.CreateMatterFolder(ctx, createReq)
	if err != nil {
		log.WithError(err).Error("failed to create matter folder")
		return nil, fmt.Errorf("failed to create matter folder: %w", err)
	}

	log.Infof("successfully created matter folder with drive_id=%s", createResp.FolderID)
	return createResp.DocumentMapping, nil
}

// recordFailure records a failed event for retry mechanism (placeholder)
func (c *MatterEventConsumer) recordFailure(ctx context.Context, eventType string, err error) error {
	log := logger.WithCtx(ctx, "recordFailure").WithFields(map[string]interface{}{
		"event_type": eventType,
		"error":      err.Error(),
	})

	log.Error("Recording event failure for retry (not implemented yet)")
	// TODO: Implement failure recording mechanism in subtask 5.13
	return nil
}
