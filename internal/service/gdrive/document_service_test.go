package gdrive

import (
	"context"
	"errors"
	"strings"
	"testing"
	"time"

	"bilabl/docman/domain/model"
	mockGdrive "bilabl/docman/mocks/gdrive"
	mockRepos "bilabl/docman/mocks/repositories"
	"bilabl/docman/pkg/gdrive"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
)

func TestValidateDocumentName(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid name",
			input:       "Valid Document Name",
			expectError: false,
		},
		{
			name:        "empty name",
			input:       "",
			expectError: true,
			errorMsg:    "document name cannot be empty",
		},
		{
			name:        "name too long",
			input:       string(make([]byte, MaxDocumentNameLength+1)),
			expectError: true,
			errorMsg:    "document name exceeds maximum length",
		},
		{
			name:        "invalid character slash",
			input:       "Invalid/Name",
			expectError: true,
			errorMsg:    "document name contains invalid character: /",
		},
		{
			name:        "invalid character backslash",
			input:       "Invalid\\Name",
			expectError: true,
			errorMsg:    "document name contains invalid character: \\",
		},
		{
			name:        "path traversal attempt",
			input:       "../malicious",
			expectError: true,
			errorMsg:    "document name contains invalid path traversal pattern",
		},
		{
			name:        "leading whitespace",
			input:       " Invalid Name",
			expectError: true,
			errorMsg:    "document name cannot have leading or trailing whitespace",
		},
		{
			name:        "trailing whitespace",
			input:       "Invalid Name ",
			expectError: true,
			errorMsg:    "document name cannot have leading or trailing whitespace",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateDocumentName(tt.input)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateCreateDocumentRequest(t *testing.T) {
	tests := []struct {
		name        string
		request     *CreateDocumentRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid request",
			request: &CreateDocumentRequest{
				TenantID: 1,
				Name:     "Valid Document",
				DocType:  model.DocTypeDir,
			},
			expectError: false,
		},
		{
			name:        "nil request",
			request:     nil,
			expectError: true,
			errorMsg:    "request cannot be nil",
		},
		{
			name: "missing tenant ID",
			request: &CreateDocumentRequest{
				Name:    "Valid Document",
				DocType: model.DocTypeDir,
			},
			expectError: true,
			errorMsg:    "tenant ID is required",
		},
		{
			name: "invalid document name",
			request: &CreateDocumentRequest{
				TenantID: 1,
				Name:     "Invalid/Name",
				DocType:  model.DocTypeDir,
			},
			expectError: true,
			errorMsg:    "invalid document name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateCreateDocumentRequest(tt.request)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// setupDocumentServiceMocks creates mocks for document service with all dependencies
func setupDocumentServiceMocksWithSettings(t *testing.T) (*documentService, *mockRepos.MockDocumentRepository, *mockRepos.MockDocumentMappingRepository, *mockRepos.MockDocumentSettingRepository, *mockGdrive.MockDriveClient) {
	mockDocRepo := mockRepos.NewMockDocumentRepository(t)
	mockMappingRepo := mockRepos.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mockRepos.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mockRepos.NewMockDocumentSettingRepository(t)
	mockDriveClient := mockGdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo:        mockDocRepo,
		mappingRepo:         mockMappingRepo,
		uploadSessionRepo:   mockUploadSessionRepo,
		documentSettingRepo: mockDocSettingRepo,
		driveClient:         mockDriveClient,
	}

	return service, mockDocRepo, mockMappingRepo, mockDocSettingRepo, mockDriveClient
}

// setupDocumentServiceMocks maintains backward compatibility for existing tests
func setupDocumentServiceMocks(t *testing.T) (*documentService, *mockRepos.MockDocumentRepository, *mockRepos.MockDocumentMappingRepository, *mockRepos.MockUploadSessionRepository, *mockGdrive.MockDriveClient) {
	mockDocRepo := mockRepos.NewMockDocumentRepository(t)
	mockMappingRepo := mockRepos.NewMockDocumentMappingRepository(t)
	mockUploadSessionRepo := mockRepos.NewMockUploadSessionRepository(t)
	mockDocSettingRepo := mockRepos.NewMockDocumentSettingRepository(t)
	mockDriveClient := mockGdrive.NewMockDriveClient(t)

	service := &documentService{
		documentRepo:        mockDocRepo,
		mappingRepo:         mockMappingRepo,
		uploadSessionRepo:   mockUploadSessionRepo,
		documentSettingRepo: mockDocSettingRepo,
		driveClient:         mockDriveClient,
	}

	return service, mockDocRepo, mockMappingRepo, mockUploadSessionRepo, mockDriveClient
}

func TestCreateDocument_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  model.DocTypeDir,
		ParentID: "parent123",
	}

	expectedDriveFolder := &drive.File{
		Id:          "folder123",
		Name:        "Test Folder",
		WebViewLink: "https://drive.google.com/folder123",
	}

	// Mock expectations - only Google Drive operations (no database)
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("Test Folder", "parent123").
		Return(expectedDriveFolder, nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "folder123", result.DriveFileID)
	assert.Equal(t, "Test Folder", result.Name)
	assert.Equal(t, uint64(1), result.TenantID)
	assert.Equal(t, "parent123", result.ParentDriveID)
	assert.False(t, result.IsFile)

	// Verify all expectations
	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_SuccessWithObjectMapping(t *testing.T) {
	service, _, mockMappingRepo, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID:   1,
		Name:       "Test Folder",
		ObjectType: "client",
		ObjectID:   123,
		DocType:    model.DocTypeDir,
	}

	expectedMapping := &model.DocumentMapping{
		DriveID: "parent123",
	}

	expectedDriveFolder := &drive.File{
		Id:          "folder123",
		Name:        "Test Folder",
		WebViewLink: "https://drive.google.com/folder123",
	}

	// Mock expectations for parent folder lookup
	mockMappingRepo.EXPECT().
		FindOne(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("*model.Query")).
		Return(expectedMapping, nil).
		Once()

	// Mock Google Drive operations
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("Test Folder", "parent123").
		Return(expectedDriveFolder, nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "folder123", result.DriveFileID)
	assert.Equal(t, "Test Folder", result.Name)
	assert.Equal(t, uint64(1), result.TenantID)
	assert.Equal(t, "parent123", result.ParentDriveID)
	assert.False(t, result.IsFile)

	// Verify all expectations
	mockMappingRepo.AssertExpectations(t)
	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_ValidationError(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 0, // Invalid - missing tenant ID
		Name:     "Test Folder",
		DocType:  model.DocTypeDir,
	}

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Invalid request")
}

func TestCreateDocument_DuplicateFolderError(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  model.DocTypeDir,
		ParentID: "parent123",
	}

	existingFolder := &drive.File{
		Id:   "existing123",
		Name: "Test Folder",
	}

	// Mock expectations - duplicate found
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string"), "").
		Return([]*drive.File{existingFolder}, nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to check for duplicate folders")

	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_DriveAPIError(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  model.DocTypeDir,
		ParentID: "parent123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		CreateFolder("Test Folder", "parent123").
		Return(nil, errors.New("Google Drive API error")).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to create folder in Google Drive")

	mockDriveClient.AssertExpectations(t)
}

func TestCreateDocument_ParentMappingNotFound(t *testing.T) {
	service, _, mockMappingRepo, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID:   1,
		Name:       "Test Folder",
		ObjectType: "client",
		ObjectID:   123,
		DocType:    model.DocTypeDir,
	}

	// Mock expectations - mapping not found
	mockMappingRepo.EXPECT().
		FindOne(mock.AnythingOfType("*context.timerCtx"), mock.AnythingOfType("*model.Query")).
		Return(nil, nil).
		Once()

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDocumentNotFound, docErr.Code)
	assert.Contains(t, docErr.Message, "No document mapping found for client with ID 123")

	mockMappingRepo.AssertExpectations(t)
}

func TestCreateDocument_InvalidParentInfo(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test Folder",
		DocType:  model.DocTypeDir,
		// Missing both ParentID and ObjectType/ObjectID
	}

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Either parent_id or both object_type and object_id must be provided")
}

func TestCreateDocument_UnsupportedDocType(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateDocumentRequest{
		TenantID: 1,
		Name:     "Test File",
		DocType:  2, // File type - unsupported
		ParentID: "parent123",
	}

	// Execute
	result, err := service.CreateDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Creating files with content is not supported yet")
}

// ========== GetDocument Tests ==========

func TestGetDocument_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "file123"
	expectedDriveFile := &drive.File{
		Id:           driveFileID,
		Name:         "Test Document.pdf",
		MimeType:     "application/pdf",
		Size:         1024,
		WebViewLink:  "https://drive.google.com/file/d/file123/view",
		Parents:      []string{"parent123"},
		CreatedTime:  "2023-01-01T10:00:00Z",
		ModifiedTime: "2023-01-02T10:00:00Z",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(expectedDriveFile, nil).
		Once()

	// Execute
	result, err := service.GetDocument(ctx, driveFileID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, driveFileID, result.DriveFileID)
	assert.Equal(t, "Test Document.pdf", result.Name)
	assert.Equal(t, "parent123", result.ParentDriveID)
	assert.Equal(t, int64(1024), result.Size)
	assert.Equal(t, "application/pdf", result.ContentType)
	assert.True(t, result.IsFile)

	mockDriveClient.AssertExpectations(t)
}

func TestGetDocument_NotFound(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "nonexistent123"

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(nil, errors.New("file not found")).
		Once()

	// Execute
	result, err := service.GetDocument(ctx, driveFileID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to retrieve document from Google Drive")

	mockDriveClient.AssertExpectations(t)
}

func TestGetDocument_EmptyDriveFileID(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	// Execute
	result, err := service.GetDocument(ctx, "")

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Drive file ID is required")
}

// ========== UpdateDocument Tests ==========

func TestUpdateDocument_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "file123"
	updateReq := &UpdateDocumentRequest{
		Name: "Updated Document.pdf",
	}

	existingFile := &drive.File{
		Id:       driveFileID,
		Name:     "Original Document.pdf",
		MimeType: "application/pdf",
		Parents:  []string{"parent123"},
	}

	updatedFile := &drive.File{
		Id:           driveFileID,
		Name:         "Updated Document.pdf",
		MimeType:     "application/pdf",
		Size:         2048,
		WebViewLink:  "https://drive.google.com/file/d/file123/view",
		Parents:      []string{"parent123"},
		CreatedTime:  "2023-01-01T10:00:00Z",
		ModifiedTime: "2023-01-03T10:00:00Z",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(existingFile, nil).
		Once()

	mockDriveClient.EXPECT().
		ListFilesWithQuery(mock.AnythingOfType("string"), "").
		Return([]*drive.File{}, nil).
		Once()

	mockDriveClient.EXPECT().
		RenameFile(driveFileID, "Updated Document.pdf").
		Return(updatedFile, nil).
		Once()

	// Execute
	result, err := service.UpdateDocument(ctx, driveFileID, updateReq)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, driveFileID, result.DriveFileID)
	assert.Equal(t, "Updated Document.pdf", result.Name)
	assert.Equal(t, "parent123", result.ParentDriveID)
	assert.Equal(t, int64(2048), result.Size)
	assert.True(t, result.IsFile)

	mockDriveClient.AssertExpectations(t)
}

func TestUpdateDocument_InvalidName(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "file123"
	updateReq := &UpdateDocumentRequest{
		Name: "Invalid/Name", // Contains invalid character
	}

	// Execute
	result, err := service.UpdateDocument(ctx, driveFileID, updateReq)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Invalid document name")
}

func TestUpdateDocument_NotFound(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "nonexistent123"
	updateReq := &UpdateDocumentRequest{
		Name: "Updated Name",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(nil, errors.New("file not found")).
		Once()

	// Execute
	result, err := service.UpdateDocument(ctx, driveFileID, updateReq)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to retrieve existing document")

	mockDriveClient.AssertExpectations(t)
}

// ========== DeleteDocument Tests ==========

func TestDeleteDocument_Success_File(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "file123"
	existingFile := &drive.File{
		Id:       driveFileID,
		Name:     "Test Document.pdf",
		MimeType: "application/pdf",
		Parents:  []string{"parent123"},
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(existingFile, nil).
		Once()

	mockDriveClient.EXPECT().
		DeleteFile(driveFileID).
		Return(nil).
		Once()

	// Execute
	err := service.DeleteDocument(ctx, driveFileID)

	// Assert
	assert.NoError(t, err)
	mockDriveClient.AssertExpectations(t)
}

func TestDeleteDocument_Success_EmptyFolder(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "folder123"
	existingFolder := &drive.File{
		Id:       driveFileID,
		Name:     "Test Folder",
		MimeType: "application/vnd.google-apps.folder",
		Parents:  []string{"parent123"},
	}

	emptyFolderResponse := &gdrive.PaginatedResult{
		Files: []*drive.File{}, // Empty folder
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(existingFolder, nil).
		Once()

	mockDriveClient.EXPECT().
		ListFilesInFolder(driveFileID, mock.AnythingOfType("*gdrive.PaginationOptions")).
		Return(emptyFolderResponse, nil).
		Once()

	mockDriveClient.EXPECT().
		DeleteFile(driveFileID).
		Return(nil).
		Once()

	// Execute
	err := service.DeleteDocument(ctx, driveFileID)

	// Assert
	assert.NoError(t, err)
	mockDriveClient.AssertExpectations(t)
}

func TestDeleteDocument_NonEmptyFolder(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "folder123"
	existingFolder := &drive.File{
		Id:       driveFileID,
		Name:     "Test Folder",
		MimeType: "application/vnd.google-apps.folder",
		Parents:  []string{"parent123"},
	}

	nonEmptyFolderResponse := &gdrive.PaginatedResult{
		Files: []*drive.File{
			{Id: "child1", Name: "Child File.pdf"},
		},
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(existingFolder, nil).
		Once()

	mockDriveClient.EXPECT().
		ListFilesInFolder(driveFileID, mock.AnythingOfType("*gdrive.PaginationOptions")).
		Return(nonEmptyFolderResponse, nil).
		Once()

	// Execute
	err := service.DeleteDocument(ctx, driveFileID)

	// Assert
	assert.Error(t, err)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Cannot delete folder that contains files")

	mockDriveClient.AssertExpectations(t)
}

func TestDeleteDocument_NotFound(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	driveFileID := "nonexistent123"

	// Mock expectations
	mockDriveClient.EXPECT().
		GetFileInfo(driveFileID).
		Return(nil, errors.New("file not found")).
		Once()

	// Execute
	err := service.DeleteDocument(ctx, driveFileID)

	// Assert
	assert.Error(t, err)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to retrieve document for deletion")

	mockDriveClient.AssertExpectations(t)
}

func TestDeleteDocument_EmptyDriveFileID(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	// Execute
	err := service.DeleteDocument(ctx, "")

	// Assert
	assert.Error(t, err)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Drive file ID is required")
}

// ========== ListDocuments Tests ==========

func TestListDocuments_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &ListDocumentsRequest{
		TenantID:      1,
		ParentDriveID: "folder123",
		PageSize:      50,
		PageToken:     "",
	}

	expectedFiles := []*drive.File{
		{
			Id:             "file1",
			Name:           "Document1.pdf",
			MimeType:       "application/pdf",
			WebViewLink:    "https://drive.google.com/file/d/file1/view",
			WebContentLink: "https://drive.google.com/file/d/file1/export",
		},
		{
			Id:          "folder1",
			Name:        "Subfolder",
			MimeType:    "application/vnd.google-apps.folder",
			WebViewLink: "https://drive.google.com/drive/folders/folder1",
		},
	}

	expectedResult := &gdrive.PaginatedResult{
		Files:         expectedFiles,
		NextPageToken: "next-token-123",
		HasNextPage:   true,
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		ListFilesInFolder("folder123", mock.MatchedBy(func(opts *gdrive.PaginationOptions) bool {
			return opts.PageSize == 50 && opts.PageToken == ""
		})).
		Return(expectedResult, nil).
		Once()

	// Execute
	result, err := service.ListDocuments(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Documents, 2)
	assert.Equal(t, "next-token-123", result.NextPageToken)
	assert.Equal(t, 2, result.Total)

	// Check first document (file)
	assert.Equal(t, "file1", result.Documents[0].DriveFileID)
	assert.Equal(t, "Document1.pdf", result.Documents[0].Name)
	assert.True(t, result.Documents[0].IsFile)

	// Check second document (folder)
	assert.Equal(t, "folder1", result.Documents[1].DriveFileID)
	assert.Equal(t, "Subfolder", result.Documents[1].Name)
	assert.False(t, result.Documents[1].IsFile)

	mockDriveClient.AssertExpectations(t)
}

func TestListDocuments_PageSizeValidation(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	tests := []struct {
		name             string
		inputPageSize    int
		expectedPageSize int64
	}{
		{
			name:             "Zero page size uses default",
			inputPageSize:    0,
			expectedPageSize: 100, // gdrive.DefaultPageSize
		},
		{
			name:             "Negative page size uses default",
			inputPageSize:    -10,
			expectedPageSize: 100, // gdrive.DefaultPageSize
		},
		{
			name:             "Valid page size",
			inputPageSize:    250,
			expectedPageSize: 250,
		},
		{
			name:             "Max page size",
			inputPageSize:    1000,
			expectedPageSize: 1000, // gdrive.MaxPageSize
		},
		{
			name:             "Over max page size gets capped",
			inputPageSize:    2000,
			expectedPageSize: 1000, // gdrive.MaxPageSize
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &ListDocumentsRequest{
				TenantID:      1,
				ParentDriveID: "folder123",
				PageSize:      tt.inputPageSize,
			}

			expectedResult := &gdrive.PaginatedResult{
				Files:         []*drive.File{},
				NextPageToken: "",
				HasNextPage:   false,
			}

			// Mock expectations with page size validation
			mockDriveClient.EXPECT().
				ListFilesInFolder("folder123", mock.MatchedBy(func(opts *gdrive.PaginationOptions) bool {
					return opts.PageSize == tt.expectedPageSize
				})).
				Return(expectedResult, nil).
				Once()

			// Execute
			result, err := service.ListDocuments(ctx, req)

			// Assert
			assert.NoError(t, err)
			assert.NotNil(t, result)

			mockDriveClient.AssertExpectations(t)
		})
	}
}

func TestListDocuments_RootFolder(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &ListDocumentsRequest{
		TenantID:      1,
		ParentDriveID: "", // Empty means root
		PageSize:      100,
	}

	expectedResult := &gdrive.PaginatedResult{
		Files:         []*drive.File{},
		NextPageToken: "",
		HasNextPage:   false,
	}

	// Mock expectations - should call ListFiles for root
	mockDriveClient.EXPECT().
		ListFiles(mock.MatchedBy(func(opts *gdrive.PaginationOptions) bool {
			return opts.PageSize == 100
		})).
		Return(expectedResult, nil).
		Once()

	// Execute
	result, err := service.ListDocuments(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)

	mockDriveClient.AssertExpectations(t)
}

func TestSearchDocuments_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &SearchDocumentsRequest{
		TenantID:      1,
		ParentDriveID: "folder123",
		Query:         "retry.txt",
		PageSize:      50,
	}

	expectedFiles := []*drive.File{
		{
			Id:             "file1",
			Name:           "retry.txt",
			MimeType:       "text/plain",
			WebViewLink:    "https://drive.google.com/file/d/file1/view",
			WebContentLink: "https://drive.google.com/file/d/file1/export",
		},
	}

	// Mock expectations - should call ListFilesWithQuery with scoped search using fullText
	mockDriveClient.EXPECT().
		ListFilesWithQuery("fullText contains 'retry.txt' and 'folder123' in parents and trashed=false", "").
		Return(expectedFiles, nil).
		Once()

	// Execute
	result, err := service.SearchDocuments(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Documents, 1)
	assert.Equal(t, 1, result.Total)

	// Check found document
	assert.Equal(t, "file1", result.Documents[0].DriveFileID)
	assert.Equal(t, "retry.txt", result.Documents[0].Name)
	assert.True(t, result.Documents[0].IsFile)

	mockDriveClient.AssertExpectations(t)
}

func TestSearchDocuments_GlobalSearch(t *testing.T) {
	service, _, _, mockDocSettingRepo, mockDriveClient := setupDocumentServiceMocksWithSettings(t)
	ctx := context.Background()

	req := &SearchDocumentsRequest{
		TenantID:      1,
		ParentDriveID: "root", // Root search - should resolve to shared drive ID
		Query:         "test",
		PageSize:      50,
	}

	// Mock DocumentSetting to return shared drive config
	mockDocumentSetting := &model.DocumentSetting{
		TenantID: 1,
		Key:      model.KeyGdriveConfig,
		Value:    `{"enabled":true,"root_id":"shared-drive-123","drive_id":"shared-drive-123","resource_type":"shared_drive"}`,
	}

	mockDocSettingRepo.EXPECT().
		GetValueByKey(mock.Anything, uint64(1), model.KeyGdriveConfig).
		Return(mockDocumentSetting, nil).
		Once()

	expectedFiles := []*drive.File{
		{
			Id:          "file1",
			Name:        "test.pdf",
			MimeType:    "application/pdf",
			WebViewLink: "https://drive.google.com/file/d/file1/view",
			DriveId:     "shared-drive-123", // File belongs to target shared drive
		},
	}

	// Mock expectations - should call ListFilesWithQuery with shared drive search using native scoping
	mockDriveClient.EXPECT().
		ListFilesWithQuery("fullText contains 'test' and trashed=false", "shared-drive-123").
		Return(expectedFiles, nil).
		Once()

	// Execute
	result, err := service.SearchDocuments(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Documents, 1)

	mockDriveClient.AssertExpectations(t)
}

func TestListDocuments_MissingTenantID(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &ListDocumentsRequest{
		TenantID:      0, // Missing tenant ID
		ParentDriveID: "folder123",
		PageSize:      100,
	}

	// Execute
	result, err := service.ListDocuments(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
	assert.Contains(t, docErr.Message, "Missing tenant ID")
}

// ========== UploadDocument Tests ==========

func TestUploadDocument_Success(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &UploadDocumentRequest{
		TenantID:      1,
		FileName:      "test-document.pdf",
		ParentDriveID: "folder123",
	}

	expectedUploadInfo := &gdrive.ResumableUploadInfo{
		UploadURL: "https://www.googleapis.com/upload/drive/v3/files/file123?uploadType=resumable",
		FileID:    "file123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		CreateResumableUploadURL("test-document.pdf", "folder123", int64(0)).
		Return(expectedUploadInfo, nil).
		Once()

	// Execute
	result, err := service.UploadDocument(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedUploadInfo.UploadURL, result.UploadURL)
	assert.Equal(t, expectedUploadInfo.FileID, result.DriveFileID)
	assert.Equal(t, req.FileName, result.FileName)

	mockDriveClient.AssertExpectations(t)
}

func TestUploadDocument_ValidationErrors(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	tests := []struct {
		name        string
		request     *UploadDocumentRequest
		expectedErr string
	}{
		{
			name:        "Nil request",
			request:     nil,
			expectedErr: "upload request is required",
		},
		{
			name: "Missing tenant ID",
			request: &UploadDocumentRequest{
				TenantID:      0,
				FileName:      "test.pdf",
				ParentDriveID: "folder123",
			},
			expectedErr: "tenant ID is required",
		},
		{
			name: "Missing file name",
			request: &UploadDocumentRequest{
				TenantID:      1,
				FileName:      "",
				ParentDriveID: "folder123",
			},
			expectedErr: "file name is required",
		},
		{
			name: "Missing parent drive ID",
			request: &UploadDocumentRequest{
				TenantID:      1,
				FileName:      "test.pdf",
				ParentDriveID: "",
			},
			expectedErr: "parent drive ID is required",
		},
		{
			name: "Invalid file name - too long",
			request: &UploadDocumentRequest{
				TenantID:      1,
				FileName:      strings.Repeat("a", 256), // Over 255 character limit
				ParentDriveID: "folder123",
			},
			expectedErr: "invalid file name",
		},
		{
			name: "Invalid file name - invalid characters",
			request: &UploadDocumentRequest{
				TenantID:      1,
				FileName:      "test<>file.pdf", // Contains invalid characters
				ParentDriveID: "folder123",
			},
			expectedErr: "invalid file name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result, err := service.UploadDocument(ctx, tt.request)

			// Assert
			assert.Error(t, err)
			assert.Nil(t, result)

			docErr, ok := err.(*DocumentServiceError)
			assert.True(t, ok)
			assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
			assert.Contains(t, docErr.Message, tt.expectedErr)
		})
	}
}

func TestUploadDocument_DriveAPIError(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &UploadDocumentRequest{
		TenantID:      1,
		FileName:      "test-document.pdf",
		ParentDriveID: "folder123",
	}

	// Mock expectations - API failure
	mockDriveClient.EXPECT().
		CreateResumableUploadURL("test-document.pdf", "folder123", int64(0)).
		Return(nil, errors.New("Google Drive API error")).
		Once()

	// Execute
	result, err := service.UploadDocument(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to generate upload URL")

	mockDriveClient.AssertExpectations(t)
}

// ========== DocumentServiceError Tests ==========

func TestDocumentServiceError_Error(t *testing.T) {
	tests := []struct {
		name     string
		err      *DocumentServiceError
		expected string
	}{
		{
			name: "Error with details",
			err: &DocumentServiceError{
				Code:    ErrCodeDriveAPIError,
				Message: "Failed to create document",
				Details: "Google Drive API returned 403 Forbidden",
			},
			expected: "DRIVE_API_ERROR: Failed to create document (Google Drive API returned 403 Forbidden)",
		},
		{
			name: "Error without details",
			err: &DocumentServiceError{
				Code:    ErrCodeInvalidRequest,
				Message: "Missing required field",
			},
			expected: "INVALID_REQUEST: Missing required field",
		},
		{
			name: "Error with empty details",
			err: &DocumentServiceError{
				Code:    ErrCodeDocumentNotFound,
				Message: "Document not found",
				Details: "",
			},
			expected: "DOCUMENT_NOT_FOUND: Document not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.err.Error()
			assert.Equal(t, tt.expected, result)
		})
	}
}

// ========== Upload Session Tests ==========

func TestCreateUploadSession_Success(t *testing.T) {
	service, _, _, mockUploadSessionRepo, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateUploadSessionRequest{
		TenantID:      1,
		FileName:      "test-document.pdf",
		ParentDriveID: "folder123",
	}

	expectedUploadInfo := &gdrive.ResumableUploadInfo{
		UploadURL: "https://www.googleapis.com/upload/drive/v3/files?uploadType=resumable&upload_id=test123",
		FileID:    "",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		CreateResumableUploadURL(req.FileName, req.ParentDriveID, int64(0)).
		Return(expectedUploadInfo, nil).
		Once()

	mockUploadSessionRepo.EXPECT().
		Create(mock.AnythingOfType("*context.timerCtx"), mock.MatchedBy(func(session *model.UploadSession) bool {
			return session.TenantID == req.TenantID &&
				session.FileName == req.FileName &&
				session.ParentDriveID == req.ParentDriveID &&
				session.GoogleUploadURL == expectedUploadInfo.UploadURL &&
				session.Status == model.UploadSessionStatusPending &&
				session.SessionToken != ""
		})).
		Return(nil).
		Once()

	// Execute
	result, err := service.CreateUploadSession(ctx, req)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.SessionToken)
	assert.Equal(t, result.SessionToken, result.UploadURL) // Should be same for compatibility
	assert.Equal(t, req.FileName, result.FileName)
	assert.Equal(t, string(model.UploadSessionStatusPending), result.Status)
	assert.False(t, result.ExpiresAt.IsZero())

	mockDriveClient.AssertExpectations(t)
	mockUploadSessionRepo.AssertExpectations(t)
}

func TestCreateUploadSession_ValidationError(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	tests := []struct {
		name    string
		request *CreateUploadSessionRequest
		errMsg  string
	}{
		{
			name:    "Nil request",
			request: nil,
			errMsg:  "request cannot be nil",
		},
		{
			name: "Missing tenant ID",
			request: &CreateUploadSessionRequest{
				TenantID:      0,
				FileName:      "test.pdf",
				ParentDriveID: "folder123",
			},
			errMsg: "tenant_id is required",
		},
		{
			name: "Missing file name",
			request: &CreateUploadSessionRequest{
				TenantID:      1,
				FileName:      "",
				ParentDriveID: "folder123",
			},
			errMsg: "file_name is required",
		},
		{
			name: "Missing parent drive ID",
			request: &CreateUploadSessionRequest{
				TenantID:      1,
				FileName:      "test.pdf",
				ParentDriveID: "",
			},
			errMsg: "parent_drive_id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.CreateUploadSession(ctx, tt.request)

			assert.Nil(t, result)
			assert.Error(t, err)

			docErr, ok := err.(*DocumentServiceError)
			assert.True(t, ok)
			assert.Equal(t, ErrCodeInvalidRequest, docErr.Code)
			assert.Contains(t, docErr.Message, tt.errMsg)
		})
	}
}

func TestCreateUploadSession_DriveAPIError(t *testing.T) {
	service, _, _, _, mockDriveClient := setupDocumentServiceMocks(t)
	ctx := context.Background()

	req := &CreateUploadSessionRequest{
		TenantID:      1,
		FileName:      "test-document.pdf",
		ParentDriveID: "folder123",
	}

	// Mock expectations
	mockDriveClient.EXPECT().
		CreateResumableUploadURL(req.FileName, req.ParentDriveID, int64(0)).
		Return(nil, errors.New("Google Drive API error")).
		Once()

	// Execute
	result, err := service.CreateUploadSession(ctx, req)

	// Verify
	assert.Nil(t, result)
	assert.Error(t, err)

	docErr, ok := err.(*DocumentServiceError)
	assert.True(t, ok)
	assert.Equal(t, ErrCodeDriveAPIError, docErr.Code)
	assert.Contains(t, docErr.Message, "Failed to generate upload URL")

	mockDriveClient.AssertExpectations(t)
}

func TestGenerateSessionToken_GloballyUnique(t *testing.T) {
	// Generate multiple tokens to verify uniqueness
	tokens := make(map[string]bool)
	numTokens := 1000

	for i := 0; i < numTokens; i++ {
		token, err := generateSessionToken()
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Verify token format
		assert.True(t, strings.HasPrefix(token, "upload_"))
		assert.True(t, len(token) > 50) // Should be long enough for global uniqueness

		// Verify uniqueness
		assert.False(t, tokens[token], "Token should be unique: %s", token)
		tokens[token] = true
	}

	// All tokens should be unique
	assert.Equal(t, numTokens, len(tokens))
}

func TestGetUploadSession_Success(t *testing.T) {
	service, _, _, mockUploadSessionRepo, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	sessionToken := "upload_test_token_123"
	expectedSession := &model.UploadSession{
		SessionToken:    sessionToken,
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}

	// Mock expectations
	mockUploadSessionRepo.EXPECT().
		GetByToken(ctx, sessionToken).
		Return(expectedSession, nil).
		Once()

	// Execute
	result, err := service.GetUploadSession(ctx, sessionToken)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, sessionToken, result.SessionToken)
	assert.Equal(t, expectedSession.FileName, result.FileName)
	assert.Equal(t, string(expectedSession.Status), result.Status)
	assert.Equal(t, expectedSession.GoogleUploadURL, result.GoogleUploadURL)

	mockUploadSessionRepo.AssertExpectations(t)
}

func TestGetUploadSession_NotFound(t *testing.T) {
	service, _, _, mockUploadSessionRepo, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	sessionToken := "non_existent_token"

	// Mock expectations
	mockUploadSessionRepo.EXPECT().
		GetByToken(ctx, sessionToken).
		Return(nil, errors.New("upload session not found")).
		Once()

	// Execute
	result, err := service.GetUploadSession(ctx, sessionToken)

	// Verify
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "Upload session not found")

	mockUploadSessionRepo.AssertExpectations(t)
}

func TestGetUploadSession_EmptyToken(t *testing.T) {
	service, _, _, _, _ := setupDocumentServiceMocks(t)
	ctx := context.Background()

	// Execute with empty token
	result, err := service.GetUploadSession(ctx, "")

	// Verify
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "Session token is required")
}
