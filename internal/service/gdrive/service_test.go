package gdrive_test

import (
	"context"
	"errors"
	"testing"

	"bilabl/docman/internal/service/gdrive"
	gdriveSvc "bilabl/docman/pkg/gdrive"

	mock_gdrive "bilabl/docman/mocks/gdrive"
	mock_repositories "bilabl/docman/mocks/repositories"

	mockEntity "code.mybil.net/gophers/gokit/mocks/code.mybil.net/gophers/gokit/components/entity"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
)

func TestService_TestSetup(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name          string
		urlOrID       string
		setupMocks    func(mockClient *mock_gdrive.MockDriveClient)
		expectedInfo  *gdriveSvc.DriveInfo
		expectedError bool
	}{
		{
			name:    "Success - Shared Drive",
			urlOrID: "shared-drive-id",
			setupMocks: func(mockClient *mock_gdrive.MockDriveClient) {
				mockClient.On("ParseSharedURL", "shared-drive-id").Return(&gdriveSvc.DriveInfo{
					ID:   "shared-drive-id",
					Name: "Test Shared Drive",
					Type: gdriveSvc.ResourceTypeSharedDrive,
				}, nil).Once()
				opts := &gdriveSvc.PaginationOptions{PageSize: 1}
				mockResult := &gdriveSvc.PaginatedResult{Files: []*drive.File{}}
				mockClient.On("ListFilesInSharedDrive", "shared-drive-id", opts).Return(mockResult, nil).Once()
			},
			expectedInfo: &gdriveSvc.DriveInfo{
				ID:   "shared-drive-id",
				Name: "Test Shared Drive",
				Type: gdriveSvc.ResourceTypeSharedDrive,
			},
			expectedError: false,
		},
		{
			name:    "Error - Parse URL Fails",
			urlOrID: "invalid-id",
			setupMocks: func(mockClient *mock_gdrive.MockDriveClient) {
				mockClient.On("ParseSharedURL", "invalid-id").Return(nil, errors.New("parsing error")).Once()
			},
			expectedError: true,
		},
		{
			name:    "Error - No Permission to List Data",
			urlOrID: "no-permission-id",
			setupMocks: func(mockClient *mock_gdrive.MockDriveClient) {
				mockClient.On("ParseSharedURL", "no-permission-id").Return(&gdriveSvc.DriveInfo{
					ID:   "no-permission-id",
					Type: gdriveSvc.ResourceTypeFolder,
				}, nil).Once()
				opts := &gdriveSvc.PaginationOptions{PageSize: 1}
				mockClient.On("ListFilesInFolder", "no-permission-id", opts).Return(nil, errors.New("permission denied")).Once()
			},
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockGdriveClient := mock_gdrive.NewMockDriveClient(t)
			tc.setupMocks(mockGdriveClient)

			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)
			svc := gdrive.New(mockGdriveClient, nil, nil, nil, mockEntityFetcher)
			info, err := svc.TestSetup(ctx, tc.urlOrID)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedInfo, info)
			}
			mockGdriveClient.AssertExpectations(t)
		})
	}
}

func TestService_CompleteSetup(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name          string
		request       *gdrive.CompleteSetupRequest
		setupMocks    func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository)
		expectedError bool
	}{
		{
			name: "Success - Shared Drive - Enabled",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "valid-id",
				TenantID: 1,
				Enabled:  true,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				driveInfo := &gdriveSvc.DriveInfo{
					ID:   "drive-id-123",
					Name: "Test Drive",
					Type: gdriveSvc.ResourceTypeSharedDrive,
				}
				mockGdrive.On("ParseSharedURL", "valid-id").Return(driveInfo, nil).Once()
				// Mock the access test for shared drive
				opts := &gdriveSvc.PaginationOptions{PageSize: 1}
				mockResult := &gdriveSvc.PaginatedResult{Files: []*drive.File{}}
				mockGdrive.On("ListFilesInSharedDrive", "drive-id-123", opts).Return(mockResult, nil).Once()
				// Expect only 1 call now for the JSON config
				mockRepo.On("CreateOrUpdate", ctx, mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
		},
		{
			name: "Success - Folder in Shared Drive - Disabled",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "folder-id",
				TenantID: 1,
				Enabled:  false,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				driveInfo := &gdriveSvc.DriveInfo{
					ID:      "folder-id-456",
					Name:    "Test Folder",
					Type:    gdriveSvc.ResourceTypeFolder,
					DriveID: "parent-drive-id",
				}
				mockGdrive.On("ParseSharedURL", "folder-id").Return(driveInfo, nil).Once()
				// Expect only 1 call now for the JSON config
				mockRepo.On("CreateOrUpdate", ctx, mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
		},
		{
			name: "Success - Folder in My Drive - Enabled",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "my-folder-id",
				TenantID: 1,
				Enabled:  true,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				driveInfo := &gdriveSvc.DriveInfo{
					ID:      "my-folder-id-789",
					Name:    "My Folder",
					Type:    gdriveSvc.ResourceTypeFolder,
					DriveID: "", // Empty DriveID means it's in My Drive
				}
				mockGdrive.On("ParseSharedURL", "my-folder-id").Return(driveInfo, nil).Once()
				// Mock the access test for folder
				opts := &gdriveSvc.PaginationOptions{PageSize: 1}
				mockResult := &gdriveSvc.PaginatedResult{Files: []*drive.File{}}
				mockGdrive.On("ListFilesInFolder", "my-folder-id-789", opts).Return(mockResult, nil).Once()
				// Expect only 1 call now for the JSON config
				mockRepo.On("CreateOrUpdate", ctx, mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
		},
		{
			name: "Error - Parse URL Fails",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "invalid-id",
				TenantID: 1,
				Enabled:  true,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				mockGdrive.On("ParseSharedURL", "invalid-id").Return(nil, errors.New("parsing error")).Once()
			},
			expectedError: true,
		},
		{
			name: "Error - DB Fails",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "valid-id",
				TenantID: 1,
				Enabled:  true,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				driveInfo := &gdriveSvc.DriveInfo{ID: "drive-id-123", Name: "Test Drive", Type: gdriveSvc.ResourceTypeSharedDrive}
				mockGdrive.On("ParseSharedURL", "valid-id").Return(driveInfo, nil).Once()
				// Mock the access test for shared drive
				opts := &gdriveSvc.PaginationOptions{PageSize: 1}
				mockResult := &gdriveSvc.PaginatedResult{Files: []*drive.File{}}
				mockGdrive.On("ListFilesInSharedDrive", "drive-id-123", opts).Return(mockResult, nil).Once()
				mockRepo.On("CreateOrUpdate", ctx, mock.Anything, mock.Anything).Return(errors.New("db error")).Once()
			},
			expectedError: true,
		},
		{
			name: "Error - Zero Tenant ID",
			request: &gdrive.CompleteSetupRequest{
				URLOrID:  "valid-id",
				TenantID: 0,
				Enabled:  true,
			},
			setupMocks: func(mockGdrive *mock_gdrive.MockDriveClient, mockRepo *mock_repositories.MockDocumentSettingRepository) {
				driveInfo := &gdriveSvc.DriveInfo{ID: "drive-id-123", Name: "Test Drive", Type: gdriveSvc.ResourceTypeSharedDrive}
				mockGdrive.On("ParseSharedURL", "valid-id").Return(driveInfo, nil).Once()
			},
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockGdriveClient := mock_gdrive.NewMockDriveClient(t)
			mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
			tc.setupMocks(mockGdriveClient, mockDocSettingRepo)

			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)
			svc := gdrive.New(mockGdriveClient, mockDocSettingRepo, nil, nil, mockEntityFetcher)
			response, err := svc.CompleteSetup(ctx, tc.request)

			if tc.expectedError {
				assert.Error(t, err)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotNil(t, response.AppliedPathConfig)
			}
			mockGdriveClient.AssertExpectations(t)
			mockDocSettingRepo.AssertExpectations(t)
		})
	}
}

// Test SetPermissionSyncer functionality
func TestService_SetPermissionSyncer(t *testing.T) {
	// Create mock permission syncer
	mockPermissionSyncer := &MockPermissionSyncer{}

	// Create service
	mockGdriveClient := mock_gdrive.NewMockDriveClient(t)
	mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)
	service := gdrive.New(mockGdriveClient, nil, nil, nil, mockEntityFetcher)

	// Test SetPermissionSyncer
	service.SetPermissionSyncer(mockPermissionSyncer)

	// This test mainly verifies that the method doesn't panic
	// and can be called successfully. The actual functionality
	// is tested in the event consumer tests.
	assert.NotNil(t, service)
}

// Test getter methods
func TestService_GetterMethods(t *testing.T) {
	// Create service
	mockGdriveClient := mock_gdrive.NewMockDriveClient(t)
	mockDocSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
	mockDocMappingRepo := mock_repositories.NewMockDocumentMappingRepository(t)
	mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)

	service := gdrive.New(mockGdriveClient, mockDocSettingRepo, mockDocMappingRepo, nil, mockEntityFetcher)

	t.Run("GetDocumentSettingRepo", func(t *testing.T) {
		repo := service.GetDocumentSettingRepo()
		assert.NotNil(t, repo)
		assert.Equal(t, mockDocSettingRepo, repo)
	})

	t.Run("GetDocumentMappingRepo", func(t *testing.T) {
		repo := service.GetDocumentMappingRepo()
		assert.NotNil(t, repo)
		assert.Equal(t, mockDocMappingRepo, repo)
	})

	t.Run("GetMatterEventConsumer", func(t *testing.T) {
		consumer := service.GetMatterEventConsumer()
		assert.NotNil(t, consumer)
	})

	t.Run("GetClientEventConsumer", func(t *testing.T) {
		consumer := service.GetClientEventConsumer()
		assert.NotNil(t, consumer)
	})

	t.Run("GetFolderService", func(t *testing.T) {
		folderSvc := service.GetFolderService()
		assert.NotNil(t, folderSvc)
	})
}
