package gdrive_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/mocks/repositories"
	mockGdrive "bilabl/docman/mocks/service/gdrive"

	"code.mybil.net/gophers/gokit/components/entity"
	mockEntity "code.mybil.net/gophers/gokit/mocks/code.mybil.net/gophers/gokit/components/entity"
)

func TestMatterEventConsumer_HandleMatterCreated(t *testing.T) {
	tests := []struct {
		name          string
		payload       string
		setupMocks    func(*repositories.MockDocumentSettingRepository, *mockGdrive.MockFolderService, *repositories.MockDocumentMappingRepository, *mockEntity.MockGenericFetcher)
		expectedError bool
		expectLog     string
	}{
		{
			name: "Success_CreateNewMatterFolder",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch
				mockClient := &entity.Generic{
					ID:        123,
					Name:      "Test Client",
					ShortName: "TC",
					Code:      "TC001",
				}
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(mockClient, nil)

				// Mock client folder creation
				mockFolderService.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == 1 && req.ClientID == 123
				})).Return(&gdrive.CreateClientFolderResponse{
					FolderID:        "client-folder-id",
					FolderName:      "Test Client - TC001",
					WebViewLink:     "https://drive.google.com/folders/client-folder-id",
					DocumentMapping: &model.DocumentMapping{DriveID: "client-folder-id"},
				}, nil)

				// Mock CreateMatterFolder call with config passed
				mockFolderService.On("CreateMatterFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateMatterFolderRequest) bool {
					return req.TenantID == 1 &&
						req.ClientID == 123 &&
						req.MatterID == 789 &&
						req.MatterName == "Test Matter" &&
						req.MatterCode == "2024-001" &&
						req.Config != nil &&
						req.Config.Enabled == true
				})).Return(&gdrive.CreateMatterFolderResponse{
					FolderID:    "test-folder-id",
					FolderName:  "Test Matter - 2024-001",
					WebViewLink: "https://drive.google.com/folders/test-folder-id",
				}, nil)
			},
			expectedError: false,
		},
		{
			name: "Success_GoogleDriveDisabled",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with disabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					Key:   model.KeyGdriveConfig,
					Value: `{"enabled":false,"root_id":"root-folder-id","resource_type":"folder"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(mockDocSetting, nil)
				// FolderService should not be called when disabled
			},
			expectedError: false,
		},
		{
			name: "Error_InvalidJSON",
			payload: `{
				"topic": "matter.create",
				"body": invalid json
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// No mocks needed as it should fail during JSON parsing
			},
			expectedError: true,
		},
		{
			name: "Error_MissingBody",
			payload: `{
				"topic": "matter.create",
				"body": null
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// No mocks needed as it should fail during validation
			},
			expectedError: true,
		},
		{
			name: "Error_ConfigMissing",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock config retrieval failure
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(nil, errors.New("config not found"))
			},
			expectedError: true,
		},
		{
			name: "Error_FolderCreationFailed",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch
				mockClient := &entity.Generic{
					ID:        123,
					Name:      "Test Client",
					ShortName: "TC",
					Code:      "TC001",
				}
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(mockClient, nil)

				// Mock client folder creation failure
				mockFolderService.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == 1 && req.ClientID == 123
				})).Return(nil, errors.New("client folder creation failed"))
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocSettingRepo := repositories.NewMockDocumentSettingRepository(t)
			mockFolderService := mockGdrive.NewMockFolderService(t)
			mockDocMappingRepo := repositories.NewMockDocumentMappingRepository(t)
			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)

			tt.setupMocks(mockDocSettingRepo, mockFolderService, mockDocMappingRepo, mockEntityFetcher)

			// Create consumer
			consumer := gdrive.NewMatterEventConsumer(mockFolderService, mockDocSettingRepo, mockDocMappingRepo, mockEntityFetcher, nil)

			// Execute
			ctx := context.Background()
			err := consumer.HandleMatterCreated(ctx, tt.payload)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			mockDocSettingRepo.AssertExpectations(t)
			mockFolderService.AssertExpectations(t)
			mockDocMappingRepo.AssertExpectations(t)
		})
	}
}

func TestMatterEventConsumer_HandleMatterUpdated(t *testing.T) {
	tests := []struct {
		name          string
		payload       string
		setupMocks    func(*repositories.MockDocumentSettingRepository, *mockGdrive.MockFolderService, *repositories.MockDocumentMappingRepository, *mockEntity.MockGenericFetcher)
		expectedError bool
	}{
		{
			name: "Success_GoogleDriveEnabled",
			payload: `{
				"topic": "matter.update",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Updated Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					Key:   model.KeyGdriveConfig,
					Value: `{"enabled":true,"root_id":"root-folder-id","resource_type":"folder"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(mockDocSetting, nil)

				// Mock matter folder check - already exists
				mockDocMappingRepo.On("FirstObjectMapping",
					mock.Anything,
					model.DocTypeMatter,
					model.DocProviderGoogle,
					uint64(1),
					uint64(789),
					uint64(123),
				).Return(&model.DocumentMapping{
					DriveID: "matter-folder-id",
				}, nil)

				// Mock RenameMatterFolder call
				mockFolderService.On("RenameMatterFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.RenameMatterFolderRequest) bool {
					return req.TenantID == 1 &&
						req.MatterID == 789 &&
						req.ClientID == 123 &&
						req.MatterName == "Updated Matter" &&
						req.MatterCode == "2024-001" &&
						req.Config != nil &&
						req.Config.Enabled == true
				})).Return(&gdrive.RenameMatterFolderResponse{
					FolderID:    "matter-folder-id",
					OldName:     "Test Matter - 2024-001",
					NewName:     "Updated Matter - 2024-001",
					WebViewLink: "https://drive.google.com/folders/matter-folder-id",
				}, nil)
			},
			expectedError: false,
		},
		{
			name: "Success_GoogleDriveDisabled",
			payload: `{
				"topic": "matter.update",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Updated Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with disabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					Key:   model.KeyGdriveConfig,
					Value: `{"enabled":false,"root_id":"root-folder-id","resource_type":"folder"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(mockDocSetting, nil)
			},
			expectedError: false,
		},
		{
			name: "Error_InvalidJSON",
			payload: `{
				"topic": "matter.update",
				"body": invalid json
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// No mocks needed as it should fail during JSON parsing
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocSettingRepo := repositories.NewMockDocumentSettingRepository(t)
			mockFolderService := mockGdrive.NewMockFolderService(t)
			mockDocMappingRepo := repositories.NewMockDocumentMappingRepository(t)
			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)

			tt.setupMocks(mockDocSettingRepo, mockFolderService, mockDocMappingRepo, mockEntityFetcher)

			// Create consumer
			consumer := gdrive.NewMatterEventConsumer(mockFolderService, mockDocSettingRepo, mockDocMappingRepo, mockEntityFetcher, nil)

			// Execute
			ctx := context.Background()
			err := consumer.HandleMatterUpdated(ctx, tt.payload)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			mockDocSettingRepo.AssertExpectations(t)
			mockFolderService.AssertExpectations(t)
			mockDocMappingRepo.AssertExpectations(t)
		})
	}
}

func TestMatterEventConsumer_HandleMatterCreated_WithPermissionSync(t *testing.T) {
	tests := []struct {
		name          string
		payload       string
		setupMocks    func(*repositories.MockDocumentSettingRepository, *mockGdrive.MockFolderService, *repositories.MockDocumentMappingRepository, *mockEntity.MockGenericFetcher, *MockPermissionSyncer)
		expectedError bool
	}{
		{
			name: "Success_WithPermissionSync",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false,
					"owner_users": [
						{"email": "<EMAIL>"},
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher, permSyncer *MockPermissionSyncer) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch
				mockClient := &entity.Generic{
					ID:        123,
					Name:      "Test Client",
					ShortName: "TC",
					Code:      "TC001",
				}
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(mockClient, nil)

				// Mock client folder creation
				mockFolderService.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == 1 && req.ClientID == 123
				})).Return(&gdrive.CreateClientFolderResponse{
					FolderID:        "client-folder-id",
					FolderName:      "Test Client - TC001",
					WebViewLink:     "https://drive.google.com/folders/client-folder-id",
					DocumentMapping: &model.DocumentMapping{DriveID: "client-folder-id"},
				}, nil)

				// Mock CreateMatterFolder call
				mockFolderService.On("CreateMatterFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateMatterFolderRequest) bool {
					return req.TenantID == 1 &&
						req.ClientID == 123 &&
						req.MatterID == 789 &&
						req.MatterName == "Test Matter" &&
						req.MatterCode == "2024-001" &&
						req.Config != nil &&
						req.Config.Enabled == true
				})).Return(&gdrive.CreateMatterFolderResponse{
					FolderID:    "test-folder-id",
					FolderName:  "Test Matter - 2024-001",
					WebViewLink: "https://drive.google.com/folders/test-folder-id",
				}, nil)

				// Mock permission sync
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"matter",
					uint64(789),
					[]string{"<EMAIL>", "<EMAIL>"}).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "Success_WithPermissionSyncFailure_DoesNotFailOperation",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false,
					"owner_users": [
						{"email": "<EMAIL>"}
					]
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher, permSyncer *MockPermissionSyncer) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch
				mockClient := &entity.Generic{
					ID:        123,
					Name:      "Test Client",
					ShortName: "TC",
					Code:      "TC001",
				}
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(mockClient, nil)

				// Mock client folder creation
				mockFolderService.On("CreateClientFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateClientFolderRequest) bool {
					return req.TenantID == 1 && req.ClientID == 123
				})).Return(&gdrive.CreateClientFolderResponse{
					FolderID:        "client-folder-id",
					FolderName:      "Test Client - TC001",
					WebViewLink:     "https://drive.google.com/folders/client-folder-id",
					DocumentMapping: &model.DocumentMapping{DriveID: "client-folder-id"},
				}, nil)

				// Mock CreateMatterFolder call
				mockFolderService.On("CreateMatterFolder", mock.Anything, mock.Anything).Return(&gdrive.CreateMatterFolderResponse{
					FolderID:    "test-folder-id",
					FolderName:  "Test Matter - 2024-001",
					WebViewLink: "https://drive.google.com/folders/test-folder-id",
				}, nil)

				// Mock permission sync failure
				permSyncer.On("SyncGoogleDrivePermissions",
					mock.Anything,
					uint64(1),
					"matter",
					uint64(789),
					[]string{"<EMAIL>"}).Return(errors.New("permission sync failed"))
			},
			expectedError: false, // Should not fail the entire operation
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocSettingRepo := repositories.NewMockDocumentSettingRepository(t)
			mockFolderService := mockGdrive.NewMockFolderService(t)
			mockDocMappingRepo := repositories.NewMockDocumentMappingRepository(t)
			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)
			permSyncer := &MockPermissionSyncer{}

			tt.setupMocks(mockDocSettingRepo, mockFolderService, mockDocMappingRepo, mockEntityFetcher, permSyncer)

			// Create consumer with permission syncer
			consumer := gdrive.NewMatterEventConsumer(mockFolderService, mockDocSettingRepo, mockDocMappingRepo, mockEntityFetcher, permSyncer)

			// Execute
			ctx := context.Background()
			err := consumer.HandleMatterCreated(ctx, tt.payload)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			mockDocSettingRepo.AssertExpectations(t)
			mockFolderService.AssertExpectations(t)
			mockDocMappingRepo.AssertExpectations(t)
			permSyncer.AssertExpectations(t)
		})
	}
}

func TestMatterEventConsumer_HandleMatterCreated_EdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		payload       string
		setupMocks    func(*repositories.MockDocumentSettingRepository, *mockGdrive.MockFolderService, *repositories.MockDocumentMappingRepository, *mockEntity.MockGenericFetcher)
		expectedError bool
	}{
		{
			name: "Error_InvalidPayloadFields",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 0,
					"id": 0,
					"client_id": 0,
					"name": "Test Matter",
					"code": "2024-001"
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// No mocks needed as it should fail during validation
			},
			expectedError: true,
		},
		{
			name: "Error_ClientFolderExistsButMatterCreationFails",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - exists
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{DriveID: "client-folder-id"}, nil)

				// Mock CreateMatterFolder call failure
				mockFolderService.On("CreateMatterFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.CreateMatterFolderRequest) bool {
					return req.TenantID == 1 &&
						req.ClientID == 123 &&
						req.MatterID == 789
				})).Return(nil, errors.New("matter folder creation failed"))
			},
			expectedError: true,
		},
		{
			name: "Error_ClientEntityFetchFails",
			payload: `{
				"topic": "matter.create",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Test Matter",
					"code": "2024-001",
					"actor_id": 456,
					"no_notify": false
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					TenantID: 1,
					Key:      "gdrive_config",
					Value:    `{"enabled": true, "root_id": "test-root"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").
					Return(mockDocSetting, nil)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch failure
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(nil, errors.New("client entity fetch failed"))
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocSettingRepo := repositories.NewMockDocumentSettingRepository(t)
			mockFolderService := mockGdrive.NewMockFolderService(t)
			mockDocMappingRepo := repositories.NewMockDocumentMappingRepository(t)
			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)

			tt.setupMocks(mockDocSettingRepo, mockFolderService, mockDocMappingRepo, mockEntityFetcher)

			// Create consumer
			consumer := gdrive.NewMatterEventConsumer(mockFolderService, mockDocSettingRepo, mockDocMappingRepo, mockEntityFetcher, nil)

			// Execute
			ctx := context.Background()
			err := consumer.HandleMatterCreated(ctx, tt.payload)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			mockDocSettingRepo.AssertExpectations(t)
			mockFolderService.AssertExpectations(t)
			mockDocMappingRepo.AssertExpectations(t)
		})
	}
}

func TestMatterEventConsumer_HandleMatterUpdated_EdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		payload       string
		setupMocks    func(*repositories.MockDocumentSettingRepository, *mockGdrive.MockFolderService, *repositories.MockDocumentMappingRepository, *mockEntity.MockGenericFetcher)
		expectedError bool
	}{
		{
			name: "Error_WrongEventTopic",
			payload: `{
				"topic": "matter.wrong",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Updated Matter",
					"code": "2024-001"
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// No mocks needed as it should return early
			},
			expectedError: false, // Should return nil for wrong topic
		},
		{
			name: "Error_MatterFolderNotFoundAndCreationFails",
			payload: `{
				"topic": "matter.update",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Updated Matter",
					"code": "2024-001"
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					Key:   model.KeyGdriveConfig,
					Value: `{"enabled":true,"root_id":"root-folder-id","resource_type":"folder"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(mockDocSetting, nil)

				// Mock matter folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping",
					mock.Anything,
					model.DocTypeMatter,
					model.DocProviderGoogle,
					uint64(1),
					uint64(789),
					uint64(123),
				).Return(nil, gorm.ErrRecordNotFound)

				// Mock client folder check - not found
				mockDocMappingRepo.On("FirstObjectMapping", mock.Anything, model.DocTypeClient, model.DocProviderGoogle, uint64(1), uint64(123), uint64(0)).
					Return(nil, gorm.ErrRecordNotFound)

				// Mock client entity fetch failure
				mockEntityFetcher.On("FetchCache", mock.Anything, entity.KindClient, entity.ID(123)).
					Return(nil, errors.New("client entity fetch failed"))
			},
			expectedError: true,
		},
		{
			name: "Error_RenameMatterFolderFails",
			payload: `{
				"topic": "matter.update",
				"body": {
					"tenant_id": 1,
					"id": 789,
					"client_id": 123,
					"name": "Updated Matter",
					"code": "2024-001"
				}
			}`,
			setupMocks: func(mockDocSettingRepo *repositories.MockDocumentSettingRepository, mockFolderService *mockGdrive.MockFolderService, mockDocMappingRepo *repositories.MockDocumentMappingRepository, mockEntityFetcher *mockEntity.MockGenericFetcher) {
				// Mock DocumentSetting with enabled Google Drive config
				mockDocSetting := &model.DocumentSetting{
					Key:   model.KeyGdriveConfig,
					Value: `{"enabled":true,"root_id":"root-folder-id","resource_type":"folder"}`,
				}
				mockDocSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), "gdrive_config").Return(mockDocSetting, nil)

				// Mock matter folder check - exists
				mockDocMappingRepo.On("FirstObjectMapping",
					mock.Anything,
					model.DocTypeMatter,
					model.DocProviderGoogle,
					uint64(1),
					uint64(789),
					uint64(123),
				).Return(&model.DocumentMapping{
					DriveID: "matter-folder-id",
				}, nil)

				// Mock RenameMatterFolder call failure
				mockFolderService.On("RenameMatterFolder", mock.Anything, mock.MatchedBy(func(req *gdrive.RenameMatterFolderRequest) bool {
					return req.TenantID == 1 &&
						req.MatterID == 789 &&
						req.ClientID == 123 &&
						req.MatterName == "Updated Matter" &&
						req.MatterCode == "2024-001"
				})).Return(nil, errors.New("rename failed"))
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocSettingRepo := repositories.NewMockDocumentSettingRepository(t)
			mockFolderService := mockGdrive.NewMockFolderService(t)
			mockDocMappingRepo := repositories.NewMockDocumentMappingRepository(t)
			mockEntityFetcher := mockEntity.NewMockGenericFetcher(t)

			tt.setupMocks(mockDocSettingRepo, mockFolderService, mockDocMappingRepo, mockEntityFetcher)

			// Create consumer
			consumer := gdrive.NewMatterEventConsumer(mockFolderService, mockDocSettingRepo, mockDocMappingRepo, mockEntityFetcher, nil)

			// Execute
			ctx := context.Background()
			err := consumer.HandleMatterUpdated(ctx, tt.payload)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			mockDocSettingRepo.AssertExpectations(t)
			mockFolderService.AssertExpectations(t)
			mockDocMappingRepo.AssertExpectations(t)
		})
	}
}
