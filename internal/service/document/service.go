package document

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/transport"

	"github.com/lib/pq"
)

type documentService struct {
	rDocument repositories.DocumentRepository
	glob      transport.Glob
}

// NewDocumentService creates a new document service
func NewDocumentService(
	rDocument repositories.DocumentRepository,
	glob transport.Glob,
) DocumentService {
	return &documentService{
		rDocument: rDocument,
		glob:      glob,
	}
}

// CreateDocument handles all business logic for document creation
func (s *documentService) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*CreateDocumentResponse, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Validate doc_type is valid
	if req.DocType != model.DocTypeDir && req.DocType != model.DocTypeFile {
		log.Error("invalid doc_type, must be 1 (folder) or 2 (file)")
		return nil, fmt.Errorf("doc_type must be 1 (folder) or 2 (file)")
	}

	// Validate key requirement for files
	if req.DocType == model.DocTypeFile && strings.TrimSpace(req.Key) == "" {
		log.Error("key is required when creating file (doc_type=2)")
		return nil, fmt.Errorf("key is required when creating file")
	}

	// Handle AutoDocRoot creation
	if req.AutoDocRoot {
		if err := s.handleAutoDocRoot(ctx, req); err != nil {
			return nil, err
		}
	}

	// Handle parent path resolution (must be done before other operations)
	if req.ParentPath != "" {
		parentID, err := s.resolveParentPath(ctx, req)
		if err != nil {
			return nil, err
		}
		req.ParentID = parentID
	}

	// Resolve parent document and determine object type/id
	objectType, objectID, parentID, err := s.resolveParentDocument(ctx, req)
	if err != nil {
		return nil, err
	}

	// Validate duplicate name
	if err := s.validateDuplicateName(ctx, req, parentID); err != nil {
		return nil, err
	}

	// Calculate file size
	size, err := s.calculateFileSize(ctx, req)
	if err != nil {
		return nil, err
	}

	// Create document model
	document := &model.Document{
		ParentID:    parentID,
		Name:        req.Name,
		TenantID:    req.TenantID,
		ObjectType:  objectType,
		ObjectID:    objectID,
		SubObjectID: req.SubObjectID,
		WaiverWith:  pq.Int64Array(req.WaiverWith),
		Key:         req.Key,
		Note:        req.Note,
		CreatedUser: req.UserID,
		UpdatedUser: req.UserID,
		Status:      req.Status,
		Type:        req.Type,
		DocType:     req.DocType,
		Model: model.Model{
			CreatedAt: time.Now(),
		},
		Size: size,
	}

	// Save document to database
	if err := s.rDocument.CreateDoc(ctx, document); err != nil {
		return nil, fmt.Errorf("failed to create document: %w", err)
	}

	return &CreateDocumentResponse{
		Document: document,
	}, nil
}

// handleAutoDocRoot handles AutoDocRoot creation logic
func (s *documentService) handleAutoDocRoot(ctx context.Context, req *CreateDocumentRequest) error {
	if req.ObjectType == 0 && req.ObjectID == 0 {
		// Ensure AutoDocRoot exists
		root, err := s.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return fmt.Errorf("failed to ensure AutoDocRoot: %w", err)
		}

		// Set ParentID to AutoDocRoot if not already set
		if req.ParentID == 0 {
			req.ParentID = root.ID
		}
	}
	return nil
}

// resolveParentPath resolves parent path to parent ID by creating folders if needed
func (s *documentService) resolveParentPath(ctx context.Context, req *CreateDocumentRequest) (uint64, error) {
	if req.ParentPath == "" {
		return req.ParentID, nil
	}

	// For AutoDoc context, ObjectType=0 and ObjectID=0 are valid
	// For other contexts, both ObjectType and ObjectID must be provided
	if req.ObjectType == 0 && req.ObjectID == 0 && req.AutoDocRoot {
		// This is AutoDoc context - valid for parent path resolution
	} else if req.ObjectType == 0 || req.ObjectID == 0 {
		// Mixed state - one is 0 but not both, this is invalid
		return 0, fmt.Errorf("object type and object id must both be provided or both be 0 (AutoDoc context)")
	}

	segments := strings.Split(req.ParentPath, "/")

	// For AutoDoc context, start from AutoDocRoot ID instead of 0
	var currentParentID uint64 = 0
	if req.AutoDocRoot {
		// Get AutoDocRoot to use as base parent for path resolution
		autoDocRoot, err := s.EnsureAutoDocRoot(ctx, req.TenantID)
		if err != nil {
			return 0, fmt.Errorf("failed to ensure AutoDocRoot for parent path resolution: %w", err)
		}
		currentParentID = autoDocRoot.ID
	}

	var lastDoc *model.Document

	for _, name := range segments {
		if name == "" {
			continue // skip empty segments
		}

		// Find existing folder
		filters := []*model.Filter{
			model.NewFilterE("tenant_id", req.TenantID),
			model.NewFilterE("object_type", req.ObjectType),
			model.NewFilterE("object_id", req.ObjectID),
			model.NewFilterE("name", name),
			model.NewFilterE("parent_id", currentParentID),
		}
		queryBuild := helper.BuildQuery("", filters, nil, nil)
		existingDoc, err := s.rDocument.FindOne(ctx, queryBuild)

		if err != nil {
			// Check if it's a "not found" error (either GORM or ginext)
			isNotFound := model.IsNotFound(err)
			if !isNotFound {
				// Check if error message contains "document not found" (from repository)
				errorMsg := err.Error()
				if strings.Contains(errorMsg, "document not found") {
					isNotFound = true
				}
			}

			// Only create folder if record not found, otherwise return error
			if !isNotFound {
				return 0, fmt.Errorf("failed to find folder '%s': %w", name, err)
			}
			// Create new folder (record not found)
			newFolder := &model.Document{
				ParentID:    currentParentID,
				ObjectType:  req.ObjectType,
				ObjectID:    req.ObjectID,
				DocType:     model.DocTypeDir,
				Name:        name,
				CreatedUser: req.UserID,
				UpdatedUser: req.UserID,
				TenantID:    req.TenantID,
				Model: model.Model{
					CreatedAt: time.Now(),
				},
			}
			if err := s.rDocument.CreateDoc(ctx, newFolder); err != nil {
				return 0, fmt.Errorf("failed to create folder '%s': %w", name, err)
			}
			// Validate created folder has valid ID
			if newFolder.ID == 0 {
				return 0, fmt.Errorf("created folder '%s' has invalid ID", name)
			}
			lastDoc = newFolder
			currentParentID = newFolder.ID
		} else if existingDoc != nil {
			// Found existing folder
			lastDoc = existingDoc
			currentParentID = existingDoc.ID
		} else {
			// This shouldn't happen (no error but nil doc), but handle gracefully
			return 0, fmt.Errorf("unexpected state: no error but folder '%s' not found", name)
		}
	}

	if lastDoc == nil {
		return req.ParentID, nil
	}
	return lastDoc.ID, nil
}

// resolveParentDocument resolves parent document and determines object type/id
func (s *documentService) resolveParentDocument(ctx context.Context, req *CreateDocumentRequest) (int, uint64, uint64, error) {
	var objectType int
	var objectID uint64
	var parentID uint64

	if req.ParentID != 0 {
		filters := []*model.Filter{
			{
				Key:    "id",
				Value:  req.ParentID,
				Method: "=",
			},
			{
				Key:    "tenant_id",
				Value:  req.TenantID,
				Method: "=",
			},
		}
		queryBuild := helper.BuildQuery("", filters, nil, nil)
		doc, err := s.rDocument.FindOne(ctx, queryBuild)
		if err != nil {
			return 0, 0, 0, fmt.Errorf("failed to find parent document: %w", err)
		}

		if doc.SubObjectID != 0 {
			objectType = 3
			objectID = doc.SubObjectID
			parentID = 0
		} else {
			objectType = doc.ObjectType
			objectID = doc.ObjectID
			parentID = doc.ID
		}
	} else {
		objectType = req.ObjectType
		objectID = req.ObjectID
		parentID = req.ParentID
	}

	return objectType, objectID, parentID, nil
}

// validateDuplicateName validates that no duplicate name exists
func (s *documentService) validateDuplicateName(ctx context.Context, req *CreateDocumentRequest, parentID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	duplicateFilters := []*model.Filter{
		{
			Key:    "name",
			Value:  req.Name,
			Method: "=",
		},
		{
			Key:    "doc_type",
			Value:  req.DocType,
			Method: "=",
		},
		{
			Key:    "parent_id",
			Value:  parentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  req.TenantID,
			Method: "=",
		},
	}
	duplicateQuery := helper.BuildQuery("", duplicateFilters, nil, nil)
	existingDoc, err := s.rDocument.FindOne(ctx, duplicateQuery)
	if err == nil && existingDoc != nil {
		docTypeStr := "folder"
		if req.DocType == model.DocTypeFile {
			docTypeStr = "file"
		}
		log.Error("duplicate name found", map[string]interface{}{
			"name":      req.Name,
			"doc_type":  req.DocType,
			"parent_id": parentID,
		})
		return fmt.Errorf("%s with name '%s' already exists in this location", docTypeStr, req.Name)
	}
	return nil
}

// calculateFileSize calculates file size from storage
func (s *documentService) calculateFileSize(ctx context.Context, req *CreateDocumentRequest) (int64, error) {
	if req.Key != "" {
		fileSize, _ := s.glob.GetObjectSize(ctx, req.Key, strconv.FormatUint(req.UserID, 10))
		return fileSize.Data.Size, nil
	}
	return 0, nil
}

// EnsureAutoDocRoot ensures AutoDocRoot folder exists for a tenant
func (s *documentService) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Search for existing AutoDocRoot
	filters := []*model.Filter{
		model.NewFilterE("tenant_id", tenantID),
		model.NewFilterE("name", model.AutoDocRootFolderName),
		model.NewFilterE("doc_type", model.DocTypeDir),
		model.NewFilterE("parent_id", 0),
	}

	query := helper.BuildQuery("", filters, nil, nil)
	existingRoot, err := s.rDocument.FindOne(ctx, query)
	if err == nil && existingRoot != nil {
		log.Debugf("Found existing AutoDocRoot tenant_id=%d document_id=%d", tenantID, existingRoot.ID)
		return existingRoot, nil
	}

	// Create AutoDocRoot if not exists
	autoDocRoot := &model.Document{
		ParentID:    0,
		Name:        model.AutoDocRootFolderName,
		TenantID:    tenantID,
		ObjectType:  0,
		ObjectID:    0,
		DocType:     model.DocTypeDir,
		CreatedUser: 1, // System user
		UpdatedUser: 1, // System user
		Status:      1, // Active
		Model: model.Model{
			CreatedAt: time.Now(),
		},
	}

	if err := s.rDocument.CreateDoc(ctx, autoDocRoot); err != nil {
		log.WithError(err).Errorf("Failed to create AutoDocRoot tenant_id=%d", tenantID)
		return nil, fmt.Errorf("failed to create AutoDocRoot: %w", err)
	}

	log.Infof("Created AutoDocRoot tenant_id=%d document_id=%d", tenantID, autoDocRoot.ID)
	return autoDocRoot, nil
}
