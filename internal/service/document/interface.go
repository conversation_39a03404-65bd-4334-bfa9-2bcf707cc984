package document

import (
	"bilabl/docman/domain/model"
	"context"
)

// CreateDocumentRequest contains all data needed to create a document
type CreateDocumentRequest struct {
	TenantID    uint64
	UserID      uint64
	Name        string
	DocType     int
	Key         string
	ParentID    uint64
	ParentPath  string
	ObjectType  int
	ObjectID    uint64
	SubObjectID uint64
	AutoDocRoot bool
	Status      int
	Type        int
	Note        string
	WaiverWith  []int64
}

// CreateDocumentResponse contains the result of document creation
type CreateDocumentResponse struct {
	Document *model.Document
}

// DocumentService handles document business logic
type DocumentService interface {
	// CreateDocument handles all business logic for document creation
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*CreateDocumentResponse, error)

	// EnsureAutoDocRoot ensures AutoDoc root folder exists for tenant
	EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error)
}
