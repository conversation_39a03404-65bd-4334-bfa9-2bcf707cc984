package template

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"strings"

	"bilabl/docman/pkg/bilabllog"

	"baliance.com/gooxml/document"
)

// TemplateProcessor handles DOCX template processing with placeholder replacement
type TemplateProcessor interface {
	// ProcessDocx processes a DOCX template with the given data context
	ProcessDocx(ctx context.Context, templateBuffer []byte, dataContext map[string]interface{}) ([]byte, error)
}

// templateProcessor implements TemplateProcessor interface
type templateProcessor struct {
	placeholderRegex *regexp.Regexp
}

// NewTemplateProcessor creates a new template processor instance
func NewTemplateProcessor() TemplateProcessor {
	return &templateProcessor{
		// Regex to match {variable} or {object.property} patterns
		placeholderRegex: regexp.MustCompile(`\{([^}]+)\}`),
	}
}

// ProcessDocx processes a DOCX template by replacing placeholders with data from context
func (tp *templateProcessor) ProcessDocx(ctx context.Context, templateBuffer []byte, dataContext map[string]interface{}) ([]byte, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// Create a reader from buffer and open document
	reader := bytes.NewReader(templateBuffer)
	doc, err := document.Read(reader, int64(len(templateBuffer)))
	if err != nil {
		log.WithError(err).Error("Failed to open DOCX template")
		return nil, fmt.Errorf("failed to open DOCX template: %w", err)
	}

	log.WithField("placeholders_found", 0).Info("Starting DOCX template processing")

	// Process all paragraphs in the document
	placeholdersReplaced := 0
	for _, para := range doc.Paragraphs() {
		placeholdersReplaced += tp.processParagraph(ctx, para, dataContext)
	}

	// Process headers
	for _, header := range doc.Headers() {
		for _, para := range header.Paragraphs() {
			placeholdersReplaced += tp.processParagraph(ctx, para, dataContext)
		}
	}

	// Process footers
	for _, footer := range doc.Footers() {
		for _, para := range footer.Paragraphs() {
			placeholdersReplaced += tp.processParagraph(ctx, para, dataContext)
		}
	}

	// Process tables
	for _, table := range doc.Tables() {
		for _, row := range table.Rows() {
			for _, cell := range row.Cells() {
				for _, para := range cell.Paragraphs() {
					placeholdersReplaced += tp.processParagraph(ctx, para, dataContext)
				}
			}
		}
	}

	log.WithField("placeholders_replaced", placeholdersReplaced).Info("Completed DOCX template processing")

	// Save the processed document to buffer
	var outputBuffer bytes.Buffer
	if err := doc.Save(&outputBuffer); err != nil {
		log.WithError(err).Error("Failed to save processed DOCX document")
		return nil, fmt.Errorf("failed to save processed DOCX document: %w", err)
	}

	return outputBuffer.Bytes(), nil
}

// processParagraph processes a single paragraph, replacing placeholders in text runs
func (tp *templateProcessor) processParagraph(ctx context.Context, para document.Paragraph, dataContext map[string]interface{}) int {
	log := bilabllog.CreateContextLogger(ctx)
	placeholdersReplaced := 0

	// Get all runs in the paragraph
	runs := para.Runs()
	if len(runs) == 0 {
		return 0
	}

	// Combine all text from runs to handle placeholders that might be split across runs
	var combinedText strings.Builder
	var runTexts []string

	for _, run := range runs {
		text := run.Text()
		runTexts = append(runTexts, text)
		combinedText.WriteString(text)
	}

	originalText := combinedText.String()

	// Find and replace placeholders
	processedText := tp.placeholderRegex.ReplaceAllStringFunc(originalText, func(match string) string {
		placeholder := tp.placeholderRegex.FindStringSubmatch(match)[1]
		value := tp.resolveValue(ctx, placeholder, dataContext)
		placeholdersReplaced++

		log.WithFields(map[string]interface{}{
			"placeholder": placeholder,
			"value":       value,
		}).Debug("Replacing placeholder")

		return value
	})

	// If no changes were made, return early
	if processedText == originalText {
		return 0
	}

	// Update the first run with processed text and clear others
	// This is a simplified approach - in production, you might want to preserve formatting
	if len(runs) > 0 {
		// Clear all runs first
		for _, run := range runs {
			run.Clear()
		}
		// Set processed text in first run
		runs[0].AddText(processedText)
	} else {
		// No existing runs, create a new one
		newRun := para.AddRun()
		newRun.AddText(processedText)
	}

	return placeholdersReplaced
}

// resolveValue resolves a placeholder value from the data context
// Supports nested properties like "matter.name" or "client.address.city"
func (tp *templateProcessor) resolveValue(ctx context.Context, placeholder string, dataContext map[string]interface{}) string {
	log := bilabllog.CreateContextLogger(ctx)

	// Split placeholder by dots for nested access
	parts := strings.Split(strings.TrimSpace(placeholder), ".")

	var current interface{} = dataContext

	for i, part := range parts {
		switch v := current.(type) {
		case map[string]interface{}:
			if val, exists := v[part]; exists {
				current = val
			} else {
				log.WithFields(map[string]interface{}{
					"placeholder": placeholder,
					"missing_key": part,
					"level":       i,
				}).Warn("Placeholder key not found in data context")
				return fmt.Sprintf("{%s}", placeholder) // Return original placeholder if not found
			}
		default:
			log.WithFields(map[string]interface{}{
				"placeholder": placeholder,
				"invalid_key": part,
				"level":       i,
			}).Warn("Cannot access nested property on non-object")
			return fmt.Sprintf("{%s}", placeholder)
		}
	}

	// Convert final value to string
	return tp.valueToString(current)
}

// valueToString converts various types to string representation
func (tp *templateProcessor) valueToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		return fmt.Sprintf("%v", v)
	}
}
