package template

import (
	"bytes"
	"context"
	"testing"

	"github.com/stretchr/testify/suite"

	"baliance.com/gooxml/document"
)

type TemplateProcessorTestSuite struct {
	suite.Suite
	processor TemplateProcessor
}

func (suite *TemplateProcessorTestSuite) SetupTest() {
	suite.processor = NewTemplateProcessor()
}

func (suite *TemplateProcessorTestSuite) TestNewTemplateProcessor() {
	processor := NewTemplateProcessor()
	suite.NotNil(processor)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_SimpleTemplate() {
	ctx := context.Background()

	// Create a simple DOCX template with placeholders
	doc := document.New()
	para := doc.AddParagraph()
	run := para.AddRun()
	run.AddText("Hello {name}, welcome to {company}!")

	// Convert to bytes
	templateBuffer, err := suite.docToBytes(doc)
	suite.NoError(err)

	// Data context
	dataContext := map[string]interface{}{
		"name":    "<PERSON>",
		"company": "Bilabl",
	}

	// Process template
	result, err := suite.processor.ProcessDocx(ctx, templateBuffer, dataContext)
	suite.NoError(err)
	suite.NotNil(result)

	// Verify the result contains replaced text
	// Note: We can't easily verify the exact content without parsing the DOCX again
	// In a real scenario, you might want to parse the result and check the text
	suite.Greater(len(result), 0)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_NestedProperties() {
	ctx := context.Background()

	// Create a DOCX template with nested placeholders
	doc := document.New()
	para := doc.AddParagraph()
	run := para.AddRun()
	run.AddText("Matter: {matter.name}, Client: {matter.client.name}")

	templateBuffer, err := suite.docToBytes(doc)
	suite.NoError(err)

	// Data context with nested objects
	dataContext := map[string]interface{}{
		"matter": map[string]interface{}{
			"name": "Contract Review",
			"client": map[string]interface{}{
				"name": "ABC Corp",
			},
		},
	}

	result, err := suite.processor.ProcessDocx(ctx, templateBuffer, dataContext)
	suite.NoError(err)
	suite.NotNil(result)
	suite.Greater(len(result), 0)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_MissingPlaceholder() {
	ctx := context.Background()

	// Create a DOCX template with a placeholder that won't be found
	doc := document.New()
	para := doc.AddParagraph()
	run := para.AddRun()
	run.AddText("Hello {name}, missing: {missing_value}")

	templateBuffer, err := suite.docToBytes(doc)
	suite.NoError(err)

	// Data context missing the "missing_value" key
	dataContext := map[string]interface{}{
		"name": "John Doe",
	}

	result, err := suite.processor.ProcessDocx(ctx, templateBuffer, dataContext)
	suite.NoError(err)
	suite.NotNil(result)
	suite.Greater(len(result), 0)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_DifferentDataTypes() {
	ctx := context.Background()

	// Create a DOCX template with various data types
	doc := document.New()
	para := doc.AddParagraph()
	run := para.AddRun()
	run.AddText("String: {str}, Number: {num}, Float: {float}, Bool: {bool}")

	templateBuffer, err := suite.docToBytes(doc)
	suite.NoError(err)

	// Data context with different types
	dataContext := map[string]interface{}{
		"str":   "test",
		"num":   42,
		"float": 3.14,
		"bool":  true,
	}

	result, err := suite.processor.ProcessDocx(ctx, templateBuffer, dataContext)
	suite.NoError(err)
	suite.NotNil(result)
	suite.Greater(len(result), 0)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_EmptyTemplate() {
	ctx := context.Background()

	// Create an empty DOCX document
	doc := document.New()

	templateBuffer, err := suite.docToBytes(doc)
	suite.NoError(err)

	dataContext := map[string]interface{}{
		"name": "John Doe",
	}

	result, err := suite.processor.ProcessDocx(ctx, templateBuffer, dataContext)
	suite.NoError(err)
	suite.NotNil(result)
	suite.Greater(len(result), 0)
}

func (suite *TemplateProcessorTestSuite) TestProcessDocx_InvalidDocx() {
	ctx := context.Background()

	// Invalid DOCX buffer
	invalidBuffer := []byte("not a docx file")

	dataContext := map[string]interface{}{
		"name": "John Doe",
	}

	result, err := suite.processor.ProcessDocx(ctx, invalidBuffer, dataContext)
	suite.Error(err)
	suite.Nil(result)
	suite.Contains(err.Error(), "failed to open DOCX template")
}

func (suite *TemplateProcessorTestSuite) TestResolveValue_SimpleValue() {
	ctx := context.Background()
	processor := suite.processor.(*templateProcessor)

	dataContext := map[string]interface{}{
		"name": "John Doe",
	}

	result := processor.resolveValue(ctx, "name", dataContext)
	suite.Equal("John Doe", result)
}

func (suite *TemplateProcessorTestSuite) TestResolveValue_NestedValue() {
	ctx := context.Background()
	processor := suite.processor.(*templateProcessor)

	dataContext := map[string]interface{}{
		"user": map[string]interface{}{
			"profile": map[string]interface{}{
				"name": "John Doe",
			},
		},
	}

	result := processor.resolveValue(ctx, "user.profile.name", dataContext)
	suite.Equal("John Doe", result)
}

func (suite *TemplateProcessorTestSuite) TestResolveValue_MissingKey() {
	ctx := context.Background()
	processor := suite.processor.(*templateProcessor)

	dataContext := map[string]interface{}{
		"name": "John Doe",
	}

	result := processor.resolveValue(ctx, "missing", dataContext)
	suite.Equal("{missing}", result) // Should return original placeholder
}

func (suite *TemplateProcessorTestSuite) TestValueToString_DifferentTypes() {
	processor := suite.processor.(*templateProcessor)

	// Test string
	suite.Equal("test", processor.valueToString("test"))

	// Test integer
	suite.Equal("42", processor.valueToString(42))

	// Test float
	suite.Equal("3.14", processor.valueToString(3.14))

	// Test boolean
	suite.Equal("true", processor.valueToString(true))
	suite.Equal("false", processor.valueToString(false))

	// Test nil
	suite.Equal("", processor.valueToString(nil))
}

// Helper method to convert document to bytes
func (suite *TemplateProcessorTestSuite) docToBytes(doc *document.Document) ([]byte, error) {
	// Use bytes.Buffer to save document
	var buffer bytes.Buffer
	if err := doc.Save(&buffer); err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}

func TestTemplateProcessorTestSuite(t *testing.T) {
	suite.Run(t, new(TemplateProcessorTestSuite))
}
