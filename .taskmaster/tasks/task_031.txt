# Task ID: 31
# Title: Create Event Consumer for 'matter.create'
# Status: pending
# Dependencies: 27, 26
# Priority: high
# Description: Create an asynchronous event consumer to listen for 'matter.create' events from the message bus/queue.
# Details:
Using the existing event bus framework, subscribe to the 'matter.create' topic/queue. The consumer's handler will receive the event payload, extract relevant data, and pass it to the `AutoDocService.findMatchingRules` method. For each matching rule, it will asynchronously trigger the `RuleExecutionEngine`.

# Test Strategy:
Write an integration test that publishes a mock 'matter.create' event to the bus. Verify that the consumer picks it up and calls the `AutoDocService` with the correct payload. Use mocks to prevent actual rule execution.
