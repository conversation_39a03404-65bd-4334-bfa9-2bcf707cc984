# Task ID: 41
# Title: API Endpoint: Retrieve Execution History
# Status: pending
# Dependencies: 30
# Priority: medium
# Description: Create an API endpoint to retrieve the execution history for a specific rule or for the entire tenant.
# Details:
Implement `GET /api/autodoc/rules/{id}/history` and `GET /api/autodoc/history`. These endpoints will query the `RuleExecutionHistory` table, applying the necessary filters for rule ID and tenant ID. Support pagination for these endpoints.

# Test Strategy:
Write an API test. Trigger a rule execution (manually or via event), then call the history endpoint to verify the execution record is returned correctly.
