# Task ID: 32
# Title: Create Event Consumer for 'client.update'
# Status: pending
# Dependencies: 27, 26
# Priority: high
# Description: Create an asynchronous event consumer to listen for 'client.update' events from the message bus/queue.
# Details:
Similar to the matter consumer, subscribe to the 'client.update' topic/queue. The handler will parse the event, find matching rules via `AutoDocService`, and trigger the `RuleExecutionEngine` for each match. This demonstrates support for multiple trigger types.

# Test Strategy:
Write an integration test that publishes a mock 'client.update' event. Verify the consumer is triggered and correctly interacts with the `AutoDocService`.
