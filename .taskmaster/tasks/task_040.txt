# Task ID: 40
# Title: Implement API Input Validation
# Status: pending
# Dependencies: 34, 35
# Priority: high
# Description: Implement strict input validation on all API endpoints to ensure the integrity of rule configurations and payloads.
# Details:
Use a validation library (like Zod, Joi, or built-in framework validators) to define schemas for API request bodies. For rule creation/updates, validate that the `conditions` and `actions` JSON have the expected structure, that action types are valid, and that paths are well-formed.

# Test Strategy:
In the API tests for CRUD endpoints, add test cases that send malformed or incomplete JSON payloads and assert that the server responds with a 400 Bad Request and a descriptive error message.
