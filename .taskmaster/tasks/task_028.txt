# Task ID: 28
# Title: Implement 'generate_document' Action Handler
# Status: pending
# Dependencies: 24, 25
# Priority: medium
# Description: Create a specific action handler for the 'generate_document' action. This will integrate the DOCX processor with the file service.
# Details:
Within the `RuleExecutionEngine`, create a private method `handleGenerateDocument(action, context)`. This handler will: 1. Fetch the template file from the path specified in the action. 2. Process it using the `TemplateProcessor`. 3. Generate a dynamic target filename using the `sanitizeFilename` utility. 4. Save the processed document to the target path using the `AutoDocFileService`.

# Test Strategy:
Integration test this specific action. Create a rule with a `generate_document` action, trigger the execution engine, and verify that a new, processed DOCX file appears in the correct location with the correct content and name.
