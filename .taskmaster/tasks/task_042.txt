# Task ID: 42
# Title: Enforce Strict Tenant Isolation
# Status: pending
# Dependencies: 19, 21, 34
# Priority: high
# Description: Enforce tenant isolation at every level of the application: repository, services, and API controllers.
# Details:
Ensure all database queries in repositories include a `WHERE tenant_id = ?` clause. In services and controllers, the `tenant_id` should be retrieved from the authenticated user's context and passed down through all layers. Never allow a tenant ID to be specified directly in an API request body.

# Test Strategy:
Write security-focused integration tests. Authenticate as Tenant A, create a rule, then authenticate as Tenant B and attempt to read, update, or delete Tenant A's rule. Assert that all attempts result in a 404 Not Found or 403 Forbidden error.
