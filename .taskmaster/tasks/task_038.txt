# Task ID: 38
# Title: API Endpoint: Manage AutoDocRoot Templates
# Status: pending
# Dependencies: 20
# Priority: low
# Description: Create a simple API endpoint to manage or view the AutoDocRoot structure, primarily for diagnostic purposes.
# Details:
Implement a GET endpoint like `/api/autodoc/templates` that lists the file and folder structure within the tenant's `AutoDocRoot`. This helps users see which templates are available for use in rules. This can leverage the existing document service's listing capabilities.

# Test Strategy:
API test: Create some files and folders in the `AutoDocRoot`, then call this endpoint and assert that the response correctly reflects the created structure.
