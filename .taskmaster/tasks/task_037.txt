# Task ID: 37
# Title: API Endpoint: Manual Rule Execution for Testing
# Status: pending
# Dependencies: 26
# Priority: medium
# Description: Create a REST API endpoint to manually trigger a rule for testing purposes (POST /api/autodoc/rules/{id}/execute).
# Details:
The endpoint will accept a rule ID and an optional JSON body representing a mock event payload. It will fetch the rule, and then directly invoke the `RuleExecutionEngine` with the rule and the provided payload. This is crucial for debugging and setup.

# Test Strategy:
Write an API test that creates a rule and a template. Call this endpoint with a mock payload and verify that the document generation is triggered successfully and the result appears in the document system.
