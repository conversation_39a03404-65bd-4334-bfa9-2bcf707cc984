# Task ID: 29
# Title: Implement Dynamic Target Path Generation
# Status: pending
# Dependencies: 24
# Priority: medium
# Description: Implement the logic to dynamically generate the target path for file and folder operations based on event data.
# Details:
Create a utility function `resolveTargetPath(pathTemplate, context)`. This function will replace placeholders in a path string (e.g., '/clients/{{client.name}}/matters/{{matter.id}}') with data from the event context. This will be used by all action handlers before calling the file service.

# Test Strategy:
Unit test the utility with various path templates and context objects. Ensure it correctly handles nested placeholders and produces a clean, valid path string.
