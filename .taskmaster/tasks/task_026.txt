# Task ID: 26
# Title: Develop Core Rule Execution Engine
# Status: pending
# Dependencies: 21, 22, 23, 25
# Priority: high
# Description: Develop the main Rule Execution Engine. This service will take a rule and event data, and orchestrate the execution of the defined actions.
# Details:
Create a `RuleExecutionEngine` service. It will have a primary method `execute(rule, eventData)`. This method will iterate through the `actions` in the rule's JSON configuration. For each action, it will call the appropriate handler (e.g., `AutoDocFileService.copyFile`).

# Test Strategy:
Write integration tests for the engine. Create mock rules with different action types and mock the underlying services (`AutoDocFileService`, `TemplateProcessor`) to verify that the correct methods are called with the correct parameters.
