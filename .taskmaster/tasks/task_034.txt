# Task ID: 34
# Title: API Endpoint: Create Automation Rule
# Status: pending
# Dependencies: 21, 19
# Priority: high
# Description: Implement the REST API endpoint for creating a new DocumentAutomationRule (POST /api/autodoc/rules).
# Details:
Create a controller that handles the POST request. The request body will contain the rule configuration (name, trigger, conditions, actions). The controller will call `AutoDocService` to validate and persist the new rule using the repository. Return the created rule with its new ID.

# Test Strategy:
Write an API integration test. Send a valid JSON payload to the endpoint and assert a 201 Created response. Verify the rule is correctly saved in the database. Test with invalid payloads to ensure 400 Bad Request responses.
