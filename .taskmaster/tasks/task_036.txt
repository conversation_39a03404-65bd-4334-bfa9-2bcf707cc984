# Task ID: 36
# Title: API Endpoint: List All Rules for Tenant
# Status: pending
# Dependencies: 21, 19
# Priority: medium
# Description: Implement the REST API endpoint for listing all automation rules for the current tenant (GET /api/autodoc/rules).
# Details:
Add a GET method to the controller that lists all rules. The corresponding service method will call `repository.findByTenantId` to ensure only the rules for the authenticated tenant are returned. Support pagination if necessary.

# Test Strategy:
Write an API test that creates several rules for a tenant, then calls this endpoint to verify that all and only those rules are returned in the response.
