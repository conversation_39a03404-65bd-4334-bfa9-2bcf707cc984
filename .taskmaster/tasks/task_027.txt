# Task ID: 27
# Title: Implement Rule Matching Logic
# Status: pending
# Dependencies: 26
# Priority: high
# Description: Implement the logic within the AutoDocService to match incoming events against stored automation rules based on trigger type and conditions.
# Details:
Create a method `findMatchingRules(triggerType, eventPayload)` in `AutoDocService`. It will first query the repository for all active rules with the given `triggerType`. Then, it will iterate through them, evaluating the `conditions` JSON against the `eventPayload`. A simple condition could be `{"matter_category": "litigation"}`.

# Test Strategy:
Unit test the matching logic with various event payloads and rule conditions. Test cases should include: perfect match, partial match, no match, multiple matching rules, and rules with multiple conditions.
