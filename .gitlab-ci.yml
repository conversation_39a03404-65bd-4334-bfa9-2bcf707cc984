include:
  - project: 'mybil/ci'
    ref: 'v2023.9.4'
    file: '.go.yml'

variables:
  SWAGGER_FILE: "./api/api.yml"
  GOBILLING_URL: "http://billing"
  CLIENT_URL: "http://hrglassclient"
  HRGLASSID_URL: "http://hrglassid"
  DOCMAN_URL: "http://docman"
  MATTER_URL: "http://matter"

stg:deploy:
  variables:
    DB_HOST             : $STG_DB_HOST
    DB_PORT             : $STG_DB_PORT
    DB_USER             : $STG_DB_USER
    DB_NAME             : $STG_DB_NAME
    DB_PASS             : $STG_DB_PASS
    SP_CLIENT_ID        : $STG_SP_CLIENT_ID
    SP_CLIENT_SECRET    : $STG_SP_CLIENT_SECRET
    SP_SCOPE            : $STG_SP_SCOPE
    LOG_LEVEL           : debug

prd:deploy:
  variables:
    DB_HOST             : $PRD_DB_HOST
    DB_PORT             : $PRD_DB_PORT
    DB_USER             : $PRD_DB_USER
    DB_NAME             : $PRD_DB_NAME
    DB_PASS             : $PRD_DB_PASS
    SP_CLIENT_ID        : $PRD_SP_CLIENT_ID
    SP_CLIENT_SECRET    : $PRD_SP_CLIENT_SECRET
    SP_SCOPE            : $PRD_SP_SCOPE
    LOG_LEVEL           : error
