VERSION := $(shell git rev-parse --short HEAD)

bin/server: $(shell find . -name '*.go')
	go build -o bin/server -ldflags="-s -w -X main.version=$(VERSION)" ./cmd/server

run: bin/server
	./bin/server

deps:
	@go install github.com/vektra/mockery/v2@v2.43.2

gen:
	@mockery --config .mockery.yaml

mocks: gen

test:
	# wget -0 go_test.sh https://gitlab.com/mybil/ci/-/raw/master/scripts/go_test.sh
	./go_test.sh
