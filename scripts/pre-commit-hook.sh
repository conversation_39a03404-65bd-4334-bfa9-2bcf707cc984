#!/bin/sh
# Pre-commit hook to ensure mocks are up to date
# Install: cp scripts/pre-commit-hook.sh .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit

set -e

echo "🔍 Checking if mocks need to be regenerated..."

# Generate mocks
make mocks

# Check if there are any changes in the mocks directory
if [[ -n $(git status --porcelain mocks/) ]]; then
    echo "❌ Mocks were regenerated and have uncommitted changes."
    echo "   Please add the updated mocks to your commit:"
    echo "   git add mocks/"
    echo "   git commit --amend --no-edit"
    echo ""
    echo "   Changed files:"
    git status --porcelain mocks/
    exit 1
fi

echo "✅ Mocks are up to date."

# Run tests to ensure everything still works
echo "🧪 Running tests..."
if ! make test > /dev/null 2>&1; then
    echo "❌ Tests failed. Please fix the issues before committing."
    exit 1
fi

echo "✅ All tests passed."
echo "🚀 Ready to commit!"
