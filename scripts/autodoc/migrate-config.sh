#!/bin/bash

# AutoDoc Configuration Migration Script
# This script helps migrate configuration for AutoDoc deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_BACKUP_DIR="${PROJECT_ROOT}/config/backup"
ENV_FILE="${PROJECT_ROOT}/.env"
ENV_EXAMPLE="${PROJECT_ROOT}/.env.example"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. Consider using a non-root user for security."
    fi
}

# Create backup directory
create_backup_dir() {
    log_info "Creating backup directory..."
    mkdir -p "$CONFIG_BACKUP_DIR"
    log_success "Backup directory created: $CONFIG_BACKUP_DIR"
}

# Backup existing configuration
backup_config() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="${CONFIG_BACKUP_DIR}/config_backup_${timestamp}.tar.gz"
    
    log_info "Backing up existing configuration..."
    
    # Files to backup
    local files_to_backup=()
    
    if [[ -f "$ENV_FILE" ]]; then
        files_to_backup+=("$ENV_FILE")
    fi
    
    if [[ -f "${PROJECT_ROOT}/config/production.yml" ]]; then
        files_to_backup+=("${PROJECT_ROOT}/config/production.yml")
    fi
    
    if [[ -f "${PROJECT_ROOT}/config/staging.yml" ]]; then
        files_to_backup+=("${PROJECT_ROOT}/config/staging.yml")
    fi
    
    if [[ ${#files_to_backup[@]} -gt 0 ]]; then
        tar -czf "$backup_file" -C "$PROJECT_ROOT" "${files_to_backup[@]#$PROJECT_ROOT/}"
        log_success "Configuration backed up to: $backup_file"
    else
        log_warning "No configuration files found to backup"
    fi
}

# Generate environment file template
generate_env_template() {
    log_info "Generating AutoDoc environment template..."
    
    cat > "${PROJECT_ROOT}/.env.autodoc" << 'EOF'
# AutoDoc Configuration
# Copy these variables to your .env file and adjust values as needed

# Core AutoDoc Settings
AUTODOC_ENABLED=true
AUTODOC_DEFAULT_PROVIDER=internal
AUTODOC_MAX_RULES_PER_TENANT=100

# Feature Flags
FEATURE_AUTODOC_RULES=true
FEATURE_AUTODOC_PROVIDERS=true
FEATURE_AUTODOC_FILES=true
FEATURE_MULTI_PROVIDER=false

# Performance Settings
AUTODOC_RULE_EXECUTION_TIMEOUT=30s
AUTODOC_BATCH_SIZE=50
AUTODOC_MAX_CONCURRENT_RULES=10

# Monitoring and Health Checks
AUTODOC_METRICS_ENABLED=true
AUTODOC_HEALTH_CHECK_INTERVAL=60s
AUTODOC_PROVIDER_HEALTH_TIMEOUT=10s

# Provider Configuration
GDRIVE_ENABLED=false
GDRIVE_SERVICE_ACCOUNT_KEY_PATH=/path/to/service-account.json
GDRIVE_DOMAIN_WIDE_DELEGATION=true
GDRIVE_MAX_RETRIES=3
GDRIVE_RETRY_DELAY=1s

SHAREPOINT_ENABLED=false
SHAREPOINT_CLIENT_ID=your-client-id
SHAREPOINT_CLIENT_SECRET=your-client-secret
SHAREPOINT_TENANT_ID=your-tenant-id
SHAREPOINT_MAX_RETRIES=3
SHAREPOINT_RETRY_DELAY=1s

# Database Settings (if not already configured)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=docman
# DB_USER=docman_user
# DB_PASSWORD=your_password
# DB_SSLMODE=disable
# DB_MAX_OPEN_CONNS=25
# DB_MAX_IDLE_CONNS=5
# DB_CONN_MAX_LIFETIME=300s

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
AUTODOC_LOG_LEVEL=info

# Security
AUTODOC_RATE_LIMIT_ENABLED=true
AUTODOC_RATE_LIMIT_REQUESTS_PER_HOUR=1000
AUTODOC_RATE_LIMIT_BURST=50

# Development/Testing (set to false in production)
AUTODOC_DEBUG_MODE=false
AUTODOC_MOCK_PROVIDERS=false
EOF

    log_success "AutoDoc environment template created: ${PROJECT_ROOT}/.env.autodoc"
    log_info "Review and copy relevant variables to your .env file"
}

# Validate environment configuration
validate_config() {
    log_info "Validating configuration..."
    
    local errors=0
    
    # Check required environment variables
    local required_vars=(
        "DB_HOST"
        "DB_NAME"
        "DB_USER"
        "DB_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable not set: $var"
            ((errors++))
        fi
    done
    
    # Check AutoDoc specific variables
    if [[ "${AUTODOC_ENABLED:-false}" == "true" ]]; then
        local autodoc_vars=(
            "AUTODOC_DEFAULT_PROVIDER"
            "AUTODOC_MAX_RULES_PER_TENANT"
        )
        
        for var in "${autodoc_vars[@]}"; do
            if [[ -z "${!var:-}" ]]; then
                log_warning "AutoDoc variable not set: $var (will use default)"
            fi
        done
    fi
    
    # Validate provider configuration
    if [[ "${GDRIVE_ENABLED:-false}" == "true" ]]; then
        if [[ -z "${GDRIVE_SERVICE_ACCOUNT_KEY_PATH:-}" ]]; then
            log_error "Google Drive enabled but GDRIVE_SERVICE_ACCOUNT_KEY_PATH not set"
            ((errors++))
        elif [[ ! -f "${GDRIVE_SERVICE_ACCOUNT_KEY_PATH}" ]]; then
            log_error "Google Drive service account key file not found: ${GDRIVE_SERVICE_ACCOUNT_KEY_PATH}"
            ((errors++))
        fi
    fi
    
    if [[ "${SHAREPOINT_ENABLED:-false}" == "true" ]]; then
        local sharepoint_vars=(
            "SHAREPOINT_CLIENT_ID"
            "SHAREPOINT_CLIENT_SECRET"
            "SHAREPOINT_TENANT_ID"
        )
        
        for var in "${sharepoint_vars[@]}"; do
            if [[ -z "${!var:-}" ]]; then
                log_error "SharePoint enabled but $var not set"
                ((errors++))
            fi
        done
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_success "Configuration validation passed"
        return 0
    else
        log_error "Configuration validation failed with $errors errors"
        return 1
    fi
}

# Test database connectivity
test_database() {
    log_info "Testing database connectivity..."
    
    local db_host="${DB_HOST:-localhost}"
    local db_port="${DB_PORT:-5432}"
    local db_name="${DB_NAME:-docman}"
    local db_user="${DB_USER:-postgres}"
    
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD="${DB_PASSWORD}" psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -c "SELECT 1;" >/dev/null 2>&1; then
            log_success "Database connectivity test passed"
            return 0
        else
            log_error "Database connectivity test failed"
            return 1
        fi
    else
        log_warning "psql not found, skipping database connectivity test"
        return 0
    fi
}

# Generate systemd service file
generate_systemd_service() {
    log_info "Generating systemd service file..."
    
    local service_file="${PROJECT_ROOT}/scripts/docman.service"
    local user="${USER:-docman}"
    local group="${USER:-docman}"
    local working_dir="$PROJECT_ROOT"
    local exec_start="${PROJECT_ROOT}/bin/docman"
    
    cat > "$service_file" << EOF
[Unit]
Description=DocMan AutoDoc Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=$user
Group=$group
WorkingDirectory=$working_dir
ExecStart=$exec_start
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=docman

# Environment
Environment=GIN_MODE=release
EnvironmentFile=$ENV_FILE

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$working_dir

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    log_success "Systemd service file created: $service_file"
    log_info "To install: sudo cp $service_file /etc/systemd/system/"
    log_info "To enable: sudo systemctl enable docman"
}

# Main execution
main() {
    echo "🚀 AutoDoc Configuration Migration"
    echo "=================================="
    
    check_permissions
    create_backup_dir
    backup_config
    generate_env_template
    generate_systemd_service
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Review and update .env.autodoc template"
    echo "2. Copy relevant variables to your .env file"
    echo "3. Set up provider credentials (Google Drive/SharePoint)"
    echo "4. Test configuration with: source .env && ./scripts/migrate-config.sh validate"
    echo "5. Deploy application with new configuration"
    
    log_success "Configuration migration preparation completed!"
}

# Command line interface
case "${1:-}" in
    "validate")
        # Load environment variables
        if [[ -f "$ENV_FILE" ]]; then
            set -a
            source "$ENV_FILE"
            set +a
        fi
        
        validate_config && test_database
        ;;
    "backup")
        create_backup_dir
        backup_config
        ;;
    "template")
        generate_env_template
        ;;
    "service")
        generate_systemd_service
        ;;
    *)
        main
        ;;
esac
