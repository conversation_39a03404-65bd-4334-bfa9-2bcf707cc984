package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"bilabl/docman/domain/model"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ValidationResult represents the result of a validation check
type ValidationResult struct {
	Check   string
	Status  string
	Message string
	Details map[string]interface{}
}

// MigrationValidator validates the database state after AutoDoc migration
type MigrationValidator struct {
	db      *gorm.DB
	results []ValidationResult
}

func main() {
	fmt.Println("🔍 Starting AutoDoc Migration Validation...")
	fmt.Println(strings.Repeat("=", 50))

	validator, err := NewMigrationValidator()
	if err != nil {
		log.Fatalf("Failed to initialize validator: %v", err)
	}
	defer validator.Close()

	// Run all validation checks
	validator.ValidateTableStructure()
	validator.ValidateIndexes()
	validator.ValidateDataIntegrity()
	validator.ValidateConstraints()
	validator.ValidatePerformance()

	// Generate report
	validator.GenerateReport()

	// Exit with appropriate code
	if validator.HasFailures() {
		fmt.Println("\n❌ Migration validation FAILED")
		os.Exit(1)
	} else {
		fmt.Println("\n✅ Migration validation PASSED")
		os.Exit(0)
	}
}

// NewMigrationValidator creates a new migration validator
func NewMigrationValidator() (*MigrationValidator, error) {
	// Get database connection string from environment
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
			getEnvOrDefault("DB_HOST", "localhost"),
			getEnvOrDefault("DB_USER", "postgres"),
			getEnvOrDefault("DB_PASSWORD", ""),
			getEnvOrDefault("DB_NAME", "docman"),
			getEnvOrDefault("DB_PORT", "5432"),
			getEnvOrDefault("DB_SSLMODE", "disable"),
		)
	}

	// Configure GORM logger for validation
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return &MigrationValidator{
		db:      db,
		results: make([]ValidationResult, 0),
	}, nil
}

// Close closes the database connection
func (v *MigrationValidator) Close() {
	if sqlDB, err := v.db.DB(); err == nil {
		sqlDB.Close()
	}
}

// ValidateTableStructure validates that all required tables and columns exist
func (v *MigrationValidator) ValidateTableStructure() {
	fmt.Println("📋 Validating table structure...")

	// Check document_automation_rules table
	if v.db.Migrator().HasTable(&model.DocumentAutomationRule{}) {
		v.addResult("document_automation_rules_table", "PASS", "Table exists", nil)

		// Check required columns
		requiredColumns := []string{
			"id", "tenant_id", "name", "description", "trigger_type",
			"trigger_rules", "rule_config", "is_active", "created_user",
			"updated_user", "created_at", "updated_at", "deleted_at",
		}

		missingColumns := make([]string, 0)
		for _, column := range requiredColumns {
			if !v.db.Migrator().HasColumn(&model.DocumentAutomationRule{}, column) {
				missingColumns = append(missingColumns, column)
			}
		}

		if len(missingColumns) == 0 {
			v.addResult("document_automation_rules_columns", "PASS", "All required columns exist", nil)
		} else {
			v.addResult("document_automation_rules_columns", "FAIL", "Missing columns", map[string]interface{}{
				"missing_columns": missingColumns,
			})
		}
	} else {
		v.addResult("document_automation_rules_table", "FAIL", "Table does not exist", nil)
	}

	// Check document_mapping enhancements
	if v.db.Migrator().HasTable(&model.DocumentMapping{}) {
		v.addResult("document_mapping_table", "PASS", "Table exists", nil)

		// Check provider column
		if v.db.Migrator().HasColumn(&model.DocumentMapping{}, "provider") {
			v.addResult("document_mapping_provider_column", "PASS", "Provider column exists", nil)
		} else {
			v.addResult("document_mapping_provider_column", "FAIL", "Provider column missing", nil)
		}
	} else {
		v.addResult("document_mapping_table", "FAIL", "Table does not exist", nil)
	}

	// Check documents table enhancements
	if v.db.Migrator().HasTable(&model.Document{}) {
		v.addResult("documents_table", "PASS", "Table exists", nil)

		// Check new columns
		newColumns := map[string]string{
			"parent_path": "Parent path column",
			"size":        "Size column",
		}

		for column, description := range newColumns {
			if v.db.Migrator().HasColumn(&model.Document{}, column) {
				v.addResult(fmt.Sprintf("documents_%s_column", column), "PASS", fmt.Sprintf("%s exists", description), nil)
			} else {
				v.addResult(fmt.Sprintf("documents_%s_column", column), "WARN", fmt.Sprintf("%s missing (may be optional)", description), nil)
			}
		}
	} else {
		v.addResult("documents_table", "FAIL", "Table does not exist", nil)
	}
}

// ValidateIndexes validates that required indexes exist
func (v *MigrationValidator) ValidateIndexes() {
	fmt.Println("🔍 Validating indexes...")

	ctx := context.Background()

	// Check for critical indexes
	criticalIndexes := []struct {
		table string
		name  string
		desc  string
	}{
		{"document_automation_rules", "idx_document_automation_rules_tenant_id", "Tenant ID index"},
		{"document_automation_rules", "idx_document_automation_rules_trigger_type", "Trigger type index"},
		{"document_automation_rules", "idx_document_automation_rules_is_active", "Active status index"},
		{"document_mapping", "idx_document_mapping_tenant_id", "Tenant ID index"},
		{"documents", "idx_documents_tenant_id", "Tenant ID index"},
	}

	for _, idx := range criticalIndexes {
		var count int64
		err := v.db.WithContext(ctx).Raw(`
			SELECT COUNT(*)
			FROM pg_indexes
			WHERE tablename = ? AND indexname LIKE ?`,
			idx.table, fmt.Sprintf("%%%s%%", idx.name)).Scan(&count).Error

		if err != nil {
			v.addResult(fmt.Sprintf("index_%s", idx.name), "ERROR", fmt.Sprintf("Failed to check %s", idx.desc), map[string]interface{}{
				"error": err.Error(),
			})
		} else if count > 0 {
			v.addResult(fmt.Sprintf("index_%s", idx.name), "PASS", fmt.Sprintf("%s exists", idx.desc), nil)
		} else {
			v.addResult(fmt.Sprintf("index_%s", idx.name), "WARN", fmt.Sprintf("%s missing", idx.desc), nil)
		}
	}
}

// ValidateDataIntegrity validates data consistency and integrity
func (v *MigrationValidator) ValidateDataIntegrity() {
	fmt.Println("🔒 Validating data integrity...")

	ctx := context.Background()

	// Check for orphaned automation rules
	var orphanedRules int64
	err := v.db.WithContext(ctx).Model(&model.DocumentAutomationRule{}).
		Where("tenant_id = ? OR tenant_id IS NULL", 0).
		Count(&orphanedRules).Error

	if err != nil {
		v.addResult("orphaned_rules_check", "ERROR", "Failed to check orphaned rules", map[string]interface{}{
			"error": err.Error(),
		})
	} else if orphanedRules == 0 {
		v.addResult("orphaned_rules_check", "PASS", "No orphaned automation rules", nil)
	} else {
		v.addResult("orphaned_rules_check", "WARN", "Found orphaned automation rules", map[string]interface{}{
			"count": orphanedRules,
		})
	}

	// Check for invalid JSON in automation rules
	var invalidJSON int64
	err = v.db.WithContext(ctx).Raw(`
		SELECT COUNT(*)
		FROM document_automation_rules
		WHERE NOT (trigger_rules::text ~ '^{.*}$' AND rule_config::text ~ '^\[.*\]$')
	`).Scan(&invalidJSON).Error

	if err != nil {
		v.addResult("invalid_json_check", "ERROR", "Failed to check JSON validity", map[string]interface{}{
			"error": err.Error(),
		})
	} else if invalidJSON == 0 {
		v.addResult("invalid_json_check", "PASS", "All JSON fields are valid", nil)
	} else {
		v.addResult("invalid_json_check", "FAIL", "Found invalid JSON in automation rules", map[string]interface{}{
			"count": invalidJSON,
		})
	}

	// Check document mapping consistency
	var invalidMappings int64
	err = v.db.WithContext(ctx).Model(&model.DocumentMapping{}).
		Where("provider IS NULL OR provider = ''").
		Count(&invalidMappings).Error

	if err != nil {
		v.addResult("mapping_consistency_check", "ERROR", "Failed to check mapping consistency", map[string]interface{}{
			"error": err.Error(),
		})
	} else if invalidMappings == 0 {
		v.addResult("mapping_consistency_check", "PASS", "All document mappings have valid providers", nil)
	} else {
		v.addResult("mapping_consistency_check", "WARN", "Found mappings with invalid providers", map[string]interface{}{
			"count": invalidMappings,
		})
	}
}

// ValidateConstraints validates database constraints
func (v *MigrationValidator) ValidateConstraints() {
	fmt.Println("⚖️ Validating constraints...")

	ctx := context.Background()

	// Check for constraint violations
	constraints := []struct {
		name  string
		query string
		desc  string
	}{
		{
			"tenant_id_not_null",
			"SELECT COUNT(*) FROM document_automation_rules WHERE tenant_id IS NULL",
			"Tenant ID not null constraint",
		},
		{
			"trigger_type_not_empty",
			"SELECT COUNT(*) FROM document_automation_rules WHERE trigger_type = '' OR trigger_type IS NULL",
			"Trigger type not empty constraint",
		},
		{
			"name_not_empty",
			"SELECT COUNT(*) FROM document_automation_rules WHERE name = '' OR name IS NULL",
			"Name not empty constraint",
		},
	}

	for _, constraint := range constraints {
		var violationCount int64
		err := v.db.WithContext(ctx).Raw(constraint.query).Scan(&violationCount).Error

		if err != nil {
			v.addResult(fmt.Sprintf("constraint_%s", constraint.name), "ERROR", fmt.Sprintf("Failed to check %s", constraint.desc), map[string]interface{}{
				"error": err.Error(),
			})
		} else if violationCount == 0 {
			v.addResult(fmt.Sprintf("constraint_%s", constraint.name), "PASS", fmt.Sprintf("%s satisfied", constraint.desc), nil)
		} else {
			v.addResult(fmt.Sprintf("constraint_%s", constraint.name), "FAIL", fmt.Sprintf("%s violated", constraint.desc), map[string]interface{}{
				"violations": violationCount,
			})
		}
	}
}

// ValidatePerformance validates basic performance characteristics
func (v *MigrationValidator) ValidatePerformance() {
	fmt.Println("⚡ Validating performance...")

	ctx := context.Background()

	// Test query performance for common operations
	start := time.Now()
	var count int64
	err := v.db.WithContext(ctx).Model(&model.DocumentAutomationRule{}).
		Where("tenant_id = ? AND is_active = ?", 1, true).
		Count(&count).Error

	duration := time.Since(start)

	if err != nil {
		v.addResult("query_performance", "ERROR", "Failed to test query performance", map[string]interface{}{
			"error": err.Error(),
		})
	} else if duration < 100*time.Millisecond {
		v.addResult("query_performance", "PASS", "Query performance acceptable", map[string]interface{}{
			"duration_ms": duration.Milliseconds(),
			"count":       count,
		})
	} else {
		v.addResult("query_performance", "WARN", "Query performance may need optimization", map[string]interface{}{
			"duration_ms": duration.Milliseconds(),
			"count":       count,
		})
	}
}

// addResult adds a validation result
func (v *MigrationValidator) addResult(check, status, message string, details map[string]interface{}) {
	v.results = append(v.results, ValidationResult{
		Check:   check,
		Status:  status,
		Message: message,
		Details: details,
	})
}

// HasFailures returns true if any validation failed
func (v *MigrationValidator) HasFailures() bool {
	for _, result := range v.results {
		if result.Status == "FAIL" || result.Status == "ERROR" {
			return true
		}
	}
	return false
}

// GenerateReport generates a comprehensive validation report
func (v *MigrationValidator) GenerateReport() {
	fmt.Println("\n📊 Migration Validation Report")
	fmt.Println(strings.Repeat("=", 50))

	passed := 0
	warned := 0
	failed := 0
	errors := 0

	for _, result := range v.results {
		var icon string
		switch result.Status {
		case "PASS":
			icon = "✅"
			passed++
		case "WARN":
			icon = "⚠️"
			warned++
		case "FAIL":
			icon = "❌"
			failed++
		case "ERROR":
			icon = "💥"
			errors++
		}

		fmt.Printf("%s %s: %s\n", icon, result.Check, result.Message)
		if result.Details != nil {
			for key, value := range result.Details {
				fmt.Printf("   %s: %v\n", key, value)
			}
		}
	}

	fmt.Println("\n📈 Summary")
	fmt.Printf("✅ Passed: %d\n", passed)
	fmt.Printf("⚠️  Warnings: %d\n", warned)
	fmt.Printf("❌ Failed: %d\n", failed)
	fmt.Printf("💥 Errors: %d\n", errors)
	fmt.Printf("📊 Total Checks: %d\n", len(v.results))

	if failed > 0 || errors > 0 {
		fmt.Println("\n🚨 Action Required: Address failed checks before proceeding")
	} else if warned > 0 {
		fmt.Println("\n⚠️  Review Warnings: Some issues may need attention")
	} else {
		fmt.Println("\n🎉 All validations passed successfully!")
	}
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
