#!/bin/bash

# Test script for AutoDoc Rename & Delete API
# Usage: ./scripts/test-rename-delete-api.sh [BASE_URL] [AUTH_TOKEN]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8088}"
AUTH_TOKEN="${2:-test-token}"
TENANT_ID="1"
USER_ID="1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test helper function
test_api() {
    local method="$1"
    local url="$2"
    local data="$3"
    local expected_status="$4"
    local description="$5"
    
    echo
    log_info "Testing: $description"
    echo "  Method: $method"
    echo "  URL: $url"
    if [ -n "$data" ]; then
        echo "  Data: $data"
    fi
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            -H "X-Tenant-ID: $TENANT_ID" \
            -H "X-User-ID: $USER_ID" \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "X-Tenant-ID: $TENANT_ID" \
            -H "X-User-ID: $USER_ID")
    fi
    
    # Split response body and status code
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "  Response Status: $status"
    echo "  Response Body: $body"
    
    if [ "$status" = "$expected_status" ]; then
        log_info "✅ Test passed"
        return 0
    else
        log_error "❌ Test failed - Expected status $expected_status, got $status"
        return 1
    fi
}

# Main test execution
main() {
    log_info "Starting AutoDoc Rename & Delete API Tests"
    log_info "Base URL: $BASE_URL"
    log_info "Tenant ID: $TENANT_ID"
    echo

    # Test 1: Create a test folder for internal provider
    log_info "=== Test 1: Create Test Folder (Internal Provider) ==="
    test_api "POST" "$BASE_URL/v3/autodoc/files?provider=internal" \
        '{"name":"Test Folder for Rename","doc_type":1,"provider":"internal"}' \
        "200" \
        "Create test folder in internal storage"
    
    # Extract folder ID from response (assuming JSON response with id field)
    INTERNAL_FOLDER_ID=$(echo "$body" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    log_info "Created internal folder with ID: $INTERNAL_FOLDER_ID"

    # Test 2: Rename internal folder
    if [ -n "$INTERNAL_FOLDER_ID" ]; then
        log_info "=== Test 2: Rename Internal Folder ==="
        test_api "PUT" "$BASE_URL/v3/autodoc/files/$INTERNAL_FOLDER_ID?provider=internal" \
            '{"name":"Renamed Test Folder"}' \
            "200" \
            "Rename internal folder"
    else
        log_warn "Skipping rename test - no folder ID available"
    fi

    # Test 3: Delete internal folder
    if [ -n "$INTERNAL_FOLDER_ID" ]; then
        log_info "=== Test 3: Delete Internal Folder ==="
        test_api "DELETE" "$BASE_URL/v3/autodoc/files/$INTERNAL_FOLDER_ID?provider=internal" \
            "" \
            "200" \
            "Delete internal folder"
    else
        log_warn "Skipping delete test - no folder ID available"
    fi

    # Test 4: Test error cases
    log_info "=== Test 4: Error Cases ==="
    
    # Test rename with invalid ID
    test_api "PUT" "$BASE_URL/v3/autodoc/files/999999?provider=internal" \
        '{"name":"Should Fail"}' \
        "404" \
        "Rename non-existent file (should fail)"
    
    # Test delete with invalid ID
    test_api "DELETE" "$BASE_URL/v3/autodoc/files/999999?provider=internal" \
        "" \
        "404" \
        "Delete non-existent file (should fail)"
    
    # Test rename with empty name
    test_api "PUT" "$BASE_URL/v3/autodoc/files/1?provider=internal" \
        '{"name":""}' \
        "400" \
        "Rename with empty name (should fail)"
    
    # Test rename with invalid characters
    test_api "PUT" "$BASE_URL/v3/autodoc/files/1?provider=internal" \
        '{"name":"invalid/name"}' \
        "400" \
        "Rename with invalid characters (should fail)"

    # Test 5: Google Drive provider tests (if available)
    log_info "=== Test 5: Google Drive Provider Tests ==="
    log_warn "Note: These tests require Google Drive configuration and may fail if not set up"
    
    # Test Google Drive folder creation
    test_api "POST" "$BASE_URL/v3/autodoc/files?provider=gdrive" \
        '{"name":"GDrive Test Folder","doc_type":1,"provider":"gdrive"}' \
        "200" \
        "Create test folder in Google Drive (may fail if not configured)"
    
    # Extract Google Drive folder ID if successful
    if [ "$?" -eq 0 ]; then
        GDRIVE_FOLDER_ID=$(echo "$body" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        log_info "Created Google Drive folder with ID: $GDRIVE_FOLDER_ID"
        
        # Test Google Drive rename
        if [ -n "$GDRIVE_FOLDER_ID" ]; then
            test_api "PUT" "$BASE_URL/v3/autodoc/files/$GDRIVE_FOLDER_ID?provider=gdrive" \
                '{"name":"Renamed GDrive Folder"}' \
                "200" \
                "Rename Google Drive folder"
            
            # Test Google Drive delete
            test_api "DELETE" "$BASE_URL/v3/autodoc/files/$GDRIVE_FOLDER_ID?provider=gdrive" \
                "" \
                "200" \
                "Delete Google Drive folder"
        fi
    else
        log_warn "Skipping Google Drive rename/delete tests - folder creation failed"
    fi

    echo
    log_info "=== Test Summary ==="
    log_info "All tests completed. Check individual test results above."
    log_info "Note: Some tests may fail if the service is not running or not properly configured."
}

# Check if curl is available
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed. Please install curl and try again."
    exit 1
fi

# Run main function
main "$@"
