# Debug GDrive AutoDoc Flow

## Issue
Autodoc rules với target gdrive không tạo folder và file trên GDrive UI.

## Root Cause Found
Trong `copy_file_handler.go`, method `uploadContentToGoogleDrive` chỉ là **simulation** <PERSON><PERSON> chưa implement thật sự upload lên Google Drive.

## Fix Applied
✅ **Fixed uploadContentToGoogleDrive method** - Now uses actual GDrive service instead of simulation

```go
// Before (Simulation)
func (h *CopyFileHandler) uploadContentToGoogleDrive(ctx context.Context, sessionToken string, content []byte) error {
    // TODO: Implement actual Google Drive content upload
    // For now, simulate successful upload
    log.Infof("Google Drive content upload simulated successfully")
    return nil
}

// After (Real Implementation)
func (h *CopyFileHandler) uploadContentToGoogleDrive(ctx context.Context, sessionToken string, content []byte) error {
    // Get Google Drive document service
    gdriveService, err := h.documentRegistry.GetProvider("gdrive")
    
    // Use actual UploadFileContent method
    uploader, ok := gdriveService.(GDriveUploader)
    uploadResult, err := uploader.UploadFileContent(ctx, sessionToken, contentReader, contentType, contentLength)
    
    return nil
}
```

## Testing Steps

### 1. Check Current AutoDoc Rules
```bash
curl -X GET "http://localhost:8088/v3/autodoc/rules" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. Create Test Rule for GDrive
```bash
curl -X POST "http://localhost:8088/v3/autodoc/rules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Test GDrive Copy",
    "description": "Test copying file to Google Drive",
    "trigger_type": "client.create",
    "rule_config": [
      {
        "action_type": "copy_file",
        "source_path": "templates/test.txt",
        "target_path": "{client_folder}/Test File.txt",
        "provider": "internal",
        "target_provider": "gdrive"
      }
    ],
    "is_active": true
  }'
```

### 3. Execute Rule Manually
```bash
curl -X POST "http://localhost:8088/v3/autodoc/rules/{rule_id}/execute" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "event_data": {
      "client_id": 123,
      "client_name": "Test Client",
      "client_code": "TC001"
    }
  }'
```

### 4. Check Logs
```bash
tail -f /usr/local/var/log/runlog_docman.log | grep -E "(copy_file|gdrive|upload)"
```

## Expected Flow

### 1. Rule Execution
- ✅ Rule triggers on client.create event
- ✅ CopyFileHandler.Execute() called
- ✅ Cross-provider copy detected (internal → gdrive)

### 2. File Reading
- ✅ Source file read from internal storage
- ✅ File content loaded into memory

### 3. GDrive Upload Session
- ✅ GDriveUploadProvider.CreateUploadSession() called
- ✅ Google Drive resumable upload URL created
- ✅ Session token generated

### 4. Content Upload (FIXED)
- ✅ uploadContentToGoogleDrive() now uses real implementation
- ✅ Calls gdriveService.UploadFileContent()
- ✅ Uses Google Drive resumable upload API
- ✅ File actually uploaded to GDrive

### 5. Upload Registration
- ✅ RegisterUpload() called to finalize
- ✅ DocumentMapping created (if needed)

## Verification

### Check GDrive UI
1. Navigate to configured Shared Drive
2. Look for client folder structure
3. Verify file exists with correct content

### Check Database
```sql
-- Check upload sessions
SELECT * FROM upload_sessions WHERE provider = 'gdrive' ORDER BY created_at DESC LIMIT 10;

-- Check document mappings
SELECT * FROM document_mapping WHERE provider = 'gdrive' ORDER BY created_at DESC LIMIT 10;
```

### Check Service Logs
```bash
# Look for successful upload messages
grep "Successfully uploaded content to Google Drive" /usr/local/var/log/runlog_docman.log

# Look for any errors
grep -i "error.*gdrive\|gdrive.*error" /usr/local/var/log/runlog_docman.log
```

## Additional Checks

### 1. GDrive Configuration
```bash
curl -X GET "http://localhost:8088/v3/gdrive/config" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. Tenant Root Folder
```bash
curl -X GET "http://localhost:8088/v3/gdrive/folders/root" \
  -H "Authorization: Bearer $TOKEN"
```

### 3. Service Account Permissions
- Verify Service Account has access to Shared Drive
- Check folder permissions in GDrive UI
- Ensure Service Account is not using My Drive (quota issues)

## Next Steps

1. **Test the fix** - Execute autodoc rule and verify file appears in GDrive
2. **Monitor logs** - Check for any remaining issues
3. **Verify folder creation** - Ensure folders are also created properly
4. **Test different file types** - Try various file formats
5. **Performance testing** - Check upload speed for larger files

## Related Files Modified
- ✅ `internal/service/autodoc/copy_file_handler.go` - Fixed uploadContentToGoogleDrive
- ✅ Added proper GDrive service integration
- ✅ Added error handling and logging
