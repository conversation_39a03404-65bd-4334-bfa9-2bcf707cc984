package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/transport"
)

// MockGlobClient for testing without real glob storage
type MockGlobClient struct{}

func (m *MockGlobClient) GetFileContent(ctx context.Context, key, userID string) ([]byte, error) {
	return nil, fmt.Errorf("mock glob client - not implemented")
}

func (m *MockGlobClient) UploadFile(ctx context.Context, filename string, content io.Reader, userID, tenantID, userRoles string) (*transport.UploadResult, error) {
	return &transport.UploadResult{
		Key:       "mock-upload-key",
		UploadURL: "mock-upload-url",
	}, nil
}

func (m *MockGlobClient) EmitDownload(ctx context.Context, key string, userID string) (*transport.DownloadResp, error) {
	return nil, fmt.Errorf("mock glob client - not implemented")
}

func (m *MockGlobClient) GetObjectSize(ctx context.Context, key string, userID string) (fileSize transport.GlobSizeResponse, err error) {
	return transport.GlobSizeResponse{}, fmt.Errorf("mock glob client - not implemented")
}

func (m *MockGlobClient) GetPresignedUploadURL(ctx context.Context, filename string, userID string, tenantID string, userRoles string) (*transport.PresignUploadResp, error) {
	return &transport.PresignUploadResp{
		UploadURL: "mock-presigned-upload-url",
		Key:       "mock-presigned-key",
	}, nil
}

func main() {
	// Command line flags
	var (
		docxPath        = flag.String("file", "", "Path to DOCX file to test (required)")
		placeholdersStr = flag.String("placeholders", "{}", "JSON string of placeholders (optional)")
		outputPath      = flag.String("output", "", "Output path for processed DOCX (optional)")
		validateOnly    = flag.Bool("validate", false, "Only validate the DOCX file without processing")
		analyze         = flag.Bool("analyze", false, "Analyze DOCX structure for debugging")
	)
	flag.Parse()

	// Validate required arguments
	if *docxPath == "" {
		fmt.Println("Usage: test-docx -file <path-to-docx> [options]")
		fmt.Println("\nOptions:")
		flag.PrintDefaults()
		fmt.Println("\nExamples:")
		fmt.Println("  test-docx -file template.docx -validate")
		fmt.Println("  test-docx -file template.docx -placeholders '{\"client.name\":\"John Doe\",\"matter.id\":\"12345\"}'")
		fmt.Println("  test-docx -file template.docx -output processed.docx")
		fmt.Println("  test-docx -file template.docx -analyze")
		os.Exit(1)
	}

	// Setup logging (simplified for CLI tool)
	// Note: bilabllog doesn't have SetLevel method, so we'll use basic logging

	ctx := context.Background()
	log := bilabllog.CreateContextLogger(ctx)

	// Check if file exists
	if _, err := os.Stat(*docxPath); os.IsNotExist(err) {
		log.WithError(err).Fatal("DOCX file does not exist")
	}

	// Read DOCX file
	templateContent, err := os.ReadFile(*docxPath)
	if err != nil {
		log.WithError(err).Fatal("Failed to read DOCX file")
	}

	log.WithField("file_path", *docxPath).
		WithField("file_size", len(templateContent)).
		Info("Successfully loaded DOCX file")

	// Create DOCX processor
	mockGlob := &MockGlobClient{}
	processor := autodoc.NewDocxProcessor(mockGlob)

	// Parse placeholders
	var placeholders map[string]interface{}
	if err := json.Unmarshal([]byte(*placeholdersStr), &placeholders); err != nil {
		log.WithError(err).Fatal("Failed to parse placeholders JSON")
	}

	if len(placeholders) > 0 {
		log.WithField("placeholders", placeholders).Info("Using placeholders")
	}

	// Analyze DOCX structure if requested
	if *analyze {
		fmt.Println("\n=== DOCX Structure Analysis ===")
		analyzeDocxStructure(ctx, processor, templateContent)
		return
	}

	// Validate DOCX if requested
	if *validateOnly {
		fmt.Println("\n=== DOCX Validation ===")
		if err := processor.ValidateTemplate(ctx, templateContent); err != nil {
			log.WithError(err).Error("DOCX validation failed")
			os.Exit(1)
		}
		log.Info("✅ DOCX validation successful")
		return
	}

	// Process DOCX template
	fmt.Println("\n=== DOCX Processing ===")
	processedContent, err := processor.ProcessTemplate(ctx, templateContent, placeholders)
	if err != nil {
		log.WithError(err).Error("DOCX processing failed")
		os.Exit(1)
	}

	log.WithField("processed_size", len(processedContent)).
		Info("✅ DOCX processing successful")

	// Save processed content if output path specified
	if *outputPath != "" {
		if err := os.WriteFile(*outputPath, processedContent, 0644); err != nil {
			log.WithError(err).Error("Failed to save processed DOCX")
			os.Exit(1)
		}
		log.WithField("output_path", *outputPath).
			Info("✅ Processed DOCX saved successfully")
	} else {
		// Generate default output filename
		dir := filepath.Dir(*docxPath)
		base := filepath.Base(*docxPath)
		ext := filepath.Ext(base)
		name := base[:len(base)-len(ext)]
		defaultOutput := filepath.Join(dir, name+"_processed"+ext)

		if err := os.WriteFile(defaultOutput, processedContent, 0644); err != nil {
			log.WithError(err).Error("Failed to save processed DOCX")
			os.Exit(1)
		}
		log.WithField("output_path", defaultOutput).
			Info("✅ Processed DOCX saved to default location")
	}

	fmt.Println("\n🎉 DOCX processing completed successfully!")
}

// analyzeDocxStructure performs detailed analysis of DOCX structure
func analyzeDocxStructure(ctx context.Context, processor *autodoc.DocxProcessor, content []byte) {
	log := bilabllog.CreateContextLogger(ctx)

	fmt.Printf("File Size: %d bytes\n", len(content))

	if len(content) < 4 {
		fmt.Println("❌ File too small to be a valid DOCX")
		return
	}

	// Check ZIP signature
	header := content[:4]
	fmt.Printf("File Header: %x\n", header)

	if string(header) == "PK\x03\x04" {
		fmt.Println("✅ Valid ZIP signature detected")
	} else {
		fmt.Println("❌ Invalid ZIP signature - not a valid DOCX file")
		return
	}

	// Try to validate with processor
	if err := processor.ValidateTemplate(ctx, content); err != nil {
		log.WithError(err).Error("DOCX validation failed during analysis")
	} else {
		log.Info("✅ DOCX validation passed")
	}

	fmt.Println("\nFor detailed ZIP structure analysis, check debug logs with -verbose flag")
}
