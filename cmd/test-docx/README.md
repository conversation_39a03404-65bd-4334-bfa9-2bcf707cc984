# DOCX Template Testing Tool

A command-line tool for testing DOCX template processing functionality from the autodoc service.

## Build

```bash
go build -o test-docx ./cmd/test-docx/
```

## Usage

### Basic Commands

```bash
# Validate a DOCX file
./test-docx -file template.docx -validate

# Analyze DOCX structure for debugging
./test-docx -file template.docx -analyze

# Process template with placeholders
./test-docx -file template.docx -placeholders '{"client.name":"John Doe","matter.id":"12345"}'

# Process and save to specific output
./test-docx -file template.docx -output processed.docx
```

### Options

- `-file`: Path to DOCX file to test (required)
- `-placeholders`: JSON string of placeholders (optional, default: `{}`)
- `-output`: Output path for processed DOCX (optional, auto-generated if not specified)
- `-validate`: Only validate the DOCX file without processing
- `-analyze`: Analyze DOCX structure for debugging

## Examples

### 1. Validate Template

```bash
./test-docx -file /path/to/template.docx -validate
```

**Expected Output:**
```
✅ DOCX validation successful
```

### 2. Debug Invalid DOCX

```bash
./test-docx -file /path/to/corrupted.docx -analyze
```

**Expected Output:**
```
=== DOCX Structure Analysis ===
File Size: 1234 bytes
File Header: 504b0304
✅ Valid ZIP signature detected
❌ DOCX validation failed during analysis
```

### 3. Process Template with Placeholders

```bash
./test-docx -file template.docx -placeholders '{
  "client.name": "John Doe",
  "client.email": "<EMAIL>",
  "matter.id": "M-12345",
  "matter.title": "Contract Review",
  "date": "2024-01-15"
}'
```

**Expected Output:**
```
✅ DOCX processing completed successfully!
✅ Processed DOCX saved to template_processed.docx
```

## Debugging DOCX Issues

### Common Error: "invalid docx archive, word/document.xml is missing"

Use the analyze flag to debug:

```bash
./test-docx -file problematic.docx -analyze
```

The tool will show:
- File size and header information
- ZIP signature validation
- Detailed DOCX structure analysis (in debug logs)

### Error Analysis

1. **File too small**: Content is empty or truncated
2. **Invalid ZIP signature**: File is not a valid ZIP/DOCX
3. **Missing word/document.xml**: DOCX structure is corrupted
4. **Validation failed**: go-docx library cannot parse the file

## Integration with AutoDoc Service

This tool uses the same `DocxProcessor` from the autodoc service, so results should match the behavior in the actual service.

### Mock vs Real Environment

- **Mock Glob Client**: Used for testing without real storage
- **Same Processing Logic**: Uses identical DOCX processing code
- **Enhanced Error Analysis**: Includes improved debugging features

## Troubleshooting

### Build Issues

```bash
# Ensure you're in the project root
cd /path/to/docman

# Clean and rebuild
go clean ./cmd/test-docx/
go build -o test-docx ./cmd/test-docx/
```

### Runtime Issues

1. **File not found**: Check file path is correct
2. **Permission denied**: Ensure read access to input file and write access to output directory
3. **Invalid JSON**: Validate placeholders JSON syntax

### Debug Mode

For detailed logging, check the source code and enable debug logging in the bilabllog configuration.

## Development

To add new features:

1. Modify `cmd/test-docx/main.go`
2. Use the same patterns as existing autodoc service
3. Test with various DOCX files
4. Update this README

## Related Files

- `internal/service/autodoc/docx_processor.go`: Core DOCX processing logic
- `pkg/transport/glob.go`: Glob storage interface
- `internal/service/autodoc/docx_processor_test.go`: Unit tests
