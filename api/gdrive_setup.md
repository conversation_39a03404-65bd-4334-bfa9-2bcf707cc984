# Google Drive Integration Setup

This document outlines the steps to configure and enable Google Drive integration for document management. The setup process involves two main API calls: testing the setup and completing the setup.

> **Note**: Google Drive integration has been consolidated under the V3 API. Previous V1 endpoints (`/v1/integrations/google-drive/`) have been removed. All Google Drive functionality is now available under the unified `/v3/gdrive/` namespace.

## Step 1: Test Setup

Before saving any configuration, it's crucial to verify that the service account has the correct permissions for the target Shared Drive or Folder.

### Endpoint

`POST /v3/gdrive/setup/test-setup`

### Purpose

This endpoint checks the validity of a Google Drive URL/ID and confirms that the application's service account has sufficient permissions (at least `Viewer`) to list its contents.

### Request Body

```json
{
  "url_or_id": "string"
}
```

-   `url_or_id`: The full URL of the Google Shared Drive/Folder, or just its unique ID.

### Example cURL

Here's an example of how to call the endpoint using `curl`. This is useful for quick testing from your terminal.

```bash
curl --location 'http://localhost:8088/v3/gdrive/setup/test-setup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <YOUR_ADMIN_TOKEN>' \
--data '{
    "url_or_id": "<YOUR_GOOGLE_DRIVE_URL_OR_ID>"
}'
```
> **Note:**
> - Replace `<YOUR_GOOGLE_DRIVE_URL_OR_ID>` with the actual URL or ID of your Shared Drive or folder
> - Replace `<YOUR_ADMIN_TOKEN>` with a valid JWT token from a user with Admin, Manager, or Sysadmin roles

### Responses

-   **`200 OK`**: The connection is successful. The service account has the required permissions. The response body will contain information about the resource.

    ```json
    {
        "data": {
            "status": "success",
            "drive_info": {
                "id": "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
                "name": "My Target Folder",
                "type": "folder",
                "drive_id": "0ABCdefG1hIjKlMnOpQrStUvWxYz"
            }
        }
    }
    ```

-   **`400 Bad Request`**: The provided `url_or_id` is invalid or could not be parsed into a recognizable Google Drive ID.

    ```json
    {
        "error": {
            "err_code": 400,
            "msg": "Invalid Google Drive URL or ID. Please provide a valid URL or the resource ID."
        }
    }
    ```

-   **`401 Unauthorized`**: The request was valid, but the service account does not have permission to access the resource. Please ensure the service account email has been granted at least "Viewer" access to the target Shared Drive or Folder.

    ```json
    {
        "error": {
            "err_code": 401,
            "msg": "Unauthorized"
        }
    }
    ```

## Step 2: Complete Setup

After successfully testing the setup, use this endpoint to save the Google Drive configuration for your tenant.

### Endpoint

`POST /v3/gdrive/setup/complete-setup`

### Purpose

This endpoint saves the Google Drive configuration for the authenticated tenant. It stores all configuration details (resource ID, type, drive ID for shared drives, and enabled status) as a single JSON configuration in the database.

### Authentication

This endpoint requires authentication with **Admin, Manager, or Sysadmin roles**. Include a valid JWT token in the Authorization header.

### Request Body

```json
{
  "url_or_id": "string",
  "enabled": true
}
```

-   `url_or_id`: The same Google Drive URL or ID that was successfully tested in Step 1.
-   `enabled`: Boolean flag to enable/disable the Google Drive integration. Set to `true` to enable, `false` to disable.

### Example cURL

```bash
curl --location 'http://localhost:8088/v3/gdrive/setup/complete-setup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <YOUR_ADMIN_TOKEN>' \
--data '{
    "url_or_id": "<YOUR_GOOGLE_DRIVE_URL_OR_ID>",
    "enabled": true
}'
```

> **Note:**
> - Replace `<YOUR_ADMIN_TOKEN>` with a valid JWT token from a user with Admin, Manager, or Sysadmin roles
> - Replace `<YOUR_GOOGLE_DRIVE_URL_OR_ID>` with the same URL or ID used in the test setup

### Responses

-   **`200 OK`**: The configuration has been successfully saved for your tenant.

    ```json
    {
        "data": {
            "status": "success",
            "message": "Google Drive integration has been configured successfully.",
            "enabled": true,
            "state": "enabled"
        }
    }
    ```

-   **`400 Bad Request`**: The provided `url_or_id` is invalid or could not be parsed.

    ```json
    {
        "error": {
            "err_code": 400,
            "msg": "Invalid Google Drive URL or ID. Please provide a valid URL or the resource ID."
        }
    }
    ```

-   **`401 Unauthorized`**: Authentication is required or the provided credentials are invalid.

    ```json
    {
        "error": {
            "err_code": 401,
            "msg": "Unauthorized"
        }
    }
    ```

-   **`500 Internal Server Error`**: An error occurred while saving the configuration.

    ```json
    {
        "error": {
            "err_code": 500,
            "msg": "failed to save gdrive configuration"
        }
    }
    ```

## Complete Workflow Example

Here's a complete example of the two-step setup process:

```bash
# Step 1: Test the setup
curl --location 'http://localhost:8088/v3/gdrive/setup/test-setup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer your_admin_token_here' \
--data '{
    "url_or_id": "https://drive.google.com/drive/folders/1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_"
}'

# If successful (200 OK), proceed to Step 2: Complete the setup (Enable)
curl --location 'http://localhost:8088/v3/gdrive/setup/complete-setup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer your_admin_token_here' \
--data '{
    "url_or_id": "https://drive.google.com/drive/folders/1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
    "enabled": true
}'

# To disable the integration (keeping the configuration but disabling it)
curl --location 'http://localhost:8088/v1/integrations/google-drive/complete-setup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer your_auth_token_here' \
--data '{
    "url_or_id": "https://drive.google.com/drive/folders/1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
    "enabled": false
}'
```

## What Gets Stored

When you complete the setup, the system stores a single Google Drive configuration per tenant with the key `gdrive_config`. The configuration is stored as JSON and contains the following fields:

### Configuration Structure

```json
{
  "enabled": true,
  "root_id": "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
  "drive_id": "0ABCdefG1hIjKlMnOpQrStUvWxYz",
  "resource_type": "folder"
}
```

### Field Descriptions

- **`enabled`**: Boolean flag indicating whether Google Drive integration is active for the tenant
- **`root_id`**: The unique identifier of your chosen folder or shared drive (the main resource)
- **`drive_id`**: The shared drive ID (only present for resources within shared drives)
- **`resource_type`**: The type of resource - can be "shared_drive", "folder", or "file"

### Storage Examples by Resource Type

#### Shared Drive Configuration
```json
{
  "enabled": true,
  "root_id": "0ABCdefG1hIjKlMnOpQrStUvWxYz",
  "drive_id": "0ABCdefG1hIjKlMnOpQrStUvWxYz",
  "resource_type": "shared_drive"
}
```

#### Folder in Shared Drive Configuration
```json
{
  "enabled": true,
  "root_id": "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
  "drive_id": "0ABCdefG1hIjKlMnOpQrStUvWxYz",
  "resource_type": "folder"
}
```

#### My Drive Folder Configuration
```json
{
  "enabled": true,
  "root_id": "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_",
  "resource_type": "folder"
}
```
*Note: `drive_id` is omitted for My Drive resources as it's not needed for API operations.*

> **Important**: The `drive_id` field is crucial for Google Drive API operations on shared drives, as many API calls require the `driveId` parameter for proper access control and file operations. The system automatically detects when this field is needed and includes it in the configuration.

These settings are saved per tenant and will be used by the application for all future Google Drive operations.

## Supported URL Formats

The following URL formats are supported for both endpoints:

- Full shared drive URLs: `https://drive.google.com/drive/folders/{id}`
- User-specific URLs: `https://drive.google.com/drive/u/0/folders/{id}`
- Direct resource IDs: `{id}` (just the ID string)
- Various other Google Drive URL patterns

The system automatically extracts the resource ID from any of these formats. 
