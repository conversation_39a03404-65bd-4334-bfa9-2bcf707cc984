openapi: 3.0.3
info:
  title: docman
  version: v3.0.0
  description: |
    Hour Glass document management services with comprehensive AutoDoc automation capabilities.

    ## Features
    - **Multi-Provider Support**: Internal, Google Drive, and SharePoint integration
    - **Cross-Provider Operations**: Copy files/folders between different providers
    - **Automation Rules**: Event-driven document processing with flexible rule engine
    - **Target Provider Resolution**: Automatic provider selection with tenant defaults
    - **Provider Management**: Health monitoring and configuration management
    - **File Operations**: Upload, download, search, and metadata management
    - **Role-Based Access**: Admin, Manager, and Staff permission levels

    ## Authentication
    All endpoints require Bearer token authentication unless specified otherwise.

    ## Rate Limiting
    API requests are rate-limited per tenant and user role.

    ## Versioning
    - **v1**: Legacy endpoints (deprecated)
    - **v2**: SharePoint-specific endpoints
    - **v3**: Current API with AutoDoc automation features
tags:
  - name: google-drive-setup
    description: Google Drive integration setup and configuration (Admin only)
  - name: google-drive-documents
    description: Google Drive document management operations
  - name: integrations
    description: Third-party service integrations
  - name: autodoc-rules
    description: AutoDoc automation rule management
  - name: autodoc-providers
    description: AutoDoc provider configuration and health monitoring
  - name: autodoc-files
    description: AutoDoc file management operations
  - name: documents
    description: Document management operations
servers:
- url: https://{environment}.myhrglass.net/docman
  variables:
    environment:
      default: api-stg
      enum:
        - api
        - api-stg
paths:
  /v1/documents:
    get:
      operationId: documents_list
      description: 'get document list'
      parameters:
      - name: object_id
        required: false
        in: query
        description: Filter by object_id
        schema:
          type: integer
      - name: object_type
        required: false
        in: query
        description: Filter by object_type, separated by comma , e.g. 6,8,10
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      - in: query
        name: status
        schema:
          type: integer
          nullable: true
      - in: query
        name: sort
        schema:
          type: string
          nullable: true
          enum:
          - short_name
          - name
          - email
          - created_at
      tags:
      - documents
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Document'
                  meta:
                    type: object
                    properties:
                      pages:
                        type: integer
                      page:
                        type: integer
                      page_size:
                        type: integer
                      total:
                        type: integer

          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'
    post:
      operationId: documents_create
      description: 'document body'
      tags:
      - documents
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        '201':
          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'
  /v1/documents/{id}:
    get:
      operationId: documents_retrieve
      description: 'get document detail'
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - documents
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: '#/components/schemas/Document'
          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'
    patch:
      operationId: documents_update
      description: ''
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - documents
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        '204':
          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'
    delete:
      operationId: documents_delete
      description: 'delete document'
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - documents
      responses:
        '204':
          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'
  /v1/documents/{id}/downloads:
    get:
      operationId: documents_download
      description: 'get document detail'
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - documents
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: '#/components/schemas/DocumentDownload'
          description: 'success'
        '400':
          description: 'client error'
        '403':
          description: 'unauthorized'

  /share/download:
    post:
      operationId: downloadShare
      description: 'download share'
      tags:
        - share
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pass_code:
                  type: string
                object_type:
                  type: string
                  description: |
                    object type, supported: `billing`
                object_id:
                  type: integer
                  description: object id
              required:
                - pass_code
                - object_type
                - object_id
      responses:
        200:
          description: 'success'
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        description: |
                          URL with the token included to get file content.

  /v3/documents:
    post:
      operationId: createDocumentV3
      description: 'create document'
      tags:
        - documents
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                object_id:
                  type: number
                  format: uint64
                object_type:
                  type: integer
                  description: |
                    - 1: client
                    - 2: staff
                    - 3: matter
                    - 9: task
                doc_type:
                  type: integer
                  description: |
                    - 1: folder
                    - 2: file
                parent_id:
                  type: number
                  format: uint64
                  description: |
                    Parent ID (optional), can use parent_path instead
                parent_path:
                  type: string
                  description: |
                    Parent path, e.g., /Correspondence (parent_id will be ignored if this is set)
              required:
                - name
                - object_id
                - object_type
                - doc_type
      responses:
        201:
          description: success

  /v3/gdrive/setup/test-setup:
    post:
      operationId: gdrive_test_setup_v3
      summary: Test Google Drive Setup (V3)
      description: |
        Tests the connection and permissions for a given Google Drive Shared Drive or Folder URL/ID.
        This endpoint verifies that the service account has the necessary access to list files within the specified resource.
        Requires Admin, Manager, or Sysadmin roles.
      tags:
        - google-drive-setup
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GdriveTestSetupV3Request'
        required: true
      responses:
        '200':
          description: 'Connection successful. Returns information about the Drive/Folder.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveTestSetupV3Response'
        '400':
          description: 'Bad Request. The provided URL/ID is invalid or could not be parsed.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. User does not have required admin permissions.'
        '500':
          description: 'Internal Server Error. Failed to test Google Drive connection.'

  /v3/gdrive/setup/complete-setup:
    post:
      operationId: gdrive_complete_setup_v3
      summary: Complete Google Drive Setup (V3)
      description: |
        Completes the Google Drive integration setup for the tenant by saving the configuration.
        This endpoint enables or disables the Google Drive integration based on the request.
        Requires Admin, Manager, or Sysadmin roles.
      tags:
        - google-drive-setup
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GdriveCompleteSetupV3Request'
        required: true
      responses:
        '200':
          description: 'Setup completed successfully.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveCompleteSetupV3Response'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. User does not have required admin permissions.'
        '500':
          description: 'Internal Server Error. Failed to save Google Drive configuration.'

  /v3/gdrive/documents:
    post:
      operationId: gdrive_create_document_v3
      summary: Create Google Drive Document/Folder (V3)
      description: |
        Creates a new folder in Google Drive. This endpoint is part of the unified V3 Google Drive API.
      tags:
        - google-drive-documents
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GdriveCreateDocumentV3Request'
        required: true
      responses:
        '201':
          description: 'Document/folder created successfully.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveCreateDocumentV3Response'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. Insufficient permissions.'
        '500':
          description: 'Internal Server Error. Failed to create document.'

    get:
      operationId: gdrive_list_documents_v3
      summary: List Google Drive Documents (V3)
      description: |
        Lists documents and folders in a specified parent folder with optional search functionality.
      tags:
        - google-drive-documents
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: query
          description: "Direct parent folder ID"
          schema:
            type: string
        - name: object_id
          in: query
          description: "Object ID for mapping lookup"
          schema:
            type: string
        - name: object_type
          in: query
          description: "Object type for mapping lookup"
          schema:
            type: string
        - name: search
          in: query
          description: "Search query"
          schema:
            type: string
        - name: page_size
          in: query
          description: "Number of items per page (default: 100, max: 1000)"
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
        - name: page_token
          in: query
          description: "Pagination token"
          schema:
            type: string
      responses:
        '200':
          description: 'Documents listed successfully.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveListDocumentV3Response'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. Insufficient permissions.'
        '500':
          description: 'Internal Server Error. Failed to list documents.'

  /v3/gdrive/documents/{document_id}:
    patch:
      operationId: gdrive_update_document_v3
      summary: Update Google Drive Document (V3)
      description: |
        Updates a document or folder name in Google Drive.
      tags:
        - google-drive-documents
      security:
        - bearerAuth: []
      parameters:
        - name: document_id
          in: path
          required: true
          description: "Document/folder ID"
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GdriveUpdateDocumentV3Request'
        required: true
      responses:
        '200':
          description: 'Document updated successfully.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveUpdateDocumentV3Response'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. Insufficient permissions.'
        '404':
          description: 'Not Found. Document does not exist.'
        '500':
          description: 'Internal Server Error. Failed to update document.'

    delete:
      operationId: gdrive_delete_document_v3
      summary: Delete Google Drive Document (V3)
      description: |
        Deletes a document or folder from Google Drive.
      tags:
        - google-drive-documents
      security:
        - bearerAuth: []
      parameters:
        - name: document_id
          in: path
          required: true
          description: "Document/folder ID"
          schema:
            type: string
      responses:
        '204':
          description: 'Document deleted successfully.'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. Insufficient permissions.'
        '404':
          description: 'Not Found. Document does not exist.'
        '500':
          description: 'Internal Server Error. Failed to delete document.'

  /v3/gdrive/documents/upload:
    post:
      operationId: gdrive_upload_document_v3
      summary: Generate Upload URL (V3)
      description: |
        Generates a resumable upload URL for client-side file uploads to Google Drive.
      tags:
        - google-drive-documents
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GdriveUploadV3Request'
        required: true
      responses:
        '200':
          description: 'Upload URL generated successfully.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GdriveUploadV3Response'
        '400':
          description: 'Bad Request. Invalid request parameters.'
        '401':
          description: 'Unauthorized. Missing or invalid authentication token.'
        '403':
          description: 'Forbidden. Insufficient permissions.'
        '500':
          description: 'Internal Server Error. Failed to generate upload URL.'

  # AutoDoc API v3 - Automation Rules Management
  /v3/autodoc/rules:
    post:
      operationId: createAutoDocRule
      summary: Create automation rule
      description: Create a new automation rule for document processing
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAutoDocRuleRequest'
      responses:
        '201':
          description: Rule created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocRule'
        '400':
          description: Bad Request - Invalid rule configuration
        '401':
          description: Unauthorized - Missing or invalid token
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error
    get:
      operationId: listAutoDocRules
      summary: List automation rules
      description: Retrieve list of automation rules with filtering and pagination
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          required: false
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          required: false
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          required: false
          description: Filter by rule status
          schema:
            type: string
            enum: [active, inactive, draft]
        - name: provider
          in: query
          required: false
          description: Filter by provider
          schema:
            type: string
            enum: [internal, gdrive, sharepoint]
      responses:
        '200':
          description: Rules retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocRuleListResponse'
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/rules/active:
    get:
      operationId: getActiveAutoDocRules
      summary: Get active automation rules
      description: Retrieve all currently active automation rules
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Active rules retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AutoDocRule'
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/rules/{id}:
    get:
      operationId: getAutoDocRule
      summary: Get automation rule by ID
      description: Retrieve a specific automation rule by its ID
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Rule ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Rule retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocRule'
        '404':
          description: Rule not found
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    put:
      operationId: updateAutoDocRule
      summary: Update automation rule
      description: Update an existing automation rule
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Rule ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAutoDocRuleRequest'
      responses:
        '200':
          description: Rule updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocRule'
        '400':
          description: Bad Request - Invalid rule configuration
        '404':
          description: Rule not found
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error
    delete:
      operationId: deleteAutoDocRule
      summary: Delete automation rule
      description: Delete an automation rule by its ID
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Rule ID
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Rule deleted successfully
        '404':
          description: Rule not found
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error

  /v3/autodoc/rules/{id}/execute:
    post:
      operationId: executeAutoDocRule
      summary: Execute automation rule
      description: Manually execute a specific automation rule with provided event data
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Rule ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExecuteRuleRequest'
      responses:
        '200':
          description: Rule executed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecuteRuleResponse'
        '400':
          description: Bad Request - Invalid execution parameters
        '404':
          description: Rule not found
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error

  # AutoDoc Template Variables
  /v3/autodoc/variables:
    get:
      operationId: listAutoDocVariables
      summary: List template variables
      description: |
        Get curated template variables for document generation in `{field.name}` format.

        **Variable Categories:**
        - **client**: 20 curated client fields in `{client.field}` format (id, name, email, etc.)
        - **special**: Special placeholders ({client_folder})
        - **timestamp**: Time-based placeholders ({timestamp}, {date})

        **Key Features:**
        - ✅ **Curated**: Only 23 most important variables (vs 126+ previously)
        - ✅ **Template-ready**: `{client.name}` format for direct template usage
        - ✅ **Performance**: Single API call with filtered important fields

        **Usage Modes:**
        - **Schema-only**: Without object_id, returns 23 variable definitions with null values
        - **Data-populated**: With object_id, returns 23 variables with real data from the specified object

        **Recent Improvements (v3):**
        - Reduced from 126+ variables to 23 curated important fields
        - Consistent `{client.field}` template format for easy usage
        - Single API call performance optimization
        - Removed complex/rarely used fields (addresses arrays, banks, etc.)
      tags:
        - autodoc-rules
      security:
        - bearerAuth: []
      parameters:
        - name: object_type
          in: query
          required: true
          description: |
            Business object type:
            - `1`: Client (returns 23 curated client variables)
            - `3`: Matter (returns curated matter + client variables)
          schema:
            type: integer
            enum: [1, 3]
            example: 1
          example: 1
        - name: object_id
          in: query
          required: false
          description: |
            Specific object ID to get real values from.
            - **Without object_id**: Returns 23 variable definitions with null values (schema-only mode)
            - **With object_id**: Returns 23 variables with actual data from the specified object
          schema:
            type: integer
            format: int64
            minimum: 1
          example: 534
      responses:
        '200':
          description: |
            Variables retrieved successfully.
            Returns 23 curated variables in template-ready format.
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      variables:
                        type: array
                        description: Array of 23 curated template variables
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              description: Variable name in template format (e.g., {client.name}, {client_folder})
                              example: "{client.name}"
                            value:
                              description: Variable value (null in schema-only mode, populated with real data when object_id provided)
                              oneOf:
                                - type: string
                                - type: number
                                - type: boolean
                                - type: "null"
                              example: "bilabl engineering team 002"
                            description:
                              type: string
                              description: Human-readable description of the variable
                              example: "Client company name"
                            category:
                              type: string
                              enum: ["client", "matter", "special", "timestamp"]
                              description: Variable category
                              example: "client"
                          required:
                            - name
                            - value
                            - description
                            - category
                      object_type:
                        type: integer
                        enum: [1, 3]
                        description: Business object type (1=client, 3=matter)
                        example: 1
                      object_id:
                        type: integer
                        format: int64
                        description: Object ID (only present when provided in request)
                        example: 534
                    required:
                      - variables
                      - object_type
              examples:
                client_schema_only:
                  summary: Client variables (schema-only)
                  description: Get 23 curated client variable definitions without real data
                  value:
                    data:
                      variables:
                        - name: "{client.id}"
                          value: null
                          description: "Client ID"
                          category: "client"
                        - name: "{client.name}"
                          value: null
                          description: "Client company name"
                          category: "client"
                        - name: "{client.short_name}"
                          value: null
                          description: "Client short name"
                          category: "client"
                        - name: "{client.code}"
                          value: null
                          description: "Client code"
                          category: "client"
                        - name: "{client.email}"
                          value: null
                          description: "Client email address"
                          category: "client"
                        - name: "{client_folder}"
                          value: null
                          description: "Special placeholder that resolves to client's root folder"
                          category: "special"
                        - name: "{timestamp}"
                          value: null
                          description: "Current timestamp (YYYYMMDD_HHMMSS)"
                          category: "timestamp"
                        - name: "{date}"
                          value: null
                          description: "Current date (YYYY-MM-DD)"
                          category: "timestamp"
                      object_type: 1
                client_with_data:
                  summary: Client variables with real data
                  description: Get 23 curated client variables populated with real data
                  value:
                    data:
                      variables:
                        - name: "{client.id}"
                          value: 534
                          description: "Client ID"
                          category: "client"
                        - name: "{client.name}"
                          value: "bilabl engineering team 002"
                          description: "Client name"
                          category: "client"
                        - name: "{client.short_name}"
                          value: "MYBIL"
                          description: "Short name"
                          category: "client"
                        - name: "{client.code}"
                          value: "C00037"
                          description: "Client code"
                          category: "client"
                        - name: "{client.email}"
                          value: "<EMAIL>"
                          description: "Email address"
                          category: "client"
                        - name: "{client_folder}"
                          value: "object:1:534:1"
                          description: "Special placeholder that resolves to client's root folder"
                          category: "special"
                        - name: "{timestamp}"
                          value: "20250820_200517"
                          description: "Current timestamp (YYYYMMDD_HHMMSS)"
                          category: "timestamp"
                        - name: "{date}"
                          value: "2025-08-20"
                          description: "Current date (YYYY-MM-DD)"
                          category: "timestamp"
                      object_type: 1
                      object_id: 534
                matter_with_data:
                  summary: Matter variables with real data
                  description: Get curated matter variables populated with real data (includes client context)
                  value:
                    data:
                      variables:
                        - name: "{matter.id}"
                          value: 12088
                          description: "Matter ID"
                          category: "matter"
                        - name: "{matter.name}"
                          value: "Patent Application Review"
                          description: "Matter name"
                          category: "matter"
                        - name: "{matter.code}"
                          value: "MAT001"
                          description: "Matter code"
                          category: "matter"
                        - name: "{client.name}"
                          value: "bilabl engineering team 002"
                          description: "Client company name"
                          category: "client"
                        - name: "{client.code}"
                          value: "C00037"
                          description: "Client code"
                          category: "client"
                        - name: "{matter_folder}"
                          value: "object:3:12088:1"
                          description: "Special placeholder that resolves to matter's root folder"
                          category: "special"
                        - name: "{client_folder}"
                          value: "object:1:534:1"
                          description: "Special placeholder that resolves to client's root folder"
                          category: "special"
                      object_type: 3
                      object_id: 12088
        '400':
          description: Bad Request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_object_type:
                  summary: Invalid object_type
                  value:
                    error:
                      detail: "object_type must be 1 (client) or 3 (matter)"
                invalid_object_id:
                  summary: Invalid object_id
                  value:
                    error:
                      detail: "object_id must be greater than 0"
        '401':
          description: Unauthorized - Missing or invalid authentication
        '404':
          description: Not Found - Object with specified ID does not exist
        '500':
          description: Internal Server Error

  # AutoDoc Provider Management
  /v3/autodoc/providers:
    get:
      operationId: listAutoDocProviders
      summary: List available providers
      description: Get list of all available document providers and their status
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Providers retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AutoDocProvider'
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/providers/default:
    put:
      operationId: setDefaultAutoDocProvider
      summary: Set default provider
      description: Set the default provider for new document operations
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider:
                  type: string
                  enum: [internal, gdrive, sharepoint]
                  description: Provider name to set as default
              required:
                - provider
      responses:
        '200':
          description: Default provider updated successfully
        '400':
          description: Bad Request - Invalid provider name
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error

  /v3/autodoc/providers/health:
    get:
      operationId: getAutoDocProvidersHealth
      summary: Get all providers health status
      description: Check health status of all configured providers
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Health status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  providers:
                    type: array
                    items:
                      $ref: '#/components/schemas/AutoDocProviderHealth'
                  overall_status:
                    type: string
                    enum: [healthy, degraded, unhealthy]
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/providers/{provider}:
    get:
      operationId: getAutoDocProviderInfo
      summary: Get provider information
      description: Get detailed information about a specific provider
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      parameters:
        - name: provider
          in: path
          required: true
          description: Provider name
          schema:
            type: string
            enum: [internal, gdrive, sharepoint]
      responses:
        '200':
          description: Provider information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocProvider'
        '404':
          description: Provider not found
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/providers/{provider}/health:
    get:
      operationId: getAutoDocProviderHealth
      summary: Get provider health status
      description: Check health status of a specific provider
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      parameters:
        - name: provider
          in: path
          required: true
          description: Provider name
          schema:
            type: string
            enum: [internal, gdrive, sharepoint]
      responses:
        '200':
          description: Health status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocProviderHealth'
        '404':
          description: Provider not found
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /v3/autodoc/providers/{provider}/test:
    post:
      operationId: testAutoDocProviderConnection
      summary: Test provider connection
      description: Test connectivity and authentication with a specific provider
      tags:
        - autodoc-providers
      security:
        - bearerAuth: []
      parameters:
        - name: provider
          in: path
          required: true
          description: Provider name
          schema:
            type: string
            enum: [internal, gdrive, sharepoint]
      responses:
        '200':
          description: Connection test successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [success, failed]
                  message:
                    type: string
                  response_time_ms:
                    type: integer
                  tested_at:
                    type: string
                    format: date-time
        '400':
          description: Bad Request - Invalid provider
        '404':
          description: Provider not found
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  # AutoDoc File Management
  /v3/autodoc/files:
    get:
      operationId: listAutoDocFiles
      summary: List files
      description: List files and folders with filtering and pagination
      tags:
        - autodoc-files
      security:
        - bearerAuth: []
      parameters:
        - name: parent_id
          in: query
          required: false
          description: Parent folder ID to list files from
          schema:
            type: integer
            format: int64
        - name: path
          in: query
          required: false
          description: Folder path to list files from (alternative to parent_id)
          schema:
            type: string
        - name: provider
          in: query
          required: false
          description: Provider to use for file operations
          schema:
            type: string
            enum: [internal, gdrive, sharepoint]
        - name: file_type
          in: query
          required: false
          description: Filter by file type
          schema:
            type: string
            enum: [file, folder, all]
            default: all
        - name: recursive
          in: query
          required: false
          description: Include files from subfolders
          schema:
            type: boolean
            default: false
        - name: page
          in: query
          required: false
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          required: false
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sort_by
          in: query
          required: false
          description: Sort field
          schema:
            type: string
            enum: [name, created_at, updated_at, size]
            default: name
        - name: sort_order
          in: query
          required: false
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Files retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocFileListResponse'
        '400':
          description: Bad Request - Invalid parameters
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    post:
      operationId: createAutoDocFile
      summary: Create folder
      description: Create a new folder in the specified location (only folder creation supported, doc_type=1)
      tags:
        - autodoc-files
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutoDocCreateFileRequest'
      responses:
        '201':
          description: Folder created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocCreateFileResponse'
        '400':
          description: Bad Request - Invalid folder parameters or unsupported doc_type
        '401':
          description: Unauthorized
        '413':
          description: Payload Too Large - File size exceeds limit
        '500':
          description: Internal Server Error

  /v3/autodoc/files/{id}:
    get:
      operationId: getAutoDocFile
      summary: Get file information
      description: Get detailed information about a specific file or folder
      tags:
        - autodoc-files
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: File ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: File information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocFile'
        '404':
          description: File not found
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    put:
      operationId: updateAutoDocFile
      summary: Update file
      description: Update file or folder properties
      tags:
        - autodoc-files
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: File ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutoDocFileUpdateRequest'
      responses:
        '200':
          description: File updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoDocFile'
        '400':
          description: Bad Request - Invalid update parameters
        '404':
          description: File not found
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error
    delete:
      operationId: deleteAutoDocFile
      summary: Delete file
      description: Delete a file or folder
      tags:
        - autodoc-files
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: File ID
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: File deleted successfully
        '404':
          description: File not found
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '500':
          description: Internal Server Error

components:
  schemas:
    Document:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          maxLength: 1024
        parent_id:
          type: integer
        object_type:
          type: integer
        object_id:
          type: integer
        sub_object_id:
          type: integer
        name:
          type: string
        key:
          type: string
        type:
          type: integer
        doc_type:
          type: integer
        status:
          type: integer
        waiver_with:
          type: array
          items:
            type: integer
          nullable: true
        note:
          type: string
        size:
          type: integer
      required:
      - id
      - name
    # V3 Google Drive Setup Schemas
    GdriveTestSetupV3Request:
      type: object
      properties:
        url_or_id:
          type: string
          description: "The URL or ID of the Google Shared Drive or Folder to test."
          example: "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
      required:
        - url_or_id

    GdriveTestSetupV3Response:
      type: object
      properties:
        status:
          type: string
          description: "Operation status"
          example: "success"
        drive_info:
          $ref: '#/components/schemas/GdriveInfo'
        message:
          type: string
          description: "Success message"
          example: "Google Drive connection test successful"
      required:
        - status
        - drive_info
        - message

    GdriveCompleteSetupV3Request:
      type: object
      properties:
        url_or_id:
          type: string
          description: "The URL or ID of the Google Shared Drive or Folder to configure."
          example: "https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        enabled:
          type: boolean
          description: "Enable or disable the Google Drive integration"
          example: true
      required:
        - url_or_id
        - enabled

    GdriveCompleteSetupV3Response:
      type: object
      properties:
        status:
          type: string
          description: "Operation status"
          example: "success"
        message:
          type: string
          description: "Success or error message"
          example: "Google Drive integration has been enabled successfully"
        enabled:
          type: boolean
          description: "Current enabled state"
          example: true
        state:
          type: string
          description: "Human-readable state"
          example: "enabled"
          enum: ["enabled", "disabled"]
      required:
        - status
        - message
        - enabled
        - state

    GdriveInfo:
      type: object
      properties:
        id:
          type: string
          description: "The ID of the Google Drive resource."
          example: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        name:
          type: string
          description: "The name of the Google Drive resource."
          example: "My Shared Drive"
        type:
          type: string
          description: "The type of Google Drive resource."
          example: "folder"
          enum: ["folder", "drive"]
      required:
        - id
        - name
        - type

    # V3 Google Drive Document Schemas
    GdriveCreateDocumentV3Request:
      type: object
      properties:
        parent_id:
          type: string
          description: "Parent folder ID"
          example: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        name:
          type: string
          description: "Document/folder name"
          example: "New Project Folder"
        object_id:
          type: integer
          format: int64
          description: "Optional object ID for mapping"
          example: 123
        object_type:
          type: string
          description: "Optional object type for mapping"
          example: "client"
      required:
        - parent_id
        - name

    GdriveCreateDocumentV3Response:
      type: object
      properties:
        id:
          type: string
          description: "Created document ID"
          example: "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_"
      required:
        - id

    GdriveListDocumentV3Response:
      type: object
      properties:
        cwd:
          $ref: '#/components/schemas/GdriveDocumentV3'
        data:
          type: array
          items:
            $ref: '#/components/schemas/GdriveDocumentV3'
        meta:
          $ref: '#/components/schemas/GdrivePaginationMeta'
      required:
        - cwd
        - data
        - meta

    GdriveDocumentV3:
      type: object
      properties:
        id:
          type: string
          description: "Drive file ID"
          example: "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_"
        name:
          type: string
          description: "Document name"
          example: "Project Document.pdf"
        parent_id:
          type: integer
          format: int64
          description: "Parent ID (for compatibility)"
          example: 0
        doc_type:
          type: integer
          description: "Document type (1=folder, 2=file)"
          example: 2
        object_type:
          type: integer
          description: "Object type (for compatibility)"
          example: 0
        object_id:
          type: integer
          format: int64
          description: "Object ID (for compatibility)"
          example: 0
        is_file:
          type: boolean
          description: "True if file, false if folder"
          example: true
        is_internal:
          type: boolean
          description: "Internal flag (always false for Google Drive)"
          example: false
        has_child:
          type: boolean
          description: "True if folder has children"
          example: false
        size:
          type: integer
          format: int64
          description: "File size in bytes"
          example: 1024
        type:
          type: string
          description: "File extension/type"
          example: "pdf"
        date_created:
          type: string
          format: date-time
          description: "Creation date"
          example: "2023-01-01T00:00:00Z"
        date_modified:
          type: string
          format: date-time
          description: "Last modified date"
          example: "2023-01-01T00:00:00Z"
        last_modified_by:
          type: string
          description: "Last modifier name"
          example: "Google Drive User"
        web_url:
          type: string
          description: "Web view URL"
          example: "https://drive.google.com/file/d/1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_/view"
        status:
          type: integer
          description: "Status (always 1 for active)"
          example: 1
      required:
        - id
        - name
        - is_file
        - size
        - type
        - date_created
        - date_modified
        - status

    GdrivePaginationMeta:
      type: object
      properties:
        next_page:
          type: string
          description: "Next page token"
          example: "eyJwYWdlIjoyLCJzaXplIjoxMDB9"
        page_size:
          type: integer
          description: "Current page size"
          example: 100
        total:
          type: integer
          description: "Total number of items"
          example: 250
        has_next:
          type: boolean
          description: "Whether there are more pages"
          example: true
      required:
        - page_size
        - total
        - has_next

    GdriveUpdateDocumentV3Request:
      type: object
      properties:
        name:
          type: string
          description: "New document name"
          example: "Updated Document Name"
        object_id:
          type: integer
          format: int64
          description: "Optional object ID for mapping"
          example: 123
        object_type:
          type: string
          description: "Optional object type for mapping"
          example: "client"
      required:
        - name

    GdriveUpdateDocumentV3Response:
      type: object
      properties:
        id:
          type: string
          description: "Updated document ID"
          example: "1aBCdE2fGhI3jKlM4nOpQ5rStU6vWxYz_"
        name:
          type: string
          description: "Updated document name"
          example: "Updated Document Name"
      required:
        - id
        - name

    GdriveUploadV3Request:
      type: object
      properties:
        id:
          type: string
          description: "Parent folder ID"
          example: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        file_name:
          type: string
          description: "File name for upload"
          example: "document.pdf"
      required:
        - id
        - file_name

    GdriveUploadV3Response:
      type: object
      properties:
        upload_url:
          type: string
          description: "Generated upload URL"
          example: "https://www.googleapis.com/upload/drive/v3/files/file123?uploadType=resumable"
      required:
        - upload_url

    # Legacy V1 Schema (deprecated)
    GdriveTestSetupRequest:
      type: object
      deprecated: true
      description: "Deprecated: Use GdriveTestSetupV3Request instead"
      properties:
        url_or_id:
          type: string
          description: "The URL or ID of the Google Shared Drive or Folder to test."
      required:
        - url_or_id
    ConfigTypeEnum:
      enum:
      - DOCUMENT_TYPE
      type: string
    DocumentDownload:
      type: object
      properties:
        url:
          type: string
          description: |
            URL with the token included to get file content.

    # AutoDoc Schemas
    AutoDocRule:
      type: object
      properties:
        id:
          type: integer
          format: int64
          readOnly: true
          description: Unique rule identifier
        tenant_id:
          type: integer
          format: int64
          description: Tenant ID
        name:
          type: string
          description: Rule name
          maxLength: 255
        description:
          type: string
          description: Rule description
          maxLength: 1000
        trigger_type:
          type: string
          enum: [matter.create, matter.update, client.create, client.update]
          description: Event type that triggers this rule
        trigger_rules:
          type: object
          description: Rule conditions in JSON format for event matching
          additionalProperties: true
        rule_config:
          type: array
          description: Array of actions to execute when rule matches
          items:
            $ref: '#/components/schemas/RuleAction'
        is_active:
          type: boolean
          description: Whether the rule is active
          default: false
        created_user:
          type: integer
          format: int64
          description: User ID who created the rule
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
        - name
        - trigger_type
        - trigger_rules
        - rule_config
        - is_active

    RuleAction:
      type: object
      properties:
        action_type:
          type: string
          enum: [copy_file, copy_folder, generate_document]
          description: Type of action to execute
        source_path:
          type: string
          description: Path to source file/folder from AutoDocRoot
          maxLength: 500
        target_path:
          type: string
          description: Target path with placeholders (e.g., {client_folder}/Document.docx)
          maxLength: 500
        provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Source provider (defaults to "internal" if not specified)
          default: internal
        target_provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Target provider (uses tenant default_dms if not specified)
      required:
        - action_type
        - source_path
        - target_path
      example:
        action_type: copy_file
        source_path: templates/contract.docx
        target_path: "{client_folder}/Contract.docx"
        provider: internal
        target_provider: gdrive

    CreateAutoDocRuleRequest:
      type: object
      properties:
        name:
          type: string
          description: Rule name
          maxLength: 255
        description:
          type: string
          description: Rule description
          maxLength: 1000
        trigger_type:
          type: string
          enum: [matter.create, matter.update, client.create, client.update]
          description: Event type that triggers this rule
        trigger_rules:
          type: object
          description: Rule conditions in JSON format for event matching
          additionalProperties: true
        rule_config:
          type: array
          description: Array of actions to execute when rule matches
          items:
            $ref: '#/components/schemas/RuleAction'
          minItems: 1
        is_active:
          type: boolean
          description: Whether the rule is active
          default: false
        created_user:
          type: integer
          format: int64
          description: User ID creating the rule
      required:
        - name
        - trigger_type
        - trigger_rules
        - rule_config
      example:
        name: "Client Folder Creation"
        description: "Create client folder when client is created"
        trigger_type: "client.create"
        trigger_rules: {}
        rule_config:
          - action_type: "copy_folder"
            source_path: "templates/client-folder"
            target_path: "{client_folder}"
            provider: "internal"
            target_provider: "gdrive"
        is_active: true

    UpdateAutoDocRuleRequest:
      type: object
      properties:
        name:
          type: string
          description: Rule name
          maxLength: 255
        description:
          type: string
          description: Rule description
          maxLength: 1000
        trigger_type:
          type: string
          enum: [matter.create, matter.update, client.create, client.update]
          description: Event type that triggers this rule
        trigger_rules:
          type: object
          description: Rule conditions in JSON format for event matching
          additionalProperties: true
        rule_config:
          type: array
          description: Array of actions to execute when rule matches
          items:
            $ref: '#/components/schemas/RuleAction'
          minItems: 1
        is_active:
          type: boolean
          description: Whether the rule is active
      description: All fields are optional for partial updates
      example:
        name: "Updated Client Folder Creation"
        description: "Updated description"
        is_active: true
        rule_config:
          - action_type: "copy_folder"
            source_path: "templates/updated-client-folder"
            target_path: "{client_folder}"
            provider: "internal"
            target_provider: "gdrive"

    AutoDocRuleListResponse:
      type: object
      properties:
        rules:
          type: array
          items:
            $ref: '#/components/schemas/AutoDocRule'
        total:
          type: integer
          minimum: 0
          description: Total number of rules
      required:
        - rules
        - total
      example:
        rules:
          - id: 1
            name: "Client Folder Creation"
            trigger_type: "client.create"
            is_active: true
        total: 1

    AutoDocProvider:
      type: object
      properties:
        name:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Provider name
        display_name:
          type: string
          description: Human-readable provider name
        description:
          type: string
          description: Provider description
        status:
          type: string
          enum: [active, inactive, error]
          description: Provider status
        is_default:
          type: boolean
          description: Whether this is the default provider
        capabilities:
          type: array
          items:
            type: string
            enum: [create, read, update, delete, search, upload, download, permissions]
          description: Supported operations
        configuration:
          type: object
          description: Provider-specific configuration
          additionalProperties: true
        health:
          $ref: '#/components/schemas/AutoDocProviderHealth'
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
        - name
        - display_name
        - status
        - capabilities

    AutoDocProviderHealth:
      type: object
      properties:
        provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Provider name
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          description: Health status
        last_check:
          type: string
          format: date-time
          description: Last health check timestamp
        response_time_ms:
          type: integer
          minimum: 0
          description: Last response time in milliseconds
        error_message:
          type: string
          description: Error message if unhealthy
          nullable: true
        uptime_percentage:
          type: number
          format: float
          minimum: 0
          maximum: 100
          description: Uptime percentage over last 24 hours
        metrics:
          type: object
          properties:
            total_requests:
              type: integer
              minimum: 0
            successful_requests:
              type: integer
              minimum: 0
            failed_requests:
              type: integer
              minimum: 0
            average_response_time_ms:
              type: number
              format: float
              minimum: 0
          description: Performance metrics
      required:
        - provider
        - status
        - last_check

    AutoDocFile:
      type: object
      properties:
        id:
          type: integer
          format: int64
          readOnly: true
          description: File ID
        name:
          type: string
          description: File or folder name
          maxLength: 255
        path:
          type: string
          description: Full file path
          readOnly: true
        parent_id:
          type: integer
          format: int64
          description: Parent folder ID
          nullable: true
        file_type:
          type: string
          enum: [file, folder]
          description: File type
        mime_type:
          type: string
          description: MIME type for files
          nullable: true
        size:
          type: integer
          format: int64
          minimum: 0
          description: File size in bytes
          nullable: true
        provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Storage provider
        external_id:
          type: string
          description: External provider file ID
          nullable: true
        tenant_id:
          type: integer
          format: int64
          description: Tenant ID
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        created_by:
          type: integer
          format: int64
          description: User ID who created the file
        updated_by:
          type: integer
          format: int64
          description: User ID who last updated the file
        metadata:
          type: object
          description: Additional file metadata
          additionalProperties: true
      required:
        - name
        - file_type
        - provider

    AutoDocFileListResponse:
      type: object
      properties:
        files:
          type: array
          items:
            $ref: '#/components/schemas/AutoDocFile'
        pagination:
          type: object
          properties:
            page:
              type: integer
              minimum: 1
            limit:
              type: integer
              minimum: 1
            total_count:
              type: integer
              minimum: 0
            total_pages:
              type: integer
              minimum: 0
            has_next:
              type: boolean
            has_prev:
              type: boolean
        parent_info:
          type: object
          properties:
            id:
              type: integer
              format: int64
            name:
              type: string
            path:
              type: string
          description: Information about the parent folder
          nullable: true
      required:
        - files
        - pagination

    AutoDocFileUploadRequest:
      type: object
      properties:
        name:
          type: string
          description: File name
          maxLength: 255
        parent_id:
          type: integer
          format: int64
          description: Parent folder ID
          nullable: true
        parent_path:
          type: string
          description: Parent folder path (alternative to parent_id)
          nullable: true
        provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Storage provider
          default: internal
        content_type:
          type: string
          description: File content type
        size:
          type: integer
          format: int64
          minimum: 0
          description: File size in bytes
        metadata:
          type: object
          description: Additional file metadata
          additionalProperties: true
      required:
        - name
        - content_type
        - size

    AutoDocCreateFileRequest:
      type: object
      properties:
        name:
          type: string
          description: Folder name
          maxLength: 255
        doc_type:
          type: integer
          description: Document type (1 = folder, only folders supported)
          enum: [1]
        parent_id:
          type: integer
          format: int64
          description: Parent folder ID (internal)
          nullable: true
        parent_path:
          type: string
          description: Parent folder path (alternative to parent_id)
          nullable: true
        provider:
          type: string
          enum: [internal, gdrive, sharepoint]
          description: Storage provider
          default: internal
        object_type:
          type: integer
          description: Associated business object type
          nullable: true
        object_id:
          type: integer
          format: int64
          description: Associated business object ID
          nullable: true
        description:
          type: string
          description: Optional folder description
          nullable: true
      required:
        - name
        - doc_type

    AutoDocCreateFileResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Internal folder ID
        name:
          type: string
          description: Folder name
        doc_type:
          type: integer
          description: Document type (1 = folder)
        parent_id:
          type: integer
          format: int64
          description: Internal parent folder ID
        external_id:
          type: string
          description: External folder ID (for external providers)
          nullable: true
        provider:
          type: string
          description: Storage provider used
        message:
          type: string
          description: Success message
      required:
        - id
        - name
        - doc_type
        - parent_id
        - provider
        - message

    AutoDocFileUpdateRequest:
      type: object
      properties:
        name:
          type: string
          description: New file name
          maxLength: 255
        parent_id:
          type: integer
          format: int64
          description: New parent folder ID
          nullable: true
        metadata:
          type: object
          description: Updated file metadata
          additionalProperties: true

    ExecuteRuleRequest:
      type: object
      properties:
        event_data:
          type: object
          description: Event data to use for rule execution
          additionalProperties: true
      required:
        - event_data
      example:
        event_data:
          client_id: 123
          client_name: "Test Client"
          client_code: "TC001"

    ExecuteRuleResponse:
      type: object
      properties:
        rule_id:
          type: integer
          format: int64
          description: ID of the executed rule
        execution_id:
          type: string
          description: Unique execution identifier
        status:
          type: string
          enum: [completed, failed, partial]
          description: Execution status
        actions_executed:
          type: integer
          minimum: 0
          description: Number of actions successfully executed
        actions_failed:
          type: integer
          minimum: 0
          description: Number of actions that failed
        execution_time_ms:
          type: integer
          minimum: 0
          description: Total execution time in milliseconds
        results:
          type: array
          description: Detailed results for each action
          items:
            type: object
            properties:
              action_type:
                type: string
                enum: [copy_file, copy_folder, generate_document]
              status:
                type: string
                enum: [success, failed]
              source_path:
                type: string
              target_path:
                type: string
              created_documents:
                type: integer
                minimum: 0
              error_message:
                type: string
                nullable: true
      required:
        - rule_id
        - status
        - actions_executed
        - actions_failed
      example:
        rule_id: 1
        execution_id: "exec_abc123"
        status: "completed"
        actions_executed: 1
        actions_failed: 0
        execution_time_ms: 450
        results:
          - action_type: "copy_folder"
            status: "success"
            source_path: "templates/client-folder"
            target_path: "Test Client - TC001"
            created_documents: 5

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authentication. Required for all authenticated endpoints."
