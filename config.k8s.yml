---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env.CONFIGMAP_NAME }}"
  namespace: "{{ env.APP_ENV }}"
data:
  DB_HOST : "{{ env.DB_HOST }}"
  DB_USER : "{{ env.DB_USER }}"
  DB_PASS : "{{ env.DB_PASS }}"
  DB_NAME : "{{ env.DB_NAME }}"
  LOG_LEVEL : "{{ env.LOG_LEVEL }}"
  SP_CLIENT_ID : "{{ env.SP_CLIENT_ID }}"
  SP_CLIENT_SECRET : "{{ env.SP_CLIENT_SECRET }}"
  SP_SCOPE : "{{ env.SP_SCOPE }}"
  LOG_FORMAT: "json"
  GOBILLING_URL: "{{ env.GOBILLING_URL }}"
  PUBLIC_BASE_URL: "{{ env.PUBLIC_BASE_URL }}"
  GOOGLE_CREDENTIALS_BASE64: "{{ env.GOOGLE_CREDENTIALS_BASE64 }}"
  GOOGLE_CREDENTIALS_EMAIL: "{{ env.GOOGLE_CREDENTIALS_EMAIL }}"
  GDRIVE_PERMISSION_ROLE: "{{ env.GDRIVE_PERMISSION_ROLE | default('writer') }}"
  GDRIVE_PERMISSION_RETRY_COUNT: "{{ env.GDRIVE_PERMISSION_RETRY_COUNT | default('3') }}"
  GDRIVE_PERMISSION_RETRY_DELAY_MS: "{{ env.GDRIVE_PERMISSION_RETRY_DELAY_MS | default('1000') }}"
  GDRIVE_SYNC_ON_CREATE: "{{ env.GDRIVE_SYNC_ON_CREATE | default('true') }}"
  GDRIVE_SYNC_ON_UPDATE: "{{ env.GDRIVE_SYNC_ON_UPDATE | default('true') }}"
  GDRIVE_PERMISSION_TIMEOUT: "{{ env.GDRIVE_PERMISSION_TIMEOUT | default('30') }}"
  MATTER_URL: "{{ env.MATTER_URL }}"
  CLIENT_URL: "{{ env.CLIENT_URL }}"
