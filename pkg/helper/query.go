package helper

import (
	"bilabl/docman/domain/model"
	"fmt"
)

// BuildQuery for handler want to get data
func BuildQuery(q string, filters []*model.Filter, sort []*model.Sort, pagination *model.Pagination) *model.Query {
	query := &model.Query{}
	//Search by keyword
	query.SetQ(q)
	//Build Filters
	query.SetFilters(filters)
	//Build sort
	if sort != nil && len(sort) > 0 {
		query.SetSort(sort)
	}
	query.SetPagination(pagination)
	return query
}

// Using for repo query data
// BuildFilters build filters from list fields and value
func BuildFilters(q string, filters []*model.Filter) (fields []string, values []interface{}) {

	if q != "" {
		filter := buildSearchFilter(q)
		filters = append(filters, filter)
	}
	for _, filter := range filters {
		fields = append(fields, fmt.Sprintf("%s %s ?", filter.Key, filter.Method))
		values = append(values, filter.Value)
	}
	return fields, values
}

func buildSearchFilter(q string) *model.Filter {
	if q == "" {
		return nil
	}
	return &model.Filter{
		Key:    "name",
		Value:  fmt.Sprint("%" + q + "%"),
		Method: "LIKE",
	}
}
