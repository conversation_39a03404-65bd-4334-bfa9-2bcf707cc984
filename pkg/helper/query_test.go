package helper

import (
	"bilabl/docman/domain/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestBuildFilters(t *testing.T) {
	t.Run("Test with nil params", func(t *testing.T) {
		fields, values := BuildFilters("", nil)
		assert.Equal(t, []string(nil), fields)
		assert.Equal(t, []interface{}(nil), values)
	})
	t.Run("Test with keyword empty", func(t *testing.T) {
		filters := []*model.Filter{
			{
				Key:    "id",
				Value:  1,
				Method: "<",
			},
		}
		fields, values := BuildFilters("", filters)
		assert.Equal(t, []string{"id < ?"}, fields)
		assert.Equal(t, []interface{}{1}, values)
	})
	t.Run("Test with filters empty", func(t *testing.T) {
		fields, values := BuildFilters("abc", nil)
		assert.Equal(t, []string{"name LIKE ?"}, fields)
		assert.Equal(t, []interface{}{"%abc%"}, values)
	})
}

func TestBuildSearchFilter(t *testing.T) {
	t.Run("Test with keyword empty", func(t *testing.T) {
		actual := buildSearchFilter("")
		require.Nil(t, actual)
	})
	t.Run("Test with keyword has value", func(t *testing.T) {
		expected := &model.Filter{
			Key:    "name",
			Value:  "%abc%",
			Method: "LIKE",
		}
		actual := buildSearchFilter("abc")
		assert.Equal(t, expected, actual)
	})
}
