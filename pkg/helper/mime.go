package helper

import "strings"

var mimeTypes = map[string]string{
	"image/avif":                     ".avif",
	"text/css; charset=utf-8":        ".css",
	"image/gif":                      ".gif",
	"text/html; charset=utf-8":       ".html",
	"image/jpeg":                     ".jpeg",
	"text/javascript; charset=utf-8": ".js",
	"application/json":               ".json",
	"application/pdf":                ".pdf",
	"image/png":                      ".png",
	"image/svg+xml":                  ".svg",
	"application/wasm":               ".wasm",
	"image/webp":                     ".webp",
	"text/xml; charset=utf-8":        ".xml",
	"application/msword":             ".doc",
	"application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
	"application/vnd.ms-excel": ".xls",
	"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
}

func ExtensionByType(mimeType string) string {
	fileExtension := mimeTypes[strings.ToLower(mimeType)]

	return fileExtension
}
