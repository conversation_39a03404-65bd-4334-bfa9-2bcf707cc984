package helper

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStr2Uint64Slice(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		s := "1,2,4"
		expected := []uint64{1, 2, 4}
		assert.Equal(t, expected, Str2Uint64Slice(s))
	})

	t.Run("SkipInvalid", func(t *testing.T) {
		s := "1,2,4,abs"
		expected := []uint64{1, 2, 4}
		assert.Equal(t, expected, Str2Uint64Slice(s))
	})
}
func TestStr2StrSlice(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		s := "a,b,c"
		expected := []string{"a", "b", "c"}
		assert.Equal(t, expected, Str2StrSlice(s))
	})

	t.Run("StrHaveNumber", func(t *testing.T) {
		s := "a,b,c,1"
		expected := []string{"a", "b", "c", "1"}
		assert.Equal(t, expected, Str2StrSlice(s))
	})
}

func TestStr2ByteSlice(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		s := "1,2,4"
		expected := []byte{1, 2, 4}
		assert.Equal(t, expected, Str2ByteSlice(s))
	})

	t.Run("SkipInvalid", func(t *testing.T) {
		s := "1,2,4,abs"
		expected := []byte{1, 2, 4}
		assert.Equal(t, expected, Str2ByteSlice(s))
	})
}

func TestStr2Bool(t *testing.T) {
	t.Run("True", func(t *testing.T) {
		assert.True(t, Str2Bool("true"))
		assert.True(t, Str2Bool("yes"))
		assert.True(t, Str2Bool("1"))
		assert.True(t, Str2Bool("on"))
	})
	t.Run("False", func(t *testing.T) {
		assert.False(t, Str2Bool("true2"))
		assert.False(t, Str2Bool("false"))
		assert.False(t, Str2Bool("0"))
		assert.False(t, Str2Bool("off"))
	})
}
