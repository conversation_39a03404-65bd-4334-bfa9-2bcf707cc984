package helper

import (
	"strconv"
	"strings"
)

func Str2Uint64Slice(s string) []uint64 {
	parts := strings.Split(s, ",")
	var result []uint64

	for _, p := range parts {
		if n, err := strconv.ParseUint(p, 10, 64); err == nil {
			result = append(result, n)
		}
	}

	return result
}

func Str2StrSlice(s string) []string {
	parts := strings.Split(s, ",")
	var result []string

	for _, p := range parts {
		if len(p) > 0 {
			result = append(result, p)
		}
	}

	return result
}

func Str2ByteSlice(s string) []byte {
	resultInInt := Str2Uint64Slice(s)
	var result []byte

	for _, n := range resultInInt {
		result = append(result, byte(n))
	}

	return result
}

func Str2Bool(s string) bool {
	s = strings.ToLower(s)

	return s == "1" || s == "yes" || s == "true" || s == "on"
}
