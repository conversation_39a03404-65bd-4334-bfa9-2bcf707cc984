package sharepointclient

type File struct {
	MimeType string `json:"mimeType"`
}

type Folder struct {
	ChildCount uint64 `json:"childCount"`
}

type UserObject struct {
	Email       string `json:"email"`
	ID          string `json:"id"`
	DisplayName string `json:"displayName"`
}

type ModifyUserObject struct {
	User UserObject `json:"user"`
}

type ParentDrive struct {
	ID      string `json:"id"`
	DriveID string `json:"driveId"`
	Path    string `json:"path"`
}

type DriveItemResponse struct {
	ID                   string           `json:"id"`
	Name                 string           `json:"name"`
	CreatedDateTime      string           `json:"createdDateTime"`
	LastModifiedDateTime string           `json:"lastModifiedDateTime"`
	WebUrl               string           `json:"webUrl"`
	File                 File             `json:"file"`
	Folder               Folder           `json:"folder"`
	Size                 uint64           `json:"size"`
	LastModifiedBy       ModifyUserObject `json:"lastModifiedBy"`
}

type ListDriveItemResponse struct {
	DataContext string               `json:"@odata.context"`
	NextLink    string               `json:"@odata.nextLink"`
	Value       []*DriveItemResponse `json:"value"`
}

type SiteResponse struct {
	Name   string `json:"name"`
	WebUrl string `json:"webUrl"`
	ID     string `json:"ID"`
}

type ListSiteResponse struct {
	DataContext string          `json:"@odata.context"`
	Value       []*SiteResponse `json:"value"`
}

type DriveResponse struct {
	Name   string `json:"name"`
	WebUrl string `json:"webUrl"`
	ID     string `json:"ID"`
}

type ListDriveResponse struct {
	DataContext string           `json:"@odata.context"`
	Value       []*DriveResponse `json:"value"`
}

type RequestUploadBody struct {
	ConflictBehavior string `json:"@microsoft.graph.conflictBehavior"`
	Name             string `json:"name"`
	DeferCommit      bool   `json:"deferCommit"`
}

type RequestUploadResponse struct {
	UploadUrl string `json:"uploadUrl"`
}

type ParentFolder struct {
	ID   string `json:"id"`
	Path string `json:"path"`
}

type MoveDriveItemBody struct {
	ParentReference ParentFolder `json:"parentReference"`
}

type CopyDriveItemBody struct {
	ParentReference ParentFolder `json:"parentReference"`
}

type FolderRequest struct{}

type CreateFolderBody struct {
	Name             string         `json:"name"`
	Folder           *FolderRequest `json:"folder"`
	ConflictBehavior string         `json:"@microsoft.graph.conflictBehavior"`
}

type RenameFolderBody struct {
	Name string `json:"name"`
}

type Recipient struct {
	Email string `json:"email"`
}

type InvitePermissionBody struct {
	Recipients     []Recipient `json:"recipients"`
	Message        string      `json:"message"`
	RequireSignIn  bool        `json:"requireSignIn"`
	SendInvitation bool        `json:"sendInvitation"`
	Roles          []string    `json:"roles"`
}

type PermGrantedTo struct {
	User *PermGrantedToUser `json:"user"`
}

type PermGrantedToUser struct {
	Email string `json:"email"`
}

type PermRespItem struct {
	ID                  string          `json:"id"`
	GrantedTo           *PermGrantedTo  `json:"grantedTo"`
	GrantedToIdentities []PermGrantedTo `json:"grantedToIdentities"`
}
type ListPermissionResponse struct {
	DataContext string          `json:"@odata.context"`
	Value       []*PermRespItem `json:"value"`
}

type ActorUser struct {
	Email             string `json:"email"`
	DisplayName       string `json:"displayName"`
	UserPrincipalName string `json:"userPrincipalName"`
}

type Actor struct {
	User ActorUser `json:"user"`
}

type ActivityTimes struct {
	RecordedDateTime string `json:"recordedDateTime"`
}

type ActivityCreate struct{}
type EditVersion struct {
	NewVersion string `json:"newVersion"`
}

type ActivityEdit struct{}

type ActivityRename struct {
	OldName string `json:"oldName"`
}

type ActivityMove struct {
	From string `json:"from"`
	To   string `json:"to"`
}

type ActivityDelete struct {
	Name       string `json:"name"`
	ObjectType string `json:"objectType"`
}

type ActivityAction struct {
	Edit    ActivityEdit   `json:"edit"`
	Create  ActivityCreate `json:"create"`
	Move    ActivityMove   `json:"move"`
	Delete  ActivityDelete `json:"delete"`
	Rename  ActivityRename `json:"rename"`
	Version EditVersion    `json:"version"`
}

type DriveItem struct {
	ID              string       `json:"id"`
	Name            string       `json:"name"`
	WebUrl          string       `json:"webUrl"`
	ParentReference ParentFolder `json:"parentReference"`
	File            File         `json:"file"`
	Folder          Folder       `json:"folder"`
}

type ActivityResponse struct {
	ID        string         `json:"id"`
	Actor     Actor          `json:"actor"`
	Action    ActivityAction `json:"action"`
	Times     ActivityTimes  `json:"times"`
	DriveItem DriveItem      `json:"driveItem"`
}

type ListActivityResponse struct {
	DataContext string              `json:"@odata.context"`
	NextLink    string              `json:"@odata.nextLink"`
	Value       []*ActivityResponse `json:"value"`
}

type TokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	Error       string `json:"error"`
}
