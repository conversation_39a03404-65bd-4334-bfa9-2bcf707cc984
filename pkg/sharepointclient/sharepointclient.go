package sharepointclient

import (
	"code.mybil.net/gophers/gokit/pkg/httpwrapper"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
)

const (
	BaseURL = "https://graph.microsoft.com/v1.0"

	EndpointSite                      = "/sites"
	EndpointToken                     = "https://login.microsoftonline.com/%s/oauth2/v2.0/token"
	EndpointDriveItemDetail           = "/drives/%s/items/%s"
	EndpointDriveItemChildren         = "/drives/%s/items/%s/children"
	EndpointDriveItemCopy             = "/drives/%s/items/%s/copy"
	EndpointDriveItemUpload           = "/drives/%s/items/%s:/%s:/createUploadSession"
	EndpointDriveList                 = "/sites/%s/drives"
	EndpointDriveItemSearch           = "/drives/%s/items/%s/search(q='%s')?$top=%d&$skiptoken=%s"
	EndpointDriveItemInvitePermission = "/drives/%s/items/%s/invite"
	EndpointDriveItemDeletePermission = "/drives/%s/items/%s/permissions/%s"
	EndpointDriveActivities           = "/drives/%s/activities?$top=%d&$expand=%s&$skiptoken=%s"

	EndpointAdminConsent = "https://login.microsoftonline.com/common/adminconsent?client_id=%s&state=%d&redirect_uri=%s"
)

var (
	ErrNoTokenFound     = errors.New("no token found")
	ErrNoDriveItemFound = errors.New("no drive item found")
)

type HttpClient interface {
	Do(r *http.Request) (*http.Response, error)
}

type Client interface {
	ExchangeTokenBySecret(ctx context.Context, externalTenantId string) (string, error)
	GetDriveItemDetail(ctx context.Context, driveId string, accessToken string, itemId string) (DriveItemResponse, error)
	GetListDriveItemChildren(ctx context.Context, driveId string, accessToken string, itemId string) (ListDriveItemResponse, error)
	GetListSite(ctx context.Context, accessToken string) (ListSiteResponse, error)
	GetListDrive(ctx context.Context, siteId string, accessToken string) (ListDriveResponse, error)
	CreateDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, folderName string) (DriveItemResponse, error)
	RenameDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, changeName string) (DriveItemResponse, error)
	CopyDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) error
	DeleteDriveItem(ctx context.Context, driveId string, accessToken string, itemId string) error
	MoveDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) (DriveItemResponse, error)
	CreateUploadURL(ctx context.Context, driveId string, accessToken string, itemId string, fileName string) (RequestUploadResponse, error)
	SearchDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, str string, pageSize int, skipToken string) (ListDriveItemResponse, error)
	InvitePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, emails []string) (ListPermissionResponse, error)
	DeletePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, permId string) error
	GetListActivity(ctx context.Context, driveId string, accessToken string, skipToken string) (ListActivityResponse, error)

	FilterChildren(ctx context.Context, driveId string, accessToken string, itemId string, name string) (*ListDriveItemResponse, error)
}

type spClientImpl struct {
	options *ClientOptions
}

type ClientOptions struct {
	HttpClient
	ClientID                          string
	ClientSecret                      string
	Scopes                            string
	SharepointAdminConsentRedirectURI string
	wire                              httpwrapper.Client
	baseUrl                           string
}

type Option func(options *ClientOptions)

func NewClient(clientID string, clientSecret string, Scopes string, options ...Option) Client {
	defaultOpts := &ClientOptions{
		HttpClient:   &http.Client{},
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Scopes:       Scopes,
		wire:         httpwrapper.NewDefaultHttpWrapper(),
		baseUrl:      BaseURL,
	}
	for _, opt := range options {
		opt(defaultOpts)
	}

	// override good status behaviour
	defaultOpts.wire.SetStatusFunc(func(statusCode int) bool {
		return statusCode >= 200 && statusCode < 500
	})

	return &spClientImpl{options: defaultOpts}
}

func WithSharepointAdminConsentURI(uri string) Option {
	return func(options *ClientOptions) {
		options.SharepointAdminConsentRedirectURI = uri
	}
}

func WithBaseURL(url string) Option {
	return func(options *ClientOptions) {
		if url != "" {
			options.baseUrl = url
		}
	}
}

func safeClose(f io.Closer) {
	_ = f.Close()
}

func sanitizeString(s string) string {
	//reg := regexp.MustCompile("[^a-zA-Z0-9-.]+")
	//return reg.ReplaceAllString(s, " ")

	return s
}

func (c *spClientImpl) GenerateAdminConsentURL(tenantID uint64) string {
	return fmt.Sprintf(EndpointAdminConsent, c.options.ClientID, tenantID, c.options.SharepointAdminConsentRedirectURI)
}

func (c *spClientImpl) buildURL(endpoint string, params ...interface{}) string {
	if !strings.HasPrefix(endpoint, "http") {
		endpoint = c.options.baseUrl + endpoint
	}
	return fmt.Sprintf(endpoint, params...)
}

func (c *spClientImpl) handleOutRequest(ctx context.Context, req *httpwrapper.Request, output any) error {
	resp, err := c.options.wire.Do(ctx, req, output)
	if err != nil {
		return err
	}

	// custom response code
	switch resp.StatusCode {
	case http.StatusTooManyRequests:
		er := &errRateLimited{
			retryAfter: resp.Header.Get(`Retry-After`),
		}
		return er
	case http.StatusBadRequest:
		return errors.New("bad request")
	}

	return nil
}

func (c *spClientImpl) ExchangeTokenBySecret(ctx context.Context, externalTenantId string) (string, error) {
	query := url.Values{}
	query.Set("grant_type", "client_credentials")
	query.Set("client_id", c.options.ClientID)
	query.Set("scope", c.options.Scopes)
	query.Set("client_secret", c.options.ClientSecret)

	req := httpwrapper.NewRequest(http.MethodPost, c.buildURL(EndpointToken, externalTenantId), strings.NewReader(query.Encode()))
	req.SetHeader("content-type", "application/x-www-form-urlencoded")

	var resp TokenResponse
	if err := c.handleOutRequest(ctx, req, &resp); err != nil {
		return "", err
	}

	return resp.AccessToken, nil
}

func (c *spClientImpl) GetDriveItemDetail(ctx context.Context, driveId string, accessToken string, itemId string) (DriveItemResponse, error) {
	query := url.Values{}
	driveItem := DriveItemResponse{}

	req := httpwrapper.NewRequest(http.MethodGet, c.buildURL(EndpointDriveItemDetail, driveId, itemId), strings.NewReader(query.Encode()))
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")

	if err := c.handleOutRequest(ctx, req, &driveItem); err != nil {
		return DriveItemResponse{}, err
	}

	return driveItem, nil
}

func (c *spClientImpl) GetListDriveItemChildren(ctx context.Context, driveId string, accessToken string, itemId string) (ListDriveItemResponse, error) {
	listDriveItem := ListDriveItemResponse{}

	req := httpwrapper.NewRequest(http.MethodGet, c.buildURL(EndpointDriveItemChildren, driveId, itemId), nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")
	if err := c.handleOutRequest(ctx, req, &listDriveItem); err != nil {
		return ListDriveItemResponse{}, err
	}

	return listDriveItem, nil
}

func (c *spClientImpl) GetListSite(ctx context.Context, accessToken string) (ListSiteResponse, error) {
	listSite := ListSiteResponse{}

	req := httpwrapper.NewRequest(http.MethodGet, c.buildURL(EndpointSite), nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	if err := c.handleOutRequest(ctx, req, &listSite); err != nil {
		return ListSiteResponse{}, err
	}
	return listSite, nil
}

func (c *spClientImpl) GetListDrive(ctx context.Context, siteId string, accessToken string) (ListDriveResponse, error) {
	listDrive := ListDriveResponse{}
	reqUrl := c.buildURL(EndpointDriveList, siteId)

	req := httpwrapper.NewRequest(http.MethodGet, reqUrl, nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")
	if err := c.handleOutRequest(ctx, req, &listDrive); err != nil {
		return ListDriveResponse{}, err
	}

	return listDrive, nil
}

func (c *spClientImpl) CreateDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, folderName string) (DriveItemResponse, error) {
	driveItem := DriveItemResponse{}

	folder := &FolderRequest{}
	reqBody := &CreateFolderBody{
		Name:             sanitizeString(folderName),
		Folder:           folder,
		ConflictBehavior: "rename",
	}

	req := httpwrapper.NewRequestWithData(http.MethodPost, c.buildURL(EndpointDriveItemChildren, driveId, itemId), reqBody)

	req.AddHeader("Authorization", "Bearer "+accessToken)
	req.AddHeader("content-type", "application/json")

	if err := c.handleOutRequest(ctx, req, &driveItem); err != nil {
		return driveItem, err
	}
	return driveItem, nil
}

func (c *spClientImpl) RenameDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, changeName string) (DriveItemResponse, error) {
	driveItem := DriveItemResponse{}

	reqBody := &RenameFolderBody{
		Name: sanitizeString(changeName),
	}

	req := httpwrapper.NewRequestWithData(http.MethodPatch, c.buildURL(EndpointDriveItemDetail, driveId, itemId), reqBody)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/json")

	if err := c.handleOutRequest(ctx, req, &driveItem); err != nil {
		return driveItem, err
	}

	return driveItem, nil
}

func (c *spClientImpl) DeleteDriveItem(ctx context.Context, driveId string, accessToken string, itemId string) error {
	req := httpwrapper.NewRequest(http.MethodDelete, c.buildURL(EndpointDriveItemDetail, driveId, itemId), nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")
	return c.handleOutRequest(ctx, req, nil)
}

func (c *spClientImpl) MoveDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) (DriveItemResponse, error) {
	driveItem := DriveItemResponse{}

	parentFolder := ParentFolder{
		ID: newItemId,
	}
	reqBody := &MoveDriveItemBody{
		ParentReference: parentFolder,
	}

	req := httpwrapper.NewRequestWithData(http.MethodPatch, c.buildURL(EndpointDriveItemDetail, driveId, itemId), reqBody)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/json")

	if err := c.handleOutRequest(ctx, req, &driveItem); err != nil {
		return driveItem, err
	}

	return driveItem, nil
}

func (c *spClientImpl) CopyDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) error {
	parentFolder := ParentFolder{
		ID: newItemId,
	}
	reqBody := &CopyDriveItemBody{
		ParentReference: parentFolder,
	}

	req := httpwrapper.NewRequestWithData(http.MethodPost, c.buildURL(EndpointDriveItemCopy, driveId, itemId), reqBody)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/json")

	return c.handleOutRequest(ctx, req, nil)
}

func (c *spClientImpl) SearchDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, str string, pageSize int, skipToken string) (ListDriveItemResponse, error) {
	listDriveItem := ListDriveItemResponse{}

	req := httpwrapper.NewRequest(http.MethodGet, c.buildURL(EndpointDriveItemSearch, driveId, itemId, str, pageSize, skipToken), nil)

	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")
	err := c.handleOutRequest(ctx, req, &listDriveItem)

	return listDriveItem, err
}

func (c *spClientImpl) InvitePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, emails []string) (ListPermissionResponse, error) {
	permissionItem := ListPermissionResponse{}

	var recipient []Recipient
	for _, email := range emails {
		recipient = append(recipient, Recipient{Email: email})
	}

	reqBody := &InvitePermissionBody{
		Recipients:     recipient,
		Message:        "(bilabl app) Invite permission",
		RequireSignIn:  true,
		SendInvitation: false,
		Roles:          []string{"write"},
	}

	req := httpwrapper.NewRequestWithData(http.MethodPost, c.buildURL(EndpointDriveItemInvitePermission, driveId, itemId), reqBody)

	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/json")

	err := c.handleOutRequest(ctx, req, &permissionItem)

	return permissionItem, err
}

func (c *spClientImpl) DeletePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, permId string) error {
	req := httpwrapper.NewRequest(http.MethodDelete, c.buildURL(EndpointDriveItemDeletePermission, driveId, itemId, permId), nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")

	return c.handleOutRequest(ctx, req, nil)
}

func (c *spClientImpl) CreateUploadURL(ctx context.Context, driveId string, accessToken string, itemId string, fileName string) (RequestUploadResponse, error) {
	upload := RequestUploadResponse{}

	reqBody := &RequestUploadBody{
		ConflictBehavior: "rename",
		Name:             fileName,
		DeferCommit:      false,
	}

	req := httpwrapper.NewRequestWithData(http.MethodPost, c.buildURL(EndpointDriveItemUpload, driveId, itemId, fileName), reqBody)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/json")
	err := c.handleOutRequest(ctx, req, &upload)

	return upload, err
}

func (c *spClientImpl) GetListActivity(ctx context.Context, driveId string, accessToken string, skipToken string) (ListActivityResponse, error) {
	listActivity := ListActivityResponse{}

	req := httpwrapper.NewRequest("GET", c.buildURL(EndpointDriveActivities, driveId, 20, "driveItem($select=id,name,webUrl,parentReference,file,folder)", skipToken), nil)

	req.SetHeader("Authorization", "Bearer "+accessToken)
	req.SetHeader("content-type", "application/x-www-form-urlencoded")

	err := c.handleOutRequest(ctx, req, &listActivity)

	return listActivity, err
}

func (c *spClientImpl) FilterChildren(ctx context.Context, driveId string, accessToken string, itemId string, name string) (*ListDriveItemResponse, error) {

	values := url.Values{}
	values.Add(`$filter`, fmt.Sprintf(`name eq '%s'`, name))
	reqUrl := c.buildURL(`/drives/%s/items/%s/children?`, driveId, itemId) + values.Encode()
	req := httpwrapper.NewRequest(http.MethodGet, reqUrl, nil)
	req.SetHeader("Authorization", "Bearer "+accessToken)
	resp := &ListDriveItemResponse{}
	r, err := c.options.wire.Do(ctx, req, resp)

	// handle case not found (no issue)
	if err != nil && r != nil && r.StatusCode == 404 {
		return resp, nil
	}
	return resp, err
}
