package bilabllog

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateContextLogger(t *testing.T) {
	t.Run("Run CreateContextLogger success", func(t *testing.T) {
		myContext := context.Background()
		log := CreateContextLogger(myContext)
		assert.NotEqual(t, log, nil)
	})
}
func TestCtxLog(t *testing.T) {
	t.Run("Run CtxLog success", func(t *testing.T) {
		myContext := context.Background()
		parent := CreateContextLogger(myContext)

		requestID := "n/a"
		if v := myContext.Value("x-request-id"); v != nil {
			requestID = v.(string)
		}
		log := CtxLog(myContext, parent, "TestCtxLog")
		assert.Equal(t, log.Data["tag"], "TestCtxLog")
		assert.Equal(t, log.Data["x-request-id"], requestID)
	})

}
