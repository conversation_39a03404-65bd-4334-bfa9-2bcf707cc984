package bilabllog

import (
	"context"
	"fmt"
	"runtime"

	"github.com/sirupsen/logrus"
	"gitlab.com/goxp/cloud0/logger"
)

func CreateContextLogger(ctx context.Context) *logrus.Entry {
	pc, file, no, _ := runtime.Caller(1)
	details := runtime.FuncForPC(pc)

	log := logger.WithCtx(ctx, details.Name())
	log.WithField("ref", fmt.Sprintf("%s:%d", file, no))

	return log
}

func CtxLog(ctx context.Context, parent *logrus.Entry, tag string) *logrus.Entry {
	requestID := "n/a"
	if v := ctx.Value("x-request-id"); v != nil {
		requestID = v.(string)
	}

	return parent.WithField("tag", tag).WithField("x-request-id", requestID)
}
