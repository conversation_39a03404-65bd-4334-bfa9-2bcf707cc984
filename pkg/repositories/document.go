package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/pkg/db"

	"github.com/sirupsen/logrus"
	"gitlab.com/goxp/cloud0/ginext"
	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
)

var (
	ErrInvalidTenant = ginext.NewError(400, "invalid TenantRepository")
)

type options struct {
	log *logrus.Entry
}

type documentRepositoryImpl struct {
	*db.BaseRepository
	*options
	db *gorm.DB
}

type Option func(opts *options)

func NewDocumentRepo(
	conn *gorm.DB,
	opts ...Option,
) DocumentRepository {
	r := &documentRepositoryImpl{
		BaseRepository: db.NewBaseRepository(conn),
		options: &options{
			log: logger.DefaultBaseEntry,
		},
	}
	for _, o := range opts {
		o(r.options)
	}

	// backward compatibility
	r.db = r.BaseRepository.Tx(context.Background())

	return r
}

func (r *documentRepositoryImpl) CreateDoc(ctx context.Context, doc *model.Document) (err error) {
	log := bilabllog.CreateContextLogger(ctx)

	if doc.TenantID == 0 {
		return ErrInvalidTenant
	}

	var tx = r.db.WithContext(ctx)
	_, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	defer func() {
		if err != nil {
			log.WithError(err).Info("rollback create document transaction")
			tx.Rollback()
			return
		}

		tx.Commit()
	}()

	tx = tx.Begin()

	err = tx.Create(&doc).Error
	if err != nil {
		log.WithError(err).Error("error while creating document")
		err = ginext.NewError(500, "error while creating document")
		return
	}

	return nil
}

func (r *documentRepositoryImpl) Find(ctx context.Context, query *model.Query, pager *ginext.Pager) (documents []*model.Document, err error) {
	log := bilabllog.CreateContextLogger(ctx)

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()
	if query.Q != "" {
		term := "%" + strings.ToLower(query.Q) + "%"
		searchColumns := []string{"name", "note"}
		sub := r.db
		for _, c := range searchColumns {
			sub = sub.Or(fmt.Sprintf("LOWER(%s) LIKE ?", c), term)
		}
		tx = tx.Where(sub)
	}

	// Default sort by doc_type (asc) and updated_at (desc)
	if pager.Sort == "" {
		pager.Sort = "doc_type, -updated_at"
	}

	err = pager.DoQuery(&documents, tx).Error
	if err != nil {
		log.WithError(err).Error("error while querying users")
		err = ginext.NewError(http.StatusInternalServerError, "db error")
		return nil, err
	}

	return documents, err
}

func (r *documentRepositoryImpl) List(ctx context.Context, query *model.Query) (documents []*model.Document, err error) {
	log := bilabllog.CreateContextLogger(ctx)

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Begin()

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()

	if query.Q != "" {
		term := "%" + strings.ToLower(query.Q) + "%"
		searchColumns := []string{"name", "note"}
		sub := r.db
		for _, c := range searchColumns {
			sub = sub.Or(fmt.Sprintf("LOWER(%s) LIKE ?", c), term)
		}
		tx = tx.Where(sub)
	}

	tx.Order("doc_type ASC, updated_at DESC, id DESC")

	err = tx.Find(&documents).Error
	return documents, err
}

func (r *documentRepositoryImpl) FindOne(ctx context.Context, query *model.Query) (document *model.Document, err error) {
	log := bilabllog.CreateContextLogger(ctx)

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.
		Where(strings.Join(fields, " AND "), values...).
		Where("deleted_at IS NULL")

	err = tx.First(&document).Error
	if err != nil {
		if model.IsNotFound(err) {
			log.WithError(err).Debug("Document not found (expected for new documents)")
			return nil, ginext.NewError(http.StatusNotFound, "document not found")
		}
		log.WithError(err).Error("Error executing document query")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return document, err
}

// Exists checks if a document exists based on the given query without retrieving the full document
func (r *documentRepositoryImpl) Exists(ctx context.Context, query *model.Query) (exists bool, err error) {
	log := bilabllog.CreateContextLogger(ctx)

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.
		Where(strings.Join(fields, " AND "), values...).
		Where("deleted_at IS NULL")

	var count int64
	err = tx.Count(&count).Error
	if err != nil {
		log.WithError(err).Error("Error checking document existence")
		return false, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return count > 0, nil
}

func (r *documentRepositoryImpl) UpdateOne(ctx context.Context, query *model.Query, document *model.Document) (err error) {
	log := bilabllog.CreateContextLogger(ctx)
	getDoc := &model.Document{}

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)
	tx = tx.Where(strings.Join(fields, " AND "), values...)
	err = tx.First(&getDoc).Error
	if err != nil {
		if model.IsNotFound(err) {
			log.WithError(err).Debug("Document not found for update")
			return ginext.NewError(http.StatusNotFound, "document not found")
		}
		log.WithError(err).Error("Error executing document query for update")
		return ginext.NewError(http.StatusBadRequest, err.Error())
	}

	tx = r.db.WithContext(ctx).
		Model(&model.Document{})
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()
	document.UpdatedAt = time.Now()

	return tx.Updates(&document).Error
}

func (r *documentRepositoryImpl) CreateOrUpdate(ctx context.Context, query *model.Query, document *model.Document) (err error) {
	log := bilabllog.CreateContextLogger(ctx)
	getDoc := &model.Document{}

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)
	tx = tx.Where(strings.Join(fields, " AND "), values...)
	err = tx.First(&getDoc).Error
	if err != nil {
		r.CreateDoc(ctx, document)
	}

	tx = r.db.WithContext(ctx).
		Model(&model.Document{})
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()
	document.UpdatedAt = time.Now()

	return tx.Updates(&document).Error
}

func (r *documentRepositoryImpl) DeleteOne(ctx context.Context, query *model.Query) (err error) {
	log := bilabllog.CreateContextLogger(ctx)
	var document *model.Document

	tx := r.db.WithContext(ctx).
		Model(&model.Document{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)
	tx = tx.Where(strings.Join(fields, " AND "), values...)
	err = tx.First(&document).Error
	if err != nil {
		log.WithError(err).Error(fmt.Sprintf("error whiling querying document: %v", document.ID))
		if model.IsNotFound(err) {
			return ginext.NewError(http.StatusNotFound, "document not found")
		}
		return ginext.NewError(http.StatusBadRequest, err.Error())
	}

	tx = r.db.WithContext(ctx).
		Model(&model.Document{})
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()

	return tx.Delete(&model.Document{}, "id = ?", document.ID).Error
}
