package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/pkg/db"

	"gitlab.com/goxp/cloud0/ginext"
	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
)

// documentAutomationRuleRepositoryImpl implements DocumentAutomationRuleRepository
type documentAutomationRuleRepositoryImpl struct {
	*db.BaseRepository
	*options
	db *gorm.DB
}

// NewDocumentAutomationRuleRepository creates a new instance of DocumentAutomationRuleRepository
func NewDocumentAutomationRuleRepository(
	conn *gorm.DB,
	opts ...Option,
) DocumentAutomationRuleRepository {
	r := &documentAutomationRuleRepositoryImpl{
		BaseRepository: db.NewBaseRepository(conn),
		options: &options{
			log: logger.DefaultBaseEntry,
		},
	}
	for _, o := range opts {
		o(r.options)
	}

	// backward compatibility
	r.db = r.BaseRepository.Tx(context.Background())

	return r
}

// CreateRule creates a new document automation rule
func (r *documentAutomationRuleRepositoryImpl) CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	log := bilabllog.CreateContextLogger(ctx)

	if rule.TenantID == 0 {
		return ErrInvalidTenant
	}

	tx := r.db.WithContext(ctx)
	_, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	tx = tx.Begin()
	defer func() {
		if err := recover(); err != nil {
			log.WithField("error", err).Error("panic during create automation rule transaction")
			tx.Rollback()
			return
		}
	}()

	err := tx.Select("*").Create(rule).Error
	if err != nil {
		log.WithError(err).Error("error while creating automation rule")
		tx.Rollback()
		return ginext.NewError(500, "error while creating automation rule")
	}

	return tx.Commit().Error
}

// FindByID finds an automation rule by ID with tenant isolation
func (r *documentAutomationRuleRepositoryImpl) FindByID(ctx context.Context, id uint64, tenantID uint64) (*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rule model.DocumentAutomationRule
	tx := r.db.WithContext(ctx)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	// SECURITY: Enforce tenant isolation
	err := tx.Where("id = ? AND tenant_id = ?", id, tenantID).First(&rule).Error
	if err != nil {
		if model.IsNotFound(err) {
			log.WithField("rule_id", id).WithField("tenant_id", tenantID).Info("automation rule not found or access denied")
			return nil, ginext.NewError(http.StatusNotFound, "automation rule not found")
		}
		log.WithError(err).Error("error while querying automation rule")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return &rule, nil
}

// FindByTenantID finds all automation rules for a tenant
func (r *documentAutomationRuleRepositoryImpl) FindByTenantID(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rules []*model.DocumentAutomationRule
	tx := r.db.WithContext(ctx)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	err := tx.Where("tenant_id = ?", tenantID).Find(&rules).Error
	if err != nil {
		log.WithError(err).Error("error while querying automation rules by tenant")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return rules, nil
}

// FindByTriggerType finds automation rules by tenant and trigger type
func (r *documentAutomationRuleRepositoryImpl) FindByTriggerType(ctx context.Context, tenantID uint64, triggerType string) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rules []*model.DocumentAutomationRule
	tx := r.db.WithContext(ctx)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	err := tx.Where("tenant_id = ? AND trigger_type = ?", tenantID, triggerType).Find(&rules).Error
	if err != nil {
		log.WithError(err).Error("error while querying automation rules by trigger type")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return rules, nil
}

// List finds automation rules with query filters and tenant isolation
func (r *documentAutomationRuleRepositoryImpl) List(ctx context.Context, query *model.Query, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rules []*model.DocumentAutomationRule
	tx := r.db.WithContext(ctx).Model(&model.DocumentAutomationRule{})

	// SECURITY: Always enforce tenant isolation
	tx = tx.Where("tenant_id = ?", tenantID)

	// Apply filters using helper
	fields, values := helper.BuildFilters(query.Q, query.Filters)
	if len(fields) > 0 {
		tx = tx.Where(strings.Join(fields, " AND "), values...)
	}

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	defer func() {
		cancel()
		log.WithField("duration", time.Since(start).String()).Info("filter duration")
	}()

	if query.Q != "" {
		term := "%" + strings.ToLower(query.Q) + "%"
		searchColumns := []string{"name", "description"}
		sub := r.db
		for _, c := range searchColumns {
			sub = sub.Or(fmt.Sprintf("LOWER(%s) LIKE ?", c), term)
		}
		tx = tx.Where(sub)
	}

	tx = tx.Order("created_at DESC")

	err := tx.Find(&rules).Error
	if err != nil {
		log.WithError(err).Error("error while listing automation rules")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return rules, nil
}

// UpdateRule updates an existing document automation rule with tenant isolation
func (r *documentAutomationRuleRepositoryImpl) UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	if tenantID == 0 {
		return ErrInvalidTenant
	}

	// SECURITY: Verify tenant ownership before update
	if rule.TenantID != tenantID {
		log.WithField("rule_tenant_id", rule.TenantID).WithField("request_tenant_id", tenantID).Warn("tenant mismatch in update request")
		return ginext.NewError(http.StatusForbidden, "access denied")
	}

	tx := r.db.WithContext(ctx)
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	rule.UpdatedAt = time.Now()

	// SECURITY: Update with tenant isolation
	// Use Select to explicitly update all fields including boolean false values
	err := tx.Model(rule).Where("id = ? AND tenant_id = ?", rule.ID, tenantID).Select("*").Updates(rule).Error
	if err != nil {
		log.WithError(err).Error("error while updating automation rule")
		return ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return nil
}

// DeleteRule soft deletes a document automation rule by ID with tenant isolation
func (r *documentAutomationRuleRepositoryImpl) DeleteRule(ctx context.Context, id uint64, tenantID uint64) error {
	log := bilabllog.CreateContextLogger(ctx)

	if tenantID == 0 {
		return ErrInvalidTenant
	}

	tx := r.db.WithContext(ctx)
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	// SECURITY: Delete with tenant isolation
	result := tx.Where("id = ? AND tenant_id = ?", id, tenantID).Delete(&model.DocumentAutomationRule{})
	if result.Error != nil {
		log.WithError(result.Error).Error("error while deleting automation rule")
		return ginext.NewError(http.StatusBadRequest, result.Error.Error())
	}

	// Check if any row was actually deleted (tenant isolation check)
	if result.RowsAffected == 0 {
		log.WithField("rule_id", id).WithField("tenant_id", tenantID).Warn("rule not found or access denied")
		return ginext.NewError(http.StatusNotFound, "automation rule not found")
	}

	return nil
}

// FindActiveRules finds all active automation rules for a tenant
func (r *documentAutomationRuleRepositoryImpl) FindActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rules []*model.DocumentAutomationRule
	tx := r.db.WithContext(ctx)

	// Add timeout to prevent long-running queries
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	err := tx.Where("tenant_id = ? AND is_active = ?", tenantID, true).Find(&rules).Error
	if err != nil {
		log.WithError(err).Error("error while querying active automation rules")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return rules, nil
}

// FindRulesByTrigger finds automation rules that match specific trigger conditions
func (r *documentAutomationRuleRepositoryImpl) FindRulesByTrigger(ctx context.Context, tenantID uint64, triggerType string, triggerRules map[string]interface{}) ([]*model.DocumentAutomationRule, error) {
	log := bilabllog.CreateContextLogger(ctx)

	var rules []*model.DocumentAutomationRule
	tx := r.db.WithContext(ctx)

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	// First, get all active rules of the specified trigger type
	err := tx.Where("tenant_id = ? AND trigger_type = ? AND is_active = ?", tenantID, triggerType, true).Find(&rules).Error
	if err != nil {
		log.WithError(err).Error("error while querying automation rules by trigger")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	// Filter rules based on trigger conditions
	// This is a simplified implementation - in production, you might want more sophisticated rule matching
	var matchingRules []*model.DocumentAutomationRule
	for _, rule := range rules {
		if r.matchesTriggerRules(rule.TriggerRules, triggerRules) {
			matchingRules = append(matchingRules, rule)
		}
	}

	return matchingRules, nil
}

// matchesTriggerRules checks if the rule's trigger conditions match the provided trigger data
func (r *documentAutomationRuleRepositoryImpl) matchesTriggerRules(ruleTriggers model.TriggerRulesMap, eventData map[string]interface{}) bool {
	// Simple matching logic - all rule conditions must be satisfied
	for key, expectedValue := range ruleTriggers {
		if actualValue, exists := eventData[key]; !exists || actualValue != expectedValue {
			return false
		}
	}
	return true
}
