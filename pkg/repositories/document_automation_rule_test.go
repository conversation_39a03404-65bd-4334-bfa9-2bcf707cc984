package repositories

import (
	"bilabl/docman/domain/model"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type DocumentAutomationRuleRepositoryTestSuite struct {
	suite.Suite
	db   *gorm.DB
	repo DocumentAutomationRuleRepository
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) SetupTest() {
	// Setup fresh in-memory SQLite database for each test
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate the schema
	err = db.AutoMigrate(&model.DocumentAutomationRule{})
	suite.Require().NoError(err)

	suite.db = db
	suite.repo = NewDocumentAutomationRuleRepository(db)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestCreateRule_Success() {
	ctx := context.Background()

	rule := &model.DocumentAutomationRule{
		TenantID:    123,
		Name:        "Test Rule",
		Description: "Test automation rule",
		TriggerType: "matter.create",
		TriggerRules: model.TriggerRulesMap{
			"matter_type": "litigation",
		},
		RuleConfig: model.RuleConfigArray{
			{
				ActionType: "copy_file",
				SourcePath: "templates/contract.docx",
				TargetPath: "matters/{{matter.id}}/contract.docx",
			},
		},
		IsActive:    true,
		CreatedUser: 1,
		UpdatedUser: 1,
	}

	err := suite.repo.CreateRule(ctx, rule)
	suite.NoError(err)
	suite.NotZero(rule.ID)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestCreateRule_InvalidTenant() {
	ctx := context.Background()

	rule := &model.DocumentAutomationRule{
		TenantID: 0, // Invalid tenant ID
		Name:     "Test Rule",
	}

	err := suite.repo.CreateRule(ctx, rule)
	suite.Error(err)
	suite.Equal(ErrInvalidTenant, err)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindByID_Success() {
	ctx := context.Background()

	// Create a test rule first
	rule := &model.DocumentAutomationRule{
		TenantID:    123,
		Name:        "Test Rule",
		Description: "Test automation rule",
		TriggerType: "matter.create",
		IsActive:    true,
	}

	err := suite.repo.CreateRule(ctx, rule)
	suite.NoError(err)

	// Find the rule by ID
	foundRule, err := suite.repo.FindByID(ctx, rule.ID, rule.TenantID)
	suite.NoError(err)
	suite.NotNil(foundRule)
	suite.Equal(rule.ID, foundRule.ID)
	suite.Equal(rule.Name, foundRule.Name)
	suite.Equal(rule.TenantID, foundRule.TenantID)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindByID_NotFound() {
	ctx := context.Background()

	foundRule, err := suite.repo.FindByID(ctx, 999, uint64(123))
	suite.Error(err)
	suite.Nil(foundRule)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindByTenantID_Success() {
	ctx := context.Background()
	tenantID := uint64(123)

	// Create multiple rules for the tenant
	rules := []*model.DocumentAutomationRule{
		{
			TenantID:    tenantID,
			Name:        "Rule 1",
			TriggerType: "matter.create",
			IsActive:    true,
		},
		{
			TenantID:    tenantID,
			Name:        "Rule 2",
			TriggerType: "client.update",
			IsActive:    false,
		},
		{
			TenantID:    456, // Different tenant
			Name:        "Rule 3",
			TriggerType: "matter.create",
			IsActive:    true,
		},
	}

	for _, rule := range rules {
		err := suite.repo.CreateRule(ctx, rule)
		suite.NoError(err)
	}

	// Find rules by tenant ID
	foundRules, err := suite.repo.FindByTenantID(ctx, tenantID)
	suite.NoError(err)
	suite.Len(foundRules, 2) // Only rules for tenant 123

	for _, rule := range foundRules {
		suite.Equal(tenantID, rule.TenantID)
	}
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindByTriggerType_Success() {
	ctx := context.Background()
	tenantID := uint64(123)
	triggerType := "matter.create"

	// Create rules with different trigger types
	rules := []*model.DocumentAutomationRule{
		{
			TenantID:    tenantID,
			Name:        "Matter Rule 1",
			TriggerType: triggerType,
			IsActive:    true,
		},
		{
			TenantID:    tenantID,
			Name:        "Matter Rule 2",
			TriggerType: triggerType,
			IsActive:    false,
		},
		{
			TenantID:    tenantID,
			Name:        "Client Rule",
			TriggerType: "client.update",
			IsActive:    true,
		},
	}

	for _, rule := range rules {
		err := suite.repo.CreateRule(ctx, rule)
		suite.NoError(err)
	}

	// Find rules by trigger type
	foundRules, err := suite.repo.FindByTriggerType(ctx, tenantID, triggerType)
	suite.NoError(err)
	suite.Len(foundRules, 2) // Both matter.create rules

	for _, rule := range foundRules {
		suite.Equal(triggerType, rule.TriggerType)
		suite.Equal(tenantID, rule.TenantID)
	}
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestUpdateRule_Success() {
	ctx := context.Background()

	// Create a test rule first
	rule := &model.DocumentAutomationRule{
		TenantID:    123,
		Name:        "Original Name",
		Description: "Original description",
		TriggerType: "matter.create",
		IsActive:    true,
		CreatedUser: 1,
		UpdatedUser: 1,
	}

	err := suite.repo.CreateRule(ctx, rule)
	suite.NoError(err)
	originalUpdatedAt := rule.UpdatedAt

	// Wait a bit to ensure timestamp difference
	time.Sleep(10 * time.Millisecond)

	// Update the rule
	rule.Name = "Updated Name"
	rule.Description = "Updated description"
	rule.IsActive = false
	rule.UpdatedUser = 2

	err = suite.repo.UpdateRule(ctx, rule, rule.TenantID)
	suite.NoError(err)

	// Verify the update
	foundRule, err := suite.repo.FindByID(ctx, rule.ID, rule.TenantID)
	suite.NoError(err)
	suite.Equal("Updated Name", foundRule.Name)
	suite.Equal("Updated description", foundRule.Description)
	suite.False(foundRule.IsActive)
	suite.Equal(uint64(2), foundRule.UpdatedUser)
	suite.True(foundRule.UpdatedAt.After(originalUpdatedAt))
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestDeleteRule_Success() {
	ctx := context.Background()

	// Create a test rule first
	rule := &model.DocumentAutomationRule{
		TenantID:    123,
		Name:        "Test Rule",
		TriggerType: "matter.create",
		IsActive:    true,
	}

	err := suite.repo.CreateRule(ctx, rule)
	suite.NoError(err)

	// Delete the rule
	err = suite.repo.DeleteRule(ctx, rule.ID, rule.TenantID)
	suite.NoError(err)

	// Verify the rule is deleted
	foundRule, err := suite.repo.FindByID(ctx, rule.ID, rule.TenantID)
	suite.Error(err)
	suite.Nil(foundRule)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindActiveRules_Success() {
	ctx := context.Background()
	tenantID := uint64(123)

	// Create rules with different active states
	rules := []*model.DocumentAutomationRule{
		{
			TenantID:    tenantID,
			Name:        "Active Rule 1",
			TriggerType: "matter.create",
			IsActive:    true,
		},
		{
			TenantID:    tenantID,
			Name:        "Active Rule 2",
			TriggerType: "client.update",
			IsActive:    true,
		},
		{
			TenantID:    tenantID,
			Name:        "Inactive Rule",
			TriggerType: "matter.create",
			IsActive:    false,
		},
	}

	for _, rule := range rules {
		err := suite.repo.CreateRule(ctx, rule)
		suite.NoError(err)
	}

	// Find active rules
	activeRules, err := suite.repo.FindActiveRules(ctx, tenantID)
	suite.NoError(err)
	suite.Len(activeRules, 2) // Only active rules

	for _, rule := range activeRules {
		suite.True(rule.IsActive)
		suite.Equal(tenantID, rule.TenantID)
	}
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestFindRulesByTrigger_Success() {
	ctx := context.Background()
	tenantID := uint64(123)
	triggerType := "matter.create"

	// Create rules with different trigger conditions
	rules := []*model.DocumentAutomationRule{
		{
			TenantID:    tenantID,
			Name:        "Litigation Rule",
			TriggerType: triggerType,
			TriggerRules: model.TriggerRulesMap{
				"matter_type": "litigation",
			},
			IsActive: true,
		},
		{
			TenantID:    tenantID,
			Name:        "Corporate Rule",
			TriggerType: triggerType,
			TriggerRules: model.TriggerRulesMap{
				"matter_type": "corporate",
			},
			IsActive: true,
		},
		{
			TenantID:    tenantID,
			Name:        "Inactive Rule",
			TriggerType: triggerType,
			TriggerRules: model.TriggerRulesMap{
				"matter_type": "litigation",
			},
			IsActive: false,
		},
	}

	for _, rule := range rules {
		err := suite.repo.CreateRule(ctx, rule)
		suite.NoError(err)
	}

	// Find rules matching trigger conditions
	triggerData := map[string]interface{}{
		"matter_type": "litigation",
	}

	matchingRules, err := suite.repo.FindRulesByTrigger(ctx, tenantID, triggerType, triggerData)
	suite.NoError(err)
	suite.Len(matchingRules, 1) // Only active litigation rule
	suite.Equal("Litigation Rule", matchingRules[0].Name)
}

func (suite *DocumentAutomationRuleRepositoryTestSuite) TestList_WithQuery() {
	ctx := context.Background()

	// Create test rules
	rules := []*model.DocumentAutomationRule{
		{
			TenantID:    123,
			Name:        "Contract Template Rule",
			Description: "Handles contract generation",
			TriggerType: "matter.create",
			IsActive:    true,
		},
		{
			TenantID:    123,
			Name:        "Invoice Rule",
			Description: "Handles invoice processing",
			TriggerType: "client.update",
			IsActive:    true,
		},
	}

	for _, rule := range rules {
		err := suite.repo.CreateRule(ctx, rule)
		suite.NoError(err)
	}

	// Test search by name
	query := &model.Query{
		Q: "contract",
		Filters: []*model.Filter{
			{Key: "tenant_id", Value: 123, Method: "="},
		},
	}

	foundRules, err := suite.repo.List(ctx, query, uint64(123))
	suite.NoError(err)
	suite.Len(foundRules, 1)
	suite.Equal("Contract Template Rule", foundRules[0].Name)
}

func TestDocumentAutomationRuleRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(DocumentAutomationRuleRepositoryTestSuite))
}
