package repositories

import (
	"bilabl/docman/domain/model"
	"context"

	"gorm.io/gorm"
)

type MigrationRepo struct {
	db *gorm.DB
}

func NewMigrationRepo(
	db *gorm.DB,
) *MigrationRepo {
	return &MigrationRepo{
		db: db,
	}
}

func panicIf(err interface{}) {
	if err != nil {
		panic(err)
	}
}

func (r *MigrationRepo) MigrateDB(ctx context.Context) {
	models := []interface{}{
		&model.Document{},
		&model.DocumentAutomationRule{},
		&model.DocumentConfig{},
		&model.DocumentMapping{},
		&model.DocumentPermissionMapping{},
		&model.DocumentSetting{},
		&model.UploadSession{},
		&model.OperationCoordinationStatus{},
	}
	tx := r.db.WithContext(ctx)

	// First, migrate the schema
	panicIf(tx.AutoMigrate(models...))

	// Then, handle unique index migration for DocumentPermissionMapping
	r.migratePermissionMappingIndex(ctx)
}

// migratePermissionMappingIndex handles the migration of unique index for DocumentPermissionMapping
func (r *MigrationRepo) migratePermissionMappingIndex(ctx context.Context) {
	tx := r.db.WithContext(ctx)

	// Check if old index exists and drop it
	if tx.Migrator().HasIndex(&model.DocumentPermissionMapping{}, "idx_email_drive_perm") {
		err := tx.Migrator().DropIndex(&model.DocumentPermissionMapping{}, "idx_email_drive_perm")
		if err != nil {
			// Log error but don't panic - index might not exist in some environments
			// This is expected in fresh installations
		}
	}

	// Create new index with provider included
	if !tx.Migrator().HasIndex(&model.DocumentPermissionMapping{}, "idx_email_drive_perm_provider") {
		err := tx.Migrator().CreateIndex(&model.DocumentPermissionMapping{}, "idx_email_drive_perm_provider")
		if err != nil {
			// Only panic if we can't create the new index
			panicIf(err)
		}
	}
}
