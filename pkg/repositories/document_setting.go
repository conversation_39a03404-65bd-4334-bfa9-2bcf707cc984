package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/helper"
	"context"
	"strings"
	"time"

	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type documentSettingRepositoryImpl struct {
	db *gorm.DB
	*options
}

func NewDocumentSettingRepo(
	db *gorm.DB,
	opts ...Option,
) DocumentSettingRepository {
	r := &documentSettingRepositoryImpl{
		db: db,
		options: &options{
			log: logger.DefaultBaseEntry,
		},
	}
	for _, o := range opts {
		o(r.options)
	}

	return r
}

// withTimeout is a helper method to handle context timeout with logging
func (r *documentSettingRepositoryImpl) withTimeout(ctx context.Context, operation string) (context.Context, context.CancelFunc, func()) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	return ctx, cancel, func() {
		cancel()
		r.options.log.WithField("duration", time.Since(start).String()).
			WithField("operation", operation).Info("operation duration")
	}
}

func (r *documentSettingRepositoryImpl) Create(ctx context.Context, documentSetting *model.DocumentSetting) (err error) {
	if documentSetting.TenantID == 0 {
		return ErrInvalidTenant
	}

	var tx = r.db.WithContext(ctx)

	err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "tenant_id"}, {Name: "key"}},
		UpdateAll: true,
	}).Create(documentSetting).Error

	return err
}

func (r *documentSettingRepositoryImpl) CreateOrUpdate(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "CreateOrUpdate")
	defer logFunc()
	defer cancel()

	if documentSetting.TenantID == 0 {
		return ErrInvalidTenant
	}

	// Use UPSERT for better performance - single query instead of SELECT + UPDATE
	documentSetting.UpdatedAt = time.Now()

	err = r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "tenant_id"}, {Name: "key"}},
		DoUpdates: clause.AssignmentColumns([]string{"value", "updated_at"}),
	}).Create(documentSetting).Error

	return err
}

func (r *documentSettingRepositoryImpl) FindOne(ctx context.Context, query *model.Query) (documentSetting *model.DocumentSetting, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "FindOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Model(&model.DocumentSetting{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.First(&documentSetting).Error

	return documentSetting, err
}

func (r *documentSettingRepositoryImpl) UpdateOne(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "UpdateOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Debug().Model(&model.DocumentSetting{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	documentSetting.UpdatedAt = time.Now()
	result := tx.Updates(&documentSetting)
	return result.Error
}

func (r *documentSettingRepositoryImpl) GetValueByKey(ctx context.Context, tenantId uint64, key string) (documentSetting *model.DocumentSetting, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "GetValueByKey")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Model(&model.DocumentSetting{})

	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  tenantId,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  key,
			Method: "=",
		},
	}
	fields, values := helper.BuildFilters("", filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.First(&documentSetting).Error

	return documentSetting, err
}
