package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/helper"
	"context"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/pkg/db"
	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type documentPermissionMappingRepositoryImpl struct {
	*db.BaseRepository

	db *gorm.DB
	*options
}

func NewDocumentPermissionMappingRepo(
	conn *gorm.DB,
	opts ...Option,
) DocumentPermissionMappingRepository {
	r := &documentPermissionMappingRepositoryImpl{
		db: conn,
		options: &options{
			log: logger.DefaultBaseEntry,
		},
		BaseRepository: db.NewBaseRepository(conn),
	}
	for _, o := range opts {
		o(r.options)
	}

	return r
}

// withTimeout is a helper method to handle context timeout with logging
func (r *documentPermissionMappingRepositoryImpl) withTimeout(ctx context.Context, operation string) (context.Context, context.CancelFunc, func()) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	return ctx, cancel, func() {
		cancel()
		r.options.log.WithField("duration", time.Since(start).String()).
			WithField("operation", operation).Info("operation duration")
	}
}

func (r *documentPermissionMappingRepositoryImpl) CreateOrUpdate(ctx context.Context, documentPermissionMapping *model.DocumentPermissionMapping) (err error) {
	tx := r.Tx(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email"}, {Name: "drive_id"}, {Name: "perm_id"}, {Name: "provider"}},
		UpdateAll: true,
	}).Create(&documentPermissionMapping)

	return tx.Error
}

func (r *documentPermissionMappingRepositoryImpl) BulkCreateOrUpdate(ctx context.Context, documentPermissionMappings []*model.DocumentPermissionMapping) (err error) {
	if len(documentPermissionMappings) == 0 {
		return nil
	}

	// Use batch insert with conflict resolution
	tx := r.Tx(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email"}, {Name: "drive_id"}, {Name: "perm_id"}, {Name: "provider"}},
		UpdateAll: true,
	}).CreateInBatches(documentPermissionMappings, 100) // Process in batches of 100

	return tx.Error
}

func (r *documentPermissionMappingRepositoryImpl) FindOne(ctx context.Context, query *model.Query) (documentPermissionMapping *model.DocumentPermissionMapping, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "FindOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Model(&model.DocumentPermissionMapping{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.First(&documentPermissionMapping).Error

	return documentPermissionMapping, err
}

func (r *documentPermissionMappingRepositoryImpl) UpdateOne(ctx context.Context, query *model.Query, documentPermissionMapping *model.DocumentPermissionMapping) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "UpdateOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Debug().Model(&model.DocumentPermissionMapping{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	result := tx.Updates(&documentPermissionMapping)
	return result.Error
}

func (r *documentPermissionMappingRepositoryImpl) DeleteOne(ctx context.Context, query *model.Query) (id uint64, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "DeleteOne")
	defer logFunc()
	defer cancel()

	var documentPermissionMapping *model.DocumentPermissionMapping
	tx := r.db.WithContext(ctx).Model(&model.DocumentPermissionMapping{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.Limit(1).Delete(nil).First(&documentPermissionMapping).Error

	return documentPermissionMapping.ID, err
}

type PermMapFindArgs struct {
	TenantID        *uint64
	Email           *string
	DriveID         *string
	PermID          *string
	Provider        *[]string // Filter by provider(s): "google", "sharepoint"
	Emails          *[]string
	Limit           int
	FireNotFoundErr bool
}

func (r *documentPermissionMappingRepositoryImpl) Find(ctx context.Context, args *PermMapFindArgs) ([]*model.DocumentPermissionMapping, error) {
	var items []*model.DocumentPermissionMapping
	query := r.Tx(ctx).Model(&model.DocumentPermissionMapping{})
	if args.TenantID != nil {
		query = query.Where("tenant_id = ?", *args.TenantID)
	}
	if args.Email != nil {
		query = query.Where("email = ?", *args.Email)
	}
	if args.DriveID != nil {
		query = query.Where("drive_id = ?", *args.DriveID)
	}
	if args.PermID != nil {
		query = query.Where("perm_id = ?", *args.PermID)
	}
	if args.Provider != nil && len(*args.Provider) > 0 {
		query = query.Where("provider IN ?", *args.Provider)
	}
	if args.Emails != nil && len(*args.Emails) > 0 {
		query = query.Where("email IN ?", *args.Emails)
	}

	if args.Limit == 0 {
		args.Limit = 100
	}

	err := query.Limit(args.Limit).Find(&items).Error
	return items, err
}
