package repositories

import (
	"bilabl/docman/domain/model"
	"context"

	"code.mybil.net/gophers/gokit/pkg/db"

	"gitlab.com/goxp/cloud0/ginext"
)

type DocumentRepository interface {
	db.Repository

	CreateDoc(ctx context.Context, document *model.Document) (err error)
	FindOne(ctx context.Context, query *model.Query) (res *model.Document, err error)
	Find(ctx context.Context, query *model.Query, pager *ginext.Pager) (res []*model.Document, err error)
	List(ctx context.Context, query *model.Query) (res []*model.Document, err error)
	UpdateOne(ctx context.Context, query *model.Query, document *model.Document) (err error)
	CreateOrUpdate(ctx context.Context, query *model.Query, document *model.Document) (err error)
	DeleteOne(ctx context.Context, query *model.Query) (err error)
	Exists(ctx context.Context, query *model.Query) (exists bool, err error)
}

type DocumentConfigRepository interface {
	Create(ctx context.Context, documentConfig *model.DocumentConfig) (err error)
	FindOne(ctx context.Context, query *model.Query) (res *model.DocumentConfig, err error)
	Find(ctx context.Context, query *model.Query) (res []*model.DocumentConfig, err error)
	UpdateOne(ctx context.Context, query *model.Query, document *model.DocumentConfig) (err error)
	DeleteOne(ctx context.Context, query *model.Query) (id uint64, err error)
	Destroy(ctx context.Context) (err error)
}

type DocumentMappingRepository interface {
	db.Repository

	FindOne(ctx context.Context, query *model.Query) (documentMapping *model.DocumentMapping, err error)
	UpdateOne(ctx context.Context, query *model.Query, documentMapping *model.DocumentMapping) (err error)
	CreateOrUpdate(ctx context.Context, documentMapping *model.DocumentMapping) (err error)

	FirstObjectMapping(
		ctx context.Context,
		docType string,
		provider string,
		tenantID uint64,
		objectID uint64,
		parentID uint64,
	) (*model.DocumentMapping, error)
}

type DocumentSettingRepository interface {
	Create(ctx context.Context, documentSetting *model.DocumentSetting) (err error)
	FindOne(ctx context.Context, query *model.Query) (documentSetting *model.DocumentSetting, err error)
	UpdateOne(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) (err error)
	CreateOrUpdate(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) (err error)
	GetValueByKey(ctx context.Context, tenantId uint64, key string) (documentSetting *model.DocumentSetting, err error)
}

type DocumentPermissionMappingRepository interface {
	db.Repository

	FindOne(ctx context.Context, query *model.Query) (documentPermissionMapping *model.DocumentPermissionMapping, err error)
	UpdateOne(ctx context.Context, query *model.Query, documentPermissionMapping *model.DocumentPermissionMapping) (err error)
	CreateOrUpdate(ctx context.Context, documentPermissionMapping *model.DocumentPermissionMapping) (err error)
	BulkCreateOrUpdate(ctx context.Context, documentPermissionMappings []*model.DocumentPermissionMapping) (err error)
	DeleteOne(ctx context.Context, query *model.Query) (id uint64, err error)
	Find(ctx context.Context, args *PermMapFindArgs) ([]*model.DocumentPermissionMapping, error)
}

type DocumentAutomationRuleRepository interface {
	db.Repository

	// Core CRUD operations with tenant isolation
	CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error
	FindByID(ctx context.Context, id uint64, tenantID uint64) (*model.DocumentAutomationRule, error)
	FindByTenantID(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error)
	FindByTriggerType(ctx context.Context, tenantID uint64, triggerType string) ([]*model.DocumentAutomationRule, error)
	List(ctx context.Context, query *model.Query, tenantID uint64) ([]*model.DocumentAutomationRule, error)
	UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule, tenantID uint64) error
	DeleteRule(ctx context.Context, id uint64, tenantID uint64) error

	// Business logic operations
	FindActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error)
	FindRulesByTrigger(ctx context.Context, tenantID uint64, triggerType string, triggerRules map[string]interface{}) ([]*model.DocumentAutomationRule, error)
}

// UploadSessionRepository interface is defined in upload_session.go to avoid circular imports
