package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/helper"
	"context"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/pkg/db"
	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type docMapImpl struct {
	*db.BaseRepository

	*options
}

func NewDocumentMappingRepo(
	conn *gorm.DB,
	opts ...Option,
) DocumentMappingRepository {
	r := &docMapImpl{
		BaseRepository: db.NewBaseRepository(conn),
		options: &options{
			log: logger.DefaultBaseEntry,
		},
	}
	for _, o := range opts {
		o(r.options)
	}

	return r
}

// withTimeout is a helper method to handle context timeout with logging
func (r *docMapImpl) withTimeout(ctx context.Context, operation string) (context.Context, context.CancelFunc, func()) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	return ctx, cancel, func() {
		cancel()
		r.options.log.WithField("duration", time.Since(start).String()).
			WithField("operation", operation).Info("operation duration")
	}
}

func (r *docMapImpl) CreateOrUpdate(ctx context.Context, documentMapping *model.DocumentMapping) (err error) {
	tx := r.Tx(ctx).Clauses(clause.OnConflict{
		UpdateAll: true,
	}).Create(&documentMapping)

	return tx.Error
}

func (r *docMapImpl) FindOne(ctx context.Context, query *model.Query) (documentMapping *model.DocumentMapping, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "FindOne")
	defer logFunc()
	defer cancel()

	tx := r.Tx(ctx).Model(&model.DocumentMapping{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.First(&documentMapping).Error

	return documentMapping, err
}

func (r *docMapImpl) UpdateOne(ctx context.Context, query *model.Query, documentMapping *model.DocumentMapping) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "UpdateOne")
	defer logFunc()
	defer cancel()

	tx := r.Tx(ctx).Model(&model.DocumentMapping{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	documentMapping.UpdatedAt = time.Now()
	result := tx.Updates(&documentMapping)
	return result.Error
}

func (r *docMapImpl) FirstObjectMapping(
	ctx context.Context,
	docType string,
	provider string,
	tenantID uint64,
	objectID uint64,
	parentID uint64,
) (*model.DocumentMapping, error) {
	var doc model.DocumentMapping
	err := r.Tx(ctx).
		Where("tenant_id", tenantID).
		Where("object_id", objectID).
		Where("parent_object_id", parentID).
		Where("type", docType).
		Where("provider", provider).
		First(&doc).Error
	return &doc, err
}
