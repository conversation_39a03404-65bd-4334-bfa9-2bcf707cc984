package repositories

import (
	"fmt"
	"testing"

	"bilabl/docman/domain/model"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// TestErrorHandlingConsistency verifies that all repositories use model.IsNotFound consistently
func TestErrorHandlingConsistency(t *testing.T) {
	// This test verifies that our fix for using model.IsNotFound instead of direct comparison
	// works correctly with wrapped errors

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "direct gorm.ErrRecordNotFound should be detected",
			err:      gorm.ErrRecordNotFound,
			expected: true,
		},
		{
			name:     "wrapped gorm.ErrRecordNotFound should be detected",
			err:      fmt.<PERSON><PERSON><PERSON>("database error: %w", gorm.ErrRecordNotFound),
			expected: true,
		},
		{
			name:     "double wrapped gorm.ErrRecordNotFound should be detected",
			err:      fmt.<PERSON><PERSON><PERSON>("query failed: %w", fmt.Errorf("database error: %w", gorm.ErrRecordNotFound)),
			expected: true,
		},
		{
			name:     "other error should not be detected as not found",
			err:      fmt.<PERSON><PERSON><PERSON>("connection timeout"),
			expected: false,
		},
		{
			name:     "nil error should not be detected as not found",
			err:      nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := model.IsNotFound(tt.err)
			assert.Equal(t, tt.expected, result, "model.IsNotFound(%v) = %v, want %v", tt.err, result, tt.expected)
		})
	}
}

// TestDirectComparisonVsIsNotFound demonstrates why direct comparison fails with wrapped errors
func TestDirectComparisonVsIsNotFound(t *testing.T) {
	// Create a wrapped error (common in real applications)
	wrappedErr := fmt.Errorf("database connection failed: %w", gorm.ErrRecordNotFound)

	// Direct comparison fails with wrapped errors
	directComparison := (wrappedErr == gorm.ErrRecordNotFound)
	assert.False(t, directComparison, "Direct comparison should fail with wrapped errors")

	// model.IsNotFound works correctly with wrapped errors
	isNotFoundResult := model.IsNotFound(wrappedErr)
	assert.True(t, isNotFoundResult, "model.IsNotFound should work with wrapped errors")

	// This demonstrates why our fix is important
	t.Logf("Direct comparison result: %v", directComparison)
	t.Logf("model.IsNotFound result: %v", isNotFoundResult)
}
