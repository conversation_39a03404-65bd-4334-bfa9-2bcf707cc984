package repositories

import (
	"bilabl/docman/domain/model"
	"context"
	"time"

	"code.mybil.net/gophers/gokit/pkg/db"
	"gitlab.com/goxp/cloud0/ginext"
	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
)

// UploadSessionRepository defines the interface for upload session operations
type UploadSessionRepository interface {
	// Create creates a new upload session
	Create(ctx context.Context, session *model.UploadSession) error

	// GetByToken retrieves an upload session by its token
	GetByToken(ctx context.Context, token string) (*model.UploadSession, error)

	// GetByTokenAndTenant retrieves an upload session by token and tenant ID for security
	GetByTokenAndTenant(ctx context.Context, token string, tenantID uint64) (*model.UploadSession, error)

	// UpdateStatus updates the status of an upload session
	UpdateStatus(ctx context.Context, token string, status model.UploadSessionStatus) error

	// UpdateStatusAndFileID updates both status and Google file ID
	UpdateStatusAndFileID(ctx context.Context, token string, status model.UploadSessionStatus, googleFileID string) error

	// UpdateFileInfo updates file-related information (size, content type)
	UpdateFileInfo(ctx context.Context, token string, fileSize int64, contentType string) error

	// CleanupExpired removes expired upload sessions
	CleanupExpired(ctx context.Context) (int64, error)

	// ListByTenantAndStatus lists upload sessions by tenant and status with pagination
	ListByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus, limit, offset int) ([]*model.UploadSession, error)

	// CountByTenantAndStatus counts upload sessions by tenant and status
	CountByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus) (int64, error)
}

// uploadSessionRepositoryImpl implements UploadSessionRepository
type uploadSessionRepositoryImpl struct {
	*db.BaseRepository
	*options
	db *gorm.DB
}

// NewUploadSessionRepo creates a new upload session repository
func NewUploadSessionRepo(
	conn *gorm.DB,
	opts ...Option,
) UploadSessionRepository {
	r := &uploadSessionRepositoryImpl{
		BaseRepository: db.NewBaseRepository(conn),
		options: &options{
			log: logger.DefaultBaseEntry,
		},
		db: conn,
	}
	for _, o := range opts {
		o(r.options)
	}

	return r
}

// Create creates a new upload session
func (r *uploadSessionRepositoryImpl) Create(ctx context.Context, session *model.UploadSession) error {
	log := logger.WithCtx(ctx, "UploadSessionRepository.Create")

	if session.TenantID == 0 {
		return ginext.NewError(400, "invalid tenant ID")
	}

	if session.SessionToken == "" {
		return ginext.NewError(400, "session token is required")
	}

	// Set timestamps
	now := time.Now()
	session.CreatedAt = now
	session.UpdatedAt = now

	// Default expiration to 1 hour if not set
	if session.ExpiresAt.IsZero() {
		session.ExpiresAt = now.Add(1 * time.Hour)
	}

	// Default status if not set
	if session.Status == "" {
		session.Status = model.UploadSessionStatusPending
	}

	err := r.db.WithContext(ctx).Create(session).Error
	if err != nil {
		log.WithError(err).Error("failed to create upload session")
		return ginext.NewError(500, "failed to create upload session")
	}

	log.Infof("created upload session session_token=%s tenant_id=%d", session.SessionToken, session.TenantID)
	return nil
}

// GetByToken retrieves an upload session by its token
func (r *uploadSessionRepositoryImpl) GetByToken(ctx context.Context, token string) (*model.UploadSession, error) {
	log := logger.WithCtx(ctx, "UploadSessionRepository.GetByToken")

	if token == "" {
		return nil, ginext.NewError(400, "session token is required")
	}

	var session model.UploadSession
	err := r.db.WithContext(ctx).Where("session_token = ?", token).First(&session).Error
	if err != nil {
		if model.IsNotFound(err) {
			log.Warnf("upload session not found session_token=%s", token)
			return nil, ginext.NewError(404, "upload session not found")
		}
		log.WithError(err).Errorf("failed to get upload session session_token=%s", token)
		return nil, ginext.NewError(500, "failed to get upload session")
	}

	return &session, nil
}

// GetByTokenAndTenant retrieves an upload session by token and tenant ID for security
func (r *uploadSessionRepositoryImpl) GetByTokenAndTenant(ctx context.Context, token string, tenantID uint64) (*model.UploadSession, error) {
	log := logger.WithCtx(ctx, "UploadSessionRepository.GetByTokenAndTenant")

	if token == "" {
		return nil, ginext.NewError(400, "session token is required")
	}

	if tenantID == 0 {
		return nil, ginext.NewError(400, "tenant ID is required")
	}

	var session model.UploadSession
	err := r.db.WithContext(ctx).Where("session_token = ? AND tenant_id = ?", token, tenantID).First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Warnf("upload session not found session_token=%s tenant_id=%d", token, tenantID)
			return nil, ginext.NewError(404, "upload session not found")
		}
		log.WithError(err).Errorf("failed to get upload session session_token=%s tenant_id=%d", token, tenantID)
		return nil, ginext.NewError(500, "failed to get upload session")
	}

	return &session, nil
}

// UpdateStatus updates the status of an upload session
func (r *uploadSessionRepositoryImpl) UpdateStatus(ctx context.Context, token string, status model.UploadSessionStatus) error {
	log := logger.WithCtx(ctx, "UploadSessionRepository.UpdateStatus")

	if token == "" {
		return ginext.NewError(400, "session token is required")
	}

	result := r.db.WithContext(ctx).
		Model(&model.UploadSession{}).
		Where("session_token = ?", token).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		log.WithError(result.Error).Errorf("failed to update upload session status session_token=%s status=%s", token, status)
		return ginext.NewError(500, "failed to update upload session status")
	}

	if result.RowsAffected == 0 {
		log.Warnf("no upload session found to update session_token=%s", token)
		return ginext.NewError(404, "upload session not found")
	}

	log.Infof("updated upload session status session_token=%s status=%s", token, status)
	return nil
}

// UpdateStatusAndFileID updates both status and Google file ID
func (r *uploadSessionRepositoryImpl) UpdateStatusAndFileID(ctx context.Context, token string, status model.UploadSessionStatus, googleFileID string) error {
	log := logger.WithCtx(ctx, "UploadSessionRepository.UpdateStatusAndFileID")

	if token == "" {
		return ginext.NewError(400, "session token is required")
	}

	result := r.db.WithContext(ctx).
		Model(&model.UploadSession{}).
		Where("session_token = ?", token).
		Updates(map[string]interface{}{
			"status":         status,
			"google_file_id": googleFileID,
			"updated_at":     time.Now(),
		})

	if result.Error != nil {
		log.WithError(result.Error).Errorf("failed to update upload session session_token=%s status=%s file_id=%s", token, status, googleFileID)
		return ginext.NewError(500, "failed to update upload session")
	}

	if result.RowsAffected == 0 {
		log.Warnf("no upload session found to update session_token=%s", token)
		return ginext.NewError(404, "upload session not found")
	}

	log.Infof("updated upload session session_token=%s status=%s file_id=%s", token, status, googleFileID)
	return nil
}

// UpdateFileInfo updates file-related information (size, content type)
func (r *uploadSessionRepositoryImpl) UpdateFileInfo(ctx context.Context, token string, fileSize int64, contentType string) error {
	log := logger.WithCtx(ctx, "UploadSessionRepository.UpdateFileInfo")

	if token == "" {
		return ginext.NewError(400, "session token is required")
	}

	result := r.db.WithContext(ctx).
		Model(&model.UploadSession{}).
		Where("session_token = ?", token).
		Updates(map[string]interface{}{
			"file_size":    fileSize,
			"content_type": contentType,
			"updated_at":   time.Now(),
		})

	if result.Error != nil {
		log.WithError(result.Error).Errorf("failed to update upload session file info session_token=%s", token)
		return ginext.NewError(500, "failed to update upload session file info")
	}

	if result.RowsAffected == 0 {
		log.Warnf("no upload session found to update session_token=%s", token)
		return ginext.NewError(404, "upload session not found")
	}

	log.Infof("updated upload session file info session_token=%s size=%d content_type=%s", token, fileSize, contentType)
	return nil
}

// CleanupExpired removes expired upload sessions
func (r *uploadSessionRepositoryImpl) CleanupExpired(ctx context.Context) (int64, error) {
	log := logger.WithCtx(ctx, "UploadSessionRepository.CleanupExpired")

	result := r.db.WithContext(ctx).
		Where("expires_at < ? OR (status = ? AND updated_at < ?)",
			time.Now(),
			model.UploadSessionStatusCompleted,
			time.Now().Add(-24*time.Hour), // Remove completed sessions older than 24 hours
		).
		Delete(&model.UploadSession{})

	if result.Error != nil {
		log.WithError(result.Error).Error("failed to cleanup expired upload sessions")
		return 0, ginext.NewError(500, "failed to cleanup expired upload sessions")
	}

	log.Infof("cleaned up expired upload sessions count=%d", result.RowsAffected)
	return result.RowsAffected, nil
}

// ListByTenantAndStatus lists upload sessions by tenant and status with pagination
func (r *uploadSessionRepositoryImpl) ListByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus, limit, offset int) ([]*model.UploadSession, error) {
	log := logger.WithCtx(ctx, "UploadSessionRepository.ListByTenantAndStatus")

	if tenantID == 0 {
		return nil, ginext.NewError(400, "tenant ID is required")
	}

	var sessions []*model.UploadSession
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&sessions).Error

	if err != nil {
		log.WithError(err).Errorf("failed to list upload sessions tenant_id=%d status=%s", tenantID, status)
		return nil, ginext.NewError(500, "failed to list upload sessions")
	}

	return sessions, nil
}

// CountByTenantAndStatus counts upload sessions by tenant and status
func (r *uploadSessionRepositoryImpl) CountByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus) (int64, error) {
	log := logger.WithCtx(ctx, "UploadSessionRepository.CountByTenantAndStatus")

	if tenantID == 0 {
		return 0, ginext.NewError(400, "tenant ID is required")
	}

	var count int64
	query := r.db.WithContext(ctx).Model(&model.UploadSession{}).Where("tenant_id = ?", tenantID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&count).Error
	if err != nil {
		log.WithError(err).Errorf("failed to count upload sessions tenant_id=%d status=%s", tenantID, status)
		return 0, ginext.NewError(500, "failed to count upload sessions")
	}

	return count, nil
}
