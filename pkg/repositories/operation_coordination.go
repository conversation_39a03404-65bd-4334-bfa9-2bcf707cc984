package repositories

import (
	"time"

	"bilabl/docman/domain/model"

	"gorm.io/gorm"
)

// operationCoordinationStatusRepositoryImpl implements OperationCoordinationStatusRepository
type operationCoordinationStatusRepositoryImpl struct {
	db *gorm.DB
}

// NewOperationCoordinationStatusRepository creates a new operation coordination status repository
func NewOperationCoordinationStatusRepository(db *gorm.DB) model.OperationCoordinationStatusRepository {
	return &operationCoordinationStatusRepositoryImpl{
		db: db,
	}
}

// Create creates a new operation coordination status record
func (r *operationCoordinationStatusRepositoryImpl) Create(status *model.OperationCoordinationStatus) error {
	return r.db.Create(status).Error
}

// GetByOperationID retrieves status by operation ID
func (r *operationCoordinationStatusRepositoryImpl) GetByOperationID(operationID string) (*model.OperationCoordinationStatus, error) {
	var status model.OperationCoordinationStatus
	err := r.db.Where("operation_id = ?", operationID).First(&status).Error
	if err != nil {
		return nil, err
	}
	return &status, nil
}

// GetByEntity retrieves status by entity and operation type
func (r *operationCoordinationStatusRepositoryImpl) GetByEntity(tenantID uint64, entityType string, entityID uint64, operationType string) (*model.OperationCoordinationStatus, error) {
	var status model.OperationCoordinationStatus
	err := r.db.Where("tenant_id = ? AND entity_type = ? AND entity_id = ? AND operation_type = ?", 
		tenantID, entityType, entityID, operationType).
		Order("created_at DESC").
		First(&status).Error
	if err != nil {
		return nil, err
	}
	return &status, nil
}

// Update updates an operation coordination status record
func (r *operationCoordinationStatusRepositoryImpl) Update(status *model.OperationCoordinationStatus) error {
	return r.db.Save(status).Error
}

// UpdateProviderStatus updates the status of a specific provider
func (r *operationCoordinationStatusRepositoryImpl) UpdateProviderStatus(operationID, provider string, status model.ProviderStatus) error {
	// First get the current record
	var coordinationStatus model.OperationCoordinationStatus
	err := r.db.Where("operation_id = ?", operationID).First(&coordinationStatus).Error
	if err != nil {
		return err
	}
	
	// Update the provider status
	if err := coordinationStatus.UpdateProviderStatus(provider, status); err != nil {
		return err
	}
	
	// Save the updated record
	return r.db.Save(&coordinationStatus).Error
}

// GetPendingStatuses retrieves all pending statuses (for timeout monitoring)
func (r *operationCoordinationStatusRepositoryImpl) GetPendingStatuses(olderThan time.Time) ([]*model.OperationCoordinationStatus, error) {
	var statuses []*model.OperationCoordinationStatus
	err := r.db.Where("overall_status = ? AND created_at < ?", 
		model.CompletionStatusPending, olderThan).
		Find(&statuses).Error
	return statuses, err
}

// MarkAsTimedOut marks statuses as timed out
func (r *operationCoordinationStatusRepositoryImpl) MarkAsTimedOut(operationIDs []string) error {
	if len(operationIDs) == 0 {
		return nil
	}
	
	return r.db.Model(&model.OperationCoordinationStatus{}).
		Where("operation_id IN ?", operationIDs).
		Updates(map[string]interface{}{
			"overall_status": model.CompletionStatusTimeout,
			"updated_at":     time.Now(),
		}).Error
}

// DeleteOldRecords deletes old completed records
func (r *operationCoordinationStatusRepositoryImpl) DeleteOldRecords(olderThan time.Time) error {
	return r.db.Where("overall_status IN ? AND completed_at < ?", 
		[]model.CompletionStatus{
			model.CompletionStatusCompleted,
			model.CompletionStatusFailed,
			model.CompletionStatusTimeout,
		}, olderThan).
		Delete(&model.OperationCoordinationStatus{}).Error
}
