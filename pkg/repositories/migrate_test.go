package repositories

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gitlab.com/goxp/cloud0/db"
)

type migrationTestSuite struct {
	suite.Suite
	repo *MigrationRepo
}

func (ts *migrationTestSuite) SetupSuite() {
	var inMemorySqliteCfg = &db.Config{
		Driver:          "sqlite3",
		DSN:             "file::memory:",
		MaxOpenConns:    1, // should be 1 cuz sqlite doesn't support concurrency writing operation.
		MaxIdleConns:    1,
		ConnMaxLifetime: 600,
	}

	conn, err := db.Open(inMemorySqliteCfg)
	ts.Require().NoError(err)

	ts.repo = NewMigrationRepo(conn)

	ts.repo.MigrateDB(context.Background())

	// change working dir to root (and change only once)
	if _, err := os.Stat("resource"); os.IsNotExist(err) {
		_ = os.Chdir("../../")
	}
}

func TestMigration(t *testing.T) {
	suite.Run(t, new(migrationTestSuite))
}

func TestPanicIf(t *testing.T) {
	assert.Panics(t, func() {
		panicIf(errors.New("a faked error"))
	})
}

func (ts *migrationTestSuite) TestUploadSessionTableCreated() {
	// Test that upload_sessions table was created during migration
	var count int64
	err := ts.repo.db.Table("upload_sessions").Count(&count).Error
	ts.NoError(err, "upload_sessions table should exist after migration")
}

func (ts *migrationTestSuite) TestDocumentAutomationRuleTableCreated() {
	// Test that document_automation_rules table was created during migration
	var count int64
	err := ts.repo.db.Table("document_automation_rules").Count(&count).Error
	ts.NoError(err, "document_automation_rules table should exist after migration")

	// Test that we can query the table structure
	var columns []string
	rows, err := ts.repo.db.Raw("PRAGMA table_info(document_automation_rules)").Rows()
	ts.NoError(err)
	defer rows.Close()

	for rows.Next() {
		var cid int
		var name, dataType, notNull string
		var defaultValue, pk *string // Use pointers to handle NULL values
		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		ts.NoError(err)
		columns = append(columns, name)
	}

	// Verify essential columns exist
	ts.Contains(columns, "id")
	ts.Contains(columns, "tenant_id")
	ts.Contains(columns, "name")
	ts.Contains(columns, "trigger_type")
	ts.Contains(columns, "trigger_rules")
	ts.Contains(columns, "rule_config")
	ts.Contains(columns, "is_active")
	ts.Contains(columns, "created_at")
	ts.Contains(columns, "updated_at")
}
