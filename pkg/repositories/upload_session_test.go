package repositories

import (
	"bilabl/docman/domain/model"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"gitlab.com/goxp/cloud0/db"
	"gorm.io/gorm"
)

type UploadSessionRepositoryTestSuite struct {
	suite.Suite
	repo UploadSessionRepository
	db   *gorm.DB
}

func (ts *UploadSessionRepositoryTestSuite) SetupSuite() {
	var inMemorySqliteCfg = &db.Config{
		Driver:          "sqlite3",
		DSN:             "file::memory:",
		MaxOpenConns:    1,
		MaxIdleConns:    1,
		ConnMaxLifetime: 600,
	}

	conn, err := db.Open(inMemorySqliteCfg)
	ts.Require().NoError(err)
	ts.db = conn

	// Auto-migrate the upload session table
	err = conn.AutoMigrate(&model.UploadSession{})
	ts.Require().NoError(err)

	ts.repo = NewUploadSessionRepo(conn)
}

func (ts *UploadSessionRepositoryTestSuite) TearDownTest() {
	// Clean up after each test
	ts.db.Exec("DELETE FROM upload_sessions")
}

func TestUploadSessionRepository(t *testing.T) {
	suite.Run(t, new(UploadSessionRepositoryTestSuite))
}

func (ts *UploadSessionRepositoryTestSuite) TestCreate_Success() {
	ctx := context.Background()
	session := &model.UploadSession{
		SessionToken:    "test_token_123",
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}

	err := ts.repo.Create(ctx, session)
	ts.NoError(err)
	ts.NotZero(session.ID)
	ts.NotZero(session.CreatedAt)
	ts.NotZero(session.UpdatedAt)
}

func (ts *UploadSessionRepositoryTestSuite) TestCreate_InvalidTenantID() {
	ctx := context.Background()
	session := &model.UploadSession{
		SessionToken:    "test_token_123",
		TenantID:        0, // Invalid tenant ID
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}

	err := ts.repo.Create(ctx, session)
	ts.Error(err)
	ts.Contains(err.Error(), "invalid tenant ID")
}

func (ts *UploadSessionRepositoryTestSuite) TestCreate_EmptySessionToken() {
	ctx := context.Background()
	session := &model.UploadSession{
		SessionToken:    "", // Empty session token
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}

	err := ts.repo.Create(ctx, session)
	ts.Error(err)
	ts.Contains(err.Error(), "session token is required")
}

func (ts *UploadSessionRepositoryTestSuite) TestGetByToken_Success() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_456",
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Retrieve the session
	retrievedSession, err := ts.repo.GetByToken(ctx, "test_token_456")
	ts.NoError(err)
	ts.NotNil(retrievedSession)
	ts.Equal("test_token_456", retrievedSession.SessionToken)
	ts.Equal(uint64(1), retrievedSession.TenantID)
	ts.Equal("test.pdf", retrievedSession.FileName)
}

func (ts *UploadSessionRepositoryTestSuite) TestGetByToken_NotFound() {
	ctx := context.Background()

	session, err := ts.repo.GetByToken(ctx, "non_existent_token")
	ts.Error(err)
	ts.Nil(session)
	ts.Contains(err.Error(), "upload session not found")
}

func (ts *UploadSessionRepositoryTestSuite) TestGetByToken_EmptyToken() {
	ctx := context.Background()

	session, err := ts.repo.GetByToken(ctx, "")
	ts.Error(err)
	ts.Nil(session)
	ts.Contains(err.Error(), "session token is required")
}

func (ts *UploadSessionRepositoryTestSuite) TestGetByTokenAndTenant_Success() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_789",
		TenantID:        2,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Retrieve with correct tenant
	retrievedSession, err := ts.repo.GetByTokenAndTenant(ctx, "test_token_789", 2)
	ts.NoError(err)
	ts.NotNil(retrievedSession)
	ts.Equal("test_token_789", retrievedSession.SessionToken)
	ts.Equal(uint64(2), retrievedSession.TenantID)
}

func (ts *UploadSessionRepositoryTestSuite) TestGetByTokenAndTenant_WrongTenant() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_999",
		TenantID:        2,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Try to retrieve with wrong tenant
	session, err := ts.repo.GetByTokenAndTenant(ctx, "test_token_999", 3)
	ts.Error(err)
	ts.Nil(session)
	ts.Contains(err.Error(), "upload session not found")
}

func (ts *UploadSessionRepositoryTestSuite) TestUpdateStatus_Success() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_update",
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Update status
	err = ts.repo.UpdateStatus(ctx, "test_token_update", model.UploadSessionStatusUploading)
	ts.NoError(err)

	// Verify update
	updatedSession, err := ts.repo.GetByToken(ctx, "test_token_update")
	ts.NoError(err)
	ts.Equal(model.UploadSessionStatusUploading, updatedSession.Status)
}

func (ts *UploadSessionRepositoryTestSuite) TestUpdateStatus_NotFound() {
	ctx := context.Background()

	err := ts.repo.UpdateStatus(ctx, "non_existent_token", model.UploadSessionStatusCompleted)
	ts.Error(err)
	ts.Contains(err.Error(), "upload session not found")
}

func (ts *UploadSessionRepositoryTestSuite) TestUpdateStatusAndFileID_Success() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_file_id",
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Update status and file ID
	err = ts.repo.UpdateStatusAndFileID(ctx, "test_token_file_id", model.UploadSessionStatusCompleted, "google_file_123")
	ts.NoError(err)

	// Verify update
	updatedSession, err := ts.repo.GetByToken(ctx, "test_token_file_id")
	ts.NoError(err)
	ts.Equal(model.UploadSessionStatusCompleted, updatedSession.Status)
	ts.Equal("google_file_123", updatedSession.GoogleFileID)
}

func (ts *UploadSessionRepositoryTestSuite) TestUpdateFileInfo_Success() {
	ctx := context.Background()

	// Create a session first
	originalSession := &model.UploadSession{
		SessionToken:    "test_token_file_info",
		TenantID:        1,
		FileName:        "test.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour),
	}
	err := ts.repo.Create(ctx, originalSession)
	ts.NoError(err)

	// Update file info
	err = ts.repo.UpdateFileInfo(ctx, "test_token_file_info", 1024000, "application/pdf")
	ts.NoError(err)

	// Verify update
	updatedSession, err := ts.repo.GetByToken(ctx, "test_token_file_info")
	ts.NoError(err)
	ts.Equal(int64(1024000), updatedSession.FileSize)
	ts.Equal("application/pdf", updatedSession.ContentType)
}

func (ts *UploadSessionRepositoryTestSuite) TestCleanupExpired_Success() {
	ctx := context.Background()

	// Create expired session
	expiredSession := &model.UploadSession{
		SessionToken:    "expired_token",
		TenantID:        1,
		FileName:        "expired.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
	}
	err := ts.repo.Create(ctx, expiredSession)
	ts.NoError(err)

	// Create valid session
	validSession := &model.UploadSession{
		SessionToken:    "valid_token",
		TenantID:        1,
		FileName:        "valid.pdf",
		ParentDriveID:   "parent_123",
		GoogleUploadURL: "https://upload.google.com/test",
		Status:          model.UploadSessionStatusPending,
		ExpiresAt:       time.Now().Add(1 * time.Hour), // Valid for 1 hour
	}
	err = ts.repo.Create(ctx, validSession)
	ts.NoError(err)

	// Cleanup expired sessions
	deletedCount, err := ts.repo.CleanupExpired(ctx)
	ts.NoError(err)
	ts.Equal(int64(1), deletedCount)

	// Verify expired session is deleted
	_, err = ts.repo.GetByToken(ctx, "expired_token")
	ts.Error(err)

	// Verify valid session still exists
	_, err = ts.repo.GetByToken(ctx, "valid_token")
	ts.NoError(err)
}

func (ts *UploadSessionRepositoryTestSuite) TestListByTenantAndStatus_Success() {
	ctx := context.Background()

	// Create sessions for different tenants and statuses
	sessions := []*model.UploadSession{
		{
			SessionToken:    "tenant1_pending1",
			TenantID:        1,
			FileName:        "file1.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test1",
			Status:          model.UploadSessionStatusPending,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
		{
			SessionToken:    "tenant1_pending2",
			TenantID:        1,
			FileName:        "file2.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test2",
			Status:          model.UploadSessionStatusPending,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
		{
			SessionToken:    "tenant1_completed",
			TenantID:        1,
			FileName:        "file3.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test3",
			Status:          model.UploadSessionStatusCompleted,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
		{
			SessionToken:    "tenant2_pending",
			TenantID:        2,
			FileName:        "file4.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test4",
			Status:          model.UploadSessionStatusPending,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
	}

	for _, session := range sessions {
		err := ts.repo.Create(ctx, session)
		ts.NoError(err)
	}

	// List pending sessions for tenant 1
	pendingSessions, err := ts.repo.ListByTenantAndStatus(ctx, 1, model.UploadSessionStatusPending, 10, 0)
	ts.NoError(err)
	ts.Len(pendingSessions, 2)

	// List all sessions for tenant 1 (empty status)
	allSessions, err := ts.repo.ListByTenantAndStatus(ctx, 1, "", 10, 0)
	ts.NoError(err)
	ts.Len(allSessions, 3)

	// List sessions for tenant 2
	tenant2Sessions, err := ts.repo.ListByTenantAndStatus(ctx, 2, "", 10, 0)
	ts.NoError(err)
	ts.Len(tenant2Sessions, 1)
}

func (ts *UploadSessionRepositoryTestSuite) TestCountByTenantAndStatus_Success() {
	ctx := context.Background()

	// Create sessions (reuse from previous test setup)
	sessions := []*model.UploadSession{
		{
			SessionToken:    "count_tenant1_pending1",
			TenantID:        1,
			FileName:        "file1.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test1",
			Status:          model.UploadSessionStatusPending,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
		{
			SessionToken:    "count_tenant1_pending2",
			TenantID:        1,
			FileName:        "file2.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test2",
			Status:          model.UploadSessionStatusPending,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
		{
			SessionToken:    "count_tenant1_completed",
			TenantID:        1,
			FileName:        "file3.pdf",
			ParentDriveID:   "parent_123",
			GoogleUploadURL: "https://upload.google.com/test3",
			Status:          model.UploadSessionStatusCompleted,
			ExpiresAt:       time.Now().Add(1 * time.Hour),
		},
	}

	for _, session := range sessions {
		err := ts.repo.Create(ctx, session)
		ts.NoError(err)
	}

	// Count pending sessions for tenant 1
	pendingCount, err := ts.repo.CountByTenantAndStatus(ctx, 1, model.UploadSessionStatusPending)
	ts.NoError(err)
	ts.Equal(int64(2), pendingCount)

	// Count all sessions for tenant 1
	allCount, err := ts.repo.CountByTenantAndStatus(ctx, 1, "")
	ts.NoError(err)
	ts.Equal(int64(3), allCount)

	// Count sessions for non-existent tenant
	noCount, err := ts.repo.CountByTenantAndStatus(ctx, 999, "")
	ts.NoError(err)
	ts.Equal(int64(0), noCount)
}
