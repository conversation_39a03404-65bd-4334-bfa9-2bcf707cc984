package repositories

import (
	"time"

	"bilabl/docman/domain/model"

	"gorm.io/gorm"
)

// ruleExecutionHistoryRepositoryImpl implements RuleExecutionHistoryRepository
type ruleExecutionHistoryRepositoryImpl struct {
	db *gorm.DB
}

// NewRuleExecutionHistoryRepository creates a new instance of RuleExecutionHistoryRepository
func NewRuleExecutionHistoryRepository(
	db *gorm.DB,
) model.RuleExecutionHistoryRepository {
	return &ruleExecutionHistoryRepositoryImpl{
		db: db,
	}
}

// Create creates a new execution history record
func (r *ruleExecutionHistoryRepositoryImpl) Create(history *model.RuleExecutionHistory) error {
	return r.db.Create(history).Error
}

// GetByID retrieves an execution history record by ID
func (r *ruleExecutionHistoryRepositoryImpl) GetByID(id uint64) (*model.RuleExecutionHistory, error) {
	var history model.RuleExecutionHistory
	err := r.db.Where("id = ?", id).First(&history).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

// GetByRuleID retrieves execution history for a specific rule with pagination
func (r *ruleExecutionHistoryRepositoryImpl) GetByRuleID(tenantID, ruleID uint64, limit, offset int) ([]*model.RuleExecutionHistory, int64, error) {
	var histories []*model.RuleExecutionHistory
	var total int64

	// Count total records
	err := r.db.Model(&model.RuleExecutionHistory{}).
		Where("tenant_id = ? AND rule_id = ?", tenantID, ruleID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	err = r.db.Where("tenant_id = ? AND rule_id = ?", tenantID, ruleID).
		Order("started_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&histories).Error
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// GetByTenantID retrieves execution history for a tenant with pagination
func (r *ruleExecutionHistoryRepositoryImpl) GetByTenantID(tenantID uint64, limit, offset int) ([]*model.RuleExecutionHistory, int64, error) {
	var histories []*model.RuleExecutionHistory
	var total int64

	// Count total records
	err := r.db.Model(&model.RuleExecutionHistory{}).
		Where("tenant_id = ?", tenantID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	err = r.db.Where("tenant_id = ?", tenantID).
		Order("started_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&histories).Error
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// GetByStatus retrieves execution history by status with pagination
func (r *ruleExecutionHistoryRepositoryImpl) GetByStatus(tenantID uint64, status string, limit, offset int) ([]*model.RuleExecutionHistory, int64, error) {
	var histories []*model.RuleExecutionHistory
	var total int64

	// Count total records
	err := r.db.Model(&model.RuleExecutionHistory{}).
		Where("tenant_id = ? AND execution_status = ?", tenantID, status).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	err = r.db.Where("tenant_id = ? AND execution_status = ?", tenantID, status).
		Order("started_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&histories).Error
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// Update updates an execution history record
func (r *ruleExecutionHistoryRepositoryImpl) Update(history *model.RuleExecutionHistory) error {
	return r.db.Save(history).Error
}

// Delete deletes an execution history record
func (r *ruleExecutionHistoryRepositoryImpl) Delete(id uint64) error {
	return r.db.Delete(&model.RuleExecutionHistory{}, id).Error
}

// DeleteOldRecords deletes execution history records older than the specified duration
func (r *ruleExecutionHistoryRepositoryImpl) DeleteOldRecords(olderThan time.Time) error {
	return r.db.Where("started_at < ?", olderThan).Delete(&model.RuleExecutionHistory{}).Error
}
