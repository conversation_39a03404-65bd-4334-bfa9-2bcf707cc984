package repositories

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
)

type documentConfigRepositoryImpl struct {
	db *gorm.DB
	*options
}

func NewDocumentConfigRepo(
	db *gorm.DB,
	opts ...Option,
) DocumentConfigRepository {
	r := &documentConfigRepositoryImpl{
		db: db,
		options: &options{
			log: logger.DefaultBaseEntry,
		},
	}
	for _, o := range opts {
		o(r.options)
	}

	return r
}

// withTimeout is a helper method to handle context timeout with logging
func (r *documentConfigRepositoryImpl) withTimeout(ctx context.Context, operation string) (context.Context, context.CancelFunc, func()) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	start := time.Now()
	return ctx, cancel, func() {
		cancel()
		r.options.log.WithField("duration", time.Since(start).String()).
			WithField("operation", operation).Info("operation duration")
	}
}

func (r *documentConfigRepositoryImpl) Create(ctx context.Context, doc *model.DocumentConfig) (err error) {
	if doc.TenantID == 0 {
		return ErrInvalidTenant
	}

	ctx, cancel, logFunc := r.withTimeout(ctx, "Create")
	defer logFunc()
	defer cancel()

	log := bilabllog.CreateContextLogger(ctx)

	// Start transaction
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if err != nil {
			log.WithError(err).Info("rollback create transaction")
			tx.Rollback()
		} else {
			err = tx.Commit().Error // Capture commit error
		}
	}()

	// Create document config
	if err = tx.Create(doc).Error; err != nil {
		log.WithError(err).Error("error while creating document config")
		return err
	}

	return nil
}

func (r *documentConfigRepositoryImpl) Find(ctx context.Context, query *model.Query) (documentConfigs []*model.DocumentConfig, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "Find")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Model(&model.DocumentConfig{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	if query.Q != "" {
		term := "%" + strings.ToLower(query.Q) + "%"
		searchColumns := []string{"name", "config_type", "code"}
		sub := r.db
		for _, c := range searchColumns {
			sub = sub.Or(fmt.Sprintf("LOWER(%s) LIKE ?", c), term)
		}
		tx = tx.Where(sub)
	}

	tx.Order("order_config ASC")

	err = tx.Find(&documentConfigs).Error
	return documentConfigs, err
}

func (r *documentConfigRepositoryImpl) FindOne(ctx context.Context, query *model.Query) (documentConfig *model.DocumentConfig, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "FindOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Model(&model.DocumentConfig{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	// Remove unnecessary tx.Begin() for read operations
	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.First(&documentConfig).Error

	return documentConfig, err
}

func (r *documentConfigRepositoryImpl) UpdateOne(ctx context.Context, query *model.Query, documentConfig *model.DocumentConfig) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "UpdateOne")
	defer logFunc()
	defer cancel()

	tx := r.db.WithContext(ctx).Debug().Model(&model.DocumentConfig{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	documentConfig.UpdatedAt = time.Now()
	result := tx.Updates(&documentConfig)
	return result.Error
}

func (r *documentConfigRepositoryImpl) DeleteOne(ctx context.Context, query *model.Query) (id uint64, err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "DeleteOne")
	defer logFunc()
	defer cancel()

	var documentConfig *model.DocumentConfig
	tx := r.db.WithContext(ctx).Model(&model.DocumentConfig{})
	fields, values := helper.BuildFilters(query.Q, query.Filters)

	tx = tx.Where(strings.Join(fields, " AND "), values...)

	err = tx.Limit(1).Delete(nil).First(&documentConfig).Error

	return documentConfig.ID, err
}

func (r *documentConfigRepositoryImpl) Destroy(ctx context.Context) (err error) {
	ctx, cancel, logFunc := r.withTimeout(ctx, "Destroy")
	defer logFunc()
	defer cancel()

	documentConfig := model.DocumentConfig{}
	tx := r.db.Debug().WithContext(ctx).Model(&model.DocumentConfig{})

	err = tx.Exec(fmt.Sprintf("TRUNCATE TABLE %s  RESTART IDENTITY", documentConfig.TableName())).Error

	return err
}
