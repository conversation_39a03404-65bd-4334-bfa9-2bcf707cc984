package transport

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.com/goxp/cloud0/logger"
)

type Consumer interface {
	Emit(ctx context.Context, topic string, body interface{}) error
	SafeEmit(ctx context.Context, topic string, body interface{})
}

type consumerImpl struct {
	url    string
	client *http.Client
}

// SafeEmit emits an events and skip the error
// we do logging in the Emit method, we it's safe to ignore error here
func (c *consumerImpl) SafeEmit(ctx context.Context, topic string, body interface{}) {
	go func() {
		log := logger.WithCtx(ctx, "[Consumer.SafeEmit]")
		start := time.Now()
		defer func() {
			duration := time.Now().Sub(start)
			log.Debugf("emited topic: %s | duration: %d (ms)", topic, duration.Milliseconds())
		}()
		callCtx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		defer cancel()

		// copy trace-id if any
		callCtx = context.WithValue(callCtx, "x-request-id", ctx.Value("x-request-id"))

		err := c.Emit(callCtx, topic, body)
		if err != nil {
			log.Errorf("failed to emit message: %s", err)
		}
	}()
}

type ConsumerMessage struct {
	Topic string      `json:"topic"`
	Body  interface{} `json:"body"`
}

func NewConsumer(url string) Consumer {
	return &consumerImpl{
		url:    url,
		client: &http.Client{},
	}
}

func (c *consumerImpl) Emit(ctx context.Context, topic string, body interface{}) error {
	tag := "[consumer.Emit][" + topic + "]"
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	log := logger.WithCtx(ctx, tag)
	data := &ConsumerMessage{
		Topic: topic,
		Body:  body,
	}
	bodyBytes, err := json.Marshal(data)
	if err != nil {
		log.WithError(err).Error("failed to encode data")
		return err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.url, bytes.NewReader(bodyBytes))
	if err != nil {
		log.WithError(err).Error("failed to create request")
		return fmt.Errorf("failed to create request: %v", err)
	}
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}

	req.Header.Set("content-type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling consumer: %s", err)
		return fmt.Errorf("failed to request consumer: %v", err)
	}
	log.WithField("status", resp.Status).
		Infof("emit event response status: %v", resp.Status)

	// make sure we read all body & close the body
	// to reuse tcp connection
	_, _ = io.Copy(io.Discard, resp.Body)
	_ = resp.Body.Close()
	return nil
}
