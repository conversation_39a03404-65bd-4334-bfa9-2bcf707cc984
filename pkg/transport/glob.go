package transport

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.com/goxp/cloud0/logger"
)

type DownloadResp struct {
	URL string `json:"url"`
}

// PresignUploadReq represents the request for presigned upload URL
type PresignUploadReq struct {
	Filename string `json:"filename"`
}

// PresignUploadResp represents the response from presigned upload request
type PresignUploadResp struct {
	UploadURL string `json:"upload_url"`
	Key       string `json:"key"`
}

// UploadResult represents the result of a complete upload operation
type UploadResult struct {
	Key       string `json:"key"`
	UploadURL string `json:"upload_url"`
}

type Glob interface {
	EmitDownload(ctx context.Context, key string, useriD string) (rData *DownloadResp, err error)
	GetObjectSize(ctx context.Context, key string, userID string) (fileSize GlobSizeResponse, err error)
	UploadFile(ctx context.Context, filename string, fileContent io.Reader, userID string, tenantID string, userRoles string) (*UploadResult, error)
	GetFileContent(ctx context.Context, key string, userID string) ([]byte, error)
	GetPresignedUploadURL(ctx context.Context, filename string, userID string, tenantID string, userRoles string) (*PresignUploadResp, error)
}

type globImpl struct {
	client *http.Client
	url    string
}

type globMessage struct {
	Key string `json:"key"`
}

type GlobSizeResponse struct {
	Data FileSize `json:"data"`
}

type FileSize struct {
	Size int64 `json:"size"`
}

func NewGlob(url string) Glob {
	return &globImpl{
		client: &http.Client{},
		url:    url,
	}
}

func (c *globImpl) EmitDownload(ctx context.Context, key string, userID string) (urlRsp *DownloadResp, err error) {
	tag := "[glob.EmitDownload][" + key + "]"
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	log := logger.WithCtx(ctx, tag)
	reqBody := &globMessage{
		Key: key,
	}
	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		log.WithError(err).Error("failed to encode data")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.url+"/downloads", bytes.NewReader(bodyBytes))
	if err != nil {
		log.WithError(err).Error("failed to create request")
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}

	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-user-id", userID)

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling glob: %s", err)
		return nil, fmt.Errorf("failed to request glob: %v", err)
	}
	log.WithField("status", resp.Status).
		Infof("emit event response status: %v", resp.Status)

	// make sure we read all body & close the body
	// to reuse tcp connection
	defer resp.Body.Close()

	var rData struct {
		Data *DownloadResp `json:"data"`
	}
	err = json.NewDecoder(resp.Body).Decode(&rData)
	if err != nil {
		log.Errorf("error while parseng body response glob: %s", err)
		return nil, fmt.Errorf("failed to request glob: %v", err)
	}

	return rData.Data, nil
}

func (c *globImpl) GetObjectSize(ctx context.Context, key string, userID string) (fileSize GlobSizeResponse, err error) {
	tag := "[glob.GetObjectSize][" + key + "]"
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	log := logger.WithCtx(ctx, tag)
	reqBody := &globMessage{
		Key: key,
	}
	reqResp := GlobSizeResponse{}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		log.WithError(err).Error("failed to encode data")
		return reqResp, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.url+"/size", bytes.NewReader(bodyBytes))
	if err != nil {
		log.WithError(err).Error("failed to create request")
		return reqResp, fmt.Errorf("failed to create request: %v", err)
	}
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}

	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-user-id", userID)

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling glob: %s", err)
		return reqResp, fmt.Errorf("failed to request glob: %v", err)
	}

	// make sure we read all body & close the body
	// to reuse tcp connection
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return reqResp, fmt.Errorf("failed to read response body: %v", err)
	}

	err = json.Unmarshal(body, &reqResp)
	if err != nil {
		log.Errorf("reqResp err: %v", err)
	}

	return reqResp, nil
}

// UploadFile performs a 2-step upload process: presign request + file upload
func (c *globImpl) UploadFile(ctx context.Context, filename string, fileContent io.Reader, userID string, tenantID string, userRoles string) (*UploadResult, error) {
	tag := "[glob.UploadFile][" + filename + "]"
	log := logger.WithCtx(ctx, tag)

	// Step 1: Get presigned upload URL
	presignResp, err := c.getPresignedUploadURL(ctx, filename, userID, tenantID, userRoles)
	if err != nil {
		log.WithError(err).Error("failed to get presigned upload URL")
		return nil, fmt.Errorf("failed to get presigned upload URL: %w", err)
	}

	// Step 2: Upload file to presigned URL
	err = c.uploadToPresignedURL(ctx, presignResp.UploadURL, fileContent)
	if err != nil {
		log.WithError(err).Error("failed to upload file to presigned URL")
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	result := &UploadResult{
		Key:       presignResp.Key,
		UploadURL: presignResp.UploadURL,
	}

	log.Infof("Successfully uploaded file key=%s", result.Key)
	return result, nil
}

// GetPresignedUploadURL gets a presigned upload URL from glob service (public interface method)
func (c *globImpl) GetPresignedUploadURL(ctx context.Context, filename string, userID string, tenantID string, userRoles string) (*PresignUploadResp, error) {
	return c.getPresignedUploadURL(ctx, filename, userID, tenantID, userRoles)
}

// getPresignedUploadURL gets a presigned upload URL from glob service (internal implementation)
func (c *globImpl) getPresignedUploadURL(ctx context.Context, filename string, userID string, tenantID string, userRoles string) (*PresignUploadResp, error) {
	tag := "[glob.getPresignedUploadURL][" + filename + "]"
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	log := logger.WithCtx(ctx, tag)

	reqBody := &PresignUploadReq{
		Filename: filename,
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		log.WithError(err).Error("failed to encode data")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.url+"/presign", bytes.NewReader(bodyBytes))
	if err != nil {
		log.WithError(err).Error("failed to create request")
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}
	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-user-id", userID)
	req.Header.Set("x-tenant-id", tenantID)
	req.Header.Set("x-user-roles", userRoles)

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling glob presign: %s", err)
		return nil, fmt.Errorf("failed to request glob presign: %v", err)
	}

	log.WithField("status", resp.Status).
		Infof("presign response status: %v", resp.Status)

	defer resp.Body.Close()

	var rData struct {
		Data *PresignUploadResp `json:"data"`
	}
	err = json.NewDecoder(resp.Body).Decode(&rData)
	if err != nil {
		log.Errorf("error while parsing presign response body: %s", err)
		return nil, fmt.Errorf("failed to parse presign response: %v", err)
	}

	return rData.Data, nil
}

// uploadToPresignedURL uploads file content to the presigned URL
func (c *globImpl) uploadToPresignedURL(ctx context.Context, uploadURL string, fileContent io.Reader) error {
	tag := "[glob.uploadToPresignedURL]"
	ctx, cancel := context.WithTimeout(ctx, time.Minute*5) // Longer timeout for file upload
	defer cancel()
	log := logger.WithCtx(ctx, tag)

	req, err := http.NewRequestWithContext(ctx, http.MethodPut, uploadURL, fileContent)
	if err != nil {
		log.WithError(err).Error("failed to create upload request")
		return fmt.Errorf("failed to create upload request: %v", err)
	}

	// Set content type for file upload
	req.Header.Set("content-type", "application/octet-stream")

	// Set request ID if available
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while uploading file: %s", err)
		return fmt.Errorf("failed to upload file: %v", err)
	}

	defer resp.Body.Close()

	log.WithField("status", resp.Status).
		Infof("upload response status: %v", resp.Status)

	// Check if upload was successful (200 OK)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
		return fmt.Errorf("upload failed with status %d", resp.StatusCode)
	}

	return nil
}

// GetFileContent gets file content by key using EmitDownload + HTTP GET
func (c *globImpl) GetFileContent(ctx context.Context, key string, userID string) ([]byte, error) {
	tag := "[glob.GetFileContent][" + key + "]"
	log := logger.WithCtx(ctx, tag)

	// Step 1: Get download URL using EmitDownload
	downloadResp, err := c.EmitDownload(ctx, key, userID)
	if err != nil {
		log.WithError(err).Error("failed to get download URL")
		return nil, fmt.Errorf("failed to get download URL: %w", err)
	}

	if downloadResp == nil || downloadResp.URL == "" {
		log.Error("download response is empty or missing URL")
		return nil, fmt.Errorf("download URL is empty")
	}

	// Step 2: Download file content from the URL
	content, err := c.downloadFileFromURL(ctx, downloadResp.URL)
	if err != nil {
		log.WithError(err).Error("failed to download file content")
		return nil, fmt.Errorf("failed to download file content: %w", err)
	}

	log.Infof("Successfully downloaded file content size=%d bytes", len(content))
	return content, nil
}

// downloadFileFromURL downloads file content from a given URL
func (c *globImpl) downloadFileFromURL(ctx context.Context, downloadURL string) ([]byte, error) {
	tag := "[glob.downloadFileFromURL]"
	ctx, cancel := context.WithTimeout(ctx, time.Minute*5) // Longer timeout for file download
	defer cancel()
	log := logger.WithCtx(ctx, tag)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, downloadURL, nil)
	if err != nil {
		log.WithError(err).Error("failed to create download request")
		return nil, fmt.Errorf("failed to create download request: %v", err)
	}

	// Set request ID if available
	if _requestID := ctx.Value("x-request-id"); _requestID != nil {
		if _s, ok := _requestID.(string); ok {
			req.Header.Set("x-request-id", _s)
		}
	}

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while downloading file: %s", err)
		return nil, fmt.Errorf("failed to download file: %v", err)
	}

	defer resp.Body.Close()

	log.WithField("status", resp.Status).
		Infof("download response status: %v", resp.Status)

	// Check if download was successful (200 OK)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("download failed with status %d: %s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("download failed with status %d", resp.StatusCode)
	}

	// Read file content
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read file content")
		return nil, fmt.Errorf("failed to read file content: %v", err)
	}

	return content, nil
}
