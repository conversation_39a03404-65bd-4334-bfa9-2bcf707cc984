package transport

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/domain/userrole"
	"code.mybil.net/gophers/gokit/pkg/httpwrapper"
	"code.mybil.net/gophers/gokit/util/sliceutil"

	"gitlab.com/goxp/cloud0/logger"
)

const (
	listUserPath = "/users"
)

type TeamBody struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

type TeamResponse struct {
	Data TeamBody `json:"data"`
}

type ListUsersItem struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Email     string `json:"email"`
	AvatarUrl string `json:"avatar_url"`
}

type ListUsersResponse struct {
	Data []*ListUsersItem `json:"data"`
}

type Hrglassid interface {
	GetTeamByID(ctx context.Context, teamID uint64, tenantID uint64) (TeamResponse, error)
	GetTeamListUsersByID(ctx context.Context, userID uint64, tenantID uint64) (ListUsersResponse, error)
	ListUsers(ctx context.Context, ids []entity.ID) ([]*ListUsersItem, error)
	UpdateSharepointIntegration(ctx context.Context, query url.Values) error
}

type hrglassidImpl struct {
	url    string
	client *http.Client
	wire   httpwrapper.Client
}

func NewHrglassid(url string) Hrglassid {
	return &hrglassidImpl{
		url:    url,
		client: &http.Client{},
		wire:   httpwrapper.NewDefaultHttpWrapper(),
	}
}

func (c *hrglassidImpl) buildURL(path string, query url.Values) string {
	if path != "" && path[0] != '/' {
		path = "/" + path
	}
	return c.url + path + "?" + query.Encode()
}

func (c *hrglassidImpl) GetTeamByID(ctx context.Context, teamID uint64, tenantID uint64) (TeamResponse, error) {
	tag := "[hrglassid.GetTeamByID]"
	log := logger.WithCtx(ctx, tag)
	result := TeamResponse{}

	ctx, cancel := context.WithTimeout(ctx, time.Second*defaultTimeoutSeconds)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, c.url+"/teams/"+strconv.FormatUint(teamID, 10), nil)
	if err != nil {
		log.Errorf("failed to create request: %s", err)
		return result, err
	}

	// Added header
	req.Header.Set(userAgentHeader, defaultUserAgent)
	req.Header.Set(tenantIDHeader, strconv.FormatUint(tenantID, 10))
	req.Header.Set(userIDHeader, strconv.FormatUint(1, 10))
	req.Header.Set(userRolesHeader, strconv.Itoa(16))

	// Local development
	// req.Header.Set("Authorization", "Bearer x.y.z")

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling hrglassid: %s", err)
		return result, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, fmt.Errorf("failed to read response body: %v", err)
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Errorf("body unmarshal error: %v", err)
	}

	return result, nil
}

func (c *hrglassidImpl) GetTeamListUsersByID(ctx context.Context, userID uint64, tenantID uint64) (ListUsersResponse, error) {
	tag := "[hrglassid.GetTeamListUsersByID]"
	log := logger.WithCtx(ctx, tag)
	result := ListUsersResponse{}

	values := url.Values{}
	values.Add("include_deleted", "true")
	values.Add("page_size", "20")
	values.Add("list_id", strconv.FormatUint(userID, 10))

	path := c.buildURL(listUserPath, values)
	// log.Infof("get url: `%s`", path)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, path, nil)
	if err != nil {
		log.Errorf("failed to create request: %s", err)
		return result, err
	}

	// Added header
	req.Header.Set(userAgentHeader, defaultUserAgent)
	req.Header.Set(tenantIDHeader, strconv.FormatUint(tenantID, 10))
	req.Header.Set(userIDHeader, strconv.FormatUint(1, 10))
	req.Header.Set(userRolesHeader, strconv.Itoa(16))

	// Local development
	// req.Header.Set("Authorization", "Bearer x.y.z")

	resp, err := c.client.Do(req)
	if err != nil {
		log.Errorf("error while calling hrglassid: %s", err)
		return result, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, fmt.Errorf("failed to read response body: %v", err)
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Errorf("body unmarshal error: %v", err)
	}

	return result, nil
}

func (c *hrglassidImpl) ListUsers(ctx context.Context, ids []entity.ID) ([]*ListUsersItem, error) {
	values := url.Values{}
	values.Add("include_deleted", "true")
	values.Add("page_size", "20")
	values.Add("list_id", strings.Join(sliceutil.Map(ids, func(id entity.ID) string { return strconv.FormatUint(uint64(id), 10) }), ","))
	path := c.buildURL(listUserPath, values)
	req := httpwrapper.NewRequest(http.MethodGet, path, nil)
	req.SetRequestActor(&actor.Actor{
		ID:       1,
		TenantID: 1,
		Roles:    userrole.RoleSysadmin,
	})
	resp := &ListUsersResponse{}
	_, err := c.wire.Do(ctx, req, resp)
	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

func (c *hrglassidImpl) UpdateSharepointIntegration(ctx context.Context, query url.Values) error {
	reqUrl := c.buildURL("/oauth2/callback/sharepoint-admin-consent", query)
	req := httpwrapper.NewRequest(http.MethodGet, reqUrl, nil)
	_, err := c.wire.Do(ctx, req, nil)
	return err
}
