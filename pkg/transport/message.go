package transport

// message
const (
	ActivityCreationTopic = "activity.creation"
)

// object type
const (
	ObjectTypeClient = "client"
	ObjectTypeStaff  = "staff"
	ObjectTypeMatter = "matter"
)

// document
const (
	DocumentObjectTypeClient = 1
	DocumentObjectTypeStaff  = 2
	DocumentObjectTypeMatter = 3
	DocumentObjectTypeTask   = 9
)

// activity
const (
	ActivityTypeDocument = 4
)

// activity
const (
	ActivityObjectTypeStaff  = 1
	ActivityObjectTypeClient = 2
	ActivityObjectTypeMatter = 3
	ActivityObjectTypeTask   = 16
)

// action type
const (
	ActionTypeUpdate ActionType = "update"
	ActionTypeCreate ActionType = "create"
	ActionTypeDelete ActionType = "delete"
)

// action
const (
	ActionAdd    ActivityAction = 1
	ActionUpdate ActivityAction = 2
	ActionDelete ActivityAction = 3
)

type ActionType string

type ActivityAction int64

type GeneralEventMessage struct {
	ObjectType    uint           `json:"object_type"`
	ObjectID      uint64         `json:"object_id"`
	SubObjectType uint           `json:"sub_object_type"`
	SubObjectID   uint64         `json:"sub_object_id"`
	ActionType    ActionType     `json:"action_type"`
	Action        ActivityAction `json:"action"`
	Type          uint           `json:"type"`
	ActorID       uint64         `json:"actor_id"`
	TenantID      uint64         `json:"tenant_id"`
	Extra         EventExtraData `json:"extra"`
}

type EventExtraData struct {
	ID      uint64 `json:"id"`
	DocID   string `json:"doc_id"`
	Name    string `json:"name"`
	Key     string `json:"key"`
	DocType string `json:"doc_type"`
}
