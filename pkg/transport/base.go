package transport

const (
	defaultTimeoutSeconds = 10
	defaultPageSize       = 200
	defaultUserAgent      = "bulk"
	requestIDHeader       = "x-request-id"
	userIDHeader          = "x-user-id"
	userRolesHeader       = "x-user-roles"
	tenantIDHeader        = "x-tenant-id"
	userAgentHeader       = "user-agent"
)

type MetaData struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Pages      int   `json:"pages"`
	TotalPages int   `json:"total_pages"`
	TotalRows  int64 `json:"total"`
}
