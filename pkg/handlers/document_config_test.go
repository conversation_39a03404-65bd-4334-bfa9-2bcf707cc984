package handlers

import (
	"net/http"
	"testing"

	"bilabl/docman/domain"
	"bilabl/docman/domain/model"
	mocks "bilabl/docman/mocks/repositories"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

type DocumentConfigHandlerTestSuite struct {
	suite.Suite
	mockRepo              *mocks.MockDocumentConfigRepository
	documentConfigHandler *documentConfigHandler
	mockDocumentConfig    []*model.DocumentConfig
}

func TestDocumentConfigTestSuite(t *testing.T) {
	suite.Run(t, new(DocumentConfigHandlerTestSuite))
}

func (d *DocumentConfigHandlerTestSuite) SetupTest() {
	// Initialize mock repository
	d.mockRepo = new(mocks.MockDocumentConfigRepository)
	d.documentConfigHandler = NewDocumentConfigHandler(d.mockRepo)

	// Setup mock data
	d.mockDocumentConfig = []*model.DocumentConfig{
		{
			Name:        "Adherence to Internal Labour Rules / IT Policy etc ",
			TenantID:    1,
			CreatedUser: 1,
			ConfigType:  "STAFF_DOCUMENT_TYPE",
			Code:        "",
		},
		{
			Name:        "Bar Membership",
			TenantID:    1,
			CreatedUser: 1,
			ConfigType:  "STAFF_DOCUMENT_TYPE",
			Code:        "",
		},
	}
}

func (d *DocumentConfigHandlerTestSuite) TestListDocumentConfig() {
	d.Run("should not get document config list invalid tenant", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/configs", nil,
			d.documentConfigHandler.List, func(r *http.Request) {
				r.Header.Set("content-type", "application/json")
			})

		d.Require().Equal(http.StatusBadRequest, w.Code)
	})
	d.Run("should get document config list", func() {
		d.SetupTest()

		// Setup mock behavior for Find method
		d.mockRepo.On("Find", mock.Anything, mock.MatchedBy(func(query *model.Query) bool {
			// Verify query has correct tenant_id filter
			for _, filter := range query.Filters {
				if filter.Key == "tenant_id" && filter.Value.(uint64) == uint64(1) {
					return true
				}
			}
			return false
		})).Return(d.mockDocumentConfig, nil)

		w := doTestRequestOptions(http.MethodGet, "/v1/configs", nil,
			d.documentConfigHandler.List, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			})

		d.Require().Equal(http.StatusOK, w.Code)
		d.mockRepo.AssertExpectations(d.T())
	})
}

func (d *DocumentConfigHandlerTestSuite) TestCreateDocumentConfig() {
	d.Run("should create document config", func() {
		d.SetupTest()

		// Setup expected document config
		expectedDoc := &model.DocumentConfig{
			Name:        "Test document config",
			ConfigType:  "STAFF_DOCUMENT_TYPE",
			OrderConfig: 10,
			TenantID:    1,
		}

		// Setup mock behavior
		d.mockRepo.On("Create", mock.Anything, mock.MatchedBy(func(doc *model.DocumentConfig) bool {
			return doc.Name == expectedDoc.Name &&
				doc.ConfigType == expectedDoc.ConfigType &&
				doc.OrderConfig == expectedDoc.OrderConfig &&
				doc.TenantID == expectedDoc.TenantID
		})).Return(nil)

		// Test request
		payload := &domain.CreateDocumentConfig{
			Name:       expectedDoc.Name,
			ConfigType: expectedDoc.ConfigType,
			Order:      expectedDoc.OrderConfig,
		}

		w := doTestRequestOptions(http.MethodPost, "/v1/configs", payload,
			d.documentConfigHandler.Create, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			})

		d.Require().Equal(http.StatusOK, w.Code)
		d.mockRepo.AssertExpectations(d.T())
	})
}
