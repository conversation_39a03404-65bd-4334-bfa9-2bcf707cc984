package handlers

import (
	"bilabl/docman/domain"
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"
	"encoding/json"
	"net/http"
	"os"
	"strconv"

	"gitlab.com/goxp/cloud0/ginext"
)

type documentConfigHandler struct {
	rDocumentConfig repositories.DocumentConfigRepository
}

func NewDocumentConfigHandler(
	rDocumentConfig repositories.DocumentConfigRepository,
) *documentConfigHandler {
	return &documentConfigHandler{
		rDocumentConfig: rDocumentConfig,
	}
}

type seedDocumentConfig struct {
	ConfigType string `json:"config_type"`
	Name       string `json:"name"`
	Code       string `json:"code"`
	Order      int    `json:"order"`
}

type respDocumentConfig struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	ConfigType string `json:"config_type"`
	Code       string `json:"code"`
}

func DocumentConfigsResponse(inputs []*model.DocumentConfig) []*respDocumentConfig {
	output := make([]*respDocumentConfig, 0)
	for _, v := range inputs {
		output = append(output, SingleDocumentConfigResponse(v))
	}
	return output

}
func SingleDocumentConfigResponse(input *model.DocumentConfig) *respDocumentConfig {
	if input == nil {
		return nil
	}
	return &respDocumentConfig{
		ID:         input.ID,
		Name:       input.Name,
		ConfigType: input.ConfigType,
		Code:       input.Code,
	}
}

func (h *documentConfigHandler) createDocumentConfigFilter(r *ginext.Request) *model.Query {
	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)

	query := &domain.ListDocumentConfigsFilter{}
	// Get paginatn filter
	r.MustBind(query)
	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}
	if query.ConfigType != "" {
		filters = append(filters, &model.Filter{
			Key:    "config_type",
			Method: "=",
			Value:  query.ConfigType,
		})
	}

	sorts := []*model.Sort{
		{
			Key:    "config_type",
			SortBy: "ASC",
		},
		{
			Key:    "document_configs.order",
			SortBy: "ASC",
		},
	}

	queryBuild := helper.BuildQuery(query.Search, filters, sorts, nil)
	return queryBuild
}

func (h *documentConfigHandler) List(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	queryBuild := h.createDocumentConfigFilter(r)
	documentConfigs, err := h.rDocumentConfig.Find(r.Context(), queryBuild)

	r.MustNoError(err)

	resp := ginext.NewResponseData(http.StatusOK, DocumentConfigsResponse(documentConfigs))
	return resp, nil
}

func (h *documentConfigHandler) SeedDocumentConfig(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	queryBuild := h.createDocumentConfigFilter(r)
	documentConfigs, _ := h.rDocumentConfig.Find(r.Context(), queryBuild)

	if len(documentConfigs) > 0 {
		log.Error("tenant document configs has been inited " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "tenant document configs has been inited")
	}

	dirname, _ := os.Getwd()
	file, err := os.ReadFile(dirname + "/pkg/resources/seed_documentconfigs.json")
	if err != nil {
		log.WithError(err).Info("SeedDocumentConfig")
	}
	var data []seedDocumentConfig
	_ = json.Unmarshal(file, &data)

	for _, item := range data {
		documentConfig := model.DocumentConfig{
			ConfigType:  item.ConfigType,
			Name:        item.Name,
			Code:        item.Code,
			OrderConfig: item.Order,
			TenantID:    tenantID,
		}
		err := h.rDocumentConfig.Create(r.Context(), &documentConfig)

		if err != nil {
			log.WithError(err).Info("rDocumentConfig.Create")
		}
	}
	resp := ginext.NewResponseData(http.StatusOK, nil)
	return resp, nil
}

func (h *documentConfigHandler) Create(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	body := &domain.CreateDocumentConfig{}
	r.MustBind(body)

	tenantID := ginext.Uint64TenantID(r.GinCtx)

	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	// Create new document config
	documentConfig := &model.DocumentConfig{
		Name:        body.Name,
		Code:        body.Code,
		ConfigType:  body.ConfigType,
		OrderConfig: body.Order,
		TenantID:    tenantID,
	}

	// Create in repository
	if err := h.rDocumentConfig.Create(r.Context(), documentConfig); err != nil {
		log.WithError(err).Error("failed to create document config")
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	// Return response
	resp := ginext.NewResponseData(http.StatusOK, SingleDocumentConfigResponse(documentConfig))
	return resp, nil
}
