package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/sharepointclient"
	"errors"
	"fmt"
	"html/template"
	"net/url"
	"strconv"
	"strings"
	"time"
)

type sharepointSetupArgs struct {
	Token     string
	ListSites []*sharepointclient.SiteResponse
}

func (h *SharepointHandler) loadSharepointSetupArgs(r req, externalTenantID string) (*sharepointSetupArgs, error) {
	token, err := h.spClient.ExchangeTokenBySecret(r.Context(), externalTenantID)
	if err != nil || token == "" {
		return nil, errors.New("error while exchanging token")
	}

	listDrives, err := h.spClient.GetListSite(r.Context(), token)
	if err != nil {
		return nil, errors.New("error while getting list of sites")
	}

	return &sharepointSetupArgs{
		Token:     token,
		ListSites: listDrives.Value,
	}, nil
}

func (h *SharepointHandler) SetupCallback(r req) (res, error) {
	// print the error message if any issue occurs
	// or show the form to choose the sharepoint site
	// then submit to complete the setup
	//log := logger.WithCtx(ctx, "sharepoint.setupCallback")

	checkErr := func() string {
		if r.Query("error") == "access_denied" || r.Query("admin_consent") != "True" {
			return "Access denied. Please try again."
		}
		return ""
	}

	writeErr := func(errMsg string) (res, error) {
		_, _ = r.GinCtx.Writer.WriteString(errMsg)
		return nil, nil
	}

	if errMsg := checkErr(); errMsg != "" {
		return writeErr(errMsg)
	}

	externalTenantID := r.Query("tenant")
	state := r.Query("state") // tenant_id|integration_id|token
	stateParts := strings.Split(state, "|")
	_, err := strconv.ParseUint(stateParts[0], 10, 64)
	if err != nil {
		return writeErr("Invalid state parameter, please try again.")
	}

	args, err := h.loadSharepointSetupArgs(r, externalTenantID)
	if err != nil {
		return writeErr(err.Error() + ", please try again.")
	}

	submitUrl := h.publicBaseUrl + "/sharepoint/complete-setup"

	tmpl := template.Must(template.ParseFiles("resources/templates/sharepoint-choose-site.html"))
	data := map[string]any{
		"SubmitUrl":        submitUrl,
		"ExternalTenantID": externalTenantID,
		"Sites":            args.ListSites,
		"TenantID":         stateParts[0],
	}

	r.GinCtx.Writer.Header().Set("Content-Type", "text/html")
	_ = tmpl.Execute(r.GinCtx.Writer, data)
	return nil, nil
}

func (h *SharepointHandler) CompleteSetup(r req) (res, error) {
	//log := logger.WithCtx(ctx, "sharepoint.completeSetup")

	writeErr := func(errMsg string) (res, error) {
		_, _ = r.GinCtx.Writer.WriteString(errMsg)
		return nil, nil
	}

	externalTenantID := r.GinCtx.PostForm("external_tenant_id")
	siteID := r.GinCtx.PostForm("site_id")
	tenantID_ := r.GinCtx.PostForm("tenant_id")
	tenantID, _ := strconv.ParseUint(tenantID_, 10, 64)

	if externalTenantID == "" || siteID == "" || tenantID_ == "" {
		return writeErr("invalid parameters")
	}

	args, err := h.loadSharepointSetupArgs(r, externalTenantID)
	if err != nil {
		return writeErr("error while loading sharepoint setup args")
	}

	// load driveId
	// just need to take the first drive in site

	drives, err := h.spClient.GetListDrive(r.Context(), siteID, args.Token)
	if err != nil {
		return writeErr("error while getting list of drives")
	}

	if len(drives.Value) == 0 {
		return writeErr("no drive found")
	}

	records := []*model.DocumentSetting{
		{
			TenantID: tenantID,
			Key:      model.KeyExternalTenantID,
			Value:    externalTenantID,
		},
		{
			TenantID: tenantID,
			Key:      model.KeySharepointSiteID,
			Value:    siteID,
		},
		{
			TenantID:  tenantID,
			Key:       model.KeyAccessToken,
			Value:     args.Token,
			ExpiredAt: time.Now().Local().Add(time.Second * time.Duration(3_000)),
		},
		{
			TenantID: tenantID,
			Key:      model.KeySharepointDriveID,
			Value:    drives.Value[0].ID,
		},
		{
			TenantID: tenantID,
			Key:      model.KeyMappingFolderName,
			Value:    "Matters Folder",
		},
	}

	for _, rec := range records {
		if err := h.rDocumentSetting.Create(r.Context(), rec); err != nil {
			return nil, err
		}
	}

	// call hrglassid
	val := url.Values{}
	val.Add("admin_consent", "True")
	val.Add("tenant", externalTenantID)
	val.Add("state", tenantID_)
	err = h.idClient.UpdateSharepointIntegration(r.Context(), val)
	if err != nil {
		m := fmt.Sprintf(
			"error while updating sharepoint integration, "+
				"please give this for bilabl support: `tenant:%s, x_tenant:%s`", tenantID_, externalTenantID,
		)
		return writeErr(m)
	}

	_, _ = r.GinCtx.Writer.WriteString("Setup completed. You can close this window now.")
	return nil, nil
}
