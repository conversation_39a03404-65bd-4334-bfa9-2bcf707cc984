package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"
	"code.mybil.net/gophers/gokit/util/sliceutil"
	"code.mybil.net/gophers/gokit/util/valutil"
	"gitlab.com/goxp/cloud0/ginext"
	"net/http"
)

func (h *SharepointHandler) handleClientUpdate(
	r *ginext.Request,
	body *model.ConsumeClientReq,
	spParams *sharepointParams,
	clientFolder string,
) (*ginext.Response, error) {

	ctx := r.Context()
	clientDrive, err := h.createClientDriveFolder(ctx, spParams, clientFolder, body.Body.ID)
	if err != nil {
		return nil, err
	}

	// Update the client folder name
	_, err = h.spClient.RenameDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, clientDrive.DriveID, clientFolder)
	if err != nil {
		return nil, err
	}

	_, err = h.createMatterParentFolder(ctx, spParams, clientDrive)
	if err != nil {
		return nil, err
	}

	currentPermOwners, err := h.rDocumentPermission.Find(ctx, &repositories.PermMapFindArgs{
		TenantID: valutil.CreatePtr(body.Body.TenantID),
		DriveID:  valutil.CreatePtr(clientDrive.DriveID),
		Provider: valutil.CreatePtr([]string{model.DocProviderSharepoint}),
		Emails: valutil.CreatePtr(sliceutil.Map(body.Body.Extra.Old.OwnerUsers, func(o model.OwnerUser) string {
			return o.Email
		})),
	})
	r.MustNoError(err)

	err = h.updatePermissions(ctx, spParams, clientDrive, currentPermOwners, body.Body.Extra.Old.OwnerUsers, body.Body.Extra.Current.OwnerUsers)

	return ginext.NewResponseData(http.StatusOK, nil), err
}
