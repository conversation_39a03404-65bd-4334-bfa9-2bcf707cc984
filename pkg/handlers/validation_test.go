package handlers

import (
	"testing"

	"bilabl/docman/domain/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidateStruct_CreateRuleRequest(t *testing.T) {
	tests := []struct {
		name           string
		request        CreateRuleRequest
		expectedErrors int
		expectedFields []string
	}{
		{
			name: "valid request",
			request: CreateRuleRequest{
				Name:        "Test Rule",
				Description: "Test description",
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "copy_file",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 0,
			expectedFields: []string{},
		},
		{
			name: "missing required fields",
			request: CreateRuleRequest{
				Description: "Test description",
				IsActive:    true,
			},
			expectedErrors: 3, // Updated to match actual validation errors
			expectedFields: []string{"name", "trigger_type", "rule_config"},
		},
		{
			name: "invalid rule name",
			request: CreateRuleRequest{
				Name:        "Invalid@Name#$%",
				Description: "Test description",
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "copy_file",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"name"},
		},
		{
			name: "invalid trigger type",
			request: CreateRuleRequest{
				Name:        "Test Rule",
				Description: "Test description",
				TriggerType: "invalid.trigger",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "copy_file",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"trigger_type"},
		},
		{
			name: "empty rule config",
			request: CreateRuleRequest{
				Name:        "Test Rule",
				Description: "Test description",
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig:  model.RuleConfigArray{},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"rule_config"},
		},
		{
			name: "invalid action type in rule config",
			request: CreateRuleRequest{
				Name:        "Test Rule",
				Description: "Test description",
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "invalid_action",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"rule_config"},
		},
		{
			name: "name too long",
			request: CreateRuleRequest{
				Name:        string(make([]byte, 256)), // 256 chars, exceeds max of 255
				Description: "Test description",
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "copy_file",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"name"},
		},
		{
			name: "description too long",
			request: CreateRuleRequest{
				Name:        "Test Rule",
				Description: string(make([]byte, 1001)), // 1001 chars, exceeds max of 1000
				TriggerType: "matter.create",
				TriggerRules: model.TriggerRulesMap{
					"matter_type": "litigation",
				},
				RuleConfig: model.RuleConfigArray{
					{
						ActionType: "copy_file",
						SourcePath: "/templates/matter.docx",
						TargetPath: "/matters/{{.MatterID}}/matter.docx",
					},
				},
				IsActive:    true,
				CreatedUser: 123,
			},
			expectedErrors: 1,
			expectedFields: []string{"description"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateStruct(tt.request)

			assert.Equal(t, tt.expectedErrors, len(errors), "Expected %d errors, got %d", tt.expectedErrors, len(errors))

			if tt.expectedErrors > 0 {
				errorFields := make([]string, len(errors))
				for i, err := range errors {
					errorFields[i] = err.Field
				}

				for _, expectedField := range tt.expectedFields {
					assert.Contains(t, errorFields, expectedField, "Expected validation error for field %s", expectedField)
				}
			}
		})
	}
}

func TestValidateStruct_UpdateRuleRequest(t *testing.T) {
	tests := []struct {
		name           string
		request        UpdateRuleRequest
		expectedErrors int
		expectedFields []string
	}{
		{
			name: "valid request with all fields",
			request: UpdateRuleRequest{
				Name:        stringPtr("Updated Rule"),
				Description: stringPtr("Updated description"),
				TriggerType: stringPtr("client.update"),
				TriggerRules: &model.TriggerRulesMap{
					"client_type": "corporate",
				},
				RuleConfig: &model.RuleConfigArray{
					{
						ActionType: "copy_folder",
						SourcePath: "/templates/client",
						TargetPath: "/clients/{{.ClientID}}",
					},
				},
				IsActive:    boolPtr(false),
				UpdatedUser: 456,
			},
			expectedErrors: 0,
			expectedFields: []string{},
		},
		{
			name: "valid request with partial fields",
			request: UpdateRuleRequest{
				Name:        stringPtr("Updated Rule"),
				UpdatedUser: 456,
			},
			expectedErrors: 0,
			expectedFields: []string{},
		},
		{
			name: "invalid name",
			request: UpdateRuleRequest{
				Name:        stringPtr("Invalid@Name#$%"),
				UpdatedUser: 456,
			},
			expectedErrors: 1,
			expectedFields: []string{"name"},
		},
		{
			name: "invalid trigger type",
			request: UpdateRuleRequest{
				TriggerType: stringPtr("invalid.trigger"),
				UpdatedUser: 456,
			},
			expectedErrors: 1,
			expectedFields: []string{"trigger_type"},
		},
		{
			name: "empty rule config",
			request: UpdateRuleRequest{
				RuleConfig:  &model.RuleConfigArray{},
				UpdatedUser: 456,
			},
			expectedErrors: 1,
			expectedFields: []string{"rule_config"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateStruct(tt.request)

			assert.Equal(t, tt.expectedErrors, len(errors), "Expected %d errors, got %d", tt.expectedErrors, len(errors))

			if tt.expectedErrors > 0 {
				errorFields := make([]string, len(errors))
				for i, err := range errors {
					errorFields[i] = err.Field
				}

				for _, expectedField := range tt.expectedFields {
					assert.Contains(t, errorFields, expectedField, "Expected validation error for field %s", expectedField)
				}
			}
		})
	}
}

func TestValidateTriggerRulesContent(t *testing.T) {
	tests := []struct {
		name        string
		triggerType string
		rules       model.TriggerRulesMap
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid matter.create rules",
			triggerType: "matter.create",
			rules: model.TriggerRulesMap{
				"matter_type":   "litigation",
				"practice_area": "corporate",
			},
			expectError: false,
		},
		{
			name:        "valid matter.update rules",
			triggerType: "matter.update",
			rules: model.TriggerRulesMap{
				"matter_type":   "litigation",
				"practice_area": "corporate",
			},
			expectError: false,
		},
		{
			name:        "valid client.create rules",
			triggerType: "client.create",
			rules: model.TriggerRulesMap{
				"client_type":   "corporate",
				"client_status": "active",
			},
			expectError: false,
		},
		{
			name:        "valid client.update rules",
			triggerType: "client.update",
			rules: model.TriggerRulesMap{
				"client_type":   "corporate",
				"client_status": "active",
			},
			expectError: false,
		},
		{
			name:        "invalid field for matter.create",
			triggerType: "matter.create",
			rules: model.TriggerRulesMap{
				"invalid_field": "value",
			},
			expectError: true,
			errorMsg:    "invalid field for matter.create trigger: invalid_field",
		},
		{
			name:        "invalid field for matter.update",
			triggerType: "matter.update",
			rules: model.TriggerRulesMap{
				"invalid_field": "value",
			},
			expectError: true,
			errorMsg:    "invalid field for matter.update trigger: invalid_field",
		},
		{
			name:        "invalid field for client.create",
			triggerType: "client.create",
			rules: model.TriggerRulesMap{
				"invalid_field": "value",
			},
			expectError: true,
			errorMsg:    "invalid field for client.create trigger: invalid_field",
		},
		{
			name:        "invalid field for client.update",
			triggerType: "client.update",
			rules: model.TriggerRulesMap{
				"invalid_field": "value",
			},
			expectError: true,
			errorMsg:    "invalid field for client.update trigger: invalid_field",
		},
		{
			name:        "unsupported trigger type",
			triggerType: "unsupported.trigger",
			rules: model.TriggerRulesMap{
				"field": "value",
			},
			expectError: true,
			errorMsg:    "unsupported trigger type: unsupported.trigger",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateTriggerRulesContent(tt.triggerType, tt.rules)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateTriggerRulesContentWithChangeDetection(t *testing.T) {
	tests := []struct {
		name        string
		triggerType string
		rules       model.TriggerRulesMap
		expectError bool
		expectMsg   string
		errorMsg    string
	}{
		{
			name:        "matter.update with change detection",
			triggerType: "matter.update",
			rules: model.TriggerRulesMap{
				"extra.current.category_name":  "new_category",
				"extra.previous.category_name": "old_category",
			},
			expectError: false,
			expectMsg:   "",
		},
		{
			name:        "matter.update without change detection",
			triggerType: "matter.update",
			rules: model.TriggerRulesMap{
				"matter_type": "litigation",
			},
			expectError: false,
			expectMsg:   "Warning: matter.update rule does not check for data changes. Consider adding previous/current field comparisons to ensure rule only triggers when data actually changes.",
		},
		{
			name:        "client.update with change detection",
			triggerType: "client.update",
			rules: model.TriggerRulesMap{
				"extra.current.stage_text":  "new_stage",
				"extra.previous.stage_text": "old_stage",
			},
			expectError: false,
			expectMsg:   "",
		},
		{
			name:        "client.update without change detection",
			triggerType: "client.update",
			rules: model.TriggerRulesMap{
				"client_type": "corporate",
			},
			expectError: false,
			expectMsg:   "Warning: client.update rule does not check for data changes. Consider adding previous/current field comparisons to ensure rule only triggers when data actually changes.",
		},
		{
			name:        "client.create no warning",
			triggerType: "client.create",
			rules: model.TriggerRulesMap{
				"client_type": "corporate",
			},
			expectError: false,
			expectMsg:   "",
		},
		{
			name:        "matter.create no warning",
			triggerType: "matter.create",
			rules: model.TriggerRulesMap{
				"matter_type": "litigation",
			},
			expectError: false,
			expectMsg:   "",
		},
		{
			name:        "invalid trigger type",
			triggerType: "invalid.type",
			rules: model.TriggerRulesMap{
				"field": "value",
			},
			expectError: true,
			errorMsg:    "unsupported trigger type: invalid.type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err, msg := ValidateTriggerRulesContentWithChangeDetection(tt.triggerType, tt.rules)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectMsg, msg)
			}
		})
	}
}

func TestValidateJSON(t *testing.T) {
	tests := []struct {
		name        string
		jsonData    []byte
		target      interface{}
		expectError bool
	}{
		{
			name:        "valid JSON",
			jsonData:    []byte(`{"name": "test", "value": 123}`),
			target:      &map[string]interface{}{},
			expectError: false,
		},
		{
			name:        "invalid JSON syntax",
			jsonData:    []byte(`{"name": "test", "value": 123`), // missing closing brace
			target:      &map[string]interface{}{},
			expectError: true,
		},
		{
			name:     "invalid JSON structure",
			jsonData: []byte(`{"name": "test", "value": "not_a_number"}`),
			target: &struct {
				Name  string
				Value int
			}{},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateJSON(tt.jsonData, tt.target)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCustomValidationFunctions(t *testing.T) {
	t.Run("validateRuleName", func(t *testing.T) {
		tests := []struct {
			name     string
			ruleName string
			expected bool
		}{
			{"valid name", "My Rule 123", true},
			{"valid with hyphens", "My-Rule_123", true},
			{"valid with dots", "My.Rule.123", true},
			{"empty name", "", false},
			{"invalid characters", "My@Rule#123", false},
			{"leading space", " MyRule", false},
			{"trailing space", "MyRule ", false},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				// Test through struct validation
				req := CreateRuleRequest{
					Name:        tt.ruleName,
					TriggerType: "matter.create",
					TriggerRules: model.TriggerRulesMap{
						"matter_type": "litigation",
					},
					RuleConfig: model.RuleConfigArray{
						{
							ActionType: "copy_file",
							SourcePath: "/templates/matter.docx",
							TargetPath: "/matters/{{.MatterID}}/matter.docx",
						},
					},
					CreatedUser: 123,
				}

				errors := ValidateStruct(req)
				t.Logf("Validation errors for name '%s': %+v", tt.ruleName, errors)

				hasNameError := false
				for _, err := range errors {
					if err.Field == "name" {
						hasNameError = true
						t.Logf("Found name error: %s", err.Message)
						break
					}
				}

				if tt.expected {
					assert.False(t, hasNameError, "Expected no validation error for name: %s", tt.ruleName)
				} else {
					assert.True(t, hasNameError, "Expected validation error for name: %s", tt.ruleName)
				}
			})
		}
	})
}

func TestValidationErrorMessages(t *testing.T) {
	req := CreateRuleRequest{
		// Missing required fields to trigger validation errors
	}

	errors := ValidateStruct(req)
	t.Logf("Validation errors: %+v", errors)
	require.NotEmpty(t, errors)

	// Check that error messages are user-friendly
	for _, err := range errors {
		assert.NotEmpty(t, err.Message, "Error message should not be empty")
		assert.NotEmpty(t, err.Field, "Error field should not be empty")
		assert.NotEmpty(t, err.Tag, "Error tag should not be empty")
		t.Logf("Error: Field=%s, Tag=%s, Message=%s", err.Field, err.Tag, err.Message)
	}
}

// Helper functions for pointer values
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}
