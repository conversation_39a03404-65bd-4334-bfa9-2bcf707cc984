package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"bilabl/docman/mocks/service/autodoc_mocks"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

// TestAutoDocEventToRuleExecution tests the complete flow from event consumption to rule execution
func TestAutoDocEventToRuleExecution(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test data
	tenantID := uint64(1)
	clientID := uint64(565)

	// Create event payload
	eventPayload := EventPayload{
		Topic: "client.create",
		Body: map[string]interface{}{
			"id":         float64(clientID),
			"name":       "Client Sharepoint Sample",
			"short_name": "CSS",
			"code":       "C06855",
			"tenant_id":  float64(tenantID),
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name":       "Client Sharepoint Sample",
					"owners":     []interface{}{float64(1)},
					"stage":      float64(256),
					"stage_name": "Initial",
				},
			},
		},
	}

	// Setup mocks
	mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)

	// Mock expectations for rule matching and execution
	mockEventRuleMatchingService.On("ProcessEvent", mock.Anything, tenantID, "client.create", mock.Anything).
		Return(nil)

	// Create handler
	handler := &AutoDocHandler{
		eventRuleMatchingService: mockEventRuleMatchingService,
	}

	// Create request
	payloadBytes, err := json.Marshal(eventPayload)
	assert.NoError(t, err)

	req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/client/created", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{
		GinCtx: c,
	}

	// Call handler
	resp, err := handler.ConsumeClientCreated(ginextReq)

	// Verify response
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify response data
	generalBody := resp.Body.(*ginext.GeneralBody)
	responseData := generalBody.Data.(map[string]interface{})
	assert.Equal(t, "success", responseData["status"])
	assert.Equal(t, "Client created event processed successfully", responseData["message"])
	assert.Equal(t, clientID, responseData["client_id"])
	assert.Equal(t, tenantID, responseData["tenant_id"])

	// Verify all mock expectations were met
	mockEventRuleMatchingService.AssertExpectations(t)
}

// TestAutoDocEventNoMatchingRules tests event processing when no rules match
func TestAutoDocEventNoMatchingRules(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test data
	tenantID := uint64(1)
	clientID := uint64(565)

	// Create event payload
	eventPayload := EventPayload{
		Topic: "client.create",
		Body: map[string]interface{}{
			"id":        float64(clientID),
			"name":      "Test Client",
			"tenant_id": float64(tenantID),
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name":       "Test Client",
					"stage_name": "Lead", // Different stage, won't match our rule
				},
			},
		},
	}

	// Setup mocks
	mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)

	// Mock expectations - no matching rules found, so no execution
	mockEventRuleMatchingService.On("ProcessEvent", mock.Anything, tenantID, "client.create", mock.Anything).
		Return(nil) // No error, but no rules executed

	// Create handler
	handler := &AutoDocHandler{
		eventRuleMatchingService: mockEventRuleMatchingService,
	}

	// Create request
	payloadBytes, err := json.Marshal(eventPayload)
	assert.NoError(t, err)

	req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/client/created", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{
		GinCtx: c,
	}

	// Call handler
	resp, err := handler.ConsumeClientCreated(ginextReq)

	// Verify response
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify response data
	generalBody := resp.Body.(*ginext.GeneralBody)
	responseData := generalBody.Data.(map[string]interface{})
	assert.Equal(t, "success", responseData["status"])
	assert.Equal(t, "Client created event processed successfully", responseData["message"])

	// Verify mock expectations
	mockEventRuleMatchingService.AssertExpectations(t)
}

// TestAutoDocEventProcessingError tests error handling during event processing
func TestAutoDocEventProcessingError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test data
	tenantID := uint64(1)
	clientID := uint64(565)

	// Create event payload
	eventPayload := EventPayload{
		Topic: "client.create",
		Body: map[string]interface{}{
			"id":        float64(clientID),
			"name":      "Test Client",
			"tenant_id": float64(tenantID),
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name": "Test Client",
				},
			},
		},
	}

	// Setup mocks
	mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)

	// Mock expectations - processing error
	mockEventRuleMatchingService.On("ProcessEvent", mock.Anything, tenantID, "client.create", mock.Anything).
		Return(assert.AnError)

	// Create handler
	handler := &AutoDocHandler{
		eventRuleMatchingService: mockEventRuleMatchingService,
	}

	// Create request
	payloadBytes, err := json.Marshal(eventPayload)
	assert.NoError(t, err)

	req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/client/created", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{
		GinCtx: c,
	}

	// Call handler
	resp, err := handler.ConsumeClientCreated(ginextReq)

	// Verify error response
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "Failed to process event:")

	// Verify mock expectations
	mockEventRuleMatchingService.AssertExpectations(t)
}

// TestAutoDocMatterEventProcessing tests matter event processing
func TestAutoDocMatterEventProcessing(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test data
	tenantID := uint64(1)
	matterID := uint64(123)

	// Create event payload
	eventPayload := EventPayload{
		Topic: "matter.create",
		Body: map[string]interface{}{
			"id":        float64(matterID),
			"client_id": float64(456),
			"name":      "Business Registration Matter",
			"code":      "MAT001",
			"tenant_id": float64(tenantID),
			"actor_id":  float64(789),
			"no_notify": false,
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name":   "Business Registration Matter",
					"owners": []interface{}{float64(101), float64(102)},
				},
			},
		},
	}

	// Setup mocks
	mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)

	// Mock expectations
	mockEventRuleMatchingService.On("ProcessEvent", mock.Anything, tenantID, "matter.create", mock.Anything).
		Return(nil)

	// Create handler
	handler := &AutoDocHandler{
		eventRuleMatchingService: mockEventRuleMatchingService,
	}

	// Create request
	payloadBytes, err := json.Marshal(eventPayload)
	assert.NoError(t, err)

	req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/matter/created", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{
		GinCtx: c,
	}

	// Call handler
	resp, err := handler.ConsumeMatterCreated(ginextReq)

	// Verify response
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify response data
	generalBody := resp.Body.(*ginext.GeneralBody)
	responseData := generalBody.Data.(map[string]interface{})
	assert.Equal(t, "success", responseData["status"])
	assert.Equal(t, "Matter created event processed successfully", responseData["message"])
	assert.Equal(t, matterID, responseData["matter_id"])
	assert.Equal(t, tenantID, responseData["tenant_id"])

	// Verify mock expectations
	mockEventRuleMatchingService.AssertExpectations(t)
}
