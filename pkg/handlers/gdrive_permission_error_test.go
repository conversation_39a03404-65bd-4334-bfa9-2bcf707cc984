package handlers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/googleapi"
)

func TestGDrivePermissionHandler_shouldRetryPermissionError(t *testing.T) {
	handler := &GDrivePermissionHandler{}

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error should not retry",
			err:      nil,
			expected: false,
		},
		{
			name: "400 bad request should not retry",
			err: &googleapi.Error{
				Code:    400,
				Message: "Invalid request parameters",
			},
			expected: false,
		},
		{
			name: "401 unauthorized should not retry",
			err: &googleapi.Error{
				Code:    401,
				Message: "Unauthorized",
			},
			expected: false,
		},
		{
			name: "403 forbidden should not retry",
			err: &googleapi.Error{
				Code:    403,
				Message: "Forbidden",
			},
			expected: false,
		},
		{
			name: "404 not found should not retry",
			err: &googleapi.Error{
				Code:    404,
				Message: "Not Found",
			},
			expected: false,
		},
		{
			name: "429 rate limited should retry",
			err: &googleapi.Error{
				Code:    429,
				Message: "Too Many Requests",
			},
			expected: true,
		},
		{
			name: "500 server error should retry",
			err: &googleapi.Error{
				Code:    500,
				Message: "Internal Server Error",
			},
			expected: true,
		},
		{
			name: "502 bad gateway should retry",
			err: &googleapi.Error{
				Code:    502,
				Message: "Bad Gateway",
			},
			expected: true,
		},
		{
			name: "503 service unavailable should retry",
			err: &googleapi.Error{
				Code:    503,
				Message: "Service Unavailable",
			},
			expected: true,
		},
		{
			name: "other error codes should not retry",
			err: &googleapi.Error{
				Code:    418,
				Message: "I'm a teapot",
			},
			expected: false,
		},
		{
			name:     "non-googleapi error should retry",
			err:      assert.AnError,
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.shouldRetryPermissionError(tt.err)
			assert.Equal(t, tt.expected, result, "shouldRetryPermissionError() = %v, want %v", result, tt.expected)
		})
	}
}
