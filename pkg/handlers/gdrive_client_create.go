package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"github.com/gookit/goutil/arrutil"
	"gitlab.com/goxp/cloud0/ginext"
	"gitlab.com/goxp/cloud0/logger"
)

// GDriveClientHandler handles Google Drive client events with permission sync
type GDriveClientHandler struct {
	clientEventConsumer *gdrive.ClientEventConsumer
	permissionHandler   *GDrivePermissionHandler
	docSettingRepo      repositories.DocumentSettingRepository
	autodocHandler      *AutoDocHandler
}

// NewGDriveClientHandler creates a new Google Drive client handler
func NewGDriveClientHandler(
	clientEventConsumer *gdrive.ClientEventConsumer,
	permissionHandler *GDrivePermissionHandler,
	docSettingRepo repositories.DocumentSettingRepository,
	autodocHandler *AutoDocHandler,
) *GDriveClientHandler {
	return &GDriveClientHandler{
		clientEventConsumer: clientEventConsumer,
		permissionHandler:   permissionHandler,
		docSettingRepo:      docSettingRepo,
		autodocHandler:      autodocHandler,
	}
}

// ConsumeGoogleDriveClientCrud handles client events for Google Drive with permission sync
// @deprecated This direct provider endpoint is deprecated. Use /internal/consume/autodoc/client/created
// and /internal/consume/autodoc/client/updated for coordinated processing to avoid race conditions.
func (h *GDriveClientHandler) ConsumeGoogleDriveClientCrud(r *ginext.Request) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleClientEvents")

	// backup body
	bodyBytes, err := io.ReadAll(r.GinCtx.Request.Body)
	if err != nil {
		return nil, err
	}

	// Restore body
	r.GinCtx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	var body model.ConsumeClientReq
	r.MustBind(&body)

	// Restore body
	r.GinCtx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	log.Debugf("getting client event with topic %s", body.Topic)

	// Check if Google Drive is enabled for this tenant
	config, err := h.getGDriveConfig(ctx, body.Body.TenantID)
	if err != nil {
		log.Errorf("error while getting Google Drive config: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), err
	}

	if config == nil || !config.Enabled {
		log.Debugf("tenant %d doesn't have Google Drive enabled", body.Body.TenantID)

		// also call autodoc
		err = h.callAutodoc(body.Topic, r)
		if err != nil {
			log.Errorf("error while calling autodoc: %v", err)
			return ginext.NewResponseData(http.StatusInternalServerError, nil), err
		}

		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	// Define the client folderName
	var folderName string
	if body.Body.ShortName != "" {
		folderName = body.Body.ShortName
	} else {
		folderName = body.Body.Name
	}
	clientFolder := fmt.Sprintf("%s - %s", folderName, body.Body.Code)

	resp := ginext.NewResponseData(http.StatusOK, nil)
	switch body.Topic {
	case "client.create":
		resp, err = h.handleClientCreate(r, &body, clientFolder)
	case "client.update":
		resp, err = h.handleClientUpdate(r, &body, clientFolder)
	}

	if err != nil {
		log.Errorf("error while handling client event: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), err
	}

	_ = h.callAutodoc(body.Topic, r)

	return resp, nil
}

func (h *GDriveClientHandler) callAutodoc(topic string, r *ginext.Request) error {
	var err error
	switch topic {
	case "client.create":
		_, err = h.autodocHandler.ConsumeClientCreated(r)
	case "client.update":
		_, err = h.autodocHandler.ConsumeClientUpdated(r)
	}

	return err
}

// handleClientCreate processes client creation with folder creation and permission sync
func (h *GDriveClientHandler) handleClientCreate(
	r *ginext.Request,
	body *model.ConsumeClientReq,
	clientFolder string,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleClientCreate")

	// 1. Create client folder using existing event consumer
	payloadBytes, err := json.Marshal(body)
	if err != nil {
		log.Errorf("failed to marshal event payload: %v", err)
		return nil, err
	}

	err = h.clientEventConsumer.HandleClientCreated(ctx, string(payloadBytes))
	if err != nil {
		log.Errorf("failed to create client folder: %v", err)
		return nil, err
	}

	// 2. Check if permission sync is enabled for creation
	if h.shouldSyncPermissions(ctx, body.Body.TenantID, "create") {
		ownerEmails := arrutil.Map(body.Body.Extra.Current.OwnerUsers, func(o model.OwnerUser) (string, bool) {
			return o.Email, true
		})

		if len(ownerEmails) == 0 {
			log.Warnf("no owner email found for client %d", body.Body.ID)
		} else {
			// Sync permissions using the permission handler with explicit clientID
			// For client folders, clientID = 0
			err = h.permissionHandler.SyncGoogleDrivePermissionsWithClientID(
				ctx,
				body.Body.TenantID,
				"client",
				body.Body.ID,
				ownerEmails,
				0, // clientID = 0 for client folders
			)
			if err != nil {
				log.Errorf("failed to sync permissions for client %d: %v", body.Body.ID, err)
				// Don't fail the entire operation if permission sync fails
				// Log the error and continue
			}
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

// handleClientUpdate processes client updates with folder updates and permission sync
func (h *GDriveClientHandler) handleClientUpdate(
	r *ginext.Request,
	body *model.ConsumeClientReq,
	clientFolder string,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleClientUpdate")

	// 1. Update client folder using existing event consumer
	payloadBytes, err := json.Marshal(body)
	if err != nil {
		log.Errorf("failed to marshal event payload: %v", err)
		return nil, err
	}

	err = h.clientEventConsumer.HandleClientUpdated(ctx, string(payloadBytes))
	if err != nil {
		log.Errorf("failed to update client folder: %v", err)
		return nil, err
	}

	// 2. Check if permission sync is enabled for updates
	if h.shouldSyncPermissions(ctx, body.Body.TenantID, "update") {
		currentOwnerEmails := arrutil.Map(body.Body.Extra.Current.OwnerUsers, func(o model.OwnerUser) (string, bool) {
			return o.Email, true
		})

		if len(currentOwnerEmails) == 0 {
			log.Warnf("no current owner emails found for client %d - will remove all existing permissions", body.Body.ID)
		}

		// Sync permissions using the permission handler
		// If currentOwnerEmails is empty, this will remove all existing permissions
		err = h.permissionHandler.SyncGoogleDrivePermissionsWithClientID(
			ctx,
			body.Body.TenantID,
			"client",
			body.Body.ID,
			currentOwnerEmails,
			0, // clientID = 0 for client folders
		)
		if err != nil {
			log.Errorf("failed to sync permissions for client %d: %v", body.Body.ID, err)
			// Don't fail the entire operation if permission sync fails
			// Log the error and continue
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

// getGDriveConfig retrieves Google Drive configuration for the tenant
func (h *GDriveClientHandler) getGDriveConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	setting, err := h.docSettingRepo.GetValueByKey(ctx, tenantID, model.KeyGdriveConfig)
	if model.IsNotFound(err) {
		return nil, nil // No config found, Google Drive not enabled
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get Google Drive config: %w", err)
	}

	var config model.GDriveConfig
	if err := config.FromJSON(setting.Value); err != nil {
		return nil, fmt.Errorf("failed to parse Google Drive config: %w", err)
	}

	return &config, nil
}

// shouldSyncPermissions checks if permission sync is enabled for the given operation
func (h *GDriveClientHandler) shouldSyncPermissions(ctx context.Context, tenantID uint64, operation string) bool {
	config, err := h.getGDriveConfig(ctx, tenantID)
	if err != nil || config == nil || !config.Enabled {
		return false
	}

	// Check permission config if available
	if config.PermissionConfig != nil {
		switch operation {
		case "create":
			return config.PermissionConfig.SyncOnCreate
		case "update":
			return config.PermissionConfig.SyncOnUpdate
		}
	}

	// Default to true if no specific config
	return true
}
