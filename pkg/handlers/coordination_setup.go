package handlers

import (
	"fmt"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/coordination"
	"bilabl/docman/internal/coordination/integration"
	"bilabl/docman/internal/handlers"
	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"gorm.io/gorm"
)

// CoordinationSetupConfig holds configuration for coordination setup
type CoordinationSetupConfig struct {
	// Database
	DB *gorm.DB

	// Repositories
	DocSettingRepo repositories.DocumentSettingRepository

	// Original services
	OriginalEventRuleMatchingService autodoc.EventRuleMatchingService
	MatterEventConsumer              autodoc.MatterEventConsumer
	ClientEventConsumer              autodoc.ClientEventConsumer

	// Provider consumers
	GDriveConsumer       *gdrive.MatterEventConsumer
	GDriveClientConsumer *gdrive.ClientEventConsumer
	// SharePointConsumer coordination.SharePointMatterEventConsumer
	// InternalConsumer   coordination.InternalMatterEventConsumer
}

// SetupCoordination sets up the coordination system and returns the coordinated EventRuleMatchingService
func SetupCoordination(config *CoordinationSetupConfig) (autodoc.EventRuleMatchingService, error) {
	// Auto-migrate coordination tables
	if err := config.DB.AutoMigrate(&model.OperationCoordinationStatus{}); err != nil {
		return nil, fmt.Errorf("failed to migrate coordination tables: %w", err)
	}

	// Create coordination repository
	statusRepo := repositories.NewOperationCoordinationStatusRepository(config.DB)

	// Create autodoc handler that will be triggered after folder creation
	autodocHandler := handlers.NewAutodocRulesHandler(config.OriginalEventRuleMatchingService)

	// Build coordination services
	coordinationServices := coordination.NewCoordinationServiceBuilder().
		WithStatusRepository(statusRepo).
		WithDocSettingRepository(config.DocSettingRepo).
		WithEventRuleMatchingService(config.OriginalEventRuleMatchingService).
		WithAutodocHandler(autodocHandler).
		Build()

	// Setup coordination integration
	coordinatedService, err := integration.SetupCoordinationIntegration(&integration.CoordinationIntegrationConfig{
		OriginalEventRuleMatchingService: config.OriginalEventRuleMatchingService,
		MatterEventConsumer:              config.MatterEventConsumer,
		ClientEventConsumer:              config.ClientEventConsumer,
		CoordinationServices:             coordinationServices,
		GDriveConsumer:                   config.GDriveConsumer,
		GDriveClientConsumer:             config.GDriveClientConsumer,
		// SharePointConsumer:               config.SharePointConsumer,
		// InternalConsumer:                 config.InternalConsumer,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to setup coordination integration: %w", err)
	}

	return coordinatedService, nil
}

// CoordinationHealthCheck checks if coordination system is healthy
func CoordinationHealthCheck(db *gorm.DB) error {
	// Check if coordination table exists and is accessible
	var count int64
	if err := db.Model(&model.OperationCoordinationStatus{}).Count(&count).Error; err != nil {
		return fmt.Errorf("coordination health check failed: %w", err)
	}
	return nil
}

// GetCoordinationStats returns coordination system statistics
func GetCoordinationStats(db *gorm.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Count total operations
	var totalCount int64
	if err := db.Model(&model.OperationCoordinationStatus{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	stats["total_operations"] = totalCount

	// Count by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	if err := db.Model(&model.OperationCoordinationStatus{}).
		Select("overall_status as status, count(*) as count").
		Group("overall_status").
		Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	statusMap := make(map[string]int64)
	for _, sc := range statusCounts {
		statusMap[sc.Status] = sc.Count
	}
	stats["status_counts"] = statusMap

	// Count recent operations (last 24 hours)
	var recentCount int64
	if err := db.Model(&model.OperationCoordinationStatus{}).
		Where("created_at > datetime('now', '-24 hours')").
		Count(&recentCount).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent count: %w", err)
	}
	stats["recent_operations_24h"] = recentCount

	return stats, nil
}

// CleanupOldCoordinationRecords cleans up old coordination records
func CleanupOldCoordinationRecords(db *gorm.DB, olderThanHours int) (int64, error) {
	result := db.Where("created_at < datetime('now', '-' || ? || ' hours')", olderThanHours).
		Delete(&model.OperationCoordinationStatus{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old records: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// Example usage in handler setup:
/*
func setupCoordinationInHandler(app App) (autodoc.EventRuleMatchingService, error) {
	setting := app.GetSetting()

	// Create original event rule matching service
	originalService := autodocService.NewEventRuleMatchingService(...)

	// Setup coordination
	coordinatedService, err := SetupCoordination(&CoordinationSetupConfig{
		Enabled:                          setting.CoordinationEnabled,
		DB:                              app.GetDB(),
		DocSettingRepo:                  docSettingRepo,
		OriginalEventRuleMatchingService: originalService,
		MatterEventConsumer:             autoDocMatterEventConsumer,
		ClientEventConsumer:             autoDocClientEventConsumer,
		GDriveConsumer:                  gdriveConsumer,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to setup coordination: %w", err)
	}

	return coordinatedService, nil
}
*/
