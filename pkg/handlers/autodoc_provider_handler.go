package handlers

import (
	"net/http"
	"strconv"
	"time"

	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/pkg/bilabllog"

	"gitlab.com/goxp/cloud0/ginext"
)

// AutoDocProviderHandler handles AutoDoc provider configuration endpoints
type AutoDocProviderHandler struct {
	documentRegistry autodoc.DocumentServiceRegistry
}

// NewAutoDocProviderHandler creates a new AutoDocProviderHandler
func NewAutoDocProviderHandler(documentRegistry autodoc.DocumentServiceRegistry) *AutoDocProviderHandler {
	return &AutoDocProviderHandler{
		documentRegistry: documentRegistry,
	}
}

// ListProvidersRequest represents the request for listing providers
type ListProvidersRequest struct {
	// Health check functionality removed
}

// ListProvidersResponse represents the response for listing providers
type ListProvidersResponse struct {
	Providers       []string `json:"providers"`
	DefaultProvider string   `json:"default_provider"`
}

// SetDefaultProviderRequest represents the request for setting default provider
type SetDefaultProviderRequest struct {
	Provider string `json:"provider" binding:"required"`
}

// SetDefaultProviderResponse represents the response for setting default provider
type SetDefaultProviderResponse struct {
	Provider string `json:"provider"`
	Message  string `json:"message"`
}



// ListProviders handles GET /api/v1/autodoc/providers
func (h *AutoDocProviderHandler) ListProviders(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Listing AutoDoc providers")

	var req ListProvidersRequest
	if err := r.GinCtx.ShouldBindQuery(&req); err != nil {
		log.WithError(err).Error("Failed to bind query parameters")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid query parameters: "+err.Error())
	}

	// Get list of providers
	providers := h.documentRegistry.ListProviders()
	defaultProvider := h.documentRegistry.GetDefaultProvider()

	response := &ListProvidersResponse{
		Providers:       providers,
		DefaultProvider: defaultProvider,
	}

	// Health check functionality removed

	log.WithField("provider_count", len(providers)).WithField("default_provider", defaultProvider).Info("Providers listed successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// SetDefaultProvider handles PUT /api/v1/autodoc/providers/default
func (h *AutoDocProviderHandler) SetDefaultProvider(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Setting default AutoDoc provider")

	var req SetDefaultProviderRequest
	if err := r.GinCtx.ShouldBindJSON(&req); err != nil {
		log.WithError(err).Error("Failed to bind request body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid request body: "+err.Error())
	}

	// Validate provider exists
	if !h.documentRegistry.IsProviderRegistered(req.Provider) {
		log.WithField("provider", req.Provider).Error("Provider not registered")
		return nil, ginext.NewError(http.StatusBadRequest, "Provider '"+req.Provider+"' is not registered")
	}

	// Set default provider
	err := h.documentRegistry.SetDefaultProvider(req.Provider)
	if err != nil {
		log.WithError(err).WithField("provider", req.Provider).Error("Failed to set default provider")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to set default provider: "+err.Error())
	}

	response := &SetDefaultProviderResponse{
		Provider: req.Provider,
		Message:  "Default provider set successfully",
	}

	log.WithField("provider", req.Provider).Info("Default provider set successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}





// GetProviderInfo handles GET /api/v1/autodoc/providers/:provider
func (h *AutoDocProviderHandler) GetProviderInfo(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	provider := r.GinCtx.Param("provider")
	if provider == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "Provider parameter is required")
	}

	log.WithField("provider", provider).Info("Getting provider information")

	// Validate provider exists
	if !h.documentRegistry.IsProviderRegistered(provider) {
		log.WithField("provider", provider).Error("Provider not registered")
		return nil, ginext.NewError(http.StatusNotFound, "Provider '"+provider+"' is not registered")
	}

	// Get provider service (to verify it's accessible)
	_, err := h.documentRegistry.GetProvider(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get provider service")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to access provider: "+err.Error())
	}

	// Get health status
	response := map[string]interface{}{
		"provider":   provider,
		"registered": true,
		"is_default": provider == h.documentRegistry.GetDefaultProvider(),
	}

	log.WithField("provider", provider).Info("Provider information retrieved successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// TestProviderConnection handles POST /api/v1/autodoc/providers/:provider/test
func (h *AutoDocProviderHandler) TestProviderConnection(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	provider := r.GinCtx.Param("provider")
	if provider == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "Provider parameter is required")
	}

	log.WithField("provider", provider).Info("Testing provider connection")

	// Validate provider exists
	if !h.documentRegistry.IsProviderRegistered(provider) {
		log.WithField("provider", provider).Error("Provider not registered")
		return nil, ginext.NewError(http.StatusNotFound, "Provider '"+provider+"' is not registered")
	}

	// Get provider service
	service, err := h.documentRegistry.GetProvider(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get provider service")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to access provider: "+err.Error())
	}

	// Test connection by trying to ensure AutoDoc root (safe operation)
	tenantIDStr := r.GinCtx.Query("tenant_id")
	if tenantIDStr == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "tenant_id query parameter is required")
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 64)
	if err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid tenant_id: "+err.Error())
	}

	// Test provider by ensuring AutoDoc root exists
	_, err = service.EnsureAutoDocRoot(r.Context(), tenantID)
	if err != nil {
		log.WithError(err).WithField("provider", provider).WithField("tenant_id", tenantID).Error("Provider connection test failed")
		return nil, ginext.NewError(http.StatusServiceUnavailable, "Provider connection test failed: "+err.Error())
	}

	response := map[string]interface{}{
		"provider":  provider,
		"status":    "success",
		"message":   "Provider connection test successful",
		"tenant_id": tenantID,
		"tested_at": time.Now().Format(time.RFC3339),
	}

	log.WithField("provider", provider).WithField("tenant_id", tenantID).Info("Provider connection test successful")

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// GetProviderFallback handles GET /api/v1/autodoc/providers/:provider/fallback
func (h *AutoDocProviderHandler) GetProviderFallback(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	provider := r.GinCtx.Param("provider")
	if provider == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "Provider parameter is required")
	}

	log.WithField("provider", provider).Info("Getting provider with fallback")

	// Get provider with fallback
	service, actualProvider, err := h.documentRegistry.GetProviderWithFallback(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get provider with fallback")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to get provider: "+err.Error())
	}

	response := map[string]interface{}{
		"requested_provider": provider,
		"actual_provider":    actualProvider,
		"fallback_used":      provider != actualProvider,
		"service_available":  service != nil,
	}

	log.WithField("requested_provider", provider).WithField("actual_provider", actualProvider).Info("Provider fallback resolved successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}
