package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"

	"gitlab.com/goxp/cloud0/ginext"
)

// ExecuteRuleRequest represents the request body for manual rule execution
type ExecuteRuleRequest struct {
	MockPayload map[string]interface{} `json:"mock_payload,omitempty"`
}

// ExecuteRuleResponse represents the response for manual rule execution
type ExecuteRuleResponse struct {
	RuleID     uint64                 `json:"rule_id"`
	RuleName   string                 `json:"rule_name"`
	ExecutedAt string                 `json:"executed_at"`
	Status     string                 `json:"status"`
	Message    string                 `json:"message"`
	Results    map[string]interface{} `json:"results,omitempty"`
}

// ExecuteRule handles POST /api/autodoc/rules/:id/execute
func (h *AutoDocHandler) ExecuteRule(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	// Parse request body (optional mock payload)
	var executeReq ExecuteRuleRequest
	if err := r.GinCtx.ShouldBindJSON(&executeReq); err != nil {
		// Log the parsing error for debugging
		log.WithError(err).
			WithField("content_type", r.GinCtx.GetHeader("Content-Type")).
			WithField("content_length", r.GinCtx.Request.ContentLength).
			WithField("rule_id", params.RuleID).
			Debug("Failed to parse request body for ExecuteRule - using empty payload")

		// If no body provided or parsing failed, use empty payload
		executeReq.MockPayload = make(map[string]interface{})
	} else {
		// Log successful parsing for debugging
		log.WithField("mock_payload", executeReq.MockPayload).
			WithField("payload_keys", getMapKeys(executeReq.MockPayload)).
			WithField("rule_id", params.RuleID).
			Debug("Successfully parsed ExecuteRule request body")
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	ctx := withTenantContext(r.Context(), tenantID)

	// Get the rule using the service
	rule, err := h.autoDocService.GetRule(ctx, params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if rule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Check if rule is active
	if !rule.IsActive {
		return nil, ginext.NewError(http.StatusBadRequest, "Cannot execute inactive rule")
	}

	// Check if mock payload matches trigger rules
	matchResult, err := h.evaluateTriggerRules(ctx, rule, executeReq.MockPayload)
	if err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to evaluate trigger rules: "+err.Error())
	}

	if !matchResult.Matches {
		// Return response indicating no match
		response := ExecuteRuleResponse{
			RuleID:     rule.ID,
			RuleName:   rule.Name,
			ExecutedAt: fmt.Sprintf("%d", time.Now().Unix()),
			Status:     "skipped",
			Message:    "Rule skipped: trigger conditions not met",
			Results: map[string]interface{}{
				"trigger_evaluation": matchResult,
				"actions_executed":   0,
				"execution_details": []map[string]interface{}{
					{
						"action_type": "trigger_evaluation",
						"status":      "no_match",
						"message":     matchResult.Reason,
					},
				},
			},
		}
		return ginext.NewResponseData(http.StatusOK, response), nil
	}

	// Execute the rule using RuleExecutionEngine
	err = h.ruleExecutionEngine.ExecuteRule(ctx, rule.ID, executeReq.MockPayload)
	if err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to execute rule: "+err.Error())
	}
	// Build execution results
	executionResults := map[string]interface{}{
		"actions_executed": len(rule.RuleConfig),
		"trigger_type":     rule.TriggerType,
		"payload_received": executeReq.MockPayload,
		"execution_details": []map[string]interface{}{
			{
				"action_type": "rule_execution",
				"status":      "success",
				"message":     "Rule executed successfully via RuleExecutionEngine",
			},
		},
	}

	// Return execution response
	response := ExecuteRuleResponse{
		RuleID:     rule.ID,
		RuleName:   rule.Name,
		ExecutedAt: fmt.Sprintf("%d", time.Now().Unix()),
		Status:     "success",
		Message:    "Rule executed successfully",
		Results:    executionResults,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// TestRuleRequest represents the request body for rule testing
type TestRuleRequest struct {
	MockPayload map[string]interface{} `json:"mock_payload,omitempty"`
}

// TestRuleResponse represents the response for rule testing
type TestRuleResponse struct {
	RuleID         uint64                   `json:"rule_id"`
	RuleName       string                   `json:"rule_name"`
	TestedAt       string                   `json:"tested_at"`
	Status         string                   `json:"status"`
	Message        string                   `json:"message"`
	MatchResult    bool                     `json:"match_result"`
	TriggerDetails map[string]interface{}   `json:"trigger_details,omitempty"`
	ActionTests    []map[string]interface{} `json:"action_tests,omitempty"`
}

// TestRule handles POST /v3/autodoc/test/:id
func (h *AutoDocHandler) TestRule(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	// Parse request body (optional mock payload)
	var testReq TestRuleRequest
	if err := r.GinCtx.ShouldBindJSON(&testReq); err != nil {
		// Log the parsing error for debugging

		log.WithError(err).
			WithField("content_type", r.GinCtx.GetHeader("Content-Type")).
			WithField("content_length", r.GinCtx.Request.ContentLength).
			WithField("rule_id", params.RuleID).
			Debug("Failed to parse request body for TestRule - using empty payload")

		// If no body provided or parsing failed, use empty payload
		testReq.MockPayload = make(map[string]interface{})
	} else {
		// Log successful parsing for debugging
		log.WithField("mock_payload", testReq.MockPayload).
			WithField("payload_keys", getMapKeys(testReq.MockPayload)).
			WithField("rule_id", params.RuleID).
			Debug("Successfully parsed TestRule request body")
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	ctx := withTenantContext(r.Context(), tenantID)

	// Get the rule using the service
	rule, err := h.autoDocService.GetRule(ctx, params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if rule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Evaluate trigger rules first
	matchResult, err := h.evaluateTriggerRules(ctx, rule, testReq.MockPayload)
	if err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to evaluate trigger rules: "+err.Error())
	}

	// Use RuleExecutionEngine to test placeholder replacement
	placeholderData := h.ruleExecutionEngine.ExtractPlaceholderData(ctx, testReq.MockPayload)

	// Test action configurations with placeholder replacement
	actionTests := make([]map[string]interface{}, 0)
	for i, action := range rule.RuleConfig {
		// Test placeholder replacement for this action
		resolvedSourcePath := h.ruleExecutionEngine.ReplacePlaceholders(ctx, action.SourcePath, placeholderData)
		resolvedTargetPath := h.ruleExecutionEngine.ReplacePlaceholders(ctx, action.TargetPath, placeholderData)

		actionTest := map[string]interface{}{
			"action_index":         i + 1,
			"action_type":          action.ActionType,
			"source_path":          action.SourcePath,
			"target_path":          action.TargetPath,
			"resolved_source_path": resolvedSourcePath,
			"resolved_target_path": resolvedTargetPath,
			"status":               "valid",
			"message":              "Action configuration is valid with placeholder resolution",
		}
		actionTests = append(actionTests, actionTest)
	}

	// Use actual trigger evaluation results
	triggerDetails := map[string]interface{}{
		"trigger_type":  rule.TriggerType,
		"trigger_rules": rule.TriggerRules,
		"payload_match": matchResult.Matches,
		"match_details": matchResult.Reason,
	}
	if matchResult.Details != nil {
		triggerDetails["evaluation_details"] = matchResult.Details
	}

	// Return test response
	response := TestRuleResponse{
		RuleID:         rule.ID,
		RuleName:       rule.Name,
		TestedAt:       fmt.Sprintf("%d", time.Now().Unix()),
		Status:         "success",
		Message:        "Rule test completed successfully",
		MatchResult:    matchResult.Matches,
		TriggerDetails: triggerDetails,
		ActionTests:    actionTests,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// TriggerMatchResult represents the result of trigger rule evaluation
type TriggerMatchResult struct {
	Matches bool                   `json:"matches"`
	Reason  string                 `json:"reason"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// evaluateTriggerRules evaluates if the payload matches the rule's trigger conditions
func (h *AutoDocHandler) evaluateTriggerRules(ctx context.Context, rule *model.DocumentAutomationRule, payload map[string]interface{}) (*TriggerMatchResult, error) {
	// Use RuleMatchingService to evaluate the rule
	matches, err := h.ruleMatchingService.EvaluateRule(ctx, rule, payload)
	if err != nil {
		return &TriggerMatchResult{
			Matches: false,
			Reason:  fmt.Sprintf("Failed to evaluate rule: %v", err),
		}, err
	}

	if matches {
		return &TriggerMatchResult{
			Matches: true,
			Reason:  "All trigger conditions satisfied",
		}, nil
	} else {
		return &TriggerMatchResult{
			Matches: false,
			Reason:  "One or more trigger conditions not met",
		}, nil
	}
}

// getMapKeys returns keys of a map for debugging
func getMapKeys(m map[string]interface{}) []string {
	if m == nil {
		return []string{}
	}
	keys := make([]string, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}
