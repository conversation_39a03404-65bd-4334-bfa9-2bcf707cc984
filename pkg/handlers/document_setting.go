package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"
	"gitlab.com/goxp/cloud0/ginext"
	"net/http"
	"time"
)

type documentSettingHandler struct {
	rDocumentSetting repositories.DocumentSettingRepository
}

func NewDocumentSettingHandler(
	rDocumentSetting repositories.DocumentSettingRepository,
) *documentSettingHandler {
	return &documentSettingHandler{
		rDocumentSetting: rDocumentSetting,
	}
}

type reqDocumentSetting struct {
	TenantID uint64 `json:"tenant_id"`
	Key      string `json:"key"`
	Value    string `json:"value"`
}

func (h *documentSettingHandler) CreateDocumentSetting(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body reqDocumentSetting
	r.MustBind(&body)

	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  body.TenantID,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  body.Key,
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	docSetting := &model.DocumentSetting{
		TenantID:  body.TenantID,
		Key:       body.Key,
		Value:     body.Value,
		ExpiredAt: time.Now(),
	}
	err := h.rDocumentSetting.CreateOrUpdate(r.Context(), queryBuild, docSetting)
	if err != nil {
		log.Errorf("server error: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), nil
	}

	data, err := h.rDocumentSetting.FindOne(r.Context(), queryBuild)
	if err != nil {
		log.Errorf("server error: %v", err)
	}
	resp := ginext.NewResponseData(http.StatusOK, data)

	return resp, nil
}
