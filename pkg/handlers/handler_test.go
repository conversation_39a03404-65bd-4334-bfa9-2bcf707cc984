package handlers

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"gitlab.com/goxp/cloud0/ginext"
)

func toBodyReader(data interface{}) io.ReadCloser {
	bytesData, _ := json.<PERSON>(data) // we know the test data we put in, skip error check here
	return io.NopCloser(bytes.NewReader(bytesData))
}

// doTestRequestOptions calls the test request with an options function to modify request
func doTestRequestOptions(method, path string, data interface{}, handler ginext.<PERSON>ler, options func(r *http.Request), pattern ...string) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	engine := gin.Default()
	req := httptest.NewRequest(method, path, toBodyReader(data))
	req.Header.Set("content-type", "application/json")

	if options != nil {
		options(req)
	}

	engine.Use(ginext.CreateErrorHandler(false))

	if len(pattern) > 0 {
		engine.Handle(method, pattern[0], ginext.WrapHandler(handler))
	} else {
		engine.NoRoute(ginext.WrapHandler(handler))
	}

	engine.ServeHTTP(w, req)

	return w
}

func TestMain(m *testing.M) {
	// change working dir to root (and change only once)
	if _, err := os.Stat("resource"); os.IsNotExist(err) {
		_ = os.Chdir("../../")
	}

	gin.SetMode(gin.TestMode)

	os.Exit(m.Run())
}

// TestImprovedRequestParsingDocumentation documents the improved request parsing
// This test serves as documentation for the enhanced error handling and logging
// added to TestRule and ExecuteRule handlers
func TestImprovedRequestParsingDocumentation(t *testing.T) {
	t.Log("=== Improved Request Parsing Documentation ===")
	t.Log("")
	t.Log("The TestRule and ExecuteRule handlers now include enhanced logging for request parsing:")
	t.Log("")
	t.Log("1. SUCCESS CASE - Valid JSON payload:")
	t.Log("   Request: POST /v3/autodoc/test/123")
	t.Log("   Body: {\"mock_payload\": {\"id\": 123, \"name\": \"Test\"}}")
	t.Log("   Log: Successfully parsed TestRule request body")
	t.Log("   Fields: mock_payload, payload_keys, rule_id")
	t.Log("")
	t.Log("2. ERROR CASE - Invalid JSON:")
	t.Log("   Request: POST /v3/autodoc/test/123")
	t.Log("   Body: {\"mock_payload\": {\"id\": 123, \"name\": \"Test\"")
	t.Log("   Log: Failed to parse request body for TestRule - using empty payload")
	t.Log("   Fields: error, content_type, content_length, rule_id")
	t.Log("")
	t.Log("3. ERROR CASE - Empty body:")
	t.Log("   Request: POST /v3/autodoc/test/123")
	t.Log("   Body: (empty)")
	t.Log("   Log: Failed to parse request body for TestRule - using empty payload")
	t.Log("   Fields: error, content_type, content_length, rule_id")
	t.Log("")
	t.Log("4. ERROR CASE - Missing Content-Type:")
	t.Log("   Request: POST /v3/autodoc/test/123")
	t.Log("   Headers: (no Content-Type)")
	t.Log("   Log: Failed to parse request body for TestRule - using empty payload")
	t.Log("   Fields: error, content_type='', content_length, rule_id")
	t.Log("")
	t.Log("This helps debug why mock_payload becomes empty and trigger conditions fail!")
}
