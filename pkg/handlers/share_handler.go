package handlers

import (
	"code.mybil.net/gophers/gokit/clients/settings"
	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/errors"
	"gitlab.com/goxp/cloud0/ginext"
)

type downloadShareReq struct {
	ObjectType string `json:"object_type"`
	ObjectID   uint64 `json:"object_id"`
	PassCode   string `json:"pass_code"`
}

func (h *DocumentHandler) DownloadShareHandler(r *ginext.Request) (*ginext.Response, error) {
	var req downloadShareReq
	r.MustBind(&req)

	// only support billing now
	en, err := h.fetcher.Fetch(r.Context(), entity.Kind(req.ObjectType), entity.ID(req.ObjectID))
	if err != nil {
		return nil, err
	}

	// load tenant setting
	sett, err := h.settLoader.Fetch(r.Context(), settings.SettingKeyTenantPassCode, uint64(en.TenantID), 0)
	if err != nil {
		return nil, errors.WrapInternalErr(err, "failed to load tenant setting")
	}

	if sett.Value != req.PassCode {
		return nil, errors.ErrUnauthorized
	}

	if en.File == "" {
		return nil, errors.NewBadDataErr("file not found")
	}

	downloadDat, err := h.globClient.Download(r.Context(), en.File)
	if err != nil {
		return nil, errors.WrapInternalErr(err, "failed to download file")
	}

	return ginext.NewResponseData(200, downloadDat), nil
}
