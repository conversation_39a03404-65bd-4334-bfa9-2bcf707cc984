package handlers

import (
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/sharepointclient"
	"bilabl/docman/pkg/transport"
	"context"
	"net/http"
	"time"

	"code.mybil.net/gophers/gokit/clients"
	globkit "code.mybil.net/gophers/gokit/clients/glob"
	"code.mybil.net/gophers/gokit/clients/settings"
	"code.mybil.net/gophers/gokit/components/entity"

	"github.com/gin-gonic/gin"
	"gitlab.com/goxp/cloud0/ginext"
	"gorm.io/gorm"

	gdriveV3 "bilabl/docman/internal/handlers/v3/gdrive"
	autodocService "bilabl/docman/internal/service/autodoc"
	documentService "bilabl/docman/internal/service/document"
	gdriveService "bilabl/docman/internal/service/gdrive"
	gdriveclient "bilabl/docman/pkg/gdrive"
)

type req = *ginext.Request
type res = *ginext.Response

type App interface {
	Router() *gin.Engine
	GetDB() *gorm.DB
	GetGlobURL() string
	GetHrglassidURL() string
	GetConsumerURL() string
	GetSharepointClient() sharepointclient.Client
	GetGdriveService() gdriveclient.DriveClient
	GetSetting() *Setting
}

// Init initializes and add routes to router group
func Init(app App) {
	w := ginext.WrapHandler

	glob := transport.NewGlob(app.GetGlobURL())
	hrglassid := transport.NewHrglassid(app.GetHrglassidURL())
	consumer := transport.NewConsumer(app.GetConsumerURL())

	db := app.GetDB()
	sp := app.GetSharepointClient()
	migrationRepo := repositories.NewMigrationRepo(db)

	docRepo := repositories.NewDocumentRepo(db)
	docConfigRepo := repositories.NewDocumentConfigRepo(db)
	docMappingRepo := repositories.NewDocumentMappingRepo(db)
	docPermissionRepo := repositories.NewDocumentPermissionMappingRepo(db)
	docSettingRepo := repositories.NewDocumentSettingRepo(db)
	docAutomationRuleRepo := repositories.NewDocumentAutomationRuleRepository(db)

	entityFetcher := entity.NewGenericFetcher()
	settLoader := settings.NewDefaultClient()
	globClient := globkit.New(app.GetGlobURL())

	// Note: DocumentHandler will be initialized after AutoDoc service is created
	docConfigHandler := NewDocumentConfigHandler(docConfigRepo)
	docSettingHandler := NewDocumentSettingHandler(docSettingRepo)
	docSharepointDocumentHandler := NewSharepointDocumentHandler(
		docMappingRepo,
		docPermissionRepo,
		docSettingRepo,
		sp,
		consumer,
		entity.NewGenericFetcher(),
		hrglassid,
		app.GetSetting().PublicBaseUrl,
	)

	gdriveSvcInstance := app.GetGdriveService()
	gdriveSvc := gdriveService.New(gdriveSvcInstance, docSettingRepo, docMappingRepo, docPermissionRepo, entityFetcher)

	// Google Drive permission handler
	gdriveClient := app.GetGdriveService()
	gdrivePermissionHandler := NewGDrivePermissionHandler(
		gdriveClient,
		docPermissionRepo,
		docMappingRepo,
		docSettingRepo,
	)

	// Set permission syncer in the service
	gdriveSvc.SetPermissionSyncer(gdrivePermissionHandler)

	// AutoDoc service and handler
	// Note: Will be initialized after gdriveDocumentService is created

	// Get event consumers from service
	clientEventConsumer := gdriveSvc.GetClientEventConsumer()
	matterEventConsumer := gdriveSvc.GetMatterEventConsumer()

	// Google Drive V3 API handlers
	uploadSessionRepo := repositories.NewUploadSessionRepo(app.GetDB())
	gdriveDocumentService := gdriveService.NewDocumentService(docRepo, docMappingRepo, uploadSessionRepo, docSettingRepo, gdriveSvcInstance)

	// Create document service registry
	docServiceRegistry := autodocService.NewDocumentServiceRegistry()

	// Create mapping service for ID mapping
	mappingCache := autodocService.NewInMemoryMappingCache(time.Hour)
	mappingService := autodocService.NewMappingService(docMappingRepo, mappingCache)

	// Register internal document service
	internalDocService := autodocService.NewInternalDocumentService(docRepo, glob)
	if err := docServiceRegistry.RegisterProvider("internal", internalDocService); err != nil {
		log := bilabllog.CreateContextLogger(context.Background())
		log.WithError(err).Error("Failed to register internal provider")
	} else {
		log := bilabllog.CreateContextLogger(context.Background())
		log.Info("Successfully registered internal provider")
	}

	// Register Google Drive document service adapter
	gdriveAdapter := autodocService.NewGDriveAdapter(gdriveDocumentService, mappingService, docSettingRepo)
	if err := docServiceRegistry.RegisterProvider("gdrive", gdriveAdapter); err != nil {
		// Use a background context for startup logging
		log := bilabllog.CreateContextLogger(context.Background())
		log.WithError(err).Error("Failed to register gdrive provider")
	} else {
		log := bilabllog.CreateContextLogger(context.Background())
		log.Info("Successfully registered gdrive provider")
	}

	// Set default provider
	docServiceRegistry.SetDefaultProvider("internal")

	// Log all registered providers for debugging
	log := bilabllog.CreateContextLogger(context.Background())
	providers := docServiceRegistry.ListProviders()
	log.WithField("providers", providers).WithField("default_provider", docServiceRegistry.GetDefaultProvider()).Info("AutoDoc providers registered")

	// AutoDoc service and handler (using document service registry)
	autoDocSvc := autodocService.NewAutoDocService(docRepo, docServiceRegistry, docAutomationRuleRepo, glob)

	// Create DefaultDMSService for tenant settings
	defaultDMSService := autodocService.NewDefaultDMSService(settLoader)

	// Create upload provider registry
	uploadRegistry := autodocService.NewUploadProviderRegistry("internal")

	// Create RuleExecutionEngine for rule execution with document registry
	ruleExecutionEngine := autodocService.NewRuleExecutionEngine(autoDocSvc, docRepo, docServiceRegistry, defaultDMSService, mappingService, uploadRegistry)

	// Create RuleMatchingService for trigger evaluation
	ruleMatchingService := autodocService.NewRuleMatchingService(autoDocSvc)

	// Create AutoDoc Event Consumers (different from GDrive event consumers)
	autoDocMatterEventConsumer := autodocService.NewMatterEventConsumer(ruleMatchingService, ruleExecutionEngine)
	autoDocClientEventConsumer := autodocService.NewClientEventConsumer(ruleMatchingService, ruleExecutionEngine)

	// Create original EventRuleMatchingService
	originalEventRuleMatchingService := autodocService.NewEventRuleMatchingService(autoDocMatterEventConsumer, autoDocClientEventConsumer, ruleMatchingService)

	// Setup coordination (always enabled)
	// eventRuleMatchingService, err := SetupCoordination(&CoordinationSetupConfig{
	// 	DB:                               db,
	// 	DocSettingRepo:                   docSettingRepo,
	// 	OriginalEventRuleMatchingService: originalEventRuleMatchingService,
	// 	MatterEventConsumer:              autoDocMatterEventConsumer,
	// 	ClientEventConsumer:              autoDocClientEventConsumer,
	// 	GDriveConsumer:                   matterEventConsumer,
	// 	GDriveClientConsumer:             clientEventConsumer,
	// })
	// if err != nil {
	// 	panic(fmt.Sprintf("Failed to setup coordination: %v", err))
	// }

	// Get service clients for AutoDoc handler
	clientClient := clients.GetClient()
	matterClient := clients.GetMatter()

	autoDocHandler := NewAutoDocHandler(autoDocSvc, ruleExecutionEngine, ruleMatchingService, autoDocMatterEventConsumer, autoDocClientEventConsumer, originalEventRuleMatchingService, clientClient, matterClient, entityFetcher)

	// Create AutoDoc provider configuration handler
	autoDocProviderHandler := NewAutoDocProviderHandler(docServiceRegistry)

	// Google Drive client handler with permission sync
	gdriveClientHandler := NewGDriveClientHandler(
		clientEventConsumer,
		gdrivePermissionHandler,
		docSettingRepo,
		autoDocHandler,
	)

	// Google Drive matter handler with permission sync
	gdriveMatterHandler := NewGDriveMatterHandler(
		matterEventConsumer,
		gdrivePermissionHandler,
		docSettingRepo,
		autoDocHandler,
	)

	// Register upload providers
	// Internal upload provider
	internalUploadProvider := autodocService.NewInternalUploadProvider(internalDocService, app.GetGlobURL())
	uploadRegistry.RegisterProvider("internal", internalUploadProvider)

	// Google Drive upload provider
	gdriveUploadProvider := autodocService.NewGDriveUploadProvider(gdriveAdapter, gdriveDocumentService, app.GetSetting().GatewayBaseURL+"/docman")
	uploadRegistry.RegisterProvider("gdrive", gdriveUploadProvider)

	// Create file provider registry
	fileRegistry := autodocService.NewFileProviderRegistry("internal")

	// Register file providers
	// Internal file provider
	internalFileProvider := autodocService.NewInternalFileProvider(internalDocService)
	fileRegistry.RegisterProvider("internal", internalFileProvider)

	// Google Drive file provider
	gdriveFileProvider := autodocService.NewGDriveFileProvider(gdriveAdapter)
	fileRegistry.RegisterProvider("gdrive", gdriveFileProvider)

	// Create AutoDoc file management handler
	autoDocFileHandler := NewAutoDocFileHandler(docServiceRegistry, uploadRegistry, fileRegistry)

	// DocumentService
	documentSvc := documentService.NewDocumentService(docRepo, glob)

	// DocumentHandler (now that AutoDoc service is available)
	docHandler := NewDocumentHandler(docRepo, glob, hrglassid, consumer, entityFetcher, settLoader, globClient, autoDocSvc, documentSvc)

	gdriveV3DocumentsHandler := gdriveV3.NewDocumentsHandler(
		gdriveDocumentService,
		docMappingRepo,
	)
	gdriveV3UploadHandler := gdriveV3.NewUploadHandler(
		gdriveDocumentService,
		app.GetSetting().GatewayBaseURL+"/docman",
	)
	gdriveV3SetupHandler := gdriveV3.NewSetupHandler(
		gdriveSvc,
		clientEventConsumer,
		matterEventConsumer,
	)

	postWithAuth := func(route string, h ginext.Handler) {
		app.Router().POST(route, ginext.AuthRequiredMiddleware, w(h))
	}
	withAuth := func(method, route string, h ginext.Handler) {
		app.Router().Handle(method, route, ginext.AuthRequiredMiddleware, w(h))
	}

	// internal routes
	app.Router().POST("/internal/install", w(NewMigrationHandler(migrationRepo).Install))
	app.Router().POST("/internal/configs/seed", w(docConfigHandler.SeedDocumentConfig))
	app.Router().POST("/internal/consume/documents/crud", w(docHandler.ConsumeCrud))
	app.Router().POST("/internal/consume/documents/billing/cost/attach", w(docHandler.ConsumerBillingCostAttach))
	app.Router().POST("/internal/consume/documents/billing/payment/attach", w(docHandler.ConsumerBillingPaymentAttach))
	app.Router().POST("/internal/consume/documents/billing/cost/deattach", w(docHandler.ConsumerBillingCostDeattach))
	app.Router().POST("/internal/consume/documents/billing/payment/deattach", w(docHandler.ConsumerBillingPaymentDeattach))
	// @deprecated use /internal/consume/autodoc/client/created and /internal/consume/autodoc/client/updated instead
	// Direct provider endpoints may cause race conditions with coordination system
	app.Router().POST("/internal/consume/sharepoint/client/crud", w(docSharepointDocumentHandler.ConsumeSharepointClientCrud))

	// @deprecated use /internal/consume/autodoc/matter/created and /internal/consume/autodoc/matter/updated instead
	// Direct provider endpoints may cause race conditions with coordination system
	app.Router().POST("/internal/consume/sharepoint/matter/crud", w(docSharepointDocumentHandler.ConsumeSharepointMatterCrud))

	// AutoDoc event consumer endpoints - Unified (Recommended)
	app.Router().POST("/internal/consume/autodoc/client/events", w(autoDocHandler.ConsumeClientEvents))
	app.Router().POST("/internal/consume/autodoc/matter/events", w(autoDocHandler.ConsumeMatterEvents))

	// AutoDoc event consumer endpoints - Individual (Backward Compatibility)
	// @deprecated Use /internal/consume/autodoc/client/events instead
	app.Router().POST("/internal/consume/autodoc/client/created", w(autoDocHandler.ConsumeClientCreated))
	// @deprecated Use /internal/consume/autodoc/client/events instead
	app.Router().POST("/internal/consume/autodoc/client/updated", w(autoDocHandler.ConsumeClientUpdated))
	// @deprecated Use /internal/consume/autodoc/matter/events instead
	app.Router().POST("/internal/consume/autodoc/matter/created", w(autoDocHandler.ConsumeMatterCreated))
	// @deprecated Use /internal/consume/autodoc/matter/events instead
	app.Router().POST("/internal/consume/autodoc/matter/updated", w(autoDocHandler.ConsumeMatterUpdated))

	// migrate size
	app.Router().POST("/internal/size/migrate", w(docHandler.MigrateSize))

	// documents v1
	// @TODO: To be removed
	app.Router().POST("/v1/documents", w(docHandler.CreateV1))
	app.Router().GET("/v1/documents", w(docHandler.ListDocumentV1))
	app.Router().GET("/v1/documents/:document_id", w(docHandler.Get))
	app.Router().GET("/v1/documents/:document_id/downloads", w(docHandler.Download))
	app.Router().PATCH("/v1/documents/:document_id", w(docHandler.Update))
	app.Router().DELETE("/v1/documents/:document_id", w(docHandler.Delete))

	app.Router().GET("/v1/configs", w(docConfigHandler.List))

	// sharepoint v2
	app.Router().POST("/v2/sharepoint/setting", w(docSettingHandler.CreateDocumentSetting))
	app.Router().GET("/v2/sharepoint/sites", ginext.AuthRequiredMiddleware, w(docSharepointDocumentHandler.ListSite))
	app.Router().GET("/v2/sharepoint/activities", w(docSharepointDocumentHandler.ListActivity))
	app.Router().GET("/v2/sharepoint/drives/:site_id", ginext.AuthRequiredMiddleware, w(docSharepointDocumentHandler.ListDrive))
	app.Router().GET("/v2/sharepoint/search", w(docSharepointDocumentHandler.SearchFiles))

	// documents v3
	postWithAuth("/v3/documents", docHandler.CreateV3)
	withAuth(http.MethodGet, "/v3/documents", docHandler.ListDocumentV3)

	app.Router().GET("/v3/documents/:document_id", w(docHandler.GetV3))
	app.Router().GET("/v3/documents/:document_id/downloads", w(docHandler.DownloadV3))
	app.Router().PATCH("/v3/documents/:document_id", w(docHandler.UpdateV3))
	app.Router().DELETE("/v3/documents/:document_id", w(docHandler.DeleteV3))

	// sharepoint documents v3
	withAuth(http.MethodPost, "/v3/sharepoint/documents", docSharepointDocumentHandler.CreateV3)
	withAuth(http.MethodGet, "/v3/sharepoint/documents", docSharepointDocumentHandler.ListV3)
	withAuth(http.MethodPatch, "/v3/sharepoint/documents/:document_id", docSharepointDocumentHandler.UpdateV3)
	withAuth(http.MethodDelete, "/v3/sharepoint/documents/:document_id", docSharepointDocumentHandler.DeleteV3)
	withAuth(http.MethodPost, "/v3/sharepoint/documents/upload", docSharepointDocumentHandler.UploadV3)

	// google drive v3 api (consolidated)
	gdriveV3Group := app.Router().Group("/v3/gdrive")
	gdriveV3Group.Use(ginext.AuthRequiredMiddleware)
	gdriveV3Group.Use(gdriveV3.GDriveLoggingMiddleware())
	gdriveV3Group.Use(gdriveV3.GDriveErrorRecoveryMiddleware())

	// Setup endpoints (admin-only)
	setupGroup := gdriveV3Group.Group("/setup")
	setupGroup.Use(gdriveV3.GDriveAdminMiddleware())
	setupGroup.POST("/test-setup", w(gdriveV3SetupHandler.TestSetupV3))
	setupGroup.POST("/complete-setup", w(gdriveV3SetupHandler.CompleteSetupV3))

	// Document endpoints (standard auth)
	documentsGroup := gdriveV3Group.Group("/documents")
	documentsGroup.Use(gdriveV3.GDriveAuthMiddleware())
	documentsGroup.POST("", w(gdriveV3DocumentsHandler.CreateV3))
	documentsGroup.GET("", w(gdriveV3DocumentsHandler.ListV3))
	documentsGroup.PATCH("/:document_id", w(gdriveV3DocumentsHandler.UpdateV3))
	documentsGroup.DELETE("/:document_id", w(gdriveV3DocumentsHandler.DeleteV3))
	documentsGroup.POST("/upload", w(gdriveV3UploadHandler.UploadV3))

	// Search endpoints (standard auth)
	searchGroup := gdriveV3Group.Group("/search")
	searchGroup.Use(gdriveV3.GDriveAuthMiddleware())
	searchGroup.GET("/files", w(gdriveV3DocumentsHandler.SearchFilesV3))

	// Public upload endpoint (no auth required for file content upload)
	app.Router().PUT("/v3/gdrive/upload/:session_token", w(gdriveV3UploadHandler.UploadContentV3))

	// AutoDoc API v3 (automation rules)
	autoDocV3Group := app.Router().Group("/v3/autodoc")
	autoDocV3Group.Use(ginext.AuthRequiredMiddleware)

	// Rule management endpoints
	rulesGroup := autoDocV3Group.Group("/rules")
	rulesGroup.POST("", w(autoDocHandler.CreateRule))
	rulesGroup.GET("", w(autoDocHandler.ListRules))
	rulesGroup.GET("/active", w(autoDocHandler.GetActiveRules))
	rulesGroup.GET("/:id", w(autoDocHandler.GetRule))
	rulesGroup.PUT("/:id", w(autoDocHandler.UpdateRule))
	rulesGroup.DELETE("/:id", w(autoDocHandler.DeleteRule))

	// Rule execution endpoints
	autoDocV3Group.POST("/execute/:id", w(autoDocHandler.ExecuteRule))
	autoDocV3Group.POST("/test/:id", w(autoDocHandler.TestRule))
	autoDocV3Group.GET("/history", w(autoDocHandler.GetExecutionHistory))

	// Variables endpoint
	autoDocV3Group.GET("/variables", w(autoDocHandler.ListVariables))

	// Provider configuration endpoints
	providersGroup := autoDocV3Group.Group("/providers")
	providersGroup.GET("", w(autoDocProviderHandler.ListProviders))
	providersGroup.PUT("/default", w(autoDocProviderHandler.SetDefaultProvider))
	providersGroup.GET("/:provider", w(autoDocProviderHandler.GetProviderInfo))
	providersGroup.POST("/:provider/test", w(autoDocProviderHandler.TestProviderConnection))
	providersGroup.GET("/:provider/fallback", w(autoDocProviderHandler.GetProviderFallback))

	// File management endpoints
	filesGroup := autoDocV3Group.Group("/files")
	filesGroup.GET("", w(autoDocFileHandler.ListFiles))
	filesGroup.POST("", w(autoDocFileHandler.CreateFile))              // Unified endpoint for creating folders (doc_type=1)
	filesGroup.POST("/upload", w(autoDocFileHandler.PreUpload))        // Step 1: Register to get upload link
	filesGroup.POST("/register", w(autoDocFileHandler.RegisterUpload)) // Step 2: Register after upload success
	filesGroup.GET("/:id", w(autoDocFileHandler.GetFile))
	filesGroup.PUT("/:id", w(autoDocFileHandler.UpdateFile))
	filesGroup.DELETE("/:id", w(autoDocFileHandler.DeleteFile))

	// Google Drive internal event endpoints
	app.Router().POST("/internal/consume/gdrive/client/crud", w(gdriveClientHandler.ConsumeGoogleDriveClientCrud))
	app.Router().POST("/internal/consume/gdrive/matter/crud", w(gdriveMatterHandler.ConsumeGoogleDriveMatterCrud))

	app.Router().POST("/share/download", w(docHandler.DownloadShareHandler))

	app.Router().GET("/sharepoint/setup", w(docSharepointDocumentHandler.SetupCallback))
	app.Router().POST("/sharepoint/complete-setup", w(docSharepointDocumentHandler.CompleteSetup))

}
