package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/sharepointclient"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"code.mybil.net/gophers/gokit/util/sliceutil"
	"context"
	"gitlab.com/goxp/cloud0/ginext"
	"net/http"
)

func (h *SharepointHandler) createMatterFolder(
	ctx context.Context,
	sp *sharepointParams,
	folderName string,
	tenantID uint64,
	matterID uint64,
	clientID uint64,
) (*model.DocumentMapping, error) {
	parentDrive, err := h.rDocumentMapping.FirstObjectMapping(
		ctx,
		model.DocTypeParent,
		model.DocProviderSharepoint,
		tenantID,
		0,
		clientID,
	)
	if err != nil {
		return nil, err
	}

	// try to search the folder
	searchResp, err := h.spClient.FilterChildren(ctx, sp.SiteDriveID, sp.AccessToken, parentDrive.DriveID, folderName)
	if err != nil {
		return nil, err
	}

	var driveItem sharepointclient.DriveItemResponse

	if searchResp != nil && len(searchResp.Value) > 0 {
		driveItem = *searchResp.Value[0]
	} else {
		driveItem, err = h.spClient.CreateDriveItem(ctx, sp.SiteDriveID, sp.AccessToken, parentDrive.DriveID, folderName)
		if err != nil {
			return nil, err
		}
	}

	docMatterMapping := &model.DocumentMapping{
		TenantID:       tenantID,
		Type:           model.DocTypeMatter,
		ParentObjectID: 0,
		ObjectID:       matterID,
		ParentDriveID:  parentDrive.DriveID,
		DriveID:        driveItem.ID,
		Provider:       model.DocProviderSharepoint,
	}
	err = h.rDocumentMapping.CreateOrUpdate(ctx, docMatterMapping)
	return docMatterMapping, err
}

func (h *SharepointHandler) handleMatterCreate(
	r *ginext.Request,
	body *model.ConsumeMatterReq,
	spParams *sharepointParams,
	matterFolder string,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "sharepoint.handleMatterCreate")

	matterDrive, err := h.createMatterFolder(ctx, spParams, matterFolder, body.Body.TenantID, body.Body.ID, body.Body.ClientID)
	if err != nil {
		log.Errorf("create matter folder error: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), nil
	}

	// Create mapping permission on sharepoint
	owners := body.Body.Extra.Current.OwnerUsers
	ownerEmails := sliceutil.Map(owners, func(owner model.OwnerUser) string {
		return owner.Email
	})

	if len(ownerEmails) == 0 {
		log.Warnf("no owner email found for matter %d", body.Body.ID)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	listPerm, err := h.spClient.InvitePermissionDriveItem(r.Context(),
		spParams.SiteDriveID,
		spParams.AccessToken,
		matterDrive.DriveID,
		ownerEmails,
	)
	if err != nil {
		log.Errorf(`failed to invite permission for drive item %s: %v`, matterDrive.DriveID, err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), nil
	}

	if err = h.handlePermissionMapping(r.Context(), &listPerm, body.Body.TenantID, matterDrive.DriveID); err != nil {
		log.Errorf("error while handling permission mapping: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), nil
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}
