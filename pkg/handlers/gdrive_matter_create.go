package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/gdrive"
	"bilabl/docman/pkg/repositories"

	"github.com/gookit/goutil/arrutil"
	"gitlab.com/goxp/cloud0/ginext"
	"gitlab.com/goxp/cloud0/logger"
)

// GDriveMatterHandler handles Google Drive matter events with permission sync
type GDriveMatterHandler struct {
	matterEventConsumer *gdrive.MatterEventConsumer
	permissionHandler   *GDrivePermissionHandler
	docSettingRepo      repositories.DocumentSettingRepository
	autodocHandler      *AutoDocHandler
}

// NewGDriveMatterHandler creates a new Google Drive matter handler
func NewGDriveMatterHandler(
	matterEventConsumer *gdrive.MatterEventConsumer,
	permissionHandler *GDrivePermissionHandler,
	docSettingRepo repositories.DocumentSettingRepository,
	autodocHandler *AutoDocHandler,
) *GDriveMatterHandler {
	return &GDriveMatterHandler{
		matterEventConsumer: matterEventConsumer,
		permissionHandler:   permissionHandler,
		docSettingRepo:      docSettingRepo,
		autodocHandler:      autodocHandler,
	}
}

// ConsumeGoogleDriveMatterCrud handles matter events for Google Drive with permission sync
func (h *GDriveMatterHandler) ConsumeGoogleDriveMatterCrud(r *ginext.Request) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleMatterEvents")

	// backup body
	bodyBytes, err := io.ReadAll(r.GinCtx.Request.Body)
	if err != nil {
		return nil, err
	}

	// Restore body
	r.GinCtx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	var body model.ConsumeMatterReq
	r.MustBind(&body)

	// Restore body
	r.GinCtx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	log.Debugf("getting matter event with topic %s", body.Topic)

	// Check if Google Drive is enabled for this tenant
	config, err := h.getGDriveConfig(ctx, body.Body.TenantID)
	if err != nil {
		log.Errorf("error while getting Google Drive config: %v", err)
		_ = h.callAutodoc(body.Topic, r)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), err
	}

	if config == nil || !config.Enabled {
		log.Debugf("tenant %d doesn't have Google Drive enabled", body.Body.TenantID)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	resp := ginext.NewResponseData(http.StatusOK, nil)
	switch body.Topic {
	case "matter.create":
		resp, err = h.handleMatterCreate(r, &body)
	case "matter.update":
		resp, err = h.handleMatterUpdate(r, &body)
	}

	if err != nil {
		log.Errorf("error while handling matter event: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), err
	}

	_ = h.callAutodoc(body.Topic, r)

	return resp, nil
}

func (h *GDriveMatterHandler) callAutodoc(topic string, r *ginext.Request) error {
	var err error
	switch topic {
	case "matter.create":
		_, err = h.autodocHandler.ConsumeMatterCreated(r)
	case "matter.update":
		_, err = h.autodocHandler.ConsumeMatterUpdated(r)
	}

	return err
}

// handleMatterCreate processes matter creation with folder creation and permission sync
func (h *GDriveMatterHandler) handleMatterCreate(
	r *ginext.Request,
	body *model.ConsumeMatterReq,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleMatterCreate")

	// 1. Create matter folder using existing event consumer
	payloadBytes, err := json.Marshal(body)
	if err != nil {
		log.Errorf("failed to marshal event payload: %v", err)
		return nil, err
	}

	err = h.matterEventConsumer.HandleMatterCreated(ctx, string(payloadBytes))
	if err != nil {
		log.Errorf("failed to create matter folder: %v", err)
		return nil, err
	}

	// 2. Check if permission sync is enabled for creation
	if h.shouldSyncPermissions(ctx, body.Body.TenantID, "create") {
		ownerEmails := h.extractOwnerEmails(body.Body.Extra.Current.OwnerUsers)

		if len(ownerEmails) == 0 {
			log.Warnf("no owner email found for matter %d", body.Body.ID)
		} else {
			// Sync permissions using the permission handler with explicit clientID
			// For matter folders, use clientID from body.Body.ClientID
			err = h.permissionHandler.SyncGoogleDrivePermissionsWithClientID(
				ctx,
				body.Body.TenantID,
				"matter",
				body.Body.ID,
				ownerEmails,
				body.Body.ClientID, // Use clientID from matter data
			)
			if err != nil {
				log.Errorf("failed to sync permissions for matter %d: %v", body.Body.ID, err)
				// Don't fail the entire operation if permission sync fails
				// Log the error and continue
			}
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

// handleMatterUpdate processes matter updates with folder updates and permission sync
func (h *GDriveMatterHandler) handleMatterUpdate(
	r *ginext.Request,
	body *model.ConsumeMatterReq,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "gdrive.handleMatterUpdate")

	// 1. Update matter folder using existing event consumer
	payloadBytes, err := json.Marshal(body)
	if err != nil {
		log.Errorf("failed to marshal event payload: %v", err)
		return nil, err
	}

	err = h.matterEventConsumer.HandleMatterUpdated(ctx, string(payloadBytes))
	if err != nil {
		log.Errorf("failed to update matter folder: %v", err)
		return nil, err
	}

	// 2. Check if permission sync is enabled for updates
	if h.shouldSyncPermissions(ctx, body.Body.TenantID, "update") {
		currentOwnerEmails := h.extractOwnerEmails(body.Body.Extra.Current.OwnerUsers)

		if len(currentOwnerEmails) == 0 {
			log.Warnf("no current owner emails found for matter %d - will remove all existing permissions", body.Body.ID)
		}

		// Sync permissions using the permission handler
		// If currentOwnerEmails is empty, this will remove all existing permissions
		err = h.permissionHandler.SyncGoogleDrivePermissionsWithClientID(
			ctx,
			body.Body.TenantID,
			"matter",
			body.Body.ID,
			currentOwnerEmails,
			body.Body.ClientID, // Use clientID from matter data
		)
		if err != nil {
			log.Errorf("failed to sync permissions for matter %d: %v", body.Body.ID, err)
			// Don't fail the entire operation if permission sync fails
			// Log the error and continue
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

// extractOwnerEmails extracts email addresses from owner users
func (h *GDriveMatterHandler) extractOwnerEmails(ownerUsers []model.OwnerUser) []string {
	return arrutil.Map(ownerUsers, func(o model.OwnerUser) (string, bool) {
		return o.Email, true
	})
}

// getGDriveConfig retrieves Google Drive configuration for the tenant
func (h *GDriveMatterHandler) getGDriveConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	setting, err := h.docSettingRepo.GetValueByKey(ctx, tenantID, model.KeyGdriveConfig)
	if model.IsNotFound(err) {
		return nil, nil // No config found, Google Drive not enabled
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get Google Drive config: %w", err)
	}

	var config model.GDriveConfig
	if err := config.FromJSON(setting.Value); err != nil {
		return nil, fmt.Errorf("failed to parse Google Drive config: %w", err)
	}

	return &config, nil
}

// shouldSyncPermissions checks if permission sync is enabled for the given operation
func (h *GDriveMatterHandler) shouldSyncPermissions(ctx context.Context, tenantID uint64, operation string) bool {
	config, err := h.getGDriveConfig(ctx, tenantID)
	if err != nil || config == nil || !config.Enabled {
		return false
	}

	// Check permission config if available
	if config.PermissionConfig != nil {
		switch operation {
		case "create":
			return config.PermissionConfig.SyncOnCreate
		case "update":
			return config.PermissionConfig.SyncOnUpdate
		}
	}

	// Default to true if no specific config
	return true
}
