package handlers

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/googleapi"
)

// TestGDrivePermissionHandler_RealWorldErrors tests with actual error messages from production
func TestGDrivePermissionHandler_RealWorldErrors(t *testing.T) {
	handler := &GDrivePermissionHandler{}

	tests := []struct {
		name        string
		err         error
		expected    bool
		description string
	}{
		{
			name: "real invalidSharingRequest error should not retry",
			err: &googleapi.Error{
				Code:    400,
				Message: "failed to create permission for file 14XoOz8gn_xlug-pAXtc_fjlzSqllNg3L: googleapi: Error 400: Bad Request. User message: \"We weren't able to share your <NAME_EMAIL>. There's a problem with this email or domain.\", invalidSharingRequest.",
			},
			expected:    false,
			description: "This is the exact error message from production logs",
		},
		{
			name: "another real sharing error should not retry",
			err: &googleapi.Error{
				Code:    400,
				Message: "Bad Request. User message: \"Sorry, an internal error has occurred and your request was not completed.\", invalidSharingRequest",
			},
			expected:    false,
			description: "Another common invalidSharingRequest error",
		},
		{
			name: "email domain problem should not retry",
			err: &googleapi.Error{
				Code:    400,
				Message: "We weren't able to share your <NAME_EMAIL>. There's a problem with this email or domain.",
			},
			expected:    false,
			description: "Email domain validation error",
		},
		{
			name: "generic 400 should not retry",
			err: &googleapi.Error{
				Code:    400,
				Message: "Invalid request parameters",
			},
			expected:    false,
			description: "Any 400 error should not be retried",
		},
		{
			name: "500 server error should retry",
			err: &googleapi.Error{
				Code:    500,
				Message: "Internal Server Error",
			},
			expected:    true,
			description: "Server errors should be retried",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.shouldRetryPermissionError(tt.err)
			assert.Equal(t, tt.expected, result,
				"shouldRetryPermissionError() = %v, want %v\nDescription: %s\nError: %v",
				result, tt.expected, tt.description, tt.err)
		})
	}
}

// TestGDrivePermissionHandler_WrappedErrors tests that wrapped Google API errors are handled correctly
func TestGDrivePermissionHandler_WrappedErrors(t *testing.T) {
	handler := &GDrivePermissionHandler{}

	tests := []struct {
		name        string
		err         error
		expected    bool
		description string
	}{
		{
			name: "wrapped 400 error should not retry",
			err: fmt.Errorf("failed to create permission for file 1ecJZO_llE6Tye41he5QdVLDTzpgB1ntW: %w", &googleapi.Error{
				Code:    400,
				Message: "Bad Request. User message: \"You are trying <NAME_EMAIL>. Since there is no Google account associated with this email address, you must check the \"Notify people\" box to invite this recipient.\", invalidSharingRequest",
			}),
			expected:    false,
			description: "Wrapped 400 errors should not be retried",
		},
		{
			name: "wrapped 500 error should retry",
			err: fmt.Errorf("failed to create permission for file 1ecJZO_llE6Tye41he5QdVLDTzpgB1ntW: %w", &googleapi.Error{
				Code:    500,
				Message: "Internal Server Error",
			}),
			expected:    true,
			description: "Wrapped 500 errors should be retried",
		},
		{
			name: "double wrapped 400 error should not retry",
			err: fmt.Errorf("operation failed: %w", fmt.Errorf("failed to create permission for file 1ecJZO_llE6Tye41he5QdVLDTzpgB1ntW: %w", &googleapi.Error{
				Code:    400,
				Message: "invalidSharingRequest",
			})),
			expected:    false,
			description: "Double wrapped 400 errors should not be retried",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.shouldRetryPermissionError(tt.err)
			assert.Equal(t, tt.expected, result,
				"shouldRetryPermissionError() = %v, want %v\nDescription: %s\nError: %v",
				result, tt.expected, tt.description, tt.err)
		})
	}
}
