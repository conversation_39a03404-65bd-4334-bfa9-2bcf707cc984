package handlers

import (
	"net/http"
	"strconv"

	"bilabl/docman/domain/model"

	"gitlab.com/goxp/cloud0/ginext"
)

// ExecutionHistoryResponse represents a single execution history record in API response
type ExecutionHistoryResponse struct {
	ID               uint64                 `json:"id"`
	TenantID         uint64                 `json:"tenant_id"`
	RuleID           uint64                 `json:"rule_id"`
	RuleName         string                 `json:"rule_name"`
	TriggerType      string                 `json:"trigger_type"`
	TriggerPayload   map[string]interface{} `json:"trigger_payload,omitempty"`
	ExecutionStatus  string                 `json:"execution_status"`
	ExecutionResult  map[string]interface{} `json:"execution_result,omitempty"`
	ActionsExecuted  int                    `json:"actions_executed"`
	ActionsSucceeded int                    `json:"actions_succeeded"`
	ActionsFailed    int                    `json:"actions_failed"`
	ErrorMessage     string                 `json:"error_message,omitempty"`
	ExecutionTime    int64                  `json:"execution_time_ms"` // in milliseconds
	StartedAt        string                 `json:"started_at"`
	CompletedAt      *string                `json:"completed_at,omitempty"`
	ExecutedBy       string                 `json:"executed_by"`
	UserID           *uint64                `json:"user_id,omitempty"`
	CreatedAt        string                 `json:"created_at"`
	UpdatedAt        string                 `json:"updated_at"`
}

// ExecutionHistoryListResponse represents the response for listing execution history
type ExecutionHistoryListResponse struct {
	History    []ExecutionHistoryResponse `json:"history"`
	Total      int64                      `json:"total"`
	Page       int                        `json:"page"`
	Limit      int                        `json:"limit"`
	TotalPages int                        `json:"total_pages"`
}

// GetRuleExecutionHistory handles GET /api/autodoc/rules/:id/history
func (h *AutoDocHandler) GetRuleExecutionHistory(r *ginext.Request) (*ginext.Response, error) {
	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// Verify rule exists and belongs to tenant
	rule, err := h.autoDocService.GetRule(r.Context(), params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if rule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := r.GinCtx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := r.GinCtx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// offset := (page - 1) * limit // Will be used when implementing real repository

	// TODO: Get execution history from repository
	// For now, return mock data
	// In the future, this should call:
	// histories, total, err := h.executionHistoryRepo.GetByRuleID(tenantID, params.RuleID, limit, offset)

	// Mock execution history data
	completedAt := "2025-07-23T10:30:01Z"
	userID := ginext.Uint64UserID(r.GinCtx)
	mockHistories := []ExecutionHistoryResponse{
		{
			ID:               1,
			TenantID:         tenantID,
			RuleID:           params.RuleID,
			RuleName:         rule.Name,
			TriggerType:      rule.TriggerType,
			TriggerPayload:   map[string]interface{}{"matter": map[string]interface{}{"id": "MTR-001", "name": "Test Matter"}},
			ExecutionStatus:  model.ExecutionStatusSuccess,
			ExecutionResult:  map[string]interface{}{"actions_completed": 2, "files_created": 1},
			ActionsExecuted:  2,
			ActionsSucceeded: 2,
			ActionsFailed:    0,
			ExecutionTime:    1500, // 1.5 seconds in milliseconds
			StartedAt:        "2025-07-23T10:30:00Z",
			CompletedAt:      &completedAt,
			ExecutedBy:       model.ExecutedByManual,
			UserID:           &userID,
			CreatedAt:        "2025-07-23T10:30:00Z",
			UpdatedAt:        "2025-07-23T10:30:01Z",
		},
	}

	totalPages := int((int64(len(mockHistories)) + int64(limit) - 1) / int64(limit))

	response := ExecutionHistoryListResponse{
		History:    mockHistories,
		Total:      int64(len(mockHistories)),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// GetAllExecutionHistory handles GET /api/autodoc/history
func (h *AutoDocHandler) GetAllExecutionHistory(r *ginext.Request) (*ginext.Response, error) {
	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := r.GinCtx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := r.GinCtx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Parse optional status filter
	status := r.GinCtx.Query("status")

	// offset := (page - 1) * limit // Will be used when implementing real repository

	// TODO: Get execution history from repository
	// For now, return mock data
	// In the future, this should call:
	// if status != "" {
	//     histories, total, err := h.executionHistoryRepo.GetByStatus(tenantID, status, limit, offset)
	// } else {
	//     histories, total, err := h.executionHistoryRepo.GetByTenantID(tenantID, limit, offset)
	// }

	// Mock execution history data for all rules
	completedAt1 := "2025-07-23T10:30:01Z"
	completedAt2 := "2025-07-23T10:25:00Z"
	userID := ginext.Uint64UserID(r.GinCtx)
	mockHistories := []ExecutionHistoryResponse{
		{
			ID:               1,
			TenantID:         tenantID,
			RuleID:           1,
			RuleName:         "Test Rule 1",
			TriggerType:      "matter.create",
			TriggerPayload:   map[string]interface{}{"matter": map[string]interface{}{"id": "MTR-001", "name": "Test Matter"}},
			ExecutionStatus:  model.ExecutionStatusSuccess,
			ExecutionResult:  map[string]interface{}{"actions_completed": 2, "files_created": 1},
			ActionsExecuted:  2,
			ActionsSucceeded: 2,
			ActionsFailed:    0,
			ExecutionTime:    1500,
			StartedAt:        "2025-07-23T10:30:00Z",
			CompletedAt:      &completedAt1,
			ExecutedBy:       model.ExecutedByManual,
			UserID:           &userID,
			CreatedAt:        "2025-07-23T10:30:00Z",
			UpdatedAt:        "2025-07-23T10:30:01Z",
		},
		{
			ID:               2,
			TenantID:         tenantID,
			RuleID:           2,
			RuleName:         "Test Rule 2",
			TriggerType:      "client.update",
			TriggerPayload:   map[string]interface{}{"client": map[string]interface{}{"id": "CLI-001", "name": "Test Client"}},
			ExecutionStatus:  model.ExecutionStatusFailed,
			ExecutionResult:  map[string]interface{}{"actions_completed": 0, "error": "Template not found"},
			ActionsExecuted:  1,
			ActionsSucceeded: 0,
			ActionsFailed:    1,
			ErrorMessage:     "Template file not found: template.docx",
			ExecutionTime:    500,
			StartedAt:        "2025-07-23T10:25:00Z",
			CompletedAt:      &completedAt2,
			ExecutedBy:       model.ExecutedBySystem,
			CreatedAt:        "2025-07-23T10:25:00Z",
			UpdatedAt:        "2025-07-23T10:25:00Z",
		},
	}

	// Filter by status if provided
	if status != "" {
		filtered := make([]ExecutionHistoryResponse, 0)
		for _, h := range mockHistories {
			if h.ExecutionStatus == status {
				filtered = append(filtered, h)
			}
		}
		mockHistories = filtered
	}

	// If no data matches filter, return empty but valid response
	if len(mockHistories) == 0 && status != "" {
		mockHistories = []ExecutionHistoryResponse{}
	}

	totalPages := int((int64(len(mockHistories)) + int64(limit) - 1) / int64(limit))

	response := ExecutionHistoryListResponse{
		History:    mockHistories,
		Total:      int64(len(mockHistories)),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// GetExecutionHistory handles GET /v3/autodoc/history
func (h *AutoDocHandler) GetExecutionHistory(r *ginext.Request) (*ginext.Response, error) {
	// Parse query parameters
	page := 1
	limit := 20
	ruleIDStr := r.GinCtx.Query("rule_id")
	status := r.GinCtx.Query("status")

	if pageStr := r.GinCtx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := r.GinCtx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// TODO: Integrate with ExecutionHistoryService when available
	// For now, return mock execution history data
	// In the future, this should call:
	// history, total, err := h.executionHistoryService.GetHistory(r.Context(), tenantID, filters)

	// Mock execution history data
	mockHistory := []ExecutionHistoryResponse{
		{
			ID:               1,
			TenantID:         tenantID,
			RuleID:           1,
			RuleName:         "Client Welcome Package",
			TriggerType:      "client.update",
			ExecutionStatus:  "success",
			ActionsExecuted:  2,
			ActionsSucceeded: 2,
			ActionsFailed:    0,
			ExecutionTime:    1250,
			StartedAt:        "2024-01-15T10:30:00Z",
			CompletedAt:      stringPtrForHistory("2024-01-15T10:30:01Z"),
			ExecutedBy:       "system",
			CreatedAt:        "2024-01-15T10:30:00Z",
			UpdatedAt:        "2024-01-15T10:30:01Z",
		},
		{
			ID:               2,
			TenantID:         tenantID,
			RuleID:           2,
			RuleName:         "Matter Document Setup",
			TriggerType:      "matter.create",
			ExecutionStatus:  "failed",
			ActionsExecuted:  1,
			ActionsSucceeded: 0,
			ActionsFailed:    1,
			ErrorMessage:     "Source template not found",
			ExecutionTime:    500,
			StartedAt:        "2024-01-15T11:00:00Z",
			CompletedAt:      stringPtrForHistory("2024-01-15T11:00:01Z"),
			ExecutedBy:       "system",
			CreatedAt:        "2024-01-15T11:00:00Z",
			UpdatedAt:        "2024-01-15T11:00:01Z",
		},
	}

	// Filter by rule_id if specified
	if ruleIDStr != "" {
		if ruleID, err := strconv.ParseUint(ruleIDStr, 10, 64); err == nil {
			filteredHistory := make([]ExecutionHistoryResponse, 0)
			for _, item := range mockHistory {
				if item.RuleID == ruleID {
					filteredHistory = append(filteredHistory, item)
				}
			}
			mockHistory = filteredHistory
		}
	}

	// Filter by status if specified
	if status != "" {
		filteredHistory := make([]ExecutionHistoryResponse, 0)
		for _, item := range mockHistory {
			if item.ExecutionStatus == status {
				filteredHistory = append(filteredHistory, item)
			}
		}
		mockHistory = filteredHistory
	}

	// Calculate pagination
	total := int64(len(mockHistory))
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	// Apply pagination
	start := (page - 1) * limit
	end := start + limit
	if start >= int(total) {
		mockHistory = []ExecutionHistoryResponse{}
	} else {
		if end > int(total) {
			end = int(total)
		}
		mockHistory = mockHistory[start:end]
	}

	// Create response
	response := ExecutionHistoryListResponse{
		History:    mockHistory,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// Helper functions for execution history
func stringPtrForHistory(s string) *string {
	return &s
}

func uint64PtrForHistory(u uint64) *uint64 {
	return &u
}
