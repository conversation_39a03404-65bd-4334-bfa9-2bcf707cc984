package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/sharepointclient"
	"bilabl/docman/pkg/transport"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/pkg/logger"

	"gitlab.com/goxp/cloud0/ginext"
)

type DriveResponse struct {
	Name   string `json:"name"`
	WebUrl string `json:"webUrl"`
	ID     string `json:"ID"`
}

type SiteResponse struct {
	Name   string `json:"name"`
	WebUrl string `json:"webUrl"`
	ID     string `json:"ID"`
}

type ActionData struct {
	DocID        string `json:"doc_id"`
	Name         string `json:"name"`
	OldName      string `json:"old_name"`
	WebURL       string `json:"web_url"`
	ParentFolder string `json:"parent_folder"`
	IsFile       bool   `json:"is_file"`
	From         string `json:"from"`
	To           string `json:"to"`
}

type ActivityItemResponse struct {
	ID         string     `json:"id"`
	ActorEmail string     `json:"actor_email"`
	ActorName  string     `json:"actor_name"`
	ActionType string     `json:"action_type"`
	ActionData ActionData `json:"action_data"`
	TenantID   uint64     `json:"tenant_id"`
	Timestamp  string     `json:"timestamp"`
}

type ActivityMeta struct {
	NextPage string `json:"next_page"`
	PageSize uint64 `json:"page_size"`
}

type ActivityListResponse struct {
	Data []ActivityItemResponse `json:"data"`
	Meta ActivityMeta           `json:"meta"`
}

type FileItem struct {
	DocID          string `json:"docId"`
	Name           string `json:"name"`
	Size           uint64 `json:"size"`
	WebURL         string `json:"webUrl"`
	IsFile         bool   `json:"isFile"`
	DateModified   string `json:"dateModified" example:"2022-09-17T04:13:47.1140441+00:00"`
	DateCreated    string `json:"dateCreated" example:"2022-09-17T04:13:47.1140441+00:00"`
	LastModifiedBy string `json:"lastModifiedBy"`
}

type FileListMeta struct {
	NextPage string `json:"next_page"`
	PageSize int    `json:"page_size"`
	Total    int    `json:"total"`
}

type FileListItemResponse struct {
	Data []FileItem   `json:"data"`
	Meta FileListMeta `json:"meta"`
}

type ListDocumentV3Request struct {
	ID         string `json:"id" query:"id" form:"id"`
	ObjectID   string `json:"object_id" query:"object_id" form:"object_id"`
	ObjectType string `json:"object_type" query:"object_type" form:"object_type"`
	Search     string `json:"search" query:"search" form:"search"`
}

type DocumentV3Response struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	ParentID       uint64 `json:"parent_id"`
	DocType        int    `json:"doc_type"`
	ObjectType     int    `json:"object_type"`
	ObjectID       uint64 `json:"object_id"`
	SubObjectID    uint64 `json:"sub_object_id"`
	HasChild       bool   `json:"has_child"`
	IsFile         bool   `json:"is_file"`
	IsInternal     bool   `json:"is_internal"`
	Size           uint64 `json:"size"`
	Type           string `json:"type"`
	InternalType   int    `json:"internal_type"`
	DateCreated    string `json:"date_created"`
	DateModified   string `json:"date_modified"`
	LastModifiedBy string `json:"last_modified_by"`
	WebURL         string `json:"web_url"`
	Status         int    `json:"status"`
}

type ListDocumentV3Response struct {
	Cwd  DocumentV3Response   `json:"cwd"`
	Data []DocumentV3Response `json:"data"`
}

type CreatDocumentV3Request struct {
	ParentID   string `json:"parent_id"`
	Name       string `json:"name"`
	ObjectID   uint64 `json:"object_id"`
	ObjectType string `json:"object_type"`
}

type CreatDocumentV3Response struct {
	ID string `json:"id"`
}

type UpdateDocumentV3Request struct {
	Name       string `json:"name"`
	ObjectID   uint64 `json:"object_id"`
	ObjectType string `json:"object_type"`
}

type UpdateDocumentV3Response struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type UploadV3Request struct {
	ID       string `json:"id"`
	FileName string `json:"file_name"`
}

type UploadV3Response struct {
	UploadUrl string `json:"upload_url"`
}

type SharepointHandler struct {
	rDocumentMapping    repositories.DocumentMappingRepository
	rDocumentPermission repositories.DocumentPermissionMappingRepository
	rDocumentSetting    repositories.DocumentSettingRepository
	spClient            sharepointclient.Client
	consumer            transport.Consumer
	fetcher             entity.GenericFetcher
	idClient            transport.Hrglassid
	publicBaseUrl       string
}

func NewSharepointDocumentHandler(
	rDocumentMapping repositories.DocumentMappingRepository,
	rDocumentPermission repositories.DocumentPermissionMappingRepository,
	rDocumentSetting repositories.DocumentSettingRepository,
	spClient sharepointclient.Client,
	consumer transport.Consumer,
	fetcher entity.GenericFetcher,
	idClient transport.Hrglassid,
	publicBaseUrl string,
) *SharepointHandler {
	return &SharepointHandler{
		rDocumentMapping:    rDocumentMapping,
		rDocumentPermission: rDocumentPermission,
		rDocumentSetting:    rDocumentSetting,
		spClient:            spClient,
		consumer:            consumer,
		fetcher:             fetcher,
		idClient:            idClient,
		publicBaseUrl:       publicBaseUrl,
	}
}

func IsValidObjectType(objectType string) bool {
	switch objectType {
	case
		"client",
		"matter",
		"staff":
		return true
	}
	return false
}

func lastURI(url string) string {
	parts := strings.Split(url, "/")
	last := parts[len(parts)-1]

	return last
}

type sharepointParams struct {
	TenantID         uint64
	ExternalTenantID string
	SiteDriveID      string
	AccessToken      string
}

func SharepointActivityEvent(objectID uint64, tenantID uint64, objectType string, actionType transport.ActionType, action transport.ActivityAction, activityType uint, actor uint64, extra transport.EventExtraData) transport.GeneralEventMessage {
	var objectTypeInt uint
	switch objectType {
	case transport.ObjectTypeClient:
		objectTypeInt = transport.ActivityObjectTypeClient
	case transport.ObjectTypeStaff:
		objectTypeInt = transport.ActivityObjectTypeStaff
	case transport.ObjectTypeMatter:
		objectTypeInt = transport.ActivityObjectTypeMatter
	}

	return transport.GeneralEventMessage{
		ObjectType:    objectTypeInt,
		ObjectID:      objectID,
		SubObjectType: objectTypeInt,
		SubObjectID:   objectID,
		ActionType:    actionType,
		Action:        action,
		Type:          activityType,
		ActorID:       actor,
		TenantID:      tenantID,
		Extra:         extra,
	}
}

func (h *SharepointHandler) GetAccessToken(ctx context.Context, tenantID uint64, externalTenantId string) (string, error) {
	log := bilabllog.CreateContextLogger(ctx)

	// get exist access token
	filters := []*model.Filter{
		model.NewFilterE("tenant_id", tenantID),
		model.NewFilterE("key", model.KeyAccessToken),
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	token, err := h.rDocumentSetting.FindOne(ctx, queryBuild)
	if err != nil {
		log.Errorf("can not load access token: %v", err)
		return "", err
	}

	if !time.Now().Before(token.ExpiredAt) || token.Value == "" {
		accessToken, err := h.spClient.ExchangeTokenBySecret(ctx, externalTenantId)
		if err != nil {
			log.Errorf("failed to renew access token: %v", err)
			return "", err
		}

		docClientSetting := &model.DocumentSetting{
			TenantID:  tenantID,
			Key:       "access_token",
			Value:     accessToken,
			ExpiredAt: time.Now().Local().Add(time.Second * time.Duration(3599)),
		}
		err = h.rDocumentSetting.CreateOrUpdate(ctx, queryBuild, docClientSetting)
		if err != nil {
			log.Errorf("can not save access token: %v", err)
			return "", err
		}

		return accessToken, nil
	}

	return token.Value, nil
}

func (h *SharepointHandler) loadSharepointParams(ctx context.Context, tenantID uint64) (*sharepointParams, error) {
	externalTenant, err := h.rDocumentSetting.GetValueByKey(ctx, tenantID, "external_tenant_id")
	if err != nil {
		return nil, err
	}

	// Get site drive_id
	siteDrive, err := h.rDocumentSetting.GetValueByKey(ctx, tenantID, "site_drive_id")
	if err != nil {
		return nil, err
	}

	// Get access token
	accessToken, err := h.GetAccessToken(ctx, tenantID, externalTenant.Value)
	if err != nil {
		return nil, err
	}

	return &sharepointParams{
		TenantID:         tenantID,
		ExternalTenantID: externalTenant.Value,
		SiteDriveID:      siteDrive.Value,
		AccessToken:      accessToken,
	}, nil
}

func (h *SharepointHandler) ListV3(r *ginext.Request) (*ginext.Response, error) {
	query := &ListDocumentV3Request{}
	r.MustBind(query)

	act := actor.FromGinCtx(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), act.TenantID)
	r.MustNoError(err)

	// In old version, we return data in body directly
	// without .data nesting
	// To keep consistent with all APIs, we add a query param to control it
	isNestDataNeed := r.Query("datanested") == "true"

	var driveId string
	if query.ID != "" {
		driveId = query.ID
	} else {
		// Get drive_id
		filters := []*model.Filter{
			model.NewFilterE("tenant_id", act.TenantID),
			model.NewFilterE("object_id", query.ObjectID),
			model.NewFilterE("type", query.ObjectType),
			model.NewFilterE("provider", model.DocProviderSharepoint),
		}

		queryBuild := helper.BuildQuery("", filters, nil, nil)
		drive, err := h.rDocumentMapping.FindOne(r.Context(), queryBuild)
		r.MustNoError(err)
		driveId = drive.DriveID
	}

	var documents []DocumentV3Response
	if query.Search != "" {
		searchStr := strings.Trim(query.Search, "*")
		listDriveItem, err := h.spClient.SearchDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, driveId, searchStr, 1000, "")
		r.MustNoError(err)
		for _, item := range listDriveItem.Value {
			document := DocumentV3Response{
				ID:             item.ID,
				Name:           item.Name,
				IsFile:         item.File.MimeType != "",
				IsInternal:     false,
				HasChild:       item.Folder.ChildCount > 0,
				Size:           item.Size,
				Type:           helper.ExtensionByType(item.File.MimeType),
				DateCreated:    item.CreatedDateTime,
				DateModified:   item.LastModifiedDateTime,
				LastModifiedBy: item.LastModifiedBy.User.DisplayName,
				WebURL:         item.WebUrl,
			}
			documents = append(documents, document)
		}
	} else {
		listDriveItem, err := h.spClient.GetListDriveItemChildren(r.Context(), spParams.SiteDriveID, spParams.AccessToken, driveId)
		r.MustNoError(err)
		for _, item := range listDriveItem.Value {
			document := DocumentV3Response{
				ID:             item.ID,
				Name:           item.Name,
				IsFile:         item.File.MimeType != "",
				IsInternal:     false,
				HasChild:       item.Folder.ChildCount > 0,
				Size:           item.Size,
				Type:           helper.ExtensionByType(item.File.MimeType),
				DateCreated:    item.CreatedDateTime,
				DateModified:   item.LastModifiedDateTime,
				LastModifiedBy: item.LastModifiedBy.User.DisplayName,
				WebURL:         item.WebUrl,
			}
			documents = append(documents, document)
		}
	}

	driveItem, _ := h.spClient.GetDriveItemDetail(r.Context(), spParams.SiteDriveID, spParams.AccessToken, driveId)

	// Response format
	resp := ListDocumentV3Response{
		Cwd: DocumentV3Response{
			ID:             driveItem.ID,
			Name:           driveItem.Name,
			IsFile:         driveItem.File.MimeType != "",
			IsInternal:     false,
			HasChild:       driveItem.Folder.ChildCount > 0,
			Size:           driveItem.Size,
			Type:           helper.ExtensionByType(driveItem.File.MimeType),
			DateCreated:    driveItem.CreatedDateTime,
			DateModified:   driveItem.LastModifiedDateTime,
			LastModifiedBy: driveItem.LastModifiedBy.User.DisplayName,
			WebURL:         driveItem.WebUrl,
		},
		Data: documents,
	}

	// Return response based on API version compatibility:
	// - If datanested=true: Return with standard API wrapper format {"data": resp, "status": "success"}
	// - If datanested=false: Return raw response body directly for backward compatibility with old clients
	if isNestDataNeed {
		return ginext.NewResponseData(http.StatusOK, resp), nil
	}

	return ginext.NewResponse(http.StatusOK, ginext.WithRawBody(resp)), nil
}

func (h *SharepointHandler) CreateV3(r *ginext.Request) (*ginext.Response, error) {
	var body CreatDocumentV3Request
	r.MustBind(&body)
	act := actor.FromGinCtx(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), act.TenantID)
	r.MustNoError(err)

	document, err := h.spClient.CreateDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, body.ParentID, body.Name)
	r.MustNoError(err)

	// trigger create document activity
	extra := transport.EventExtraData{
		DocID:   body.ParentID,
		Name:    body.Name,
		Key:     document.WebUrl,
		DocType: "folder",
	}
	eventDat := SharepointActivityEvent(body.ObjectID, act.TenantID, body.ObjectType, transport.ActionTypeCreate, transport.ActionAdd, transport.ActivityTypeDocument, act.ID, extra)
	h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)

	return ginext.NewResponseData(http.StatusCreated, &CreatDocumentV3Response{ID: document.ID}), nil
}

func (h *SharepointHandler) UpdateV3(r *ginext.Request) (*ginext.Response, error) {
	body := &UpdateDocumentV3Request{}
	r.MustBind(body)

	query := struct {
		DocumentID string `uri:"document_id"`
	}{}
	r.MustBindUri(&query)

	act := actor.FromGinCtx(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), act.TenantID)
	r.MustNoError(err)

	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  act.TenantID,
			Method: "=",
		},
		{
			Key:    "drive_id",
			Value:  query.DocumentID,
			Method: "=",
		},
		{
			Key:    "provider",
			Value:  "sharepoint",
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)

	// Load existing mapping
	drive, _ := h.rDocumentMapping.FindOne(r.Context(), queryBuild)
	if drive.ID == 0 {
		driveItem, err := h.spClient.RenameDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, query.DocumentID, body.Name)
		r.MustNoError(err)

		// trigger delete document activity
		var docType string
		if driveItem.File.MimeType != "" {
			docType = "document"
		} else {
			docType = "folder"
		}
		extra := transport.EventExtraData{
			DocID:   query.DocumentID,
			Name:    body.Name,
			Key:     "",
			DocType: docType,
		}
		eventDat := SharepointActivityEvent(body.ObjectID, act.TenantID, body.ObjectType, transport.ActionTypeUpdate, transport.ActionUpdate, transport.ActivityTypeDocument, act.ID, extra)
		h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)
	}

	return ginext.NewResponseData(http.StatusNoContent, &UpdateDocumentV3Response{ID: query.DocumentID, Name: body.Name}), nil
}

func (h *SharepointHandler) DeleteV3(r *ginext.Request) (*ginext.Response, error) {
	query := struct {
		DocumentID string `uri:"document_id"`
	}{}
	r.MustBindUri(&query)

	act := actor.FromGinCtx(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), act.TenantID)
	r.MustNoError(err)

	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  act.TenantID,
			Method: "=",
		},
		{
			Key:    "drive_id",
			Value:  query.DocumentID,
			Method: "=",
		},
		{
			Key:    "provider",
			Value:  "sharepoint",
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)

	// Load existing mapping
	drive, _ := h.rDocumentMapping.FindOne(r.Context(), queryBuild)
	// Ensure delete the folder without mapping.
	if drive.ID == 0 {
		// delete on sharepoint
		_ = h.spClient.DeleteDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, query.DocumentID)
	}

	return ginext.NewResponseData(http.StatusNoContent, nil), nil
}

func (h *SharepointHandler) ListSite(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	act := actor.FromGinCtx(r.GinCtx)
	externalTenant, err := h.rDocumentSetting.GetValueByKey(r.Context(), act.TenantID, "external_tenant_id")
	if err != nil {
		return nil, err
	}

	// Get access token
	accessToken, err := h.GetAccessToken(r.Context(), act.TenantID, externalTenant.Value)
	if err != nil {
		return nil, err
	}

	listSite, err := h.spClient.GetListSite(r.Context(), accessToken)
	if err != nil {
		log.Errorf("server error: %v", err)
		return nil, ginext.NewError(500, "server error")
	}

	// Get list site
	var sites []SiteResponse
	for _, item := range listSite.Value {
		site := SiteResponse{
			Name:   item.Name,
			WebUrl: item.WebUrl,
			ID:     item.ID,
		}

		sites = append(sites, site)
	}

	return ginext.NewResponseData(http.StatusOK, sites), nil
}

func (h *SharepointHandler) ListDrive(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	query := struct {
		SiteID string `uri:"site_id"`
	}{}
	r.MustBindUri(&query)

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	// Get external_tenant_id
	externalTenant, err := h.rDocumentSetting.GetValueByKey(r.Context(), tenantID, "external_tenant_id")
	r.MustNoError(err)

	// Get access token
	accessToken, err := h.GetAccessToken(r.Context(), tenantID, externalTenant.Value)
	r.MustNoError(err)

	listDrive, err := h.spClient.GetListDrive(r.Context(), query.SiteID, accessToken)
	if err != nil {
		log.Errorf("server error: %v", err)
		return nil, ginext.NewError(500, "server error")
	}

	// Get list drive
	var drives []DriveResponse
	for _, item := range listDrive.Value {
		drive := DriveResponse{
			Name:   item.Name,
			WebUrl: item.WebUrl,
			ID:     item.ID,
		}

		drives = append(drives, drive)
	}

	return ginext.NewResponseData(http.StatusOK, drives), nil
}

func (h *SharepointHandler) ConsumeSharepointMatterCrud(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeMatterReq
	r.MustBind(&body)

	log.Debugf("getting client event with topic %s", body.Topic)

	// Direct provider endpoints are deprecated - use coordinated processing
	deprecateDirectCalls := strings.ToLower(os.Getenv("COORDINATION_DEPRECATE_DIRECT_CALLS")) == "true"

	if deprecateDirectCalls {
		log.Warn("Direct SharePoint provider endpoint is deprecated")
		return nil, ginext.NewError(http.StatusGone,
			"Direct provider endpoints are deprecated. Use /internal/consume/autodoc/matter/created for coordinated processing")
	} else {
		log.Warn("Direct SharePoint provider endpoint called - this may cause conflicts with coordination")
		log.Warn("Consider using /internal/consume/autodoc/matter/created for coordinated processing")
	}

	// Get external_tenant_id
	_, err := h.rDocumentSetting.GetValueByKey(r.Context(), body.Body.TenantID, model.KeyExternalTenantID)

	if model.IsNotFound(err) {
		log.Debugf("tenant %d doesn't have external_tenant_id", body.Body.TenantID)
		// no retry here
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	spParams, err := h.loadSharepointParams(r.Context(), body.Body.TenantID)
	r.MustNoError(err)

	// Define the matter folderName
	matterFolder := fmt.Sprintf("%s - %s", body.Body.Name, body.Body.Code)

	if "matter.create" == body.Topic {
		return h.handleMatterCreate(r, &body, spParams, matterFolder)
	}

	if "matter.update" == body.Topic {
		return h.handleMatterUpdate(r, &body, spParams, matterFolder)
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *SharepointHandler) UploadV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	body := UploadV3Request{}
	r.MustBind(&body)

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), tenantID)
	r.MustNoError(err)

	resp, err := h.spClient.CreateUploadURL(r.Context(), spParams.SiteDriveID, spParams.AccessToken, body.ID, body.FileName)

	if err != nil {
		log.Errorf("server error: %v", err)
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	return ginext.NewResponseData(http.StatusOK, &UploadV3Response{UploadUrl: resp.UploadUrl}), nil
}

func (h *SharepointHandler) ListActivity(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	query := struct {
		NextPage string `json:"next_page" query:"next_page" form:"next_page"`
	}{}
	r.MustBind(&query)

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	spParams, err := h.loadSharepointParams(r.Context(), tenantID)
	r.MustNoError(err)

	listActivity, err := h.spClient.GetListActivity(r.Context(), spParams.SiteDriveID, spParams.AccessToken, query.NextPage)
	if err != nil {
		log.Errorf("server error: %v", err)
		return nil, ginext.NewError(500, "server error")
	}

	var nextPage string
	if listActivity.NextLink != "" {
		u, _ := url.Parse(listActivity.NextLink)
		nextPage = u.Query().Get("$skiptoken")
	}

	// Get list activity item
	var actionType string
	var actionData ActionData
	var activities []ActivityItemResponse
	for _, item := range listActivity.Value {
		actionType = "create"
		actionData = ActionData{
			DocID:        item.DriveItem.ID,
			Name:         item.DriveItem.Name,
			WebURL:       item.DriveItem.WebUrl,
			ParentFolder: lastURI(item.DriveItem.ParentReference.Path),
			IsFile:       item.DriveItem.File.MimeType != "",
		}
		if item.Action.Version.NewVersion != "" {
			actionType = "edit"
			actionData = ActionData{
				DocID:        item.DriveItem.ID,
				Name:         item.DriveItem.Name,
				WebURL:       item.DriveItem.WebUrl,
				ParentFolder: lastURI(item.DriveItem.ParentReference.Path),
				IsFile:       item.DriveItem.File.MimeType != "",
			}
		}
		if item.Action.Move.From != "" {
			actionType = "move"
			actionData = ActionData{
				DocID:        item.DriveItem.ID,
				Name:         item.DriveItem.Name,
				WebURL:       item.DriveItem.WebUrl,
				ParentFolder: lastURI(item.DriveItem.ParentReference.Path),
				IsFile:       item.DriveItem.File.MimeType != "",
				From:         item.Action.Move.From,
				To:           lastURI(item.DriveItem.ParentReference.Path),
			}
		}
		if item.Action.Move.To != "" {
			continue
		}
		if item.Action.Rename.OldName != "" {
			actionType = "rename"
			actionData = ActionData{
				DocID:        item.DriveItem.ID,
				Name:         item.DriveItem.Name,
				OldName:      item.Action.Rename.OldName,
				WebURL:       item.DriveItem.WebUrl,
				ParentFolder: lastURI(item.DriveItem.ParentReference.Path),
				IsFile:       item.DriveItem.File.MimeType != "",
			}
		}
		if item.Action.Delete.Name != "" {
			actionType = "delete"
			actionData = ActionData{
				Name:         item.Action.Delete.Name,
				ParentFolder: item.DriveItem.Name,
				IsFile:       item.Action.Delete.ObjectType == "File",
			}
		}

		activity := ActivityItemResponse{
			ID:         item.ID,
			ActorEmail: item.Actor.User.UserPrincipalName,
			ActorName:  item.Actor.User.DisplayName,
			ActionType: actionType,
			ActionData: actionData,
			TenantID:   tenantID,
			Timestamp:  item.Times.RecordedDateTime,
		}

		activities = append(activities, activity)
	}

	// response
	resp := ActivityListResponse{
		Data: activities,
		Meta: ActivityMeta{
			NextPage: nextPage,
			PageSize: 20,
		},
	}

	return ginext.NewResponse(200, ginext.WithRawBody(resp)), nil
}

func (h *SharepointHandler) SearchFiles(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	query := struct {
		Keyword  string `json:"keyword" query:"keyword" form:"keyword"`
		NextPage string `json:"next_page" query:"next_page" form:"next_page"`
		PageSize int    `json:"page_size" query:"page_size" form:"page_size"`
	}{}
	r.MustBind(&query)

	// Input validation
	if strings.TrimSpace(query.Keyword) == "" {
		log.Error("Search keyword is required")
		return nil, ginext.NewError(http.StatusBadRequest, "Search keyword is required")
	}

	// Sanitize keyword to prevent injection attacks
	sanitizedKeyword := strings.TrimSpace(query.Keyword)
	if len(sanitizedKeyword) > 255 {
		log.Error("Search keyword too long")
		return nil, ginext.NewError(http.StatusBadRequest, "Search keyword must be 255 characters or less")
	}

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("Invalid tenant ID")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid tenant ID")
	}

	spParams, err := h.loadSharepointParams(r.Context(), tenantID)
	if err != nil {
		log.Errorf("Failed to load SharePoint parameters: %v", err)
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to load SharePoint configuration")
	}

	// Validate and set page size with proper bounds
	pageSize := query.PageSize
	if pageSize <= 0 {
		pageSize = 100 // Default page size
	} else if pageSize > 1000 {
		pageSize = 1000 // Maximum page size to prevent abuse
	}

	ctx, cancel := context.WithTimeout(r.Context(), time.Second*60)
	defer cancel()

	var files []FileItem
	var nextPage string
	maxResults := 100 // Limit total results to prevent excessive memory usage

	for {
		select {
		case <-ctx.Done():
			log.Warn("Search operation timed out")
			goto searchComplete
		default:
		}

		listDriveItem, err := h.spClient.SearchDriveItem(ctx, spParams.SiteDriveID, spParams.AccessToken, "root", sanitizedKeyword, pageSize, nextPage)
		if err != nil {
			log.Errorf("SharePoint search failed: %v", err)
			return nil, ginext.NewError(http.StatusInternalServerError, "Search operation failed")
		}

		// Parse next page token with proper error handling
		if listDriveItem.NextLink != "" {
			u, parseErr := url.Parse(listDriveItem.NextLink)
			if parseErr != nil {
				log.Errorf("Failed to parse next link URL: %v", parseErr)
				nextPage = ""
			} else {
				nextPage = u.Query().Get("$skiptoken")
			}
		} else {
			nextPage = ""
		}

		// Process search results with nil checks
		for _, item := range listDriveItem.Value {
			if item == nil {
				continue
			}

			// Only include files (not folders) in search results
			if item.File.MimeType != "" {
				// Validate required fields before creating FileItem
				if item.ID == "" || item.Name == "" {
					log.Warnf("Skipping item with missing required fields: ID=%s, Name=%s", item.ID, item.Name)
					continue
				}

				// Safe access to nested fields with nil checks
				var lastModifiedBy string
				if item.LastModifiedBy.User.DisplayName != "" {
					lastModifiedBy = item.LastModifiedBy.User.DisplayName
				}

				file := FileItem{
					DocID:          item.ID,
					Name:           item.Name,
					WebURL:         item.WebUrl,
					Size:           item.Size,
					IsFile:         true, // We already confirmed this is a file
					DateCreated:    item.CreatedDateTime,
					DateModified:   item.LastModifiedDateTime,
					LastModifiedBy: lastModifiedBy,
				}
				files = append(files, file)
			}
		}

		// Break if no more pages or reached maximum results
		if nextPage == "" || len(files) >= maxResults {
			break
		}
	}

searchComplete:
	// Prepare response with consistent structure
	resp := FileListItemResponse{
		Data: files,
		Meta: FileListMeta{
			NextPage: nextPage,
			PageSize: pageSize,
			Total:    len(files),
		},
	}

	return ginext.NewResponse(http.StatusOK, ginext.WithRawBody(resp)), nil
}

// createClientDriveFolder creates a folder for the client in the sharepoint
// returns the mapping if already exists
func (h *SharepointHandler) createClientDriveFolder(
	ctx context.Context,
	sp *sharepointParams,
	folderName string,
	clientID uint64,
) (*model.DocumentMapping, error) {
	docMapping, err := h.rDocumentMapping.FirstObjectMapping(
		ctx,
		model.DocTypeClient,
		model.DocProviderSharepoint,
		sp.TenantID,
		clientID,
		0,
	)

	if err == nil {
		return docMapping, nil
	}

	if !model.IsNotFound(err) {
		return nil, fmt.Errorf("failed to get client folder mapping: %w", err)
	}

	var driveItem sharepointclient.DriveItemResponse

	// try to search the folder
	searchResp, err := h.spClient.FilterChildren(ctx, sp.SiteDriveID, sp.AccessToken, "root", folderName)
	if err != nil {
		return nil, err
	}

	if searchResp != nil && len(searchResp.Value) > 0 {
		driveItem = *searchResp.Value[0]
	} else {
		driveItem, err = h.spClient.CreateDriveItem(ctx, sp.SiteDriveID, sp.AccessToken, "root", folderName)
		if err != nil {
			return nil, err
		}
	}

	docMapping = &model.DocumentMapping{
		TenantID: sp.TenantID,
		Type:     model.DocTypeClient,
		ObjectID: clientID,
		DriveID:  driveItem.ID,
		Provider: model.DocProviderSharepoint,
	}
	err = h.rDocumentMapping.CreateOrUpdate(ctx, docMapping)
	return docMapping, err
}

func (h *SharepointHandler) createMatterParentFolder(
	ctx context.Context,
	sp *sharepointParams,
	clientDrive *model.DocumentMapping,
) (*model.DocumentMapping, error) {

	log := logger.WithCtx(ctx, "sharepoint.createMatterParentFolder")

	// make sure it doesn't exist
	matterFolderMap, err := h.rDocumentMapping.FirstObjectMapping(
		ctx,
		model.DocTypeParent,
		model.DocProviderSharepoint,
		clientDrive.TenantID,
		0,
		clientDrive.ObjectID,
	)

	if err == nil {
		return matterFolderMap, nil
	}

	if !model.IsNotFound(err) {
		log.Errorf("failed to query matter parent folder mapping: %v", err)
		return nil, err
	}

	matterFolder, err := h.rDocumentSetting.GetValueByKey(ctx, clientDrive.TenantID, model.KeyMappingFolderName)
	if err != nil {
		log.Errorf("failed to get matter parent folder name: %v", err)
		return nil, err
	}

	folderName := matterFolder.Value

	// try to search the folder
	searchResp, err := h.spClient.FilterChildren(ctx, sp.SiteDriveID, sp.AccessToken, clientDrive.DriveID, folderName)
	if err != nil {
		log.Errorf("failed to search matter parent folder: %v", err)
		return nil, err
	}

	var driveItem sharepointclient.DriveItemResponse

	if searchResp != nil && len(searchResp.Value) > 0 {
		driveItem = *searchResp.Value[0]
	} else {
		driveItem, err = h.spClient.CreateDriveItem(ctx, sp.SiteDriveID, sp.AccessToken, clientDrive.DriveID, matterFolder.Value)
		if err != nil {
			log.Errorf("failed to create matter parent folder: %v", err)
			return nil, err
		}
	}

	matterFolderMap = &model.DocumentMapping{
		TenantID:       clientDrive.TenantID,
		Type:           model.DocTypeParent,
		Provider:       model.DocProviderSharepoint,
		ObjectID:       0,
		ParentObjectID: clientDrive.ObjectID,
		ParentDriveID:  clientDrive.DriveID,
		DriveID:        driveItem.ID,
	}
	err = h.rDocumentMapping.Create(ctx, matterFolderMap)

	return matterFolderMap, err
}
