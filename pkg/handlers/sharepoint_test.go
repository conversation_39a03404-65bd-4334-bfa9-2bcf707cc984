package handlers

import (
	"testing"

	"bilabl/docman/domain/model"
	mockr "bilabl/docman/mocks/repositories"
	mockSharepoint "bilabl/docman/mocks/sharepointclient"
	"bilabl/docman/pkg/sharepointclient"
	"context"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

var (
	at = mock.Anything
)

func TestCreateClientFolder(t *testing.T) {
	t.Run("createSuccess", func(t *testing.T) {
		mockDocRepo := mockr.NewMockDocumentMappingRepository(t)
		mockSp := mockSharepoint.NewMockClient(t)
		h := &SharepointHandler{
			rDocumentMapping: mockDocRepo,
			spClient:         mockSp,
		}

		mockDocRepo.On("FirstObjectMapping",
			at,
			model.DocTypeClient,
			model.DocProviderSharepoint,
			uint64(1),
			uint64(1_000),
			uint64(0),
		).Return(nil, gorm.ErrRecordNotFound)

		mockDocRepo.On("CreateOrUpdate", at, at).Return(nil)

		mockSp.On("FilterChildren", at, at, at, at, at).Return(nil, nil)

		testSpParams := &sharepointParams{
			TenantID:    1,
			SiteDriveID: "siteDriveID",
			AccessToken: "accessToken",
		}

		mockSp.On("CreateDriveItem",
			at,
			testSpParams.SiteDriveID,
			testSpParams.AccessToken,
			"root",
			"clientName",
		).Return(sharepointclient.DriveItemResponse{ID: "mock drive id"}, nil)

		// Act
		folder, err := h.createClientDriveFolder(context.Background(),
			testSpParams,
			"clientName",
			uint64(1_000),
		)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "mock drive id", folder.DriveID)
	})

	t.Run("skipCreatingOnExists", func(t *testing.T) {
		mockDocRepo := mockr.NewMockDocumentMappingRepository(t)
		mockSp := mockSharepoint.NewMockClient(t)
		h := &SharepointHandler{
			rDocumentMapping: mockDocRepo,
			spClient:         mockSp,
		}

		mockDocRepo.On("FirstObjectMapping",
			at,
			model.DocTypeClient,
			model.DocProviderSharepoint,
			uint64(1),
			uint64(1_000),
			uint64(0),
		).Return(&model.DocumentMapping{
			DriveID:  "exist-drive-id",
			ObjectID: 1_000,
		}, nil)

		// test data
		testSpParams := &sharepointParams{
			TenantID:    1,
			SiteDriveID: "siteDriveID",
			AccessToken: "accessToken",
		}
		testClientName := "clientName"
		ctx := context.Background()
		m, err := h.createClientDriveFolder(ctx, testSpParams, testClientName, 1_000)
		require.NoError(t, err)
		assert.Equal(t, uint64(1_000), m.ObjectID)
		assert.Equal(t, "exist-drive-id", m.DriveID)
	})
}

func testCreateMatterParentFolderSuccess(t *testing.T) {

	testClientDrive := &model.DocumentMapping{
		TenantID: 1,
		ObjectID: 1_000,
		DriveID:  "test client drive id",
	}
	testSpParams := &sharepointParams{
		TenantID:    1,
		SiteDriveID: "siteDriveID",
		AccessToken: "accessToken",
	}

	mockDocMapRepo := mockr.NewMockDocumentMappingRepository(t)
	mockSp := mockSharepoint.NewMockClient(t)
	mockDocSett := mockr.NewMockDocumentSettingRepository(t)
	h := &SharepointHandler{
		rDocumentMapping: mockDocMapRepo,
		spClient:         mockSp,
		rDocumentSetting: mockDocSett,
	}

	mockSp.On("FilterChildren", at, at, at, at, at).Return(nil, nil)

	mockDocMapRepo.On("FirstObjectMapping",
		at,
		model.DocTypeParent,
		model.DocProviderSharepoint,
		testSpParams.TenantID,
		uint64(0),
		testClientDrive.ObjectID,
	).Return(nil, gorm.ErrRecordNotFound)

	mockDocSett.On("GetValueByKey",
		at,
		testSpParams.TenantID,
		model.KeyMappingFolderName,
	).Return(&model.DocumentSetting{
		Value: "matters folder",
	}, nil)

	mockSp.On("CreateDriveItem",
		at,
		testSpParams.SiteDriveID,
		testSpParams.AccessToken,
		testClientDrive.DriveID,
		"matters folder",
	).Return(sharepointclient.DriveItemResponse{ID: "test new drive id"}, nil)

	mockDocMapRepo.On("Create",
		at,
		&model.DocumentMapping{
			TenantID:       testSpParams.TenantID,
			Type:           model.DocTypeParent,
			ObjectID:       0,
			ParentObjectID: testClientDrive.ObjectID,
			ParentDriveID:  testClientDrive.DriveID,
			DriveID:        "test new drive id",
			Provider:       model.DocProviderSharepoint,
		},
	).Return(nil)

	doc, err := h.createMatterParentFolder(context.Background(), testSpParams, testClientDrive)
	require.NoError(t, err)
	assert.Equal(t, "test new drive id", doc.DriveID)
	assert.Equal(t, uint64(0), doc.ObjectID)
	assert.Equal(t, uint64(1_000), doc.ParentObjectID)
	assert.Equal(t, model.DocTypeParent, doc.Type)
}

func TestCreateMatterParentFolder(t *testing.T) {
	t.Run("createSuccess", testCreateMatterParentFolderSuccess)
}

// Note: Comprehensive integration tests for SearchFiles would require
// setting up ginext.Request mocks which is complex. The fixes have been
// applied to address the identified issues:
// 1. Input validation for keyword (required, length limits)
// 2. Proper error handling with appropriate HTTP status codes
// 3. Nil pointer checks for nested fields
// 4. Context timeout handling with proper loop exit
// 5. Bounds checking for page size
// 6. Consistent response structure
