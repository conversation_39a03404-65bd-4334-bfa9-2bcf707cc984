package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"bilabl/docman/mocks/service/autodoc_mocks"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

func TestAutoDocHandler_ConsumeMatterCreated(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		payload        EventPayload
		setupMocks     func(*autodoc_mocks.MockEventRuleMatchingService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful matter.create event processing",
			payload: EventPayload{
				Topic: "matter.create",
				Body: map[string]interface{}{
					"id":        float64(123),
					"client_id": float64(456),
					"name":      "Test Matter",
					"code":      "MAT001",
					"tenant_id": float64(1),
					"actor_id":  float64(789),
					"no_notify": false,
					"extra": map[string]interface{}{
						"current": map[string]interface{}{
							"name":   "Test Matter",
							"owners": []interface{}{float64(101), float64(102)},
						},
					},
				},
			},
			setupMocks: func(mockService *autodoc_mocks.MockEventRuleMatchingService) {
				mockService.On("ProcessEvent", mock.Anything, uint64(1), "matter.create", mock.Anything).Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "invalid topic",
			payload: EventPayload{
				Topic: "invalid.topic",
				Body: map[string]interface{}{
					"id":        float64(123),
					"tenant_id": float64(1),
				},
			},
			setupMocks:     func(mockService *autodoc_mocks.MockEventRuleMatchingService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid topic: expected 'matter.create'",
		},
		{
			name: "missing tenant_id",
			payload: EventPayload{
				Topic: "matter.create",
				Body: map[string]interface{}{
					"id":   float64(123),
					"name": "Test Matter",
				},
			},
			setupMocks:     func(mockService *autodoc_mocks.MockEventRuleMatchingService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Missing tenant_id in event body",
		},
		{
			name: "missing matter id",
			payload: EventPayload{
				Topic: "matter.create",
				Body: map[string]interface{}{
					"tenant_id": float64(1),
					"name":      "Test Matter",
				},
			},
			setupMocks:     func(mockService *autodoc_mocks.MockEventRuleMatchingService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Missing matter id in event body",
		},
		{
			name: "event processing failure",
			payload: EventPayload{
				Topic: "matter.create",
				Body: map[string]interface{}{
					"id":        float64(123),
					"client_id": float64(456),
					"name":      "Test Matter",
					"tenant_id": float64(1),
					"extra": map[string]interface{}{
						"current": map[string]interface{}{
							"name": "Test Matter",
						},
					},
				},
			},
			setupMocks: func(mockService *autodoc_mocks.MockEventRuleMatchingService) {
				mockService.On("ProcessEvent", mock.Anything, uint64(1), "matter.create", mock.Anything).Return(assert.AnError)
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "Failed to process event:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)
			tt.setupMocks(mockEventRuleMatchingService)

			// Create handler
			handler := &AutoDocHandler{
				eventRuleMatchingService: mockEventRuleMatchingService,
			}

			// Create request
			payloadBytes, err := json.Marshal(tt.payload)
			assert.NoError(t, err)

			req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/matter/created", bytes.NewReader(payloadBytes))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Create ginext request
			ginextReq := &ginext.Request{
				GinCtx: c,
			}

			// Call handler
			resp, err := handler.ConsumeMatterCreated(ginextReq)

			// Verify response
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				if resp.Code == http.StatusOK {
					// ginext.Response.Body is a GeneralBody with Data field
					generalBody := resp.Body.(*ginext.GeneralBody)
					responseData := generalBody.Data.(map[string]interface{})
					assert.Equal(t, "success", responseData["status"])
					assert.Equal(t, "Matter created event processed successfully", responseData["message"])
					assert.Equal(t, uint64(123), responseData["matter_id"])
					assert.Equal(t, uint64(1), responseData["tenant_id"])
				}
			}

			// Verify mock expectations
			mockEventRuleMatchingService.AssertExpectations(t)
		})
	}
}

func TestAutoDocHandler_ConsumeClientUpdated(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		payload        EventPayload
		setupMocks     func(*autodoc_mocks.MockEventRuleMatchingService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful client.update event processing",
			payload: EventPayload{
				Topic: "client.update",
				Body: map[string]interface{}{
					"id":         float64(10219),
					"name":       "acclime Vietnam",
					"short_name": "ACCLIME",
					"code":       "C06855",
					"tenant_id":  float64(1),
					"extra": map[string]interface{}{
						"current": map[string]interface{}{
							"name":       "acclime Vietnam",
							"owners":     []interface{}{float64(23)},
							"stage_name": "Initial",
							"stage_text": "initial",
						},
						"old": map[string]interface{}{
							"stage_name": "Lead",
							"stage_text": "lead",
						},
					},
				},
			},
			setupMocks: func(mockService *autodoc_mocks.MockEventRuleMatchingService) {
				mockService.On("ProcessEvent", mock.Anything, uint64(1), "client.update", mock.Anything).Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "invalid topic",
			payload: EventPayload{
				Topic: "invalid.topic",
				Body: map[string]interface{}{
					"id":        float64(123),
					"tenant_id": float64(1),
				},
			},
			setupMocks:     func(mockService *autodoc_mocks.MockEventRuleMatchingService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid topic: expected 'client.update'",
		},
		{
			name: "event processing failure",
			payload: EventPayload{
				Topic: "client.update",
				Body: map[string]interface{}{
					"id":        float64(123),
					"name":      "Test Client",
					"tenant_id": float64(1),
					"extra": map[string]interface{}{
						"current": map[string]interface{}{
							"name": "Test Client",
						},
					},
				},
			},
			setupMocks: func(mockService *autodoc_mocks.MockEventRuleMatchingService) {
				mockService.On("ProcessEvent", mock.Anything, uint64(1), "client.update", mock.Anything).Return(assert.AnError)
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "Failed to process event:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)
			tt.setupMocks(mockEventRuleMatchingService)

			// Create handler
			handler := &AutoDocHandler{
				eventRuleMatchingService: mockEventRuleMatchingService,
			}

			// Create request
			payloadBytes, err := json.Marshal(tt.payload)
			assert.NoError(t, err)

			req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/client/updated", bytes.NewReader(payloadBytes))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Create ginext request
			ginextReq := &ginext.Request{
				GinCtx: c,
			}

			// Call handler
			resp, err := handler.ConsumeClientUpdated(ginextReq)

			// Verify response
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				if resp.Code == http.StatusOK {
					// ginext.Response.Body is a GeneralBody with Data field
					generalBody := resp.Body.(*ginext.GeneralBody)
					responseData := generalBody.Data.(map[string]interface{})
					assert.Equal(t, "success", responseData["status"])
					assert.Equal(t, "Client updated event processed successfully", responseData["message"])
					assert.Equal(t, uint64(10219), responseData["client_id"])
					assert.Equal(t, uint64(1), responseData["tenant_id"])
				}
			}

			// Verify mock expectations
			mockEventRuleMatchingService.AssertExpectations(t)
		})
	}
}

func TestAutoDocHandler_ConsumeClientCreated(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test successful client.create event processing
	payload := EventPayload{
		Topic: "client.create",
		Body: map[string]interface{}{
			"id":         float64(565),
			"name":       "Client Sharepoint Sample",
			"short_name": "CSS",
			"code":       "C06855",
			"tenant_id":  float64(1),
			"extra": map[string]interface{}{
				"current": map[string]interface{}{
					"name":       "Client Sharepoint Sample",
					"owners":     []interface{}{float64(1)},
					"stage":      float64(256),
					"stage_name": "Initial",
				},
			},
		},
	}

	// Setup mocks
	mockEventRuleMatchingService := autodoc_mocks.NewMockEventRuleMatchingService(t)
	mockEventRuleMatchingService.On("ProcessEvent", mock.Anything, uint64(1), "client.create", mock.Anything).Return(nil)

	// Create handler
	handler := &AutoDocHandler{
		eventRuleMatchingService: mockEventRuleMatchingService,
	}

	// Create request
	payloadBytes, err := json.Marshal(payload)
	assert.NoError(t, err)

	req := httptest.NewRequest(http.MethodPost, "/internal/consume/autodoc/client/created", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Create ginext request
	ginextReq := &ginext.Request{
		GinCtx: c,
	}

	// Call handler
	resp, err := handler.ConsumeClientCreated(ginextReq)

	// Verify response
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// ginext.Response.Body is a GeneralBody with Data field
	generalBody := resp.Body.(*ginext.GeneralBody)
	responseData := generalBody.Data.(map[string]interface{})
	assert.Equal(t, "success", responseData["status"])
	assert.Equal(t, "Client created event processed successfully", responseData["message"])
	assert.Equal(t, uint64(565), responseData["client_id"])
	assert.Equal(t, uint64(1), responseData["tenant_id"])

	// Verify mock expectations
	mockEventRuleMatchingService.AssertExpectations(t)
}
