package handlers

import (
	"bilabl/docman/domain"
	"bilabl/docman/domain/model"
	"bilabl/docman/domain/share"
	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/internal/service/document"
	"bilabl/docman/pkg/bilabllog"
	"bilabl/docman/pkg/helper"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/transport"
	"net/http"
	"path"
	"strconv"
	"strings"
	"time"

	"code.mybil.net/gophers/gokit/clients/glob"
	"code.mybil.net/gophers/gokit/clients/settings"
	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/domain/actor"

	"gitlab.com/goxp/cloud0/ginext"
)

type DocumentHandler struct {
	rDocument       repositories.DocumentRepository
	rShare          share.Repository
	glob            transport.Glob
	hrglassid       transport.Hrglassid
	consumer        transport.Consumer
	fetcher         entity.GenericFetcher
	settLoader      settings.Client
	globClient      glob.Client
	autoDocService  autodoc.AutoDocService
	documentService document.DocumentService
}

type BodyMeta map[string]interface{}

type rspDocumentV1 struct {
	ID          uint64  `json:"id" example:"1"`
	ParentID    uint64  `json:"parent_id"`
	Name        string  `json:"name" example:"Document name"`
	ObjectID    uint64  `json:"object_id" example:"1"`
	ObjectType  int     `json:"object_type" example:"1"`
	SubObjectID uint64  `json:"sub_object_id"`
	Key         string  `json:"key" example:"document" `
	Type        int     `json:"type"`
	DocType     int     `json:"doc_type" example:"2"`
	Status      int     `json:"status" example:"1"`
	Note        string  `json:"note" example:"This is secure"`
	CreatedUser uint64  `json:"created_user"`
	UpdatedUser uint64  `json:"updated_user"`
	CreatedAt   string  `json:"created_at" example:"2022-07-25T23:47:09+07:00"`
	UpdatedAt   string  `json:"updated_at" example:"2022-07-25T23:47:09+07:00"`
	WaiverWith  []int64 `json:"waiver_with" gorm:"type:integer[]"`
	Size        int64   `json:"size"`
}

type rspDocumentV3 struct {
	ID                      uint64 `json:"id"`
	Name                    string `json:"name"`
	ParentID                uint64 `json:"parent_id"`
	DocType                 int    `json:"doc_type"`
	ObjectType              int    `json:"object_type"`
	ObjectID                uint64 `json:"object_id"`
	SubObjectID             uint64 `json:"sub_object_id"`
	IsFile                  bool   `json:"is_file"`
	IsInternal              bool   `json:"is_internal"`
	Size                    int64  `json:"size"`
	Type                    string `json:"type"`
	InternalType            int    `json:"internal_type"`
	DateCreated             string `json:"date_created"`
	DateModified            string `json:"date_modified"`
	LastModifiedBy          string `json:"last_modified_by"`
	LastModifiedByID        uint64 `json:"last_modified_by_id"`
	LastModifiedByEmail     string `json:"last_modified_by_email"`
	LastModifiedByAvatarURL string `json:"last_modified_by_avatar_url"`
	WebURL                  string `json:"web_url"`
	Status                  int    `json:"status"`
}

type rspListDocumentV3 struct {
	Cwd  rspDocumentV3   `json:"cwd"`
	Data []rspDocumentV3 `json:"data"`
	Meta BodyMeta        `json:"meta"`
}

type documentDetailParams struct {
	DocumentID uint64 `uri:"document_id" validate:"required,min=1"`
}

type rspCreateDocumentBody struct {
	ID uint64 `json:"id"`
}

type rspUpdateDocumentBody struct{}

type rspDeleteDocumentBody struct{}

type DocumentResponse struct {
	Path            any    `json:"path"`
	Action          any    `json:"action"`
	NewName         any    `json:"newName"`
	Names           any    `json:"names"`
	Name            string `json:"name" example:"Matter Folders"`
	Size            int64  `json:"size"`
	WebURL          string `json:"webUrl"`
	PreviousName    any    `json:"previousName"`
	DateModified    string `json:"dateModified" example:"2022-09-17T04:13:47.1140441+00:00"`
	DateCreated     string `json:"dateCreated" example:"2022-09-17T04:13:47.1140441+00:00"`
	LastModifiedBy  string `json:"lastModifiedBy"`
	HasChild        bool   `json:"hasChild"`
	IsFile          bool   `json:"isFile"`
	IsInternal      bool   `json:"isInternal"`
	Type            string `json:"type"`
	ID              any    `json:"id"`
	DocID           uint64 `json:"docId"`
	ParentID        uint64 `json:"parentId"`
	ObjectID        uint64 `json:"objectId"`
	ObjectType      int    `json:"objectType"`
	SubObjectID     uint64 `json:"subObjectId"`
	Status          int    `json:"status" example:"1"`
	InternalType    int    `json:"internalType"`
	FilterPath      string `json:"filterPath"`
	FilterId        any    `json:"filterId"`
	TargetPath      any    `json:"targetPath"`
	RenameFiles     any    `json:"renameFiles"`
	UploadFiles     any    `json:"uploadFiles"`
	CaseSensitive   bool   `json:"caseSensitive"`
	SearchString    any    `json:"searchString"`
	ShowHiddenItems bool   `json:"showHiddenItems"`
	Data            any    `json:"data"`
	TargetData      any    `json:"targetData"`
	Permission      any    `json:"permission"`
}

type DocumentListResponse struct {
	Cwd     DocumentResponse   `json:"cwd"`
	Files   []DocumentResponse `json:"files"`
	Error   any                `json:"error"`
	Details any                `json:"details"`
}

type DocumentRequest struct {
	DocumentResponse
	FmId        string `json:"_fm_id"`
	FmIcon      string `json:"_fm_icon"`
	FmIconClass string `json:"_fm_iconClass"`
	FmHtmlAttr  any    `json:"_fm_htmlAttr"`
	FmSelected  bool   `json:"_fm_selected"`
	FmExpanded  bool   `json:"_fm_expanded"`
	FmCreated   string `json:"_fm_created"`
	FmModified  string `json:"_fm_modified"`
	FmPId       string `json:"_fm_pId"`
}

type DocumentListRequest struct {
	Action          string            `json:"action"`
	Path            string            `json:"path"`
	SearchString    string            `json:"searchString"`
	Name            string            `json:"name"`
	NewName         string            `json:"newName"`
	ShowHiddenItems bool              `json:"showHiddenItems"`
	Data            []DocumentRequest `json:"data"`
	TargetData      DocumentRequest   `json:"targetData"`
	TargetPath      string            `json:"targetPath"`
}

type DocumentErrorMessage struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type DocumentErrorResponse struct {
	Error DocumentErrorMessage `json:"error"`
}

func NewDocumentHandler(
	rDocumentRepo repositories.DocumentRepository,
	glob transport.Glob,
	hrglassid transport.Hrglassid,
	consumer transport.Consumer,
	fetcher entity.GenericFetcher,
	settLoader settings.Client,
	globClient glob.Client,
	autoDocService autodoc.AutoDocService,
	documentService document.DocumentService,
) *DocumentHandler {
	return &DocumentHandler{
		rDocument:       rDocumentRepo,
		glob:            glob,
		hrglassid:       hrglassid,
		consumer:        consumer,
		fetcher:         fetcher,
		settLoader:      settLoader,
		globClient:      globClient,
		autoDocService:  autoDocService,
		documentService: documentService,
	}
}

func validType(objectType string) bool {
	switch objectType {
	case
		"client",
		"matter",
		"staff":
		return true
	}
	return false
}

func typeValue(objectType string) int {
	switch objectType {
	case "client":
		return 1
	case "matter":
		return 3
	case "staff":
		return 2
	}
	return 0
}

func DocumentsResponse(inputs []*model.Document) []*rspDocumentV1 {
	output := make([]*rspDocumentV1, 0)
	for _, v := range inputs {
		output = append(output, SingleDocumentResponse(v))
	}
	return output
}

func SingleDocumentResponse(input *model.Document) *rspDocumentV1 {
	if input == nil {
		return nil
	}
	return &rspDocumentV1{
		ID:          input.ID,
		ParentID:    input.ParentID,
		ObjectID:    input.ObjectID,
		ObjectType:  input.ObjectType,
		SubObjectID: input.SubObjectID,
		DocType:     input.DocType,
		Status:      input.Status,
		Name:        input.Name,
		Type:        input.Type,
		Key:         input.Key,
		Note:        input.Note,
		CreatedUser: input.CreatedUser,
		UpdatedUser: input.UpdatedUser,
		WaiverWith:  input.WaiverWith,
		CreatedAt:   helper.TimeToString(input.CreatedAt),
		UpdatedAt:   helper.TimeToString(input.CreatedAt),
		Size:        input.Size,
	}
}

func ActivityEvent(d *model.Document, actionType transport.ActionType, action transport.ActivityAction, activityType uint, actor uint64, extra transport.EventExtraData) transport.GeneralEventMessage {
	var objectType uint

	switch d.ObjectType {
	case transport.DocumentObjectTypeClient:
		objectType = transport.ActivityObjectTypeClient
	case transport.DocumentObjectTypeStaff:
		objectType = transport.ActivityObjectTypeStaff
	case transport.DocumentObjectTypeMatter:
		objectType = transport.ActivityObjectTypeMatter
	case transport.DocumentObjectTypeTask:
		objectType = transport.ActivityObjectTypeTask
	}

	eventDat := transport.GeneralEventMessage{
		ObjectType:    objectType,
		ObjectID:      d.ObjectID,
		SubObjectType: objectType,
		SubObjectID:   d.ObjectID,
		ActionType:    actionType,
		Action:        action,
		Type:          activityType,
		ActorID:       actor,
		TenantID:      d.TenantID,
		Extra:         extra,
	}

	return eventDat
}

// @TODO: To be removed
func (h *DocumentHandler) Get(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	req := new(documentDetailParams)
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document, err := h.rDocument.FindOne(r.Context(), queryBuild)
	r.MustNoError(err)

	// Get file size
	// Get file size
	fileSize, _ := h.glob.GetObjectSize(r.Context(), document.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
	document.Size = fileSize.Data.Size

	return ginext.NewResponseData(http.StatusOK, SingleDocumentResponse(document)), nil
}

func (h *DocumentHandler) GetV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	req := new(documentDetailParams)
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document, err := h.rDocument.FindOne(r.Context(), queryBuild)
	r.MustNoError(err)

	// Get file size
	fileSize, _ := h.glob.GetObjectSize(r.Context(), document.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
	document.Size = fileSize.Data.Size

	return ginext.NewResponseData(http.StatusOK, SingleDocumentResponse(document)), nil
}

func (h *DocumentHandler) ConsumeCrud(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeDocumentReq
	r.MustBind(&body)

	log.Debug("getting document crud event with topic " + body.Topic)

	if body.Topic != "docman.crud.document" {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	if body.Body.Action <= 0 || body.Body.Action > 3 {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	filters := []*model.Filter{
		{
			Key:    "object_id",
			Value:  body.Body.Data.ObjectID,
			Method: "=",
		},
		{
			Key:    "sub_object_id",
			Value:  body.Body.Data.SubObjectID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  body.Body.Data.TenantID,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	// constant.ACTION_CREATE: 1
	// constant.ACTION_UPDATE: 2
	if body.Body.Action == 1 || body.Body.Action == 2 {
		document := &model.Document{
			Name:        body.Body.Data.Name,
			TenantID:    body.Body.Data.TenantID,
			ObjectType:  body.Body.Data.ObjectType,
			ObjectID:    body.Body.Data.ObjectID,
			SubObjectID: body.Body.Data.SubObjectID,
			Type:        1,
			DocType:     body.Body.Data.DocType,
			CreatedUser: body.Body.Data.CreatedUser,
			UpdatedUser: body.Body.Data.CreatedUser,
			Model: model.Model{
				CreatedAt: time.Now(),
			},
		}

		err := h.rDocument.CreateOrUpdate(r.Context(), queryBuild, document)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
	}

	// constant.ACTION_DELETE: 3
	if body.Body.Action == 3 {
		err := h.rDocument.DeleteOne(r.Context(), queryBuild)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *DocumentHandler) ConsumerBillingCostAttach(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeBillingCostReq
	r.MustBind(&body)

	log.Debug("getting billing cost attach event with topic " + body.Topic)

	if body.Topic != "billing.cost.attach" {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	filters := []*model.Filter{
		{
			Key:    "object_id",
			Value:  body.Body.BillingID,
			Method: "=",
		},
		{
			Key:    "sub_object_id",
			Value:  body.Body.CostID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  body.Body.TenantID,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  body.Body.Key,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document := &model.Document{
		Name:        body.Body.Name,
		TenantID:    body.Body.TenantID,
		ObjectType:  6, // cost
		ObjectID:    body.Body.BillingID,
		Key:         body.Body.Key,
		SubObjectID: body.Body.CostID,
		DocType:     2, // file
		CreatedUser: body.Body.UserID,
		UpdatedUser: body.Body.UserID,
		Model: model.Model{
			CreatedAt: time.Now(),
		},
	}
	err := h.rDocument.CreateOrUpdate(r.Context(), queryBuild, document)
	if err != nil {
		log.Errorf("server error when attaching cost file: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *DocumentHandler) ConsumerBillingCostDeattach(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeBillingCostReq
	r.MustBind(&body)

	log.Debug("getting billing cost deattach event with topic " + body.Topic)

	if body.Topic != "billing.cost.deattach" {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	filters := []*model.Filter{
		{
			Key:    "object_id",
			Value:  body.Body.BillingID,
			Method: "=",
		},
		{
			Key:    "sub_object_id",
			Value:  body.Body.CostID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  body.Body.TenantID,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  body.Body.Key,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	err := h.rDocument.DeleteOne(r.Context(), queryBuild)
	if err != nil {
		log.Errorf("server error when deattaching cost file: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *DocumentHandler) ConsumerBillingPaymentAttach(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeBillingPaymentReq
	r.MustBind(&body)

	log.Debug("getting billing payment attach event with topic " + body.Topic)

	if body.Topic != "billing.payment.attach" {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	filters := []*model.Filter{
		{
			Key:    "object_id",
			Value:  body.Body.BillingID,
			Method: "=",
		},
		{
			Key:    "sub_object_id",
			Value:  body.Body.PaymentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  body.Body.TenantID,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  body.Body.Key,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document := &model.Document{
		Name:        body.Body.Name,
		TenantID:    body.Body.TenantID,
		ObjectType:  8, // payment
		ObjectID:    body.Body.BillingID,
		Key:         body.Body.Key,
		SubObjectID: body.Body.PaymentID,
		DocType:     2, // file
		CreatedUser: body.Body.UserID,
		UpdatedUser: body.Body.UserID,
		Model: model.Model{
			CreatedAt: time.Now(),
		},
	}
	err := h.rDocument.CreateOrUpdate(r.Context(), queryBuild, document)
	if err != nil {
		log.Errorf("server error when attaching payment file: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *DocumentHandler) ConsumerBillingPaymentDeattach(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var body model.ConsumeBillingPaymentReq
	r.MustBind(&body)

	log.Debug("getting billing payment deattach event with topic " + body.Topic)

	if body.Topic != "billing.payment.deattach" {
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	filters := []*model.Filter{
		{
			Key:    "object_id",
			Value:  body.Body.BillingID,
			Method: "=",
		},
		{
			Key:    "sub_object_id",
			Value:  body.Body.PaymentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  body.Body.TenantID,
			Method: "=",
		},
		{
			Key:    "key",
			Value:  body.Body.Key,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	err := h.rDocument.DeleteOne(r.Context(), queryBuild)
	if err != nil {
		log.Errorf("server error when deattaching payment file: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

// @TODO: To be removed
func (h *DocumentHandler) CreateV1(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	var err error
	var body model.CreateDocumentReq
	r.MustBind(&body)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	var size int64
	if body.Key != "" {
		fileSize, _ := h.glob.GetObjectSize(r.Context(), body.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
		size = fileSize.Data.Size
	} else {
		size = 0
	}

	document := &model.Document{
		ParentID:    body.ParentID,
		Name:        body.Name,
		TenantID:    tenantID,
		ObjectType:  body.ObjectType,
		SubObjectID: body.SubObjectID,
		WaiverWith:  body.WaiverWith,
		Key:         body.Key,
		Note:        body.Note,
		CreatedUser: userID,
		UpdatedUser: userID,
		Status:      body.Status,
		Type:        body.Type,
		DocType:     body.DocType,
		ObjectID:    body.ObjectID,
		Model: model.Model{
			CreatedAt: time.Now(),
		},
		Size: size,
	}

	err = h.rDocument.CreateDoc(r.Context(), document)
	if err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	// trigger create document activity
	//var docType string
	//if body.DocType == 1 {
	//	docType = "folder"
	//} else {
	//	docType = "document"
	//}
	//extra := transport.EventExtraData{
	//	ID:      document.ID,
	//	Name:    document.Name,
	//	Key:     document.Key,
	//	DocType: docType,
	//}
	//eventDat := ActivityEvent(document, transport.ActionTypeCreate, transport.ActionAdd, transport.ActivityTypeDocument, document.CreatedUser, extra)
	//h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)

	return ginext.NewResponseData(http.StatusCreated, &rspCreateDocumentBody{ID: document.ID}), nil
}

func (h *DocumentHandler) CreateV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())

	// Parse HTTP request
	var body model.CreateDocumentReq
	r.MustBind(&body)

	// Extract HTTP context data
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	// Create service request
	req := &document.CreateDocumentRequest{
		TenantID:    tenantID,
		UserID:      userID,
		Name:        body.Name,
		DocType:     body.DocType,
		Key:         body.Key,
		ParentID:    body.ParentID,
		ParentPath:  body.ParentPath,
		ObjectType:  body.ObjectType,
		ObjectID:    body.ObjectID,
		SubObjectID: body.SubObjectID,
		AutoDocRoot: body.AutoDocRoot,
		Status:      body.Status,
		Type:        body.Type,
		Note:        body.Note,
		WaiverWith:  body.WaiverWith,
	}

	// Call service layer for business logic
	resp, err := h.documentService.CreateDocument(r.Context(), req)
	if err != nil {
		// Map service errors to HTTP errors
		if strings.Contains(err.Error(), "doc_type must be") {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
		if strings.Contains(err.Error(), "key is required") {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
		if strings.Contains(err.Error(), "already exists in this location") {
			return nil, ginext.NewError(http.StatusConflict, err.Error())
		}
		return nil, ginext.NewError(http.StatusBadRequest, err.Error())
	}

	document := resp.Document

	// trigger create document activity
	if !body.DisableActivity {
		var docType string
		if body.DocType == 1 {
			docType = "folder"
		} else {
			docType = "document"
		}
		extra := transport.EventExtraData{
			ID:      document.ID,
			Name:    document.Name,
			Key:     document.Key,
			DocType: docType,
		}
		eventDat := ActivityEvent(document, transport.ActionTypeCreate, transport.ActionAdd, transport.ActivityTypeDocument, document.CreatedUser, extra)
		h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)
	}

	return ginext.NewResponseData(http.StatusCreated, &rspCreateDocumentBody{ID: document.ID}), nil
}

func (h *DocumentHandler) createListDocumentV1Query(r *ginext.Request) *model.Query {

	query := &domain.ListDocumentsFilter{}
	// Get pagination filter
	r.MustBind(query)

	// Get tenant id in header
	tenant_id := ginext.Uint64TenantID(r.GinCtx)
	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  tenant_id,
			Method: "=",
		},
	}

	if query.ObjectID != "" {
		filters = append(filters, &model.Filter{
			Key:    "object_id",
			Method: "=",
			Value:  query.ObjectID,
		})
	}
	if query.ObjectType != "" {
		filters = append(filters, &model.Filter{
			Key:    "object_type",
			Method: "IN",
			Value:  helper.Str2Uint64Slice(query.ObjectType),
		})
	}
	if query.Key != "" {
		filters = append(filters, &model.Filter{
			Key:    "key",
			Method: "=",
			Value:  query.Key,
		})
	}
	if query.Status != "" {
		filters = append(filters, &model.Filter{
			Key:    "status",
			Method: "IN",
			Value:  helper.Str2Uint64Slice(query.Status),
		})
	}

	queryBuild := helper.BuildQuery(query.Search, filters, nil, nil)
	return queryBuild
}

// @TODO: To be removed
func (h *DocumentHandler) ListDocumentV1(r *ginext.Request) (*ginext.Response, error) {
	queryBuild := h.createListDocumentV1Query(r)
	pager := ginext.NewPagerWithGinCtx(r.GinCtx)

	items, err := h.rDocument.Find(r.Context(), queryBuild, pager)
	r.MustNoError(err)

	var documents []rspDocumentV1
	for _, item := range items {
		// Get file size
		fileSize, _ := h.glob.GetObjectSize(r.Context(), item.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))

		document := rspDocumentV1{
			ID:          item.ID,
			ParentID:    item.ParentID,
			Name:        item.Name,
			ObjectID:    item.ObjectID,
			ObjectType:  item.ObjectType,
			SubObjectID: item.SubObjectID,
			Key:         item.Key,
			Type:        item.Type,
			DocType:     item.DocType,
			Status:      item.Status,
			Note:        item.Note,
			CreatedUser: item.CreatedUser,
			UpdatedUser: item.UpdatedUser,
			WaiverWith:  item.WaiverWith,
			CreatedAt:   item.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   item.UpdatedAt.Format(time.RFC3339),
			Size:        fileSize.Data.Size,
		}
		documents = append(documents, document)
	}

	resp := ginext.NewResponseWithPager(
		http.StatusOK,
		documents,
		pager,
	)

	return resp, nil
}

func (h *DocumentHandler) ListDocumentV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	query := &domain.ListDocumentsV3Filter{}
	r.MustBind(query)

	act := actor.FromGinCtx(r.GinCtx)

	// Handle AutoDocRoot request - ensure root doc exists and set parentID for filtering
	if query.AutoDocRoot {
		// Ensure AutoDocRoot exists for the tenant
		autoDocRoot, err := h.autoDocService.EnsureAutoDocRoot(r.Context(), act.TenantID)
		if err != nil {
			return nil, ginext.NewError(http.StatusInternalServerError, "Failed to ensure AutoDocRoot")
		}

		// If object_id and object_type are empty, use AutoDocRoot as parent for filtering
		if query.ObjectID == "" && query.ObjectType == "" {
			query.ObjectID = strconv.FormatUint(autoDocRoot.ObjectID, 10)
			query.ObjectType = strconv.Itoa(autoDocRoot.ObjectType)
			// Set ID to AutoDocRoot ID so it uses the existing logic to fetch children
			query.ID = autoDocRoot.ID
		}
	}

	// Initial filter query
	filters := []*model.Filter{
		{
			Key:    "tenant_id",
			Value:  act.TenantID,
			Method: "=",
		},
	}

	// Filter by status
	if query.Status != "" {
		filters = append(filters, &model.Filter{
			Key:    "status",
			Method: "IN",
			Value:  helper.Str2Uint64Slice(query.Status),
		})
	}

	var cwd rspDocumentV3
	// Build query for sub-folder or matter folder
	if query.ID != 0 {
		qfilters := []*model.Filter{
			{
				Key:    "id",
				Method: "=",
				Value:  query.ID,
			},
			{
				Key:    "tenant_id",
				Method: "=",
				Value:  act.TenantID,
			},
		}
		qBuild := helper.BuildQuery("", qfilters, nil, nil)
		doc, err := h.rDocument.FindOne(r.Context(), qBuild)
		r.MustNoError(err)

		var fileSize transport.GlobSizeResponse
		if doc.DocType == 2 {
			fileSize, err = h.glob.GetObjectSize(r.Context(), doc.Key, strconv.FormatUint(act.ID, 10))
			if err != nil {
				log.WithError(err).Errorf("Failed to get object size for key=%s", doc.Key)
			}
		}

		user, err := h.hrglassid.GetTeamListUsersByID(r.Context(), doc.UpdatedUser, act.TenantID)
		if err != nil {
			log.WithError(err).Errorf("Failed to get user info for id=%d", doc.UpdatedUser)
		}

		// Get userName
		var userName string
		var email string
		var avatarUrl string
		if len(user.Data) > 0 {
			firstUser := user.Data[0]
			userName = firstUser.Name
			email = firstUser.Email
			avatarUrl = firstUser.AvatarUrl
		} else {
			userName = ""
			email = ""
			avatarUrl = ""
		}

		cwd = rspDocumentV3{
			ID:                      doc.ID,
			Name:                    doc.Name,
			ParentID:                doc.ParentID,
			DocType:                 doc.DocType,
			ObjectID:                doc.ObjectID,
			ObjectType:              doc.ObjectType,
			SubObjectID:             doc.SubObjectID,
			IsFile:                  doc.DocType == 2,
			IsInternal:              true,
			InternalType:            doc.Type,
			Type:                    strings.ToLower(path.Ext(doc.Key)),
			Size:                    fileSize.Data.Size,
			DateCreated:             doc.CreatedAt.Format(time.RFC3339),
			DateModified:            doc.UpdatedAt.Format(time.RFC3339),
			LastModifiedBy:          userName,
			LastModifiedByID:        doc.UpdatedUser,
			LastModifiedByEmail:     email,
			LastModifiedByAvatarURL: avatarUrl,
			WebURL:                  doc.Key,
			Status:                  doc.Status,
		}

		// If fetch child item of matter folder, use object_id and object_type to query children
		if doc.SubObjectID != 0 {
			filters = append(filters, &model.Filter{
				Key:    "object_id",
				Method: "=",
				Value:  doc.SubObjectID,
			})
			filters = append(filters, &model.Filter{
				Key:    "object_type",
				Method: "=",
				Value:  3,
			})
		} else {
			// sub folder, just use the parent id to query children
			filters = append(filters, &model.Filter{
				Key:    "parent_id",
				Method: "=",
				Value:  doc.ID,
			})
		}
	} else {
		// Fetch root folder
		if query.ObjectID != "" {
			filters = append(filters, &model.Filter{
				Key:    "object_id",
				Method: "=",
				Value:  query.ObjectID,
			})
		}

		filters = append(filters, &model.Filter{
			Key:    "object_type",
			Method: "=",
			Value:  typeValue(query.ObjectType),
		})
		filters = append(filters, &model.Filter{
			Key:    "parent_id",
			Method: "=",
			Value:  0,
		})

		objectID, _ := strconv.ParseUint(query.ObjectID, 10, 64)
		cwd = rspDocumentV3{
			Name:       "Documents",
			ParentID:   0,
			ObjectID:   objectID,
			ObjectType: typeValue(query.ObjectType),
			IsInternal: true,
		}
	}

	queryBuild := helper.BuildQuery(query.Search, filters, nil, nil)
	pager := ginext.NewPagerWithGinCtx(r.GinCtx)

	items, err := h.rDocument.Find(r.Context(), queryBuild, pager)
	r.MustNoError(err)

	var documents []rspDocumentV3
	for _, item := range items {
		// Get file size
		var fileSize transport.GlobSizeResponse
		if item.DocType == 2 {
			fileSize, _ = h.glob.GetObjectSize(r.Context(), item.Key, strconv.FormatUint(act.ID, 10))
		}

		user, _ := h.hrglassid.GetTeamListUsersByID(r.Context(), item.UpdatedUser, act.TenantID)
		// Get userName
		var userName string
		var email string
		var avatarUrl string
		if len(user.Data) > 0 {
			firstUser := user.Data[0]
			userName = firstUser.Name
			email = firstUser.Email
			avatarUrl = firstUser.AvatarUrl
		} else {
			userName = ""
			email = ""
			avatarUrl = ""
		}

		document := rspDocumentV3{
			ID:                      item.ID,
			Name:                    item.Name,
			ParentID:                item.ParentID,
			DocType:                 item.DocType,
			ObjectID:                item.ObjectID,
			ObjectType:              item.ObjectType,
			SubObjectID:             item.SubObjectID,
			IsFile:                  item.DocType == 2,
			IsInternal:              true,
			InternalType:            item.Type,
			Type:                    strings.ToLower(path.Ext(item.Key)),
			Size:                    fileSize.Data.Size,
			DateCreated:             item.CreatedAt.Format(time.RFC3339),
			DateModified:            item.UpdatedAt.Format(time.RFC3339),
			LastModifiedBy:          userName,
			LastModifiedByID:        item.UpdatedUser,
			LastModifiedByEmail:     email,
			LastModifiedByAvatarURL: avatarUrl,
			WebURL:                  item.Key,
			Status:                  item.Status,
		}
		documents = append(documents, document)
	}

	// Response format
	resp := rspListDocumentV3{
		Cwd:  cwd,
		Data: documents,
		Meta: BodyMeta{
			"page":        pager.GetPage(),
			"total_pages": pager.GetTotalPages(),
			"page_size":   pager.GetPageSize(),
			"total":       pager.TotalRows,
			"pages":       pager.GetTotalPages(),
		},
	}

	if r.Query("datanested") == "true" {
		return ginext.NewResponseWithPager(http.StatusOK, resp, pager), nil
	}

	return ginext.NewResponse(http.StatusOK, ginext.WithRawBody(resp)), nil
}

// @TODO: To be removed
func (h *DocumentHandler) Update(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	payload := &model.UpdateDocumentReq{}
	req := new(documentDetailParams)

	// Get pagination filter
	r.MustBind(payload)
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}

	document := &model.Document{}
	queryBuild := helper.BuildQuery("", filters, nil, nil)

	if payload.ParentID != 0 {
		document.ParentID = payload.ParentID
	}
	if payload.Name != "" {
		document.Name = payload.Name
	}
	if payload.Type != 0 {
		document.Type = payload.Type
	}
	if payload.Status != 0 {
		document.Status = payload.Status
	}
	if payload.Note != "" {
		document.Note = payload.Note
	}
	if payload.ObjectType != 0 {
		document.ObjectType = payload.ObjectType
	}
	if userID != 0 {
		document.UpdatedUser = userID
	}
	if payload.Size != 0 {
		document.Size = payload.Size
	}

	err := h.rDocument.UpdateOne(r.Context(), queryBuild, document)
	doc, _ := h.rDocument.FindOne(r.Context(), queryBuild)
	r.MustNoError(err)

	// trigger update document activity
	var docType string
	if doc.DocType == 1 {
		docType = "folder"
	} else {
		docType = "document"
	}
	extra := transport.EventExtraData{
		ID:      doc.ID,
		Name:    doc.Name,
		Key:     doc.Key,
		DocType: docType,
	}
	eventDat := ActivityEvent(document, transport.ActionTypeUpdate, transport.ActionUpdate, transport.ActivityTypeDocument, document.UpdatedUser, extra)
	_ = eventDat
	//h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)

	resp := ginext.NewResponseData(
		http.StatusNoContent,
		SingleDocumentResponse(doc),
	)
	return resp, nil
}

func (h *DocumentHandler) UpdateV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	payload := &model.UpdateDocumentV3Req{}
	req := new(documentDetailParams)

	// Get pagination filter
	r.MustBind(payload)
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}

	document := &model.Document{}
	queryBuild := helper.BuildQuery("", filters, nil, nil)

	if payload.Name != "" {
		document.Name = payload.Name
	}
	if payload.Key != "" {
		document.Key = payload.Key
	}
	if userID != 0 {
		document.UpdatedUser = userID
	}

	// Load document
	doc, _ := h.rDocument.FindOne(r.Context(), queryBuild)

	// Does not update matter folder
	if doc.ID != 0 && doc.SubObjectID == 0 {
		err := h.rDocument.UpdateOne(r.Context(), queryBuild, document)
		r.MustNoError(err)

		// load document again after updated
		doc, _ = h.rDocument.FindOne(r.Context(), queryBuild)

		// trigger update document activity
		var docType string
		if doc.DocType == 1 {
			docType = "folder"
		} else {
			docType = "document"
		}
		extra := transport.EventExtraData{
			ID:      doc.ID,
			Name:    doc.Name,
			Key:     doc.Key,
			DocType: docType,
		}
		eventDat := ActivityEvent(document, transport.ActionTypeUpdate, transport.ActionUpdate, transport.ActivityTypeDocument, document.UpdatedUser, extra)
		h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)
	}

	resp := ginext.NewResponseData(
		http.StatusNoContent,
		SingleDocumentResponse(doc),
	)
	return resp, nil
}

// @TODO: To be removed
func (h *DocumentHandler) Delete(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	req := new(documentDetailParams)
	// Get pagination filter
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	//userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))

		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)

	// Delete
	err := h.rDocument.DeleteOne(r.Context(), queryBuild)
	r.MustNoError(err)

	// temporary disable this for unable to handling in activity service
	//trigger delete document activity
	//document, _ := h.rDocument.FindOne(r.Context(), queryBuild)
	//var docType string
	//if document.DocType == 1 {
	//	docType = "folder"
	//} else {
	//	docType = "document"
	//}
	//extra := transport.EventExtraData{
	//	ID:      document.ID,
	//	Name:    document.Name,
	//	Key:     "",
	//	DocType: docType,
	//}
	//eventDat := ActivityEvent(document, transport.ActionTypeDelete, transport.ActionDelete, transport.ActivityTypeDocument, userID, extra)
	//h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)

	resp := ginext.NewResponseData(
		http.StatusNoContent,
		nil,
	)
	return resp, nil
}

func (h *DocumentHandler) DeleteV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	req := new(documentDetailParams)
	// Get pagination filter
	r.MustBindUri(req)

	tenantID := ginext.Uint64TenantID(r.GinCtx)
	userID := ginext.Uint64UserID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))

		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}

	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document, _ := h.rDocument.FindOne(r.Context(), queryBuild)

	// Does not delete matter folder
	if document.ID != 0 && document.SubObjectID == 0 {
		// Delete
		err := h.rDocument.DeleteOne(r.Context(), queryBuild)
		r.MustNoError(err)

		// trigger delete document activity
		var docType string
		if document.DocType == 1 {
			docType = "folder"
		} else {
			docType = "document"
		}
		extra := transport.EventExtraData{
			ID:      document.ID,
			Name:    document.Name,
			Key:     "",
			DocType: docType,
		}
		eventDat := ActivityEvent(document, transport.ActionTypeDelete, transport.ActionDelete, transport.ActivityTypeDocument, userID, extra)
		h.consumer.SafeEmit(r.Context(), transport.ActivityCreationTopic, eventDat)
	}

	resp := ginext.NewResponseData(
		http.StatusNoContent,
		nil,
	)
	return resp, nil
}

// @TODO: To be removed
func (h *DocumentHandler) Download(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	req := new(documentDetailParams)
	r.MustBindUri(req)

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)

	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}
	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document, err := h.rDocument.FindOne(r.Context(), queryBuild)
	r.MustNoError(err)

	resp, err := h.glob.EmitDownload(r.Context(), document.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
	r.MustNoError(err)

	return ginext.NewResponseData(
		http.StatusOK,
		resp,
	), nil
}

func (h *DocumentHandler) DownloadV3(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	req := new(documentDetailParams)
	r.MustBindUri(req)

	// Get tenant id in header
	tenantID := ginext.Uint64TenantID(r.GinCtx)

	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(http.StatusBadRequest, "invalid tenant id ")
	}
	filters := []*model.Filter{
		{
			Key:    "id",
			Value:  req.DocumentID,
			Method: "=",
		},
		{
			Key:    "tenant_id",
			Value:  tenantID,
			Method: "=",
		},
	}
	queryBuild := helper.BuildQuery("", filters, nil, nil)
	document, err := h.rDocument.FindOne(r.Context(), queryBuild)
	r.MustNoError(err)

	resp, err := h.glob.EmitDownload(r.Context(), document.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
	r.MustNoError(err)

	return ginext.NewResponseData(
		http.StatusOK,
		resp,
	), nil
}

func (h *DocumentHandler) MigrateSize(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		log.Error("invalid tenant id " + strconv.FormatInt(int64(tenantID), 10))
		return nil, ginext.NewError(400, "invalid tenant id ")
	}

	// Initial filter query
	filters := []*model.Filter{
		{
			Key:    "size",
			Value:  0,
			Method: "=",
		},
		{
			Key:    "doc_type",
			Value:  2,
			Method: "=",
		},
	}

	queryBuild := helper.BuildQuery("", filters, nil, nil)
	items, _ := h.rDocument.List(r.Context(), queryBuild)
	for _, item := range items {
		if item.Key != "" {
			fileSize, _ := h.glob.GetObjectSize(r.Context(), item.Key, strconv.FormatUint(ginext.Uint64UserID((r.GinCtx)), 10))
			filter := []*model.Filter{
				{
					Key:    "id",
					Value:  item.ID,
					Method: "=",
				},
			}
			queryBuild := helper.BuildQuery("", filter, nil, nil)
			document := &model.Document{
				Size: fileSize.Data.Size,
			}
			_ = h.rDocument.UpdateOne(r.Context(), queryBuild, document)
		}
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}
