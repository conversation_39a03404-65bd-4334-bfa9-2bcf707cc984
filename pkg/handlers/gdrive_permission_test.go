package handlers

import (
	"context"
	"errors"
	"testing"

	"bilabl/docman/domain/model"
	mock_gdrive "bilabl/docman/mocks/gdrive"
	mock_repositories "bilabl/docman/mocks/repositories"
	"bilabl/docman/pkg/repositories"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/drive/v3"
)

func TestGDrivePermissionHandler_SyncGoogleDrivePermissions(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint64
		objectType    string
		objectID      uint64
		ownerEmails   []string
		currentPerms  []*model.DocumentPermissionMapping
		mockSetup     func(*mock_gdrive.MockDriveClient, *mock_repositories.MockDocumentPermissionMappingRepository, *mock_repositories.MockDocumentMappingRepository, *mock_repositories.MockDocumentSettingRepository)
		expectedError bool
		errorContains string
	}{
		{
			name:        "successful sync with new permissions",
			tenantID:    1,
			objectType:  "matter",
			objectID:    123,
			ownerEmails: []string{"<EMAIL>", "<EMAIL>"},
			currentPerms: []*model.DocumentPermissionMapping{
				{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
			},
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Mock configuration lookup (return not found to use defaults)
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))

				// Mock document mapping lookup
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "matter", "google", uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{
						TenantID:       1,
						DriveID:        "drive123",
						Provider:       "google",
						ParentObjectID: 123, // Set ParentObjectID for matter folder
					}, nil)

				// Mock current permissions lookup
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					DriveID:  &[]string{"drive123"}[0],
					Provider: &[]string{model.DocProviderGoogle},
				}).Return([]*model.DocumentPermissionMapping{
					{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
				}, nil)

				// Mock permission creation
				mockClient.On("CreatePermission", mock.Anything, "drive123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>" && perm.Type == "user" && perm.Role == "writer"
				})).Return(&drive.Permission{
					Id:           "perm2",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission mapping storage
				// Wrapper method always passes ClientID=0 for legacy compatibility
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "perm2" && mapping.Provider == model.DocProviderGoogle && mapping.ClientID == uint64(0)
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:        "successful sync with permission removal",
			tenantID:    1,
			objectType:  "client",
			objectID:    123,
			ownerEmails: []string{"<EMAIL>"},
			currentPerms: []*model.DocumentPermissionMapping{
				{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
				{Email: "<EMAIL>", PermID: "perm2", DriveID: "drive123"},
			},
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Mock configuration lookup (return not found to use defaults)
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))

				// Mock document mapping lookup
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "client", "google", uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{
						TenantID: 1,
						DriveID:  "drive123",
						Provider: "google",
					}, nil)

				// Mock current permissions lookup
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					DriveID:  &[]string{"drive123"}[0],
					Provider: &[]string{model.DocProviderGoogle}, // Add provider filter
				}).Return([]*model.DocumentPermissionMapping{
					{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
					{Email: "<EMAIL>", PermID: "perm2", DriveID: "drive123"},
				}, nil)

				// Mock permission deletion
				mockClient.On("DeletePermission", mock.Anything, "drive123", "perm2").Return(nil)

				// Mock permission mapping deletion
				mockPermRepo.On("Delete", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "perm2"
				})).Return(nil)

				// Mock preserveMatterPermissions query (for client removal)
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					Emails:   &[]string{"<EMAIL>"},
					Provider: &[]string{model.DocProviderGoogle},
				}).Return([]*model.DocumentPermissionMapping{}, nil) // No matters to preserve
			},
			expectedError: false,
		},
		{
			name:        "error when document mapping not found",
			tenantID:    1,
			objectType:  "matter",
			objectID:    999,
			ownerEmails: []string{"<EMAIL>"},
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "matter", "google", uint64(1), uint64(999), uint64(0)).
					Return(nil, errors.New("not found"))
			},
			expectedError: true,
			errorContains: "failed to get document mapping",
		},
		{
			name:        "error when Google Drive API fails",
			tenantID:    1,
			objectType:  "client",
			objectID:    123,
			ownerEmails: []string{"<EMAIL>", "<EMAIL>"},
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Mock configuration lookup (return not found to use defaults)
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))

				// Mock document mapping lookup
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "client", "google", uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{
						TenantID: 1,
						DriveID:  "drive123",
						Provider: "google",
					}, nil)

				// Mock current permissions lookup
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					DriveID:  &[]string{"drive123"}[0],
					Provider: &[]string{model.DocProviderGoogle}, // Add provider filter
				}).Return([]*model.DocumentPermissionMapping{
					{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
				}, nil)

				// Mock API failure
				mockClient.On("CreatePermission", mock.Anything, "drive123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>"
				})).Return(nil, errors.New("user not found"))
			},
			expectedError: true,
			errorContains: "failed to add permissions",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockClient := mock_gdrive.NewMockDriveClient(t)
			mockPermRepo := mock_repositories.NewMockDocumentPermissionMappingRepository(t)
			mockDocRepo := mock_repositories.NewMockDocumentMappingRepository(t)
			mockSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)

			if tt.mockSetup != nil {
				tt.mockSetup(mockClient, mockPermRepo, mockDocRepo, mockSettingRepo)
			}

			// Create handler
			handler := NewGDrivePermissionHandler(mockClient, mockPermRepo, mockDocRepo, mockSettingRepo)

			// Execute test
			err := handler.SyncGoogleDrivePermissions(context.Background(), tt.tenantID, tt.objectType, tt.objectID, tt.ownerEmails)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			mockClient.AssertExpectations(t)
			mockPermRepo.AssertExpectations(t)
			mockDocRepo.AssertExpectations(t)
		})
	}
}

func TestGDrivePermissionHandler_SyncGoogleDrivePermissionsWithClientID(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint64
		objectType    string
		objectID      uint64
		ownerEmails   []string
		clientID      uint64
		mockSetup     func(*mock_gdrive.MockDriveClient, *mock_repositories.MockDocumentPermissionMappingRepository, *mock_repositories.MockDocumentMappingRepository, *mock_repositories.MockDocumentSettingRepository)
		expectedError bool
		errorContains string
	}{
		{
			name:        "successful sync with explicit clientID for matter",
			tenantID:    1,
			objectType:  "matter",
			objectID:    123,
			ownerEmails: []string{"<EMAIL>", "<EMAIL>"},
			clientID:    456, // Explicit clientID for matter
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Mock configuration lookup
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))

				// Mock document mapping lookup
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "matter", "google", uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{
						TenantID: 1,
						DriveID:  "drive123",
						Provider: "google",
					}, nil)

				// Mock current permissions lookup
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					DriveID:  &[]string{"drive123"}[0],
					Provider: &[]string{model.DocProviderGoogle},
				}).Return([]*model.DocumentPermissionMapping{
					{Email: "<EMAIL>", PermID: "perm1", DriveID: "drive123"},
				}, nil)

				// Mock permission creation
				mockClient.On("CreatePermission", mock.Anything, "drive123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>" && perm.Type == "user" && perm.Role == "writer"
				})).Return(&drive.Permission{
					Id:           "perm2",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission mapping storage with explicit clientID
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "perm2" && mapping.Provider == model.DocProviderGoogle && mapping.ClientID == uint64(456)
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:        "successful sync with clientID=0 for client",
			tenantID:    1,
			objectType:  "client",
			objectID:    123,
			ownerEmails: []string{"<EMAIL>"},
			clientID:    0, // ClientID=0 for client folders
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository, mockDocRepo *mock_repositories.MockDocumentMappingRepository, mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Mock configuration lookup
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))

				// Mock document mapping lookup
				mockDocRepo.On("FirstObjectMapping", mock.Anything, "client", "google", uint64(1), uint64(123), uint64(0)).
					Return(&model.DocumentMapping{
						TenantID: 1,
						DriveID:  "drive123",
						Provider: "google",
					}, nil)

				// Mock current permissions lookup
				mockPermRepo.On("Find", mock.Anything, &repositories.PermMapFindArgs{
					TenantID: &[]uint64{1}[0],
					DriveID:  &[]string{"drive123"}[0],
					Provider: &[]string{model.DocProviderGoogle},
				}).Return([]*model.DocumentPermissionMapping{}, nil)

				// Mock permission creation
				mockClient.On("CreatePermission", mock.Anything, "drive123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>"
				})).Return(&drive.Permission{
					Id:           "perm1",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission mapping storage with clientID=0
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "perm1" && mapping.Provider == model.DocProviderGoogle && mapping.ClientID == uint64(0)
				})).Return(nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockClient := mock_gdrive.NewMockDriveClient(t)
			mockPermRepo := mock_repositories.NewMockDocumentPermissionMappingRepository(t)
			mockDocRepo := mock_repositories.NewMockDocumentMappingRepository(t)
			mockSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)

			if tt.mockSetup != nil {
				tt.mockSetup(mockClient, mockPermRepo, mockDocRepo, mockSettingRepo)
			}

			// Create handler
			handler := NewGDrivePermissionHandler(mockClient, mockPermRepo, mockDocRepo, mockSettingRepo)

			// Execute test
			err := handler.SyncGoogleDrivePermissionsWithClientID(context.Background(), tt.tenantID, tt.objectType, tt.objectID, tt.ownerEmails, tt.clientID)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations
			mockClient.AssertExpectations(t)
			mockPermRepo.AssertExpectations(t)
			mockDocRepo.AssertExpectations(t)
		})
	}
}

func TestGDrivePermissionHandler_getEffectiveConfig(t *testing.T) {
	tests := []struct {
		name           string
		tenantID       uint64
		mockSetup      func(*mock_repositories.MockDocumentSettingRepository)
		expectedConfig PermissionHandlerConfig
	}{
		{
			name:     "use default config when no tenant config found",
			tenantID: 1,
			mockSetup: func(mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(nil, errors.New("not found"))
			},
			expectedConfig: PermissionHandlerConfig{
				DefaultRole:         "writer",
				RetryCount:          3,
				RetryDelayMs:        1000,
				SyncOnCreate:        true,
				SyncOnUpdate:        true,
				TimeoutSeconds:      30,
				EnableBatchSync:     false,
				BatchSize:           10,
				FailureNotification: true,
			},
		},
		{
			name:     "use tenant config when found",
			tenantID: 1,
			mockSetup: func(mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				// Correct JSON structure with nested permission_config
				configJSON := `{"enabled":true,"permission_config":{"default_role":"reader","retry_count":5,"retry_delay_ms":2000,"sync_on_create":false,"sync_on_update":true,"timeout_seconds":60,"enable_batch_sync":true,"batch_size":20,"failure_notification":false}}`
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(&model.DocumentSetting{
						Value: configJSON,
					}, nil)
			},
			expectedConfig: PermissionHandlerConfig{
				DefaultRole:         "reader",
				RetryCount:          5,
				RetryDelayMs:        2000,
				SyncOnCreate:        false,
				SyncOnUpdate:        true,
				TimeoutSeconds:      60,
				EnableBatchSync:     true,
				BatchSize:           20,
				FailureNotification: false,
			},
		},
		{
			name:     "use default config when JSON parsing fails",
			tenantID: 1,
			mockSetup: func(mockSettingRepo *mock_repositories.MockDocumentSettingRepository) {
				mockSettingRepo.On("GetValueByKey", mock.Anything, uint64(1), model.KeyGdriveConfig).
					Return(&model.DocumentSetting{
						Value: "invalid json",
					}, nil)
			},
			expectedConfig: PermissionHandlerConfig{
				DefaultRole:         "writer",
				RetryCount:          3,
				RetryDelayMs:        1000,
				SyncOnCreate:        true,
				SyncOnUpdate:        true,
				TimeoutSeconds:      30,
				EnableBatchSync:     false,
				BatchSize:           10,
				FailureNotification: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
			if tt.mockSetup != nil {
				tt.mockSetup(mockSettingRepo)
			}

			// Create handler
			handler := NewGDrivePermissionHandler(nil, nil, nil, mockSettingRepo)

			// Execute test
			config := handler.getEffectiveConfig(context.Background(), tt.tenantID)

			// Verify results
			assert.Equal(t, tt.expectedConfig, config)

			// Verify mock expectations
			mockSettingRepo.AssertExpectations(t)
		})
	}
}

func TestGDrivePermissionHandler_reshareMattersForUser(t *testing.T) {
	tests := []struct {
		name               string
		permissionMappings []*model.DocumentPermissionMapping
		tenantID           uint64
		mockSetup          func(*mock_gdrive.MockDriveClient, *mock_repositories.MockDocumentPermissionMappingRepository)
		expectedError      bool
		errorContains      string
	}{
		{
			name: "successful reshare for single matter",
			permissionMappings: []*model.DocumentPermissionMapping{
				{
					Email:    "<EMAIL>",
					DriveID:  "matter123",
					PermID:   "perm1",
					ClientID: 456,
					Provider: model.DocProviderGoogle,
				},
			},
			tenantID: 1,
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository) {
				// Mock permission creation
				mockClient.On("CreatePermission", mock.Anything, "matter123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>" && perm.Type == "user" && perm.Role == "writer"
				})).Return(&drive.Permission{
					Id:           "new_perm1",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission mapping update
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "new_perm1" && mapping.DriveID == "matter123"
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "successful reshare for multiple matters",
			permissionMappings: []*model.DocumentPermissionMapping{
				{
					Email:    "<EMAIL>",
					DriveID:  "matter123",
					PermID:   "perm1",
					ClientID: 456,
					Provider: model.DocProviderGoogle,
				},
				{
					Email:    "<EMAIL>",
					DriveID:  "matter456",
					PermID:   "perm2",
					ClientID: 456,
					Provider: model.DocProviderGoogle,
				},
			},
			tenantID: 1,
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository) {
				// Mock permission creation for first matter
				mockClient.On("CreatePermission", mock.Anything, "matter123", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>"
				})).Return(&drive.Permission{
					Id:           "new_perm1",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission creation for second matter
				mockClient.On("CreatePermission", mock.Anything, "matter456", mock.MatchedBy(func(perm *drive.Permission) bool {
					return perm.EmailAddress == "<EMAIL>"
				})).Return(&drive.Permission{
					Id:           "new_perm2",
					EmailAddress: "<EMAIL>",
					Type:         "user",
					Role:         "writer",
				}, nil)

				// Mock permission mapping updates
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "new_perm1" && mapping.DriveID == "matter123"
				})).Return(nil)
				mockPermRepo.On("CreateOrUpdate", mock.Anything, mock.MatchedBy(func(mapping *model.DocumentPermissionMapping) bool {
					return mapping.Email == "<EMAIL>" && mapping.PermID == "new_perm2" && mapping.DriveID == "matter456"
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:               "no matters to reshare",
			permissionMappings: []*model.DocumentPermissionMapping{},
			tenantID:           1,
			mockSetup: func(mockClient *mock_gdrive.MockDriveClient, mockPermRepo *mock_repositories.MockDocumentPermissionMappingRepository) {
				// No mocks needed for empty case - method returns early
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockClient := mock_gdrive.NewMockDriveClient(t)
			mockPermRepo := mock_repositories.NewMockDocumentPermissionMappingRepository(t)

			if tt.mockSetup != nil {
				tt.mockSetup(mockClient, mockPermRepo)
			}

			// Create handler with mock setting repo for getEffectiveConfig
			mockSettingRepo := mock_repositories.NewMockDocumentSettingRepository(t)
			// Setup mock for getEffectiveConfig if we have permissions to process
			// (method calls getEffectiveConfig even if ListPermissions fails)
			if len(tt.permissionMappings) > 0 {
				mockSettingRepo.On("GetValueByKey", mock.Anything, tt.tenantID, model.KeyGdriveConfig).
					Return(nil, errors.New("not found")).Maybe() // Use default config, allow multiple calls
			}
			handler := NewGDrivePermissionHandler(mockClient, mockPermRepo, nil, mockSettingRepo)

			// Execute test
			err := handler.reshareMattersForUser(context.Background(), tt.permissionMappings, tt.tenantID)

			// Verify results
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify mock expectations
			mockClient.AssertExpectations(t)
			mockPermRepo.AssertExpectations(t)
		})
	}
}

func TestGDrivePermissionHandler_computePermissionDiff(t *testing.T) {
	handler := &GDrivePermissionHandler{}

	tests := []struct {
		name            string
		currentPerms    []*model.DocumentPermissionMapping
		desiredEmails   []string
		expectedAdds    []string
		expectedRemoves int
	}{
		{
			name: "add new permissions",
			currentPerms: []*model.DocumentPermissionMapping{
				{Email: "<EMAIL>", PermID: "perm1"},
			},
			desiredEmails:   []string{"<EMAIL>", "<EMAIL>"},
			expectedAdds:    []string{"<EMAIL>"},
			expectedRemoves: 0,
		},
		{
			name: "remove permissions",
			currentPerms: []*model.DocumentPermissionMapping{
				{Email: "<EMAIL>", PermID: "perm1"},
				{Email: "<EMAIL>", PermID: "perm2"},
			},
			desiredEmails:   []string{"<EMAIL>"},
			expectedAdds:    []string{},
			expectedRemoves: 1,
		},
		{
			name: "no changes needed",
			currentPerms: []*model.DocumentPermissionMapping{
				{Email: "<EMAIL>", PermID: "perm1"},
				{Email: "<EMAIL>", PermID: "perm2"},
			},
			desiredEmails:   []string{"<EMAIL>", "<EMAIL>"},
			expectedAdds:    []string{},
			expectedRemoves: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toAdd, toRemove := handler.computePermissionDiff(tt.currentPerms, tt.desiredEmails)

			assert.Equal(t, tt.expectedAdds, toAdd)
			assert.Equal(t, tt.expectedRemoves, len(toRemove))
		})
	}
}
