package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"
	"code.mybil.net/gophers/gokit/components/entity"
	"code.mybil.net/gophers/gokit/pkg/logger"
	"code.mybil.net/gophers/gokit/util/sliceutil"
	"code.mybil.net/gophers/gokit/util/valutil"
	"context"
	"gitlab.com/goxp/cloud0/ginext"
	"net/http"
)

func (h *SharepointHandler) handleMatterUpdate(
	r *ginext.Request,
	body *model.ConsumeMatterReq,
	spParams *sharepointParams,
	matterFolder string,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(r.Context(), "sharepoint.handleMatterUpdate")

	drive, err := h.rDocumentMapping.FirstObjectMapping(
		ctx,
		model.DocTypeMatter,
		model.DocProviderSharepoint,
		body.Body.TenantID,
		body.Body.ID,
		0,
	)
	if model.IsNotFound(err) {
		drive, err = h.createMatterFolder(ctx, spParams, matterFolder, body.Body.TenantID, body.Body.ID, body.Body.ClientID)
	}

	if err != nil {
		log.Errorf("failed to find matter folder drive: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	// Update the matter folder name
	_, err = h.spClient.RenameDriveItem(r.Context(), spParams.SiteDriveID, spParams.AccessToken, drive.DriveID, matterFolder)
	if err != nil {
		log.Errorf("failed to update matter folder name: %v", err)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	// fetch matter detail to determine the current owners
	// use all client owners and matter owners as the current owners
	matterOwners, err := h.loadMatterClientOwners(ctx, entity.ID(body.Body.ID))
	if err != nil {
		log.Errorf("failed to load matter client owners: %v", err)
		matterOwners = body.Body.Extra.Current.OwnerUsers
	}

	oldEmails := sliceutil.Map(body.Body.Extra.Old.OwnerUsers, func(o model.OwnerUser) string {
		return o.Email
	})

	currentPermOwners, err := h.rDocumentPermission.Find(r.Context(), &repositories.PermMapFindArgs{
		TenantID: valutil.CreatePtr(body.Body.TenantID),
		DriveID:  valutil.CreatePtr(drive.DriveID),
		Provider: valutil.CreatePtr([]string{model.DocProviderSharepoint}),
		Emails:   valutil.CreatePtr(oldEmails),
	})
	r.MustNoError(err)
	err = h.updatePermissions(r.Context(), spParams, drive, currentPermOwners, body.Body.Extra.Old.OwnerUsers, matterOwners)

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *SharepointHandler) loadMatterClientOwners(ctx context.Context, matterID entity.ID) ([]model.OwnerUser, error) {
	matter, err := h.fetcher.Fetch(ctx, entity.KindMatter, matterID)
	if err != nil {
		return nil, err
	}

	// translate matter.Owners to emails
	if len(matter.Owners) == 0 {
		return nil, nil
	}

	users, err := h.idClient.ListUsers(ctx, matter.Owners)
	if err != nil {
		return nil, err
	}

	owners := make([]model.OwnerUser, 0, len(users))
	for _, u := range users {
		owners = append(owners, model.OwnerUser{
			Email: u.Email,
			Id:    uint64(u.ID),
		})
	}

	return owners, nil
}
