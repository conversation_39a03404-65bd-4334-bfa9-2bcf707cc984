package handlers

import (
	"net/http"
	"strconv"
	"time"

	"bilabl/docman/internal/service/autodoc"
	"bilabl/docman/pkg/bilabllog"

	"code.mybil.net/gophers/gokit/domain/actor"
	"code.mybil.net/gophers/gokit/util/valutil"
	"gitlab.com/goxp/cloud0/ginext"
)

// AutoDocFileHandler handles AutoDoc file management endpoints
type AutoDocFileHandler struct {
	documentRegistry autodoc.DocumentServiceRegistry
	uploadRegistry   autodoc.UploadProviderRegistry
	fileRegistry     autodoc.FileProviderRegistry
}

// NewAutoDocFileHandler creates a new AutoDocFileHandler
func NewAutoDocFileHandler(documentRegistry autodoc.DocumentServiceRegistry, uploadRegistry autodoc.UploadProviderRegistry, fileRegistry autodoc.FileProviderRegistry) *AutoDocFileHandler {
	return &AutoDocFileHandler{
		documentRegistry: documentRegistry,
		uploadRegistry:   uploadRegistry,
		fileRegistry:     fileRegistry,
	}
}

// ListFilesRequest represents the request for listing files
type ListFilesRequest struct {
	ParentID   string `form:"parent_id"` // String to support both internal (uint64) and external (string) IDs
	Provider   string `form:"provider"`  // Provider name (internal, gdrive, etc.) - optional, defaults to internal
	Page       int    `form:"page" binding:"min=1"`
	Limit      int    `form:"limit" binding:"min=1,max=100"`
	Search     string `form:"search"`
	DocType    int    `form:"doc_type"`
	ObjectType int    `form:"object_type"`
}

// ListFilesResponse represents the response for listing files
type ListFilesResponse struct {
	Data       []FileInfo `json:"data"`
	Pagination Pagination `json:"pagination"`
	Provider   string     `json:"provider"`
}

// FileInfo represents file information in the response
type FileInfo struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	ParentID   uint64 `json:"parent_id"`
	Size       int64  `json:"size"`
	DocType    int    `json:"doc_type"`
	ObjectType int    `json:"object_type"`
	ObjectID   uint64 `json:"object_id"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
	IsFolder   bool   `json:"is_folder"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// PreUploadRequest represents the request for pre-upload (register to get upload link)
type PreUploadRequest struct {
	ParentID   string `json:"parent_id"` // Parent folder ID (string to support multi-provider)
	FolderID   string `json:"folder_id"` // Alternative field name for parent_id (for backward compatibility)
	Provider   string `json:"provider"`  // Provider name (internal, gdrive, etc.) - optional, defaults to internal
	ObjectType int    `json:"object_type"`
	ObjectID   uint64 `json:"object_id"`
	DocType    int    `json:"doc_type"`
	FileName   string `json:"file_name" binding:"required"`
	FileSize   int64  `json:"file_size"`
	MimeType   string `json:"mime_type"`
}

// PreUploadResponse represents the response for pre-upload
type PreUploadResponse struct {
	UploadURL    string `json:"upload_url"`    // For external providers (Google Drive, SharePoint)
	SessionToken string `json:"session_token"` // For tracking upload session
	Key          string `json:"key"`           // Key from glob service or external provider
	ExpiresAt    string `json:"expires_at"`    // Upload URL expiration
	Provider     string `json:"provider"`      // Actual provider used
	Message      string `json:"message"`
}

// RegisterUploadRequest represents the request for registering uploaded file
type RegisterUploadRequest struct {
	SessionToken string `json:"session_token" binding:"required"` // From pre-upload response
	Key          string `json:"key"`                              // Key from glob service (required for internal, optional for external providers)
	FileName     string `json:"file_name"`                        // File name (required for internal, optional for external providers)
	FileSize     int64  `json:"file_size"`
	MimeType     string `json:"mime_type"`
	// Fields for internal provider compatibility
	ObjectID   uint64 `json:"object_id,omitempty"`   // Business object ID (for internal provider)
	ObjectType int    `json:"object_type,omitempty"` // Business object type (for internal provider)
	ParentID   string `json:"parent_id,omitempty"`   // Parent folder ID (string to support multi-provider)
	ExternalID string `json:"external_id"`           // External file ID (deprecated, use Key)
}

// RegisterUploadResponse represents the response for registering uploaded file
type RegisterUploadResponse struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	Size       int64  `json:"size"`
	ExternalID string `json:"external_id,omitempty"` // For external providers
	Provider   string `json:"provider"`
	Message    string `json:"message"`
}

// CreateFolderRequest represents the request for creating a folder
type CreateFolderRequest struct {
	Name        string `json:"name" binding:"required"`
	ParentID    string `json:"parent_id"` // Parent folder ID (string to support multi-provider)
	ParentPath  string `json:"parent_path"`
	Provider    string `json:"provider"`    // Provider name (internal, gdrive, etc.) - optional, defaults to internal
	ObjectType  int    `json:"object_type"` // Associated object type
	ObjectID    uint64 `json:"object_id"`   // Associated object ID
	Description string `json:"description"` // Optional folder description
}

// CreateFolderResponse represents the response for creating a folder
type CreateFolderResponse struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	ParentID   string `json:"parent_id"`             // Parent folder ID (string to support multi-provider)
	ExternalID string `json:"external_id,omitempty"` // For external providers
	Provider   string `json:"provider"`
	Message    string `json:"message"`
}

// CreateFileRequest represents the unified request for creating files/folders (only folders supported)
type CreateFileRequest struct {
	Name        string `json:"name" binding:"required"`
	DocType     int    `json:"doc_type" binding:"required"` // 1 = folder (only supported type)
	ParentID    string `json:"parent_id"`                   // Parent folder ID (string to support multi-provider)
	ParentPath  string `json:"parent_path"`
	Provider    string `json:"provider"`    // Provider name (internal, gdrive, etc.) - optional, defaults to internal
	ObjectType  int    `json:"object_type"` // Associated object type
	ObjectID    uint64 `json:"object_id"`   // Associated object ID
	Description string `json:"description"` // Optional description
}

// CreateFileResponse represents the unified response for file/folder creation
type CreateFileResponse struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	DocType    int    `json:"doc_type"`              // 1 = folder
	ParentID   string `json:"parent_id"`             // Parent folder ID (string to support multi-provider)
	ExternalID string `json:"external_id,omitempty"` // For external providers
	Provider   string `json:"provider"`
	Message    string `json:"message"`
}

// UploadFileRequest represents the unified request for uploading files or creating folders
type UploadFileRequest struct {
	Name        string `json:"name" binding:"required"`
	FileType    int    `json:"file_type" binding:"required"` // 1 = folder, 2 = file
	ParentID    string `json:"parent_id"`                    // Parent folder ID (string to support multi-provider)
	ParentPath  string `json:"parent_path"`
	Provider    string `json:"provider"`    // Provider name (internal, gdrive, etc.) - optional, defaults to internal
	ObjectType  int    `json:"object_type"` // Associated object type
	ObjectID    uint64 `json:"object_id"`   // Associated object ID
	Content     []byte `json:"content"`     // Required for file_type=2, ignored for file_type=1
	MimeType    string `json:"mime_type"`   // Required for file_type=2
	Overwrite   bool   `json:"overwrite"`
	Description string `json:"description"` // Optional description
}

// UploadFileResponse represents the unified response for file upload or folder creation
type UploadFileResponse struct {
	ID         uint64 `json:"id"`
	Name       string `json:"name"`
	FileType   int    `json:"file_type"` // 1 = folder, 2 = file
	ParentID   uint64 `json:"parent_id"`
	ExternalID string `json:"external_id,omitempty"` // For external providers
	Provider   string `json:"provider"`
	Size       int64  `json:"size"`
	MimeType   string `json:"mime_type,omitempty"`
	Message    string `json:"message"`
}

// UpdateFileRequest represents the request for updating a file
type UpdateFileRequest struct {
	Name     string `json:"name"`
	Provider string `json:"provider"` // Provider name (internal, gdrive, etc.) - optional, defaults to internal
}

// UpdateFileResponse represents the response for updating a file
type UpdateFileResponse struct {
	ID       string `json:"id"` // Provider-specific ID
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	Provider string `json:"provider"`
	Message  string `json:"message"`
}

// DeleteFileRequest represents the request for deleting a file
type DeleteFileRequest struct {
	Provider string `form:"provider"` // Provider name (internal, gdrive, etc.) - optional, defaults to internal
}

// DeleteFileResponse represents the response for deleting a file
type DeleteFileResponse struct {
	ID       string `json:"id"` // Provider-specific ID
	Provider string `json:"provider"`
	Message  string `json:"message"`
}

// GetFileRequest represents the request for getting a file
type GetFileRequest struct {
	Provider string `form:"provider"` // Provider name (internal, gdrive, etc.) - optional, defaults to internal
}

// GetFileResponse represents the response for getting a file
type GetFileResponse struct {
	ID         string `json:"id"` // Provider-specific ID (uint64 for internal, string for external)
	Name       string `json:"name"`
	Size       int64  `json:"size"`
	MimeType   string `json:"mime_type,omitempty"`
	ExternalID string `json:"external_id,omitempty"` // External file ID (for external providers)
	Provider   string `json:"provider"`
	CreatedAt  string `json:"created_at,omitempty"`
	UpdatedAt  string `json:"updated_at,omitempty"`
}

// ListFiles handles GET /api/v1/autodoc/files
func (h *AutoDocFileHandler) ListFiles(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Listing AutoDoc files")

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body ListFilesRequest
	r.MustBind(&body)

	// Set defaults
	if body.Page == 0 {
		body.Page = 1
	}
	if body.Limit == 0 {
		body.Limit = 20
	}

	// Resolve provider (from query string for GET requests)
	provider := valutil.FirstNonZero(r.GinCtx.Query("provider"), body.Provider)
	if provider == "" {
		provider = h.documentRegistry.GetDefaultProvider()
	}

	// Get document service
	documentService, actualProvider, err := h.documentRegistry.GetProviderWithFallback(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get document service")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to get document service: "+err.Error())
	}

	// For now, return a placeholder response since we need to implement document listing
	// This would typically call documentService.ListDocuments() when that method exists
	// Log that we have the service available for future implementation
	log.WithField("provider", actualProvider).WithField("service_available", documentService != nil).Debug("Document service resolved for listing")

	// Create service request with provider-specific ParentID handling
	serviceReq := &autodoc.ListFilesRequest{
		TenantID: reqActor.TenantID,
		Page:     body.Page,
		Limit:    body.Limit,
	}

	// Handle ParentID based on provider type
	if actualProvider == "internal" {
		// For internal provider, convert string ParentID to uint64
		if body.ParentID != "" {
			parentID, err := strconv.ParseUint(body.ParentID, 10, 64)
			if err != nil {
				return nil, ginext.NewError(http.StatusBadRequest, "Invalid parent_id format for internal provider")
			}
			serviceReq.ParentID = parentID
		}
	} else {
		// For external providers, use string ParentID as ExternalParentID
		serviceReq.ExternalParentID = body.ParentID
	}

	result, err := documentService.ListFiles(r.Context(), serviceReq)
	r.MustNoError(err)

	// Create pager from result pagination info
	pager := &ginext.Pager{
		Page:      result.Page,
		TotalRows: result.TotalCount,
		PageSize:  result.Limit,
	}

	return ginext.NewResponseWithPager(http.StatusOK, result, pager), nil
}

// CreateFolder handles POST /api/v1/autodoc/folders
func (h *AutoDocFileHandler) CreateFolder(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Creating AutoDoc folder")

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body CreateFolderRequest
	r.MustBind(&body)

	// Resolve provider
	provider := body.Provider
	if provider == "" {
		provider = h.documentRegistry.GetDefaultProvider()
	}

	// Get document service for the provider
	documentService, actualProvider, err := h.documentRegistry.GetProviderWithFallback(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get document service")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to get document service: "+err.Error())
	}

	log.WithField("provider", actualProvider).WithField("service_available", documentService != nil).Debug("Document service resolved for folder creation")

	// Create folder using document service
	createReq := &autodoc.CreateFolderRequest{
		Name:        body.Name,
		Path:        body.ParentPath,
		TenantID:    reqActor.TenantID,
		ObjectType:  body.ObjectType,
		ObjectID:    body.ObjectID,
		CreatedUser: reqActor.ID,
	}

	// Handle ParentID based on provider type
	if actualProvider == "internal" {
		// For internal provider, convert string ParentID to uint64
		if body.ParentID != "" {
			parentID, err := strconv.ParseUint(body.ParentID, 10, 64)
			if err != nil {
				return nil, ginext.NewError(http.StatusBadRequest, "Invalid parent_id format for internal provider")
			}
			createReq.ParentID = parentID
		}
	} else {
		// For external providers, use string ParentID as ExternalParentID
		createReq.ExternalParentID = body.ParentID
	}

	result, err := documentService.CreateFolderWithResponse(r.Context(), createReq)
	r.MustNoError(err)

	// Add provider info to response
	response := &CreateFolderResponse{
		ID:         result.ID,
		Name:       result.Name,
		ParentID:   strconv.FormatUint(result.ParentID, 10), // Convert uint64 to string
		ExternalID: result.ExternalID,
		Provider:   actualProvider,
		Message:    "Folder created successfully",
	}

	log.WithField("folder_id", result.ID).WithField("provider", actualProvider).WithField("folder_name", result.Name).Info("Folder created successfully")
	return ginext.NewResponseData(http.StatusCreated, response), nil
}

// CreateFile handles POST /v3/autodoc/files - Unified endpoint for creating folders
func (h *AutoDocFileHandler) CreateFile(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Creating AutoDoc file/folder")

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body CreateFileRequest
	r.MustBind(&body)

	// Validate doc_type - only folders are supported
	if body.DocType != 1 {
		return nil, ginext.NewError(http.StatusBadRequest, "Only folder creation is supported (doc_type=1)")
	}

	// Resolve provider
	provider := body.Provider
	if provider == "" {
		provider = h.documentRegistry.GetDefaultProvider()
	}

	// Get document service for the provider
	documentService, actualProvider, err := h.documentRegistry.GetProviderWithFallback(provider)
	if err != nil {
		log.WithError(err).WithField("provider", provider).Error("Failed to get document service")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to get document service: "+err.Error())
	}

	log.WithField("provider", actualProvider).WithField("service_available", documentService != nil).Debug("Document service resolved for folder creation")

	// Create folder using document service
	createReq := &autodoc.CreateFolderRequest{
		Name:        body.Name,
		Path:        body.ParentPath,
		TenantID:    reqActor.TenantID,
		ObjectType:  body.ObjectType,
		ObjectID:    body.ObjectID,
		CreatedUser: reqActor.ID,
	}

	// Handle ParentID based on provider type
	if actualProvider == "internal" {
		// For internal provider, convert string ParentID to uint64
		if body.ParentID != "" {
			parentID, err := strconv.ParseUint(body.ParentID, 10, 64)
			if err != nil {
				return nil, ginext.NewError(http.StatusBadRequest, "Invalid parent_id format for internal provider")
			}
			createReq.ParentID = parentID
		}
	} else {
		// For external providers, use string ParentID as ExternalParentID
		createReq.ExternalParentID = body.ParentID
	}

	result, err := documentService.CreateFolderWithResponse(r.Context(), createReq)
	r.MustNoError(err)

	// Convert to unified response format
	response := &CreateFileResponse{
		ID:         result.ID,
		Name:       result.Name,
		DocType:    1,                                       // Always folder
		ParentID:   strconv.FormatUint(result.ParentID, 10), // Convert uint64 to string
		ExternalID: result.ExternalID,
		Provider:   actualProvider,
		Message:    "Folder created successfully",
	}

	log.WithField("folder_id", result.ID).WithField("provider", actualProvider).WithField("folder_name", result.Name).Info("Folder created via unified endpoint")
	return ginext.NewResponseData(http.StatusCreated, response), nil
}

// PreUpload handles POST /api/v1/autodoc/files/upload - Step 1: Register to get upload link
func (h *AutoDocFileHandler) PreUpload(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Pre-upload: Registering file for upload")

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body PreUploadRequest
	r.MustBind(&body)

	// Resolve provider (from payload for POST requests)
	provider := body.Provider
	if provider == "" {
		provider = "internal" // Default to internal
	}

	// Get upload provider
	uploadProvider, err := h.uploadRegistry.GetProvider(provider)
	r.MustNoError(err)

	// Create upload session request
	sessionReq := &autodoc.CreateUploadSessionRequest{
		TenantID:    reqActor.TenantID,
		FileName:    body.FileName,
		FileSize:    body.FileSize,
		MimeType:    body.MimeType,
		ObjectType:  body.ObjectType,
		ObjectID:    body.ObjectID,
		CreatedUser: reqActor.ID,
	}

	// Resolve parent ID from either parent_id or folder_id field
	parentIDStr := body.ParentID
	if parentIDStr == "" {
		parentIDStr = body.FolderID // Fallback to folder_id for backward compatibility
	}

	// Handle ParentID based on provider type
	if provider == "internal" {
		// For internal provider, convert string ParentID to uint64
		if parentIDStr != "" {
			parentID, err := strconv.ParseUint(parentIDStr, 10, 64)
			if err != nil {
				return nil, ginext.NewError(http.StatusBadRequest, "Invalid parent_id format for internal provider")
			}
			sessionReq.ParentID = parentID
		}
	} else {
		// For external providers, use string ParentID as ExternalParentID
		sessionReq.ExternalParentID = parentIDStr
	}

	// Create upload session
	sessionResult, err := uploadProvider.CreateUploadSession(r.Context(), sessionReq)
	r.MustNoError(err)

	// Convert to response format
	response := &PreUploadResponse{
		UploadURL:    sessionResult.UploadURL,
		SessionToken: sessionResult.SessionToken,
		Key:          sessionResult.Key,
		ExpiresAt:    sessionResult.ExpiresAt.Format(time.RFC3339),
		Provider:     sessionResult.Provider,
		Message:      sessionResult.Message,
	}

	log.WithField("provider", sessionResult.Provider).WithField("file_name", body.FileName).WithField("session_token", sessionResult.SessionToken).Info("Pre-upload completed")
	return ginext.NewResponseData(http.StatusOK, response), nil
}

// RegisterUpload handles POST /api/v1/autodoc/files/register - Step 2: Register after upload success
func (h *AutoDocFileHandler) RegisterUpload(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Register upload: Recording uploaded file")

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body RegisterUploadRequest
	r.MustBind(&body)

	// Resolve provider (from query string for POST requests)
	provider := r.Query("provider")
	if provider == "" {
		provider = "internal" // Default to internal
	}

	// Provider-specific validation
	if provider == "internal" {
		if body.Key == "" {
			log.Error("Key is required for internal provider")
			return nil, ginext.NewError(http.StatusBadRequest, "key is required for internal provider")
		}
		if body.FileName == "" {
			log.Error("File name is required for internal provider")
			return nil, ginext.NewError(http.StatusBadRequest, "file_name is required for internal provider")
		}
	}

	// Get upload provider
	uploadProvider, err := h.uploadRegistry.GetProvider(provider)
	r.MustNoError(err)

	// Create register upload request
	registerReq := &autodoc.RegisterUploadRequest{
		TenantID:     reqActor.TenantID,
		SessionToken: body.SessionToken,
		Key:          body.Key,
		FileName:     body.FileName,
		FileSize:     body.FileSize,
		MimeType:     body.MimeType,
		ObjectID:     body.ObjectID,   // For internal provider
		ObjectType:   body.ObjectType, // For internal provider
		ExternalID:   body.ExternalID,
		CreatedUser:  reqActor.ID,
	}

	// Handle ParentID based on provider type
	// Note: RegisterUploadRequest in service layer only has ParentID (uint64), no ExternalParentID
	// For external providers, ParentID should be 0 since parent is handled during upload session
	if provider == "internal" && body.ParentID != "" {
		// For internal provider, convert string ParentID to uint64
		parentID, err := strconv.ParseUint(body.ParentID, 10, 64)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, "Invalid parent_id format for internal provider")
		}
		registerReq.ParentID = parentID
	}

	// Register upload
	registerResult, err := uploadProvider.RegisterUpload(r.Context(), registerReq)
	if err != nil {
		// Check if it's a validation error and return 400
		if _, ok := err.(*autodoc.ValidationError); ok {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
		// For other errors, use MustNoError for proper error handling
		r.MustNoError(err)
	}

	// Convert to response format
	response := &RegisterUploadResponse{
		ID:         registerResult.ID,
		Name:       registerResult.Name,
		Size:       registerResult.Size,
		ExternalID: registerResult.ExternalID,
		Provider:   registerResult.Provider,
		Message:    registerResult.Message,
	}

	log.WithField("document_id", registerResult.ID).WithField("provider", registerResult.Provider).WithField("session_token", body.SessionToken).Info("Upload registered")
	return ginext.NewResponseData(http.StatusCreated, response), nil
}

// GetFile handles GET /api/v1/autodoc/files/:id
func (h *AutoDocFileHandler) GetFile(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Getting AutoDoc file")

	// Get file ID from URL parameter (provider-specific format)
	fileID := r.GinCtx.Param("id")
	if fileID == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "File ID is required")
	}

	reqActor := actor.FromGinCtx(r.GinCtx)

	// Resolve provider
	provider := r.Query("provider")
	if provider == "" {
		provider = "internal" // Default to internal
	}

	// Get file provider by name
	fileProvider, err := h.fileRegistry.GetProvider(provider)
	r.MustNoError(err)

	// Get file using provider
	getReq := &autodoc.GetFileRequest{
		ID:       fileID,
		TenantID: reqActor.TenantID,
	}

	fileResult, err := fileProvider.GetFile(r.Context(), getReq)
	r.MustNoError(err)

	response := &GetFileResponse{
		ID:         fileResult.ID,
		Name:       fileResult.Name,
		Size:       fileResult.Size,
		ExternalID: fileResult.ExternalID,
		Provider:   fileResult.Provider,
		CreatedAt:  fileResult.CreatedAt,
		UpdatedAt:  fileResult.UpdatedAt,
	}

	log.WithField("file_id", fileID).WithField("provider", fileResult.Provider).WithField("file_name", fileResult.Name).Info("File retrieved successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// UpdateFile handles PUT /api/v1/autodoc/files/:id
func (h *AutoDocFileHandler) UpdateFile(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Updating AutoDoc file")

	// Get file ID from URL parameter (provider-specific format)
	fileID := r.GinCtx.Param("id")
	if fileID == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "File ID is required")
	}

	reqActor := actor.FromGinCtx(r.GinCtx)

	var body UpdateFileRequest
	r.MustBind(&body)

	// Resolve provider (from payload for PUT requests)
	provider := valutil.FirstNonZero(r.Query("provider"), body.Provider)
	if provider == "" {
		provider = "internal" // Default to internal
	}

	// Get file provider by name
	fileProvider, err := h.fileRegistry.GetProvider(provider)
	r.MustNoError(err)

	// Update file using provider
	updateReq := &autodoc.UpdateFileRequest{
		ID:       fileID,
		TenantID: reqActor.TenantID,
		Name:     body.Name,
	}

	fileResult, err := fileProvider.UpdateFile(r.Context(), updateReq)
	r.MustNoError(err)

	response := &UpdateFileResponse{
		ID:       fileResult.ID,
		Name:     fileResult.Name,
		Size:     fileResult.Size,
		Provider: fileResult.Provider,
		Message:  fileResult.Message,
	}

	log.WithField("file_id", fileID).WithField("provider", fileResult.Provider).WithField("new_name", body.Name).Info("File updated successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// DeleteFile handles DELETE /api/v1/autodoc/files/:id
func (h *AutoDocFileHandler) DeleteFile(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Deleting AutoDoc file")

	// Get file ID from URL parameter (provider-specific format)
	fileID := r.GinCtx.Param("id")
	if fileID == "" {
		return nil, ginext.NewError(http.StatusBadRequest, "File ID is required")
	}

	reqActor := actor.FromGinCtx(r.GinCtx)

	// Resolve provider (from query string for DELETE requests)
	provider := r.GinCtx.Query("provider")
	if provider == "" {
		provider = "internal" // Default to internal
	}

	// Get file provider by name
	fileProvider, err := h.fileRegistry.GetProvider(provider)
	r.MustNoError(err)

	// Delete file using provider
	deleteReq := &autodoc.FileDeleteRequest{
		ID:       fileID,
		TenantID: reqActor.TenantID,
	}

	fileResult, err := fileProvider.DeleteFile(r.Context(), deleteReq)
	r.MustNoError(err)

	response := &DeleteFileResponse{
		ID:       fileResult.ID,
		Provider: fileResult.Provider,
		Message:  fileResult.Message,
	}

	log.WithField("file_id", fileID).WithField("provider", fileResult.Provider).Info("File deleted successfully")

	return ginext.NewResponseData(http.StatusOK, response), nil
}
