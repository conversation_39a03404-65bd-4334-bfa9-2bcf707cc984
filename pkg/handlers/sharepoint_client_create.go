package handlers

import (
	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/sharepointclient"
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"

	"code.mybil.net/gophers/gokit/pkg/logger"
	"github.com/gookit/goutil/arrutil"
	"gitlab.com/goxp/cloud0/ginext"
)

// ConsumeSharepointClientCrud handles client events for SharePoint
// @deprecated This direct provider endpoint is deprecated. Use /internal/consume/autodoc/client/created
// and /internal/consume/autodoc/client/updated for coordinated processing to avoid race conditions.
func (h *SharepointHandler) ConsumeSharepointClientCrud(r *ginext.Request) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "sharepoint.handleClientEvents")

	var body model.ConsumeClientReq
	r.MustBind(&body)

	log.Debugf("getting client event with topic %s", body.Topic)

	// Direct provider endpoints are deprecated - use coordinated processing
	deprecateDirectCalls := strings.ToLower(os.Getenv("COORDINATION_DEPRECATE_DIRECT_CALLS")) == "true"

	if deprecateDirectCalls {
		log.Warn("Direct SharePoint client provider endpoint is deprecated")
		return nil, ginext.NewError(http.StatusGone,
			"Direct provider endpoints are deprecated. Use /internal/consume/autodoc/client/created or /internal/consume/autodoc/client/updated for coordinated processing")
	} else {
		log.Warn("Direct SharePoint client provider endpoint called - this may cause conflicts with coordination")
		log.Warn("Consider using /internal/consume/autodoc/client/created or /internal/consume/autodoc/client/updated for coordinated processing")
	}

	// Get external_tenant_id
	_, err := h.rDocumentSetting.GetValueByKey(r.Context(), body.Body.TenantID, model.KeyExternalTenantID)

	if model.IsNotFound(err) {
		log.Debugf("tenant %d doesn't have external_tenant_id", body.Body.TenantID)
		// no retry here
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	if err != nil {
		log.Errorf("error while query external_id: %v", err)
		return ginext.NewResponseData(http.StatusInternalServerError, nil), err
	}

	spParams, err := h.loadSharepointParams(r.Context(), body.Body.TenantID)
	r.MustNoError(err)

	// Define the client folderName
	var folderName string
	if body.Body.ShortName != "" {
		folderName = body.Body.ShortName
	} else {
		folderName = body.Body.Name
	}
	clientFolder := fmt.Sprintf("%s - %s", folderName, body.Body.Code)

	resp := ginext.NewResponseData(http.StatusOK, nil)
	switch body.Topic {
	case "client.create":
		resp, err = h.handleClientCreate(r, &body, spParams, clientFolder)
	case "client.update":
		resp, err = h.handleClientUpdate(r, &body, spParams, clientFolder)
	}

	// handle rate limited error
	if err != nil {
		if rateLimitedErr, ok := err.(sharepointclient.ErrRateLimited); ok {
			log.Infof("rate limited error, retry after %s", rateLimitedErr.RetryAfterHeader())
			resp = ginext.NewResponseData(http.StatusTooManyRequests, nil)
			resp.Header = http.Header{}
			resp.Header.Add("Retry-After", rateLimitedErr.RetryAfterHeader())

			return resp, nil
		}
	}

	return resp, err
}

func (h *SharepointHandler) handleClientCreate(
	r *ginext.Request,
	body *model.ConsumeClientReq,
	spParams *sharepointParams,
	clientFolder string,
) (*ginext.Response, error) {
	ctx := r.Context()
	log := logger.WithCtx(ctx, "sharepoint.handleClientCreate")
	// Create client folder under root
	clientDrive, err := h.createClientDriveFolder(ctx, spParams, clientFolder, body.Body.ID)
	if err != nil {
		log.Errorf("create client drive folder error: %v", err)
		return nil, err
	}

	_, err = h.createMatterParentFolder(ctx, spParams, clientDrive)
	if err != nil {
		log.Errorf("create matter parent folder error: %v", err)
		return nil, err
	}

	ownerEmails := arrutil.Map(body.Body.Extra.Current.OwnerUsers, func(o model.OwnerUser) (string, bool) {
		return o.Email, true
	})

	if len(ownerEmails) == 0 {
		log.Warnf("no owner email found for client %d", body.Body.ID)
		return ginext.NewResponseData(http.StatusOK, nil), nil
	}

	perms, err := h.spClient.InvitePermissionDriveItem(ctx, spParams.SiteDriveID, spParams.AccessToken, clientDrive.DriveID, ownerEmails)
	if err != nil {
		log.Errorf("failed to invite permission: %v", err)
		return nil, err
	}

	if err = h.handlePermissionMapping(ctx, &perms, body.Body.TenantID, clientDrive.DriveID); err != nil {
		log.Errorf("error while handling permission mapping: %v", err)
		return nil, err
	}

	return ginext.NewResponseData(http.StatusOK, nil), nil
}

func (h *SharepointHandler) handlePermissionMapping(ctx context.Context, perms *sharepointclient.ListPermissionResponse,
	tenantID uint64, driveId string) error {
	// Create or update permission mapping
	var permMaps []*model.DocumentPermissionMapping

	for _, perm := range perms.Value {
		if perm.GrantedTo != nil && perm.GrantedTo.User != nil {
			permMaps = append(permMaps, &model.DocumentPermissionMapping{
				TenantID: tenantID,
				Email:    perm.GrantedTo.User.Email,
				DriveID:  driveId,
				PermID:   perm.ID,
				Provider: model.DocProviderSharepoint, // Set provider to sharepoint
			})
		}

		for _, idPerm := range perm.GrantedToIdentities {
			if idPerm.User != nil {
				permMaps = append(permMaps, &model.DocumentPermissionMapping{
					TenantID: tenantID,
					Email:    idPerm.User.Email,
					DriveID:  driveId,
					PermID:   perm.ID,
					Provider: model.DocProviderSharepoint, // Set provider to sharepoint
				})
			}
		}
	}

	return h.rDocumentPermission.BulkCreateOrUpdate(ctx, permMaps)
}
