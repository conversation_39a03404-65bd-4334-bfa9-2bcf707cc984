package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gitlab.com/goxp/cloud0/ginext"
)

func TestNewAutoDocProviderHandler(t *testing.T) {
	mockRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
	handler := NewAutoDocProviderHandler(mockRegistry)
	assert.NotNil(t, handler)
}

func TestAutoDocProviderHandler_ListProviders(t *testing.T) {
	mockRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
	handler := NewAutoDocProviderHandler(mockRegistry)

	// Create request directly
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/providers", nil)
	req := &ginext.Request{GinCtx: c}

	// Mock expectations
	mockRegistry.EXPECT().ListProviders().Return([]string{"internal", "gdrive"})
	mockRegistry.EXPECT().GetDefaultProvider().Return("internal")

	// Execute
	resp, err := handler.ListProviders(req)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify response data
	responseData := resp.Body.(*ginext.GeneralBody).Data.(*ListProvidersResponse)
	assert.Equal(t, []string{"internal", "gdrive"}, responseData.Providers)
	assert.Equal(t, "internal", responseData.DefaultProvider)
}

func TestAutoDocProviderHandler_SetDefaultProvider(t *testing.T) {
	mockRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
	handler := NewAutoDocProviderHandler(mockRegistry)

	// Create request directly
	requestBody := SetDefaultProviderRequest{Provider: "gdrive"}
	jsonBody, _ := json.Marshal(requestBody)

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/providers/default", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")
	req := &ginext.Request{GinCtx: c}

	// Mock expectations
	mockRegistry.EXPECT().IsProviderRegistered("gdrive").Return(true)
	mockRegistry.EXPECT().SetDefaultProvider("gdrive").Return(nil)

	// Execute
	resp, err := handler.SetDefaultProvider(req)

	// Verify
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify response data
	responseData := resp.Body.(*ginext.GeneralBody).Data.(*SetDefaultProviderResponse)
	assert.Equal(t, "gdrive", responseData.Provider)
	assert.Equal(t, "Default provider set successfully", responseData.Message)
}

func TestAutoDocProviderHandler_SetDefaultProvider_NotRegistered(t *testing.T) {
	mockRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
	handler := NewAutoDocProviderHandler(mockRegistry)

	// Mock expectations
	mockRegistry.EXPECT().IsProviderRegistered("unknown").Return(false)

	// Create request directly
	requestBody := SetDefaultProviderRequest{Provider: "unknown"}
	jsonBody, _ := json.Marshal(requestBody)

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/providers/default", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")
	req := &ginext.Request{GinCtx: c}

	// Execute
	resp, err := handler.SetDefaultProvider(req)

	// Verify
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "Provider 'unknown' is not registered")
}
