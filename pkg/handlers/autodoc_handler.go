package handlers

import (
	"net/http"
	"strings"

	"bilabl/docman/domain/model"
	"bilabl/docman/internal/service/autodoc"

	"code.mybil.net/gophers/gokit/clients"
	"code.mybil.net/gophers/gokit/components/entity"
	"gitlab.com/goxp/cloud0/ginext"
)

// AutoDocHandler handles automation rule API endpoints
type AutoDocHandler struct {
	autoDocService           autodoc.AutoDocService
	ruleExecutionEngine      autodoc.RuleExecutionEngine
	ruleMatchingService      autodoc.RuleMatchingService
	matterEventConsumer      autodoc.MatterEventConsumer
	clientEventConsumer      autodoc.ClientEventConsumer
	eventRuleMatchingService autodoc.EventRuleMatchingService
	clientClient             clients.ClientServiceClient
	matterClient             clients.MatterClient
	entityFetcher            entity.GenericFetcher
}

// NewAutoDocHandler creates a new AutoDocHandler
func NewAutoDocHandler(
	autoDocService autodoc.AutoDocService,
	ruleExecutionEngine autodoc.RuleExecutionEngine,
	ruleMatchingService autodoc.RuleMatchingService,
	matterEventConsumer autodoc.MatterEventConsumer,
	clientEventConsumer autodoc.ClientEventConsumer,
	eventRuleMatchingService autodoc.EventRuleMatchingService,
	clientClient clients.ClientServiceClient,
	matterClient clients.MatterClient,
	entityFetcher entity.GenericFetcher,
) *AutoDocHandler {
	return &AutoDocHandler{
		autoDocService:           autoDocService,
		ruleExecutionEngine:      ruleExecutionEngine,
		ruleMatchingService:      ruleMatchingService,
		matterEventConsumer:      matterEventConsumer,
		clientEventConsumer:      clientEventConsumer,
		eventRuleMatchingService: eventRuleMatchingService,
		clientClient:             clientClient,
		matterClient:             matterClient,
		entityFetcher:            entityFetcher,
	}
}

// CreateRuleRequest represents the request body for creating an automation rule
type CreateRuleRequest struct {
	Name         string                `json:"name" validate:"required,min=1,max=255,rule_name"`
	Description  string                `json:"description" validate:"max=1000"`
	TriggerType  string                `json:"trigger_type" validate:"required,oneof=matter.create matter.update client.create client.update"`
	TriggerRules model.TriggerRulesMap `json:"trigger_rules" validate:"trigger_rules"`
	RuleConfig   model.RuleConfigArray `json:"rule_config" validate:"required,min=1,rule_config"`
	IsActive     bool                  `json:"is_active"`
	CreatedUser  uint64                `json:"created_user"`
}

// CreateRuleResponse represents the response for creating an automation rule
type CreateRuleResponse struct {
	ID           uint64                `json:"id"`
	TenantID     uint64                `json:"tenant_id"`
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	TriggerType  string                `json:"trigger_type"`
	TriggerRules model.TriggerRulesMap `json:"trigger_rules"`
	RuleConfig   model.RuleConfigArray `json:"rule_config"`
	IsActive     bool                  `json:"is_active"`
	CreatedUser  uint64                `json:"created_user"`
	CreatedAt    string                `json:"created_at"`
	UpdatedAt    string                `json:"updated_at"`
	Message      string                `json:"message,omitempty"` // Extra message for warnings or info
}

// ListRulesResponse represents the response for listing automation rules
type ListRulesResponse struct {
	Rules []CreateRuleResponse `json:"rules"`
	Total int                  `json:"total"`
}

// UpdateRuleRequest represents the request body for updating an automation rule
type UpdateRuleRequest struct {
	Name         *string                `json:"name,omitempty" validate:"omitempty,min=1,max=255,rule_name"`
	Description  *string                `json:"description,omitempty" validate:"omitempty,max=1000"`
	TriggerType  *string                `json:"trigger_type,omitempty" validate:"omitempty,oneof=matter.create matter.update client.create client.update"`
	TriggerRules *model.TriggerRulesMap `json:"trigger_rules,omitempty" validate:"omitempty,trigger_rules"`
	RuleConfig   *model.RuleConfigArray `json:"rule_config,omitempty" validate:"omitempty,min=1,rule_config"`
	IsActive     *bool                  `json:"is_active,omitempty"`
	UpdatedUser  uint64                 `json:"updated_user"`
}

// ruleIDParams represents path parameters for rule operations
type ruleIDParams struct {
	RuleID uint64 `uri:"id" validate:"required,min=1"`
}

// CreateRule handles POST /api/autodoc/rules
func (h *AutoDocHandler) CreateRule(r *ginext.Request) (*ginext.Response, error) {
	var createReq CreateRuleRequest

	r.MustBind(&createReq)
	createReq.CreatedUser = ginext.Uint64UserID(r.GinCtx)

	// Additional business logic validation
	if err := validateCreateRuleRequest(createReq); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// Validate trigger rules content based on trigger type with change detection
	err, extraMsg := ValidateTriggerRulesContentWithChangeDetection(createReq.TriggerType, createReq.TriggerRules)
	if err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid trigger rules: "+err.Error())
	}

	// Create the rule model
	rule := &model.DocumentAutomationRule{
		TenantID:     tenantID,
		Name:         createReq.Name,
		Description:  createReq.Description,
		TriggerType:  createReq.TriggerType,
		TriggerRules: createReq.TriggerRules,
		RuleConfig:   createReq.RuleConfig,
		IsActive:     createReq.IsActive,
		CreatedUser:  createReq.CreatedUser,
	}

	// Create the rule using the service
	if err := h.autoDocService.CreateRule(r.Context(), rule); err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to create automation rule: "+err.Error())
	}

	// Return the created rule
	response := CreateRuleResponse{
		ID:           rule.ID,
		TenantID:     rule.TenantID,
		Name:         rule.Name,
		Description:  rule.Description,
		TriggerType:  rule.TriggerType,
		TriggerRules: rule.TriggerRules,
		RuleConfig:   rule.RuleConfig,
		IsActive:     rule.IsActive,
		CreatedUser:  rule.CreatedUser,
		CreatedAt:    rule.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    rule.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		Message:      extraMsg,
	}

	return ginext.NewResponseData(http.StatusCreated, response), nil
}

// GetRule handles GET /api/autodoc/rules/:id
func (h *AutoDocHandler) GetRule(r *ginext.Request) (*ginext.Response, error) {
	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	ctx := withTenantContext(r.Context(), tenantID)

	// Get the rule using the service
	rule, err := h.autoDocService.GetRule(ctx, params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if rule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Return the rule
	response := CreateRuleResponse{
		ID:           rule.ID,
		TenantID:     rule.TenantID,
		Name:         rule.Name,
		Description:  rule.Description,
		TriggerType:  rule.TriggerType,
		TriggerRules: rule.TriggerRules,
		RuleConfig:   rule.RuleConfig,
		IsActive:     rule.IsActive,
		CreatedUser:  rule.CreatedUser,
		CreatedAt:    rule.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    rule.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ListRules handles GET /api/autodoc/rules
func (h *AutoDocHandler) ListRules(r *ginext.Request) (*ginext.Response, error) {
	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// Get rules using the service
	rules, err := h.autoDocService.GetRulesByTenant(r.Context(), tenantID)
	if err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rules: "+err.Error())
	}

	// Convert to response format
	var responseRules []CreateRuleResponse
	for _, rule := range rules {
		responseRules = append(responseRules, CreateRuleResponse{
			ID:           rule.ID,
			TenantID:     rule.TenantID,
			Name:         rule.Name,
			Description:  rule.Description,
			TriggerType:  rule.TriggerType,
			TriggerRules: rule.TriggerRules,
			RuleConfig:   rule.RuleConfig,
			IsActive:     rule.IsActive,
			CreatedUser:  rule.CreatedUser,
			CreatedAt:    rule.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:    rule.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		})
	}

	response := ListRulesResponse{
		Rules: responseRules,
		Total: len(responseRules),
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// UpdateRule handles PUT /api/autodoc/rules/:id
func (h *AutoDocHandler) UpdateRule(r *ginext.Request) (*ginext.Response, error) {
	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	var updateReq UpdateRuleRequest
	r.MustBind(&updateReq)
	updateReq.UpdatedUser = ginext.Uint64UserID(r.GinCtx)

	// Additional business logic validation
	if err := validateUpdateRuleRequest(updateReq); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	ctx := withTenantContext(r.Context(), tenantID)

	// Get existing rule first
	existingRule, err := h.autoDocService.GetRule(ctx, params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if existingRule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Update fields if provided
	if updateReq.Name != nil {
		existingRule.Name = *updateReq.Name
	}
	if updateReq.Description != nil {
		existingRule.Description = *updateReq.Description
	}
	if updateReq.TriggerType != nil {
		existingRule.TriggerType = *updateReq.TriggerType
	}
	if updateReq.TriggerRules != nil {
		existingRule.TriggerRules = *updateReq.TriggerRules
	}
	if updateReq.RuleConfig != nil {
		existingRule.RuleConfig = *updateReq.RuleConfig
	}
	if updateReq.IsActive != nil {
		existingRule.IsActive = *updateReq.IsActive
	}
	existingRule.UpdatedUser = updateReq.UpdatedUser

	// Validate trigger rules content with change detection
	var extraMsg string
	if updateReq.TriggerType != nil && updateReq.TriggerRules != nil {
		err, msg := ValidateTriggerRulesContentWithChangeDetection(*updateReq.TriggerType, *updateReq.TriggerRules)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, "Invalid trigger rules: "+err.Error())
		}
		extraMsg = msg
	} else if updateReq.TriggerRules != nil {
		// If only trigger rules are updated, validate against existing trigger type
		err, msg := ValidateTriggerRulesContentWithChangeDetection(existingRule.TriggerType, *updateReq.TriggerRules)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, "Invalid trigger rules: "+err.Error())
		}
		extraMsg = msg
	} else if updateReq.TriggerType != nil {
		// If only trigger type is updated, validate against existing trigger rules
		err, msg := ValidateTriggerRulesContentWithChangeDetection(*updateReq.TriggerType, existingRule.TriggerRules)
		if err != nil {
			return nil, ginext.NewError(http.StatusBadRequest, "Trigger type incompatible with existing trigger rules: "+err.Error())
		}
		extraMsg = msg
	}

	// Update the rule using the service
	if err := h.autoDocService.UpdateRule(ctx, existingRule); err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to update automation rule: "+err.Error())
	}

	// Return the updated rule
	response := CreateRuleResponse{
		ID:           existingRule.ID,
		TenantID:     existingRule.TenantID,
		Name:         existingRule.Name,
		Description:  existingRule.Description,
		TriggerType:  existingRule.TriggerType,
		TriggerRules: existingRule.TriggerRules,
		RuleConfig:   existingRule.RuleConfig,
		IsActive:     existingRule.IsActive,
		CreatedUser:  existingRule.CreatedUser,
		CreatedAt:    existingRule.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    existingRule.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		Message:      extraMsg,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// DeleteRule handles DELETE /api/autodoc/rules/:id
func (h *AutoDocHandler) DeleteRule(r *ginext.Request) (*ginext.Response, error) {
	// Get rule ID from path parameter
	var params ruleIDParams
	if err := r.GinCtx.ShouldBindUri(&params); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid rule ID: "+err.Error())
	}

	// Validate rule ID
	if params.RuleID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "Rule ID must be greater than 0")
	}

	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}
	ctx := withTenantContext(r.Context(), tenantID)

	// Verify rule exists and belongs to tenant
	existingRule, err := h.autoDocService.GetRule(ctx, params.RuleID)
	if err != nil {
		if model.IsNotFound(err) {
			return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve automation rule: "+err.Error())
	}

	// Verify tenant access
	if existingRule.TenantID != tenantID {
		return nil, ginext.NewError(http.StatusNotFound, "Automation rule not found")
	}

	// Delete the rule using the service
	if err := h.autoDocService.DeleteRule(ctx, params.RuleID); err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to delete automation rule: "+err.Error())
	}

	return ginext.NewResponseData(http.StatusNoContent, nil), nil
}

// GetActiveRules handles GET /api/autodoc/rules/active
func (h *AutoDocHandler) GetActiveRules(r *ginext.Request) (*ginext.Response, error) {
	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}

	// Get active rules using the service
	rules, err := h.autoDocService.GetActiveRules(r.Context(), tenantID)
	if err != nil {
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve active automation rules: "+err.Error())
	}

	// Convert to response format
	var responseRules []CreateRuleResponse
	for _, rule := range rules {
		responseRules = append(responseRules, CreateRuleResponse{
			ID:           rule.ID,
			TenantID:     rule.TenantID,
			Name:         rule.Name,
			Description:  rule.Description,
			TriggerType:  rule.TriggerType,
			TriggerRules: rule.TriggerRules,
			RuleConfig:   rule.RuleConfig,
			IsActive:     rule.IsActive,
			CreatedUser:  rule.CreatedUser,
			CreatedAt:    rule.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:    rule.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		})
	}

	response := ListRulesResponse{
		Rules: responseRules,
		Total: len(responseRules),
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ListVariablesRequest represents the request for listing available template variables
type ListVariablesRequest struct {
	ObjectType int     `form:"object_type" binding:"required,oneof=1 3"`
	ObjectID   *uint64 `form:"object_id"`
}

// VariableInfo represents information about a template variable
type VariableInfo struct {
	Name        string      `json:"name"`
	Value       interface{} `json:"value"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
}

// ListVariablesResponse represents the response for listing template variables
type ListVariablesResponse struct {
	Variables  []VariableInfo `json:"variables"`
	ObjectType int            `json:"object_type"`
	ObjectID   *uint64        `json:"object_id,omitempty"`
}

// ListVariables handles GET /v3/autodoc/variables
func (h *AutoDocHandler) ListVariables(r *ginext.Request) (*ginext.Response, error) {
	// Get tenant ID from context
	tenantID := ginext.Uint64TenantID(r.GinCtx)
	if tenantID == 0 {
		return nil, ginext.NewError(http.StatusUnauthorized, "Tenant ID not found in context")
	}



	// Parse and validate request parameters
	var req ListVariablesRequest
	if err := r.GinCtx.ShouldBindQuery(&req); err != nil {
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid request parameters: "+err.Error())
	}

	// Additional validation for object_type
	if req.ObjectType != 1 && req.ObjectType != 3 {
		return nil, ginext.NewError(http.StatusBadRequest, "object_type must be 1 (client) or 3 (matter)")
	}

	// Validate object_id if provided
	if req.ObjectID != nil && *req.ObjectID == 0 {
		return nil, ginext.NewError(http.StatusBadRequest, "object_id must be greater than 0")
	}

	// Create variable service and get variables
	variableService := autodoc.NewVariableService(h.clientClient, h.matterClient, h.entityFetcher)
	variables, err := variableService.GetAvailableVariables(r.Context(), req.ObjectType, req.ObjectID, tenantID)
	if err != nil {
		// Check for specific error types to return appropriate HTTP status
		if strings.Contains(err.Error(), "unsupported object_type") {
			return nil, ginext.NewError(http.StatusBadRequest, err.Error())
		}
		if strings.Contains(err.Error(), "not found") {
			return nil, ginext.NewError(http.StatusNotFound, err.Error())
		}
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to retrieve variables: "+err.Error())
	}

	// Convert autodoc.VariableInfo to handler VariableInfo
	handlerVariables := make([]VariableInfo, len(variables))
	for i, v := range variables {
		handlerVariables[i] = VariableInfo{
			Name:        v.Name,
			Value:       v.Value,
			Description: v.Description,
			Category:    v.Category,
		}
	}



	response := ListVariablesResponse{
		Variables:  handlerVariables,
		ObjectType: req.ObjectType,
		ObjectID:   req.ObjectID,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}
