package handlers

import (
	"bilabl/docman/pkg/repositories"
	"context"
	"time"

	"gitlab.com/goxp/cloud0/ginext"
)

type InstallHandler struct {
	migrate *repositories.MigrationRepo
}

func NewMigrationHandler(repo *repositories.MigrationRepo) *InstallHandler {
	return &InstallHandler{repo}
}

func (h *InstallHandler) Install(r *ginext.Request) (*ginext.Response, error) {
	ctx, cancel := context.WithTimeout(r.Context(), 20*time.Second)
	defer cancel()
	h.migrate.MigrateDB(ctx)
	return ginext.NewResponse(204), nil
}
