package handlers

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strings"

	"bilabl/docman/domain/model"

	"github.com/go-playground/validator/v10"

	_ "gitlab.com/goxp/cloud0/ginext" // init ginext validator first

	"github.com/gin-gonic/gin/binding"
)

// ValidationError represents a detailed validation error
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// ValidationErrorResponse represents the response for validation errors
type ValidationErrorResponse struct {
	Message string            `json:"message"`
	Errors  []ValidationError `json:"errors"`
}

var (
	// Custom validator instance
	customValidator *validator.Validate

	// Regex patterns for validation
	ruleNamePattern    = regexp.MustCompile(`^[a-zA-Z0-9\s\-_\.\(\)]+$`)
	placeholderPattern = regexp.MustCompile(`\{[^}]+\}`)
)

// init initializes custom validator with custom validation functions
func init() {
	customValidator = binding.Validator.Engine().(*validator.Validate)

	// Register custom validation functions
	if err := customValidator.RegisterValidation("rule_name", validateRuleName); err != nil {
		panic(fmt.Sprintf("Failed to register rule_name validation: %v", err))
	}
	if err := customValidator.RegisterValidation("trigger_rules", validateTriggerRules); err != nil {
		panic(fmt.Sprintf("Failed to register trigger_rules validation: %v", err))
	}
	if err := customValidator.RegisterValidation("rule_config", validateRuleConfig); err != nil {
		panic(fmt.Sprintf("Failed to register rule_config validation: %v", err))
	}
	if err := customValidator.RegisterValidation("action_type", validateActionType); err != nil {
		panic(fmt.Sprintf("Failed to register action_type validation: %v", err))
	}
	if err := customValidator.RegisterValidation("path_with_placeholders", validatePathWithPlaceholders); err != nil {
		panic(fmt.Sprintf("Failed to register path_with_placeholders validation: %v", err))
	}

	// Register custom tag name function for better field names in errors
	customValidator.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

}

// validateRuleName validates rule name format
func validateRuleName(fl validator.FieldLevel) bool {
	name := fl.Field().String()
	if name == "" {
		return false
	}

	// Check pattern
	if !ruleNamePattern.MatchString(name) {
		return false
	}

	// Check for reasonable length and no leading/trailing spaces
	trimmed := strings.TrimSpace(name)
	return len(trimmed) > 0 && trimmed == name
}

// validateTriggerRules validates trigger rules structure
func validateTriggerRules(fl validator.FieldLevel) bool {
	triggerRules, ok := fl.Field().Interface().(model.TriggerRulesMap)
	if !ok {
		return false
	}

	// Empty trigger rules are allowed (match all events)
	if len(triggerRules) == 0 {
		return true
	}

	// Validate each rule
	for key, value := range triggerRules {
		// Key must be non-empty and valid
		if strings.TrimSpace(key) == "" {
			return false
		}

		// Value must be non-nil
		if value == nil {
			return false
		}

		// If value is string, must be non-empty
		if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
			return false
		}
	}

	return true
}

// validateRuleConfig validates rule configuration array
func validateRuleConfig(fl validator.FieldLevel) bool {
	ruleConfig, ok := fl.Field().Interface().(model.RuleConfigArray)
	if !ok {
		return false
	}

	// Must have at least one action
	if len(ruleConfig) == 0 {
		return false
	}

	// Validate each action
	for _, action := range ruleConfig {
		// ActionType must be valid
		if !isValidActionType(action.ActionType) {
			return false
		}

		// SourcePath must be non-empty
		if strings.TrimSpace(action.SourcePath) == "" {
			return false
		}

		// TargetPath must be non-empty
		if strings.TrimSpace(action.TargetPath) == "" {
			return false
		}

		// TargetPath should contain placeholders for dynamic actions
		if !placeholderPattern.MatchString(action.TargetPath) {
			// Allow static paths but warn in logs
			continue
		}
	}

	return true
}

// validateActionType validates action type
func validateActionType(fl validator.FieldLevel) bool {
	actionType := fl.Field().String()
	return isValidActionType(actionType)
}

// validatePathWithPlaceholders validates path contains valid placeholders
func validatePathWithPlaceholders(fl validator.FieldLevel) bool {
	path := fl.Field().String()
	if strings.TrimSpace(path) == "" {
		return false
	}

	// Find all placeholders
	matches := placeholderPattern.FindAllString(path, -1)
	for _, match := range matches {
		// Remove { and }
		placeholder := strings.TrimSpace(match[1 : len(match)-1])
		if placeholder == "" {
			return false
		}

		// Validate placeholder format (should be valid Go template syntax)
		if strings.Contains(placeholder, "{") || strings.Contains(placeholder, "}") {
			return false
		}
	}

	return true
}

// isValidActionType checks if action type is supported
func isValidActionType(actionType string) bool {
	validTypes := []string{
		"copy_file",
		"copy_folder",
		"generate_document",
	}

	for _, validType := range validTypes {
		if actionType == validType {
			return true
		}
	}

	return false
}

// ValidateStruct validates a struct using custom validator
func ValidateStruct(s interface{}) []ValidationError {
	var errors []ValidationError

	if customValidator == nil {
		return []ValidationError{{
			Field:   "validator",
			Tag:     "init",
			Value:   "nil",
			Message: "Custom validator not initialized",
		}}
	}

	err := customValidator.Struct(s)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			ve := ValidationError{
				Field:   err.Field(),
				Tag:     err.Tag(),
				Value:   fmt.Sprintf("%v", err.Value()),
				Message: getValidationMessage(err),
			}
			errors = append(errors, ve)
		}
	}

	return errors
}

// getValidationMessage returns user-friendly validation message
func getValidationMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fe.Field(), fe.Param())
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", fe.Field(), fe.Param())
	case "rule_name":
		return fmt.Sprintf("%s must contain only letters, numbers, spaces, hyphens, underscores, dots, and parentheses", fe.Field())
	case "trigger_rules":
		return fmt.Sprintf("%s must contain at least one valid trigger condition", fe.Field())
	case "rule_config":
		return fmt.Sprintf("%s must contain at least one valid action with proper source and target paths", fe.Field())
	case "action_type":
		return fmt.Sprintf("%s must be one of: copy_file, copy_folder, generate_document", fe.Field())
	case "path_with_placeholders":
		return fmt.Sprintf("%s contains invalid placeholder syntax", fe.Field())
	default:
		return fmt.Sprintf("%s is invalid", fe.Field())
	}
}

// ValidateJSON validates JSON structure and content
func ValidateJSON(data []byte, v interface{}) error {
	// First validate JSON syntax
	if !json.Valid(data) {
		return fmt.Errorf("invalid JSON format")
	}

	// Unmarshal to check structure
	if err := json.Unmarshal(data, v); err != nil {
		return fmt.Errorf("JSON structure error: %w", err)
	}

	return nil
}

// ValidateTriggerRulesContent validates trigger rules content based on trigger type
func ValidateTriggerRulesContent(triggerType string, triggerRules model.TriggerRulesMap) error {
	switch triggerType {
	case "matter.create":
		return validateMatterCreateRules(triggerRules)
	case "matter.update":
		return validateMatterUpdateRules(triggerRules)
	case "client.create":
		return validateClientCreateRules(triggerRules)
	case "client.update":
		return validateClientUpdateRules(triggerRules)
	default:
		return fmt.Errorf("unsupported trigger type: %s", triggerType)
	}
}

// validateMatterCreateRules validates rules for matter.create trigger
func validateMatterCreateRules(rules model.TriggerRulesMap) error {
	validFields := map[string]bool{
		"matter_type":                 true,
		"practice_area":               true,
		"client_type":                 true,
		"matter_status":               true,
		"extra.current.category_name": true,
		"extra.current.stage_text":    true,
	}

	for key := range rules {
		if !validFields[key] {
			return fmt.Errorf("invalid field for matter.create trigger: %s", key)
		}
	}

	return nil
}

// validateMatterUpdateRules validates rules for matter.update trigger
func validateMatterUpdateRules(rules model.TriggerRulesMap) error {
	validFields := map[string]bool{
		"matter_type":                  true,
		"practice_area":                true,
		"client_type":                  true,
		"matter_status":                true,
		"extra.current.category_name":  true,
		"extra.previous.category_name": true, // For update comparison
		"extra.current.stage_text":     true,
		"extra.previous.stage_text":    true, // For update comparison
	}

	for key := range rules {
		if !validFields[key] {
			return fmt.Errorf("invalid field for matter.update trigger: %s", key)
		}
	}

	return nil
}

// validateClientCreateRules validates rules for client.create trigger
func validateClientCreateRules(rules model.TriggerRulesMap) error {
	validFields := map[string]bool{
		"client_type":              true,
		"client_status":            true,
		"industry":                 true,
		"region":                   true,
		"extra.current.stage_text": true,
	}

	for key := range rules {
		if !validFields[key] {
			return fmt.Errorf("invalid field for client.create trigger: %s", key)
		}
	}

	return nil
}

// validateClientUpdateRules validates rules for client.update trigger
func validateClientUpdateRules(rules model.TriggerRulesMap) error {
	validFields := map[string]bool{
		"client_type":               true,
		"client_status":             true,
		"industry":                  true,
		"region":                    true,
		"extra.current.stage_text":  true,
		"extra.previous.stage_text": true, // For update comparison
	}

	for key := range rules {
		if !validFields[key] {
			return fmt.Errorf("invalid field for client.update trigger: %s", key)
		}
	}

	return nil
}

// ValidateTriggerRulesContentWithChangeDetection validates trigger rules and returns extra message for update rules without changes
func ValidateTriggerRulesContentWithChangeDetection(triggerType string, triggerRules model.TriggerRulesMap) (error, string) {
	// First validate the basic trigger rules content
	if err := ValidateTriggerRulesContent(triggerType, triggerRules); err != nil {
		return err, ""
	}

	// For update triggers, check if there are change detection rules
	if triggerType == "matter.update" || triggerType == "client.update" {
		hasChangeDetection := false

		// Check if any rule uses previous/current comparison
		for key := range triggerRules {
			if strings.Contains(key, "extra.previous.") ||
				(strings.Contains(key, "extra.current.") && hasCorrespondingPrevious(key, triggerRules)) {
				hasChangeDetection = true
				break
			}
		}

		if !hasChangeDetection {
			extraMsg := fmt.Sprintf("Warning: %s rule does not check for data changes. Consider adding previous/current field comparisons to ensure rule only triggers when data actually changes.", triggerType)
			return nil, extraMsg
		}
	}

	return nil, ""
}

// hasCorrespondingPrevious checks if a current field has a corresponding previous field in the rules
func hasCorrespondingPrevious(currentKey string, rules model.TriggerRulesMap) bool {
	if !strings.Contains(currentKey, "extra.current.") {
		return false
	}

	// Convert current key to previous key
	previousKey := strings.Replace(currentKey, "extra.current.", "extra.previous.", 1)
	_, exists := rules[previousKey]
	return exists
}

// validateCreateRuleRequest performs additional business logic validation for CreateRuleRequest
func validateCreateRuleRequest(req CreateRuleRequest) error {
	// Validate rule name format
	if !ruleNamePattern.MatchString(req.Name) {
		return fmt.Errorf("rule name contains invalid characters")
	}

	// Check for reasonable length and no leading/trailing spaces
	trimmed := strings.TrimSpace(req.Name)
	if len(trimmed) == 0 || trimmed != req.Name {
		return fmt.Errorf("rule name cannot have leading/trailing spaces")
	}

	// Empty trigger rules are allowed (match all events)
	// No validation needed for empty trigger rules

	// Validate each trigger rule
	for key, value := range req.TriggerRules {
		if strings.TrimSpace(key) == "" {
			return fmt.Errorf("trigger rule key cannot be empty")
		}
		if value == nil {
			return fmt.Errorf("trigger rule value cannot be nil")
		}
		if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
			return fmt.Errorf("trigger rule value cannot be empty")
		}
	}

	// Validate rule config is not empty
	if len(req.RuleConfig) == 0 {
		return fmt.Errorf("rule config cannot be empty")
	}

	// Validate each action in rule config
	for i, action := range req.RuleConfig {
		if !isValidActionType(action.ActionType) {
			return fmt.Errorf("invalid action type '%s' at index %d", action.ActionType, i)
		}
		if strings.TrimSpace(action.SourcePath) == "" {
			return fmt.Errorf("source path cannot be empty at index %d", i)
		}
		if strings.TrimSpace(action.TargetPath) == "" {
			return fmt.Errorf("target path cannot be empty at index %d", i)
		}
	}

	return nil
}

// validateUpdateRuleRequest performs additional business logic validation for UpdateRuleRequest
func validateUpdateRuleRequest(req UpdateRuleRequest) error {
	// Validate rule name format if provided
	if req.Name != nil {
		if !ruleNamePattern.MatchString(*req.Name) {
			return fmt.Errorf("rule name contains invalid characters")
		}
		trimmed := strings.TrimSpace(*req.Name)
		if len(trimmed) == 0 || trimmed != *req.Name {
			return fmt.Errorf("rule name cannot have leading/trailing spaces")
		}
	}

	// Validate trigger rules if provided
	if req.TriggerRules != nil {
		// Empty trigger rules are allowed (match all events)
		for key, value := range *req.TriggerRules {
			if strings.TrimSpace(key) == "" {
				return fmt.Errorf("trigger rule key cannot be empty")
			}
			if value == nil {
				return fmt.Errorf("trigger rule value cannot be nil")
			}
			if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
				return fmt.Errorf("trigger rule value cannot be empty")
			}
		}
	}

	// Validate rule config if provided
	if req.RuleConfig != nil {
		if len(*req.RuleConfig) == 0 {
			return fmt.Errorf("rule config cannot be empty")
		}
		for i, action := range *req.RuleConfig {
			if !isValidActionType(action.ActionType) {
				return fmt.Errorf("invalid action type '%s' at index %d", action.ActionType, i)
			}
			if strings.TrimSpace(action.SourcePath) == "" {
				return fmt.Errorf("source path cannot be empty at index %d", i)
			}
			if strings.TrimSpace(action.TargetPath) == "" {
				return fmt.Errorf("target path cannot be empty at index %d", i)
			}
		}
	}

	return nil
}
