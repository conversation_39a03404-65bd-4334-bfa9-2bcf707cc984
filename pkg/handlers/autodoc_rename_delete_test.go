package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"bilabl/docman/internal/service/autodoc"
	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

func TestAutoDocFileHandler_UpdateFile(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		fileID         string
		provider       string
		requestBody    UpdateFileRequest
		setupMocks     func(*autodocmocks.MockFileProvider)
		expectedStatus int
		expectedError  string
	}{
		{
			name:     "successful internal file rename",
			fileID:   "123",
			provider: "internal",
			requestBody: UpdateFileRequest{
				Name:     "Updated Document.pdf",
				Provider: "internal",
			},
			setupMocks: func(mockProvider *autodocmocks.MockFileProvider) {
				mockProvider.EXPECT().
					UpdateFile(mock.Anything, &autodoc.UpdateFileRequest{
						ID:       "123",
						TenantID: uint64(1),
						Name:     "Updated Document.pdf",
					}).
					Return(&autodoc.UpdateFileResponse{
						ID:       "123",
						Name:     "Updated Document.pdf",
						Size:     1024,
						Provider: "internal",
						Message:  "File updated successfully in internal storage",
					}, nil).
					Once()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:     "successful google drive file rename",
			fileID:   "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
			provider: "gdrive",
			requestBody: UpdateFileRequest{
				Name:     "Renamed Contract.docx",
				Provider: "gdrive",
			},
			setupMocks: func(mockProvider *autodocmocks.MockFileProvider) {
				mockProvider.EXPECT().
					UpdateFile(mock.Anything, &autodoc.UpdateFileRequest{
						ID:       "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
						TenantID: uint64(1),
						Name:     "Renamed Contract.docx",
					}).
					Return(&autodoc.UpdateFileResponse{
						ID:       "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
						Name:     "Renamed Contract.docx",
						Size:     2048,
						Provider: "gdrive",
						Message:  "File updated successfully in Google Drive",
					}, nil).
					Once()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:     "missing file ID",
			fileID:   "",
			provider: "internal",
			requestBody: UpdateFileRequest{
				Name: "Should Fail",
			},
			setupMocks:     func(mockProvider *autodocmocks.MockFileProvider) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "File ID is required",
		},
		{
			name:     "empty name",
			fileID:   "123",
			provider: "internal",
			requestBody: UpdateFileRequest{
				Name: "",
			},
			setupMocks: func(mockProvider *autodocmocks.MockFileProvider) {
				mockProvider.EXPECT().
					UpdateFile(mock.Anything, &autodoc.UpdateFileRequest{
						ID:       "123",
						TenantID: uint64(1),
						Name:     "",
					}).
					Return(&autodoc.UpdateFileResponse{
						ID:       "123",
						Name:     "",
						Size:     0,
						Provider: "internal",
						Message:  "File updated successfully in internal storage",
					}, nil).
					Once()
			},
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
			mockUploadRegistry := autodocmocks.NewMockUploadProviderRegistry(t)
			mockFileRegistry := autodocmocks.NewMockFileProviderRegistry(t)
			mockFileProvider := autodocmocks.NewMockFileProvider(t)

			// Setup file provider mock
			if tt.provider != "" && tt.fileID != "" {
				mockFileRegistry.EXPECT().
					GetProvider(tt.provider).
					Return(mockFileProvider, nil).
					Maybe()
				tt.setupMocks(mockFileProvider)
			}

			// Create handler
			handler := NewAutoDocFileHandler(mockDocRegistry, mockUploadRegistry, mockFileRegistry)

			// Create request
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PUT", "/v3/autodoc/files/"+tt.fileID+"?provider="+tt.provider, bytes.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")
			// Set headers that actor.FromGinCtx() expects
			req.Header.Set("x-user-id", "1")
			req.Header.Set("x-tenant-id", "1")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = gin.Params{{Key: "id", Value: tt.fileID}}

			// Also set query parameters for provider
			c.Request.URL.RawQuery = "provider=" + tt.provider

			// Create ginext request
			ginextReq := &ginext.Request{GinCtx: c}

			// Execute
			resp, err := handler.UpdateFile(ginextReq)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else if tt.expectedStatus == http.StatusOK {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				// Verify response structure
				if resp.Body != nil {
					responseData := resp.Body.(*ginext.GeneralBody).Data.(*UpdateFileResponse)
					assert.Equal(t, tt.requestBody.Name, responseData.Name)
					assert.NotEmpty(t, responseData.ID)
					assert.NotEmpty(t, responseData.Provider)
				}
			}

			// Verify mock expectations
			mockFileRegistry.AssertExpectations(t)
			mockFileProvider.AssertExpectations(t)
		})
	}
}

func TestAutoDocFileHandler_DeleteFileRename(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		fileID         string
		provider       string
		setupMocks     func(*autodocmocks.MockFileProvider)
		expectedStatus int
		expectedError  string
	}{
		{
			name:     "successful internal file deletion",
			fileID:   "123",
			provider: "internal",
			setupMocks: func(mockProvider *autodocmocks.MockFileProvider) {
				mockProvider.EXPECT().
					DeleteFile(mock.Anything, &autodoc.FileDeleteRequest{
						ID:       "123",
						TenantID: uint64(1),
					}).
					Return(&autodoc.FileDeleteResponse{
						ID:       "123",
						Provider: "internal",
						Message:  "File deleted successfully from internal storage",
					}, nil).
					Once()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:     "successful google drive file deletion",
			fileID:   "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
			provider: "gdrive",
			setupMocks: func(mockProvider *autodocmocks.MockFileProvider) {
				mockProvider.EXPECT().
					DeleteFile(mock.Anything, &autodoc.FileDeleteRequest{
						ID:       "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
						TenantID: uint64(1),
					}).
					Return(&autodoc.FileDeleteResponse{
						ID:       "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
						Provider: "gdrive",
						Message:  "File deleted successfully from Google Drive",
					}, nil).
					Once()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "missing file ID",
			fileID:         "",
			provider:       "internal",
			setupMocks:     func(mockProvider *autodocmocks.MockFileProvider) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "File ID is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDocRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
			mockUploadRegistry := autodocmocks.NewMockUploadProviderRegistry(t)
			mockFileRegistry := autodocmocks.NewMockFileProviderRegistry(t)
			mockFileProvider := autodocmocks.NewMockFileProvider(t)

			// Setup file provider mock
			if tt.provider != "" && tt.fileID != "" {
				mockFileRegistry.EXPECT().
					GetProvider(tt.provider).
					Return(mockFileProvider, nil).
					Maybe()
				tt.setupMocks(mockFileProvider)
			}

			// Create handler
			handler := NewAutoDocFileHandler(mockDocRegistry, mockUploadRegistry, mockFileRegistry)

			// Create request
			req := httptest.NewRequest("DELETE", "/v3/autodoc/files/"+tt.fileID+"?provider="+tt.provider, nil)
			// Set headers that actor.FromGinCtx() expects
			req.Header.Set("x-user-id", "1")
			req.Header.Set("x-tenant-id", "1")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = gin.Params{{Key: "id", Value: tt.fileID}}

			// Also set query parameters for provider
			c.Request.URL.RawQuery = "provider=" + tt.provider

			// Create ginext request
			ginextReq := &ginext.Request{GinCtx: c}

			// Execute
			resp, err := handler.DeleteFile(ginextReq)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else if tt.expectedStatus == http.StatusOK {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				// Verify response structure
				if resp.Body != nil {
					responseData := resp.Body.(*ginext.GeneralBody).Data.(*DeleteFileResponse)
					assert.Equal(t, tt.fileID, responseData.ID)
					assert.NotEmpty(t, responseData.Provider)
					assert.NotEmpty(t, responseData.Message)
				}
			}

			// Verify mock expectations
			mockFileRegistry.AssertExpectations(t)
			mockFileProvider.AssertExpectations(t)
		})
	}
}
