package handlers

import (
	"testing"

	autodocmocks "bilabl/docman/mocks/service/autodoc"

	"github.com/stretchr/testify/assert"
)

func TestNewAutoDocFileHandler(t *testing.T) {
	mockDocRegistry := autodocmocks.NewMockDocumentServiceRegistry(t)
	mockUploadRegistry := autodocmocks.NewMockUploadProviderRegistry(t)
	mockFileRegistry := autodocmocks.NewMockFileProviderRegistry(t)
	handler := NewAutoDocFileHandler(mockDocRegistry, mockUploadRegistry, mockFileRegistry)
	assert.NotNil(t, handler)
}

// TODO: Rewrite tests for new provider-based architecture
func TestAutoDocFileHandler_ListFiles(t *testing.T) {
	t.Skip("TODO: Rewrite test for new provider-based architecture with reqActor and FileProvider")
}

func TestCreateFileRequest_StringParentID(t *testing.T) {
	// Test that CreateFileRequest accepts string ParentID for multi-provider support
	tests := []struct {
		name     string
		parentID string
		expected string
	}{
		{
			name:     "numeric string parent ID",
			parentID: "123",
			expected: "123",
		},
		{
			name:     "external provider parent ID",
			parentID: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
			expected: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
		},
		{
			name:     "empty parent ID",
			parentID: "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := CreateFileRequest{
				Name:     "Test Folder",
				DocType:  1,
				ParentID: tt.parentID,
				Provider: "internal",
			}

			assert.Equal(t, tt.expected, req.ParentID)
			assert.Equal(t, "Test Folder", req.Name)
			assert.Equal(t, 1, req.DocType)
		})
	}
}

func TestAutoDocFileHandler_DeleteFile(t *testing.T) {
	t.Skip("TODO: Rewrite test for new provider-based architecture with reqActor and FileProvider")
}
