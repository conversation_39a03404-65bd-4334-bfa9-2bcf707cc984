package handlers

import (
	"bilabl/docman/domain/model"
	"context"
	"github.com/gookit/goutil/arrutil"
)

func (h *SharepointHandler) updatePermissions(ctx context.Context, spParams *sharepointParams, drive *model.DocumentMapping, currentPermOwners []*model.DocumentPermissionMapping, lastOwners, currentOwners []model.OwnerUser) error {
	removedOwners := model.FindMissingOwner(currentOwners, lastOwners)

	ownersToAdd := arrutil.Filter(currentOwners, func(o model.OwnerUser) bool {
		_, err := arrutil.Find(currentPermOwners, func(p *model.DocumentPermissionMapping) bool {
			return p.Email == o.Email
		})
		return err != nil
	})
	permsToRemove := arrutil.Filter(currentPermOwners, func(p *model.DocumentPermissionMapping) bool {
		_, err := arrutil.Find(removedOwners, func(o model.OwnerUser) bool {
			return p.Email == o.Email
		})
		return err == nil
	})

	// add permissions
	if err := h.addPermissions(ctx, spParams, drive, ownersToAdd); err != nil {
		return err
	}

	// remove permissions
	if err := h.removePermissions(ctx, spParams, drive, permsToRemove); err != nil {
		return err
	}

	return nil
}

func (h *SharepointHandler) addPermissions(ctx context.Context, spParams *sharepointParams, drive *model.DocumentMapping, ownersToAdd []model.OwnerUser) error {
	if len(ownersToAdd) == 0 {
		return nil
	}

	emails := arrutil.Map(ownersToAdd, func(o model.OwnerUser) (string, bool) {
		return o.Email, true
	})

	if len(emails) == 0 {
		return nil
	}

	perms, err := h.spClient.InvitePermissionDriveItem(ctx, spParams.SiteDriveID, spParams.AccessToken, drive.DriveID, emails)
	if err != nil {
		return err
	}

	return h.handlePermissionMapping(ctx, &perms, spParams.TenantID, drive.DriveID)
}

func (h *SharepointHandler) removePermissions(
	ctx context.Context,
	spParams *sharepointParams,
	drive *model.DocumentMapping,
	ownersToRemove []*model.DocumentPermissionMapping,
) error {

	if len(ownersToRemove) == 0 {
		return nil
	}

	for _, perm := range ownersToRemove {

		// not able to remove permission without permID currently
		// keep this log for future reference
		if perm.PermID == "" {
			continue
		}

		if err := h.spClient.DeletePermissionDriveItem(ctx, spParams.SiteDriveID, spParams.AccessToken, drive.DriveID, perm.PermID); err != nil {
			return err
		}
		if err := h.rDocumentPermission.Delete(ctx, perm); err != nil {
			return err
		}
	}
	return nil
}
