package handlers

type Setting struct {
	DBDebug        bool   `env:"DB_DEBUG" envDefault:"false"`
	GlobURL        string `env:"GLOB_URL" envDefault:"http://glob"`
	HrglassidURL   string `env:"HRGLASSID_URL" envDefault:"http://hrglassid"`
	ConsumerURL    string `env:"CONSUMER_URL" envDefault:"http://consumer/events"`
	SPClientID     string `env:"SP_CLIENT_ID" envDefault:"sp_client_id_fake"`
	SPClientSecret string `env:"SP_CLIENT_SECRET" envDefault:"sp_client_secret_fake"`
	SPScope        string `env:"SP_SCOPE" envDefault:"https://graph.microsoft.com/.default"`
	SPBaseUrl      string `env:"SP_BASE_URL" envDefault:"https://graph.microsoft.com/v1.0"`
	GatewayBaseURL string `env:"GATEWAY_BASE_URL"`
	PublicBaseUrl  string `env:"PUBLIC_BASE_URL" envDefault:"https://stg-api.bilabl.io/docman"`

	// Google Drive Permission Settings
	GDrivePermissionRole         string `env:"GDRIVE_PERMISSION_ROLE" envDefault:"writer"`
	GDrivePermissionRetryCount   int    `env:"GDRIVE_PERMISSION_RETRY_COUNT" envDefault:"3"`
	GDrivePermissionRetryDelayMs int    `env:"GDRIVE_PERMISSION_RETRY_DELAY_MS" envDefault:"1000"`
	GDriveSyncOnCreate           bool   `env:"GDRIVE_SYNC_ON_CREATE" envDefault:"true"`
	GDriveSyncOnUpdate           bool   `env:"GDRIVE_SYNC_ON_UPDATE" envDefault:"true"`
	GDrivePermissionTimeout      int    `env:"GDRIVE_PERMISSION_TIMEOUT" envDefault:"30"`

	// AutoDoc Configuration (Legacy support - use config package for new features)
	AutoDocEnabled           bool   `env:"AUTODOC_ENABLED" envDefault:"false"`
	AutoDocDefaultProvider   string `env:"AUTODOC_DEFAULT_PROVIDER" envDefault:"internal"`
	AutoDocMaxRulesPerTenant int    `env:"AUTODOC_MAX_RULES_PER_TENANT" envDefault:"100"`

	// AutoDoc Feature Flags (Legacy support)
	FeatureAutoDocRules     bool `env:"FEATURE_AUTODOC_RULES" envDefault:"true"`
	FeatureAutoDocProviders bool `env:"FEATURE_AUTODOC_PROVIDERS" envDefault:"true"`
	FeatureAutoDocFiles     bool `env:"FEATURE_AUTODOC_FILES" envDefault:"true"`
	FeatureMultiProvider    bool `env:"FEATURE_MULTI_PROVIDER" envDefault:"false"`

	// Coordination Settings (always enabled)
	CoordinationFolderCreationTimeout int  `env:"COORDINATION_FOLDER_CREATION_TIMEOUT" envDefault:"300"`  // 5 minutes in seconds
	CoordinationCleanupInterval       int  `env:"COORDINATION_CLEANUP_INTERVAL" envDefault:"3600"`        // 1 hour in seconds
	CoordinationDeprecateDirectCalls  bool `env:"COORDINATION_DEPRECATE_DIRECT_CALLS" envDefault:"false"` // Deprecate direct provider calls
}
