package handlers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/repositories"

	"github.com/gookit/goutil/arrutil"
	"gitlab.com/goxp/cloud0/logger"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/googleapi"
)

// GDrivePermissionHandler handles Google Drive permission synchronization
type GDrivePermissionHandler struct {
	gdriveClient        gdrive.DriveClient
	rDocumentPermission repositories.DocumentPermissionMappingRepository
	rDocumentMapping    repositories.DocumentMappingRepository
	rDocumentSetting    repositories.DocumentSettingRepository
	defaultConfig       PermissionHandlerConfig
}

// PermissionHandlerConfig holds configuration for permission operations
type PermissionHandlerConfig struct {
	DefaultRole         string
	RetryCount          int
	RetryDelayMs        int
	SyncOnCreate        bool
	SyncOnUpdate        bool
	TimeoutSeconds      int
	EnableBatchSync     bool
	BatchSize           int
	FailureNotification bool
}

// NewGDrivePermissionHandler creates a new Google Drive permission handler
func NewGDrivePermissionHandler(
	gdriveClient gdrive.DriveClient,
	rDocumentPermission repositories.DocumentPermissionMappingRepository,
	rDocumentMapping repositories.DocumentMappingRepository,
	rDocumentSetting repositories.DocumentSettingRepository,
) *GDrivePermissionHandler {
	return &GDrivePermissionHandler{
		gdriveClient:        gdriveClient,
		rDocumentPermission: rDocumentPermission,
		rDocumentMapping:    rDocumentMapping,
		rDocumentSetting:    rDocumentSetting,
		defaultConfig: PermissionHandlerConfig{
			DefaultRole:         "writer",
			RetryCount:          3,
			RetryDelayMs:        1000,
			SyncOnCreate:        true,
			SyncOnUpdate:        true,
			TimeoutSeconds:      30,
			EnableBatchSync:     false,
			BatchSize:           10,
			FailureNotification: true,
		},
	}
}

// SyncGoogleDrivePermissions synchronizes Google Drive permissions following SharePoint pattern
// This is a wrapper method that maintains backward compatibility with existing interface
// Legacy callers should migrate to SyncGoogleDrivePermissionsWithClientID for accurate clientID tracking
func (h *GDrivePermissionHandler) SyncGoogleDrivePermissions(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string) error {
	// For legacy compatibility, pass clientID = 0 (no matter permission preservation)
	return h.syncWithClientID(ctx, tenantID, objectType, objectID, ownerEmails, 0)
}

// SyncGoogleDrivePermissionsWithClientID performs permission sync with explicit clientID
// This method should be used by new callers to ensure correct clientID is passed
// clientID should be: 0 for client folders, actual clientID for matter folders
func (h *GDrivePermissionHandler) SyncGoogleDrivePermissionsWithClientID(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string, clientID uint64) error {
	return h.syncWithClientID(ctx, tenantID, objectType, objectID, ownerEmails, clientID)
}

// syncWithClientID is the internal method that performs the actual permission sync with explicit clientID
func (h *GDrivePermissionHandler) syncWithClientID(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string, clientID uint64) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.SyncGoogleDrivePermissions")
	log.Infof("Starting permission sync for %s %d with %d owners", objectType, objectID, len(ownerEmails))

	// 1. Get document mapping
	mapping, err := h.getDocumentMapping(ctx, tenantID, "google", objectType, objectID)
	if err != nil {
		return fmt.Errorf("failed to get document mapping: %w", err)
	}

	// clientID is passed as parameter

	// 2. Get current permission mappings from database
	currentPermOwners, err := h.rDocumentPermission.Find(ctx, &repositories.PermMapFindArgs{
		TenantID: &tenantID,
		DriveID:  &mapping.DriveID,
		Provider: &[]string{model.DocProviderGoogle}, // Only get Google Drive permissions
	})
	if err != nil {
		return fmt.Errorf("failed to get current permission mappings: %w", err)
	}

	// 3. Compute differences
	toAdd, toRemove := h.computePermissionDiff(currentPermOwners, ownerEmails)

	log.Infof("Permission diff: %d to add, %d to remove", len(toAdd), len(toRemove))

	// 4. Apply changes
	if len(toAdd) > 0 {
		if err := h.addPermissions(ctx, mapping, toAdd, clientID); err != nil {
			return fmt.Errorf("failed to add permissions: %w", err)
		}
	}

	if len(toRemove) > 0 {
		if err := h.removePermissions(ctx, mapping, toRemove); err != nil {
			return fmt.Errorf("failed to remove permissions: %w", err)
		}

		// If this is a client folder removal, check for matter permissions that need preservation
		if objectType == "client" {
			if err := h.preserveMatterPermissions(ctx, tenantID, objectID, toRemove); err != nil {
				log.Warnf("Failed to preserve matter permissions after client removal: %v", err)
				// Continue execution even if matter preservation fails
			}
		}
	}

	log.Infof("Permission sync completed successfully")
	return nil
}

// getDocumentMapping retrieves document mapping for the given object
func (h *GDrivePermissionHandler) getDocumentMapping(ctx context.Context, tenantID uint64, provider, objectType string, objectID uint64) (*model.DocumentMapping, error) {
	mapping, err := h.rDocumentMapping.FirstObjectMapping(ctx, objectType, provider, tenantID, objectID, 0)
	if err != nil {
		return nil, fmt.Errorf("document mapping not found for %s %d: %w", objectType, objectID, err)
	}

	return mapping, nil
}

// getEffectiveConfig retrieves the effective permission configuration for a tenant
func (h *GDrivePermissionHandler) getEffectiveConfig(ctx context.Context, tenantID uint64) PermissionHandlerConfig {
	// Start with default configuration
	config := h.defaultConfig

	// Try to get tenant-specific configuration
	setting, err := h.rDocumentSetting.GetValueByKey(ctx, tenantID, model.KeyGdriveConfig)
	if err != nil {
		// If no tenant config found, return defaults
		return config
	}

	var gdriveConfig model.GDriveConfig
	if err := gdriveConfig.FromJSON(setting.Value); err != nil {
		// If config parsing fails, return defaults
		return config
	}

	// Override with tenant-specific permission config if available
	if gdriveConfig.PermissionConfig != nil {
		if gdriveConfig.PermissionConfig.DefaultRole != "" {
			config.DefaultRole = gdriveConfig.PermissionConfig.DefaultRole
		}
		if gdriveConfig.PermissionConfig.RetryCount > 0 {
			config.RetryCount = gdriveConfig.PermissionConfig.RetryCount
		}
		if gdriveConfig.PermissionConfig.RetryDelayMs > 0 {
			config.RetryDelayMs = gdriveConfig.PermissionConfig.RetryDelayMs
		}
		if gdriveConfig.PermissionConfig.TimeoutSeconds > 0 {
			config.TimeoutSeconds = gdriveConfig.PermissionConfig.TimeoutSeconds
		}
		config.SyncOnCreate = gdriveConfig.PermissionConfig.SyncOnCreate
		config.SyncOnUpdate = gdriveConfig.PermissionConfig.SyncOnUpdate
		config.EnableBatchSync = gdriveConfig.PermissionConfig.EnableBatchSync
		if gdriveConfig.PermissionConfig.BatchSize > 0 {
			config.BatchSize = gdriveConfig.PermissionConfig.BatchSize
		}
		config.FailureNotification = gdriveConfig.PermissionConfig.FailureNotification
	}

	return config
}

// computePermissionDiff computes the difference between current and desired permissions
func (h *GDrivePermissionHandler) computePermissionDiff(currentPerms []*model.DocumentPermissionMapping, desiredEmails []string) ([]string, []*model.DocumentPermissionMapping) {
	// Find emails to add (in desired but not in current)
	toAdd := arrutil.Filter(desiredEmails, func(email string) bool {
		_, err := arrutil.Find(currentPerms, func(p *model.DocumentPermissionMapping) bool {
			return p.Email == email
		})
		return err != nil // Not found in current
	})

	// Find permissions to remove (in current but not in desired)
	toRemove := arrutil.Filter(currentPerms, func(p *model.DocumentPermissionMapping) bool {
		_, err := arrutil.Find(desiredEmails, func(email string) bool {
			return p.Email == email
		})
		return err != nil // Not found in desired
	})

	return toAdd, toRemove
}

// addPermissions adds new permissions to Google Drive
func (h *GDrivePermissionHandler) addPermissions(ctx context.Context, mapping *model.DocumentMapping, emails []string, clientID uint64) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.addPermissions")

	if len(emails) == 0 {
		return nil
	}

	log.Infof("Adding permissions for %d emails to drive %s", len(emails), mapping.DriveID)

	var failedEmails []string
	var lastError error
	successCount := 0

	for _, email := range emails {
		if err := h.addSinglePermissionWithRetry(ctx, mapping, email, clientID); err != nil {
			failedEmails = append(failedEmails, email)
			lastError = err
			log.Errorf("Failed to add permission for %s: %v", email, err)
			continue // Continue processing remaining emails
		}
		successCount++
		log.Infof("Successfully added permission for %s", email)
	}

	// Log summary
	log.Infof("Permission addition summary: %d successful, %d failed, %d total", successCount, len(failedEmails), len(emails))

	// Return error only if all operations failed
	if len(failedEmails) == len(emails) {
		return fmt.Errorf("failed to add permissions for all %d emails, last error: %w", len(emails), lastError)
	}

	// Return partial failure error if some failed
	if len(failedEmails) > 0 {
		return fmt.Errorf("failed to add permissions for %d out of %d emails: %v, last error: %w", len(failedEmails), len(emails), failedEmails, lastError)
	}

	return nil
}

// addSinglePermissionWithRetry adds a single permission with retry logic
func (h *GDrivePermissionHandler) addSinglePermissionWithRetry(ctx context.Context, mapping *model.DocumentMapping, email string, clientID uint64) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.addSinglePermissionWithRetry")
	var lastErr error

	// Get configuration for this tenant
	config := h.getEffectiveConfig(ctx, mapping.TenantID)

	for attempt := 0; attempt <= config.RetryCount; attempt++ {
		if attempt > 0 {
			// Exponential backoff
			delay := time.Duration(config.RetryDelayMs*(1<<(attempt-1))) * time.Millisecond
			time.Sleep(delay)
		}

		permission := &drive.Permission{
			Type:         "user",
			Role:         config.DefaultRole,
			EmailAddress: email,
		}

		result, err := h.gdriveClient.CreatePermission(ctx, mapping.DriveID, permission)
		if err != nil {
			lastErr = err

			// Check if this error should be retried
			if !h.shouldRetryPermissionError(err) {
				log.Warnf("Non-retryable error for %s: %v", email, err)
				return fmt.Errorf("failed to add permission for %s: %w", email, err)
			}

			log.Warnf("Retryable error for %s (attempt %d/%d): %v", email, attempt+1, config.RetryCount+1, err)
			continue
		}

		// Store mapping in database
		if err := h.storePermissionMapping(ctx, mapping.TenantID, mapping.DriveID, email, result.Id, clientID); err != nil {
			lastErr = err
			continue
		}

		return nil // Success
	}

	return fmt.Errorf("failed to add permission for %s after %d attempts: %w", email, config.RetryCount, lastErr)
}

// removePermissions removes permissions from Google Drive
func (h *GDrivePermissionHandler) removePermissions(ctx context.Context, mapping *model.DocumentMapping, permsToRemove []*model.DocumentPermissionMapping) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.removePermissions")

	if len(permsToRemove) == 0 {
		return nil
	}

	log.Infof("Removing %d permissions from drive %s", len(permsToRemove), mapping.DriveID)

	var failedPerms []*model.DocumentPermissionMapping
	var lastError error
	successCount := 0
	skippedCount := 0

	for _, perm := range permsToRemove {
		if perm.PermID == "" {
			log.Warnf("Skipping permission removal for %s - no permission ID", perm.Email)
			skippedCount++
			continue
		}

		if err := h.removeSinglePermissionWithRetry(ctx, mapping, perm); err != nil {
			failedPerms = append(failedPerms, perm)
			lastError = err
			log.Errorf("Failed to remove permission for %s: %v", perm.Email, err)
			continue // Continue processing remaining permissions
		}
		successCount++
		log.Infof("Successfully removed permission for %s", perm.Email)
	}

	// Log summary
	log.Infof("Permission removal summary: %d successful, %d failed, %d skipped, %d total", successCount, len(failedPerms), skippedCount, len(permsToRemove))

	// Return error only if all operations failed (excluding skipped ones)
	processedCount := len(permsToRemove) - skippedCount
	if processedCount > 0 && len(failedPerms) == processedCount {
		return fmt.Errorf("failed to remove all %d processed permissions, last error: %w", processedCount, lastError)
	}

	// Return partial failure error if some failed
	if len(failedPerms) > 0 {
		failedEmails := make([]string, len(failedPerms))
		for i, perm := range failedPerms {
			failedEmails[i] = perm.Email
		}
		return fmt.Errorf("failed to remove permissions for %d out of %d processed permissions: %v, last error: %w", len(failedPerms), processedCount, failedEmails, lastError)
	}

	return nil
}

// removeSinglePermissionWithRetry removes a single permission with retry logic
func (h *GDrivePermissionHandler) removeSinglePermissionWithRetry(ctx context.Context, mapping *model.DocumentMapping, perm *model.DocumentPermissionMapping) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.removeSinglePermissionWithRetry")
	var lastErr error

	// Get configuration for this tenant
	config := h.getEffectiveConfig(ctx, mapping.TenantID)

	for attempt := 0; attempt <= config.RetryCount; attempt++ {
		if attempt > 0 {
			delay := time.Duration(config.RetryDelayMs*(1<<(attempt-1))) * time.Millisecond
			time.Sleep(delay)
		}

		// Remove from Google Drive
		if err := h.gdriveClient.DeletePermission(ctx, mapping.DriveID, perm.PermID); err != nil {
			lastErr = err

			// Check if this is a 404 error (permission not found)
			var googleErr *googleapi.Error
			if errors.As(err, &googleErr) && googleErr.Code == 404 {
				// Permission doesn't exist in Google Drive, but we should still remove from DB
				log.Infof("Permission not found in Google Drive for %s (404), removing from database", perm.Email)
				if dbErr := h.rDocumentPermission.Delete(ctx, perm); dbErr != nil {
					log.Errorf("Failed to delete DB record for %s after 404: %v", perm.Email, dbErr)
					return fmt.Errorf("failed to remove DB record for %s after 404: %w", perm.Email, dbErr)
				}
				return nil // Success - permission cleaned up from DB
			}

			// Check if this error should be retried
			if !h.shouldRetryPermissionError(err) {
				log.Warnf("Non-retryable error for %s: %v", perm.Email, err)
				return fmt.Errorf("failed to remove permission for %s: %w", perm.Email, err)
			}

			log.Warnf("Retryable error for %s (attempt %d/%d): %v", perm.Email, attempt+1, config.RetryCount+1, err)
			continue
		}

		// Remove from database
		if err := h.rDocumentPermission.Delete(ctx, perm); err != nil {
			lastErr = err
			continue
		}

		return nil // Success
	}

	return fmt.Errorf("failed to remove permission for %s after %d attempts: %w", perm.Email, config.RetryCount, lastErr)
}

// storePermissionMapping stores permission mapping in database
func (h *GDrivePermissionHandler) storePermissionMapping(ctx context.Context, tenantID uint64, driveID, email, permID string, clientID uint64) error {
	mapping := &model.DocumentPermissionMapping{
		TenantID: tenantID,
		Email:    email,
		DriveID:  driveID,
		PermID:   permID,
		Provider: model.DocProviderGoogle, // Always set provider to google for GDrive permissions
		ClientID: clientID,                // Store client ID for matter permission tracking
	}

	return h.rDocumentPermission.CreateOrUpdate(ctx, mapping)
}

// shouldRetryPermissionError determines if a permission error should be retried
func (h *GDrivePermissionHandler) shouldRetryPermissionError(err error) bool {
	if err == nil {
		return false
	}

	// Unwrap error to find the underlying Google API error
	var googleErr *googleapi.Error
	if errors.As(err, &googleErr) {
		// Don't retry 400 Bad Request errors (including invalidSharingRequest)
		if googleErr.Code == 400 {
			return false
		}

		// Don't retry 401 Unauthorized or 403 Forbidden errors
		if googleErr.Code == 401 || googleErr.Code == 403 {
			return false
		}

		// Don't retry 404 Not Found errors
		if googleErr.Code == 404 {
			return false
		}

		// Retry 429 Rate Limited and 5xx server errors
		if googleErr.Code == 429 || googleErr.Code >= 500 {
			return true
		}

		// For other error codes, don't retry by default
		return false
	}

	// For non-Google API errors, retry (network issues, timeouts, etc.)
	return true
}

// preserveMatterPermissions preserves matter folder permissions after client folder permission removal
func (h *GDrivePermissionHandler) preserveMatterPermissions(ctx context.Context, tenantID uint64, clientID uint64, removedPerms []*model.DocumentPermissionMapping) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.preserveMatterPermissions")
	log.Infof("Preserving matter permissions for client %d after removing %d permissions", clientID, len(removedPerms))

	if len(removedPerms) == 0 {
		return nil
	}

	// Extract emails from removed permissions
	removedEmails := make([]string, 0, len(removedPerms))
	for _, perm := range removedPerms {
		removedEmails = append(removedEmails, perm.Email)
	}

	// Find all matter permissions for this client and these emails
	mattersToReshare, err := h.rDocumentPermission.Find(ctx, &repositories.PermMapFindArgs{
		TenantID: &tenantID,
		Emails:   &removedEmails,
		Provider: &[]string{model.DocProviderGoogle}, // Only get Google Drive permissions
	})
	if err != nil {
		return fmt.Errorf("failed to find matter permissions to preserve: %w", err)
	}

	// Filter to only matter permissions with matching clientID
	var mattersForClient []*model.DocumentPermissionMapping
	for _, matter := range mattersToReshare {
		if matter.ClientID == clientID {
			mattersForClient = append(mattersForClient, matter)
		}
	}

	if len(mattersForClient) == 0 {
		log.Infof("No matter permissions found to preserve for client %d", clientID)
		return nil
	}

	log.Infof("Found %d matter permissions to preserve for client %d", len(mattersForClient), clientID)

	// Reshare matter permissions
	return h.reshareMattersForUser(ctx, mattersForClient, tenantID)
}

// reshareMattersForUser re-grants permissions to matter folders for users
func (h *GDrivePermissionHandler) reshareMattersForUser(ctx context.Context, permissionMappings []*model.DocumentPermissionMapping, tenantID uint64) error {
	log := logger.WithCtx(ctx, "GDrivePermissionHandler.reshareMattersForUser")
	log.Infof("Resharing %d matter permissions", len(permissionMappings))

	if len(permissionMappings) == 0 {
		return nil
	}

	successCount := 0
	failureCount := 0

	// Get effective config for permission role
	config := h.getEffectiveConfig(ctx, tenantID)

	for _, mapping := range permissionMappings {
		// Skip if this mapping has no drive ID
		if mapping.DriveID == "" {
			continue
		}

		// Create the new permission
		permission := &drive.Permission{
			Role:         config.DefaultRole,
			Type:         "user",
			EmailAddress: mapping.Email,
		}

		result, err := h.gdriveClient.CreatePermission(ctx, mapping.DriveID, permission)
		if err != nil {
			log.WithError(err).Errorf("Failed to reshare folder drive_id=%s", mapping.DriveID)
			failureCount++
			continue
		}

		// Update permission mapping with new permission ID
		mapping.PermID = result.Id
		err = h.rDocumentPermission.CreateOrUpdate(ctx, mapping)
		if err != nil {
			log.WithError(err).Errorf("Failed to update permission mapping for drive_id=%s", mapping.DriveID)
			// Continue even if mapping update fails
		}

		log.Infof("Successfully reshared folder drive_id=%s for email=%s", mapping.DriveID, mapping.Email)
		successCount++
	}

	log.Infof("Resharing summary: %d successful, %d failed, %d total", successCount, failureCount, len(permissionMappings))

	return nil
}
