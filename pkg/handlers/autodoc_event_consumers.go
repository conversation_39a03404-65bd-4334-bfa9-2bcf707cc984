package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"bilabl/docman/pkg/bilabllog"

	"gitlab.com/goxp/cloud0/ginext"
)

// EventPayload represents the generic event payload structure
type EventPayload struct {
	Topic string                 `json:"topic"`
	Body  map[string]interface{} `json:"body"`
}

// MatterCreatedEventBody represents the matter.create event body
type MatterCreatedEventBody struct {
	ID       uint64 `json:"id"`
	ClientID uint64 `json:"client_id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	TenantID uint64 `json:"tenant_id"`
	ActorID  uint64 `json:"actor_id"`
	NoNotify bool   `json:"no_notify"`
	Extra    struct {
		Current map[string]interface{} `json:"current"`
		Old     map[string]interface{} `json:"old,omitempty"`
	} `json:"extra"`
}

// ClientEventBody represents both client.create and client.update event body
type ClientEventBody struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	ShortName string `json:"short_name"`
	Code      string `json:"code"`
	TenantID  uint64 `json:"tenant_id"`
	ActorID   uint64 `json:"actor_id"`
	NoNotify  bool   `json:"no_notify"`
	Extra     struct {
		Current map[string]interface{} `json:"current"`
		Old     map[string]interface{} `json:"old,omitempty"`
	} `json:"extra"`
}

// ClientUpdatedEventBody represents the client.update event body
// @deprecated Use ClientEventBody for unified client event handling
type ClientUpdatedEventBody struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	ShortName string `json:"short_name"`
	Code      string `json:"code"`
	TenantID  uint64 `json:"tenant_id"`
	Extra     struct {
		Current map[string]interface{} `json:"current"`
		Old     map[string]interface{} `json:"old,omitempty"`
	} `json:"extra"`
}

// ConsumeMatterCreated handles POST /internal/consume/autodoc/matter/created
func (h *AutoDocHandler) ConsumeMatterCreated(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received matter.created event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic
	if eventPayload.Topic != "matter.create" {
		log.Errorf("Invalid topic for matter created event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'matter.create'")
	}

	// Parse matter event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var matterBody MatterCreatedEventBody
	if err := json.Unmarshal(bodyBytes, &matterBody); err != nil {
		log.WithError(err).Error("Failed to parse matter event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid matter event body: "+err.Error())
	}

	// Validate required fields
	if matterBody.TenantID == 0 {
		log.Error("Missing tenant_id in matter event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if matterBody.ID == 0 {
		log.Error("Missing matter id in event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing matter id in event body")
	}

	log.Infof("Processing matter.create event: matter_id=%d tenant_id=%d name=%s",
		matterBody.ID, matterBody.TenantID, matterBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), matterBody.TenantID, "matter.create", eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to process matter.create event")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Matter event processed successfully: matter_id=%d", matterBody.ID)

	// Return success response
	response := map[string]interface{}{
		"status":    "success",
		"message":   "Matter created event processed successfully",
		"matter_id": matterBody.ID,
		"tenant_id": matterBody.TenantID,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ConsumeClientUpdated handles POST /internal/consume/autodoc/client/updated
func (h *AutoDocHandler) ConsumeClientUpdated(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received client.updated event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic
	if eventPayload.Topic != "client.update" {
		log.Errorf("Invalid topic for client updated event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'client.update'")
	}

	// Parse client event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var clientBody ClientUpdatedEventBody
	if err := json.Unmarshal(bodyBytes, &clientBody); err != nil {
		log.WithError(err).Error("Failed to parse client event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid client event body: "+err.Error())
	}

	// Validate required fields
	if clientBody.TenantID == 0 {
		log.Error("Missing tenant_id in client event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if clientBody.ID == 0 {
		log.Error("Missing client id in event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing client id in event body")
	}

	log.Infof("Processing client.update event: client_id=%d tenant_id=%d name=%s",
		clientBody.ID, clientBody.TenantID, clientBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), clientBody.TenantID, "client.update", eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to process client.update event")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Client event processed successfully: client_id=%d", clientBody.ID)

	// Return success response
	response := map[string]interface{}{
		"status":    "success",
		"message":   "Client updated event processed successfully",
		"client_id": clientBody.ID,
		"tenant_id": clientBody.TenantID,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ConsumeClientCreated handles POST /internal/consume/autodoc/client/created
func (h *AutoDocHandler) ConsumeClientCreated(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received client.created event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic
	if eventPayload.Topic != "client.create" {
		log.Errorf("Invalid topic for client created event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'client.create'")
	}

	// Parse client event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var clientBody ClientUpdatedEventBody // Reuse same structure as client.update
	if err := json.Unmarshal(bodyBytes, &clientBody); err != nil {
		log.WithError(err).Error("Failed to parse client event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid client event body: "+err.Error())
	}

	// Validate required fields
	if clientBody.TenantID == 0 {
		log.Error("Missing tenant_id in client event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if clientBody.ID == 0 {
		log.Error("Missing client id in event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing client id in event body")
	}

	log.Infof("Processing client.create event: client_id=%d tenant_id=%d name=%s",
		clientBody.ID, clientBody.TenantID, clientBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), clientBody.TenantID, "client.create", eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to process client.create event")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Client event processed successfully: client_id=%d", clientBody.ID)

	// Return success response
	response := map[string]interface{}{
		"status":    "success",
		"message":   "Client created event processed successfully",
		"client_id": clientBody.ID,
		"tenant_id": clientBody.TenantID,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ConsumeMatterUpdated handles POST /internal/consume/autodoc/matter/updated
func (h *AutoDocHandler) ConsumeMatterUpdated(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received matter.updated event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic
	if eventPayload.Topic != "matter.update" {
		log.Errorf("Invalid topic for matter updated event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'matter.update'")
	}

	// Parse matter event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var matterBody MatterCreatedEventBody // Reuse same structure as matter.create
	if err := json.Unmarshal(bodyBytes, &matterBody); err != nil {
		log.WithError(err).Error("Failed to parse matter event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid matter event body: "+err.Error())
	}

	// Validate required fields
	if matterBody.TenantID == 0 {
		log.Error("Missing tenant_id in matter event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if matterBody.ID == 0 {
		log.Error("Missing matter id in event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing matter id in event body")
	}

	log.Infof("Processing matter.update event: matter_id=%d tenant_id=%d name=%s",
		matterBody.ID, matterBody.TenantID, matterBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), matterBody.TenantID, "matter.update", eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to process matter.update event")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Matter event processed successfully: matter_id=%d", matterBody.ID)

	// Return success response
	response := map[string]interface{}{
		"status":    "success",
		"message":   "Matter updated event processed successfully",
		"matter_id": matterBody.ID,
		"tenant_id": matterBody.TenantID,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ConsumeClientEvents handles POST /internal/consume/autodoc/client/events
// Unified endpoint for both client.create and client.update events
func (h *AutoDocHandler) ConsumeClientEvents(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received client event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic - support both client.create and client.update
	if eventPayload.Topic != "client.create" && eventPayload.Topic != "client.update" {
		log.Errorf("Invalid topic for client event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'client.create' or 'client.update'")
	}

	// Parse client event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var clientBody ClientEventBody
	if err := json.Unmarshal(bodyBytes, &clientBody); err != nil {
		log.WithError(err).Error("Failed to parse client event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid client event body: "+err.Error())
	}

	// Validate required fields
	if clientBody.TenantID == 0 {
		log.Error("Missing tenant_id in client event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if clientBody.ID == 0 {
		log.Error("Missing client id in client event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing client id in event body")
	}

	// Determine event type based on topic
	eventType := eventPayload.Topic // "client.create" or "client.update"

	log.Infof("Processing %s event: client_id=%d tenant_id=%d name=%s",
		eventType, clientBody.ID, clientBody.TenantID, clientBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), clientBody.TenantID, eventType, eventPayload.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to process %s event", eventType)
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Client event processed successfully: client_id=%d event_type=%s", clientBody.ID, eventType)

	// Return success response
	response := map[string]interface{}{
		"status":     "success",
		"message":    fmt.Sprintf("Client %s event processed successfully", eventType),
		"client_id":  clientBody.ID,
		"tenant_id":  clientBody.TenantID,
		"event_type": eventType,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}

// ConsumeMatterEvents handles POST /internal/consume/autodoc/matter/events
// Unified endpoint for both matter.create and matter.update events
func (h *AutoDocHandler) ConsumeMatterEvents(r *ginext.Request) (*ginext.Response, error) {
	log := bilabllog.CreateContextLogger(r.Context())
	log.Info("Received matter event for autodoc processing")

	// Parse event payload
	var eventPayload EventPayload
	if err := r.GinCtx.ShouldBindJSON(&eventPayload); err != nil {
		log.WithError(err).Error("Failed to parse event payload")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid event payload: "+err.Error())
	}

	// Validate topic - support both matter.create and matter.update
	if eventPayload.Topic != "matter.create" && eventPayload.Topic != "matter.update" {
		log.Errorf("Invalid topic for matter event: %s", eventPayload.Topic)
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid topic: expected 'matter.create' or 'matter.update'")
	}

	// Parse matter event body
	bodyBytes, err := json.Marshal(eventPayload.Body)
	if err != nil {
		log.WithError(err).Error("Failed to marshal event body")
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event body")
	}

	var matterBody MatterCreatedEventBody
	if err := json.Unmarshal(bodyBytes, &matterBody); err != nil {
		log.WithError(err).Error("Failed to parse matter event body")
		return nil, ginext.NewError(http.StatusBadRequest, "Invalid matter event body: "+err.Error())
	}

	// Validate required fields
	if matterBody.TenantID == 0 {
		log.Error("Missing tenant_id in matter event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing tenant_id in event body")
	}

	if matterBody.ID == 0 {
		log.Error("Missing matter id in matter event")
		return nil, ginext.NewError(http.StatusBadRequest, "Missing matter id in event body")
	}

	// Determine event type based on topic
	eventType := eventPayload.Topic // "matter.create" or "matter.update"

	log.Infof("Processing %s event: matter_id=%d client_id=%d tenant_id=%d name=%s",
		eventType, matterBody.ID, matterBody.ClientID, matterBody.TenantID, matterBody.Name)

	// Process the event using EventRuleMatchingService
	err = h.eventRuleMatchingService.ProcessEvent(r.Context(), matterBody.TenantID, eventType, eventPayload.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to process %s event", eventType)
		return nil, ginext.NewError(http.StatusInternalServerError, "Failed to process event: "+err.Error())
	}

	log.Infof("Matter event processed successfully: matter_id=%d event_type=%s", matterBody.ID, eventType)

	// Return success response
	response := map[string]interface{}{
		"status":     "success",
		"message":    fmt.Sprintf("Matter %s event processed successfully", eventType),
		"matter_id":  matterBody.ID,
		"tenant_id":  matterBody.TenantID,
		"event_type": eventType,
	}

	return ginext.NewResponseData(http.StatusOK, response), nil
}
