package handlers

import (
	"bilabl/docman/domain/model"
	mocks "bilabl/docman/mocks/repositories"
	"bilabl/docman/pkg/repositories"
	"bilabl/docman/pkg/transport"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"
	"gitlab.com/goxp/cloud0/db"
	"gitlab.com/goxp/cloud0/ginext"
)

type SingleTestDocumentRsp struct {
	Data TestDocumentRsp
}

type TestDocumentRsp struct {
	ID   uint64 `json:"id" example:"1"`
	Name string `json:"name" example:"Document name"`
}

type DocumentHandlerTestSuite struct {
	suite.Suite
	rDocument       mocks.MockDocumentRepository
	documentHandler *DocumentHandler
	mockDocument    []*model.Document
}

func TestDocumentTestSuite(t *testing.T) {
	suite.Run(t, new(DocumentHandlerTestSuite))
}

func (d *DocumentHandlerTestSuite) SetupTest() {

	db.MustSetupTest()
	conn := db.GetDB()

	glob := transport.NewGlob("http://glob")
	hrglassid := transport.NewHrglassid("http://hrglassid")
	consumer := transport.NewConsumer("http://consumer/events")
	documentRepo := repositories.NewDocumentRepo(conn)
	documentHandler := NewDocumentHandler(documentRepo, glob, hrglassid, consumer, nil, nil, nil, nil, nil)

	d.documentHandler = documentHandler
	migrate := repositories.NewMigrationRepo(conn)
	migrate.MigrateDB(context.Background())

	d.mockDocument = []*model.Document{
		{
			Model: model.Model{
				ID: 1,
			},
			Name:        "document 1",
			TenantID:    1,
			CreatedUser: 1,
			Key:         "document1",
			Type:        1,
		},
		{
			Model: model.Model{
				ID: 2,
			},
			Name:        "document 2",
			TenantID:    1,
			CreatedUser: 1,
			Key:         "document2",
			Type:        1,
		},
	}
	conn.Create(d.mockDocument)
}

func (d *DocumentHandlerTestSuite) TestCreateDocument() {

	d.Run("should create document", func() {
		d.SetupTest()
		payload := &model.Document{
			TenantID:    3,
			Name:        "Test document",
			ObjectType:  1,
			Key:         "file",
			SubObjectID: 1,
			CreatedUser: 1,
			ObjectID:    1,
			Type:        1,
			DocType:     1,
		}

		w := doTestRequestOptions(http.MethodPost, "/v1/documents", payload,
			d.documentHandler.CreateV1, func(r *http.Request) {
				r.Header.Set("x-user-id", "1")
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			})
		d.Require().Equal(http.StatusCreated, w.Code)
	})
}

func (d *DocumentHandlerTestSuite) TestGetDocument() {
	d.Run("should get document", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/documents/1", nil,
			d.documentHandler.Get, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")

		d.Require().Equal(http.StatusOK, w.Code)
	})

	d.Run("should not found document", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/documents/4", nil,
			d.documentHandler.Get, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")

		d.Require().Equal(http.StatusNotFound, w.Code)
	})

	d.Run("should invalid tenant", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/documents/4", nil,
			d.documentHandler.Get, func(r *http.Request) {
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")

		d.Require().Equal(http.StatusBadRequest, w.Code)
	})
}

func (d *DocumentHandlerTestSuite) TestListDocument() {
	d.Run("should list document", func() {
		d.SetupTest()
		var page float64 = 1
		var pageSize float64 = 20
		w := doTestRequestOptions(http.MethodGet, fmt.Sprintf("/v1/documents?page=%v&page_size=%v", page, pageSize), nil,
			d.documentHandler.ListDocumentV1, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			})
		response := ginext.GeneralBody{}
		bodyBytes, _ := io.ReadAll(w.Body)
		_ = json.Unmarshal(bodyBytes, &response)
		d.Require().Equal(page, response.Meta["page"])
		d.Require().Equal(pageSize, response.Meta["page_size"])
		d.Require().Equal(http.StatusOK, w.Code)
	})

	d.Run("should list document with filter by status", func() {
		d.SetupTest()
		var page float64 = 1
		var pageSize float64 = 20
		w := doTestRequestOptions(http.MethodGet, fmt.Sprintf("/v1/documents?page=%v&page_size=%v&status=1,2,4", page, pageSize), nil,
			d.documentHandler.ListDocumentV1, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			})
		response := ginext.GeneralBody{}
		bodyBytes, _ := io.ReadAll(w.Body)
		_ = json.Unmarshal(bodyBytes, &response)
		d.Require().Equal(page, response.Meta["page"])
		d.Require().Equal(pageSize, response.Meta["page_size"])
		d.Require().Equal(http.StatusOK, w.Code)
	})
}

func (d *DocumentHandlerTestSuite) TestUpdateDocument() {
	d.Run("should update document", func() {
		d.SetupTest()
		payload := &model.UpdateDocumentReq{
			Name: "document updated",
		}
		w := doTestRequestOptions(http.MethodPatch, "/v1/documents/1", payload,
			d.documentHandler.Update, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusNoContent, w.Code)

		w = doTestRequestOptions(http.MethodGet, "/v1/documents/1", nil,
			d.documentHandler.Get, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")

		d.Require().Equal(http.StatusOK, w.Code)

		rData := SingleTestDocumentRsp{}
		err := json.NewDecoder(w.Body).Decode(&rData)
		d.NoError(err)

		d.Require().Equal(payload.Name, rData.Data.Name)
	})
	d.Run("should not found document", func() {
		d.SetupTest()
		payload := &model.UpdateDocumentReq{
			Name: "Document name updated",
		}
		w := doTestRequestOptions(http.MethodPatch, "/v1/documents/4", payload,
			d.documentHandler.Update, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusNotFound, w.Code)
	})
	d.Run("should invalidate tenant", func() {
		d.SetupTest()
		payload := &model.UpdateDocumentReq{
			Name: "Document name updated",
		}
		w := doTestRequestOptions(http.MethodPatch, "/v1/documents/1", payload,
			d.documentHandler.Update, func(r *http.Request) {
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusBadRequest, w.Code)
	})
}

func (d *DocumentHandlerTestSuite) TestDeleteDocument() {
	d.Run("should delete document", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodDelete, "/v1/documents/1", nil,
			d.documentHandler.Delete, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusNoContent, w.Code)
	})
	d.Run("should not found document", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodDelete, "/v1/documents/4", nil,
			d.documentHandler.Delete, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusNotFound, w.Code)
	})
	d.Run("should invalid tenant", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodDelete, "/v1/documents/1", nil,
			d.documentHandler.Delete, func(r *http.Request) {
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id")
		d.Require().Equal(http.StatusBadRequest, w.Code)
	})
}

func (d *DocumentHandlerTestSuite) TestDownloadDocument() {
	d.Run("should not found document", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/documents/4/downloads", nil,
			d.documentHandler.Download, func(r *http.Request) {
				r.Header.Set("x-tenant-id", "1")
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id/downloads")

		d.Require().Equal(http.StatusNotFound, w.Code)
	})

	d.Run("should invalid tenant", func() {
		d.SetupTest()
		w := doTestRequestOptions(http.MethodGet, "/v1/documents/4/downloads", nil,
			d.documentHandler.Get, func(r *http.Request) {
				r.Header.Set("content-type", "application/json")
			}, "/v1/documents/:document_id/downloads")

		d.Require().Equal(http.StatusBadRequest, w.Code)
	})
}
