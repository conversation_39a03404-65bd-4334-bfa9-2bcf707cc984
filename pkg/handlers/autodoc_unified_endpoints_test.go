package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/goxp/cloud0/ginext"
)

// MockEventRuleMatchingServiceUnified for testing unified endpoints
type MockEventRuleMatchingServiceUnified struct {
	mock.Mock
}

func (m *MockEventRuleMatchingServiceUnified) ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	args := m.Called(ctx, tenantID, eventType, eventData)
	return args.Error(0)
}

func (m *MockEventRuleMatchingServiceUnified) ProcessMatterEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	args := m.Called(ctx, tenantID, eventType, eventData)
	return args.Error(0)
}

func (m *MockEventRuleMatchingServiceUnified) ProcessClientEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	args := m.Called(ctx, tenantID, eventType, eventData)
	return args.Error(0)
}

func TestConsumeClientEvents(t *testing.T) {
	tests := []struct {
		name           string
		topic          string
		payload        map[string]interface{}
		expectedStatus int
		expectError    bool
	}{
		{
			name:  "Valid client.create event",
			topic: "client.create",
			payload: map[string]interface{}{
				"id":        float64(123), // JSON unmarshaling converts to float64
				"name":      "Test Client",
				"code":      "CLI001",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:  "Valid client.update event",
			topic: "client.update",
			payload: map[string]interface{}{
				"id":        float64(123), // JSON unmarshaling converts to float64
				"name":      "Updated Client",
				"code":      "CLI001",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "Invalid topic",
			topic:          "client.delete",
			payload:        map[string]interface{}{},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:  "Missing tenant_id",
			topic: "client.create",
			payload: map[string]interface{}{
				"id":   123,
				"name": "Test Client",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := &MockEventRuleMatchingServiceUnified{}
			handler := &AutoDocHandler{
				eventRuleMatchingService: mockService,
			}

			// Setup mock expectations for valid cases
			if !tt.expectError {
				mockService.On("ProcessEvent", mock.Anything, uint64(456), tt.topic, tt.payload).Return(nil)
			}

			// Create request
			requestBody := map[string]interface{}{
				"topic": tt.topic,
				"body":  tt.payload,
			}
			bodyBytes, _ := json.Marshal(requestBody)

			// Setup Gin context
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/internal/consume/autodoc/client/events", bytes.NewBuffer(bodyBytes))
			c.Request.Header.Set("Content-Type", "application/json")

			req := &ginext.Request{GinCtx: c}

			// Execute
			resp, err := handler.ConsumeClientEvents(req)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				if resp.Code == http.StatusOK {
					// ginext.Response.Body is a GeneralBody with Data field
					generalBody := resp.Body.(*ginext.GeneralBody)
					responseData := generalBody.Data.(map[string]interface{})
					assert.Equal(t, "success", responseData["status"])
					assert.Contains(t, responseData["message"], "processed successfully")
					assert.Equal(t, tt.topic, responseData["event_type"])
				}
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestConsumeMatterEvents(t *testing.T) {
	tests := []struct {
		name           string
		topic          string
		payload        map[string]interface{}
		expectedStatus int
		expectError    bool
	}{
		{
			name:  "Valid matter.create event",
			topic: "matter.create",
			payload: map[string]interface{}{
				"id":        float64(789), // JSON unmarshaling converts to float64
				"client_id": float64(123), // JSON unmarshaling converts to float64
				"name":      "Test Matter",
				"code":      "MAT001",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:  "Valid matter.update event",
			topic: "matter.update",
			payload: map[string]interface{}{
				"id":        float64(789), // JSON unmarshaling converts to float64
				"client_id": float64(123), // JSON unmarshaling converts to float64
				"name":      "Updated Matter",
				"code":      "MAT001",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "Invalid topic",
			topic:          "matter.delete",
			payload:        map[string]interface{}{},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:  "Missing tenant_id",
			topic: "matter.create",
			payload: map[string]interface{}{
				"id":   789,
				"name": "Test Matter",
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := &MockEventRuleMatchingServiceUnified{}
			handler := &AutoDocHandler{
				eventRuleMatchingService: mockService,
			}

			// Setup mock expectations for valid cases
			if !tt.expectError {
				mockService.On("ProcessEvent", mock.Anything, uint64(456), tt.topic, tt.payload).Return(nil)
			}

			// Create request
			requestBody := map[string]interface{}{
				"topic": tt.topic,
				"body":  tt.payload,
			}
			bodyBytes, _ := json.Marshal(requestBody)

			// Setup Gin context
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/internal/consume/autodoc/matter/events", bytes.NewBuffer(bodyBytes))
			c.Request.Header.Set("Content-Type", "application/json")

			req := &ginext.Request{GinCtx: c}

			// Execute
			resp, err := handler.ConsumeMatterEvents(req)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedStatus, resp.Code)

				if resp.Code == http.StatusOK {
					// ginext.Response.Body is a GeneralBody with Data field
					generalBody := resp.Body.(*ginext.GeneralBody)
					responseData := generalBody.Data.(map[string]interface{})
					assert.Equal(t, "success", responseData["status"])
					assert.Contains(t, responseData["message"], "processed successfully")
					assert.Equal(t, tt.topic, responseData["event_type"])
				}
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestUnifiedEndpointsBackwardCompatibility(t *testing.T) {
	t.Run("Client events support both create and update", func(t *testing.T) {
		mockService := &MockEventRuleMatchingServiceUnified{}
		handler := &AutoDocHandler{
			eventRuleMatchingService: mockService,
		}

		// Test both event types work with same endpoint
		eventTypes := []string{"client.create", "client.update"}

		for _, eventType := range eventTypes {
			payload := map[string]interface{}{
				"id":        float64(123), // JSON unmarshaling converts to float64
				"name":      "Test Client",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			}

			mockService.On("ProcessEvent", mock.Anything, uint64(456), eventType, payload).Return(nil)

			requestBody := map[string]interface{}{
				"topic": eventType,
				"body":  payload,
			}
			bodyBytes, _ := json.Marshal(requestBody)

			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/internal/consume/autodoc/client/events", bytes.NewBuffer(bodyBytes))
			c.Request.Header.Set("Content-Type", "application/json")

			req := &ginext.Request{GinCtx: c}

			resp, err := handler.ConsumeClientEvents(req)

			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, http.StatusOK, resp.Code)

			if resp.Code == http.StatusOK {
				generalBody := resp.Body.(*ginext.GeneralBody)
				responseData := generalBody.Data.(map[string]interface{})
				assert.Equal(t, eventType, responseData["event_type"])
			}
		}

		mockService.AssertExpectations(t)
	})

	t.Run("Matter events support both create and update", func(t *testing.T) {
		mockService := &MockEventRuleMatchingServiceUnified{}
		handler := &AutoDocHandler{
			eventRuleMatchingService: mockService,
		}

		// Test both event types work with same endpoint
		eventTypes := []string{"matter.create", "matter.update"}

		for _, eventType := range eventTypes {
			payload := map[string]interface{}{
				"id":        float64(789), // JSON unmarshaling converts to float64
				"client_id": float64(123), // JSON unmarshaling converts to float64
				"name":      "Test Matter",
				"tenant_id": float64(456), // JSON unmarshaling converts to float64
			}

			mockService.On("ProcessEvent", mock.Anything, uint64(456), eventType, payload).Return(nil)

			requestBody := map[string]interface{}{
				"topic": eventType,
				"body":  payload,
			}
			bodyBytes, _ := json.Marshal(requestBody)

			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/internal/consume/autodoc/matter/events", bytes.NewBuffer(bodyBytes))
			c.Request.Header.Set("Content-Type", "application/json")

			req := &ginext.Request{GinCtx: c}

			resp, err := handler.ConsumeMatterEvents(req)

			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, http.StatusOK, resp.Code)

			if resp.Code == http.StatusOK {
				generalBody := resp.Body.(*ginext.GeneralBody)
				responseData := generalBody.Data.(map[string]interface{})
				assert.Equal(t, eventType, responseData["event_type"])
			}
		}

		mockService.AssertExpectations(t)
	})
}
