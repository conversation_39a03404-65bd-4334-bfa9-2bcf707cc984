# Google Drive Package

A Go package for interacting with Google Drive API v3 with comprehensive shared drive support, pagination, and file management capabilities.

## Features

### Core Operations
- ✅ **Authentication**: Service account and OAuth2 support
- ✅ **File Operations**: Upload, download, delete, rename files
- ✅ **Folder Operations**: Create, manage folders and subfolders
- ✅ **Shared Drive Support**: Full support for shared drives with proper API flags
- ✅ **Pagination**: Handle large datasets with built-in pagination support
- ✅ **Search**: Find files and folders by name with advanced queries
- ✅ **URL Parsing**: Parse Google Drive URLs to extract resource information
- ✅ **Resumable Uploads**: Support for large file uploads

### Listing APIs with Pagination
- `ListFiles(opts *PaginationOptions)` - List files from My Drive
- `ListFilesInSharedDrive(driveID, opts)` - List files in shared drive
- `ListFilesInFolder(folderID, opts)` - List files in specific folder
- `ListAllFilesInFolder(folderID)` - Get all files with auto-pagination

### File Search & Management
- `FindFileInFolder(fileName, parentFolderID)` - Find file by name
- `FindFolderInSharedDrive(folderName, driveID)` - Find folder in shared drive
- `RenameFile(fileID, newName)` - Rename files and folders
- `CreateResumableUploadURL(filename, parentFolderID, fileSize)` - Generate upload URLs

### Shared Drive Operations
All operations support shared drives with proper `SupportsAllDrives(true)` flags:
- File uploads to shared drives and folders
- Folder creation in shared drives
- Search within shared drives
- Download from shared drives

## Quick Start

```go
package main

import (
    "context"
    "log"
    "bilabl/docman/pkg/gdrive"
)

func main() {
    // Initialize service with credentials
    service, err := gdrive.NewService(
        gdrive.WithCredentialsFile("path/to/credentials.json"),
        gdrive.WithScopes(gdrive.ScopeReadWrite),
    )
    if err != nil {
        log.Fatal("Failed to create service:", err)
    }

    // List files with pagination
    opts := &gdrive.PaginationOptions{PageSize: 100}
    result, err := service.ListFiles(opts)
    if err != nil {
        log.Fatal("Failed to list files:", err)
    }
    
    log.Printf("Found %d files", len(result.Files))
    if result.HasNextPage {
        log.Printf("Next page token: %s", result.NextPageToken)
    }

    // Find a file in a folder
    file, err := service.FindFileInFolder("document.pdf", "folder123")
    if err != nil {
        log.Fatal("File not found:", err)
    }
    log.Printf("Found file: %s (ID: %s)", file.Name, file.Id)

    // Create resumable upload URL
    uploadInfo, err := service.CreateResumableUploadURL("large-file.zip", "folder123", 1024000)
    if err != nil {
        log.Fatal("Failed to create upload URL:", err)
    }
    log.Printf("Upload URL: %s", uploadInfo.UploadURL)
}
```

## Pagination Support

All listing operations support pagination to handle large datasets efficiently:

```go
// Basic pagination
opts := &gdrive.PaginationOptions{
    PageSize: 100,           // Up to 1000 items per page
    PageToken: "next-token", // For subsequent pages
}

result, err := service.ListFilesInFolder("folder123", opts)
if err != nil {
    return err
}

// Handle results
for _, file := range result.Files {
    log.Printf("File: %s", file.Name)
}

// Check for more pages
if result.HasNextPage {
    opts.PageToken = result.NextPageToken
    // Make next request...
}

// Auto-pagination helper
allFiles, err := service.ListAllFilesInFolder("folder123")
if err != nil {
    return err
}
log.Printf("Total files: %d", len(allFiles))
```

## Shared Drive Operations

```go
// List shared drives
drives, err := service.ListSharedDrives()
if err != nil {
    return err
}

// List files in shared drive with pagination
opts := &gdrive.PaginationOptions{PageSize: 50}
result, err := service.ListFilesInSharedDrive(driveID, opts)
if err != nil {
    return err
}

// Upload file to shared drive folder
file, err := service.UploadFileToSharedDriveFolder(
    "document.pdf", 
    "./local/path/document.pdf", 
    driveID, 
    folderID,
)
if err != nil {
    return err
}

// Create folder in shared drive
folder, err := service.CreateFolderInSharedDrive("New Folder", driveID)
if err != nil {
    return err
}
```

## Configuration

```go
// Service account with custom configuration
service, err := gdrive.NewService(
    gdrive.WithCredentialsFile("service-account.json"),
    gdrive.WithScopes(gdrive.ScopeReadWrite),
    gdrive.WithContext(context.Background()),
)

// OAuth2 configuration
service, err := gdrive.NewService(
    gdrive.WithClientCredentials("client-id", "client-secret"),
    gdrive.WithTokenFile("token.json"),
    gdrive.WithScopes(gdrive.ScopeReadOnly),
)
```

## URL Parsing

```go
// Parse Google Drive URLs
info, err := service.ParseSharedURL("https://drive.google.com/drive/folders/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
if err != nil {
    return err
}

log.Printf("Resource Type: %s", info.Type) // "folder", "file", or "shared_drive"
log.Printf("Resource ID: %s", info.ID)
log.Printf("Resource Name: %s", info.Name)

// Or get info directly by ID
info, err := service.GetDriveInfo("1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
```

## Advanced Search

```go
// Find files with custom queries
query := "name contains 'report' and mimeType='application/pdf'"
files, err := service.ListFilesWithQuery(query)
if err != nil {
    return err
}

// Find specific file in folder
file, err := service.FindFileInFolder("important-doc.pdf", "folder123")
if err != nil {
    return err
}
```

## Error Handling

The package provides detailed error messages with context:

```go
file, err := service.FindFileInFolder("missing.txt", "folder123")
if err != nil {
    if strings.Contains(err.Error(), "not found") {
        log.Println("File doesn't exist in folder")
    } else {
        log.Printf("API error: %v", err)
    }
    return err
}
```

## Data Structures

### PaginationOptions
```go
type PaginationOptions struct {
    PageSize  int64  // Number of items per page (max 1000)
    PageToken string // Token to retrieve next page
}
```

### PaginatedResult
```go
type PaginatedResult struct {
    Files         []*drive.File // Files in current page
    NextPageToken string        // Token for next page
    HasNextPage   bool          // Whether more pages exist
}
```

### ResumableUploadInfo
```go
type ResumableUploadInfo struct {
    UploadURL string // URL for resumable upload
    FileID    string // ID of created file
}
```

### DriveInfo
```go
type DriveInfo struct {
    ID      string      // Resource ID
    Name    string      // Resource name
    Type    ResourceType // "file", "folder", or "shared_drive"
    DriveID string      // Parent shared drive ID (if applicable)
}
```

## Testing

The package includes comprehensive mocks for testing:

```go
func TestMyFunction(t *testing.T) {
    mockClient := &gdrive.MockDriveClient{
        ListFilesFunc: func(opts *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error) {
            return &gdrive.PaginatedResult{
                Files: []*drive.File{{Name: "test.txt"}},
                HasNextPage: false,
            }, nil
        },
    }

    // Use mockClient in your tests
    result, err := mockClient.ListFiles(nil)
    assert.NoError(t, err)
    assert.Len(t, result.Files, 1)
}
```

## Performance Considerations

- Use appropriate page sizes (100-1000) for large datasets
- Implement exponential backoff for rate limiting
- Cache frequently accessed folder/drive IDs
- Use `ListAllFilesInFolder()` carefully with large folders
- Consider using `Fields()` to limit returned data

## Migration from v1

If upgrading from a previous version:

1. **Listing APIs now return `PaginatedResult`** instead of `[]*drive.File`
2. **All shared drive operations include proper API flags**
3. **New methods available for search and management**
4. **Pagination is now consistently supported**

```go
// Old way
files, err := service.ListFiles()

// New way  
result, err := service.ListFiles(nil) // nil uses default pagination
files := result.Files
```

## License

This package is part of the bilabl/docman project.

## Setup: Generating Service Account Credentials

To use this package, you need a Google Cloud Platform (GCP) service account and its JSON credentials. Follow these steps to set one up:

### 1. Create or Pick a GCP Project

-   Go to [https://console.cloud.google.com/](https://console.cloud.google.com/).
-   In the project dropdown menu, select an existing project or click **New Project**.
-   If new, give your project a name (e.g., `bilabl-drive-int`) and click **Create**.

### 2. Enable the Google Drive API

-   In the Cloud Console, navigate to **APIs & Services → Library**.
-   Search for "Google Drive API" and select it.
-   Click the **Enable** button.
-   (Optional) You can also enable the "Google Drive Activity API" if you need to monitor file changes.

### 3. Create a Service Account

-   Navigate to **IAM & Admin → Service Accounts**.
-   Click **+ CREATE SERVICE ACCOUNT**.
-   Give it a name (e.g., `bilabl-drive-sa`) and a description.
-   Click **CREATE AND CONTINUE**.
-   In the "Grant this service account access to project" step, you can skip adding roles for now. Click **CONTINUE**.
-   In the "Grant users access to this service account" step, you can also skip this. Click **DONE**.

### 4. Generate a JSON Key

-   From the list of service accounts, find the one you just created.
-   Click the **Actions** (⋮) menu next to it, then select **Manage keys**.
-   Click **ADD KEY → Create new key**.
-   Choose **JSON** as the key type and click **CREATE**.
-   A JSON file containing your credentials will be downloaded. **Store this file securely.**

> **Pro tip:** Never commit this JSON key to Git—treat it like your toothbrush. For use in applications, load it from a secure location or use a base64-encoded environment variable (see `WithCredentialsJSON` usage).

### 5. Grant Access to Your Drive Folder/Files

A service account is like a robot user—it has its own identity but no permissions by default. You must explicitly give it access to the folders or files it needs to manage.

-   Copy the email address of the service account you created (e.g., `<EMAIL>`).
-   Go to Google Drive and select the Shared Drive or folder you want the service to access.
-   Click the **Share** button.
-   Paste the service account's email address into the sharing dialog and grant it the necessary role (e.g., `Viewer` for read-only access, `Contributor` or `Content Manager` to add/edit files).

## Usage

### Basic Usage

```go
package main

import (
    "log"
    "code.mybil.net/gophers/gdriveint/pkg/drive"
)

func main() {
    // Create service with default configuration
    service, err := drive.NewService()
    if err != nil {
        log.Fatal(err)
    }
    
    // List files
    files, err := service.ListFiles()
    if err != nil {
        log.Fatal(err)
    }
    
    for _, file := range files {
        log.Printf("File: %s (%s)", file.Name, file.Id)
    }
}
```

### Configuration Options

The service can be configured using functional options.

#### With a Credentials File

```go
import (
    "context"
    "code.mybil.net/gophers/gdriveint/pkg/drive"
)

// Custom configuration using a JSON file
service, err := drive.NewService(
    drive.WithCredentialsFile("/path/to/creds.json"),
    drive.WithScopes([]string{"https://www.googleapis.com/auth/drive.readonly"}),
    drive.WithContext(context.Background()),
)
```

#### With Base64 Encoded Credentials

For environments where using files is inconvenient (like containers), you can provide the service account JSON as a base64 encoded string via an environment variable.

```go
import (
    "context"
    "os"
    "code.mybil.net/gophers/gdriveint/pkg/drive"
)

// Custom configuration using a base64 encoded JSON string
// This is the recommended approach for containerized deployments.
service, err := drive.NewService(
    drive.WithCredentialsJSON(os.Getenv("GOOGLE_CREDENTIALS_BASE64")),
    drive.WithScopes([]string{"https://www.googleapis.com/auth/drive.readonly"}),
    drive.WithContext(context.Background()),
)
```

### Testing

The package provides interfaces and mock implementations for easy testing:

```go
func TestMyFunction(t *testing.T) {
    // Create mock filesystem
    mockFS := drive.NewMockFileSystem()
    mockFS.AddFile("/test/file.txt", []byte("test content"))
    
    // Create service with mocked dependencies
    // (You would also need to mock the Google Drive API)
    service := drive.NewServiceWithDependencies(mockDriveAPI, mockFS)
    
    // Test your function
    result, err := myFunction(service)
    // ... assertions
}
```

## Architecture

### Interfaces

- **`DriveClient`**: Main interface for Drive operations
- **`FileSystem`**: Interface for file system operations
- **`DriveAPIWrapper`**: Interface for Google Drive API wrapper

### Implementations

- **`Service`**: Main implementation of DriveClient
- **`RealFileSystem`**: Real file system implementation
- **`MockFileSystem`**: Mock file system for testing

### Configuration

- **`Config`**: Configuration struct with credentials, scopes, context
- **`Option`**: Functional options pattern for configuration
- **`DefaultConfig()`**: Sensible defaults with environment variable support

## Testing Support

### Mock FileSystem

```go
mockFS := drive.NewMockFileSystem()

// Add test files
mockFS.AddFile("test.txt", []byte("content"))

// Test file operations
file, err := mockFS.Open("test.txt")
content, err := io.ReadAll(file)

// Verify operations
if mockFS.WasOpened("test.txt") {
    // File was accessed
}
```

### Configuration Testing

```go
func TestConfig(t *testing.T) {
    config := drive.DefaultConfig()
    drive.WithCredentialsFile("/custom/path")(config)
    
    // Test configuration
    assert.Equal(t, "/custom/path", config.CredentialsFile)
}
```

## Running Tests

```bash
go test ./pkg/drive -v
```

## Examples

See `cmd/demo/` and `cmd/demo-create-folder/` for complete usage examples.

## Dependencies

- `google.golang.org/api/drive/v3`
- `google.golang.org/api/option`

## Error Handling

All methods return detailed error messages with context. Errors are wrapped using `fmt.Errorf` for better debugging.

## Thread Safety

The service is safe for concurrent use as it only reads from the underlying Google Drive API client, which is thread-safe. 
