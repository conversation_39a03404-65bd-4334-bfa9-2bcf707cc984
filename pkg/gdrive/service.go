package gdrive

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"google.golang.org/api/drive/v3"
	"google.golang.org/api/googleapi"
)

// Service implements DriveClient interface with dependency injection for testing
type Service struct {
	api        DriveAPI
	fileSystem FileSystem
	client     *http.Client
}

// realDriveAPI implements the DriveAPI interface by wrapping the real Google Drive service.
type realDriveAPI struct {
	service *drive.Service
}

func (r *realDriveAPI) Files() FilesAPI {
	return &realFilesAPI{service: r.service}
}

func (r *realDriveAPI) Drives() DrivesAPI {
	return &realDrivesAPI{service: r.service}
}

func (r *realDriveAPI) Permissions() PermissionsAPI {
	return &realPermissionsAPI{service: r.service}
}

// realFilesAPI implements the FilesAPI interface.
type realFilesAPI struct {
	service *drive.Service
}

func (r *realFilesAPI) Get(fileId string) FilesGetCall {
	return &realFilesGetCall{call: r.service.Files.Get(fileId)}
}

func (r *realFilesAPI) Create(file *drive.File) FilesCreateCall {
	return &realFilesCreateCall{call: r.service.Files.Create(file)}
}

func (r *realFilesAPI) List() FilesListCall {
	return r.service.Files.List()
}

func (r *realFilesAPI) Delete(fileId string) FilesDeleteCall {
	return r.service.Files.Delete(fileId)
}

func (r *realFilesAPI) Update(fileId string, file *drive.File) FilesUpdateCall {
	return &realFilesUpdateCall{call: r.service.Files.Update(fileId, file)}
}

// realFilesGetCall wraps the actual Google Drive API FilesGetCall
type realFilesGetCall struct {
	call *drive.FilesGetCall
}

func (r *realFilesGetCall) Fields(fields ...googleapi.Field) FilesGetCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realFilesGetCall) SupportsAllDrives(supportsAllDrives bool) FilesGetCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realFilesGetCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	return r.call.Do(opts...)
}

func (r *realFilesGetCall) Download(opts ...googleapi.CallOption) (*http.Response, error) {
	return r.call.Download(opts...)
}

// realFilesCreateCall wraps the actual Google Drive API FilesCreateCall
type realFilesCreateCall struct {
	call *drive.FilesCreateCall
}

func (r *realFilesCreateCall) Media(reader io.Reader, options ...googleapi.MediaOption) FilesCreateCall {
	r.call = r.call.Media(reader, options...)
	return r
}

func (r *realFilesCreateCall) SupportsAllDrives(supportsAllDrives bool) FilesCreateCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realFilesCreateCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	return r.call.Do(opts...)
}

// realFilesUpdateCall wraps the actual Google Drive API FilesUpdateCall
type realFilesUpdateCall struct {
	call *drive.FilesUpdateCall
}

func (r *realFilesUpdateCall) SupportsAllDrives(supportsAllDrives bool) FilesUpdateCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realFilesUpdateCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	return r.call.Do(opts...)
}

// realDrivesAPI implements the DrivesAPI interface.
type realDrivesAPI struct {
	service *drive.Service
}

func (r *realDrivesAPI) Get(driveId string) DrivesGetCall {
	return &realDrivesGetCall{call: r.service.Drives.Get(driveId)}
}

func (r *realDrivesAPI) List() DrivesListCall {
	return r.service.Drives.List()
}

// realDrivesGetCall wraps the actual Google Drive API DrivesGetCall
type realDrivesGetCall struct {
	call *drive.DrivesGetCall
}

func (r *realDrivesGetCall) Fields(fields ...googleapi.Field) DrivesGetCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realDrivesGetCall) Do(opts ...googleapi.CallOption) (*drive.Drive, error) {
	return r.call.Do(opts...)
}

// realPermissionsAPI implements the PermissionsAPI interface.
type realPermissionsAPI struct {
	service *drive.Service
}

func (r *realPermissionsAPI) List(fileId string) PermissionsListCall {
	return &realPermissionsListCall{call: r.service.Permissions.List(fileId)}
}

func (r *realPermissionsAPI) Create(fileId string, permission *drive.Permission) PermissionsCreateCall {
	return &realPermissionsCreateCall{call: r.service.Permissions.Create(fileId, permission)}
}

func (r *realPermissionsAPI) Delete(fileId, permissionId string) PermissionsDeleteCall {
	return &realPermissionsDeleteCall{call: r.service.Permissions.Delete(fileId, permissionId)}
}

func (r *realPermissionsAPI) Get(fileId, permissionId string) PermissionsGetCall {
	return &realPermissionsGetCall{call: r.service.Permissions.Get(fileId, permissionId)}
}

func (r *realPermissionsAPI) Update(fileId, permissionId string, permission *drive.Permission) PermissionsUpdateCall {
	return &realPermissionsUpdateCall{call: r.service.Permissions.Update(fileId, permissionId, permission)}
}

// realPermissionsListCall wraps the actual Google Drive API PermissionsListCall
type realPermissionsListCall struct {
	call *drive.PermissionsListCall
}

func (r *realPermissionsListCall) Fields(fields ...googleapi.Field) PermissionsListCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realPermissionsListCall) SupportsAllDrives(supportsAllDrives bool) PermissionsListCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realPermissionsListCall) Do(opts ...googleapi.CallOption) (*drive.PermissionList, error) {
	return r.call.Do(opts...)
}

// realPermissionsCreateCall wraps the actual Google Drive API PermissionsCreateCall
type realPermissionsCreateCall struct {
	call *drive.PermissionsCreateCall
}

func (r *realPermissionsCreateCall) Fields(fields ...googleapi.Field) PermissionsCreateCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realPermissionsCreateCall) SupportsAllDrives(supportsAllDrives bool) PermissionsCreateCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realPermissionsCreateCall) SendNotificationEmail(sendNotificationEmail bool) PermissionsCreateCall {
	r.call = r.call.SendNotificationEmail(sendNotificationEmail)
	return r
}

func (r *realPermissionsCreateCall) Do(opts ...googleapi.CallOption) (*drive.Permission, error) {
	return r.call.Do(opts...)
}

// realPermissionsDeleteCall wraps the actual Google Drive API PermissionsDeleteCall
type realPermissionsDeleteCall struct {
	call *drive.PermissionsDeleteCall
}

func (r *realPermissionsDeleteCall) SupportsAllDrives(supportsAllDrives bool) PermissionsDeleteCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realPermissionsDeleteCall) Do(opts ...googleapi.CallOption) error {
	return r.call.Do(opts...)
}

// realPermissionsGetCall wraps the actual Google Drive API PermissionsGetCall
type realPermissionsGetCall struct {
	call *drive.PermissionsGetCall
}

func (r *realPermissionsGetCall) Fields(fields ...googleapi.Field) PermissionsGetCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realPermissionsGetCall) SupportsAllDrives(supportsAllDrives bool) PermissionsGetCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realPermissionsGetCall) Do(opts ...googleapi.CallOption) (*drive.Permission, error) {
	return r.call.Do(opts...)
}

// realPermissionsUpdateCall wraps the actual Google Drive API PermissionsUpdateCall
type realPermissionsUpdateCall struct {
	call *drive.PermissionsUpdateCall
}

func (r *realPermissionsUpdateCall) Fields(fields ...googleapi.Field) PermissionsUpdateCall {
	r.call = r.call.Fields(fields...)
	return r
}

func (r *realPermissionsUpdateCall) SupportsAllDrives(supportsAllDrives bool) PermissionsUpdateCall {
	r.call = r.call.SupportsAllDrives(supportsAllDrives)
	return r
}

func (r *realPermissionsUpdateCall) Do(opts ...googleapi.CallOption) (*drive.Permission, error) {
	return r.call.Do(opts...)
}

// NewService creates a new Drive service with default configuration
func NewService(opts ...Option) (*Service, error) {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}

	driveService, httpClient, err := NewDriveService(config)
	if err != nil {
		return nil, fmt.Errorf("unable to create Drive service: %v", err)
	}

	return &Service{
		api:        &realDriveAPI{service: driveService},
		fileSystem: NewRealFileSystem(),
		client:     httpClient,
	}, nil
}

// NewServiceWithDependencies creates a new Service with custom dependencies for testing
func NewServiceWithDependencies(api DriveAPI, fs FileSystem) *Service {
	return &Service{
		api:        api,
		fileSystem: fs,
		client:     &http.Client{}, // Use default HTTP client for testing
	}
}

// NewServiceWithDependenciesAndClient creates a new Service with custom dependencies and HTTP client for testing
func NewServiceWithDependenciesAndClient(api DriveAPI, fs FileSystem, client *http.Client) *Service {
	return &Service{
		api:        api,
		fileSystem: fs,
		client:     client,
	}
}

// ListFiles lists files from My Drive with pagination support
func (s *Service) ListFiles(opts *PaginationOptions) (*PaginatedResult, error) {
	if opts == nil {
		opts = &PaginationOptions{PageSize: 100}
	}
	if opts.PageSize <= 0 {
		opts.PageSize = 100
	} else if opts.PageSize > 1000 {
		opts.PageSize = 1000
	}

	call := s.api.Files().List().
		PageSize(opts.PageSize).
		Fields("nextPageToken, files(id,name,size,mimeType,modifiedTime,webViewLink,webContentLink,createdTime,parents)")

	if opts.PageToken != "" {
		call = call.PageToken(opts.PageToken)
	}

	r, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve files: %v", err)
	}

	return &PaginatedResult{
		Files:         r.Files,
		NextPageToken: r.NextPageToken,
		HasNextPage:   r.NextPageToken != "",
	}, nil
}

// UploadFile uploads a file to My Drive
func (s *Service) UploadFile(filename, filepath string) (*drive.File, error) {
	file, err := s.fileSystem.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("unable to open file: %v", err)
	}
	defer file.Close()

	driveFile := &drive.File{
		Name: filename,
	}

	res, err := s.api.Files().Create(driveFile).Media(file).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create file: %v", err)
	}

	return res, nil
}

// DownloadFile downloads a file from Drive to local path (updated to support shared drives)
func (s *Service) DownloadFile(fileID, outputPath string) error {
	resp, err := s.api.Files().Get(fileID).
		SupportsAllDrives(true).
		Download()
	if err != nil {
		return fmt.Errorf("unable to download file: %v", err)
	}
	defer resp.Body.Close()

	out, err := s.fileSystem.Create(outputPath)
	if err != nil {
		return fmt.Errorf("unable to create output file: %v", err)
	}
	defer out.Close()

	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("unable to copy file contents: %v", err)
	}

	return nil
}

// DeleteFile deletes a file from Drive
func (s *Service) DeleteFile(fileID string) error {
	err := s.api.Files().Delete(fileID).SupportsAllDrives(true).Do()
	if err != nil {
		return fmt.Errorf("unable to delete file: %w", err)
	}
	return nil
}

// CreateFolder creates a folder in My Drive or a specific parent folder.
// If parentID is empty, it creates the folder in My Drive root.
func (s *Service) CreateFolder(name, parentID string) (*drive.File, error) {
	folder := &drive.File{
		Name:     name,
		MimeType: "application/vnd.google-apps.folder",
	}
	if parentID != "" {
		folder.Parents = []string{parentID}
	}

	res, err := s.api.Files().Create(folder).SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create folder: %w", err)
	}

	return res, nil
}

// ListSharedDrives lists all shared drives
func (s *Service) ListSharedDrives() ([]*drive.Drive, error) {
	r, err := s.api.Drives().List().PageSize(10).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve shared drives: %v", err)
	}

	return r.Drives, nil
}

// ListFilesInSharedDrive lists files in a specific shared drive with pagination
func (s *Service) ListFilesInSharedDrive(driveID string, opts *PaginationOptions) (*PaginatedResult, error) {
	if opts == nil {
		opts = &PaginationOptions{PageSize: 100}
	}
	if opts.PageSize <= 0 || opts.PageSize > 1000 {
		opts.PageSize = 100
	}

	call := s.api.Files().List().
		PageSize(opts.PageSize).
		Corpora("drive").
		DriveId(driveID).
		IncludeItemsFromAllDrives(true).
		SupportsAllDrives(true).
		Fields("nextPageToken, files(id,name,size,mimeType,modifiedTime,webViewLink,webContentLink,createdTime,parents,driveId)")

	if opts.PageToken != "" {
		call = call.PageToken(opts.PageToken)
	}

	r, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve files from shared drive: %v", err)
	}

	return &PaginatedResult{
		Files:         r.Files,
		NextPageToken: r.NextPageToken,
		HasNextPage:   r.NextPageToken != "",
	}, nil
}

// UploadFileToSharedDrive uploads a file to a shared drive root
func (s *Service) UploadFileToSharedDrive(filename, filepath, driveID string) (*drive.File, error) {
	file, err := s.fileSystem.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("unable to open file: %v", err)
	}
	defer file.Close()

	driveFile := &drive.File{
		Name:    filename,
		Parents: []string{driveID},
	}

	res, err := s.api.Files().Create(driveFile).
		Media(file).
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create file in shared drive: %v", err)
	}

	return res, nil
}

// UploadFileToSharedDriveFolder uploads a file to a specific folder in shared drive
func (s *Service) UploadFileToSharedDriveFolder(filename, filepath, driveID, folderID string) (*drive.File, error) {
	file, err := s.fileSystem.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("unable to open file: %v", err)
	}
	defer file.Close()

	driveFile := &drive.File{
		Name:    filename,
		Parents: []string{folderID},
	}

	res, err := s.api.Files().Create(driveFile).
		Media(file).
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create file in shared drive folder: %v", err)
	}

	return res, nil
}

// CreateFolderInSharedDrive creates a folder in shared drive root
func (s *Service) CreateFolderInSharedDrive(name, driveID string) (*drive.File, error) {
	folder := &drive.File{
		Name:     name,
		MimeType: "application/vnd.google-apps.folder",
		Parents:  []string{driveID},
	}

	res, err := s.api.Files().Create(folder).
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create folder in shared drive: %v", err)
	}

	return res, nil
}

// CreateFolderInSharedDriveFolder creates a subfolder in shared drive
func (s *Service) CreateFolderInSharedDriveFolder(name, driveID, parentFolderID string) (*drive.File, error) {
	folder := &drive.File{
		Name:     name,
		MimeType: "application/vnd.google-apps.folder",
		Parents:  []string{parentFolderID},
	}

	res, err := s.api.Files().Create(folder).
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to create subfolder in shared drive: %v", err)
	}

	return res, nil
}

// FindFolderInSharedDrive finds a folder by name in shared drive
func (s *Service) FindFolderInSharedDrive(folderName, driveID string) (*drive.File, error) {
	query := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false", folderName)

	r, err := s.api.Files().List().
		Q(query).
		Corpora("drive").
		DriveId(driveID).
		IncludeItemsFromAllDrives(true).
		SupportsAllDrives(true).
		Fields("files(id,name,parents)").Do()
	if err != nil {
		return nil, fmt.Errorf("unable to search for folder: %v", err)
	}

	if len(r.Files) == 0 {
		return nil, fmt.Errorf("folder '%s' not found in shared drive", folderName)
	}

	return r.Files[0], nil
}

// ListSharedFolders lists folders directly shared with the service account
func (s *Service) ListSharedFolders() ([]*drive.File, error) {
	query := "mimeType='application/vnd.google-apps.folder' and sharedWithMe=true"
	r, err := s.api.Files().List().
		Q(query).
		PageSize(100).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		Fields("files(id,name,parents,sharingUser)").Do()
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve shared folders: %v", err)
	}
	return r.Files, nil
}

// ParseSharedURL parses a Google Drive URL and returns structured info about the resource.
func (s *Service) ParseSharedURL(input string) (*DriveInfo, error) {
	parsedURL, err := url.Parse(input)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	// It could be a direct ID instead of a URL
	if !parsedURL.IsAbs() && !strings.Contains(input, "/") {
		return s.GetDriveInfo(input)
	}

	return s.parseGoogleDriveURL(parsedURL)
}

// GetDriveInfo retrieves information about a Google Drive resource (file, folder, or shared drive) by its ID.
func (s *Service) GetDriveInfo(id string) (*DriveInfo, error) {
	// First, try to get it as a file/folder to see if it's in a shared drive.
	file, err := s.api.Files().Get(id).SupportsAllDrives(true).Fields("id, name, mimeType, driveId, parents").Do()
	if err == nil {
		info := &DriveInfo{
			ID:      file.Id,
			Name:    file.Name,
			DriveID: file.DriveId,
		}
		if file.MimeType == "application/vnd.google-apps.folder" {
			info.Type = ResourceTypeFolder
		} else {
			info.Type = ResourceTypeFile
		}
		return info, nil
	}

	// If getting it as a file failed, it might be a shared drive itself.
	drive, driveErr := s.api.Drives().Get(id).Fields("id, name").Do()
	if driveErr == nil {
		return &DriveInfo{
			ID:   drive.Id,
			Name: drive.Name,
			Type: ResourceTypeSharedDrive,
		}, nil
	}

	return nil, fmt.Errorf("unable to get info for ID '%s'. File/Folder API error: %v. Drive API error: %v", id, err, driveErr)
}

// ListFilesWithQuery executes a custom file search query
func (s *Service) ListFilesWithQuery(query, driveId string) ([]*drive.File, error) {
	q := s.api.Files().List().
		Q(query).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		PageSize(1000).
		Fields("files(id,name,size,mimeType,modifiedTime,webViewLink,webContentLink,createdTime,parents,driveId)")

	if driveId != "" {
		q = q.Corpora("drive").DriveId(driveId)
	}

	r, err := q.Do()
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve files with query: %v", err)
	}
	return r.Files, nil
}

func (s *Service) parseGoogleDriveURL(parsedURL *url.URL) (*DriveInfo, error) {
	path := parsedURL.Path
	re := regexp.MustCompile(`/(?:folders|file/d|drive/folders)/([^/?]*)`)
	matches := re.FindStringSubmatch(path)

	var resourceID string
	if len(matches) > 1 {
		resourceID = matches[1]
	} else {
		return nil, fmt.Errorf("could not extract resource ID from URL path: %s", path)
	}

	if resourceID == "" {
		return nil, fmt.Errorf("resource ID is empty")
	}

	return s.GetDriveInfo(resourceID)
}

func (s *Service) GetDriveAPI() *drive.Service {
	// This type assertion is safe because NewService always creates a realDriveAPI.
	// In tests, we would mock the DriveAPI and not call this method.
	return s.api.(*realDriveAPI).service
}

// ListFilesInFolder lists files in a specific folder with pagination
func (s *Service) ListFilesInFolder(folderID string, opts *PaginationOptions) (*PaginatedResult, error) {
	if opts == nil {
		opts = &PaginationOptions{PageSize: 100}
	}
	if opts.PageSize <= 0 || opts.PageSize > 1000 {
		opts.PageSize = 100
	}

	query := fmt.Sprintf("'%s' in parents and trashed=false", folderID)
	call := s.api.Files().List().
		Q(query).
		PageSize(opts.PageSize).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		Fields("nextPageToken, files(id,name,size,mimeType,modifiedTime,webViewLink,webContentLink,createdTime,parents,driveId)")

	if opts.PageToken != "" {
		call = call.PageToken(opts.PageToken)
	}

	r, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("unable to list files in folder: %v", err)
	}

	return &PaginatedResult{
		Files:         r.Files,
		NextPageToken: r.NextPageToken,
		HasNextPage:   r.NextPageToken != "",
	}, nil
}

// ListAllFilesInFolder gets all files in a folder using automatic pagination
func (s *Service) ListAllFilesInFolder(folderID string) ([]*drive.File, error) {
	var allFiles []*drive.File
	opts := &PaginationOptions{PageSize: 1000}

	for {
		result, err := s.ListFilesInFolder(folderID, opts)
		if err != nil {
			return nil, err
		}

		allFiles = append(allFiles, result.Files...)

		if !result.HasNextPage {
			break
		}
		opts.PageToken = result.NextPageToken
	}

	return allFiles, nil
}

// GetFileInfo retrieves detailed file information including name and web view link
func (s *Service) GetFileInfo(fileID string) (*drive.File, error) {
	file, err := s.api.Files().Get(fileID).
		SupportsAllDrives(true).
		Fields("id,name,webViewLink,parents,driveId,mimeType").
		Do()
	if err != nil {
		return nil, fmt.Errorf("unable to get file info: %w", err)
	}
	return file, nil
}

// FindFileInFolder finds a file by name in a specific folder
func (s *Service) FindFileInFolder(fileName, parentFolderID string) (*drive.File, error) {
	query := fmt.Sprintf("name='%s' and '%s' in parents and trashed=false", fileName, parentFolderID)

	r, err := s.api.Files().List().
		Q(query).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		Fields("files(id,name,parents,driveId,mimeType)").Do()
	if err != nil {
		return nil, fmt.Errorf("unable to search for file: %v", err)
	}

	if len(r.Files) == 0 {
		return nil, fmt.Errorf("file '%s' not found in folder", fileName)
	}

	return r.Files[0], nil
}

// RenameFile renames a file or folder
func (s *Service) RenameFile(fileID, newName string) (*drive.File, error) {
	file := &drive.File{Name: newName}

	result, err := s.api.Files().Update(fileID, file).
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to rename file: %w", err)
	}

	return result, nil
}

// CreateResumableUploadURL creates a resumable upload URL for large files
// This function creates a resumable upload session using Google Drive API v3 protocol
// Supports both My Drive and Shared Drives with validation for Service Account limitations
func (s *Service) CreateResumableUploadURL(filename, parentFolderID string, fileSize int64) (*ResumableUploadInfo, error) {
	// Validate parent folder for Service Account uploads
	if err := s.ValidateParentForServiceAccount(parentFolderID); err != nil {
		return nil, fmt.Errorf("parent folder validation failed: %w", err)
	}

	// Get detailed folder information to determine drive context
	folderInfo, err := s.api.Files().Get(parentFolderID).
		Fields("id,name,parents,driveId,shared,sharingUser").
		SupportsAllDrives(true).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to get parent folder info: %w", err)
	}

	// Create file metadata with proper drive context
	fileMetadata := &drive.File{
		Name:    filename,
		Parents: []string{parentFolderID},
	}

	// Since the Google Drive Go SDK doesn't expose the upload session URL directly,
	// we need to implement the resumable upload initiation manually.
	// This follows the Google Drive API v3 resumable upload protocol:
	// https://developers.google.com/drive/api/guides/manage-uploads#resumable

	return s.initiateResumableUpload(fileMetadata, fileSize, folderInfo.DriveId)
}

// initiateResumableUpload creates a resumable upload session and returns the upload URL
// Supports both My Drive and Shared Drives with proper error handling for Service Account limitations
func (s *Service) initiateResumableUpload(fileMetadata *drive.File, fileSize int64, driveId string) (*ResumableUploadInfo, error) {
	// Prepare the metadata as JSON for the request body
	metadataJSON, err := fileMetadata.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("unable to marshal file metadata: %w", err)
	}

	query := url.Values{}
	query.Add("uploadType", "resumable")
	// Enable Shared Drives support - critical for Service Account uploads
	query.Add("supportsAllDrives", "true")
	query.Add("includeItemsFromAllDrives", "true")

	// If uploading to a Shared Drive, add the driveId parameter
	if driveId != "" {
		query.Add("driveId", driveId)
	}

	// Create the initial POST request to initiate resumable upload
	uploadURL := "https://www.googleapis.com/upload/drive/v3/files?" + query.Encode()

	req, err := http.NewRequest("POST", uploadURL, strings.NewReader(string(metadataJSON)))
	if err != nil {
		return nil, fmt.Errorf("unable to create HTTP request: %w", err)
	}

	// Set required headers for resumable upload initiation
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	if fileSize > 0 {
		req.Header.Set("X-Upload-Content-Length", fmt.Sprintf("%d", fileSize))
	}

	// Execute the request using the authenticated HTTP client
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("unable to initiate resumable upload: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful response (Google Drive returns 200 OK for resumable upload initiation)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		bodyStr := string(body)

		// Enhanced error handling for Service Account storage quota issues
		if resp.StatusCode == http.StatusForbidden {
			// Check if this is the storage quota error for Service Accounts
			if strings.Contains(bodyStr, "Service Accounts do not have storage quota") ||
				strings.Contains(bodyStr, "storageQuotaExceeded") {
				return nil, fmt.Errorf("service account storage quota exceeded - must use Shared Drives or OAuth delegation. "+
					"Ensure parent folder is in a Shared Drive. Error details: %s", bodyStr)
			}
		}

		return nil, fmt.Errorf("resumable upload initiation failed with status %d: %s", resp.StatusCode, bodyStr)
	}

	// Extract the resumable upload URL from the Location header
	uploadSessionURL := resp.Header.Get("Location")
	if uploadSessionURL == "" {
		return nil, fmt.Errorf("no Location header found in resumable upload response")
	}

	// Return the actual upload session URL from Google Drive API
	return &ResumableUploadInfo{
		UploadURL: uploadSessionURL,
		FileID:    "", // File ID will be available after upload completion
	}, nil
}

// UploadToResumableURL uploads file content to a Google Drive resumable upload URL
func (s *Service) UploadToResumableURL(uploadURL string, content io.Reader, contentType string) (*drive.File, error) {
	if uploadURL == "" {
		return nil, fmt.Errorf("upload URL is required")
	}

	if content == nil {
		return nil, fmt.Errorf("content reader is required")
	}

	// Create PUT request to upload file content
	req, err := http.NewRequest("PUT", uploadURL, content)
	if err != nil {
		return nil, fmt.Errorf("unable to create upload request: %w", err)
	}

	// Set content type header
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	// Execute the upload request
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("unable to upload file content: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful upload (Google Drive returns 200 or 201 for successful uploads)
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		bodyStr := string(body)
		return nil, fmt.Errorf("file upload failed with status %d: %s", resp.StatusCode, bodyStr)
	}

	// Parse the response to get file metadata
	var driveFile drive.File
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("unable to read upload response: %w", err)
	}

	err = json.Unmarshal(body, &driveFile)
	if err != nil {
		return nil, fmt.Errorf("unable to parse upload response: %w", err)
	}

	return &driveFile, nil
}

// IsSharedDrive checks if the given ID is a Shared Drive
func (s *Service) IsSharedDrive(driveID string) (bool, error) {
	if driveID == "" {
		return false, nil
	}

	// Try to get the drive info - if it succeeds, it's a shared drive
	_, err := s.api.Drives().Get(driveID).Do()
	if err != nil {
		// If it fails, it might be a regular folder or file
		return false, nil
	}

	return true, nil
}

// IsSharedWithMe checks if a folder is shared with the Service Account
func (s *Service) IsSharedWithMe(folderID string) (bool, error) {
	if folderID == "" {
		return false, nil
	}

	// Query for the specific folder in sharedWithMe
	query := fmt.Sprintf("'%s' in parents and sharedWithMe=true", folderID)

	// Try to list files in the folder using sharedWithMe query
	// If this succeeds, the folder is shared with us
	_, err := s.api.Files().List().
		Q(query).
		PageSize(1).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		Fields("files(id)").Do()

	if err != nil {
		// If query fails, try direct approach - check if we can access the folder
		_, err := s.api.Files().Get(folderID).
			Fields("id,shared,sharingUser").
			SupportsAllDrives(true).Do()
		if err != nil {
			return false, nil // Can't access, so not shared with us
		}
		// If we can access it, assume it's shared (this is a fallback)
		return true, nil
	}

	// Alternative approach: check if the folder itself appears in sharedWithMe
	sharedQuery := fmt.Sprintf("id='%s' and sharedWithMe=true", folderID)
	sharedResult, err := s.api.Files().List().
		Q(sharedQuery).
		PageSize(1).
		SupportsAllDrives(true).
		IncludeItemsFromAllDrives(true).
		Fields("files(id)").Do()

	if err != nil {
		return false, nil
	}

	return len(sharedResult.Files) > 0, nil
}

// GetAvailableSharedDrives returns a list of Shared Drives accessible to the Service Account
// This helps users find suitable upload destinations
func (s *Service) GetAvailableSharedDrives() ([]*drive.Drive, error) {
	drives, err := s.api.Drives().List().
		PageSize(100).Do()
	if err != nil {
		return nil, fmt.Errorf("unable to list shared drives: %w", err)
	}

	return drives.Drives, nil
}

// ValidateParentForServiceAccount validates that the parent folder is suitable for Service Account uploads
// ONLY supports: Shared Drives and folders within Shared Drives
// Shared folders in My Drive are NOT supported due to Google Drive API limitations
func (s *Service) ValidateParentForServiceAccount(parentFolderID string) error {
	if strings.TrimSpace(parentFolderID) == "" {
		return fmt.Errorf("parent folder ID is required for Service Account uploads - cannot upload to My Drive root")
	}

	// Check if parent is a Shared Drive
	isSharedDrive, err := s.IsSharedDrive(parentFolderID)
	if err != nil {
		// If we can't determine, proceed but warn
		return nil
	}

	if isSharedDrive {
		// Parent is a Shared Drive - this is good for Service Accounts
		return nil
	}

	// Check if parent folder is inside a Shared Drive
	file, err := s.api.Files().Get(parentFolderID).
		Fields("id,name,parents,driveId,shared,sharingUser").
		SupportsAllDrives(true).Do()
	if err != nil {
		return fmt.Errorf("unable to validate parent folder: %w", err)
	}

	// If the file has a driveId, it's in a Shared Drive
	if file.DriveId != "" {
		return nil
	}

	// If we reach here, the folder is in My Drive (even if shared)
	// Google Drive API does not support Service Account uploads to My Drive folders,
	// even when they are shared with the Service Account
	return fmt.Errorf("parent folder is in My Drive - Service Accounts cannot upload to My Drive folders, "+
		"even when shared. Please use a Shared Drive instead. "+
		"Folder: '%s' (ID: %s)", file.Name, parentFolderID)
}

// Permission management methods implementation

// ListPermissions retrieves all permissions for a file/folder
func (s *Service) ListPermissions(ctx context.Context, fileID string) ([]*drive.Permission, error) {
	call := s.api.Permissions().List(fileID).
		Fields("permissions(id,type,role,emailAddress,displayName)").
		SupportsAllDrives(true)

	result, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to list permissions for file %s: %w", fileID, err)
	}

	return result.Permissions, nil
}

// CreatePermission creates a new permission for a file/folder
func (s *Service) CreatePermission(ctx context.Context, fileID string, permission *drive.Permission) (*drive.Permission, error) {
	call := s.api.Permissions().Create(fileID, permission).
		Fields("id,type,role,emailAddress,displayName").
		SupportsAllDrives(true).
		SendNotificationEmail(false) // Don't send email notifications

	result, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to create permission for file %s: %w", fileID, err)
	}

	return result, nil
}

// DeletePermission removes a permission from a file/folder
func (s *Service) DeletePermission(ctx context.Context, fileID, permissionID string) error {
	call := s.api.Permissions().Delete(fileID, permissionID).
		SupportsAllDrives(true)

	err := call.Do()
	if err != nil {
		return fmt.Errorf("failed to delete permission %s for file %s: %w", permissionID, fileID, err)
	}

	return nil
}

// BatchCreatePermissions creates multiple permissions with error handling
func (s *Service) BatchCreatePermissions(ctx context.Context, fileID string, permissions []*drive.Permission) ([]*drive.Permission, error) {
	var results []*drive.Permission
	var errors []error

	for i, perm := range permissions {
		result, err := s.CreatePermission(ctx, fileID, perm)
		if err != nil {
			errors = append(errors, fmt.Errorf("permission %d (%s): %w", i, perm.EmailAddress, err))
			continue
		}
		results = append(results, result)
	}

	if len(errors) > 0 {
		return results, fmt.Errorf("batch create had %d errors: %v", len(errors), errors)
	}

	return results, nil
}
