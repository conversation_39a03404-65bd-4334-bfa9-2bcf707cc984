package gdrive

import (
	"context"
	"io"
	"net/http"

	"google.golang.org/api/drive/v3"
	"google.golang.org/api/googleapi"
)

// Constants for pagination
const (
	DefaultPageSize int64 = 100
	MaxPageSize     int64 = 1000
)

// MIME type constants
const (
	MimeTypeFolder      = "application/vnd.google-apps.folder"
	MimeTypeSharedDrive = "application/vnd.google-apps.shortcut"
)

// DriveClient interface wraps Google Drive API operations for easier testing
type DriveClient interface {
	ListFiles(opts *PaginationOptions) (*PaginatedResult, error)
	ListFilesInSharedDrive(driveID string, opts *PaginationOptions) (*PaginatedResult, error)
	ListFilesInFolder(folderID string, opts *PaginationOptions) (*PaginatedResult, error)
	ListAllFilesInFolder(folderID string) ([]*drive.File, error)
	UploadFile(filename, filepath string) (*drive.File, error)
	UploadFileToSharedDrive(filename, filepath, driveID string) (*drive.File, error)
	UploadFileToSharedDriveFolder(filename, filepath, driveID, folderID string) (*drive.File, error)
	DownloadFile(fileID, outputPath string) error
	DeleteFile(fileID string) error
	RenameFile(fileID, newName string) (*drive.File, error)
	FindFileInFolder(fileName, parentFolderID string) (*drive.File, error)
	FindFolderInSharedDrive(folderName, driveID string) (*drive.File, error)
	CreateFolder(name, parentID string) (*drive.File, error)
	CreateFolderInSharedDrive(name, driveID string) (*drive.File, error)
	CreateFolderInSharedDriveFolder(name, driveID, parentFolderID string) (*drive.File, error)
	CreateResumableUploadURL(filename, parentFolderID string, fileSize int64) (*ResumableUploadInfo, error)
	UploadToResumableURL(uploadURL string, content io.Reader, contentType string) (*drive.File, error)
	IsSharedDrive(driveID string) (bool, error)
	IsSharedWithMe(folderID string) (bool, error)
	ValidateParentForServiceAccount(parentFolderID string) error
	GetAvailableSharedDrives() ([]*drive.Drive, error)
	ListSharedDrives() ([]*drive.Drive, error)
	ListSharedFolders() ([]*drive.File, error)
	ParseSharedURL(input string) (*DriveInfo, error)
	GetDriveInfo(id string) (*DriveInfo, error)
	ListFilesWithQuery(query string, driveID string) ([]*drive.File, error)
	GetFileInfo(fileID string) (*drive.File, error)

	// Permission management
	ListPermissions(ctx context.Context, fileID string) ([]*drive.Permission, error)
	CreatePermission(ctx context.Context, fileID string, permission *drive.Permission) (*drive.Permission, error)
	DeletePermission(ctx context.Context, fileID, permissionID string) error
	BatchCreatePermissions(ctx context.Context, fileID string, permissions []*drive.Permission) ([]*drive.Permission, error)
}

// FileSystem interface abstracts file system operations for testing
type FileSystem interface {
	Open(filename string) (io.ReadCloser, error)
	Create(filename string) (io.WriteCloser, error)
	Remove(filename string) error
	WriteFile(filename string, data []byte, perm uint32) error
}

// DriveAPIWrapper interface wraps the actual Google Drive API for mocking
type DriveAPIWrapper interface {
	ListFiles(pageSize int, fields string) ([]*drive.File, error)
	CreateFile(file *drive.File, media io.Reader, supportsAllDrives bool) (*drive.File, error)
	GetFileDownload(fileID string) (io.ReadCloser, error)
	ListDrives(pageSize int) ([]*drive.Drive, error)
	ListFilesInDrive(driveID string, pageSize int, fields string) ([]*drive.File, error)
	SearchFiles(query, driveID string, fields string) ([]*drive.File, error)
}

type ResourceType string

const (
	ResourceTypeFile        ResourceType = "file"
	ResourceTypeFolder      ResourceType = "folder"
	ResourceTypeSharedDrive ResourceType = "shared-drive"
)

// DriveInfo holds structured information about a Google Drive resource.
type DriveInfo struct {
	ID      string
	Name    string
	Type    ResourceType
	DriveID string // The ID of the Shared Drive this resource belongs to, if any.
}

// DriveAPI is an interface that wraps the Google Drive API.
// This is used for mocking the Google Drive API in tests.
type DriveAPI interface {
	Files() FilesAPI
	Drives() DrivesAPI
	Permissions() PermissionsAPI
}

// FilesAPI is an interface for file-related operations.
type FilesAPI interface {
	Get(fileId string) FilesGetCall
	Create(file *drive.File) FilesCreateCall
	Update(fileId string, file *drive.File) FilesUpdateCall
	List() FilesListCall
	Delete(fileId string) FilesDeleteCall
}

// DrivesAPI is an interface for drive-related operations.
type DrivesAPI interface {
	Get(driveId string) DrivesGetCall
	List() DrivesListCall
}

// PermissionsAPI is an interface for permission-related operations.
type PermissionsAPI interface {
	List(fileId string) PermissionsListCall
	Create(fileId string, permission *drive.Permission) PermissionsCreateCall
	Delete(fileId, permissionId string) PermissionsDeleteCall
	Get(fileId, permissionId string) PermissionsGetCall
	Update(fileId, permissionId string, permission *drive.Permission) PermissionsUpdateCall
}

// FilesGetCall is an interface for the Data.Get call.
type FilesGetCall interface {
	Fields(fields ...googleapi.Field) FilesGetCall
	SupportsAllDrives(supportsAllDrives bool) FilesGetCall
	Do(opts ...googleapi.CallOption) (*drive.File, error)
	Download(opts ...googleapi.CallOption) (*http.Response, error)
}

// FilesCreateCall is an interface for the Data.Create call.
type FilesCreateCall interface {
	Media(r io.Reader, options ...googleapi.MediaOption) FilesCreateCall
	SupportsAllDrives(supportsAllDrives bool) FilesCreateCall
	Do(opts ...googleapi.CallOption) (*drive.File, error)
}

// FilesListCall is an interface for the Data.List call.
type FilesListCall interface {
	PageSize(pageSize int64) *drive.FilesListCall
	Fields(fields ...googleapi.Field) *drive.FilesListCall
	Q(q string) *drive.FilesListCall
	Corpora(corpora string) *drive.FilesListCall
	DriveId(driveId string) *drive.FilesListCall
	IncludeItemsFromAllDrives(includeItemsFromAllDrives bool) *drive.FilesListCall
	SupportsAllDrives(supportsAllDrives bool) *drive.FilesListCall
	Do(opts ...googleapi.CallOption) (*drive.FileList, error)
}

// FilesDeleteCall is an interface for the Data.Delete call.
type FilesDeleteCall interface {
	SupportsAllDrives(supportsAllDrives bool) *drive.FilesDeleteCall
	Do(opts ...googleapi.CallOption) error
}

// DrivesGetCall is an interface for the Drives.Get call.
type DrivesGetCall interface {
	Fields(fields ...googleapi.Field) DrivesGetCall
	Do(opts ...googleapi.CallOption) (*drive.Drive, error)
}

// DrivesListCall is an interface for the Drives.List call.
type DrivesListCall interface {
	PageSize(pageSize int64) *drive.DrivesListCall
	Do(opts ...googleapi.CallOption) (*drive.DriveList, error)
}

// FilesUpdateCall interface for file update operations
type FilesUpdateCall interface {
	SupportsAllDrives(supportsAllDrives bool) FilesUpdateCall
	Do(opts ...googleapi.CallOption) (*drive.File, error)
}

// PermissionsListCall is an interface for the Permissions.List call.
type PermissionsListCall interface {
	Fields(fields ...googleapi.Field) PermissionsListCall
	SupportsAllDrives(supportsAllDrives bool) PermissionsListCall
	Do(opts ...googleapi.CallOption) (*drive.PermissionList, error)
}

// PermissionsCreateCall is an interface for the Permissions.Create call.
type PermissionsCreateCall interface {
	Fields(fields ...googleapi.Field) PermissionsCreateCall
	SupportsAllDrives(supportsAllDrives bool) PermissionsCreateCall
	SendNotificationEmail(sendNotificationEmail bool) PermissionsCreateCall
	Do(opts ...googleapi.CallOption) (*drive.Permission, error)
}

// PermissionsDeleteCall is an interface for the Permissions.Delete call.
type PermissionsDeleteCall interface {
	SupportsAllDrives(supportsAllDrives bool) PermissionsDeleteCall
	Do(opts ...googleapi.CallOption) error
}

// PermissionsGetCall is an interface for the Permissions.Get call.
type PermissionsGetCall interface {
	Fields(fields ...googleapi.Field) PermissionsGetCall
	SupportsAllDrives(supportsAllDrives bool) PermissionsGetCall
	Do(opts ...googleapi.CallOption) (*drive.Permission, error)
}

// PermissionsUpdateCall is an interface for the Permissions.Update call.
type PermissionsUpdateCall interface {
	Fields(fields ...googleapi.Field) PermissionsUpdateCall
	SupportsAllDrives(supportsAllDrives bool) PermissionsUpdateCall
	Do(opts ...googleapi.CallOption) (*drive.Permission, error)
}

// PaginationOptions contains options for paginated API calls
type PaginationOptions struct {
	PageSize  int64  `json:"pageSize"`  // Number of items per page (max 1000)
	PageToken string `json:"pageToken"` // Token to retrieve next page
}

// PaginatedResult contains paginated results from Drive API
type PaginatedResult struct {
	Files         []*drive.File `json:"files"`
	NextPageToken string        `json:"nextPageToken"` // Token for next page
	HasNextPage   bool          `json:"hasNextPage"`
}

// ResumableUploadInfo contains information for resumable upload
type ResumableUploadInfo struct {
	UploadURL string `json:"uploadUrl"`
	FileID    string `json:"fileId,omitempty"`
}
