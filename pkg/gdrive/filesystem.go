package gdrive

import (
	"io"
	"os"
)

// RealFileSystem implements FileSystem interface using actual OS operations
type RealFileSystem struct{}

// NewRealFileSystem creates a new real file system implementation
func NewRealFileSystem() *RealFileSystem {
	return &RealFileSystem{}
}

// Open opens a file for reading
func (fs *RealFileSystem) Open(filename string) (io.ReadCloser, error) {
	return os.Open(filename)
}

// <PERSON><PERSON> creates a file for writing
func (fs *RealFileSystem) Create(filename string) (io.WriteCloser, error) {
	return os.Create(filename)
}

// Remove removes a file
func (fs *RealFileSystem) Remove(filename string) error {
	return os.Remove(filename)
}

// WriteFile writes data to a file
func (fs *RealFileSystem) WriteFile(filename string, data []byte, perm uint32) error {
	return os.WriteFile(filename, data, os.FileMode(perm))
}
