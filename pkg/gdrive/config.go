package gdrive

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"os"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
)

// Config holds configuration for the Drive service
type Config struct {
	CredentialsFile string
	CredentialsJSON string
	Scopes          []string
	Context         context.Context
}

// Option defines functional options for Drive service configuration
type Option func(*Config)

// WithCredentialsFile sets the credentials file path
func WithCredentialsFile(path string) Option {
	return func(c *Config) {
		c.CredentialsFile = path
	}
}

// WithCredentialsJSON sets the base64 encoded credentials JSON
func WithCredentialsJSON(json string) Option {
	return func(c *Config) {
		c.CredentialsJSON = json
	}
}

// WithScopes sets the OAuth scopes
func WithScopes(scopes []string) Option {
	return func(c *Config) {
		c.Scopes = scopes
	}
}

// WithContext sets the context for API calls
func WithContext(ctx context.Context) Option {
	return func(c *Config) {
		c.Context = ctx
	}
}

// DefaultConfig returns a configuration with sensible defaults
func DefaultConfig() *Config {
	credentialsFile := "service-account.json"
	if envPath := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS"); envPath != "" {
		credentialsFile = envPath
	}

	return &Config{
		CredentialsFile: credentialsFile,
		Scopes:          []string{drive.DriveScope},
		Context:         context.Background(),
	}
}

// NewDriveService creates a new Google Drive service with the given config,
// and also returns the underlying HTTP client used.
func NewDriveService(config *Config) (*drive.Service, *http.Client, error) {
	var ts oauth2.TokenSource
	var err error

	decoded, err := base64.StdEncoding.DecodeString(config.CredentialsJSON)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode base64 credentials: %w", err)
	}
	creds, err := google.CredentialsFromJSON(config.Context, decoded, config.Scopes...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse credentials JSON: %w", err)
	}
	ts = creds.TokenSource

	httpClient := oauth2.NewClient(config.Context, ts)

	srv, err := drive.NewService(config.Context, option.WithHTTPClient(httpClient))
	if err != nil {
		return nil, nil, err
	}

	return srv, httpClient, nil
}
