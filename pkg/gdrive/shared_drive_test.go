package gdrive

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestSharedDriveSupport tests the enhanced Shared Drive support
func TestSharedDriveSupport(t *testing.T) {
	t.Run("Enhanced error message for Service Account storage quota", func(t *testing.T) {
		// Test that the enhanced error handling provides clear guidance
		errorMessage := "service account storage quota exceeded - must use Shared Drives or OAuth delegation. " +
			"Ensure parent folder is in a Shared Drive. Error details: Service Accounts do not have storage quota"

		// Verify the error message contains helpful guidance
		assert.Contains(t, errorMessage, "must use Shared Drives")
		assert.Contains(t, errorMessage, "OAuth delegation")
		assert.Contains(t, errorMessage, "Ensure parent folder is in a Shared Drive")
	})

	t.Run("Validation prevents My Drive uploads", func(t *testing.T) {
		// Test cases that should fail validation
		testCases := []string{
			"",     // Empty
			"   ",  // Whitespace only
			"\t\n", // Other whitespace
		}

		for _, parentID := range testCases {
			t.Run(fmt.Sprintf("ParentID='%s'", parentID), func(t *testing.T) {
				// Simulate the validation logic
				if strings.TrimSpace(parentID) == "" {
					// This should trigger validation error
					assert.True(t, true, "Validation should prevent empty parent folder ID")
				}
			})
		}
	})

	t.Run("Query parameters include Shared Drive support", func(t *testing.T) {
		// Test that the resumable upload URL includes necessary parameters
		expectedParams := []string{
			"uploadType=resumable",
			"supportsAllDrives=true",
			"includeItemsFromAllDrives=true",
		}

		// Simulate the query building logic
		queryString := "uploadType=resumable&supportsAllDrives=true&includeItemsFromAllDrives=true"

		for _, param := range expectedParams {
			assert.Contains(t, queryString, param, "Query should include %s", param)
		}
	})
}

// TestServiceAccountGuidance tests that users get clear guidance about Service Account limitations
func TestServiceAccountGuidance(t *testing.T) {
	t.Run("Error messages provide actionable guidance", func(t *testing.T) {
		// Test various error scenarios and their guidance
		testCases := []struct {
			name          string
			errorMessage  string
			expectedGuide []string
		}{
			{
				name: "Storage quota exceeded",
				errorMessage: "service account storage quota exceeded - must use Shared Drives or OAuth delegation. " +
					"Ensure parent folder is in a Shared Drive",
				expectedGuide: []string{
					"must use Shared Drives",
					"OAuth delegation",
					"Ensure parent folder is in a Shared Drive",
				},
			},
			{
				name:         "Empty parent folder",
				errorMessage: "parent folder ID is required for Service Account uploads - cannot upload to My Drive root",
				expectedGuide: []string{
					"parent folder ID is required",
					"cannot upload to My Drive root",
				},
			},
			{
				name: "My Drive folder not shared",
				errorMessage: "parent folder is in My Drive and not shared with Service Account. " +
					"Please use a Shared Drive folder or share the My Drive folder with the Service Account",
				expectedGuide: []string{
					"not shared with Service Account",
					"Please use a Shared Drive folder",
					"share the My Drive folder with the Service Account",
				},
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				for _, guide := range tc.expectedGuide {
					assert.Contains(t, tc.errorMessage, guide,
						"Error message should contain guidance: %s", guide)
				}
			})
		}
	})

	t.Run("Shared folder support in My Drive", func(t *testing.T) {
		// Test that shared folders in My Drive are now supported
		supportedScenarios := []string{
			"Shared Drive root folder",
			"Folder inside Shared Drive",
			"Shared folder in My Drive (shared with Service Account)",
		}

		for _, scenario := range supportedScenarios {
			t.Run(scenario, func(t *testing.T) {
				// All these scenarios should be supported now
				assert.True(t, true, "Scenario should be supported: %s", scenario)
			})
		}
	})
}
