package app

import (
	"bilabl/docman/pkg/gdrive"
	"bilabl/docman/pkg/handlers"
	"bilabl/docman/pkg/sharepointclient"
	"context"
	"fmt"
	"os"

	"github.com/caarlos0/env/v6"
	"github.com/gin-gonic/gin"
	"gitlab.com/goxp/cloud0/service"
)

var (
	name    = "docman"
	version = "v0.0.0"
)

type App struct {
	*service.BaseApp
	*handlers.Setting

	sp            sharepointclient.Client
	gdriveService gdrive.DriveClient
}

func (app *App) Router() *gin.Engine {
	return app.BaseApp.Router
}

func (app *App) GetGlobURL() string {
	return app.GlobURL
}

func (app *App) GetHrglassidURL() string {
	return app.HrglassidURL
}

func (app *App) GetConsumerURL() string {
	return app.ConsumerURL
}

func (app *App) GetGatewayBaseURL() string {
	return app.GatewayBaseURL
}

func (app *App) GetSetting() *handlers.Setting {
	return app.Setting
}

func (app *App) GetGdriveService() gdrive.DriveClient {
	return app.gdriveService
}

func newApp() (*App, error) {
	app := &App{
		BaseApp: service.NewApp(name, version),
		Setting: new(handlers.Setting),
	}

	if err := app.Initialize(); err != nil {
		return nil, err
	}

	err := env.Parse(app.Setting)
	if err != nil {
		return nil, fmt.Errorf("failed to create app: %v", err)
	}

	// Initialize Google Drive service
	gdriveCreds := os.Getenv("GOOGLE_CREDENTIALS_BASE64")
	if gdriveCreds == "" {
		// We can choose to fail fast or have a nil service
		// Failing fast is better for required services.
		return nil, fmt.Errorf("GOOGLE_CREDENTIALS_BASE64 environment variable not set")
	}
	gdriveService, err := gdrive.NewService(gdrive.WithCredentialsJSON(gdriveCreds))
	if err != nil {
		return nil, fmt.Errorf("failed to initialize google drive service: %w", err)
	}
	app.gdriveService = gdriveService

	handlers.Init(app)

	return app, nil
}

// Run initializes and starts the service
func Run() error {
	app, err := newApp()
	if err != nil {
		return err
	}
	return app.Start(context.Background())
}

func (app *App) GetSharepointClient() sharepointclient.Client {
	if app.sp == nil {
		app.sp = sharepointclient.NewClient(
			app.SPClientID,
			app.SPClientSecret,
			app.SPScope,
			sharepointclient.WithSharepointAdminConsentURI(fmt.Sprintf("%s/hrglassid/oauth2/callback/sharepoint-admin-consent", app.GetGatewayBaseURL())),
			sharepointclient.WithBaseURL(app.SPBaseUrl),
		)
	}

	return app.sp
}
