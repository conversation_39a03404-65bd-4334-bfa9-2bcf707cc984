package constant

const (
	// Header params
	HEADER_TENANT_ID = "x-tenant-id"
	HEADER_USER_ID   = "x-user-id"
	HEADER_ROLES     = "x-user-roles"

	// Role enums
	ROLE_STAFF    = 1
	ROLE_LEAD     = 2
	ROLE_MANAGER  = 4
	ROLE_ADMIN    = 8
	ROLE_SYSADMIN = 16

	// Service urls
	GLOB_SERVICE_URL = "http://glob"
	USER_SERVICE_URL = "http://hrglassid"
	CONSUMER_URL     = "http://consumer/events"
)

type ActivityObjectType int

const (
	OBJECT_TYPE_CLIENT ActivityObjectType = iota + 1
	OBJECT_TYPE_STAFF
	OBJECT_TYPE_MATTER
	OBJECT_TYPE_DOCUMENT
	OBJECT_TYPE_TIMESHEET
	OBJECT_TYPE_COST
	OBJECT_TYPE_BILLING
	OBJECT_TYPE_PAYMENT
	OBJECT_TYPE_TASK
	OBJECT_TYPE_BILLING_MANUAL
)

type ActivityAction int

const (
	ACTION_CREATE ActivityAction = iota + 1
	ACTION_UPDATE
	ACTION_DELETE
)

type ActivityType int

const (
	TYPE_STAFF ActivityType = iota + 1
	TYPE_CLIENT
	TYPE_MATTER
	TYPE_DOCUMENT
	TYPE_TIMESHEET
	TYPE_COST
	TYPE_BILLING
	TYPE_PAYMENT
	TYPE_EXCHANGE_RATE
	TYPE_WORK_HOURS
	TYPE_CLIENT_RELATION
	TYPE_CLIENT_NOTE
	TYPE_MATTER_NOTE
	TYPE_BILLING_NOTE
)

type DocType int

const (
	DOCTYPE_FOLDER DocType = iota + 1
	DOCTYPE_FILE
)
