package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// ValidationResult represents the result of configuration validation
type ValidationResult struct {
	Valid    bool                   `json:"valid"`
	Errors   []ValidationError      `json:"errors,omitempty"`
	Warnings []ValidationWarning    `json:"warnings,omitempty"`
	Info     map[string]interface{} `json:"info,omitempty"`
}

// ValidationError represents a configuration validation error
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ValidationWarning represents a configuration validation warning
type ValidationWarning struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ConfigValidator provides comprehensive configuration validation
type ConfigValidator struct {
	config *AutoDocConfig
}

// NewConfigValidator creates a new configuration validator
func NewConfigValidator(config *AutoDocConfig) *ConfigValidator {
	return &ConfigValidator{
		config: config,
	}
}

// ValidateComplete performs comprehensive validation of the configuration
func (v *ConfigValidator) ValidateComplete() *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   make([]ValidationError, 0),
		Warnings: make([]ValidationWarning, 0),
		Info:     make(map[string]interface{}),
	}

	// Validate core settings
	v.validateCoreSettings(result)

	// Validate feature flags
	v.validateFeatureFlags(result)

	// Validate performance settings
	v.validatePerformanceSettings(result)

	// Validate provider configurations
	v.validateProviderConfigurations(result)

	// Validate monitoring settings
	v.validateMonitoringSettings(result)

	// Validate security settings
	v.validateSecuritySettings(result)

	// Validate logging settings
	v.validateLoggingSettings(result)

	// Validate cross-dependencies
	v.validateCrossDependencies(result)

	// Set overall validity
	result.Valid = len(result.Errors) == 0

	// Add summary information
	result.Info["total_errors"] = len(result.Errors)
	result.Info["total_warnings"] = len(result.Warnings)
	result.Info["enabled_providers"] = v.config.GetEnabledProviders()
	result.Info["validation_timestamp"] = time.Now().UTC()

	return result
}

// validateCoreSettings validates core AutoDoc settings
func (v *ConfigValidator) validateCoreSettings(result *ValidationResult) {
	// Validate default provider
	validProviders := []string{"internal", "gdrive", "sharepoint"}
	if !contains(validProviders, v.config.DefaultProvider) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "default_provider",
			Message: fmt.Sprintf("invalid default provider, must be one of %v", validProviders),
			Value:   v.config.DefaultProvider,
		})
	}

	// Validate max rules per tenant
	if v.config.MaxRulesPerTenant <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "max_rules_per_tenant",
			Message: "must be greater than 0",
			Value:   v.config.MaxRulesPerTenant,
		})
	} else if v.config.MaxRulesPerTenant > 1000 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "max_rules_per_tenant",
			Message: "very high limit may impact performance",
			Value:   v.config.MaxRulesPerTenant,
		})
	}

	// Check if AutoDoc is enabled but no providers are enabled
	if v.config.Enabled && len(v.config.GetEnabledProviders()) == 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "enabled",
			Message: "AutoDoc is enabled but no providers are enabled",
			Value:   v.config.Enabled,
		})
	}
}

// validateFeatureFlags validates feature flag settings
func (v *ConfigValidator) validateFeatureFlags(result *ValidationResult) {
	// If AutoDoc is disabled, warn about enabled features
	if !v.config.Enabled {
		if v.config.Features.Rules || v.config.Features.Providers || v.config.Features.Files {
			result.Warnings = append(result.Warnings, ValidationWarning{
				Field:   "features",
				Message: "features are enabled but AutoDoc is disabled",
				Value:   v.config.Features,
			})
		}
	}

	// If multi-provider is enabled, check if multiple providers are available
	if v.config.Features.MultiProvider {
		enabledProviders := v.config.GetEnabledProviders()
		if len(enabledProviders) < 2 {
			result.Warnings = append(result.Warnings, ValidationWarning{
				Field:   "features.multi_provider",
				Message: "multi-provider feature enabled but less than 2 providers are enabled",
				Value:   enabledProviders,
			})
		}
	}
}

// validatePerformanceSettings validates performance-related settings
func (v *ConfigValidator) validatePerformanceSettings(result *ValidationResult) {
	perf := v.config.Performance

	// Validate rule execution timeout
	if perf.RuleExecutionTimeout <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.rule_execution_timeout",
			Message: "must be greater than 0",
			Value:   perf.RuleExecutionTimeout,
		})
	} else if perf.RuleExecutionTimeout > 5*time.Minute {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "performance.rule_execution_timeout",
			Message: "very long timeout may cause resource issues",
			Value:   perf.RuleExecutionTimeout,
		})
	}

	// Validate batch size
	if perf.BatchSize <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.batch_size",
			Message: "must be greater than 0",
			Value:   perf.BatchSize,
		})
	} else if perf.BatchSize > 1000 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "performance.batch_size",
			Message: "very large batch size may impact memory usage",
			Value:   perf.BatchSize,
		})
	}

	// Validate max concurrent rules
	if perf.MaxConcurrentRules <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.max_concurrent_rules",
			Message: "must be greater than 0",
			Value:   perf.MaxConcurrentRules,
		})
	} else if perf.MaxConcurrentRules > 100 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "performance.max_concurrent_rules",
			Message: "very high concurrency may overwhelm system resources",
			Value:   perf.MaxConcurrentRules,
		})
	}

	// Validate provider timeout
	if perf.ProviderTimeout <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.provider_timeout",
			Message: "must be greater than 0",
			Value:   perf.ProviderTimeout,
		})
	}

	// Validate retry settings
	if perf.RetryAttempts < 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.retry_attempts",
			Message: "cannot be negative",
			Value:   perf.RetryAttempts,
		})
	} else if perf.RetryAttempts > 10 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "performance.retry_attempts",
			Message: "high retry count may cause delays",
			Value:   perf.RetryAttempts,
		})
	}

	if perf.RetryDelay <= 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "performance.retry_delay",
			Message: "must be greater than 0",
			Value:   perf.RetryDelay,
		})
	}
}

// validateProviderConfigurations validates provider-specific configurations
func (v *ConfigValidator) validateProviderConfigurations(result *ValidationResult) {
	providers := v.config.Providers

	// Validate internal provider
	if providers.Internal.Enabled {
		if providers.Internal.StoragePath == "" {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "providers.internal.storage_path",
				Message: "storage path is required when internal provider is enabled",
				Value:   providers.Internal.StoragePath,
			})
		} else {
			// Check if storage path is accessible
			if err := v.validateStoragePath(providers.Internal.StoragePath); err != nil {
				result.Warnings = append(result.Warnings, ValidationWarning{
					Field:   "providers.internal.storage_path",
					Message: fmt.Sprintf("storage path validation failed: %v", err),
					Value:   providers.Internal.StoragePath,
				})
			}
		}

		if providers.Internal.MaxFileSize <= 0 {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "providers.internal.max_file_size",
				Message: "must be greater than 0",
				Value:   providers.Internal.MaxFileSize,
			})
		}
	}

	// Validate Google Drive provider
	if providers.GoogleDrive.Enabled {
		if providers.GoogleDrive.ServiceAccountKeyPath == "" {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "providers.gdrive.service_account_key_path",
				Message: "service account key path is required when Google Drive is enabled",
				Value:   providers.GoogleDrive.ServiceAccountKeyPath,
			})
		} else {
			// Check if service account key file exists
			if _, err := os.Stat(providers.GoogleDrive.ServiceAccountKeyPath); os.IsNotExist(err) {
				result.Errors = append(result.Errors, ValidationError{
					Field:   "providers.gdrive.service_account_key_path",
					Message: "service account key file does not exist",
					Value:   providers.GoogleDrive.ServiceAccountKeyPath,
				})
			}
		}

		if providers.GoogleDrive.RateLimitQPS <= 0 {
			result.Warnings = append(result.Warnings, ValidationWarning{
				Field:   "providers.gdrive.rate_limit_qps",
				Message: "rate limiting disabled, may hit API limits",
				Value:   providers.GoogleDrive.RateLimitQPS,
			})
		}
	}

	// Validate SharePoint provider
	if providers.SharePoint.Enabled {
		requiredFields := map[string]string{
			"providers.sharepoint.client_id":     providers.SharePoint.ClientID,
			"providers.sharepoint.client_secret": providers.SharePoint.ClientSecret,
			"providers.sharepoint.tenant_id":     providers.SharePoint.TenantID,
		}

		for field, value := range requiredFields {
			if value == "" {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Message: "required when SharePoint is enabled",
					Value:   value,
				})
			}
		}
	}
}

// validateMonitoringSettings validates monitoring and health check settings
func (v *ConfigValidator) validateMonitoringSettings(result *ValidationResult) {
	monitoring := v.config.Monitoring

	if monitoring.Enabled {
		if monitoring.HealthCheckInterval <= 0 {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "monitoring.health_check_interval",
				Message: "must be greater than 0 when monitoring is enabled",
				Value:   monitoring.HealthCheckInterval,
			})
		}

		if monitoring.ProviderHealthTimeout <= 0 {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "monitoring.provider_health_timeout",
				Message: "must be greater than 0 when monitoring is enabled",
				Value:   monitoring.ProviderHealthTimeout,
			})
		}

		if monitoring.MetricsEnabled {
			if monitoring.MetricsPort <= 0 || monitoring.MetricsPort > 65535 {
				result.Errors = append(result.Errors, ValidationError{
					Field:   "monitoring.metrics_port",
					Message: "must be a valid port number (1-65535)",
					Value:   monitoring.MetricsPort,
				})
			}

			if monitoring.MetricsPath == "" {
				result.Errors = append(result.Errors, ValidationError{
					Field:   "monitoring.metrics_path",
					Message: "metrics path is required when metrics are enabled",
					Value:   monitoring.MetricsPath,
				})
			}
		}
	}
}

// validateSecuritySettings validates security-related settings
func (v *ConfigValidator) validateSecuritySettings(result *ValidationResult) {
	security := v.config.Security

	if security.RateLimitEnabled {
		if security.RateLimitRequestsPerHour <= 0 {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "security.rate_limit_requests_per_hour",
				Message: "must be greater than 0 when rate limiting is enabled",
				Value:   security.RateLimitRequestsPerHour,
			})
		}

		if security.RateLimitBurst <= 0 {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "security.rate_limit_burst",
				Message: "must be greater than 0 when rate limiting is enabled",
				Value:   security.RateLimitBurst,
			})
		}
	}
}

// validateLoggingSettings validates logging configuration
func (v *ConfigValidator) validateLoggingSettings(result *ValidationResult) {
	logging := v.config.Logging

	validLogLevels := []string{"debug", "info", "warn", "error"}
	if !contains(validLogLevels, logging.Level) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "logging.level",
			Message: fmt.Sprintf("invalid log level, must be one of %v", validLogLevels),
			Value:   logging.Level,
		})
	}

	validLogFormats := []string{"json", "text"}
	if !contains(validLogFormats, logging.Format) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "logging.format",
			Message: fmt.Sprintf("invalid log format, must be one of %v", validLogFormats),
			Value:   logging.Format,
		})
	}

	if logging.EnableFile {
		if logging.FilePath == "" {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "logging.file_path",
				Message: "file path is required when file logging is enabled",
				Value:   logging.FilePath,
			})
		} else {
			// Check if log directory is writable
			logDir := filepath.Dir(logging.FilePath)
			if err := v.validateLogDirectory(logDir); err != nil {
				result.Warnings = append(result.Warnings, ValidationWarning{
					Field:   "logging.file_path",
					Message: fmt.Sprintf("log directory validation failed: %v", err),
					Value:   logging.FilePath,
				})
			}
		}
	}
}

// validateCrossDependencies validates cross-component dependencies
func (v *ConfigValidator) validateCrossDependencies(result *ValidationResult) {
	// If default provider is not enabled, that's an error
	if !v.config.IsProviderEnabled(v.config.DefaultProvider) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "default_provider",
			Message: fmt.Sprintf("default provider '%s' is not enabled", v.config.DefaultProvider),
			Value:   v.config.DefaultProvider,
		})
	}

	// If metrics are enabled but monitoring is disabled, that's inconsistent
	if v.config.Monitoring.MetricsEnabled && !v.config.Monitoring.Enabled {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "monitoring.metrics_enabled",
			Message: "metrics enabled but monitoring is disabled",
			Value:   v.config.Monitoring.MetricsEnabled,
		})
	}
}

// validateStoragePath validates that a storage path is accessible
func (v *ConfigValidator) validateStoragePath(path string) error {
	// Check if path exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		// Try to create directory
		if err := os.MkdirAll(path, 0755); err != nil {
			return fmt.Errorf("cannot create storage directory: %w", err)
		}
	}

	// Check if path is writable
	testFile := filepath.Join(path, ".write_test")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		return fmt.Errorf("storage path is not writable: %w", err)
	}

	// Clean up test file
	os.Remove(testFile)

	return nil
}

// validateLogDirectory validates that a log directory is writable
func (v *ConfigValidator) validateLogDirectory(dir string) error {
	// Check if directory exists
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// Try to create directory
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("cannot create log directory: %w", err)
		}
	}

	// Check if directory is writable
	testFile := filepath.Join(dir, ".write_test")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		return fmt.Errorf("log directory is not writable: %w", err)
	}

	// Clean up test file
	os.Remove(testFile)

	return nil
}
