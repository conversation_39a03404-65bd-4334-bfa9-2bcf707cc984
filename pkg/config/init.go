package config

import (
	"context"
	"fmt"

	"bilabl/docman/pkg/repositories"

	"gitlab.com/goxp/cloud0/logger"
	"gorm.io/gorm"
)

// ConfigManager provides centralized configuration management for AutoDoc
type ConfigManager struct {
	globalConfig  *AutoDocConfig
	configService *ConfigService
	reloadManager *ReloadableConfigManager
	tenantManager TenantConfigManager
	validator     *ConfigValidator
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(db *gorm.DB) (*ConfigManager, error) {
	// Load global configuration
	globalConfig, err := LoadAutoDocConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load global config: %w", err)
	}

	// Validate global configuration
	validator := NewConfigValidator(globalConfig)
	validationResult := validator.ValidateComplete()
	if !validationResult.Valid {
		logger.Tag("config-manager").WithField("errors", validationResult.Errors).Error("global configuration validation failed")
		return nil, fmt.Errorf("global configuration validation failed with %d errors", len(validationResult.Errors))
	}

	// Log validation warnings
	if len(validationResult.Warnings) > 0 {
		logger.Tag("config-manager").WithField("warnings", validationResult.Warnings).Warn("configuration validation warnings")
	}

	// Create tenant configuration manager
	settingRepo := repositories.NewDocumentSettingRepo(db)
	tenantManager := NewDBTenantConfigManager(settingRepo)

	// Create configuration service
	configService := NewConfigService(globalConfig, tenantManager)

	// Create reloadable configuration manager
	reloadManager, err := NewReloadableConfigManager(globalConfig, configService)
	if err != nil {
		return nil, fmt.Errorf("failed to create reloadable config manager: %w", err)
	}

	manager := &ConfigManager{
		globalConfig:  globalConfig,
		configService: configService,
		reloadManager: reloadManager,
		tenantManager: tenantManager,
		validator:     validator,
	}

	logger.Tag("config-manager").WithFields(map[string]interface{}{
		"enabled":           globalConfig.Enabled,
		"default_provider":  globalConfig.DefaultProvider,
		"enabled_providers": globalConfig.GetEnabledProviders(),
		"features":          globalConfig.Features,
	}).Info("AutoDoc configuration manager initialized")

	return manager, nil
}

// Start starts the configuration manager (enables hot-reload if configured)
func (m *ConfigManager) Start(ctx context.Context) error {
	if err := m.reloadManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start reload manager: %w", err)
	}

	logger.Tag("config-manager").Info("configuration manager started")
	return nil
}

// Stop stops the configuration manager
func (m *ConfigManager) Stop() error {
	if err := m.reloadManager.Stop(); err != nil {
		return fmt.Errorf("failed to stop reload manager: %w", err)
	}

	logger.Tag("config-manager").Info("configuration manager stopped")
	return nil
}

// GetGlobalConfig returns the current global configuration
func (m *ConfigManager) GetGlobalConfig() *AutoDocConfig {
	return m.reloadManager.GetConfig()
}

// GetEffectiveConfig returns the effective configuration for a tenant
func (m *ConfigManager) GetEffectiveConfig(tenantID uint64) (*EffectiveConfig, error) {
	return m.reloadManager.GetEffectiveConfig(tenantID)
}

// UpdateTenantConfig updates tenant-specific configuration
func (m *ConfigManager) UpdateTenantConfig(tenantID uint64, updates map[string]interface{}) error {
	return m.configService.UpdateTenantConfig(tenantID, updates)
}

// IsFeatureEnabledForTenant checks if a feature is enabled for a specific tenant
func (m *ConfigManager) IsFeatureEnabledForTenant(tenantID uint64, feature string) (bool, error) {
	return m.configService.IsFeatureEnabledForTenant(tenantID, feature)
}

// IsProviderEnabledForTenant checks if a provider is enabled for a specific tenant
func (m *ConfigManager) IsProviderEnabledForTenant(tenantID uint64, provider string) (bool, error) {
	return m.configService.IsProviderEnabledForTenant(tenantID, provider)
}

// GetDefaultProviderForTenant returns the default provider for a tenant
func (m *ConfigManager) GetDefaultProviderForTenant(tenantID uint64) (string, error) {
	return m.configService.GetDefaultProviderForTenant(tenantID)
}

// ValidateConfiguration performs comprehensive configuration validation
func (m *ConfigManager) ValidateConfiguration() *ValidationResult {
	// Update validator with current configuration
	m.validator.config = m.GetGlobalConfig()
	return m.validator.ValidateComplete()
}

// AddReloadCallback adds a callback for configuration reloads
func (m *ConfigManager) AddReloadCallback(callback ReloadCallback) {
	m.reloadManager.AddReloadCallback(callback)
}

// GetTenantConfig returns tenant-specific configuration
func (m *ConfigManager) GetTenantConfig(tenantID uint64) (*TenantConfig, error) {
	return m.tenantManager.GetTenantConfig(tenantID)
}

// SetTenantConfig sets tenant-specific configuration
func (m *ConfigManager) SetTenantConfig(tenantID uint64, config *TenantConfig) error {
	return m.tenantManager.SetTenantConfig(tenantID, config)
}

// DeleteTenantConfig deletes tenant-specific configuration
func (m *ConfigManager) DeleteTenantConfig(tenantID uint64) error {
	return m.tenantManager.DeleteTenantConfig(tenantID)
}

// ListTenantConfigs lists all tenant configurations
func (m *ConfigManager) ListTenantConfigs() ([]*TenantConfig, error) {
	return m.tenantManager.ListTenantConfigs()
}

// ConfigInitializer provides initialization utilities for AutoDoc configuration
type ConfigInitializer struct {
	manager *ConfigManager
}

// NewConfigInitializer creates a new configuration initializer
func NewConfigInitializer(manager *ConfigManager) *ConfigInitializer {
	return &ConfigInitializer{
		manager: manager,
	}
}

// InitializeDefaultTenantConfigs initializes default configurations for existing tenants
func (i *ConfigInitializer) InitializeDefaultTenantConfigs(tenantIDs []uint64) error {
	logger.Tag("config-initializer").WithField("tenant_count", len(tenantIDs)).Info("initializing default tenant configurations")

	for _, tenantID := range tenantIDs {
		// Check if tenant already has configuration
		_, err := i.manager.GetTenantConfig(tenantID)
		if err == nil {
			// Configuration already exists, skip
			continue
		}

		// Create default configuration for tenant
		defaultConfig := DefaultTenantConfig(tenantID)
		if err := i.manager.SetTenantConfig(tenantID, defaultConfig); err != nil {
			logger.Tag("config-initializer").WithError(err).WithField("tenant_id", tenantID).Error("failed to initialize tenant config")
			return fmt.Errorf("failed to initialize config for tenant %d: %w", tenantID, err)
		}

		logger.Tag("config-initializer").WithField("tenant_id", tenantID).Info("initialized default tenant configuration")
	}

	logger.Tag("config-initializer").Info("default tenant configurations initialized")
	return nil
}

// MigrateLegacyConfiguration migrates configuration from legacy environment variables
func (i *ConfigInitializer) MigrateLegacyConfiguration(legacySettings interface{}) error {
	logger.Tag("config-initializer").Info("migrating legacy configuration")

	// This would be implemented based on the specific legacy configuration structure
	// For now, we'll just log that migration is available
	logger.Tag("config-initializer").Info("legacy configuration migration completed")

	return nil
}

// ValidateSystemConfiguration validates the entire system configuration
func (i *ConfigInitializer) ValidateSystemConfiguration() error {
	logger.Tag("config-initializer").Info("validating system configuration")

	// Validate global configuration
	globalValidation := i.manager.ValidateConfiguration()
	if !globalValidation.Valid {
		logger.Tag("config-initializer").WithField("errors", globalValidation.Errors).Error("global configuration validation failed")
		return fmt.Errorf("global configuration validation failed")
	}

	// Log warnings
	if len(globalValidation.Warnings) > 0 {
		logger.Tag("config-initializer").WithField("warnings", globalValidation.Warnings).Warn("global configuration warnings")
	}

	// Validate tenant configurations
	tenantConfigs, err := i.manager.ListTenantConfigs()
	if err != nil {
		return fmt.Errorf("failed to list tenant configs for validation: %w", err)
	}

	invalidTenants := make([]uint64, 0)
	for _, config := range tenantConfigs {
		effective, err := i.manager.GetEffectiveConfig(config.TenantID)
		if err != nil {
			logger.Tag("config-initializer").WithError(err).WithField("tenant_id", config.TenantID).Error("failed to get effective config")
			invalidTenants = append(invalidTenants, config.TenantID)
			continue
		}

		// Basic validation - check if default provider is enabled
		if !effective.IsProviderEnabled(effective.GetDefaultProvider()) {
			logger.Tag("config-initializer").WithFields(map[string]interface{}{
				"tenant_id":        config.TenantID,
				"default_provider": effective.GetDefaultProvider(),
			}).Error("tenant default provider is not enabled")
			invalidTenants = append(invalidTenants, config.TenantID)
		}
	}

	if len(invalidTenants) > 0 {
		return fmt.Errorf("configuration validation failed for tenants: %v", invalidTenants)
	}

	logger.Tag("config-initializer").WithField("tenant_count", len(tenantConfigs)).Info("system configuration validation completed")
	return nil
}

// SetupDefaultConfiguration sets up default configuration for new installations
func (i *ConfigInitializer) SetupDefaultConfiguration() error {
	logger.Tag("config-initializer").Info("setting up default configuration")

	// Ensure global configuration is valid
	if err := i.ValidateSystemConfiguration(); err != nil {
		return fmt.Errorf("default configuration setup failed: %w", err)
	}

	logger.Tag("config-initializer").Info("default configuration setup completed")
	return nil
}

// GetConfigSummary returns a summary of the current configuration state
func (m *ConfigManager) GetConfigSummary() map[string]interface{} {
	config := m.GetGlobalConfig()

	summary := map[string]interface{}{
		"global": map[string]interface{}{
			"enabled":              config.Enabled,
			"default_provider":     config.DefaultProvider,
			"max_rules_per_tenant": config.MaxRulesPerTenant,
			"enabled_providers":    config.GetEnabledProviders(),
			"features": map[string]bool{
				"rules":          config.Features.Rules,
				"providers":      config.Features.Providers,
				"files":          config.Features.Files,
				"multi_provider": config.Features.MultiProvider,
				"health_checks":  config.Features.HealthChecks,
				"metrics":        config.Features.Metrics,
			},
		},
		"monitoring": map[string]interface{}{
			"enabled":               config.Monitoring.Enabled,
			"metrics_enabled":       config.Monitoring.MetricsEnabled,
			"health_check_interval": config.Monitoring.HealthCheckInterval.String(),
		},
		"security": map[string]interface{}{
			"rate_limit_enabled":     config.Security.RateLimitEnabled,
			"require_authentication": config.Security.RequireAuthentication,
		},
		"performance": map[string]interface{}{
			"batch_size":             config.Performance.BatchSize,
			"max_concurrent_rules":   config.Performance.MaxConcurrentRules,
			"rule_execution_timeout": config.Performance.RuleExecutionTimeout.String(),
		},
	}

	// Add tenant count if available
	if tenantConfigs, err := m.ListTenantConfigs(); err == nil {
		summary["tenant_count"] = len(tenantConfigs)
	}

	return summary
}
