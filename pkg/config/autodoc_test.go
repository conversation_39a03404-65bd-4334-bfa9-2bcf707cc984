package config

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultAutoDocConfig(t *testing.T) {
	config := DefaultAutoDocConfig()

	assert.False(t, config.Enabled, "AutoDoc should be disabled by default")
	assert.Equal(t, "internal", config.DefaultProvider)
	assert.Equal(t, 100, config.MaxRulesPerTenant)
	assert.True(t, config.Features.Rules)
	assert.True(t, config.Features.Providers)
	assert.True(t, config.Features.Files)
	assert.False(t, config.Features.MultiProvider, "Multi-provider should be disabled by default")
	assert.True(t, config.Providers.Internal.Enabled)
	assert.False(t, config.Providers.GoogleDrive.Enabled)
	assert.False(t, config.Providers.SharePoint.Enabled)
}

func TestAutoDocConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		setupConfig func() *AutoDocConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid default config",
			setupConfig: func() *AutoDocConfig {
				return DefaultAutoDocConfig()
			},
			expectError: false,
		},
		{
			name: "invalid default provider",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.DefaultProvider = "invalid"
				return config
			},
			expectError: true,
			errorMsg:    "invalid default provider",
		},
		{
			name: "invalid max rules per tenant - zero",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.MaxRulesPerTenant = 0
				return config
			},
			expectError: true,
			errorMsg:    "max_rules_per_tenant must be between 1 and 1000",
		},
		{
			name: "invalid max rules per tenant - too high",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.MaxRulesPerTenant = 1001
				return config
			},
			expectError: true,
			errorMsg:    "max_rules_per_tenant must be between 1 and 1000",
		},
		{
			name: "invalid batch size",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Performance.BatchSize = 0
				return config
			},
			expectError: true,
			errorMsg:    "batch_size must be between 1 and 1000",
		},
		{
			name: "invalid max concurrent rules",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Performance.MaxConcurrentRules = 0
				return config
			},
			expectError: true,
			errorMsg:    "max_concurrent_rules must be between 1 and 100",
		},
		{
			name: "no providers enabled",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Providers.Internal.Enabled = false
				config.Providers.GoogleDrive.Enabled = false
				config.Providers.SharePoint.Enabled = false
				return config
			},
			expectError: true,
			errorMsg:    "at least one provider must be enabled",
		},
		{
			name: "google drive enabled without service account key",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Providers.GoogleDrive.Enabled = true
				config.Providers.GoogleDrive.ServiceAccountKeyPath = ""
				return config
			},
			expectError: true,
			errorMsg:    "gdrive service_account_key_path is required",
		},
		{
			name: "sharepoint enabled without credentials",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Providers.SharePoint.Enabled = true
				config.Providers.SharePoint.ClientID = ""
				return config
			},
			expectError: true,
			errorMsg:    "sharepoint client_id, client_secret, and tenant_id are required",
		},
		{
			name: "invalid log level",
			setupConfig: func() *AutoDocConfig {
				config := DefaultAutoDocConfig()
				config.Logging.Level = "invalid"
				return config
			},
			expectError: true,
			errorMsg:    "invalid log level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := tt.setupConfig()
			err := config.Validate()

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAutoDocConfigFeatureFlags(t *testing.T) {
	config := DefaultAutoDocConfig()
	config.Enabled = true

	// Test feature enabled checks
	assert.True(t, config.IsFeatureEnabled("rules"))
	assert.True(t, config.IsFeatureEnabled("providers"))
	assert.True(t, config.IsFeatureEnabled("files"))
	assert.False(t, config.IsFeatureEnabled("multi_provider"))
	assert.False(t, config.IsFeatureEnabled("invalid_feature"))

	// Test when AutoDoc is disabled
	config.Enabled = false
	assert.False(t, config.IsFeatureEnabled("rules"))
	assert.False(t, config.IsFeatureEnabled("providers"))
}

func TestAutoDocConfigProviders(t *testing.T) {
	config := DefaultAutoDocConfig()
	config.Enabled = true

	// Test provider enabled checks
	assert.True(t, config.IsProviderEnabled("internal"))
	assert.False(t, config.IsProviderEnabled("gdrive"))
	assert.False(t, config.IsProviderEnabled("sharepoint"))
	assert.False(t, config.IsProviderEnabled("invalid_provider"))

	// Test when AutoDoc is disabled
	config.Enabled = false
	assert.False(t, config.IsProviderEnabled("internal"))

	// Test enabled providers list
	config.Enabled = true
	config.Providers.GoogleDrive.Enabled = true
	enabledProviders := config.GetEnabledProviders()
	assert.Contains(t, enabledProviders, "internal")
	assert.Contains(t, enabledProviders, "gdrive")
	assert.NotContains(t, enabledProviders, "sharepoint")
}

func TestLoadAutoDocConfigFromEnv(t *testing.T) {
	// Set environment variables
	envVars := map[string]string{
		"AUTODOC_ENABLED":                 "true",
		"AUTODOC_DEFAULT_PROVIDER":        "gdrive",
		"AUTODOC_MAX_RULES_PER_TENANT":    "50",
		"FEATURE_AUTODOC_RULES":           "true",
		"FEATURE_AUTODOC_PROVIDERS":       "true",
		"FEATURE_AUTODOC_FILES":           "false",
		"FEATURE_MULTI_PROVIDER":          "true",
		"AUTODOC_BATCH_SIZE":              "25",
		"AUTODOC_MAX_CONCURRENT_RULES":    "5",
		"GDRIVE_ENABLED":                  "true",
		"GDRIVE_SERVICE_ACCOUNT_KEY_PATH": "/tmp/test-key.json",
		"SHAREPOINT_ENABLED":              "false",
		"AUTODOC_METRICS_ENABLED":         "false",
		"AUTODOC_LOG_LEVEL":               "debug",
	}

	// Set environment variables
	for key, value := range envVars {
		os.Setenv(key, value)
	}

	// Clean up after test
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	config, err := LoadAutoDocConfig()
	require.NoError(t, err)

	// Verify configuration loaded from environment
	assert.True(t, config.Enabled)
	assert.Equal(t, "gdrive", config.DefaultProvider)
	assert.Equal(t, 50, config.MaxRulesPerTenant)
	assert.True(t, config.Features.Rules)
	assert.True(t, config.Features.Providers)
	assert.False(t, config.Features.Files)
	assert.True(t, config.Features.MultiProvider)
	assert.Equal(t, 25, config.Performance.BatchSize)
	assert.Equal(t, 5, config.Performance.MaxConcurrentRules)
	assert.True(t, config.Providers.GoogleDrive.Enabled)
	assert.Equal(t, "/tmp/test-key.json", config.Providers.GoogleDrive.ServiceAccountKeyPath)
	assert.False(t, config.Providers.SharePoint.Enabled)
	assert.False(t, config.Monitoring.MetricsEnabled)
	assert.Equal(t, "debug", config.Logging.Level)
}

func TestDefaultTenantConfig(t *testing.T) {
	tenantID := uint64(123)
	config := DefaultTenantConfig(tenantID)

	assert.Equal(t, tenantID, config.TenantID)
	assert.Equal(t, "internal", config.DefaultProvider)
	assert.Equal(t, 100, config.MaxRules)
	assert.True(t, config.Features["rules"])
	assert.True(t, config.Features["providers"])
	assert.True(t, config.Features["files"])
	assert.False(t, config.Features["multi_provider"])
	assert.True(t, config.Features["health_checks"])
	assert.True(t, config.Features["metrics"])

	// Check providers
	internalProvider, ok := config.Providers["internal"].(map[string]interface{})
	require.True(t, ok)
	assert.True(t, internalProvider["enabled"].(bool))

	gdriveProvider, ok := config.Providers["gdrive"].(map[string]interface{})
	require.True(t, ok)
	assert.False(t, gdriveProvider["enabled"].(bool))

	sharepointProvider, ok := config.Providers["sharepoint"].(map[string]interface{})
	require.True(t, ok)
	assert.False(t, sharepointProvider["enabled"].(bool))
}

func TestEffectiveConfig(t *testing.T) {
	// Create global config
	globalConfig := DefaultAutoDocConfig()
	globalConfig.Enabled = true
	globalConfig.DefaultProvider = "internal"
	globalConfig.Features.MultiProvider = true // Enable globally for tenant override to work

	// Create tenant config with overrides
	tenantConfig := DefaultTenantConfig(123)
	tenantConfig.DefaultProvider = "gdrive"
	tenantConfig.Features["multi_provider"] = true
	tenantConfig.Providers["gdrive"] = map[string]interface{}{
		"enabled": true,
	}

	// Create effective config
	effective := &EffectiveConfig{
		TenantID:     123,
		GlobalConfig: globalConfig,
		TenantConfig: tenantConfig,
	}

	// Test feature flags (tenant overrides global)
	assert.True(t, effective.IsFeatureEnabled("rules"))          // Both enabled
	assert.True(t, effective.IsFeatureEnabled("multi_provider")) // Tenant override

	// Test provider settings (global must be enabled first)
	assert.True(t, effective.IsProviderEnabled("internal")) // Global enabled

	// Enable gdrive globally for tenant override to work
	globalConfig.Providers.GoogleDrive.Enabled = true
	assert.True(t, effective.IsProviderEnabled("gdrive")) // Now tenant override works

	// Test default provider (tenant overrides global)
	assert.Equal(t, "gdrive", effective.GetDefaultProvider())

	// Test max rules (tenant within global limit)
	assert.Equal(t, 100, effective.GetMaxRules())

	// Test max rules with tenant limit higher than global
	tenantConfig.MaxRules = 200
	globalConfig.MaxRulesPerTenant = 150
	assert.Equal(t, 150, effective.GetMaxRules()) // Should use global limit
}

func TestConfigValidationWithRealFiles(t *testing.T) {
	// Create temporary files for testing
	tempDir := t.TempDir()

	// Test storage path validation
	config := DefaultAutoDocConfig()
	config.Enabled = true // Enable AutoDoc for validation
	config.Providers.Internal.StoragePath = tempDir

	validator := NewConfigValidator(config)
	result := validator.ValidateComplete()

	assert.True(t, result.Valid)
	assert.Empty(t, result.Errors)

	// Test with non-existent path that can be created
	nonExistentPath := tempDir + "/new_storage"
	config.Providers.Internal.StoragePath = nonExistentPath

	result = validator.ValidateComplete()
	assert.True(t, result.Valid) // Should be valid as directory can be created

	// Test with invalid path (permission denied)
	if os.Getuid() != 0 { // Skip if running as root
		invalidPath := "/root/invalid_storage"
		config.Providers.Internal.StoragePath = invalidPath

		result = validator.ValidateComplete()
		// Should have warnings about storage path
		assert.True(t, len(result.Warnings) > 0)
	}
}

func TestConfigPerformanceSettings(t *testing.T) {
	config := DefaultAutoDocConfig()

	// Test default performance settings
	assert.Equal(t, 30*time.Second, config.Performance.RuleExecutionTimeout)
	assert.Equal(t, 50, config.Performance.BatchSize)
	assert.Equal(t, 10, config.Performance.MaxConcurrentRules)
	assert.Equal(t, 10*time.Second, config.Performance.ProviderTimeout)
	assert.Equal(t, 3, config.Performance.RetryAttempts)
	assert.Equal(t, 1*time.Second, config.Performance.RetryDelay)

	// Test validation of performance settings using validator
	validator := NewConfigValidator(config)

	// Test invalid rule execution timeout
	config.Performance.RuleExecutionTimeout = 0
	result := validator.ValidateComplete()
	assert.False(t, result.Valid)
	assert.True(t, len(result.Errors) > 0)

	// Reset and test invalid batch size
	config = DefaultAutoDocConfig()
	validator = NewConfigValidator(config)
	config.Performance.BatchSize = 0
	result = validator.ValidateComplete()
	assert.False(t, result.Valid)
	assert.True(t, len(result.Errors) > 0)
}

func TestConfigMonitoringSettings(t *testing.T) {
	config := DefaultAutoDocConfig()

	// Test default monitoring settings
	assert.True(t, config.Monitoring.Enabled)
	assert.Equal(t, 60*time.Second, config.Monitoring.HealthCheckInterval)
	assert.Equal(t, 10*time.Second, config.Monitoring.ProviderHealthTimeout)
	assert.True(t, config.Monitoring.MetricsEnabled)
	assert.Equal(t, 9090, config.Monitoring.MetricsPort)
	assert.Equal(t, "/metrics", config.Monitoring.MetricsPath)
}

func TestConfigSecuritySettings(t *testing.T) {
	config := DefaultAutoDocConfig()

	// Test default security settings
	assert.True(t, config.Security.RateLimitEnabled)
	assert.Equal(t, 1000, config.Security.RateLimitRequestsPerHour)
	assert.Equal(t, 50, config.Security.RateLimitBurst)
	assert.True(t, config.Security.RequireAuthentication)
	assert.Equal(t, []string{"*"}, config.Security.AllowedOrigins)
}
