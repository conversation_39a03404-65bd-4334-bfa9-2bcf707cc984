package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// AutoDocConfig represents the complete AutoDoc configuration
type AutoDocConfig struct {
	// Core Settings
	Enabled           bool   `mapstructure:"enabled" json:"enabled"`
	DefaultProvider   string `mapstructure:"default_provider" json:"default_provider"`
	MaxRulesPerTenant int    `mapstructure:"max_rules_per_tenant" json:"max_rules_per_tenant"`

	// Feature Flags
	Features FeatureFlags `mapstructure:"features" json:"features"`

	// Performance Settings
	Performance PerformanceConfig `mapstructure:"performance" json:"performance"`

	// Provider Configuration
	Providers ProvidersConfig `mapstructure:"providers" json:"providers"`

	// Monitoring Settings
	Monitoring MonitoringConfig `mapstructure:"monitoring" json:"monitoring"`

	// Security Settings
	Security SecurityConfig `mapstructure:"security" json:"security"`

	// Logging Settings
	Logging LoggingConfig `mapstructure:"logging" json:"logging"`
}

// FeatureFlags controls gradual feature rollout
type FeatureFlags struct {
	Rules         bool `mapstructure:"rules" json:"rules"`
	Providers     bool `mapstructure:"providers" json:"providers"`
	Files         bool `mapstructure:"files" json:"files"`
	MultiProvider bool `mapstructure:"multi_provider" json:"multi_provider"`
	HealthChecks  bool `mapstructure:"health_checks" json:"health_checks"`
	Metrics       bool `mapstructure:"metrics" json:"metrics"`
}

// PerformanceConfig contains performance-related settings
type PerformanceConfig struct {
	RuleExecutionTimeout time.Duration `mapstructure:"rule_execution_timeout" json:"rule_execution_timeout"`
	BatchSize            int           `mapstructure:"batch_size" json:"batch_size"`
	MaxConcurrentRules   int           `mapstructure:"max_concurrent_rules" json:"max_concurrent_rules"`
	ProviderTimeout      time.Duration `mapstructure:"provider_timeout" json:"provider_timeout"`
	RetryAttempts        int           `mapstructure:"retry_attempts" json:"retry_attempts"`
	RetryDelay           time.Duration `mapstructure:"retry_delay" json:"retry_delay"`
}

// ProvidersConfig contains provider-specific configuration
type ProvidersConfig struct {
	Internal    InternalProviderConfig    `mapstructure:"internal" json:"internal"`
	GoogleDrive GoogleDriveProviderConfig `mapstructure:"gdrive" json:"gdrive"`
	SharePoint  SharePointProviderConfig  `mapstructure:"sharepoint" json:"sharepoint"`
}

// InternalProviderConfig for internal document provider
type InternalProviderConfig struct {
	Enabled     bool   `mapstructure:"enabled" json:"enabled"`
	StoragePath string `mapstructure:"storage_path" json:"storage_path"`
	MaxFileSize int64  `mapstructure:"max_file_size" json:"max_file_size"`
}

// GoogleDriveProviderConfig for Google Drive integration
type GoogleDriveProviderConfig struct {
	Enabled               bool          `mapstructure:"enabled" json:"enabled"`
	ServiceAccountKeyPath string        `mapstructure:"service_account_key_path" json:"service_account_key_path"`
	DomainWideDelegation  bool          `mapstructure:"domain_wide_delegation" json:"domain_wide_delegation"`
	MaxRetries            int           `mapstructure:"max_retries" json:"max_retries"`
	RetryDelay            time.Duration `mapstructure:"retry_delay" json:"retry_delay"`
	RequestTimeout        time.Duration `mapstructure:"request_timeout" json:"request_timeout"`
	RateLimitQPS          float64       `mapstructure:"rate_limit_qps" json:"rate_limit_qps"`
	RateLimitBurst        int           `mapstructure:"rate_limit_burst" json:"rate_limit_burst"`
}

// SharePointProviderConfig for SharePoint integration
type SharePointProviderConfig struct {
	Enabled        bool          `mapstructure:"enabled" json:"enabled"`
	ClientID       string        `mapstructure:"client_id" json:"client_id"`
	ClientSecret   string        `mapstructure:"client_secret" json:"client_secret"`
	TenantID       string        `mapstructure:"tenant_id" json:"tenant_id"`
	MaxRetries     int           `mapstructure:"max_retries" json:"max_retries"`
	RetryDelay     time.Duration `mapstructure:"retry_delay" json:"retry_delay"`
	RequestTimeout time.Duration `mapstructure:"request_timeout" json:"request_timeout"`
}

// MonitoringConfig contains monitoring and health check settings
type MonitoringConfig struct {
	Enabled               bool          `mapstructure:"enabled" json:"enabled"`
	HealthCheckInterval   time.Duration `mapstructure:"health_check_interval" json:"health_check_interval"`
	ProviderHealthTimeout time.Duration `mapstructure:"provider_health_timeout" json:"provider_health_timeout"`
	MetricsEnabled        bool          `mapstructure:"metrics_enabled" json:"metrics_enabled"`
	MetricsPort           int           `mapstructure:"metrics_port" json:"metrics_port"`
	MetricsPath           string        `mapstructure:"metrics_path" json:"metrics_path"`
}

// SecurityConfig contains security-related settings
type SecurityConfig struct {
	RateLimitEnabled         bool     `mapstructure:"rate_limit_enabled" json:"rate_limit_enabled"`
	RateLimitRequestsPerHour int      `mapstructure:"rate_limit_requests_per_hour" json:"rate_limit_requests_per_hour"`
	RateLimitBurst           int      `mapstructure:"rate_limit_burst" json:"rate_limit_burst"`
	RequireAuthentication    bool     `mapstructure:"require_authentication" json:"require_authentication"`
	AllowedOrigins           []string `mapstructure:"allowed_origins" json:"allowed_origins"`
}

// LoggingConfig contains logging settings
type LoggingConfig struct {
	Level      string `mapstructure:"level" json:"level"`
	Format     string `mapstructure:"format" json:"format"`
	EnableFile bool   `mapstructure:"enable_file" json:"enable_file"`
	FilePath   string `mapstructure:"file_path" json:"file_path"`
}

// DefaultAutoDocConfig returns the default configuration
func DefaultAutoDocConfig() *AutoDocConfig {
	return &AutoDocConfig{
		Enabled:           false, // Disabled by default for safety
		DefaultProvider:   "internal",
		MaxRulesPerTenant: 100,

		Features: FeatureFlags{
			Rules:         true,
			Providers:     true,
			Files:         true,
			MultiProvider: false, // Disabled by default
			HealthChecks:  true,
			Metrics:       true,
		},

		Performance: PerformanceConfig{
			RuleExecutionTimeout: 30 * time.Second,
			BatchSize:            50,
			MaxConcurrentRules:   10,
			ProviderTimeout:      10 * time.Second,
			RetryAttempts:        3,
			RetryDelay:           1 * time.Second,
		},

		Providers: ProvidersConfig{
			Internal: InternalProviderConfig{
				Enabled:     true,
				StoragePath: "/var/lib/docman/storage",
				MaxFileSize: 100 * 1024 * 1024, // 100MB
			},
			GoogleDrive: GoogleDriveProviderConfig{
				Enabled:              false,
				DomainWideDelegation: true,
				MaxRetries:           3,
				RetryDelay:           1 * time.Second,
				RequestTimeout:       30 * time.Second,
				RateLimitQPS:         10.0,
				RateLimitBurst:       20,
			},
			SharePoint: SharePointProviderConfig{
				Enabled:        false,
				MaxRetries:     3,
				RetryDelay:     1 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
		},

		Monitoring: MonitoringConfig{
			Enabled:               true,
			HealthCheckInterval:   60 * time.Second,
			ProviderHealthTimeout: 10 * time.Second,
			MetricsEnabled:        true,
			MetricsPort:           9090,
			MetricsPath:           "/metrics",
		},

		Security: SecurityConfig{
			RateLimitEnabled:         true,
			RateLimitRequestsPerHour: 1000,
			RateLimitBurst:           50,
			RequireAuthentication:    true,
			AllowedOrigins:           []string{"*"},
		},

		Logging: LoggingConfig{
			Level:      "info",
			Format:     "json",
			EnableFile: false,
			FilePath:   "/var/log/docman/autodoc.log",
		},
	}
}

// LoadAutoDocConfig loads AutoDoc configuration from environment variables and config files
func LoadAutoDocConfig() (*AutoDocConfig, error) {
	// Create a new viper instance for AutoDoc config
	v := viper.New()

	// Set default configuration
	config := DefaultAutoDocConfig()

	// Configure viper
	v.SetConfigName("autodoc")
	v.SetConfigType("yaml")
	v.AddConfigPath("./config")
	v.AddConfigPath("./")
	v.AddConfigPath("/etc/docman/")

	// Environment variable configuration
	v.SetEnvPrefix("AUTODOC")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// Bind environment variables with proper mapping
	bindEnvironmentVariables(v)

	// Try to read config file (optional)
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
		// Config file not found is OK, we'll use env vars and defaults
	}

	// Unmarshal configuration
	if err := v.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// bindEnvironmentVariables binds environment variables to viper keys
func bindEnvironmentVariables(v *viper.Viper) {
	// Core settings
	v.BindEnv("enabled", "AUTODOC_ENABLED")
	v.BindEnv("default_provider", "AUTODOC_DEFAULT_PROVIDER")
	v.BindEnv("max_rules_per_tenant", "AUTODOC_MAX_RULES_PER_TENANT")

	// Feature flags
	v.BindEnv("features.rules", "FEATURE_AUTODOC_RULES")
	v.BindEnv("features.providers", "FEATURE_AUTODOC_PROVIDERS")
	v.BindEnv("features.files", "FEATURE_AUTODOC_FILES")
	v.BindEnv("features.multi_provider", "FEATURE_MULTI_PROVIDER")
	v.BindEnv("features.health_checks", "FEATURE_HEALTH_CHECKS")
	v.BindEnv("features.metrics", "FEATURE_METRICS")

	// Performance settings
	v.BindEnv("performance.rule_execution_timeout", "AUTODOC_RULE_EXECUTION_TIMEOUT")
	v.BindEnv("performance.batch_size", "AUTODOC_BATCH_SIZE")
	v.BindEnv("performance.max_concurrent_rules", "AUTODOC_MAX_CONCURRENT_RULES")
	v.BindEnv("performance.provider_timeout", "AUTODOC_PROVIDER_TIMEOUT")
	v.BindEnv("performance.retry_attempts", "AUTODOC_RETRY_ATTEMPTS")
	v.BindEnv("performance.retry_delay", "AUTODOC_RETRY_DELAY")

	// Provider settings
	v.BindEnv("providers.internal.enabled", "INTERNAL_ENABLED")
	v.BindEnv("providers.internal.storage_path", "INTERNAL_STORAGE_PATH")
	v.BindEnv("providers.internal.max_file_size", "INTERNAL_MAX_FILE_SIZE")

	v.BindEnv("providers.gdrive.enabled", "GDRIVE_ENABLED")
	v.BindEnv("providers.gdrive.service_account_key_path", "GDRIVE_SERVICE_ACCOUNT_KEY_PATH")
	v.BindEnv("providers.gdrive.domain_wide_delegation", "GDRIVE_DOMAIN_WIDE_DELEGATION")
	v.BindEnv("providers.gdrive.max_retries", "GDRIVE_MAX_RETRIES")
	v.BindEnv("providers.gdrive.retry_delay", "GDRIVE_RETRY_DELAY")
	v.BindEnv("providers.gdrive.request_timeout", "GDRIVE_REQUEST_TIMEOUT")
	v.BindEnv("providers.gdrive.rate_limit_qps", "GDRIVE_RATE_LIMIT_QPS")
	v.BindEnv("providers.gdrive.rate_limit_burst", "GDRIVE_RATE_LIMIT_BURST")

	v.BindEnv("providers.sharepoint.enabled", "SHAREPOINT_ENABLED")
	v.BindEnv("providers.sharepoint.client_id", "SHAREPOINT_CLIENT_ID")
	v.BindEnv("providers.sharepoint.client_secret", "SHAREPOINT_CLIENT_SECRET")
	v.BindEnv("providers.sharepoint.tenant_id", "SHAREPOINT_TENANT_ID")
	v.BindEnv("providers.sharepoint.max_retries", "SHAREPOINT_MAX_RETRIES")
	v.BindEnv("providers.sharepoint.retry_delay", "SHAREPOINT_RETRY_DELAY")
	v.BindEnv("providers.sharepoint.request_timeout", "SHAREPOINT_REQUEST_TIMEOUT")

	// Monitoring settings
	v.BindEnv("monitoring.enabled", "AUTODOC_MONITORING_ENABLED")
	v.BindEnv("monitoring.health_check_interval", "AUTODOC_HEALTH_CHECK_INTERVAL")
	v.BindEnv("monitoring.provider_health_timeout", "AUTODOC_PROVIDER_HEALTH_TIMEOUT")
	v.BindEnv("monitoring.metrics_enabled", "AUTODOC_METRICS_ENABLED")
	v.BindEnv("monitoring.metrics_port", "AUTODOC_METRICS_PORT")
	v.BindEnv("monitoring.metrics_path", "AUTODOC_METRICS_PATH")

	// Security settings
	v.BindEnv("security.rate_limit_enabled", "AUTODOC_RATE_LIMIT_ENABLED")
	v.BindEnv("security.rate_limit_requests_per_hour", "AUTODOC_RATE_LIMIT_REQUESTS_PER_HOUR")
	v.BindEnv("security.rate_limit_burst", "AUTODOC_RATE_LIMIT_BURST")
	v.BindEnv("security.require_authentication", "AUTODOC_REQUIRE_AUTHENTICATION")
	v.BindEnv("security.allowed_origins", "AUTODOC_ALLOWED_ORIGINS")

	// Logging settings
	v.BindEnv("logging.level", "AUTODOC_LOG_LEVEL")
	v.BindEnv("logging.format", "AUTODOC_LOG_FORMAT")
	v.BindEnv("logging.enable_file", "AUTODOC_LOG_ENABLE_FILE")
	v.BindEnv("logging.file_path", "AUTODOC_LOG_FILE_PATH")
}

// Validate validates the configuration
func (c *AutoDocConfig) Validate() error {
	// Validate default provider
	validProviders := []string{"internal", "gdrive", "sharepoint"}
	if !contains(validProviders, c.DefaultProvider) {
		return fmt.Errorf("invalid default provider: %s, must be one of %v", c.DefaultProvider, validProviders)
	}

	// Validate max rules per tenant
	if c.MaxRulesPerTenant <= 0 || c.MaxRulesPerTenant > 1000 {
		return fmt.Errorf("max_rules_per_tenant must be between 1 and 1000, got %d", c.MaxRulesPerTenant)
	}

	// Validate performance settings
	if c.Performance.BatchSize <= 0 || c.Performance.BatchSize > 1000 {
		return fmt.Errorf("batch_size must be between 1 and 1000, got %d", c.Performance.BatchSize)
	}

	if c.Performance.MaxConcurrentRules <= 0 || c.Performance.MaxConcurrentRules > 100 {
		return fmt.Errorf("max_concurrent_rules must be between 1 and 100, got %d", c.Performance.MaxConcurrentRules)
	}

	// Validate provider configurations
	if err := c.validateProviders(); err != nil {
		return fmt.Errorf("provider validation failed: %w", err)
	}

	// Validate logging level
	validLogLevels := []string{"debug", "info", "warn", "error"}
	if !contains(validLogLevels, c.Logging.Level) {
		return fmt.Errorf("invalid log level: %s, must be one of %v", c.Logging.Level, validLogLevels)
	}

	return nil
}

// validateProviders validates provider-specific configurations
func (c *AutoDocConfig) validateProviders() error {
	// At least one provider must be enabled
	if !c.Providers.Internal.Enabled && !c.Providers.GoogleDrive.Enabled && !c.Providers.SharePoint.Enabled {
		return fmt.Errorf("at least one provider must be enabled")
	}

	// Validate Google Drive configuration if enabled
	if c.Providers.GoogleDrive.Enabled {
		if c.Providers.GoogleDrive.ServiceAccountKeyPath == "" {
			return fmt.Errorf("gdrive service_account_key_path is required when Google Drive is enabled")
		}
	}

	// Validate SharePoint configuration if enabled
	if c.Providers.SharePoint.Enabled {
		if c.Providers.SharePoint.ClientID == "" || c.Providers.SharePoint.ClientSecret == "" || c.Providers.SharePoint.TenantID == "" {
			return fmt.Errorf("sharepoint client_id, client_secret, and tenant_id are required when SharePoint is enabled")
		}
	}

	return nil
}

// IsFeatureEnabled checks if a specific feature is enabled
func (c *AutoDocConfig) IsFeatureEnabled(feature string) bool {
	if !c.Enabled {
		return false
	}

	switch feature {
	case "rules":
		return c.Features.Rules
	case "providers":
		return c.Features.Providers
	case "files":
		return c.Features.Files
	case "multi_provider":
		return c.Features.MultiProvider
	case "health_checks":
		return c.Features.HealthChecks
	case "metrics":
		return c.Features.Metrics
	default:
		return false
	}
}

// IsProviderEnabled checks if a specific provider is enabled
func (c *AutoDocConfig) IsProviderEnabled(provider string) bool {
	if !c.Enabled {
		return false
	}

	switch provider {
	case "internal":
		return c.Providers.Internal.Enabled
	case "gdrive":
		return c.Providers.GoogleDrive.Enabled
	case "sharepoint":
		return c.Providers.SharePoint.Enabled
	default:
		return false
	}
}

// GetEnabledProviders returns a list of enabled providers
func (c *AutoDocConfig) GetEnabledProviders() []string {
	var providers []string

	if c.IsProviderEnabled("internal") {
		providers = append(providers, "internal")
	}
	if c.IsProviderEnabled("gdrive") {
		providers = append(providers, "gdrive")
	}
	if c.IsProviderEnabled("sharepoint") {
		providers = append(providers, "sharepoint")
	}

	return providers
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// TenantConfig represents tenant-specific AutoDoc configuration
type TenantConfig struct {
	TenantID        uint64                 `json:"tenant_id"`
	DefaultProvider string                 `json:"default_provider"`
	MaxRules        int                    `json:"max_rules"`
	Features        map[string]bool        `json:"features"`
	Providers       map[string]interface{} `json:"providers"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// TenantConfigManager manages tenant-specific configurations
type TenantConfigManager interface {
	GetTenantConfig(tenantID uint64) (*TenantConfig, error)
	SetTenantConfig(tenantID uint64, config *TenantConfig) error
	DeleteTenantConfig(tenantID uint64) error
	ListTenantConfigs() ([]*TenantConfig, error)
}

// DefaultTenantConfig returns default tenant configuration
func DefaultTenantConfig(tenantID uint64) *TenantConfig {
	return &TenantConfig{
		TenantID:        tenantID,
		DefaultProvider: "internal",
		MaxRules:        100,
		Features: map[string]bool{
			"rules":          true,
			"providers":      true,
			"files":          true,
			"multi_provider": false,
			"health_checks":  true,
			"metrics":        true,
		},
		Providers: map[string]interface{}{
			"internal": map[string]interface{}{
				"enabled": true,
			},
			"gdrive": map[string]interface{}{
				"enabled": false,
			},
			"sharepoint": map[string]interface{}{
				"enabled": false,
			},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}
