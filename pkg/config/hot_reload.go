package config

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"gitlab.com/goxp/cloud0/logger"
)

// ConfigWatcher provides hot-reload functionality for configuration
type ConfigWatcher struct {
	mu              sync.RWMutex
	config          *AutoDocConfig
	configService   *ConfigService
	viper           *viper.Viper
	watcher         *fsnotify.Watcher
	reloadCallbacks []ReloadCallback
	stopCh          chan struct{}
	running         bool
}

// ReloadCallback is called when configuration is reloaded
type ReloadCallback func(oldConfig, newConfig *AutoDocConfig) error

// NewConfigWatcher creates a new configuration watcher
func NewConfigWatcher(config *AutoDocConfig, configService *ConfigService) (*ConfigWatcher, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("failed to create file watcher: %w", err)
	}

	return &ConfigWatcher{
		config:        config,
		configService: configService,
		watcher:       watcher,
		stopCh:        make(chan struct{}),
		running:       false,
	}, nil
}

// AddReloadCallback adds a callback to be called when configuration is reloaded
func (w *ConfigWatcher) AddReloadCallback(callback ReloadCallback) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.reloadCallbacks = append(w.reloadCallbacks, callback)
}

// Start starts the configuration watcher
func (w *ConfigWatcher) Start(ctx context.Context) error {
	w.mu.Lock()
	if w.running {
		w.mu.Unlock()
		return fmt.Errorf("config watcher is already running")
	}
	w.running = true
	w.mu.Unlock()

	// Watch configuration files
	configPaths := []string{
		"./config/autodoc.yaml",
		"./config/autodoc.yml",
		"./autodoc.yaml",
		"./autodoc.yml",
		"/etc/docman/autodoc.yaml",
		"/etc/docman/autodoc.yml",
	}

	for _, path := range configPaths {
		if err := w.watcher.Add(path); err != nil {
			// It's OK if file doesn't exist, just log it
			logger.Tag("config-watcher").WithField("path", path).Debug("config file not found, skipping watch")
		}
	}

	// Start watching in a goroutine
	go w.watchLoop(ctx)

	logger.Tag("config-watcher").Info("configuration watcher started")
	return nil
}

// Stop stops the configuration watcher
func (w *ConfigWatcher) Stop() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.running {
		return nil
	}

	close(w.stopCh)
	w.running = false

	if err := w.watcher.Close(); err != nil {
		return fmt.Errorf("failed to close file watcher: %w", err)
	}

	logger.Tag("config-watcher").Info("configuration watcher stopped")
	return nil
}

// watchLoop is the main watching loop
func (w *ConfigWatcher) watchLoop(ctx context.Context) {
	// Debounce timer to avoid multiple reloads for rapid file changes
	var debounceTimer *time.Timer
	const debounceDelay = 1 * time.Second

	for {
		select {
		case <-ctx.Done():
			logger.Tag("config-watcher").Info("context cancelled, stopping config watcher")
			return

		case <-w.stopCh:
			logger.Tag("config-watcher").Info("stop signal received, stopping config watcher")
			return

		case event, ok := <-w.watcher.Events:
			if !ok {
				logger.Tag("config-watcher").Error("watcher events channel closed")
				return
			}

			// Only handle write and create events
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				logger.Tag("config-watcher").WithField("file", event.Name).Info("config file changed")

				// Cancel previous timer if exists
				if debounceTimer != nil {
					debounceTimer.Stop()
				}

				// Set new debounce timer
				debounceTimer = time.AfterFunc(debounceDelay, func() {
					if err := w.reloadConfig(); err != nil {
						logger.Tag("config-watcher").WithError(err).Error("failed to reload configuration")
					}
				})
			}

		case err, ok := <-w.watcher.Errors:
			if !ok {
				logger.Tag("config-watcher").Error("watcher errors channel closed")
				return
			}
			logger.Tag("config-watcher").WithError(err).Error("file watcher error")
		}
	}
}

// reloadConfig reloads the configuration from files
func (w *ConfigWatcher) reloadConfig() error {
	logger.Tag("config-watcher").Info("reloading configuration")

	// Load new configuration
	newConfig, err := LoadAutoDocConfig()
	if err != nil {
		return fmt.Errorf("failed to load new configuration: %w", err)
	}

	// Get old configuration for comparison
	w.mu.RLock()
	oldConfig := w.config
	w.mu.RUnlock()

	// Validate new configuration
	if err := newConfig.Validate(); err != nil {
		return fmt.Errorf("new configuration validation failed: %w", err)
	}

	// Check if configuration actually changed
	if !w.hasConfigChanged(oldConfig, newConfig) {
		logger.Tag("config-watcher").Debug("configuration unchanged, skipping reload")
		return nil
	}

	// Call reload callbacks
	for _, callback := range w.reloadCallbacks {
		if err := callback(oldConfig, newConfig); err != nil {
			logger.Tag("config-watcher").WithError(err).Error("reload callback failed")
			// Continue with other callbacks even if one fails
		}
	}

	// Update configuration
	w.mu.Lock()
	w.config = newConfig
	w.mu.Unlock()

	logger.Tag("config-watcher").Info("configuration reloaded successfully")
	return nil
}

// hasConfigChanged checks if configuration has actually changed
func (w *ConfigWatcher) hasConfigChanged(oldConfig, newConfig *AutoDocConfig) bool {
	// Compare key configuration values that support hot-reload

	// Feature flags can be hot-reloaded
	if oldConfig.Features != newConfig.Features {
		return true
	}

	// Monitoring settings can be hot-reloaded
	if oldConfig.Monitoring.Enabled != newConfig.Monitoring.Enabled ||
		oldConfig.Monitoring.HealthCheckInterval != newConfig.Monitoring.HealthCheckInterval ||
		oldConfig.Monitoring.MetricsEnabled != newConfig.Monitoring.MetricsEnabled {
		return true
	}

	// Security settings can be hot-reloaded
	if oldConfig.Security.RateLimitEnabled != newConfig.Security.RateLimitEnabled ||
		oldConfig.Security.RateLimitRequestsPerHour != newConfig.Security.RateLimitRequestsPerHour ||
		oldConfig.Security.RateLimitBurst != newConfig.Security.RateLimitBurst {
		return true
	}

	// Logging settings can be hot-reloaded
	if oldConfig.Logging.Level != newConfig.Logging.Level ||
		oldConfig.Logging.Format != newConfig.Logging.Format {
		return true
	}

	// Performance settings that can be hot-reloaded
	if oldConfig.Performance.BatchSize != newConfig.Performance.BatchSize ||
		oldConfig.Performance.MaxConcurrentRules != newConfig.Performance.MaxConcurrentRules {
		return true
	}

	return false
}

// GetCurrentConfig returns the current configuration (thread-safe)
func (w *ConfigWatcher) GetCurrentConfig() *AutoDocConfig {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.config
}

// ReloadableConfigManager manages hot-reloadable configuration
type ReloadableConfigManager struct {
	watcher       *ConfigWatcher
	configService *ConfigService
	mu            sync.RWMutex
}

// NewReloadableConfigManager creates a new reloadable configuration manager
func NewReloadableConfigManager(config *AutoDocConfig, configService *ConfigService) (*ReloadableConfigManager, error) {
	watcher, err := NewConfigWatcher(config, configService)
	if err != nil {
		return nil, fmt.Errorf("failed to create config watcher: %w", err)
	}

	manager := &ReloadableConfigManager{
		watcher:       watcher,
		configService: configService,
	}

	// Add default reload callbacks
	watcher.AddReloadCallback(manager.onConfigReload)

	return manager, nil
}

// Start starts the configuration manager
func (m *ReloadableConfigManager) Start(ctx context.Context) error {
	return m.watcher.Start(ctx)
}

// Stop stops the configuration manager
func (m *ReloadableConfigManager) Stop() error {
	return m.watcher.Stop()
}

// GetConfig returns the current configuration
func (m *ReloadableConfigManager) GetConfig() *AutoDocConfig {
	return m.watcher.GetCurrentConfig()
}

// GetEffectiveConfig returns the effective configuration for a tenant
func (m *ReloadableConfigManager) GetEffectiveConfig(tenantID uint64) (*EffectiveConfig, error) {
	return m.configService.GetEffectiveConfig(tenantID)
}

// AddReloadCallback adds a callback for configuration reloads
func (m *ReloadableConfigManager) AddReloadCallback(callback ReloadCallback) {
	m.watcher.AddReloadCallback(callback)
}

// onConfigReload is called when configuration is reloaded
func (m *ReloadableConfigManager) onConfigReload(oldConfig, newConfig *AutoDocConfig) error {
	logger.Tag("config-manager").Info("processing configuration reload")

	// Log configuration changes
	m.logConfigChanges(oldConfig, newConfig)

	// Update config service with new global configuration
	m.configService.globalConfig = newConfig

	return nil
}

// logConfigChanges logs the differences between old and new configuration
func (m *ReloadableConfigManager) logConfigChanges(oldConfig, newConfig *AutoDocConfig) {
	log := logger.Tag("config-manager")

	// Log feature flag changes
	if oldConfig.Features.Rules != newConfig.Features.Rules {
		log.WithFields(map[string]interface{}{
			"feature": "rules",
			"old":     oldConfig.Features.Rules,
			"new":     newConfig.Features.Rules,
		}).Info("feature flag changed")
	}

	if oldConfig.Features.Providers != newConfig.Features.Providers {
		log.WithFields(map[string]interface{}{
			"feature": "providers",
			"old":     oldConfig.Features.Providers,
			"new":     newConfig.Features.Providers,
		}).Info("feature flag changed")
	}

	if oldConfig.Features.Files != newConfig.Features.Files {
		log.WithFields(map[string]interface{}{
			"feature": "files",
			"old":     oldConfig.Features.Files,
			"new":     newConfig.Features.Files,
		}).Info("feature flag changed")
	}

	if oldConfig.Features.MultiProvider != newConfig.Features.MultiProvider {
		log.WithFields(map[string]interface{}{
			"feature": "multi_provider",
			"old":     oldConfig.Features.MultiProvider,
			"new":     newConfig.Features.MultiProvider,
		}).Info("feature flag changed")
	}

	// Log monitoring changes
	if oldConfig.Monitoring.Enabled != newConfig.Monitoring.Enabled {
		log.WithFields(map[string]interface{}{
			"setting": "monitoring.enabled",
			"old":     oldConfig.Monitoring.Enabled,
			"new":     newConfig.Monitoring.Enabled,
		}).Info("monitoring setting changed")
	}

	// Log security changes
	if oldConfig.Security.RateLimitEnabled != newConfig.Security.RateLimitEnabled {
		log.WithFields(map[string]interface{}{
			"setting": "security.rate_limit_enabled",
			"old":     oldConfig.Security.RateLimitEnabled,
			"new":     newConfig.Security.RateLimitEnabled,
		}).Info("security setting changed")
	}

	// Log logging changes
	if oldConfig.Logging.Level != newConfig.Logging.Level {
		log.WithFields(map[string]interface{}{
			"setting": "logging.level",
			"old":     oldConfig.Logging.Level,
			"new":     newConfig.Logging.Level,
		}).Info("logging setting changed")
	}
}
