package config

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"bilabl/docman/domain/model"
	"bilabl/docman/pkg/repositories"

	"gorm.io/gorm"
)

// DBTenantConfigManager implements TenantConfigManager using database storage
type DBTenantConfigManager struct {
	settingRepo repositories.DocumentSettingRepository
}

// NewDBTenantConfigManager creates a new database-backed tenant config manager
func NewDBTenantConfigManager(settingRepo repositories.DocumentSettingRepository) TenantConfigManager {
	return &DBTenantConfigManager{
		settingRepo: settingRepo,
	}
}

// GetTenantConfig retrieves tenant-specific configuration from database
func (m *DBTenantConfigManager) GetTenantConfig(tenantID uint64) (*TenantConfig, error) {
	// Try to get existing configuration
	setting, err := m.settingRepo.GetValueByKey(context.Background(), tenantID, "autodoc_config")
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return default configuration if not found
			return DefaultTenantConfig(tenantID), nil
		}
		return nil, fmt.Errorf("failed to get tenant config: %w", err)
	}

	// Parse configuration from JSON
	var config TenantConfig
	if err := json.Unmarshal([]byte(setting.Value), &config); err != nil {
		return nil, fmt.Errorf("failed to parse tenant config: %w", err)
	}

	// Ensure tenant ID matches
	config.TenantID = tenantID

	return &config, nil
}

// SetTenantConfig stores tenant-specific configuration in database
func (m *DBTenantConfigManager) SetTenantConfig(tenantID uint64, config *TenantConfig) error {
	// Ensure tenant ID is set correctly
	config.TenantID = tenantID
	config.UpdatedAt = time.Now()

	// Validate configuration
	if err := m.validateTenantConfig(config); err != nil {
		return fmt.Errorf("tenant config validation failed: %w", err)
	}

	// Serialize configuration to JSON
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to serialize tenant config: %w", err)
	}

	// Check if setting already exists
	existingSetting, err := m.settingRepo.GetValueByKey(context.Background(), tenantID, "autodoc_config")
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing config: %w", err)
	}

	if err == gorm.ErrRecordNotFound {
		// Create new setting
		setting := &model.DocumentSetting{
			TenantID: tenantID,
			Key:      "autodoc_config",
			Value:    string(configJSON),
		}
		if err := m.settingRepo.Create(context.Background(), setting); err != nil {
			return fmt.Errorf("failed to create tenant config: %w", err)
		}
	} else {
		// Update existing setting using CreateOrUpdate for upsert behavior
		existingSetting.Value = string(configJSON)
		query := &model.Query{
			Filters: []*model.Filter{
				{Key: "tenant_id", Value: tenantID, Method: "="},
				{Key: "key", Value: "autodoc_config", Method: "="},
			},
		}
		if err := m.settingRepo.CreateOrUpdate(context.Background(), query, existingSetting); err != nil {
			return fmt.Errorf("failed to update tenant config: %w", err)
		}
	}

	return nil
}

// DeleteTenantConfig removes tenant-specific configuration
func (m *DBTenantConfigManager) DeleteTenantConfig(tenantID uint64) error {
	setting, err := m.settingRepo.GetValueByKey(context.Background(), tenantID, "autodoc_config")
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil // Already deleted
		}
		return fmt.Errorf("failed to get tenant config for deletion: %w", err)
	}

	// Use UpdateOne to soft delete by setting a deleted_at timestamp
	query := &model.Query{
		Filters: []*model.Filter{
			{Key: "id", Value: setting.ID, Method: "="},
		},
	}

	// Set deleted_at to current time for soft delete
	now := time.Now()
	setting.DeletedAt.Time = now
	setting.DeletedAt.Valid = true

	if err := m.settingRepo.UpdateOne(context.Background(), query, setting); err != nil {
		return fmt.Errorf("failed to delete tenant config: %w", err)
	}

	return nil
}

// ListTenantConfigs retrieves all tenant configurations
// Note: This is a simplified implementation that returns empty list
// In a real implementation, you would need to add a method to DocumentSettingRepository
// to list all settings by key across all tenants
func (m *DBTenantConfigManager) ListTenantConfigs() ([]*TenantConfig, error) {
	// For now, return empty list since the repository interface doesn't support
	// listing all settings by key across tenants
	// This would require adding a new method to DocumentSettingRepository interface

	// TODO: Add FindByKey(ctx context.Context, key string) ([]*model.DocumentSetting, error)
	// to DocumentSettingRepository interface and implement it

	return []*TenantConfig{}, nil
}

// validateTenantConfig validates tenant-specific configuration
func (m *DBTenantConfigManager) validateTenantConfig(config *TenantConfig) error {
	if config.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	// Validate default provider
	validProviders := []string{"internal", "gdrive", "sharepoint"}
	if !contains(validProviders, config.DefaultProvider) {
		return fmt.Errorf("invalid default provider: %s, must be one of %v", config.DefaultProvider, validProviders)
	}

	// Validate max rules
	if config.MaxRules <= 0 || config.MaxRules > 1000 {
		return fmt.Errorf("max_rules must be between 1 and 1000, got %d", config.MaxRules)
	}

	// Validate features
	validFeatures := []string{"rules", "providers", "files", "multi_provider", "health_checks", "metrics"}
	for feature := range config.Features {
		if !contains(validFeatures, feature) {
			return fmt.Errorf("invalid feature: %s", feature)
		}
	}

	// Validate providers
	validProviderNames := []string{"internal", "gdrive", "sharepoint"}
	for provider := range config.Providers {
		if !contains(validProviderNames, provider) {
			return fmt.Errorf("invalid provider: %s", provider)
		}
	}

	return nil
}

// ConfigService provides high-level configuration management
type ConfigService struct {
	globalConfig  *AutoDocConfig
	tenantManager TenantConfigManager
}

// NewConfigService creates a new configuration service
func NewConfigService(globalConfig *AutoDocConfig, tenantManager TenantConfigManager) *ConfigService {
	return &ConfigService{
		globalConfig:  globalConfig,
		tenantManager: tenantManager,
	}
}

// GetEffectiveConfig returns the effective configuration for a tenant
// This merges global configuration with tenant-specific overrides
func (s *ConfigService) GetEffectiveConfig(tenantID uint64) (*EffectiveConfig, error) {
	// Get tenant-specific configuration
	tenantConfig, err := s.tenantManager.GetTenantConfig(tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant config: %w", err)
	}

	// Create effective configuration by merging global and tenant configs
	effective := &EffectiveConfig{
		TenantID:     tenantID,
		GlobalConfig: s.globalConfig,
		TenantConfig: tenantConfig,
	}

	return effective, nil
}

// UpdateTenantConfig updates tenant-specific configuration
func (s *ConfigService) UpdateTenantConfig(tenantID uint64, updates map[string]interface{}) error {
	// Get current tenant configuration
	config, err := s.tenantManager.GetTenantConfig(tenantID)
	if err != nil {
		return fmt.Errorf("failed to get current tenant config: %w", err)
	}

	// Apply updates
	if defaultProvider, ok := updates["default_provider"].(string); ok {
		config.DefaultProvider = defaultProvider
	}

	if maxRules, ok := updates["max_rules"].(int); ok {
		config.MaxRules = maxRules
	}

	if features, ok := updates["features"].(map[string]bool); ok {
		for feature, enabled := range features {
			config.Features[feature] = enabled
		}
	}

	if providers, ok := updates["providers"].(map[string]interface{}); ok {
		for provider, settings := range providers {
			config.Providers[provider] = settings
		}
	}

	// Save updated configuration
	return s.tenantManager.SetTenantConfig(tenantID, config)
}

// IsFeatureEnabledForTenant checks if a feature is enabled for a specific tenant
func (s *ConfigService) IsFeatureEnabledForTenant(tenantID uint64, feature string) (bool, error) {
	effective, err := s.GetEffectiveConfig(tenantID)
	if err != nil {
		return false, err
	}

	return effective.IsFeatureEnabled(feature), nil
}

// IsProviderEnabledForTenant checks if a provider is enabled for a specific tenant
func (s *ConfigService) IsProviderEnabledForTenant(tenantID uint64, provider string) (bool, error) {
	effective, err := s.GetEffectiveConfig(tenantID)
	if err != nil {
		return false, err
	}

	return effective.IsProviderEnabled(provider), nil
}

// GetDefaultProviderForTenant returns the default provider for a tenant
func (s *ConfigService) GetDefaultProviderForTenant(tenantID uint64) (string, error) {
	effective, err := s.GetEffectiveConfig(tenantID)
	if err != nil {
		return "", err
	}

	return effective.GetDefaultProvider(), nil
}

// EffectiveConfig represents the effective configuration for a tenant
type EffectiveConfig struct {
	TenantID     uint64         `json:"tenant_id"`
	GlobalConfig *AutoDocConfig `json:"global_config"`
	TenantConfig *TenantConfig  `json:"tenant_config"`
}

// IsFeatureEnabled checks if a feature is enabled (global AND tenant)
func (e *EffectiveConfig) IsFeatureEnabled(feature string) bool {
	// Check global configuration first
	if !e.GlobalConfig.IsFeatureEnabled(feature) {
		return false
	}

	// Check tenant-specific configuration
	if enabled, exists := e.TenantConfig.Features[feature]; exists {
		return enabled
	}

	// Default to global configuration
	return e.GlobalConfig.IsFeatureEnabled(feature)
}

// IsProviderEnabled checks if a provider is enabled (global AND tenant)
func (e *EffectiveConfig) IsProviderEnabled(provider string) bool {
	// Check global configuration first
	if !e.GlobalConfig.IsProviderEnabled(provider) {
		return false
	}

	// Check tenant-specific configuration
	if providerConfig, exists := e.TenantConfig.Providers[provider]; exists {
		if config, ok := providerConfig.(map[string]interface{}); ok {
			if enabled, ok := config["enabled"].(bool); ok {
				return enabled
			}
		}
	}

	// Default to global configuration
	return e.GlobalConfig.IsProviderEnabled(provider)
}

// GetDefaultProvider returns the effective default provider for the tenant
func (e *EffectiveConfig) GetDefaultProvider() string {
	// Use tenant-specific default provider if set
	if e.TenantConfig.DefaultProvider != "" {
		return e.TenantConfig.DefaultProvider
	}

	// Fall back to global default provider
	return e.GlobalConfig.DefaultProvider
}

// GetMaxRules returns the effective max rules limit for the tenant
func (e *EffectiveConfig) GetMaxRules() int {
	// Use tenant-specific limit if set and within global limit
	if e.TenantConfig.MaxRules > 0 && e.TenantConfig.MaxRules <= e.GlobalConfig.MaxRulesPerTenant {
		return e.TenantConfig.MaxRules
	}

	// Fall back to global limit
	return e.GlobalConfig.MaxRulesPerTenant
}
