# docman

Document management service


[![pipeline status](https://gitlab.com/hrglass/services/docman/badges/master/pipeline.svg)](https://gitlab.com/hrglass/services/docman/-/commits/master)
[![coverage report](https://gitlab.com/hrglass/services/docman/badges/master/coverage.svg)](https://gitlab.com/hrglass/services/docman/-/commits/master)

## Install

Following the Makefile in root folder.

## Development

### Dependencies
```bash
make deps
```

### Generate Mocks
```bash
make mocks
```

### Testing
```bash
# Run all tests
make test

# Run tests with coverage
go test -race -cover -v -count=1 -timeout=30s ./...

# Run specific test
go test -v ./internal/handlers/v3/gdrive -run TestDocumentsHandler_CreateV3_Success_AdminRole
```

### Testing Guidelines
We use [mockery](https://github.com/vektra/mockery) for automatic mock generation. See [docs/testing-guidelines.md](docs/testing-guidelines.md) for detailed testing standards and practices.

**Key Points:**
- DO NOT manually implement mocks
- Use generated mocks from `mocks/` directory
- Regenerate mocks when interfaces change: `make mocks`
- Maintain 80%+ test coverage
- Test all error paths and user authorization scenarios

## API Documentation

### Google Drive V3 API

The service provides comprehensive Google Drive integration through the `/v3/gdrive` API endpoints.

#### Search Files

Search for files across Google Drive by keyword with pagination support.

**Endpoint:** `GET /v3/gdrive/search/files`

**Query Parameters:**
- `keyword` (required): Search keyword for file names and content
- `page_size` (optional): Number of results per page (default: 100, max: 1000)
- `next_page` (optional): Pagination token for next page

**Example Request:**
```bash
curl -X GET "https://api.example.com/v3/gdrive/search/files?keyword=project%20report&page_size=50" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Example Response:**
```json
{
  "data": {
    "data": [
      {
        "docId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "Project Report.pdf",
        "size": 1048576,
        "webUrl": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
        "isFile": true,
        "dateModified": "2024-01-15T10:30:00Z",
        "dateCreated": "2024-01-10T09:00:00Z",
        "lastModifiedBy": "Google Drive User"
      }
    ],
    "meta": {
      "next_page": "next_page_token_123",
      "page_size": 50,
      "total": 1
    }
  }
}
```

**Features:**
- Searches both file names and file content
- Filters results to files only (excludes folders)
- Supports pagination with configurable page sizes
- Compatible with SharePoint search API structure
- Proper error handling and validation
- Timeout protection (60 seconds)

**Error Responses:**
- `400 Bad Request`: Missing or invalid search parameters
- `401 Unauthorized`: Authentication required
- `500 Internal Server Error`: Google Drive API errors or service issues
