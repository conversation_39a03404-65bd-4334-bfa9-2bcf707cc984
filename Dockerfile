FROM hub.bilabl.dev/core/ci-go:v2025.4.0 as build-env

ARG VERSION=0.0.0
ENV GOPRIVATE=code.mybil.net

ADD .ssh_private_key /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa


# cache dependencies first
WORKDIR /code
COPY go.mod .
COPY go.sum .
RUN go mod download

# lastly copy source, any change in source will not break above cache
COPY . .

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
  go build -a -ldflags="-s -w -X docman/pkg/app.version=${VERSION}" \
  -o app ./cmd/server

# runtime
FROM ubuntu:20.04

ENV ENABLE_DB=true

RUN apt-get update \
  && DEBIAN_FRONTEND="noninteractive" apt-get -y install tzdata ca-certificates --no-install-recommends \
  && rm -fr /var/lib/apt/lists/*

WORKDIR /code
ADD ./resources resources

COPY --from=build-env /code/app app

CMD ["/code/app"]
