// Code generated by mockery. DO NOT EDIT.

package sharepointclient

import (
	sharepointclient "bilabl/docman/pkg/sharepointclient"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

type MockClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClient) EXPECT() *MockClient_Expecter {
	return &MockClient_Expecter{mock: &_m.Mock}
}

// CopyDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, newItemId
func (_m *MockClient) CopyDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) error {
	ret := _m.Called(ctx, driveId, accessToken, itemId, newItemId)

	if len(ret) == 0 {
		panic("no return value specified for CopyDriveItem")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) error); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, newItemId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockClient_CopyDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyDriveItem'
type MockClient_CopyDriveItem_Call struct {
	*mock.Call
}

// CopyDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - newItemId string
func (_e *MockClient_Expecter) CopyDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, newItemId interface{}) *MockClient_CopyDriveItem_Call {
	return &MockClient_CopyDriveItem_Call{Call: _e.mock.On("CopyDriveItem", ctx, driveId, accessToken, itemId, newItemId)}
}

func (_c *MockClient_CopyDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string)) *MockClient_CopyDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_CopyDriveItem_Call) Return(_a0 error) *MockClient_CopyDriveItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClient_CopyDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string) error) *MockClient_CopyDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, folderName
func (_m *MockClient) CreateDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, folderName string) (sharepointclient.DriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, folderName)

	if len(ret) == 0 {
		panic("no return value specified for CreateDriveItem")
	}

	var r0 sharepointclient.DriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, folderName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) sharepointclient.DriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, folderName)
	} else {
		r0 = ret.Get(0).(sharepointclient.DriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, folderName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_CreateDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDriveItem'
type MockClient_CreateDriveItem_Call struct {
	*mock.Call
}

// CreateDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - folderName string
func (_e *MockClient_Expecter) CreateDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, folderName interface{}) *MockClient_CreateDriveItem_Call {
	return &MockClient_CreateDriveItem_Call{Call: _e.mock.On("CreateDriveItem", ctx, driveId, accessToken, itemId, folderName)}
}

func (_c *MockClient_CreateDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, folderName string)) *MockClient_CreateDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_CreateDriveItem_Call) Return(_a0 sharepointclient.DriveItemResponse, _a1 error) *MockClient_CreateDriveItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_CreateDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)) *MockClient_CreateDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUploadURL provides a mock function with given fields: ctx, driveId, accessToken, itemId, fileName
func (_m *MockClient) CreateUploadURL(ctx context.Context, driveId string, accessToken string, itemId string, fileName string) (sharepointclient.RequestUploadResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, fileName)

	if len(ret) == 0 {
		panic("no return value specified for CreateUploadURL")
	}

	var r0 sharepointclient.RequestUploadResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (sharepointclient.RequestUploadResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, fileName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) sharepointclient.RequestUploadResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, fileName)
	} else {
		r0 = ret.Get(0).(sharepointclient.RequestUploadResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, fileName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_CreateUploadURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUploadURL'
type MockClient_CreateUploadURL_Call struct {
	*mock.Call
}

// CreateUploadURL is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - fileName string
func (_e *MockClient_Expecter) CreateUploadURL(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, fileName interface{}) *MockClient_CreateUploadURL_Call {
	return &MockClient_CreateUploadURL_Call{Call: _e.mock.On("CreateUploadURL", ctx, driveId, accessToken, itemId, fileName)}
}

func (_c *MockClient_CreateUploadURL_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, fileName string)) *MockClient_CreateUploadURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_CreateUploadURL_Call) Return(_a0 sharepointclient.RequestUploadResponse, _a1 error) *MockClient_CreateUploadURL_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_CreateUploadURL_Call) RunAndReturn(run func(context.Context, string, string, string, string) (sharepointclient.RequestUploadResponse, error)) *MockClient_CreateUploadURL_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId
func (_m *MockClient) DeleteDriveItem(ctx context.Context, driveId string, accessToken string, itemId string) error {
	ret := _m.Called(ctx, driveId, accessToken, itemId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDriveItem")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, driveId, accessToken, itemId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockClient_DeleteDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDriveItem'
type MockClient_DeleteDriveItem_Call struct {
	*mock.Call
}

// DeleteDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
func (_e *MockClient_Expecter) DeleteDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}) *MockClient_DeleteDriveItem_Call {
	return &MockClient_DeleteDriveItem_Call{Call: _e.mock.On("DeleteDriveItem", ctx, driveId, accessToken, itemId)}
}

func (_c *MockClient_DeleteDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string)) *MockClient_DeleteDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockClient_DeleteDriveItem_Call) Return(_a0 error) *MockClient_DeleteDriveItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClient_DeleteDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string) error) *MockClient_DeleteDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// DeletePermissionDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, permId
func (_m *MockClient) DeletePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, permId string) error {
	ret := _m.Called(ctx, driveId, accessToken, itemId, permId)

	if len(ret) == 0 {
		panic("no return value specified for DeletePermissionDriveItem")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) error); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, permId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockClient_DeletePermissionDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePermissionDriveItem'
type MockClient_DeletePermissionDriveItem_Call struct {
	*mock.Call
}

// DeletePermissionDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - permId string
func (_e *MockClient_Expecter) DeletePermissionDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, permId interface{}) *MockClient_DeletePermissionDriveItem_Call {
	return &MockClient_DeletePermissionDriveItem_Call{Call: _e.mock.On("DeletePermissionDriveItem", ctx, driveId, accessToken, itemId, permId)}
}

func (_c *MockClient_DeletePermissionDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, permId string)) *MockClient_DeletePermissionDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_DeletePermissionDriveItem_Call) Return(_a0 error) *MockClient_DeletePermissionDriveItem_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClient_DeletePermissionDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string) error) *MockClient_DeletePermissionDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// ExchangeTokenBySecret provides a mock function with given fields: ctx, externalTenantId
func (_m *MockClient) ExchangeTokenBySecret(ctx context.Context, externalTenantId string) (string, error) {
	ret := _m.Called(ctx, externalTenantId)

	if len(ret) == 0 {
		panic("no return value specified for ExchangeTokenBySecret")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, externalTenantId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, externalTenantId)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalTenantId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_ExchangeTokenBySecret_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExchangeTokenBySecret'
type MockClient_ExchangeTokenBySecret_Call struct {
	*mock.Call
}

// ExchangeTokenBySecret is a helper method to define mock.On call
//   - ctx context.Context
//   - externalTenantId string
func (_e *MockClient_Expecter) ExchangeTokenBySecret(ctx interface{}, externalTenantId interface{}) *MockClient_ExchangeTokenBySecret_Call {
	return &MockClient_ExchangeTokenBySecret_Call{Call: _e.mock.On("ExchangeTokenBySecret", ctx, externalTenantId)}
}

func (_c *MockClient_ExchangeTokenBySecret_Call) Run(run func(ctx context.Context, externalTenantId string)) *MockClient_ExchangeTokenBySecret_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockClient_ExchangeTokenBySecret_Call) Return(_a0 string, _a1 error) *MockClient_ExchangeTokenBySecret_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_ExchangeTokenBySecret_Call) RunAndReturn(run func(context.Context, string) (string, error)) *MockClient_ExchangeTokenBySecret_Call {
	_c.Call.Return(run)
	return _c
}

// FilterChildren provides a mock function with given fields: ctx, driveId, accessToken, itemId, name
func (_m *MockClient) FilterChildren(ctx context.Context, driveId string, accessToken string, itemId string, name string) (*sharepointclient.ListDriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, name)

	if len(ret) == 0 {
		panic("no return value specified for FilterChildren")
	}

	var r0 *sharepointclient.ListDriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (*sharepointclient.ListDriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) *sharepointclient.ListDriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sharepointclient.ListDriveItemResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_FilterChildren_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterChildren'
type MockClient_FilterChildren_Call struct {
	*mock.Call
}

// FilterChildren is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - name string
func (_e *MockClient_Expecter) FilterChildren(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, name interface{}) *MockClient_FilterChildren_Call {
	return &MockClient_FilterChildren_Call{Call: _e.mock.On("FilterChildren", ctx, driveId, accessToken, itemId, name)}
}

func (_c *MockClient_FilterChildren_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, name string)) *MockClient_FilterChildren_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_FilterChildren_Call) Return(_a0 *sharepointclient.ListDriveItemResponse, _a1 error) *MockClient_FilterChildren_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_FilterChildren_Call) RunAndReturn(run func(context.Context, string, string, string, string) (*sharepointclient.ListDriveItemResponse, error)) *MockClient_FilterChildren_Call {
	_c.Call.Return(run)
	return _c
}

// GetDriveItemDetail provides a mock function with given fields: ctx, driveId, accessToken, itemId
func (_m *MockClient) GetDriveItemDetail(ctx context.Context, driveId string, accessToken string, itemId string) (sharepointclient.DriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId)

	if len(ret) == 0 {
		panic("no return value specified for GetDriveItemDetail")
	}

	var r0 sharepointclient.DriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (sharepointclient.DriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) sharepointclient.DriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId)
	} else {
		r0 = ret.Get(0).(sharepointclient.DriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_GetDriveItemDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDriveItemDetail'
type MockClient_GetDriveItemDetail_Call struct {
	*mock.Call
}

// GetDriveItemDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
func (_e *MockClient_Expecter) GetDriveItemDetail(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}) *MockClient_GetDriveItemDetail_Call {
	return &MockClient_GetDriveItemDetail_Call{Call: _e.mock.On("GetDriveItemDetail", ctx, driveId, accessToken, itemId)}
}

func (_c *MockClient_GetDriveItemDetail_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string)) *MockClient_GetDriveItemDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockClient_GetDriveItemDetail_Call) Return(_a0 sharepointclient.DriveItemResponse, _a1 error) *MockClient_GetDriveItemDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_GetDriveItemDetail_Call) RunAndReturn(run func(context.Context, string, string, string) (sharepointclient.DriveItemResponse, error)) *MockClient_GetDriveItemDetail_Call {
	_c.Call.Return(run)
	return _c
}

// GetListActivity provides a mock function with given fields: ctx, driveId, accessToken, skipToken
func (_m *MockClient) GetListActivity(ctx context.Context, driveId string, accessToken string, skipToken string) (sharepointclient.ListActivityResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, skipToken)

	if len(ret) == 0 {
		panic("no return value specified for GetListActivity")
	}

	var r0 sharepointclient.ListActivityResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (sharepointclient.ListActivityResponse, error)); ok {
		return rf(ctx, driveId, accessToken, skipToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) sharepointclient.ListActivityResponse); ok {
		r0 = rf(ctx, driveId, accessToken, skipToken)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListActivityResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, skipToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_GetListActivity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetListActivity'
type MockClient_GetListActivity_Call struct {
	*mock.Call
}

// GetListActivity is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - skipToken string
func (_e *MockClient_Expecter) GetListActivity(ctx interface{}, driveId interface{}, accessToken interface{}, skipToken interface{}) *MockClient_GetListActivity_Call {
	return &MockClient_GetListActivity_Call{Call: _e.mock.On("GetListActivity", ctx, driveId, accessToken, skipToken)}
}

func (_c *MockClient_GetListActivity_Call) Run(run func(ctx context.Context, driveId string, accessToken string, skipToken string)) *MockClient_GetListActivity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockClient_GetListActivity_Call) Return(_a0 sharepointclient.ListActivityResponse, _a1 error) *MockClient_GetListActivity_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_GetListActivity_Call) RunAndReturn(run func(context.Context, string, string, string) (sharepointclient.ListActivityResponse, error)) *MockClient_GetListActivity_Call {
	_c.Call.Return(run)
	return _c
}

// GetListDrive provides a mock function with given fields: ctx, siteId, accessToken
func (_m *MockClient) GetListDrive(ctx context.Context, siteId string, accessToken string) (sharepointclient.ListDriveResponse, error) {
	ret := _m.Called(ctx, siteId, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for GetListDrive")
	}

	var r0 sharepointclient.ListDriveResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (sharepointclient.ListDriveResponse, error)); ok {
		return rf(ctx, siteId, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) sharepointclient.ListDriveResponse); ok {
		r0 = rf(ctx, siteId, accessToken)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListDriveResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, siteId, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_GetListDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetListDrive'
type MockClient_GetListDrive_Call struct {
	*mock.Call
}

// GetListDrive is a helper method to define mock.On call
//   - ctx context.Context
//   - siteId string
//   - accessToken string
func (_e *MockClient_Expecter) GetListDrive(ctx interface{}, siteId interface{}, accessToken interface{}) *MockClient_GetListDrive_Call {
	return &MockClient_GetListDrive_Call{Call: _e.mock.On("GetListDrive", ctx, siteId, accessToken)}
}

func (_c *MockClient_GetListDrive_Call) Run(run func(ctx context.Context, siteId string, accessToken string)) *MockClient_GetListDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockClient_GetListDrive_Call) Return(_a0 sharepointclient.ListDriveResponse, _a1 error) *MockClient_GetListDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_GetListDrive_Call) RunAndReturn(run func(context.Context, string, string) (sharepointclient.ListDriveResponse, error)) *MockClient_GetListDrive_Call {
	_c.Call.Return(run)
	return _c
}

// GetListDriveItemChildren provides a mock function with given fields: ctx, driveId, accessToken, itemId
func (_m *MockClient) GetListDriveItemChildren(ctx context.Context, driveId string, accessToken string, itemId string) (sharepointclient.ListDriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId)

	if len(ret) == 0 {
		panic("no return value specified for GetListDriveItemChildren")
	}

	var r0 sharepointclient.ListDriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (sharepointclient.ListDriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) sharepointclient.ListDriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListDriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_GetListDriveItemChildren_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetListDriveItemChildren'
type MockClient_GetListDriveItemChildren_Call struct {
	*mock.Call
}

// GetListDriveItemChildren is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
func (_e *MockClient_Expecter) GetListDriveItemChildren(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}) *MockClient_GetListDriveItemChildren_Call {
	return &MockClient_GetListDriveItemChildren_Call{Call: _e.mock.On("GetListDriveItemChildren", ctx, driveId, accessToken, itemId)}
}

func (_c *MockClient_GetListDriveItemChildren_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string)) *MockClient_GetListDriveItemChildren_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockClient_GetListDriveItemChildren_Call) Return(_a0 sharepointclient.ListDriveItemResponse, _a1 error) *MockClient_GetListDriveItemChildren_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_GetListDriveItemChildren_Call) RunAndReturn(run func(context.Context, string, string, string) (sharepointclient.ListDriveItemResponse, error)) *MockClient_GetListDriveItemChildren_Call {
	_c.Call.Return(run)
	return _c
}

// GetListSite provides a mock function with given fields: ctx, accessToken
func (_m *MockClient) GetListSite(ctx context.Context, accessToken string) (sharepointclient.ListSiteResponse, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for GetListSite")
	}

	var r0 sharepointclient.ListSiteResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (sharepointclient.ListSiteResponse, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) sharepointclient.ListSiteResponse); ok {
		r0 = rf(ctx, accessToken)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListSiteResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_GetListSite_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetListSite'
type MockClient_GetListSite_Call struct {
	*mock.Call
}

// GetListSite is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockClient_Expecter) GetListSite(ctx interface{}, accessToken interface{}) *MockClient_GetListSite_Call {
	return &MockClient_GetListSite_Call{Call: _e.mock.On("GetListSite", ctx, accessToken)}
}

func (_c *MockClient_GetListSite_Call) Run(run func(ctx context.Context, accessToken string)) *MockClient_GetListSite_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockClient_GetListSite_Call) Return(_a0 sharepointclient.ListSiteResponse, _a1 error) *MockClient_GetListSite_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_GetListSite_Call) RunAndReturn(run func(context.Context, string) (sharepointclient.ListSiteResponse, error)) *MockClient_GetListSite_Call {
	_c.Call.Return(run)
	return _c
}

// InvitePermissionDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, emails
func (_m *MockClient) InvitePermissionDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, emails []string) (sharepointclient.ListPermissionResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, emails)

	if len(ret) == 0 {
		panic("no return value specified for InvitePermissionDriveItem")
	}

	var r0 sharepointclient.ListPermissionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, []string) (sharepointclient.ListPermissionResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, emails)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, []string) sharepointclient.ListPermissionResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, emails)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListPermissionResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, []string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, emails)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_InvitePermissionDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvitePermissionDriveItem'
type MockClient_InvitePermissionDriveItem_Call struct {
	*mock.Call
}

// InvitePermissionDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - emails []string
func (_e *MockClient_Expecter) InvitePermissionDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, emails interface{}) *MockClient_InvitePermissionDriveItem_Call {
	return &MockClient_InvitePermissionDriveItem_Call{Call: _e.mock.On("InvitePermissionDriveItem", ctx, driveId, accessToken, itemId, emails)}
}

func (_c *MockClient_InvitePermissionDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, emails []string)) *MockClient_InvitePermissionDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].([]string))
	})
	return _c
}

func (_c *MockClient_InvitePermissionDriveItem_Call) Return(_a0 sharepointclient.ListPermissionResponse, _a1 error) *MockClient_InvitePermissionDriveItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_InvitePermissionDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, []string) (sharepointclient.ListPermissionResponse, error)) *MockClient_InvitePermissionDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// MoveDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, newItemId
func (_m *MockClient) MoveDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string) (sharepointclient.DriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, newItemId)

	if len(ret) == 0 {
		panic("no return value specified for MoveDriveItem")
	}

	var r0 sharepointclient.DriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, newItemId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) sharepointclient.DriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, newItemId)
	} else {
		r0 = ret.Get(0).(sharepointclient.DriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, newItemId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_MoveDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MoveDriveItem'
type MockClient_MoveDriveItem_Call struct {
	*mock.Call
}

// MoveDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - newItemId string
func (_e *MockClient_Expecter) MoveDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, newItemId interface{}) *MockClient_MoveDriveItem_Call {
	return &MockClient_MoveDriveItem_Call{Call: _e.mock.On("MoveDriveItem", ctx, driveId, accessToken, itemId, newItemId)}
}

func (_c *MockClient_MoveDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, newItemId string)) *MockClient_MoveDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_MoveDriveItem_Call) Return(_a0 sharepointclient.DriveItemResponse, _a1 error) *MockClient_MoveDriveItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_MoveDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)) *MockClient_MoveDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// RenameDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, changeName
func (_m *MockClient) RenameDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, changeName string) (sharepointclient.DriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, changeName)

	if len(ret) == 0 {
		panic("no return value specified for RenameDriveItem")
	}

	var r0 sharepointclient.DriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, changeName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) sharepointclient.DriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, changeName)
	} else {
		r0 = ret.Get(0).(sharepointclient.DriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, changeName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_RenameDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RenameDriveItem'
type MockClient_RenameDriveItem_Call struct {
	*mock.Call
}

// RenameDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - changeName string
func (_e *MockClient_Expecter) RenameDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, changeName interface{}) *MockClient_RenameDriveItem_Call {
	return &MockClient_RenameDriveItem_Call{Call: _e.mock.On("RenameDriveItem", ctx, driveId, accessToken, itemId, changeName)}
}

func (_c *MockClient_RenameDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, changeName string)) *MockClient_RenameDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockClient_RenameDriveItem_Call) Return(_a0 sharepointclient.DriveItemResponse, _a1 error) *MockClient_RenameDriveItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_RenameDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string) (sharepointclient.DriveItemResponse, error)) *MockClient_RenameDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// SearchDriveItem provides a mock function with given fields: ctx, driveId, accessToken, itemId, str, pageSize, skipToken
func (_m *MockClient) SearchDriveItem(ctx context.Context, driveId string, accessToken string, itemId string, str string, pageSize int, skipToken string) (sharepointclient.ListDriveItemResponse, error) {
	ret := _m.Called(ctx, driveId, accessToken, itemId, str, pageSize, skipToken)

	if len(ret) == 0 {
		panic("no return value specified for SearchDriveItem")
	}

	var r0 sharepointclient.ListDriveItemResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, int, string) (sharepointclient.ListDriveItemResponse, error)); ok {
		return rf(ctx, driveId, accessToken, itemId, str, pageSize, skipToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, int, string) sharepointclient.ListDriveItemResponse); ok {
		r0 = rf(ctx, driveId, accessToken, itemId, str, pageSize, skipToken)
	} else {
		r0 = ret.Get(0).(sharepointclient.ListDriveItemResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string, int, string) error); ok {
		r1 = rf(ctx, driveId, accessToken, itemId, str, pageSize, skipToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockClient_SearchDriveItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchDriveItem'
type MockClient_SearchDriveItem_Call struct {
	*mock.Call
}

// SearchDriveItem is a helper method to define mock.On call
//   - ctx context.Context
//   - driveId string
//   - accessToken string
//   - itemId string
//   - str string
//   - pageSize int
//   - skipToken string
func (_e *MockClient_Expecter) SearchDriveItem(ctx interface{}, driveId interface{}, accessToken interface{}, itemId interface{}, str interface{}, pageSize interface{}, skipToken interface{}) *MockClient_SearchDriveItem_Call {
	return &MockClient_SearchDriveItem_Call{Call: _e.mock.On("SearchDriveItem", ctx, driveId, accessToken, itemId, str, pageSize, skipToken)}
}

func (_c *MockClient_SearchDriveItem_Call) Run(run func(ctx context.Context, driveId string, accessToken string, itemId string, str string, pageSize int, skipToken string)) *MockClient_SearchDriveItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string), args[5].(int), args[6].(string))
	})
	return _c
}

func (_c *MockClient_SearchDriveItem_Call) Return(_a0 sharepointclient.ListDriveItemResponse, _a1 error) *MockClient_SearchDriveItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockClient_SearchDriveItem_Call) RunAndReturn(run func(context.Context, string, string, string, string, int, string) (sharepointclient.ListDriveItemResponse, error)) *MockClient_SearchDriveItem_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
