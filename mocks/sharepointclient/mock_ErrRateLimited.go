// Code generated by mockery. DO NOT EDIT.

package sharepointclient

import mock "github.com/stretchr/testify/mock"

// MockErrRateLimited is an autogenerated mock type for the ErrRateLimited type
type MockErrRateLimited struct {
	mock.Mock
}

type MockErrRateLimited_Expecter struct {
	mock *mock.Mock
}

func (_m *MockErrRateLimited) EXPECT() *MockErrRateLimited_Expecter {
	return &MockErrRateLimited_Expecter{mock: &_m.Mock}
}

// RetryAfterHeader provides a mock function with no fields
func (_m *MockErrRateLimited) RetryAfterHeader() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RetryAfterHeader")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockErrRateLimited_RetryAfterHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetryAfterHeader'
type MockErrRateLimited_RetryAfterHeader_Call struct {
	*mock.Call
}

// RetryAfterHeader is a helper method to define mock.On call
func (_e *MockErrRateLimited_Expecter) RetryAfterHeader() *MockErrRateLimited_RetryAfterHeader_Call {
	return &MockErrRateLimited_RetryAfterHeader_Call{Call: _e.mock.On("RetryAfterHeader")}
}

func (_c *MockErrRateLimited_RetryAfterHeader_Call) Run(run func()) *MockErrRateLimited_RetryAfterHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockErrRateLimited_RetryAfterHeader_Call) Return(_a0 string) *MockErrRateLimited_RetryAfterHeader_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockErrRateLimited_RetryAfterHeader_Call) RunAndReturn(run func() string) *MockErrRateLimited_RetryAfterHeader_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockErrRateLimited creates a new instance of MockErrRateLimited. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockErrRateLimited(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockErrRateLimited {
	mock := &MockErrRateLimited{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
