// Code generated by mockery. DO NOT EDIT.

package sharepointclient

import (
	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// MockHttpClient is an autogenerated mock type for the HttpClient type
type MockHttpClient struct {
	mock.Mock
}

type MockHttpClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHttpClient) EXPECT() *MockHttpClient_Expecter {
	return &MockHttpClient_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: r
func (_m *MockHttpClient) Do(r *http.Request) (*http.Response, error) {
	ret := _m.Called(r)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(*http.Request) (*http.Response, error)); ok {
		return rf(r)
	}
	if rf, ok := ret.Get(0).(func(*http.Request) *http.Response); ok {
		r0 = rf(r)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(*http.Request) error); ok {
		r1 = rf(r)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockHttpClient_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockHttpClient_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - r *http.Request
func (_e *MockHttpClient_Expecter) Do(r interface{}) *MockHttpClient_Do_Call {
	return &MockHttpClient_Do_Call{Call: _e.mock.On("Do", r)}
}

func (_c *MockHttpClient_Do_Call) Run(run func(r *http.Request)) *MockHttpClient_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*http.Request))
	})
	return _c
}

func (_c *MockHttpClient_Do_Call) Return(_a0 *http.Response, _a1 error) *MockHttpClient_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockHttpClient_Do_Call) RunAndReturn(run func(*http.Request) (*http.Response, error)) *MockHttpClient_Do_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockHttpClient creates a new instance of MockHttpClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHttpClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHttpClient {
	mock := &MockHttpClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
