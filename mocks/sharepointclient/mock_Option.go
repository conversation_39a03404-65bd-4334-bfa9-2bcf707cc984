// Code generated by mockery. DO NOT EDIT.

package sharepointclient

import (
	sharepointclient "bilabl/docman/pkg/sharepointclient"

	mock "github.com/stretchr/testify/mock"
)

// MockOption is an autogenerated mock type for the Option type
type MockOption struct {
	mock.Mock
}

type MockOption_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOption) EXPECT() *MockOption_Expecter {
	return &MockOption_Expecter{mock: &_m.Mock}
}

// Execute provides a mock function with given fields: options
func (_m *MockOption) Execute(options *sharepointclient.ClientOptions) {
	_m.Called(options)
}

// MockOption_Execute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Execute'
type MockOption_Execute_Call struct {
	*mock.Call
}

// Execute is a helper method to define mock.On call
//   - options *sharepointclient.ClientOptions
func (_e *MockOption_Expecter) Execute(options interface{}) *MockOption_Execute_Call {
	return &MockOption_Execute_Call{Call: _e.mock.On("Execute", options)}
}

func (_c *MockOption_Execute_Call) Run(run func(options *sharepointclient.ClientOptions)) *MockOption_Execute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*sharepointclient.ClientOptions))
	})
	return _c
}

func (_c *MockOption_Execute_Call) Return() *MockOption_Execute_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOption_Execute_Call) RunAndReturn(run func(*sharepointclient.ClientOptions)) *MockOption_Execute_Call {
	_c.Run(run)
	return _c
}

// NewMockOption creates a new instance of MockOption. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOption(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOption {
	mock := &MockOption{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
