// Code generated by mockery. DO NOT EDIT.

package autodoc_mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockEventRuleMatchingService is an autogenerated mock type for the EventRuleMatchingService type
type MockEventRuleMatchingService struct {
	mock.Mock
}

type MockEventRuleMatchingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockEventRuleMatchingService) EXPECT() *MockEventRuleMatchingService_Expecter {
	return &MockEventRuleMatchingService_Expecter{mock: &_m.Mock}
}

// ProcessEvent provides a mock function with given fields: ctx, tenantID, eventType, eventData
func (_m *MockEventRuleMatchingService) ProcessEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventType, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ProcessEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventType, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ProcessMatterEvent provides a mock function with given fields: ctx, tenantID, eventType, eventData
func (_m *MockEventRuleMatchingService) ProcessMatterEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventType, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ProcessMatterEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventType, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ProcessClientEvent provides a mock function with given fields: ctx, tenantID, eventType, eventData
func (_m *MockEventRuleMatchingService) ProcessClientEvent(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventType, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ProcessClientEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventType, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockEventRuleMatchingService_ProcessEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessEvent'
type MockEventRuleMatchingService_ProcessEvent_Call struct {
	*mock.Call
}

// ProcessEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventType string
//   - eventData map[string]interface{}
func (_e *MockEventRuleMatchingService_Expecter) ProcessEvent(ctx interface{}, tenantID interface{}, eventType interface{}, eventData interface{}) *MockEventRuleMatchingService_ProcessEvent_Call {
	return &MockEventRuleMatchingService_ProcessEvent_Call{Call: _e.mock.On("ProcessEvent", ctx, tenantID, eventType, eventData)}
}

func (_c *MockEventRuleMatchingService_ProcessEvent_Call) Run(run func(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{})) *MockEventRuleMatchingService_ProcessEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(map[string]interface{}))
	})
	return _c
}

func (_c *MockEventRuleMatchingService_ProcessEvent_Call) Return(_a0 error) *MockEventRuleMatchingService_ProcessEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockEventRuleMatchingService_ProcessEvent_Call) RunAndReturn(run func(context.Context, uint64, string, map[string]interface{}) error) *MockEventRuleMatchingService_ProcessEvent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockEventRuleMatchingService creates a new instance of MockEventRuleMatchingService. It also registers a testing interface on the mock and a cleanup function to assert the mock's expectations.
// The first argument is typically a *testing.T value.
func NewMockEventRuleMatchingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockEventRuleMatchingService {
	mock := &MockEventRuleMatchingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
