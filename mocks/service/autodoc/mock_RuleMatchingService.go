// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockRuleMatchingService is an autogenerated mock type for the RuleMatchingService type
type MockRuleMatchingService struct {
	mock.Mock
}

type MockRuleMatchingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRuleMatchingService) EXPECT() *MockRuleMatchingService_Expecter {
	return &MockRuleMatchingService_Expecter{mock: &_m.Mock}
}

// EvaluateRule provides a mock function with given fields: ctx, rule, eventData
func (_m *MockRuleMatchingService) EvaluateRule(ctx context.Context, rule *model.DocumentAutomationRule, eventData map[string]interface{}) (bool, error) {
	ret := _m.Called(ctx, rule, eventData)

	if len(ret) == 0 {
		panic("no return value specified for EvaluateRule")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule, map[string]interface{}) (bool, error)); ok {
		return rf(ctx, rule, eventData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule, map[string]interface{}) bool); ok {
		r0 = rf(ctx, rule, eventData)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DocumentAutomationRule, map[string]interface{}) error); ok {
		r1 = rf(ctx, rule, eventData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuleMatchingService_EvaluateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EvaluateRule'
type MockRuleMatchingService_EvaluateRule_Call struct {
	*mock.Call
}

// EvaluateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
//   - eventData map[string]interface{}
func (_e *MockRuleMatchingService_Expecter) EvaluateRule(ctx interface{}, rule interface{}, eventData interface{}) *MockRuleMatchingService_EvaluateRule_Call {
	return &MockRuleMatchingService_EvaluateRule_Call{Call: _e.mock.On("EvaluateRule", ctx, rule, eventData)}
}

func (_c *MockRuleMatchingService_EvaluateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule, eventData map[string]interface{})) *MockRuleMatchingService_EvaluateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleMatchingService_EvaluateRule_Call) Return(_a0 bool, _a1 error) *MockRuleMatchingService_EvaluateRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuleMatchingService_EvaluateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule, map[string]interface{}) (bool, error)) *MockRuleMatchingService_EvaluateRule_Call {
	_c.Call.Return(run)
	return _c
}

// EvaluateTriggerConditions provides a mock function with given fields: ctx, triggerRules, eventData
func (_m *MockRuleMatchingService) EvaluateTriggerConditions(ctx context.Context, triggerRules model.TriggerRulesMap, eventData map[string]interface{}) (bool, error) {
	ret := _m.Called(ctx, triggerRules, eventData)

	if len(ret) == 0 {
		panic("no return value specified for EvaluateTriggerConditions")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.TriggerRulesMap, map[string]interface{}) (bool, error)); ok {
		return rf(ctx, triggerRules, eventData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.TriggerRulesMap, map[string]interface{}) bool); ok {
		r0 = rf(ctx, triggerRules, eventData)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.TriggerRulesMap, map[string]interface{}) error); ok {
		r1 = rf(ctx, triggerRules, eventData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuleMatchingService_EvaluateTriggerConditions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EvaluateTriggerConditions'
type MockRuleMatchingService_EvaluateTriggerConditions_Call struct {
	*mock.Call
}

// EvaluateTriggerConditions is a helper method to define mock.On call
//   - ctx context.Context
//   - triggerRules model.TriggerRulesMap
//   - eventData map[string]interface{}
func (_e *MockRuleMatchingService_Expecter) EvaluateTriggerConditions(ctx interface{}, triggerRules interface{}, eventData interface{}) *MockRuleMatchingService_EvaluateTriggerConditions_Call {
	return &MockRuleMatchingService_EvaluateTriggerConditions_Call{Call: _e.mock.On("EvaluateTriggerConditions", ctx, triggerRules, eventData)}
}

func (_c *MockRuleMatchingService_EvaluateTriggerConditions_Call) Run(run func(ctx context.Context, triggerRules model.TriggerRulesMap, eventData map[string]interface{})) *MockRuleMatchingService_EvaluateTriggerConditions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(model.TriggerRulesMap), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleMatchingService_EvaluateTriggerConditions_Call) Return(_a0 bool, _a1 error) *MockRuleMatchingService_EvaluateTriggerConditions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuleMatchingService_EvaluateTriggerConditions_Call) RunAndReturn(run func(context.Context, model.TriggerRulesMap, map[string]interface{}) (bool, error)) *MockRuleMatchingService_EvaluateTriggerConditions_Call {
	_c.Call.Return(run)
	return _c
}

// MatchRules provides a mock function with given fields: ctx, tenantID, eventType, eventData
func (_m *MockRuleMatchingService) MatchRules(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID, eventType, eventData)

	if len(ret) == 0 {
		panic("no return value specified for MatchRules")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID, eventType, eventData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID, eventType, eventData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r1 = rf(ctx, tenantID, eventType, eventData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuleMatchingService_MatchRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MatchRules'
type MockRuleMatchingService_MatchRules_Call struct {
	*mock.Call
}

// MatchRules is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventType string
//   - eventData map[string]interface{}
func (_e *MockRuleMatchingService_Expecter) MatchRules(ctx interface{}, tenantID interface{}, eventType interface{}, eventData interface{}) *MockRuleMatchingService_MatchRules_Call {
	return &MockRuleMatchingService_MatchRules_Call{Call: _e.mock.On("MatchRules", ctx, tenantID, eventType, eventData)}
}

func (_c *MockRuleMatchingService_MatchRules_Call) Run(run func(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{})) *MockRuleMatchingService_MatchRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleMatchingService_MatchRules_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockRuleMatchingService_MatchRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuleMatchingService_MatchRules_Call) RunAndReturn(run func(context.Context, uint64, string, map[string]interface{}) ([]*model.DocumentAutomationRule, error)) *MockRuleMatchingService_MatchRules_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRuleMatchingService creates a new instance of MockRuleMatchingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRuleMatchingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRuleMatchingService {
	mock := &MockRuleMatchingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
