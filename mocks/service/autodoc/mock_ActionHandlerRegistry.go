// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockActionHandlerRegistry is an autogenerated mock type for the ActionHandlerRegistry type
type MockActionHandlerRegistry struct {
	mock.Mock
}

type MockActionHandlerRegistry_Expecter struct {
	mock *mock.Mock
}

func (_m *MockActionHandlerRegistry) EXPECT() *MockActionHandlerRegistry_Expecter {
	return &MockActionHandlerRegistry_Expecter{mock: &_m.Mock}
}

// ExecuteAction provides a mock function with given fields: ctx, params
func (_m *MockActionHandlerRegistry) ExecuteAction(ctx context.Context, params *autodoc.ActionExecutionParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteAction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockActionHandlerRegistry_ExecuteAction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExecuteAction'
type MockActionHandlerRegistry_ExecuteAction_Call struct {
	*mock.Call
}

// ExecuteAction is a helper method to define mock.On call
//   - ctx context.Context
//   - params *autodoc.ActionExecutionParams
func (_e *MockActionHandlerRegistry_Expecter) ExecuteAction(ctx interface{}, params interface{}) *MockActionHandlerRegistry_ExecuteAction_Call {
	return &MockActionHandlerRegistry_ExecuteAction_Call{Call: _e.mock.On("ExecuteAction", ctx, params)}
}

func (_c *MockActionHandlerRegistry_ExecuteAction_Call) Run(run func(ctx context.Context, params *autodoc.ActionExecutionParams)) *MockActionHandlerRegistry_ExecuteAction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ActionExecutionParams))
	})
	return _c
}

func (_c *MockActionHandlerRegistry_ExecuteAction_Call) Return(_a0 error) *MockActionHandlerRegistry_ExecuteAction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandlerRegistry_ExecuteAction_Call) RunAndReturn(run func(context.Context, *autodoc.ActionExecutionParams) error) *MockActionHandlerRegistry_ExecuteAction_Call {
	_c.Call.Return(run)
	return _c
}

// GetHandler provides a mock function with given fields: actionType
func (_m *MockActionHandlerRegistry) GetHandler(actionType string) (autodoc.ActionHandler, error) {
	ret := _m.Called(actionType)

	if len(ret) == 0 {
		panic("no return value specified for GetHandler")
	}

	var r0 autodoc.ActionHandler
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (autodoc.ActionHandler, error)); ok {
		return rf(actionType)
	}
	if rf, ok := ret.Get(0).(func(string) autodoc.ActionHandler); ok {
		r0 = rf(actionType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.ActionHandler)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(actionType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockActionHandlerRegistry_GetHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHandler'
type MockActionHandlerRegistry_GetHandler_Call struct {
	*mock.Call
}

// GetHandler is a helper method to define mock.On call
//   - actionType string
func (_e *MockActionHandlerRegistry_Expecter) GetHandler(actionType interface{}) *MockActionHandlerRegistry_GetHandler_Call {
	return &MockActionHandlerRegistry_GetHandler_Call{Call: _e.mock.On("GetHandler", actionType)}
}

func (_c *MockActionHandlerRegistry_GetHandler_Call) Run(run func(actionType string)) *MockActionHandlerRegistry_GetHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockActionHandlerRegistry_GetHandler_Call) Return(_a0 autodoc.ActionHandler, _a1 error) *MockActionHandlerRegistry_GetHandler_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockActionHandlerRegistry_GetHandler_Call) RunAndReturn(run func(string) (autodoc.ActionHandler, error)) *MockActionHandlerRegistry_GetHandler_Call {
	_c.Call.Return(run)
	return _c
}

// GetSupportedActions provides a mock function with no fields
func (_m *MockActionHandlerRegistry) GetSupportedActions() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSupportedActions")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockActionHandlerRegistry_GetSupportedActions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSupportedActions'
type MockActionHandlerRegistry_GetSupportedActions_Call struct {
	*mock.Call
}

// GetSupportedActions is a helper method to define mock.On call
func (_e *MockActionHandlerRegistry_Expecter) GetSupportedActions() *MockActionHandlerRegistry_GetSupportedActions_Call {
	return &MockActionHandlerRegistry_GetSupportedActions_Call{Call: _e.mock.On("GetSupportedActions")}
}

func (_c *MockActionHandlerRegistry_GetSupportedActions_Call) Run(run func()) *MockActionHandlerRegistry_GetSupportedActions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockActionHandlerRegistry_GetSupportedActions_Call) Return(_a0 []string) *MockActionHandlerRegistry_GetSupportedActions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandlerRegistry_GetSupportedActions_Call) RunAndReturn(run func() []string) *MockActionHandlerRegistry_GetSupportedActions_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterHandler provides a mock function with given fields: handler
func (_m *MockActionHandlerRegistry) RegisterHandler(handler autodoc.ActionHandler) error {
	ret := _m.Called(handler)

	if len(ret) == 0 {
		panic("no return value specified for RegisterHandler")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(autodoc.ActionHandler) error); ok {
		r0 = rf(handler)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockActionHandlerRegistry_RegisterHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterHandler'
type MockActionHandlerRegistry_RegisterHandler_Call struct {
	*mock.Call
}

// RegisterHandler is a helper method to define mock.On call
//   - handler autodoc.ActionHandler
func (_e *MockActionHandlerRegistry_Expecter) RegisterHandler(handler interface{}) *MockActionHandlerRegistry_RegisterHandler_Call {
	return &MockActionHandlerRegistry_RegisterHandler_Call{Call: _e.mock.On("RegisterHandler", handler)}
}

func (_c *MockActionHandlerRegistry_RegisterHandler_Call) Run(run func(handler autodoc.ActionHandler)) *MockActionHandlerRegistry_RegisterHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(autodoc.ActionHandler))
	})
	return _c
}

func (_c *MockActionHandlerRegistry_RegisterHandler_Call) Return(_a0 error) *MockActionHandlerRegistry_RegisterHandler_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandlerRegistry_RegisterHandler_Call) RunAndReturn(run func(autodoc.ActionHandler) error) *MockActionHandlerRegistry_RegisterHandler_Call {
	_c.Call.Return(run)
	return _c
}

// TestAction provides a mock function with given fields: ctx, params
func (_m *MockActionHandlerRegistry) TestAction(ctx context.Context, params *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for TestAction")
	}

	var r0 *autodoc.ActionTestResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) *autodoc.ActionTestResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.ActionTestResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ActionExecutionParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockActionHandlerRegistry_TestAction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TestAction'
type MockActionHandlerRegistry_TestAction_Call struct {
	*mock.Call
}

// TestAction is a helper method to define mock.On call
//   - ctx context.Context
//   - params *autodoc.ActionExecutionParams
func (_e *MockActionHandlerRegistry_Expecter) TestAction(ctx interface{}, params interface{}) *MockActionHandlerRegistry_TestAction_Call {
	return &MockActionHandlerRegistry_TestAction_Call{Call: _e.mock.On("TestAction", ctx, params)}
}

func (_c *MockActionHandlerRegistry_TestAction_Call) Run(run func(ctx context.Context, params *autodoc.ActionExecutionParams)) *MockActionHandlerRegistry_TestAction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ActionExecutionParams))
	})
	return _c
}

func (_c *MockActionHandlerRegistry_TestAction_Call) Return(_a0 *autodoc.ActionTestResult, _a1 error) *MockActionHandlerRegistry_TestAction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockActionHandlerRegistry_TestAction_Call) RunAndReturn(run func(context.Context, *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error)) *MockActionHandlerRegistry_TestAction_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockActionHandlerRegistry creates a new instance of MockActionHandlerRegistry. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockActionHandlerRegistry(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockActionHandlerRegistry {
	mock := &MockActionHandlerRegistry{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
