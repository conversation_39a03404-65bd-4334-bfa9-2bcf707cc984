// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockAdvancedFileOperations is an autogenerated mock type for the AdvancedFileOperations type
type MockAdvancedFileOperations struct {
	mock.Mock
}

type MockAdvancedFileOperations_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAdvancedFileOperations) EXPECT() *MockAdvancedFileOperations_Expecter {
	return &MockAdvancedFileOperations_Expecter{mock: &_m.Mock}
}

// CopyFile provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) CopyFile(ctx context.Context, req *autodoc.CopyFileRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CopyFile")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyFileRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyFileRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CopyFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_CopyFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyFile'
type MockAdvancedFileOperations_CopyFile_Call struct {
	*mock.Call
}

// CopyFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CopyFileRequest
func (_e *MockAdvancedFileOperations_Expecter) CopyFile(ctx interface{}, req interface{}) *MockAdvancedFileOperations_CopyFile_Call {
	return &MockAdvancedFileOperations_CopyFile_Call{Call: _e.mock.On("CopyFile", ctx, req)}
}

func (_c *MockAdvancedFileOperations_CopyFile_Call) Run(run func(ctx context.Context, req *autodoc.CopyFileRequest)) *MockAdvancedFileOperations_CopyFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CopyFileRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_CopyFile_Call) Return(_a0 *model.Document, _a1 error) *MockAdvancedFileOperations_CopyFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_CopyFile_Call) RunAndReturn(run func(context.Context, *autodoc.CopyFileRequest) (*model.Document, error)) *MockAdvancedFileOperations_CopyFile_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolder provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) CreateFolder(ctx context.Context, req *autodoc.CreateFolderRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolder")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_CreateFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolder'
type MockAdvancedFileOperations_CreateFolder_Call struct {
	*mock.Call
}

// CreateFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateFolderRequest
func (_e *MockAdvancedFileOperations_Expecter) CreateFolder(ctx interface{}, req interface{}) *MockAdvancedFileOperations_CreateFolder_Call {
	return &MockAdvancedFileOperations_CreateFolder_Call{Call: _e.mock.On("CreateFolder", ctx, req)}
}

func (_c *MockAdvancedFileOperations_CreateFolder_Call) Run(run func(ctx context.Context, req *autodoc.CreateFolderRequest)) *MockAdvancedFileOperations_CreateFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateFolderRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_CreateFolder_Call) Return(_a0 *model.Document, _a1 error) *MockAdvancedFileOperations_CreateFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_CreateFolder_Call) RunAndReturn(run func(context.Context, *autodoc.CreateFolderRequest) (*model.Document, error)) *MockAdvancedFileOperations_CreateFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolderWithResponse provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) CreateFolderWithResponse(ctx context.Context, req *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolderWithResponse")
	}

	var r0 *autodoc.CreateFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) *autodoc.CreateFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.CreateFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_CreateFolderWithResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolderWithResponse'
type MockAdvancedFileOperations_CreateFolderWithResponse_Call struct {
	*mock.Call
}

// CreateFolderWithResponse is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateFolderRequest
func (_e *MockAdvancedFileOperations_Expecter) CreateFolderWithResponse(ctx interface{}, req interface{}) *MockAdvancedFileOperations_CreateFolderWithResponse_Call {
	return &MockAdvancedFileOperations_CreateFolderWithResponse_Call{Call: _e.mock.On("CreateFolderWithResponse", ctx, req)}
}

func (_c *MockAdvancedFileOperations_CreateFolderWithResponse_Call) Run(run func(ctx context.Context, req *autodoc.CreateFolderRequest)) *MockAdvancedFileOperations_CreateFolderWithResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateFolderRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_CreateFolderWithResponse_Call) Return(_a0 *autodoc.CreateFolderResponse, _a1 error) *MockAdvancedFileOperations_CreateFolderWithResponse_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_CreateFolderWithResponse_Call) RunAndReturn(run func(context.Context, *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error)) *MockAdvancedFileOperations_CreateFolderWithResponse_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileMetadata provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) GetFileMetadata(ctx context.Context, req *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFileMetadata")
	}

	var r0 *autodoc.FileMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileMetadataRequest) *autodoc.FileMetadata); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.FileMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.GetFileMetadataRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_GetFileMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileMetadata'
type MockAdvancedFileOperations_GetFileMetadata_Call struct {
	*mock.Call
}

// GetFileMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.GetFileMetadataRequest
func (_e *MockAdvancedFileOperations_Expecter) GetFileMetadata(ctx interface{}, req interface{}) *MockAdvancedFileOperations_GetFileMetadata_Call {
	return &MockAdvancedFileOperations_GetFileMetadata_Call{Call: _e.mock.On("GetFileMetadata", ctx, req)}
}

func (_c *MockAdvancedFileOperations_GetFileMetadata_Call) Run(run func(ctx context.Context, req *autodoc.GetFileMetadataRequest)) *MockAdvancedFileOperations_GetFileMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.GetFileMetadataRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_GetFileMetadata_Call) Return(_a0 *autodoc.FileMetadata, _a1 error) *MockAdvancedFileOperations_GetFileMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_GetFileMetadata_Call) RunAndReturn(run func(context.Context, *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error)) *MockAdvancedFileOperations_GetFileMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// GetFilePath provides a mock function with given fields: ctx, fileID, tenantID
func (_m *MockAdvancedFileOperations) GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error) {
	ret := _m.Called(ctx, fileID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetFilePath")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (string, error)); ok {
		return rf(ctx, fileID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) string); ok {
		r0 = rf(ctx, fileID, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, fileID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_GetFilePath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFilePath'
type MockAdvancedFileOperations_GetFilePath_Call struct {
	*mock.Call
}

// GetFilePath is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID uint64
//   - tenantID uint64
func (_e *MockAdvancedFileOperations_Expecter) GetFilePath(ctx interface{}, fileID interface{}, tenantID interface{}) *MockAdvancedFileOperations_GetFilePath_Call {
	return &MockAdvancedFileOperations_GetFilePath_Call{Call: _e.mock.On("GetFilePath", ctx, fileID, tenantID)}
}

func (_c *MockAdvancedFileOperations_GetFilePath_Call) Run(run func(ctx context.Context, fileID uint64, tenantID uint64)) *MockAdvancedFileOperations_GetFilePath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_GetFilePath_Call) Return(_a0 string, _a1 error) *MockAdvancedFileOperations_GetFilePath_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_GetFilePath_Call) RunAndReturn(run func(context.Context, uint64, uint64) (string, error)) *MockAdvancedFileOperations_GetFilePath_Call {
	_c.Call.Return(run)
	return _c
}

// ListFiles provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) ListFiles(ctx context.Context, req *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListFiles")
	}

	var r0 *autodoc.ListFilesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest) *autodoc.ListFilesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.ListFilesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ListFilesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_ListFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFiles'
type MockAdvancedFileOperations_ListFiles_Call struct {
	*mock.Call
}

// ListFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.ListFilesRequest
func (_e *MockAdvancedFileOperations_Expecter) ListFiles(ctx interface{}, req interface{}) *MockAdvancedFileOperations_ListFiles_Call {
	return &MockAdvancedFileOperations_ListFiles_Call{Call: _e.mock.On("ListFiles", ctx, req)}
}

func (_c *MockAdvancedFileOperations_ListFiles_Call) Run(run func(ctx context.Context, req *autodoc.ListFilesRequest)) *MockAdvancedFileOperations_ListFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ListFilesRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_ListFiles_Call) Return(_a0 *autodoc.ListFilesResponse, _a1 error) *MockAdvancedFileOperations_ListFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_ListFiles_Call) RunAndReturn(run func(context.Context, *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error)) *MockAdvancedFileOperations_ListFiles_Call {
	_c.Call.Return(run)
	return _c
}

// MoveFile provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) MoveFile(ctx context.Context, req *autodoc.MoveFileRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for MoveFile")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.MoveFileRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.MoveFileRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.MoveFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_MoveFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MoveFile'
type MockAdvancedFileOperations_MoveFile_Call struct {
	*mock.Call
}

// MoveFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.MoveFileRequest
func (_e *MockAdvancedFileOperations_Expecter) MoveFile(ctx interface{}, req interface{}) *MockAdvancedFileOperations_MoveFile_Call {
	return &MockAdvancedFileOperations_MoveFile_Call{Call: _e.mock.On("MoveFile", ctx, req)}
}

func (_c *MockAdvancedFileOperations_MoveFile_Call) Run(run func(ctx context.Context, req *autodoc.MoveFileRequest)) *MockAdvancedFileOperations_MoveFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.MoveFileRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_MoveFile_Call) Return(_a0 *model.Document, _a1 error) *MockAdvancedFileOperations_MoveFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_MoveFile_Call) RunAndReturn(run func(context.Context, *autodoc.MoveFileRequest) (*model.Document, error)) *MockAdvancedFileOperations_MoveFile_Call {
	_c.Call.Return(run)
	return _c
}

// SearchFiles provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) SearchFiles(ctx context.Context, req *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SearchFiles")
	}

	var r0 *autodoc.SearchFilesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SearchFilesRequest) *autodoc.SearchFilesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.SearchFilesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.SearchFilesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAdvancedFileOperations_SearchFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchFiles'
type MockAdvancedFileOperations_SearchFiles_Call struct {
	*mock.Call
}

// SearchFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.SearchFilesRequest
func (_e *MockAdvancedFileOperations_Expecter) SearchFiles(ctx interface{}, req interface{}) *MockAdvancedFileOperations_SearchFiles_Call {
	return &MockAdvancedFileOperations_SearchFiles_Call{Call: _e.mock.On("SearchFiles", ctx, req)}
}

func (_c *MockAdvancedFileOperations_SearchFiles_Call) Run(run func(ctx context.Context, req *autodoc.SearchFilesRequest)) *MockAdvancedFileOperations_SearchFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.SearchFilesRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_SearchFiles_Call) Return(_a0 *autodoc.SearchFilesResponse, _a1 error) *MockAdvancedFileOperations_SearchFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAdvancedFileOperations_SearchFiles_Call) RunAndReturn(run func(context.Context, *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error)) *MockAdvancedFileOperations_SearchFiles_Call {
	_c.Call.Return(run)
	return _c
}

// SetFilePermissions provides a mock function with given fields: ctx, req
func (_m *MockAdvancedFileOperations) SetFilePermissions(ctx context.Context, req *autodoc.SetFilePermissionsRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SetFilePermissions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SetFilePermissionsRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAdvancedFileOperations_SetFilePermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetFilePermissions'
type MockAdvancedFileOperations_SetFilePermissions_Call struct {
	*mock.Call
}

// SetFilePermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.SetFilePermissionsRequest
func (_e *MockAdvancedFileOperations_Expecter) SetFilePermissions(ctx interface{}, req interface{}) *MockAdvancedFileOperations_SetFilePermissions_Call {
	return &MockAdvancedFileOperations_SetFilePermissions_Call{Call: _e.mock.On("SetFilePermissions", ctx, req)}
}

func (_c *MockAdvancedFileOperations_SetFilePermissions_Call) Run(run func(ctx context.Context, req *autodoc.SetFilePermissionsRequest)) *MockAdvancedFileOperations_SetFilePermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.SetFilePermissionsRequest))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_SetFilePermissions_Call) Return(_a0 error) *MockAdvancedFileOperations_SetFilePermissions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAdvancedFileOperations_SetFilePermissions_Call) RunAndReturn(run func(context.Context, *autodoc.SetFilePermissionsRequest) error) *MockAdvancedFileOperations_SetFilePermissions_Call {
	_c.Call.Return(run)
	return _c
}

// ValidatePath provides a mock function with given fields: ctx, path, tenantID
func (_m *MockAdvancedFileOperations) ValidatePath(ctx context.Context, path string, tenantID uint64) error {
	ret := _m.Called(ctx, path, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for ValidatePath")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) error); ok {
		r0 = rf(ctx, path, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAdvancedFileOperations_ValidatePath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidatePath'
type MockAdvancedFileOperations_ValidatePath_Call struct {
	*mock.Call
}

// ValidatePath is a helper method to define mock.On call
//   - ctx context.Context
//   - path string
//   - tenantID uint64
func (_e *MockAdvancedFileOperations_Expecter) ValidatePath(ctx interface{}, path interface{}, tenantID interface{}) *MockAdvancedFileOperations_ValidatePath_Call {
	return &MockAdvancedFileOperations_ValidatePath_Call{Call: _e.mock.On("ValidatePath", ctx, path, tenantID)}
}

func (_c *MockAdvancedFileOperations_ValidatePath_Call) Run(run func(ctx context.Context, path string, tenantID uint64)) *MockAdvancedFileOperations_ValidatePath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockAdvancedFileOperations_ValidatePath_Call) Return(_a0 error) *MockAdvancedFileOperations_ValidatePath_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAdvancedFileOperations_ValidatePath_Call) RunAndReturn(run func(context.Context, string, uint64) error) *MockAdvancedFileOperations_ValidatePath_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAdvancedFileOperations creates a new instance of MockAdvancedFileOperations. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAdvancedFileOperations(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAdvancedFileOperations {
	mock := &MockAdvancedFileOperations{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
