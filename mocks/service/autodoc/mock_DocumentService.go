// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockDocumentService is an autogenerated mock type for the DocumentService type
type MockDocumentService struct {
	mock.Mock
}

type MockDocumentService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentService) EXPECT() *MockDocumentService_Expecter {
	return &MockDocumentService_Expecter{mock: &_m.Mock}
}

// CopyDocument provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CopyDocument(ctx context.Context, req *autodoc.CopyDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CopyDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CopyDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CopyDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyDocument'
type MockDocumentService_CopyDocument_Call struct {
	*mock.Call
}

// CopyDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CopyDocumentRequest
func (_e *MockDocumentService_Expecter) CopyDocument(ctx interface{}, req interface{}) *MockDocumentService_CopyDocument_Call {
	return &MockDocumentService_CopyDocument_Call{Call: _e.mock.On("CopyDocument", ctx, req)}
}

func (_c *MockDocumentService_CopyDocument_Call) Run(run func(ctx context.Context, req *autodoc.CopyDocumentRequest)) *MockDocumentService_CopyDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CopyDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_CopyDocument_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_CopyDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CopyDocument_Call) RunAndReturn(run func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)) *MockDocumentService_CopyDocument_Call {
	_c.Call.Return(run)
	return _c
}

// CopyFile provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CopyFile(ctx context.Context, req *autodoc.CopyFileRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CopyFile")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyFileRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyFileRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CopyFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CopyFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyFile'
type MockDocumentService_CopyFile_Call struct {
	*mock.Call
}

// CopyFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CopyFileRequest
func (_e *MockDocumentService_Expecter) CopyFile(ctx interface{}, req interface{}) *MockDocumentService_CopyFile_Call {
	return &MockDocumentService_CopyFile_Call{Call: _e.mock.On("CopyFile", ctx, req)}
}

func (_c *MockDocumentService_CopyFile_Call) Run(run func(ctx context.Context, req *autodoc.CopyFileRequest)) *MockDocumentService_CopyFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CopyFileRequest))
	})
	return _c
}

func (_c *MockDocumentService_CopyFile_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_CopyFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CopyFile_Call) RunAndReturn(run func(context.Context, *autodoc.CopyFileRequest) (*model.Document, error)) *MockDocumentService_CopyFile_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDocument provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CreateDocument(ctx context.Context, req *autodoc.CreateDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CreateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDocument'
type MockDocumentService_CreateDocument_Call struct {
	*mock.Call
}

// CreateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateDocumentRequest
func (_e *MockDocumentService_Expecter) CreateDocument(ctx interface{}, req interface{}) *MockDocumentService_CreateDocument_Call {
	return &MockDocumentService_CreateDocument_Call{Call: _e.mock.On("CreateDocument", ctx, req)}
}

func (_c *MockDocumentService_CreateDocument_Call) Run(run func(ctx context.Context, req *autodoc.CreateDocumentRequest)) *MockDocumentService_CreateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_CreateDocument_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_CreateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CreateDocument_Call) RunAndReturn(run func(context.Context, *autodoc.CreateDocumentRequest) (*model.Document, error)) *MockDocumentService_CreateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolder provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CreateFolder(ctx context.Context, req *autodoc.CreateFolderRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolder")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CreateFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolder'
type MockDocumentService_CreateFolder_Call struct {
	*mock.Call
}

// CreateFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateFolderRequest
func (_e *MockDocumentService_Expecter) CreateFolder(ctx interface{}, req interface{}) *MockDocumentService_CreateFolder_Call {
	return &MockDocumentService_CreateFolder_Call{Call: _e.mock.On("CreateFolder", ctx, req)}
}

func (_c *MockDocumentService_CreateFolder_Call) Run(run func(ctx context.Context, req *autodoc.CreateFolderRequest)) *MockDocumentService_CreateFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateFolderRequest))
	})
	return _c
}

func (_c *MockDocumentService_CreateFolder_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_CreateFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CreateFolder_Call) RunAndReturn(run func(context.Context, *autodoc.CreateFolderRequest) (*model.Document, error)) *MockDocumentService_CreateFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolderWithResponse provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CreateFolderWithResponse(ctx context.Context, req *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolderWithResponse")
	}

	var r0 *autodoc.CreateFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateFolderRequest) *autodoc.CreateFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.CreateFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CreateFolderWithResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolderWithResponse'
type MockDocumentService_CreateFolderWithResponse_Call struct {
	*mock.Call
}

// CreateFolderWithResponse is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateFolderRequest
func (_e *MockDocumentService_Expecter) CreateFolderWithResponse(ctx interface{}, req interface{}) *MockDocumentService_CreateFolderWithResponse_Call {
	return &MockDocumentService_CreateFolderWithResponse_Call{Call: _e.mock.On("CreateFolderWithResponse", ctx, req)}
}

func (_c *MockDocumentService_CreateFolderWithResponse_Call) Run(run func(ctx context.Context, req *autodoc.CreateFolderRequest)) *MockDocumentService_CreateFolderWithResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateFolderRequest))
	})
	return _c
}

func (_c *MockDocumentService_CreateFolderWithResponse_Call) Return(_a0 *autodoc.CreateFolderResponse, _a1 error) *MockDocumentService_CreateFolderWithResponse_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CreateFolderWithResponse_Call) RunAndReturn(run func(context.Context, *autodoc.CreateFolderRequest) (*autodoc.CreateFolderResponse, error)) *MockDocumentService_CreateFolderWithResponse_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDocument provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockDocumentService) DeleteDocument(ctx context.Context, documentID uint64, tenantID uint64) error {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocument")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) error); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentService_DeleteDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDocument'
type MockDocumentService_DeleteDocument_Call struct {
	*mock.Call
}

// DeleteDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) DeleteDocument(ctx interface{}, documentID interface{}, tenantID interface{}) *MockDocumentService_DeleteDocument_Call {
	return &MockDocumentService_DeleteDocument_Call{Call: _e.mock.On("DeleteDocument", ctx, documentID, tenantID)}
}

func (_c *MockDocumentService_DeleteDocument_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_DeleteDocument_Call) Return(_a0 error) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_DeleteDocument_Call) RunAndReturn(run func(context.Context, uint64, uint64) error) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDocumentByExternalID provides a mock function with given fields: ctx, externalID, tenantID
func (_m *MockDocumentService) DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error {
	ret := _m.Called(ctx, externalID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocumentByExternalID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) error); ok {
		r0 = rf(ctx, externalID, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentService_DeleteDocumentByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDocumentByExternalID'
type MockDocumentService_DeleteDocumentByExternalID_Call struct {
	*mock.Call
}

// DeleteDocumentByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) DeleteDocumentByExternalID(ctx interface{}, externalID interface{}, tenantID interface{}) *MockDocumentService_DeleteDocumentByExternalID_Call {
	return &MockDocumentService_DeleteDocumentByExternalID_Call{Call: _e.mock.On("DeleteDocumentByExternalID", ctx, externalID, tenantID)}
}

func (_c *MockDocumentService_DeleteDocumentByExternalID_Call) Run(run func(ctx context.Context, externalID string, tenantID uint64)) *MockDocumentService_DeleteDocumentByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_DeleteDocumentByExternalID_Call) Return(_a0 error) *MockDocumentService_DeleteDocumentByExternalID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_DeleteDocumentByExternalID_Call) RunAndReturn(run func(context.Context, string, uint64) error) *MockDocumentService_DeleteDocumentByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// EnsureAutoDocRoot provides a mock function with given fields: ctx, tenantID
func (_m *MockDocumentService) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for EnsureAutoDocRoot")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.Document, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.Document); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_EnsureAutoDocRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnsureAutoDocRoot'
type MockDocumentService_EnsureAutoDocRoot_Call struct {
	*mock.Call
}

// EnsureAutoDocRoot is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) EnsureAutoDocRoot(ctx interface{}, tenantID interface{}) *MockDocumentService_EnsureAutoDocRoot_Call {
	return &MockDocumentService_EnsureAutoDocRoot_Call{Call: _e.mock.On("EnsureAutoDocRoot", ctx, tenantID)}
}

func (_c *MockDocumentService_EnsureAutoDocRoot_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockDocumentService_EnsureAutoDocRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_EnsureAutoDocRoot_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_EnsureAutoDocRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_EnsureAutoDocRoot_Call) RunAndReturn(run func(context.Context, uint64) (*model.Document, error)) *MockDocumentService_EnsureAutoDocRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocument provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockDocumentService) GetDocument(ctx context.Context, documentID uint64, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (*model.Document, error)); ok {
		return rf(ctx, documentID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) *model.Document); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, documentID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocument'
type MockDocumentService_GetDocument_Call struct {
	*mock.Call
}

// GetDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) GetDocument(ctx interface{}, documentID interface{}, tenantID interface{}) *MockDocumentService_GetDocument_Call {
	return &MockDocumentService_GetDocument_Call{Call: _e.mock.On("GetDocument", ctx, documentID, tenantID)}
}

func (_c *MockDocumentService_GetDocument_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockDocumentService_GetDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_GetDocument_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_GetDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetDocument_Call) RunAndReturn(run func(context.Context, uint64, uint64) (*model.Document, error)) *MockDocumentService_GetDocument_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentByExternalID provides a mock function with given fields: ctx, externalID, tenantID
func (_m *MockDocumentService) GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, externalID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentByExternalID")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) (*model.Document, error)); ok {
		return rf(ctx, externalID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) *model.Document); ok {
		r0 = rf(ctx, externalID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, uint64) error); ok {
		r1 = rf(ctx, externalID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetDocumentByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentByExternalID'
type MockDocumentService_GetDocumentByExternalID_Call struct {
	*mock.Call
}

// GetDocumentByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) GetDocumentByExternalID(ctx interface{}, externalID interface{}, tenantID interface{}) *MockDocumentService_GetDocumentByExternalID_Call {
	return &MockDocumentService_GetDocumentByExternalID_Call{Call: _e.mock.On("GetDocumentByExternalID", ctx, externalID, tenantID)}
}

func (_c *MockDocumentService_GetDocumentByExternalID_Call) Run(run func(ctx context.Context, externalID string, tenantID uint64)) *MockDocumentService_GetDocumentByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_GetDocumentByExternalID_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_GetDocumentByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetDocumentByExternalID_Call) RunAndReturn(run func(context.Context, string, uint64) (*model.Document, error)) *MockDocumentService_GetDocumentByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentContent provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockDocumentService) GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error) {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentContent")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) ([]byte, error)); ok {
		return rf(ctx, documentID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) []byte); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, documentID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetDocumentContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentContent'
type MockDocumentService_GetDocumentContent_Call struct {
	*mock.Call
}

// GetDocumentContent is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) GetDocumentContent(ctx interface{}, documentID interface{}, tenantID interface{}) *MockDocumentService_GetDocumentContent_Call {
	return &MockDocumentService_GetDocumentContent_Call{Call: _e.mock.On("GetDocumentContent", ctx, documentID, tenantID)}
}

func (_c *MockDocumentService_GetDocumentContent_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockDocumentService_GetDocumentContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_GetDocumentContent_Call) Return(_a0 []byte, _a1 error) *MockDocumentService_GetDocumentContent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetDocumentContent_Call) RunAndReturn(run func(context.Context, uint64, uint64) ([]byte, error)) *MockDocumentService_GetDocumentContent_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileMetadata provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) GetFileMetadata(ctx context.Context, req *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFileMetadata")
	}

	var r0 *autodoc.FileMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileMetadataRequest) *autodoc.FileMetadata); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.FileMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.GetFileMetadataRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetFileMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileMetadata'
type MockDocumentService_GetFileMetadata_Call struct {
	*mock.Call
}

// GetFileMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.GetFileMetadataRequest
func (_e *MockDocumentService_Expecter) GetFileMetadata(ctx interface{}, req interface{}) *MockDocumentService_GetFileMetadata_Call {
	return &MockDocumentService_GetFileMetadata_Call{Call: _e.mock.On("GetFileMetadata", ctx, req)}
}

func (_c *MockDocumentService_GetFileMetadata_Call) Run(run func(ctx context.Context, req *autodoc.GetFileMetadataRequest)) *MockDocumentService_GetFileMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.GetFileMetadataRequest))
	})
	return _c
}

func (_c *MockDocumentService_GetFileMetadata_Call) Return(_a0 *autodoc.FileMetadata, _a1 error) *MockDocumentService_GetFileMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetFileMetadata_Call) RunAndReturn(run func(context.Context, *autodoc.GetFileMetadataRequest) (*autodoc.FileMetadata, error)) *MockDocumentService_GetFileMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// GetFilePath provides a mock function with given fields: ctx, fileID, tenantID
func (_m *MockDocumentService) GetFilePath(ctx context.Context, fileID uint64, tenantID uint64) (string, error) {
	ret := _m.Called(ctx, fileID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetFilePath")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (string, error)); ok {
		return rf(ctx, fileID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) string); ok {
		r0 = rf(ctx, fileID, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, fileID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetFilePath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFilePath'
type MockDocumentService_GetFilePath_Call struct {
	*mock.Call
}

// GetFilePath is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID uint64
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) GetFilePath(ctx interface{}, fileID interface{}, tenantID interface{}) *MockDocumentService_GetFilePath_Call {
	return &MockDocumentService_GetFilePath_Call{Call: _e.mock.On("GetFilePath", ctx, fileID, tenantID)}
}

func (_c *MockDocumentService_GetFilePath_Call) Run(run func(ctx context.Context, fileID uint64, tenantID uint64)) *MockDocumentService_GetFilePath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_GetFilePath_Call) Return(_a0 string, _a1 error) *MockDocumentService_GetFilePath_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetFilePath_Call) RunAndReturn(run func(context.Context, uint64, uint64) (string, error)) *MockDocumentService_GetFilePath_Call {
	_c.Call.Return(run)
	return _c
}

// ListFiles provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) ListFiles(ctx context.Context, req *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListFiles")
	}

	var r0 *autodoc.ListFilesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest) *autodoc.ListFilesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.ListFilesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ListFilesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_ListFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFiles'
type MockDocumentService_ListFiles_Call struct {
	*mock.Call
}

// ListFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.ListFilesRequest
func (_e *MockDocumentService_Expecter) ListFiles(ctx interface{}, req interface{}) *MockDocumentService_ListFiles_Call {
	return &MockDocumentService_ListFiles_Call{Call: _e.mock.On("ListFiles", ctx, req)}
}

func (_c *MockDocumentService_ListFiles_Call) Run(run func(ctx context.Context, req *autodoc.ListFilesRequest)) *MockDocumentService_ListFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ListFilesRequest))
	})
	return _c
}

func (_c *MockDocumentService_ListFiles_Call) Return(_a0 *autodoc.ListFilesResponse, _a1 error) *MockDocumentService_ListFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_ListFiles_Call) RunAndReturn(run func(context.Context, *autodoc.ListFilesRequest) (*autodoc.ListFilesResponse, error)) *MockDocumentService_ListFiles_Call {
	_c.Call.Return(run)
	return _c
}

// MoveFile provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) MoveFile(ctx context.Context, req *autodoc.MoveFileRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for MoveFile")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.MoveFileRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.MoveFileRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.MoveFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_MoveFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MoveFile'
type MockDocumentService_MoveFile_Call struct {
	*mock.Call
}

// MoveFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.MoveFileRequest
func (_e *MockDocumentService_Expecter) MoveFile(ctx interface{}, req interface{}) *MockDocumentService_MoveFile_Call {
	return &MockDocumentService_MoveFile_Call{Call: _e.mock.On("MoveFile", ctx, req)}
}

func (_c *MockDocumentService_MoveFile_Call) Run(run func(ctx context.Context, req *autodoc.MoveFileRequest)) *MockDocumentService_MoveFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.MoveFileRequest))
	})
	return _c
}

func (_c *MockDocumentService_MoveFile_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_MoveFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_MoveFile_Call) RunAndReturn(run func(context.Context, *autodoc.MoveFileRequest) (*model.Document, error)) *MockDocumentService_MoveFile_Call {
	_c.Call.Return(run)
	return _c
}

// SearchFiles provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) SearchFiles(ctx context.Context, req *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SearchFiles")
	}

	var r0 *autodoc.SearchFilesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SearchFilesRequest) *autodoc.SearchFilesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.SearchFilesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.SearchFilesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_SearchFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchFiles'
type MockDocumentService_SearchFiles_Call struct {
	*mock.Call
}

// SearchFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.SearchFilesRequest
func (_e *MockDocumentService_Expecter) SearchFiles(ctx interface{}, req interface{}) *MockDocumentService_SearchFiles_Call {
	return &MockDocumentService_SearchFiles_Call{Call: _e.mock.On("SearchFiles", ctx, req)}
}

func (_c *MockDocumentService_SearchFiles_Call) Run(run func(ctx context.Context, req *autodoc.SearchFilesRequest)) *MockDocumentService_SearchFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.SearchFilesRequest))
	})
	return _c
}

func (_c *MockDocumentService_SearchFiles_Call) Return(_a0 *autodoc.SearchFilesResponse, _a1 error) *MockDocumentService_SearchFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_SearchFiles_Call) RunAndReturn(run func(context.Context, *autodoc.SearchFilesRequest) (*autodoc.SearchFilesResponse, error)) *MockDocumentService_SearchFiles_Call {
	_c.Call.Return(run)
	return _c
}

// SetFilePermissions provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) SetFilePermissions(ctx context.Context, req *autodoc.SetFilePermissionsRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SetFilePermissions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.SetFilePermissionsRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentService_SetFilePermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetFilePermissions'
type MockDocumentService_SetFilePermissions_Call struct {
	*mock.Call
}

// SetFilePermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.SetFilePermissionsRequest
func (_e *MockDocumentService_Expecter) SetFilePermissions(ctx interface{}, req interface{}) *MockDocumentService_SetFilePermissions_Call {
	return &MockDocumentService_SetFilePermissions_Call{Call: _e.mock.On("SetFilePermissions", ctx, req)}
}

func (_c *MockDocumentService_SetFilePermissions_Call) Run(run func(ctx context.Context, req *autodoc.SetFilePermissionsRequest)) *MockDocumentService_SetFilePermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.SetFilePermissionsRequest))
	})
	return _c
}

func (_c *MockDocumentService_SetFilePermissions_Call) Return(_a0 error) *MockDocumentService_SetFilePermissions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_SetFilePermissions_Call) RunAndReturn(run func(context.Context, *autodoc.SetFilePermissionsRequest) error) *MockDocumentService_SetFilePermissions_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDocument provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) UpdateDocument(ctx context.Context, req *autodoc.UpdateDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.UpdateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_UpdateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDocument'
type MockDocumentService_UpdateDocument_Call struct {
	*mock.Call
}

// UpdateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.UpdateDocumentRequest
func (_e *MockDocumentService_Expecter) UpdateDocument(ctx interface{}, req interface{}) *MockDocumentService_UpdateDocument_Call {
	return &MockDocumentService_UpdateDocument_Call{Call: _e.mock.On("UpdateDocument", ctx, req)}
}

func (_c *MockDocumentService_UpdateDocument_Call) Run(run func(ctx context.Context, req *autodoc.UpdateDocumentRequest)) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.UpdateDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_UpdateDocument_Call) Return(_a0 *model.Document, _a1 error) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_UpdateDocument_Call) RunAndReturn(run func(context.Context, *autodoc.UpdateDocumentRequest) (*model.Document, error)) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// ValidatePath provides a mock function with given fields: ctx, path, tenantID
func (_m *MockDocumentService) ValidatePath(ctx context.Context, path string, tenantID uint64) error {
	ret := _m.Called(ctx, path, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for ValidatePath")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) error); ok {
		r0 = rf(ctx, path, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentService_ValidatePath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidatePath'
type MockDocumentService_ValidatePath_Call struct {
	*mock.Call
}

// ValidatePath is a helper method to define mock.On call
//   - ctx context.Context
//   - path string
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) ValidatePath(ctx interface{}, path interface{}, tenantID interface{}) *MockDocumentService_ValidatePath_Call {
	return &MockDocumentService_ValidatePath_Call{Call: _e.mock.On("ValidatePath", ctx, path, tenantID)}
}

func (_c *MockDocumentService_ValidatePath_Call) Run(run func(ctx context.Context, path string, tenantID uint64)) *MockDocumentService_ValidatePath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_ValidatePath_Call) Return(_a0 error) *MockDocumentService_ValidatePath_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_ValidatePath_Call) RunAndReturn(run func(context.Context, string, uint64) error) *MockDocumentService_ValidatePath_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentService creates a new instance of MockDocumentService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentService {
	mock := &MockDocumentService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
