// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockMatterEventConsumer is an autogenerated mock type for the MatterEventConsumer type
type MockMatterEventConsumer struct {
	mock.Mock
}

type MockMatterEventConsumer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMatterEventConsumer) EXPECT() *MockMatterEventConsumer_Expecter {
	return &MockMatterEventConsumer_Expecter{mock: &_m.Mock}
}

// ConsumeMatterCreated provides a mock function with given fields: ctx, tenantID, eventData
func (_m *MockMatterEventConsumer) ConsumeMatterCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ConsumeMatterCreated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMatterEventConsumer_ConsumeMatterCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConsumeMatterCreated'
type MockMatterEventConsumer_ConsumeMatterCreated_Call struct {
	*mock.Call
}

// ConsumeMatterCreated is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventData map[string]interface{}
func (_e *MockMatterEventConsumer_Expecter) ConsumeMatterCreated(ctx interface{}, tenantID interface{}, eventData interface{}) *MockMatterEventConsumer_ConsumeMatterCreated_Call {
	return &MockMatterEventConsumer_ConsumeMatterCreated_Call{Call: _e.mock.On("ConsumeMatterCreated", ctx, tenantID, eventData)}
}

func (_c *MockMatterEventConsumer_ConsumeMatterCreated_Call) Run(run func(ctx context.Context, tenantID uint64, eventData map[string]interface{})) *MockMatterEventConsumer_ConsumeMatterCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockMatterEventConsumer_ConsumeMatterCreated_Call) Return(_a0 error) *MockMatterEventConsumer_ConsumeMatterCreated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMatterEventConsumer_ConsumeMatterCreated_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) error) *MockMatterEventConsumer_ConsumeMatterCreated_Call {
	_c.Call.Return(run)
	return _c
}

// ConsumeMatterUpdated provides a mock function with given fields: ctx, tenantID, eventData
func (_m *MockMatterEventConsumer) ConsumeMatterUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ConsumeMatterUpdated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMatterEventConsumer_ConsumeMatterUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConsumeMatterUpdated'
type MockMatterEventConsumer_ConsumeMatterUpdated_Call struct {
	*mock.Call
}

// ConsumeMatterUpdated is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventData map[string]interface{}
func (_e *MockMatterEventConsumer_Expecter) ConsumeMatterUpdated(ctx interface{}, tenantID interface{}, eventData interface{}) *MockMatterEventConsumer_ConsumeMatterUpdated_Call {
	return &MockMatterEventConsumer_ConsumeMatterUpdated_Call{Call: _e.mock.On("ConsumeMatterUpdated", ctx, tenantID, eventData)}
}

func (_c *MockMatterEventConsumer_ConsumeMatterUpdated_Call) Run(run func(ctx context.Context, tenantID uint64, eventData map[string]interface{})) *MockMatterEventConsumer_ConsumeMatterUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockMatterEventConsumer_ConsumeMatterUpdated_Call) Return(_a0 error) *MockMatterEventConsumer_ConsumeMatterUpdated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMatterEventConsumer_ConsumeMatterUpdated_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) error) *MockMatterEventConsumer_ConsumeMatterUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockMatterEventConsumer creates a new instance of MockMatterEventConsumer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMatterEventConsumer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMatterEventConsumer {
	mock := &MockMatterEventConsumer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
