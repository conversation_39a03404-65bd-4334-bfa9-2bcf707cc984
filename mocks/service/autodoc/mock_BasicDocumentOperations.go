// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockBasicDocumentOperations is an autogenerated mock type for the BasicDocumentOperations type
type MockBasicDocumentOperations struct {
	mock.Mock
}

type MockBasicDocumentOperations_Expecter struct {
	mock *mock.Mock
}

func (_m *MockBasicDocumentOperations) EXPECT() *MockBasicDocumentOperations_Expecter {
	return &MockBasicDocumentOperations_Expecter{mock: &_m.Mock}
}

// CopyDocument provides a mock function with given fields: ctx, req
func (_m *MockBasicDocumentOperations) CopyDocument(ctx context.Context, req *autodoc.CopyDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CopyDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CopyDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_CopyDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyDocument'
type MockBasicDocumentOperations_CopyDocument_Call struct {
	*mock.Call
}

// CopyDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CopyDocumentRequest
func (_e *MockBasicDocumentOperations_Expecter) CopyDocument(ctx interface{}, req interface{}) *MockBasicDocumentOperations_CopyDocument_Call {
	return &MockBasicDocumentOperations_CopyDocument_Call{Call: _e.mock.On("CopyDocument", ctx, req)}
}

func (_c *MockBasicDocumentOperations_CopyDocument_Call) Run(run func(ctx context.Context, req *autodoc.CopyDocumentRequest)) *MockBasicDocumentOperations_CopyDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CopyDocumentRequest))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_CopyDocument_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_CopyDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_CopyDocument_Call) RunAndReturn(run func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)) *MockBasicDocumentOperations_CopyDocument_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDocument provides a mock function with given fields: ctx, req
func (_m *MockBasicDocumentOperations) CreateDocument(ctx context.Context, req *autodoc.CreateDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_CreateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDocument'
type MockBasicDocumentOperations_CreateDocument_Call struct {
	*mock.Call
}

// CreateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateDocumentRequest
func (_e *MockBasicDocumentOperations_Expecter) CreateDocument(ctx interface{}, req interface{}) *MockBasicDocumentOperations_CreateDocument_Call {
	return &MockBasicDocumentOperations_CreateDocument_Call{Call: _e.mock.On("CreateDocument", ctx, req)}
}

func (_c *MockBasicDocumentOperations_CreateDocument_Call) Run(run func(ctx context.Context, req *autodoc.CreateDocumentRequest)) *MockBasicDocumentOperations_CreateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateDocumentRequest))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_CreateDocument_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_CreateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_CreateDocument_Call) RunAndReturn(run func(context.Context, *autodoc.CreateDocumentRequest) (*model.Document, error)) *MockBasicDocumentOperations_CreateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDocument provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockBasicDocumentOperations) DeleteDocument(ctx context.Context, documentID uint64, tenantID uint64) error {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocument")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) error); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockBasicDocumentOperations_DeleteDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDocument'
type MockBasicDocumentOperations_DeleteDocument_Call struct {
	*mock.Call
}

// DeleteDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) DeleteDocument(ctx interface{}, documentID interface{}, tenantID interface{}) *MockBasicDocumentOperations_DeleteDocument_Call {
	return &MockBasicDocumentOperations_DeleteDocument_Call{Call: _e.mock.On("DeleteDocument", ctx, documentID, tenantID)}
}

func (_c *MockBasicDocumentOperations_DeleteDocument_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockBasicDocumentOperations_DeleteDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_DeleteDocument_Call) Return(_a0 error) *MockBasicDocumentOperations_DeleteDocument_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockBasicDocumentOperations_DeleteDocument_Call) RunAndReturn(run func(context.Context, uint64, uint64) error) *MockBasicDocumentOperations_DeleteDocument_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDocumentByExternalID provides a mock function with given fields: ctx, externalID, tenantID
func (_m *MockBasicDocumentOperations) DeleteDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) error {
	ret := _m.Called(ctx, externalID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocumentByExternalID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) error); ok {
		r0 = rf(ctx, externalID, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockBasicDocumentOperations_DeleteDocumentByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDocumentByExternalID'
type MockBasicDocumentOperations_DeleteDocumentByExternalID_Call struct {
	*mock.Call
}

// DeleteDocumentByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) DeleteDocumentByExternalID(ctx interface{}, externalID interface{}, tenantID interface{}) *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call {
	return &MockBasicDocumentOperations_DeleteDocumentByExternalID_Call{Call: _e.mock.On("DeleteDocumentByExternalID", ctx, externalID, tenantID)}
}

func (_c *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call) Run(run func(ctx context.Context, externalID string, tenantID uint64)) *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call) Return(_a0 error) *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call) RunAndReturn(run func(context.Context, string, uint64) error) *MockBasicDocumentOperations_DeleteDocumentByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// EnsureAutoDocRoot provides a mock function with given fields: ctx, tenantID
func (_m *MockBasicDocumentOperations) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for EnsureAutoDocRoot")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.Document, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.Document); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_EnsureAutoDocRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnsureAutoDocRoot'
type MockBasicDocumentOperations_EnsureAutoDocRoot_Call struct {
	*mock.Call
}

// EnsureAutoDocRoot is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) EnsureAutoDocRoot(ctx interface{}, tenantID interface{}) *MockBasicDocumentOperations_EnsureAutoDocRoot_Call {
	return &MockBasicDocumentOperations_EnsureAutoDocRoot_Call{Call: _e.mock.On("EnsureAutoDocRoot", ctx, tenantID)}
}

func (_c *MockBasicDocumentOperations_EnsureAutoDocRoot_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockBasicDocumentOperations_EnsureAutoDocRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_EnsureAutoDocRoot_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_EnsureAutoDocRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_EnsureAutoDocRoot_Call) RunAndReturn(run func(context.Context, uint64) (*model.Document, error)) *MockBasicDocumentOperations_EnsureAutoDocRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocument provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockBasicDocumentOperations) GetDocument(ctx context.Context, documentID uint64, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (*model.Document, error)); ok {
		return rf(ctx, documentID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) *model.Document); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, documentID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_GetDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocument'
type MockBasicDocumentOperations_GetDocument_Call struct {
	*mock.Call
}

// GetDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) GetDocument(ctx interface{}, documentID interface{}, tenantID interface{}) *MockBasicDocumentOperations_GetDocument_Call {
	return &MockBasicDocumentOperations_GetDocument_Call{Call: _e.mock.On("GetDocument", ctx, documentID, tenantID)}
}

func (_c *MockBasicDocumentOperations_GetDocument_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockBasicDocumentOperations_GetDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocument_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_GetDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocument_Call) RunAndReturn(run func(context.Context, uint64, uint64) (*model.Document, error)) *MockBasicDocumentOperations_GetDocument_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentByExternalID provides a mock function with given fields: ctx, externalID, tenantID
func (_m *MockBasicDocumentOperations) GetDocumentByExternalID(ctx context.Context, externalID string, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, externalID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentByExternalID")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) (*model.Document, error)); ok {
		return rf(ctx, externalID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) *model.Document); ok {
		r0 = rf(ctx, externalID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, uint64) error); ok {
		r1 = rf(ctx, externalID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_GetDocumentByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentByExternalID'
type MockBasicDocumentOperations_GetDocumentByExternalID_Call struct {
	*mock.Call
}

// GetDocumentByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) GetDocumentByExternalID(ctx interface{}, externalID interface{}, tenantID interface{}) *MockBasicDocumentOperations_GetDocumentByExternalID_Call {
	return &MockBasicDocumentOperations_GetDocumentByExternalID_Call{Call: _e.mock.On("GetDocumentByExternalID", ctx, externalID, tenantID)}
}

func (_c *MockBasicDocumentOperations_GetDocumentByExternalID_Call) Run(run func(ctx context.Context, externalID string, tenantID uint64)) *MockBasicDocumentOperations_GetDocumentByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocumentByExternalID_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_GetDocumentByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocumentByExternalID_Call) RunAndReturn(run func(context.Context, string, uint64) (*model.Document, error)) *MockBasicDocumentOperations_GetDocumentByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentContent provides a mock function with given fields: ctx, documentID, tenantID
func (_m *MockBasicDocumentOperations) GetDocumentContent(ctx context.Context, documentID uint64, tenantID uint64) ([]byte, error) {
	ret := _m.Called(ctx, documentID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentContent")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) ([]byte, error)); ok {
		return rf(ctx, documentID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) []byte); ok {
		r0 = rf(ctx, documentID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, documentID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_GetDocumentContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentContent'
type MockBasicDocumentOperations_GetDocumentContent_Call struct {
	*mock.Call
}

// GetDocumentContent is a helper method to define mock.On call
//   - ctx context.Context
//   - documentID uint64
//   - tenantID uint64
func (_e *MockBasicDocumentOperations_Expecter) GetDocumentContent(ctx interface{}, documentID interface{}, tenantID interface{}) *MockBasicDocumentOperations_GetDocumentContent_Call {
	return &MockBasicDocumentOperations_GetDocumentContent_Call{Call: _e.mock.On("GetDocumentContent", ctx, documentID, tenantID)}
}

func (_c *MockBasicDocumentOperations_GetDocumentContent_Call) Run(run func(ctx context.Context, documentID uint64, tenantID uint64)) *MockBasicDocumentOperations_GetDocumentContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocumentContent_Call) Return(_a0 []byte, _a1 error) *MockBasicDocumentOperations_GetDocumentContent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_GetDocumentContent_Call) RunAndReturn(run func(context.Context, uint64, uint64) ([]byte, error)) *MockBasicDocumentOperations_GetDocumentContent_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDocument provides a mock function with given fields: ctx, req
func (_m *MockBasicDocumentOperations) UpdateDocument(ctx context.Context, req *autodoc.UpdateDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.UpdateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBasicDocumentOperations_UpdateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDocument'
type MockBasicDocumentOperations_UpdateDocument_Call struct {
	*mock.Call
}

// UpdateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.UpdateDocumentRequest
func (_e *MockBasicDocumentOperations_Expecter) UpdateDocument(ctx interface{}, req interface{}) *MockBasicDocumentOperations_UpdateDocument_Call {
	return &MockBasicDocumentOperations_UpdateDocument_Call{Call: _e.mock.On("UpdateDocument", ctx, req)}
}

func (_c *MockBasicDocumentOperations_UpdateDocument_Call) Run(run func(ctx context.Context, req *autodoc.UpdateDocumentRequest)) *MockBasicDocumentOperations_UpdateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.UpdateDocumentRequest))
	})
	return _c
}

func (_c *MockBasicDocumentOperations_UpdateDocument_Call) Return(_a0 *model.Document, _a1 error) *MockBasicDocumentOperations_UpdateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBasicDocumentOperations_UpdateDocument_Call) RunAndReturn(run func(context.Context, *autodoc.UpdateDocumentRequest) (*model.Document, error)) *MockBasicDocumentOperations_UpdateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockBasicDocumentOperations creates a new instance of MockBasicDocumentOperations. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockBasicDocumentOperations(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockBasicDocumentOperations {
	mock := &MockBasicDocumentOperations{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
