// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"

	mock "github.com/stretchr/testify/mock"
)

// MockUploadProviderRegistry is an autogenerated mock type for the UploadProviderRegistry type
type MockUploadProviderRegistry struct {
	mock.Mock
}

type MockUploadProviderRegistry_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUploadProviderRegistry) EXPECT() *MockUploadProviderRegistry_Expecter {
	return &MockUploadProviderRegistry_Expecter{mock: &_m.Mock}
}

// GetDefaultProvider provides a mock function with no fields
func (_m *MockUploadProviderRegistry) GetDefaultProvider() autodoc.UploadProvider {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultProvider")
	}

	var r0 autodoc.UploadProvider
	if rf, ok := ret.Get(0).(func() autodoc.UploadProvider); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.UploadProvider)
		}
	}

	return r0
}

// MockUploadProviderRegistry_GetDefaultProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultProvider'
type MockUploadProviderRegistry_GetDefaultProvider_Call struct {
	*mock.Call
}

// GetDefaultProvider is a helper method to define mock.On call
func (_e *MockUploadProviderRegistry_Expecter) GetDefaultProvider() *MockUploadProviderRegistry_GetDefaultProvider_Call {
	return &MockUploadProviderRegistry_GetDefaultProvider_Call{Call: _e.mock.On("GetDefaultProvider")}
}

func (_c *MockUploadProviderRegistry_GetDefaultProvider_Call) Run(run func()) *MockUploadProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUploadProviderRegistry_GetDefaultProvider_Call) Return(_a0 autodoc.UploadProvider) *MockUploadProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadProviderRegistry_GetDefaultProvider_Call) RunAndReturn(run func() autodoc.UploadProvider) *MockUploadProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Return(run)
	return _c
}

// GetProvider provides a mock function with given fields: name
func (_m *MockUploadProviderRegistry) GetProvider(name string) (autodoc.UploadProvider, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetProvider")
	}

	var r0 autodoc.UploadProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (autodoc.UploadProvider, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) autodoc.UploadProvider); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.UploadProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadProviderRegistry_GetProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProvider'
type MockUploadProviderRegistry_GetProvider_Call struct {
	*mock.Call
}

// GetProvider is a helper method to define mock.On call
//   - name string
func (_e *MockUploadProviderRegistry_Expecter) GetProvider(name interface{}) *MockUploadProviderRegistry_GetProvider_Call {
	return &MockUploadProviderRegistry_GetProvider_Call{Call: _e.mock.On("GetProvider", name)}
}

func (_c *MockUploadProviderRegistry_GetProvider_Call) Run(run func(name string)) *MockUploadProviderRegistry_GetProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockUploadProviderRegistry_GetProvider_Call) Return(_a0 autodoc.UploadProvider, _a1 error) *MockUploadProviderRegistry_GetProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadProviderRegistry_GetProvider_Call) RunAndReturn(run func(string) (autodoc.UploadProvider, error)) *MockUploadProviderRegistry_GetProvider_Call {
	_c.Call.Return(run)
	return _c
}

// ListProviders provides a mock function with no fields
func (_m *MockUploadProviderRegistry) ListProviders() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListProviders")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockUploadProviderRegistry_ListProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListProviders'
type MockUploadProviderRegistry_ListProviders_Call struct {
	*mock.Call
}

// ListProviders is a helper method to define mock.On call
func (_e *MockUploadProviderRegistry_Expecter) ListProviders() *MockUploadProviderRegistry_ListProviders_Call {
	return &MockUploadProviderRegistry_ListProviders_Call{Call: _e.mock.On("ListProviders")}
}

func (_c *MockUploadProviderRegistry_ListProviders_Call) Run(run func()) *MockUploadProviderRegistry_ListProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUploadProviderRegistry_ListProviders_Call) Return(_a0 []string) *MockUploadProviderRegistry_ListProviders_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadProviderRegistry_ListProviders_Call) RunAndReturn(run func() []string) *MockUploadProviderRegistry_ListProviders_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterProvider provides a mock function with given fields: name, provider
func (_m *MockUploadProviderRegistry) RegisterProvider(name string, provider autodoc.UploadProvider) {
	_m.Called(name, provider)
}

// MockUploadProviderRegistry_RegisterProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterProvider'
type MockUploadProviderRegistry_RegisterProvider_Call struct {
	*mock.Call
}

// RegisterProvider is a helper method to define mock.On call
//   - name string
//   - provider autodoc.UploadProvider
func (_e *MockUploadProviderRegistry_Expecter) RegisterProvider(name interface{}, provider interface{}) *MockUploadProviderRegistry_RegisterProvider_Call {
	return &MockUploadProviderRegistry_RegisterProvider_Call{Call: _e.mock.On("RegisterProvider", name, provider)}
}

func (_c *MockUploadProviderRegistry_RegisterProvider_Call) Run(run func(name string, provider autodoc.UploadProvider)) *MockUploadProviderRegistry_RegisterProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(autodoc.UploadProvider))
	})
	return _c
}

func (_c *MockUploadProviderRegistry_RegisterProvider_Call) Return() *MockUploadProviderRegistry_RegisterProvider_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUploadProviderRegistry_RegisterProvider_Call) RunAndReturn(run func(string, autodoc.UploadProvider)) *MockUploadProviderRegistry_RegisterProvider_Call {
	_c.Run(run)
	return _c
}

// NewMockUploadProviderRegistry creates a new instance of MockUploadProviderRegistry. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUploadProviderRegistry(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUploadProviderRegistry {
	mock := &MockUploadProviderRegistry{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
