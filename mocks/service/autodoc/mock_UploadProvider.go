// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockUploadProvider is an autogenerated mock type for the UploadProvider type
type MockUploadProvider struct {
	mock.Mock
}

type MockUploadProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUploadProvider) EXPECT() *MockUploadProvider_Expecter {
	return &MockUploadProvider_Expecter{mock: &_m.Mock}
}

// CreateUploadSession provides a mock function with given fields: ctx, req
func (_m *MockUploadProvider) CreateUploadSession(ctx context.Context, req *autodoc.CreateUploadSessionRequest) (*autodoc.CreateUploadSessionResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateUploadSession")
	}

	var r0 *autodoc.CreateUploadSessionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateUploadSessionRequest) (*autodoc.CreateUploadSessionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateUploadSessionRequest) *autodoc.CreateUploadSessionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.CreateUploadSessionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateUploadSessionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadProvider_CreateUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUploadSession'
type MockUploadProvider_CreateUploadSession_Call struct {
	*mock.Call
}

// CreateUploadSession is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateUploadSessionRequest
func (_e *MockUploadProvider_Expecter) CreateUploadSession(ctx interface{}, req interface{}) *MockUploadProvider_CreateUploadSession_Call {
	return &MockUploadProvider_CreateUploadSession_Call{Call: _e.mock.On("CreateUploadSession", ctx, req)}
}

func (_c *MockUploadProvider_CreateUploadSession_Call) Run(run func(ctx context.Context, req *autodoc.CreateUploadSessionRequest)) *MockUploadProvider_CreateUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateUploadSessionRequest))
	})
	return _c
}

func (_c *MockUploadProvider_CreateUploadSession_Call) Return(_a0 *autodoc.CreateUploadSessionResponse, _a1 error) *MockUploadProvider_CreateUploadSession_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadProvider_CreateUploadSession_Call) RunAndReturn(run func(context.Context, *autodoc.CreateUploadSessionRequest) (*autodoc.CreateUploadSessionResponse, error)) *MockUploadProvider_CreateUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderName provides a mock function with no fields
func (_m *MockUploadProvider) GetProviderName() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetProviderName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockUploadProvider_GetProviderName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderName'
type MockUploadProvider_GetProviderName_Call struct {
	*mock.Call
}

// GetProviderName is a helper method to define mock.On call
func (_e *MockUploadProvider_Expecter) GetProviderName() *MockUploadProvider_GetProviderName_Call {
	return &MockUploadProvider_GetProviderName_Call{Call: _e.mock.On("GetProviderName")}
}

func (_c *MockUploadProvider_GetProviderName_Call) Run(run func()) *MockUploadProvider_GetProviderName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUploadProvider_GetProviderName_Call) Return(_a0 string) *MockUploadProvider_GetProviderName_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadProvider_GetProviderName_Call) RunAndReturn(run func() string) *MockUploadProvider_GetProviderName_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterUpload provides a mock function with given fields: ctx, req
func (_m *MockUploadProvider) RegisterUpload(ctx context.Context, req *autodoc.RegisterUploadRequest) (*autodoc.RegisterUploadResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RegisterUpload")
	}

	var r0 *autodoc.RegisterUploadResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.RegisterUploadRequest) (*autodoc.RegisterUploadResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.RegisterUploadRequest) *autodoc.RegisterUploadResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.RegisterUploadResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.RegisterUploadRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadProvider_RegisterUpload_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterUpload'
type MockUploadProvider_RegisterUpload_Call struct {
	*mock.Call
}

// RegisterUpload is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.RegisterUploadRequest
func (_e *MockUploadProvider_Expecter) RegisterUpload(ctx interface{}, req interface{}) *MockUploadProvider_RegisterUpload_Call {
	return &MockUploadProvider_RegisterUpload_Call{Call: _e.mock.On("RegisterUpload", ctx, req)}
}

func (_c *MockUploadProvider_RegisterUpload_Call) Run(run func(ctx context.Context, req *autodoc.RegisterUploadRequest)) *MockUploadProvider_RegisterUpload_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.RegisterUploadRequest))
	})
	return _c
}

func (_c *MockUploadProvider_RegisterUpload_Call) Return(_a0 *autodoc.RegisterUploadResponse, _a1 error) *MockUploadProvider_RegisterUpload_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadProvider_RegisterUpload_Call) RunAndReturn(run func(context.Context, *autodoc.RegisterUploadRequest) (*autodoc.RegisterUploadResponse, error)) *MockUploadProvider_RegisterUpload_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUploadProvider creates a new instance of MockUploadProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUploadProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUploadProvider {
	mock := &MockUploadProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
