// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockMappingCache is an autogenerated mock type for the MappingCache type
type MockMappingCache struct {
	mock.Mock
}

type MockMappingCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMappingCache) EXPECT() *MockMappingCache_Expecter {
	return &MockMappingCache_Expecter{mock: &_m.Mock}
}

// Clear provides a mock function with given fields: ctx, tenantID
func (_m *MockMappingCache) Clear(ctx context.Context, tenantID uint64) {
	_m.Called(ctx, tenantID)
}

// MockMappingCache_Clear_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Clear'
type MockMappingCache_Clear_Call struct {
	*mock.Call
}

// Clear is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) Clear(ctx interface{}, tenantID interface{}) *MockMappingCache_Clear_Call {
	return &MockMappingCache_Clear_Call{Call: _e.mock.On("Clear", ctx, tenantID)}
}

func (_c *MockMappingCache_Clear_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockMappingCache_Clear_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_Clear_Call) Return() *MockMappingCache_Clear_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMappingCache_Clear_Call) RunAndReturn(run func(context.Context, uint64)) *MockMappingCache_Clear_Call {
	_c.Run(run)
	return _c
}

// ClearProvider provides a mock function with given fields: ctx, provider, tenantID
func (_m *MockMappingCache) ClearProvider(ctx context.Context, provider string, tenantID uint64) {
	_m.Called(ctx, provider, tenantID)
}

// MockMappingCache_ClearProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClearProvider'
type MockMappingCache_ClearProvider_Call struct {
	*mock.Call
}

// ClearProvider is a helper method to define mock.On call
//   - ctx context.Context
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) ClearProvider(ctx interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_ClearProvider_Call {
	return &MockMappingCache_ClearProvider_Call{Call: _e.mock.On("ClearProvider", ctx, provider, tenantID)}
}

func (_c *MockMappingCache_ClearProvider_Call) Run(run func(ctx context.Context, provider string, tenantID uint64)) *MockMappingCache_ClearProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_ClearProvider_Call) Return() *MockMappingCache_ClearProvider_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMappingCache_ClearProvider_Call) RunAndReturn(run func(context.Context, string, uint64)) *MockMappingCache_ClearProvider_Call {
	_c.Run(run)
	return _c
}

// DeleteMapping provides a mock function with given fields: ctx, internalID, provider, tenantID
func (_m *MockMappingCache) DeleteMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) {
	_m.Called(ctx, internalID, provider, tenantID)
}

// MockMappingCache_DeleteMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMapping'
type MockMappingCache_DeleteMapping_Call struct {
	*mock.Call
}

// DeleteMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - internalID uint64
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) DeleteMapping(ctx interface{}, internalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_DeleteMapping_Call {
	return &MockMappingCache_DeleteMapping_Call{Call: _e.mock.On("DeleteMapping", ctx, internalID, provider, tenantID)}
}

func (_c *MockMappingCache_DeleteMapping_Call) Run(run func(ctx context.Context, internalID uint64, provider string, tenantID uint64)) *MockMappingCache_DeleteMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_DeleteMapping_Call) Return() *MockMappingCache_DeleteMapping_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMappingCache_DeleteMapping_Call) RunAndReturn(run func(context.Context, uint64, string, uint64)) *MockMappingCache_DeleteMapping_Call {
	_c.Run(run)
	return _c
}

// DeleteMappingByExternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingCache) DeleteMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) {
	_m.Called(ctx, externalID, provider, tenantID)
}

// MockMappingCache_DeleteMappingByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMappingByExternalID'
type MockMappingCache_DeleteMappingByExternalID_Call struct {
	*mock.Call
}

// DeleteMappingByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) DeleteMappingByExternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_DeleteMappingByExternalID_Call {
	return &MockMappingCache_DeleteMappingByExternalID_Call{Call: _e.mock.On("DeleteMappingByExternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingCache_DeleteMappingByExternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingCache_DeleteMappingByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_DeleteMappingByExternalID_Call) Return() *MockMappingCache_DeleteMappingByExternalID_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMappingCache_DeleteMappingByExternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64)) *MockMappingCache_DeleteMappingByExternalID_Call {
	_c.Run(run)
	return _c
}

// GetExternalID provides a mock function with given fields: ctx, internalID, provider, tenantID
func (_m *MockMappingCache) GetExternalID(ctx context.Context, internalID uint64, provider string, tenantID uint64) (string, bool) {
	ret := _m.Called(ctx, internalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetExternalID")
	}

	var r0 string
	var r1 bool
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, uint64) (string, bool)); ok {
		return rf(ctx, internalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, uint64) string); ok {
		r0 = rf(ctx, internalID, provider, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, uint64) bool); ok {
		r1 = rf(ctx, internalID, provider, tenantID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockMappingCache_GetExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExternalID'
type MockMappingCache_GetExternalID_Call struct {
	*mock.Call
}

// GetExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - internalID uint64
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) GetExternalID(ctx interface{}, internalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_GetExternalID_Call {
	return &MockMappingCache_GetExternalID_Call{Call: _e.mock.On("GetExternalID", ctx, internalID, provider, tenantID)}
}

func (_c *MockMappingCache_GetExternalID_Call) Run(run func(ctx context.Context, internalID uint64, provider string, tenantID uint64)) *MockMappingCache_GetExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_GetExternalID_Call) Return(_a0 string, _a1 bool) *MockMappingCache_GetExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingCache_GetExternalID_Call) RunAndReturn(run func(context.Context, uint64, string, uint64) (string, bool)) *MockMappingCache_GetExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetInternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingCache) GetInternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (uint64, bool) {
	ret := _m.Called(ctx, externalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetInternalID")
	}

	var r0 uint64
	var r1 bool
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) (uint64, bool)); ok {
		return rf(ctx, externalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) uint64); ok {
		r0 = rf(ctx, externalID, provider, tenantID)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, uint64) bool); ok {
		r1 = rf(ctx, externalID, provider, tenantID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockMappingCache_GetInternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInternalID'
type MockMappingCache_GetInternalID_Call struct {
	*mock.Call
}

// GetInternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) GetInternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_GetInternalID_Call {
	return &MockMappingCache_GetInternalID_Call{Call: _e.mock.On("GetInternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingCache_GetInternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingCache_GetInternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_GetInternalID_Call) Return(_a0 uint64, _a1 bool) *MockMappingCache_GetInternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingCache_GetInternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64) (uint64, bool)) *MockMappingCache_GetInternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMapping provides a mock function with given fields: ctx, internalID, provider, tenantID
func (_m *MockMappingCache) GetMapping(ctx context.Context, internalID uint64, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	ret := _m.Called(ctx, internalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetMapping")
	}

	var r0 *model.DocumentMapping
	var r1 bool
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, uint64) (*model.DocumentMapping, bool)); ok {
		return rf(ctx, internalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, uint64) *model.DocumentMapping); ok {
		r0 = rf(ctx, internalID, provider, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, uint64) bool); ok {
		r1 = rf(ctx, internalID, provider, tenantID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockMappingCache_GetMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMapping'
type MockMappingCache_GetMapping_Call struct {
	*mock.Call
}

// GetMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - internalID uint64
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) GetMapping(ctx interface{}, internalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_GetMapping_Call {
	return &MockMappingCache_GetMapping_Call{Call: _e.mock.On("GetMapping", ctx, internalID, provider, tenantID)}
}

func (_c *MockMappingCache_GetMapping_Call) Run(run func(ctx context.Context, internalID uint64, provider string, tenantID uint64)) *MockMappingCache_GetMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_GetMapping_Call) Return(_a0 *model.DocumentMapping, _a1 bool) *MockMappingCache_GetMapping_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingCache_GetMapping_Call) RunAndReturn(run func(context.Context, uint64, string, uint64) (*model.DocumentMapping, bool)) *MockMappingCache_GetMapping_Call {
	_c.Call.Return(run)
	return _c
}

// GetMappingByExternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingCache) GetMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (*model.DocumentMapping, bool) {
	ret := _m.Called(ctx, externalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetMappingByExternalID")
	}

	var r0 *model.DocumentMapping
	var r1 bool
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) (*model.DocumentMapping, bool)); ok {
		return rf(ctx, externalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) *model.DocumentMapping); ok {
		r0 = rf(ctx, externalID, provider, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, uint64) bool); ok {
		r1 = rf(ctx, externalID, provider, tenantID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockMappingCache_GetMappingByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMappingByExternalID'
type MockMappingCache_GetMappingByExternalID_Call struct {
	*mock.Call
}

// GetMappingByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingCache_Expecter) GetMappingByExternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingCache_GetMappingByExternalID_Call {
	return &MockMappingCache_GetMappingByExternalID_Call{Call: _e.mock.On("GetMappingByExternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingCache_GetMappingByExternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingCache_GetMappingByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingCache_GetMappingByExternalID_Call) Return(_a0 *model.DocumentMapping, _a1 bool) *MockMappingCache_GetMappingByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingCache_GetMappingByExternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64) (*model.DocumentMapping, bool)) *MockMappingCache_GetMappingByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// SetMapping provides a mock function with given fields: ctx, mapping
func (_m *MockMappingCache) SetMapping(ctx context.Context, mapping *model.DocumentMapping) {
	_m.Called(ctx, mapping)
}

// MockMappingCache_SetMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetMapping'
type MockMappingCache_SetMapping_Call struct {
	*mock.Call
}

// SetMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - mapping *model.DocumentMapping
func (_e *MockMappingCache_Expecter) SetMapping(ctx interface{}, mapping interface{}) *MockMappingCache_SetMapping_Call {
	return &MockMappingCache_SetMapping_Call{Call: _e.mock.On("SetMapping", ctx, mapping)}
}

func (_c *MockMappingCache_SetMapping_Call) Run(run func(ctx context.Context, mapping *model.DocumentMapping)) *MockMappingCache_SetMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentMapping))
	})
	return _c
}

func (_c *MockMappingCache_SetMapping_Call) Return() *MockMappingCache_SetMapping_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMappingCache_SetMapping_Call) RunAndReturn(run func(context.Context, *model.DocumentMapping)) *MockMappingCache_SetMapping_Call {
	_c.Run(run)
	return _c
}

// NewMockMappingCache creates a new instance of MockMappingCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMappingCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMappingCache {
	mock := &MockMappingCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
