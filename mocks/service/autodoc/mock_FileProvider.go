// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockFileProvider is an autogenerated mock type for the FileProvider type
type MockFileProvider struct {
	mock.Mock
}

type MockFileProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFileProvider) EXPECT() *MockFileProvider_Expecter {
	return &MockFileProvider_Expecter{mock: &_m.Mock}
}

// DeleteFile provides a mock function with given fields: ctx, req
func (_m *MockFileProvider) DeleteFile(ctx context.Context, req *autodoc.FileDeleteRequest) (*autodoc.FileDeleteResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFile")
	}

	var r0 *autodoc.FileDeleteResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.FileDeleteRequest) (*autodoc.FileDeleteResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.FileDeleteRequest) *autodoc.FileDeleteResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.FileDeleteResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.FileDeleteRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileProvider_DeleteFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFile'
type MockFileProvider_DeleteFile_Call struct {
	*mock.Call
}

// DeleteFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.FileDeleteRequest
func (_e *MockFileProvider_Expecter) DeleteFile(ctx interface{}, req interface{}) *MockFileProvider_DeleteFile_Call {
	return &MockFileProvider_DeleteFile_Call{Call: _e.mock.On("DeleteFile", ctx, req)}
}

func (_c *MockFileProvider_DeleteFile_Call) Run(run func(ctx context.Context, req *autodoc.FileDeleteRequest)) *MockFileProvider_DeleteFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.FileDeleteRequest))
	})
	return _c
}

func (_c *MockFileProvider_DeleteFile_Call) Return(_a0 *autodoc.FileDeleteResponse, _a1 error) *MockFileProvider_DeleteFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileProvider_DeleteFile_Call) RunAndReturn(run func(context.Context, *autodoc.FileDeleteRequest) (*autodoc.FileDeleteResponse, error)) *MockFileProvider_DeleteFile_Call {
	_c.Call.Return(run)
	return _c
}

// GetFile provides a mock function with given fields: ctx, req
func (_m *MockFileProvider) GetFile(ctx context.Context, req *autodoc.GetFileRequest) (*autodoc.GetFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFile")
	}

	var r0 *autodoc.GetFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileRequest) (*autodoc.GetFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.GetFileRequest) *autodoc.GetFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.GetFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.GetFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileProvider_GetFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFile'
type MockFileProvider_GetFile_Call struct {
	*mock.Call
}

// GetFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.GetFileRequest
func (_e *MockFileProvider_Expecter) GetFile(ctx interface{}, req interface{}) *MockFileProvider_GetFile_Call {
	return &MockFileProvider_GetFile_Call{Call: _e.mock.On("GetFile", ctx, req)}
}

func (_c *MockFileProvider_GetFile_Call) Run(run func(ctx context.Context, req *autodoc.GetFileRequest)) *MockFileProvider_GetFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.GetFileRequest))
	})
	return _c
}

func (_c *MockFileProvider_GetFile_Call) Return(_a0 *autodoc.GetFileResponse, _a1 error) *MockFileProvider_GetFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileProvider_GetFile_Call) RunAndReturn(run func(context.Context, *autodoc.GetFileRequest) (*autodoc.GetFileResponse, error)) *MockFileProvider_GetFile_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderName provides a mock function with no fields
func (_m *MockFileProvider) GetProviderName() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetProviderName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockFileProvider_GetProviderName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderName'
type MockFileProvider_GetProviderName_Call struct {
	*mock.Call
}

// GetProviderName is a helper method to define mock.On call
func (_e *MockFileProvider_Expecter) GetProviderName() *MockFileProvider_GetProviderName_Call {
	return &MockFileProvider_GetProviderName_Call{Call: _e.mock.On("GetProviderName")}
}

func (_c *MockFileProvider_GetProviderName_Call) Run(run func()) *MockFileProvider_GetProviderName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockFileProvider_GetProviderName_Call) Return(_a0 string) *MockFileProvider_GetProviderName_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileProvider_GetProviderName_Call) RunAndReturn(run func() string) *MockFileProvider_GetProviderName_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFile provides a mock function with given fields: ctx, req
func (_m *MockFileProvider) UpdateFile(ctx context.Context, req *autodoc.UpdateFileRequest) (*autodoc.UpdateFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFile")
	}

	var r0 *autodoc.UpdateFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateFileRequest) (*autodoc.UpdateFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateFileRequest) *autodoc.UpdateFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.UpdateFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.UpdateFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileProvider_UpdateFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFile'
type MockFileProvider_UpdateFile_Call struct {
	*mock.Call
}

// UpdateFile is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.UpdateFileRequest
func (_e *MockFileProvider_Expecter) UpdateFile(ctx interface{}, req interface{}) *MockFileProvider_UpdateFile_Call {
	return &MockFileProvider_UpdateFile_Call{Call: _e.mock.On("UpdateFile", ctx, req)}
}

func (_c *MockFileProvider_UpdateFile_Call) Run(run func(ctx context.Context, req *autodoc.UpdateFileRequest)) *MockFileProvider_UpdateFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.UpdateFileRequest))
	})
	return _c
}

func (_c *MockFileProvider_UpdateFile_Call) Return(_a0 *autodoc.UpdateFileResponse, _a1 error) *MockFileProvider_UpdateFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileProvider_UpdateFile_Call) RunAndReturn(run func(context.Context, *autodoc.UpdateFileRequest) (*autodoc.UpdateFileResponse, error)) *MockFileProvider_UpdateFile_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFileProvider creates a new instance of MockFileProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFileProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFileProvider {
	mock := &MockFileProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
