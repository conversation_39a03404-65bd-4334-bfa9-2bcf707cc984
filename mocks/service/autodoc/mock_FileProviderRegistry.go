// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"

	mock "github.com/stretchr/testify/mock"
)

// MockFileProviderRegistry is an autogenerated mock type for the FileProviderRegistry type
type MockFileProviderRegistry struct {
	mock.Mock
}

type MockFileProviderRegistry_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFileProviderRegistry) EXPECT() *MockFileProviderRegistry_Expecter {
	return &MockFileProviderRegistry_Expecter{mock: &_m.Mock}
}

// GetDefaultProvider provides a mock function with no fields
func (_m *MockFileProviderRegistry) GetDefaultProvider() autodoc.FileProvider {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultProvider")
	}

	var r0 autodoc.FileProvider
	if rf, ok := ret.Get(0).(func() autodoc.FileProvider); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.FileProvider)
		}
	}

	return r0
}

// MockFileProviderRegistry_GetDefaultProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultProvider'
type MockFileProviderRegistry_GetDefaultProvider_Call struct {
	*mock.Call
}

// GetDefaultProvider is a helper method to define mock.On call
func (_e *MockFileProviderRegistry_Expecter) GetDefaultProvider() *MockFileProviderRegistry_GetDefaultProvider_Call {
	return &MockFileProviderRegistry_GetDefaultProvider_Call{Call: _e.mock.On("GetDefaultProvider")}
}

func (_c *MockFileProviderRegistry_GetDefaultProvider_Call) Run(run func()) *MockFileProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockFileProviderRegistry_GetDefaultProvider_Call) Return(_a0 autodoc.FileProvider) *MockFileProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileProviderRegistry_GetDefaultProvider_Call) RunAndReturn(run func() autodoc.FileProvider) *MockFileProviderRegistry_GetDefaultProvider_Call {
	_c.Call.Return(run)
	return _c
}

// GetProvider provides a mock function with given fields: name
func (_m *MockFileProviderRegistry) GetProvider(name string) (autodoc.FileProvider, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetProvider")
	}

	var r0 autodoc.FileProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (autodoc.FileProvider, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) autodoc.FileProvider); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.FileProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileProviderRegistry_GetProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProvider'
type MockFileProviderRegistry_GetProvider_Call struct {
	*mock.Call
}

// GetProvider is a helper method to define mock.On call
//   - name string
func (_e *MockFileProviderRegistry_Expecter) GetProvider(name interface{}) *MockFileProviderRegistry_GetProvider_Call {
	return &MockFileProviderRegistry_GetProvider_Call{Call: _e.mock.On("GetProvider", name)}
}

func (_c *MockFileProviderRegistry_GetProvider_Call) Run(run func(name string)) *MockFileProviderRegistry_GetProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFileProviderRegistry_GetProvider_Call) Return(_a0 autodoc.FileProvider, _a1 error) *MockFileProviderRegistry_GetProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileProviderRegistry_GetProvider_Call) RunAndReturn(run func(string) (autodoc.FileProvider, error)) *MockFileProviderRegistry_GetProvider_Call {
	_c.Call.Return(run)
	return _c
}

// ListProviders provides a mock function with no fields
func (_m *MockFileProviderRegistry) ListProviders() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListProviders")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockFileProviderRegistry_ListProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListProviders'
type MockFileProviderRegistry_ListProviders_Call struct {
	*mock.Call
}

// ListProviders is a helper method to define mock.On call
func (_e *MockFileProviderRegistry_Expecter) ListProviders() *MockFileProviderRegistry_ListProviders_Call {
	return &MockFileProviderRegistry_ListProviders_Call{Call: _e.mock.On("ListProviders")}
}

func (_c *MockFileProviderRegistry_ListProviders_Call) Run(run func()) *MockFileProviderRegistry_ListProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockFileProviderRegistry_ListProviders_Call) Return(_a0 []string) *MockFileProviderRegistry_ListProviders_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileProviderRegistry_ListProviders_Call) RunAndReturn(run func() []string) *MockFileProviderRegistry_ListProviders_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterProvider provides a mock function with given fields: name, provider
func (_m *MockFileProviderRegistry) RegisterProvider(name string, provider autodoc.FileProvider) {
	_m.Called(name, provider)
}

// MockFileProviderRegistry_RegisterProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterProvider'
type MockFileProviderRegistry_RegisterProvider_Call struct {
	*mock.Call
}

// RegisterProvider is a helper method to define mock.On call
//   - name string
//   - provider autodoc.FileProvider
func (_e *MockFileProviderRegistry_Expecter) RegisterProvider(name interface{}, provider interface{}) *MockFileProviderRegistry_RegisterProvider_Call {
	return &MockFileProviderRegistry_RegisterProvider_Call{Call: _e.mock.On("RegisterProvider", name, provider)}
}

func (_c *MockFileProviderRegistry_RegisterProvider_Call) Run(run func(name string, provider autodoc.FileProvider)) *MockFileProviderRegistry_RegisterProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(autodoc.FileProvider))
	})
	return _c
}

func (_c *MockFileProviderRegistry_RegisterProvider_Call) Return() *MockFileProviderRegistry_RegisterProvider_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockFileProviderRegistry_RegisterProvider_Call) RunAndReturn(run func(string, autodoc.FileProvider)) *MockFileProviderRegistry_RegisterProvider_Call {
	_c.Run(run)
	return _c
}

// NewMockFileProviderRegistry creates a new instance of MockFileProviderRegistry. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFileProviderRegistry(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFileProviderRegistry {
	mock := &MockFileProviderRegistry{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
