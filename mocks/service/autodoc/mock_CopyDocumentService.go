// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockCopyDocumentService is an autogenerated mock type for the CopyDocumentService type
type MockCopyDocumentService struct {
	mock.Mock
}

type MockCopyDocumentService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCopyDocumentService) EXPECT() *MockCopyDocumentService_Expecter {
	return &MockCopyDocumentService_Expecter{mock: &_m.Mock}
}

// CopyDocument provides a mock function with given fields: ctx, req
func (_m *MockCopyDocumentService) CopyDocument(ctx context.Context, req *autodoc.CopyDocumentRequest) (*model.Document, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CopyDocument")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CopyDocumentRequest) *model.Document); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CopyDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCopyDocumentService_CopyDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyDocument'
type MockCopyDocumentService_CopyDocument_Call struct {
	*mock.Call
}

// CopyDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CopyDocumentRequest
func (_e *MockCopyDocumentService_Expecter) CopyDocument(ctx interface{}, req interface{}) *MockCopyDocumentService_CopyDocument_Call {
	return &MockCopyDocumentService_CopyDocument_Call{Call: _e.mock.On("CopyDocument", ctx, req)}
}

func (_c *MockCopyDocumentService_CopyDocument_Call) Run(run func(ctx context.Context, req *autodoc.CopyDocumentRequest)) *MockCopyDocumentService_CopyDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CopyDocumentRequest))
	})
	return _c
}

func (_c *MockCopyDocumentService_CopyDocument_Call) Return(_a0 *model.Document, _a1 error) *MockCopyDocumentService_CopyDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCopyDocumentService_CopyDocument_Call) RunAndReturn(run func(context.Context, *autodoc.CopyDocumentRequest) (*model.Document, error)) *MockCopyDocumentService_CopyDocument_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCopyDocumentService creates a new instance of MockCopyDocumentService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCopyDocumentService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCopyDocumentService {
	mock := &MockCopyDocumentService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
