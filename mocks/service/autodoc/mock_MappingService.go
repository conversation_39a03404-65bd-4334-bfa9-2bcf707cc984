// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockMappingService is an autogenerated mock type for the MappingService type
type MockMappingService struct {
	mock.Mock
}

type MockMappingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMappingService) EXPECT() *MockMappingService_Expecter {
	return &MockMappingService_Expecter{mock: &_m.Mock}
}

// BulkCreateMappings provides a mock function with given fields: ctx, mappings
func (_m *MockMappingService) BulkCreateMappings(ctx context.Context, mappings []*autodoc.CreateMappingRequest) ([]*model.DocumentMapping, error) {
	ret := _m.Called(ctx, mappings)

	if len(ret) == 0 {
		panic("no return value specified for BulkCreateMappings")
	}

	var r0 []*model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []*autodoc.CreateMappingRequest) ([]*model.DocumentMapping, error)); ok {
		return rf(ctx, mappings)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []*autodoc.CreateMappingRequest) []*model.DocumentMapping); ok {
		r0 = rf(ctx, mappings)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []*autodoc.CreateMappingRequest) error); ok {
		r1 = rf(ctx, mappings)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_BulkCreateMappings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkCreateMappings'
type MockMappingService_BulkCreateMappings_Call struct {
	*mock.Call
}

// BulkCreateMappings is a helper method to define mock.On call
//   - ctx context.Context
//   - mappings []*autodoc.CreateMappingRequest
func (_e *MockMappingService_Expecter) BulkCreateMappings(ctx interface{}, mappings interface{}) *MockMappingService_BulkCreateMappings_Call {
	return &MockMappingService_BulkCreateMappings_Call{Call: _e.mock.On("BulkCreateMappings", ctx, mappings)}
}

func (_c *MockMappingService_BulkCreateMappings_Call) Run(run func(ctx context.Context, mappings []*autodoc.CreateMappingRequest)) *MockMappingService_BulkCreateMappings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*autodoc.CreateMappingRequest))
	})
	return _c
}

func (_c *MockMappingService_BulkCreateMappings_Call) Return(_a0 []*model.DocumentMapping, _a1 error) *MockMappingService_BulkCreateMappings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_BulkCreateMappings_Call) RunAndReturn(run func(context.Context, []*autodoc.CreateMappingRequest) ([]*model.DocumentMapping, error)) *MockMappingService_BulkCreateMappings_Call {
	_c.Call.Return(run)
	return _c
}

// BulkDeleteMappings provides a mock function with given fields: ctx, req
func (_m *MockMappingService) BulkDeleteMappings(ctx context.Context, req *autodoc.BulkDeleteMappingsRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for BulkDeleteMappings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.BulkDeleteMappingsRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMappingService_BulkDeleteMappings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkDeleteMappings'
type MockMappingService_BulkDeleteMappings_Call struct {
	*mock.Call
}

// BulkDeleteMappings is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.BulkDeleteMappingsRequest
func (_e *MockMappingService_Expecter) BulkDeleteMappings(ctx interface{}, req interface{}) *MockMappingService_BulkDeleteMappings_Call {
	return &MockMappingService_BulkDeleteMappings_Call{Call: _e.mock.On("BulkDeleteMappings", ctx, req)}
}

func (_c *MockMappingService_BulkDeleteMappings_Call) Run(run func(ctx context.Context, req *autodoc.BulkDeleteMappingsRequest)) *MockMappingService_BulkDeleteMappings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.BulkDeleteMappingsRequest))
	})
	return _c
}

func (_c *MockMappingService_BulkDeleteMappings_Call) Return(_a0 error) *MockMappingService_BulkDeleteMappings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMappingService_BulkDeleteMappings_Call) RunAndReturn(run func(context.Context, *autodoc.BulkDeleteMappingsRequest) error) *MockMappingService_BulkDeleteMappings_Call {
	_c.Call.Return(run)
	return _c
}

// CreateMapping provides a mock function with given fields: ctx, req
func (_m *MockMappingService) CreateMapping(ctx context.Context, req *autodoc.CreateMappingRequest) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateMapping")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateMappingRequest) (*model.DocumentMapping, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CreateMappingRequest) *model.DocumentMapping); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.CreateMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_CreateMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMapping'
type MockMappingService_CreateMapping_Call struct {
	*mock.Call
}

// CreateMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.CreateMappingRequest
func (_e *MockMappingService_Expecter) CreateMapping(ctx interface{}, req interface{}) *MockMappingService_CreateMapping_Call {
	return &MockMappingService_CreateMapping_Call{Call: _e.mock.On("CreateMapping", ctx, req)}
}

func (_c *MockMappingService_CreateMapping_Call) Run(run func(ctx context.Context, req *autodoc.CreateMappingRequest)) *MockMappingService_CreateMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CreateMappingRequest))
	})
	return _c
}

func (_c *MockMappingService_CreateMapping_Call) Return(_a0 *model.DocumentMapping, _a1 error) *MockMappingService_CreateMapping_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_CreateMapping_Call) RunAndReturn(run func(context.Context, *autodoc.CreateMappingRequest) (*model.DocumentMapping, error)) *MockMappingService_CreateMapping_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteMapping provides a mock function with given fields: ctx, objectID, objectType, provider, tenantID
func (_m *MockMappingService) DeleteMapping(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64) error {
	ret := _m.Called(ctx, objectID, objectType, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteMapping")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string, uint64) error); ok {
		r0 = rf(ctx, objectID, objectType, provider, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMappingService_DeleteMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMapping'
type MockMappingService_DeleteMapping_Call struct {
	*mock.Call
}

// DeleteMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - objectID uint64
//   - objectType string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) DeleteMapping(ctx interface{}, objectID interface{}, objectType interface{}, provider interface{}, tenantID interface{}) *MockMappingService_DeleteMapping_Call {
	return &MockMappingService_DeleteMapping_Call{Call: _e.mock.On("DeleteMapping", ctx, objectID, objectType, provider, tenantID)}
}

func (_c *MockMappingService_DeleteMapping_Call) Run(run func(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64)) *MockMappingService_DeleteMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(string), args[4].(uint64))
	})
	return _c
}

func (_c *MockMappingService_DeleteMapping_Call) Return(_a0 error) *MockMappingService_DeleteMapping_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMappingService_DeleteMapping_Call) RunAndReturn(run func(context.Context, uint64, string, string, uint64) error) *MockMappingService_DeleteMapping_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteMappingByExternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingService) DeleteMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) error {
	ret := _m.Called(ctx, externalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteMappingByExternalID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) error); ok {
		r0 = rf(ctx, externalID, provider, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMappingService_DeleteMappingByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMappingByExternalID'
type MockMappingService_DeleteMappingByExternalID_Call struct {
	*mock.Call
}

// DeleteMappingByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) DeleteMappingByExternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingService_DeleteMappingByExternalID_Call {
	return &MockMappingService_DeleteMappingByExternalID_Call{Call: _e.mock.On("DeleteMappingByExternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingService_DeleteMappingByExternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingService_DeleteMappingByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingService_DeleteMappingByExternalID_Call) Return(_a0 error) *MockMappingService_DeleteMappingByExternalID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMappingService_DeleteMappingByExternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64) error) *MockMappingService_DeleteMappingByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetExternalID provides a mock function with given fields: ctx, objectID, objectType, provider, tenantID
func (_m *MockMappingService) GetExternalID(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64) (string, error) {
	ret := _m.Called(ctx, objectID, objectType, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetExternalID")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string, uint64) (string, error)); ok {
		return rf(ctx, objectID, objectType, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string, uint64) string); ok {
		r0 = rf(ctx, objectID, objectType, provider, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, string, uint64) error); ok {
		r1 = rf(ctx, objectID, objectType, provider, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_GetExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExternalID'
type MockMappingService_GetExternalID_Call struct {
	*mock.Call
}

// GetExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - objectID uint64
//   - objectType string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) GetExternalID(ctx interface{}, objectID interface{}, objectType interface{}, provider interface{}, tenantID interface{}) *MockMappingService_GetExternalID_Call {
	return &MockMappingService_GetExternalID_Call{Call: _e.mock.On("GetExternalID", ctx, objectID, objectType, provider, tenantID)}
}

func (_c *MockMappingService_GetExternalID_Call) Run(run func(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64)) *MockMappingService_GetExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(string), args[4].(uint64))
	})
	return _c
}

func (_c *MockMappingService_GetExternalID_Call) Return(_a0 string, _a1 error) *MockMappingService_GetExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_GetExternalID_Call) RunAndReturn(run func(context.Context, uint64, string, string, uint64) (string, error)) *MockMappingService_GetExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetInternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingService) GetInternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (uint64, error) {
	ret := _m.Called(ctx, externalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetInternalID")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) (uint64, error)); ok {
		return rf(ctx, externalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) uint64); ok {
		r0 = rf(ctx, externalID, provider, tenantID)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, uint64) error); ok {
		r1 = rf(ctx, externalID, provider, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_GetInternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInternalID'
type MockMappingService_GetInternalID_Call struct {
	*mock.Call
}

// GetInternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) GetInternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingService_GetInternalID_Call {
	return &MockMappingService_GetInternalID_Call{Call: _e.mock.On("GetInternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingService_GetInternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingService_GetInternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingService_GetInternalID_Call) Return(_a0 uint64, _a1 error) *MockMappingService_GetInternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_GetInternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64) (uint64, error)) *MockMappingService_GetInternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMapping provides a mock function with given fields: ctx, objectID, objectType, provider, tenantID
func (_m *MockMappingService) GetMapping(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, objectID, objectType, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetMapping")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string, uint64) (*model.DocumentMapping, error)); ok {
		return rf(ctx, objectID, objectType, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string, uint64) *model.DocumentMapping); ok {
		r0 = rf(ctx, objectID, objectType, provider, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, string, uint64) error); ok {
		r1 = rf(ctx, objectID, objectType, provider, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_GetMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMapping'
type MockMappingService_GetMapping_Call struct {
	*mock.Call
}

// GetMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - objectID uint64
//   - objectType string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) GetMapping(ctx interface{}, objectID interface{}, objectType interface{}, provider interface{}, tenantID interface{}) *MockMappingService_GetMapping_Call {
	return &MockMappingService_GetMapping_Call{Call: _e.mock.On("GetMapping", ctx, objectID, objectType, provider, tenantID)}
}

func (_c *MockMappingService_GetMapping_Call) Run(run func(ctx context.Context, objectID uint64, objectType string, provider string, tenantID uint64)) *MockMappingService_GetMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(string), args[4].(uint64))
	})
	return _c
}

func (_c *MockMappingService_GetMapping_Call) Return(_a0 *model.DocumentMapping, _a1 error) *MockMappingService_GetMapping_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_GetMapping_Call) RunAndReturn(run func(context.Context, uint64, string, string, uint64) (*model.DocumentMapping, error)) *MockMappingService_GetMapping_Call {
	_c.Call.Return(run)
	return _c
}

// GetMappingByExternalID provides a mock function with given fields: ctx, externalID, provider, tenantID
func (_m *MockMappingService) GetMappingByExternalID(ctx context.Context, externalID string, provider string, tenantID uint64) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, externalID, provider, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetMappingByExternalID")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) (*model.DocumentMapping, error)); ok {
		return rf(ctx, externalID, provider, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64) *model.DocumentMapping); ok {
		r0 = rf(ctx, externalID, provider, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, uint64) error); ok {
		r1 = rf(ctx, externalID, provider, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_GetMappingByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMappingByExternalID'
type MockMappingService_GetMappingByExternalID_Call struct {
	*mock.Call
}

// GetMappingByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - provider string
//   - tenantID uint64
func (_e *MockMappingService_Expecter) GetMappingByExternalID(ctx interface{}, externalID interface{}, provider interface{}, tenantID interface{}) *MockMappingService_GetMappingByExternalID_Call {
	return &MockMappingService_GetMappingByExternalID_Call{Call: _e.mock.On("GetMappingByExternalID", ctx, externalID, provider, tenantID)}
}

func (_c *MockMappingService_GetMappingByExternalID_Call) Run(run func(ctx context.Context, externalID string, provider string, tenantID uint64)) *MockMappingService_GetMappingByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64))
	})
	return _c
}

func (_c *MockMappingService_GetMappingByExternalID_Call) Return(_a0 *model.DocumentMapping, _a1 error) *MockMappingService_GetMappingByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_GetMappingByExternalID_Call) RunAndReturn(run func(context.Context, string, string, uint64) (*model.DocumentMapping, error)) *MockMappingService_GetMappingByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// ListMappings provides a mock function with given fields: ctx, req
func (_m *MockMappingService) ListMappings(ctx context.Context, req *autodoc.ListMappingsRequest) ([]*model.DocumentMapping, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListMappings")
	}

	var r0 []*model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListMappingsRequest) ([]*model.DocumentMapping, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListMappingsRequest) []*model.DocumentMapping); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ListMappingsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_ListMappings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMappings'
type MockMappingService_ListMappings_Call struct {
	*mock.Call
}

// ListMappings is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.ListMappingsRequest
func (_e *MockMappingService_Expecter) ListMappings(ctx interface{}, req interface{}) *MockMappingService_ListMappings_Call {
	return &MockMappingService_ListMappings_Call{Call: _e.mock.On("ListMappings", ctx, req)}
}

func (_c *MockMappingService_ListMappings_Call) Run(run func(ctx context.Context, req *autodoc.ListMappingsRequest)) *MockMappingService_ListMappings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ListMappingsRequest))
	})
	return _c
}

func (_c *MockMappingService_ListMappings_Call) Return(_a0 []*model.DocumentMapping, _a1 error) *MockMappingService_ListMappings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_ListMappings_Call) RunAndReturn(run func(context.Context, *autodoc.ListMappingsRequest) ([]*model.DocumentMapping, error)) *MockMappingService_ListMappings_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMapping provides a mock function with given fields: ctx, req
func (_m *MockMappingService) UpdateMapping(ctx context.Context, req *autodoc.UpdateMappingRequest) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMapping")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateMappingRequest) (*model.DocumentMapping, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.UpdateMappingRequest) *model.DocumentMapping); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.UpdateMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockMappingService_UpdateMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMapping'
type MockMappingService_UpdateMapping_Call struct {
	*mock.Call
}

// UpdateMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.UpdateMappingRequest
func (_e *MockMappingService_Expecter) UpdateMapping(ctx interface{}, req interface{}) *MockMappingService_UpdateMapping_Call {
	return &MockMappingService_UpdateMapping_Call{Call: _e.mock.On("UpdateMapping", ctx, req)}
}

func (_c *MockMappingService_UpdateMapping_Call) Run(run func(ctx context.Context, req *autodoc.UpdateMappingRequest)) *MockMappingService_UpdateMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.UpdateMappingRequest))
	})
	return _c
}

func (_c *MockMappingService_UpdateMapping_Call) Return(_a0 *model.DocumentMapping, _a1 error) *MockMappingService_UpdateMapping_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockMappingService_UpdateMapping_Call) RunAndReturn(run func(context.Context, *autodoc.UpdateMappingRequest) (*model.DocumentMapping, error)) *MockMappingService_UpdateMapping_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateMapping provides a mock function with given fields: ctx, mapping
func (_m *MockMappingService) ValidateMapping(ctx context.Context, mapping *model.DocumentMapping) error {
	ret := _m.Called(ctx, mapping)

	if len(ret) == 0 {
		panic("no return value specified for ValidateMapping")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentMapping) error); ok {
		r0 = rf(ctx, mapping)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockMappingService_ValidateMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateMapping'
type MockMappingService_ValidateMapping_Call struct {
	*mock.Call
}

// ValidateMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - mapping *model.DocumentMapping
func (_e *MockMappingService_Expecter) ValidateMapping(ctx interface{}, mapping interface{}) *MockMappingService_ValidateMapping_Call {
	return &MockMappingService_ValidateMapping_Call{Call: _e.mock.On("ValidateMapping", ctx, mapping)}
}

func (_c *MockMappingService_ValidateMapping_Call) Run(run func(ctx context.Context, mapping *model.DocumentMapping)) *MockMappingService_ValidateMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentMapping))
	})
	return _c
}

func (_c *MockMappingService_ValidateMapping_Call) Return(_a0 error) *MockMappingService_ValidateMapping_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMappingService_ValidateMapping_Call) RunAndReturn(run func(context.Context, *model.DocumentMapping) error) *MockMappingService_ValidateMapping_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockMappingService creates a new instance of MockMappingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMappingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMappingService {
	mock := &MockMappingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
