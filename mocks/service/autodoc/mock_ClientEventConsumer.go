// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockClientEventConsumer is an autogenerated mock type for the ClientEventConsumer type
type MockClientEventConsumer struct {
	mock.Mock
}

type MockClientEventConsumer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClientEventConsumer) EXPECT() *MockClientEventConsumer_Expecter {
	return &MockClientEventConsumer_Expecter{mock: &_m.Mock}
}

// ConsumeClientCreated provides a mock function with given fields: ctx, tenantID, eventData
func (_m *MockClientEventConsumer) ConsumeClientCreated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ConsumeClientCreated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockClientEventConsumer_ConsumeClientCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConsumeClientCreated'
type MockClientEventConsumer_ConsumeClientCreated_Call struct {
	*mock.Call
}

// ConsumeClientCreated is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventData map[string]interface{}
func (_e *MockClientEventConsumer_Expecter) ConsumeClientCreated(ctx interface{}, tenantID interface{}, eventData interface{}) *MockClientEventConsumer_ConsumeClientCreated_Call {
	return &MockClientEventConsumer_ConsumeClientCreated_Call{Call: _e.mock.On("ConsumeClientCreated", ctx, tenantID, eventData)}
}

func (_c *MockClientEventConsumer_ConsumeClientCreated_Call) Run(run func(ctx context.Context, tenantID uint64, eventData map[string]interface{})) *MockClientEventConsumer_ConsumeClientCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockClientEventConsumer_ConsumeClientCreated_Call) Return(_a0 error) *MockClientEventConsumer_ConsumeClientCreated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClientEventConsumer_ConsumeClientCreated_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) error) *MockClientEventConsumer_ConsumeClientCreated_Call {
	_c.Call.Return(run)
	return _c
}

// ConsumeClientUpdated provides a mock function with given fields: ctx, tenantID, eventData
func (_m *MockClientEventConsumer) ConsumeClientUpdated(ctx context.Context, tenantID uint64, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, tenantID, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ConsumeClientUpdated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r0 = rf(ctx, tenantID, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockClientEventConsumer_ConsumeClientUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConsumeClientUpdated'
type MockClientEventConsumer_ConsumeClientUpdated_Call struct {
	*mock.Call
}

// ConsumeClientUpdated is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventData map[string]interface{}
func (_e *MockClientEventConsumer_Expecter) ConsumeClientUpdated(ctx interface{}, tenantID interface{}, eventData interface{}) *MockClientEventConsumer_ConsumeClientUpdated_Call {
	return &MockClientEventConsumer_ConsumeClientUpdated_Call{Call: _e.mock.On("ConsumeClientUpdated", ctx, tenantID, eventData)}
}

func (_c *MockClientEventConsumer_ConsumeClientUpdated_Call) Run(run func(ctx context.Context, tenantID uint64, eventData map[string]interface{})) *MockClientEventConsumer_ConsumeClientUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockClientEventConsumer_ConsumeClientUpdated_Call) Return(_a0 error) *MockClientEventConsumer_ConsumeClientUpdated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockClientEventConsumer_ConsumeClientUpdated_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) error) *MockClientEventConsumer_ConsumeClientUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockClientEventConsumer creates a new instance of MockClientEventConsumer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClientEventConsumer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClientEventConsumer {
	mock := &MockClientEventConsumer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
