// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockRuleExecutionEngine is an autogenerated mock type for the RuleExecutionEngine type
type MockRuleExecutionEngine struct {
	mock.Mock
}

type MockRuleExecutionEngine_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRuleExecutionEngine) EXPECT() *MockRuleExecutionEngine_Expecter {
	return &MockRuleExecutionEngine_Expecter{mock: &_m.Mock}
}

// ExecuteRule provides a mock function with given fields: ctx, ruleID, eventData
func (_m *MockRuleExecutionEngine) ExecuteRule(ctx context.Context, ruleID uint64, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, ruleID, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r0 = rf(ctx, ruleID, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuleExecutionEngine_ExecuteRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExecuteRule'
type MockRuleExecutionEngine_ExecuteRule_Call struct {
	*mock.Call
}

// ExecuteRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
//   - eventData map[string]interface{}
func (_e *MockRuleExecutionEngine_Expecter) ExecuteRule(ctx interface{}, ruleID interface{}, eventData interface{}) *MockRuleExecutionEngine_ExecuteRule_Call {
	return &MockRuleExecutionEngine_ExecuteRule_Call{Call: _e.mock.On("ExecuteRule", ctx, ruleID, eventData)}
}

func (_c *MockRuleExecutionEngine_ExecuteRule_Call) Run(run func(ctx context.Context, ruleID uint64, eventData map[string]interface{})) *MockRuleExecutionEngine_ExecuteRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleExecutionEngine_ExecuteRule_Call) Return(_a0 error) *MockRuleExecutionEngine_ExecuteRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuleExecutionEngine_ExecuteRule_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) error) *MockRuleExecutionEngine_ExecuteRule_Call {
	_c.Call.Return(run)
	return _c
}

// ExtractPlaceholderData provides a mock function with given fields: ctx, eventData
func (_m *MockRuleExecutionEngine) ExtractPlaceholderData(ctx context.Context, eventData map[string]interface{}) map[string]interface{} {
	ret := _m.Called(ctx, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ExtractPlaceholderData")
	}

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}) map[string]interface{}); ok {
		r0 = rf(ctx, eventData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	return r0
}

// MockRuleExecutionEngine_ExtractPlaceholderData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExtractPlaceholderData'
type MockRuleExecutionEngine_ExtractPlaceholderData_Call struct {
	*mock.Call
}

// ExtractPlaceholderData is a helper method to define mock.On call
//   - ctx context.Context
//   - eventData map[string]interface{}
func (_e *MockRuleExecutionEngine_Expecter) ExtractPlaceholderData(ctx interface{}, eventData interface{}) *MockRuleExecutionEngine_ExtractPlaceholderData_Call {
	return &MockRuleExecutionEngine_ExtractPlaceholderData_Call{Call: _e.mock.On("ExtractPlaceholderData", ctx, eventData)}
}

func (_c *MockRuleExecutionEngine_ExtractPlaceholderData_Call) Run(run func(ctx context.Context, eventData map[string]interface{})) *MockRuleExecutionEngine_ExtractPlaceholderData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleExecutionEngine_ExtractPlaceholderData_Call) Return(_a0 map[string]interface{}) *MockRuleExecutionEngine_ExtractPlaceholderData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuleExecutionEngine_ExtractPlaceholderData_Call) RunAndReturn(run func(context.Context, map[string]interface{}) map[string]interface{}) *MockRuleExecutionEngine_ExtractPlaceholderData_Call {
	_c.Call.Return(run)
	return _c
}

// ReplacePlaceholders provides a mock function with given fields: ctx, template, placeholderData
func (_m *MockRuleExecutionEngine) ReplacePlaceholders(ctx context.Context, template string, placeholderData map[string]interface{}) string {
	ret := _m.Called(ctx, template, placeholderData)

	if len(ret) == 0 {
		panic("no return value specified for ReplacePlaceholders")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context, string, map[string]interface{}) string); ok {
		r0 = rf(ctx, template, placeholderData)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockRuleExecutionEngine_ReplacePlaceholders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReplacePlaceholders'
type MockRuleExecutionEngine_ReplacePlaceholders_Call struct {
	*mock.Call
}

// ReplacePlaceholders is a helper method to define mock.On call
//   - ctx context.Context
//   - template string
//   - placeholderData map[string]interface{}
func (_e *MockRuleExecutionEngine_Expecter) ReplacePlaceholders(ctx interface{}, template interface{}, placeholderData interface{}) *MockRuleExecutionEngine_ReplacePlaceholders_Call {
	return &MockRuleExecutionEngine_ReplacePlaceholders_Call{Call: _e.mock.On("ReplacePlaceholders", ctx, template, placeholderData)}
}

func (_c *MockRuleExecutionEngine_ReplacePlaceholders_Call) Run(run func(ctx context.Context, template string, placeholderData map[string]interface{})) *MockRuleExecutionEngine_ReplacePlaceholders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleExecutionEngine_ReplacePlaceholders_Call) Return(_a0 string) *MockRuleExecutionEngine_ReplacePlaceholders_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuleExecutionEngine_ReplacePlaceholders_Call) RunAndReturn(run func(context.Context, string, map[string]interface{}) string) *MockRuleExecutionEngine_ReplacePlaceholders_Call {
	_c.Call.Return(run)
	return _c
}

// TestRule provides a mock function with given fields: ctx, ruleID, sampleData
func (_m *MockRuleExecutionEngine) TestRule(ctx context.Context, ruleID uint64, sampleData map[string]interface{}) (*autodoc.TestResult, error) {
	ret := _m.Called(ctx, ruleID, sampleData)

	if len(ret) == 0 {
		panic("no return value specified for TestRule")
	}

	var r0 *autodoc.TestResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) (*autodoc.TestResult, error)); ok {
		return rf(ctx, ruleID, sampleData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}) *autodoc.TestResult); ok {
		r0 = rf(ctx, ruleID, sampleData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.TestResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, map[string]interface{}) error); ok {
		r1 = rf(ctx, ruleID, sampleData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuleExecutionEngine_TestRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TestRule'
type MockRuleExecutionEngine_TestRule_Call struct {
	*mock.Call
}

// TestRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
//   - sampleData map[string]interface{}
func (_e *MockRuleExecutionEngine_Expecter) TestRule(ctx interface{}, ruleID interface{}, sampleData interface{}) *MockRuleExecutionEngine_TestRule_Call {
	return &MockRuleExecutionEngine_TestRule_Call{Call: _e.mock.On("TestRule", ctx, ruleID, sampleData)}
}

func (_c *MockRuleExecutionEngine_TestRule_Call) Run(run func(ctx context.Context, ruleID uint64, sampleData map[string]interface{})) *MockRuleExecutionEngine_TestRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockRuleExecutionEngine_TestRule_Call) Return(_a0 *autodoc.TestResult, _a1 error) *MockRuleExecutionEngine_TestRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuleExecutionEngine_TestRule_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}) (*autodoc.TestResult, error)) *MockRuleExecutionEngine_TestRule_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRuleExecutionEngine creates a new instance of MockRuleExecutionEngine. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRuleExecutionEngine(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRuleExecutionEngine {
	mock := &MockRuleExecutionEngine{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
