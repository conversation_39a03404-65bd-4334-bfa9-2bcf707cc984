// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockValidationService is an autogenerated mock type for the ValidationService type
type MockValidationService struct {
	mock.Mock
}

type MockValidationService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockValidationService) EXPECT() *MockValidationService_Expecter {
	return &MockValidationService_Expecter{mock: &_m.Mock}
}

// SanitizeEventData provides a mock function with given fields: ctx, eventData
func (_m *MockValidationService) SanitizeEventData(ctx context.Context, eventData map[string]interface{}) map[string]interface{} {
	ret := _m.Called(ctx, eventData)

	if len(ret) == 0 {
		panic("no return value specified for SanitizeEventData")
	}

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}) map[string]interface{}); ok {
		r0 = rf(ctx, eventData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	return r0
}

// MockValidationService_SanitizeEventData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SanitizeEventData'
type MockValidationService_SanitizeEventData_Call struct {
	*mock.Call
}

// SanitizeEventData is a helper method to define mock.On call
//   - ctx context.Context
//   - eventData map[string]interface{}
func (_e *MockValidationService_Expecter) SanitizeEventData(ctx interface{}, eventData interface{}) *MockValidationService_SanitizeEventData_Call {
	return &MockValidationService_SanitizeEventData_Call{Call: _e.mock.On("SanitizeEventData", ctx, eventData)}
}

func (_c *MockValidationService_SanitizeEventData_Call) Run(run func(ctx context.Context, eventData map[string]interface{})) *MockValidationService_SanitizeEventData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[string]interface{}))
	})
	return _c
}

func (_c *MockValidationService_SanitizeEventData_Call) Return(_a0 map[string]interface{}) *MockValidationService_SanitizeEventData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_SanitizeEventData_Call) RunAndReturn(run func(context.Context, map[string]interface{}) map[string]interface{}) *MockValidationService_SanitizeEventData_Call {
	_c.Call.Return(run)
	return _c
}

// SanitizeFilePath provides a mock function with given fields: ctx, filePath
func (_m *MockValidationService) SanitizeFilePath(ctx context.Context, filePath string) (string, error) {
	ret := _m.Called(ctx, filePath)

	if len(ret) == 0 {
		panic("no return value specified for SanitizeFilePath")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, filePath)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, filePath)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, filePath)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockValidationService_SanitizeFilePath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SanitizeFilePath'
type MockValidationService_SanitizeFilePath_Call struct {
	*mock.Call
}

// SanitizeFilePath is a helper method to define mock.On call
//   - ctx context.Context
//   - filePath string
func (_e *MockValidationService_Expecter) SanitizeFilePath(ctx interface{}, filePath interface{}) *MockValidationService_SanitizeFilePath_Call {
	return &MockValidationService_SanitizeFilePath_Call{Call: _e.mock.On("SanitizeFilePath", ctx, filePath)}
}

func (_c *MockValidationService_SanitizeFilePath_Call) Run(run func(ctx context.Context, filePath string)) *MockValidationService_SanitizeFilePath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockValidationService_SanitizeFilePath_Call) Return(_a0 string, _a1 error) *MockValidationService_SanitizeFilePath_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockValidationService_SanitizeFilePath_Call) RunAndReturn(run func(context.Context, string) (string, error)) *MockValidationService_SanitizeFilePath_Call {
	_c.Call.Return(run)
	return _c
}

// SanitizePlaceholderData provides a mock function with given fields: ctx, data
func (_m *MockValidationService) SanitizePlaceholderData(ctx context.Context, data map[string]interface{}) map[string]interface{} {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for SanitizePlaceholderData")
	}

	var r0 map[string]interface{}
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}) map[string]interface{}); ok {
		r0 = rf(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	return r0
}

// MockValidationService_SanitizePlaceholderData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SanitizePlaceholderData'
type MockValidationService_SanitizePlaceholderData_Call struct {
	*mock.Call
}

// SanitizePlaceholderData is a helper method to define mock.On call
//   - ctx context.Context
//   - data map[string]interface{}
func (_e *MockValidationService_Expecter) SanitizePlaceholderData(ctx interface{}, data interface{}) *MockValidationService_SanitizePlaceholderData_Call {
	return &MockValidationService_SanitizePlaceholderData_Call{Call: _e.mock.On("SanitizePlaceholderData", ctx, data)}
}

func (_c *MockValidationService_SanitizePlaceholderData_Call) Run(run func(ctx context.Context, data map[string]interface{})) *MockValidationService_SanitizePlaceholderData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[string]interface{}))
	})
	return _c
}

func (_c *MockValidationService_SanitizePlaceholderData_Call) Return(_a0 map[string]interface{}) *MockValidationService_SanitizePlaceholderData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_SanitizePlaceholderData_Call) RunAndReturn(run func(context.Context, map[string]interface{}) map[string]interface{}) *MockValidationService_SanitizePlaceholderData_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateEventData provides a mock function with given fields: ctx, eventType, eventData
func (_m *MockValidationService) ValidateEventData(ctx context.Context, eventType string, eventData map[string]interface{}) error {
	ret := _m.Called(ctx, eventType, eventData)

	if len(ret) == 0 {
		panic("no return value specified for ValidateEventData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, map[string]interface{}) error); ok {
		r0 = rf(ctx, eventType, eventData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockValidationService_ValidateEventData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateEventData'
type MockValidationService_ValidateEventData_Call struct {
	*mock.Call
}

// ValidateEventData is a helper method to define mock.On call
//   - ctx context.Context
//   - eventType string
//   - eventData map[string]interface{}
func (_e *MockValidationService_Expecter) ValidateEventData(ctx interface{}, eventType interface{}, eventData interface{}) *MockValidationService_ValidateEventData_Call {
	return &MockValidationService_ValidateEventData_Call{Call: _e.mock.On("ValidateEventData", ctx, eventType, eventData)}
}

func (_c *MockValidationService_ValidateEventData_Call) Run(run func(ctx context.Context, eventType string, eventData map[string]interface{})) *MockValidationService_ValidateEventData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockValidationService_ValidateEventData_Call) Return(_a0 error) *MockValidationService_ValidateEventData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_ValidateEventData_Call) RunAndReturn(run func(context.Context, string, map[string]interface{}) error) *MockValidationService_ValidateEventData_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateRule provides a mock function with given fields: ctx, rule
func (_m *MockValidationService) ValidateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	ret := _m.Called(ctx, rule)

	if len(ret) == 0 {
		panic("no return value specified for ValidateRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule) error); ok {
		r0 = rf(ctx, rule)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockValidationService_ValidateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateRule'
type MockValidationService_ValidateRule_Call struct {
	*mock.Call
}

// ValidateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
func (_e *MockValidationService_Expecter) ValidateRule(ctx interface{}, rule interface{}) *MockValidationService_ValidateRule_Call {
	return &MockValidationService_ValidateRule_Call{Call: _e.mock.On("ValidateRule", ctx, rule)}
}

func (_c *MockValidationService_ValidateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule)) *MockValidationService_ValidateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule))
	})
	return _c
}

func (_c *MockValidationService_ValidateRule_Call) Return(_a0 error) *MockValidationService_ValidateRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_ValidateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule) error) *MockValidationService_ValidateRule_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateRuleActions provides a mock function with given fields: ctx, actions
func (_m *MockValidationService) ValidateRuleActions(ctx context.Context, actions model.RuleConfigArray) error {
	ret := _m.Called(ctx, actions)

	if len(ret) == 0 {
		panic("no return value specified for ValidateRuleActions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, model.RuleConfigArray) error); ok {
		r0 = rf(ctx, actions)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockValidationService_ValidateRuleActions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateRuleActions'
type MockValidationService_ValidateRuleActions_Call struct {
	*mock.Call
}

// ValidateRuleActions is a helper method to define mock.On call
//   - ctx context.Context
//   - actions model.RuleConfigArray
func (_e *MockValidationService_Expecter) ValidateRuleActions(ctx interface{}, actions interface{}) *MockValidationService_ValidateRuleActions_Call {
	return &MockValidationService_ValidateRuleActions_Call{Call: _e.mock.On("ValidateRuleActions", ctx, actions)}
}

func (_c *MockValidationService_ValidateRuleActions_Call) Run(run func(ctx context.Context, actions model.RuleConfigArray)) *MockValidationService_ValidateRuleActions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(model.RuleConfigArray))
	})
	return _c
}

func (_c *MockValidationService_ValidateRuleActions_Call) Return(_a0 error) *MockValidationService_ValidateRuleActions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_ValidateRuleActions_Call) RunAndReturn(run func(context.Context, model.RuleConfigArray) error) *MockValidationService_ValidateRuleActions_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateTriggerRules provides a mock function with given fields: ctx, triggerRules
func (_m *MockValidationService) ValidateTriggerRules(ctx context.Context, triggerRules model.TriggerRulesMap) error {
	ret := _m.Called(ctx, triggerRules)

	if len(ret) == 0 {
		panic("no return value specified for ValidateTriggerRules")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, model.TriggerRulesMap) error); ok {
		r0 = rf(ctx, triggerRules)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockValidationService_ValidateTriggerRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateTriggerRules'
type MockValidationService_ValidateTriggerRules_Call struct {
	*mock.Call
}

// ValidateTriggerRules is a helper method to define mock.On call
//   - ctx context.Context
//   - triggerRules model.TriggerRulesMap
func (_e *MockValidationService_Expecter) ValidateTriggerRules(ctx interface{}, triggerRules interface{}) *MockValidationService_ValidateTriggerRules_Call {
	return &MockValidationService_ValidateTriggerRules_Call{Call: _e.mock.On("ValidateTriggerRules", ctx, triggerRules)}
}

func (_c *MockValidationService_ValidateTriggerRules_Call) Run(run func(ctx context.Context, triggerRules model.TriggerRulesMap)) *MockValidationService_ValidateTriggerRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(model.TriggerRulesMap))
	})
	return _c
}

func (_c *MockValidationService_ValidateTriggerRules_Call) Return(_a0 error) *MockValidationService_ValidateTriggerRules_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockValidationService_ValidateTriggerRules_Call) RunAndReturn(run func(context.Context, model.TriggerRulesMap) error) *MockValidationService_ValidateTriggerRules_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockValidationService creates a new instance of MockValidationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockValidationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockValidationService {
	mock := &MockValidationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
