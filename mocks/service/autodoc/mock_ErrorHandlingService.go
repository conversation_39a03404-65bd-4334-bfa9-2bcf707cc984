// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockErrorHandlingService is an autogenerated mock type for the ErrorHandlingService type
type MockErrorHandlingService struct {
	mock.Mock
}

type MockErrorHandlingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockErrorHandlingService) EXPECT() *MockErrorHandlingService_Expecter {
	return &MockErrorHandlingService_Expecter{mock: &_m.Mock}
}

// HandleEventProcessingError provides a mock function with given fields: ctx, tenantID, eventType, eventData, err
func (_m *MockErrorHandlingService) HandleEventProcessingError(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}, err error) error {
	ret := _m.Called(ctx, tenantID, eventType, eventData, err)

	if len(ret) == 0 {
		panic("no return value specified for HandleEventProcessingError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}, error) error); ok {
		r0 = rf(ctx, tenantID, eventType, eventData, err)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockErrorHandlingService_HandleEventProcessingError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleEventProcessingError'
type MockErrorHandlingService_HandleEventProcessingError_Call struct {
	*mock.Call
}

// HandleEventProcessingError is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - eventType string
//   - eventData map[string]interface{}
//   - err error
func (_e *MockErrorHandlingService_Expecter) HandleEventProcessingError(ctx interface{}, tenantID interface{}, eventType interface{}, eventData interface{}, err interface{}) *MockErrorHandlingService_HandleEventProcessingError_Call {
	return &MockErrorHandlingService_HandleEventProcessingError_Call{Call: _e.mock.On("HandleEventProcessingError", ctx, tenantID, eventType, eventData, err)}
}

func (_c *MockErrorHandlingService_HandleEventProcessingError_Call) Run(run func(ctx context.Context, tenantID uint64, eventType string, eventData map[string]interface{}, err error)) *MockErrorHandlingService_HandleEventProcessingError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(map[string]interface{}), args[4].(error))
	})
	return _c
}

func (_c *MockErrorHandlingService_HandleEventProcessingError_Call) Return(_a0 error) *MockErrorHandlingService_HandleEventProcessingError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockErrorHandlingService_HandleEventProcessingError_Call) RunAndReturn(run func(context.Context, uint64, string, map[string]interface{}, error) error) *MockErrorHandlingService_HandleEventProcessingError_Call {
	_c.Call.Return(run)
	return _c
}

// HandleRuleExecutionError provides a mock function with given fields: ctx, ruleID, eventData, err
func (_m *MockErrorHandlingService) HandleRuleExecutionError(ctx context.Context, ruleID uint64, eventData map[string]interface{}, err error) error {
	ret := _m.Called(ctx, ruleID, eventData, err)

	if len(ret) == 0 {
		panic("no return value specified for HandleRuleExecutionError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}, error) error); ok {
		r0 = rf(ctx, ruleID, eventData, err)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockErrorHandlingService_HandleRuleExecutionError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleRuleExecutionError'
type MockErrorHandlingService_HandleRuleExecutionError_Call struct {
	*mock.Call
}

// HandleRuleExecutionError is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
//   - eventData map[string]interface{}
//   - err error
func (_e *MockErrorHandlingService_Expecter) HandleRuleExecutionError(ctx interface{}, ruleID interface{}, eventData interface{}, err interface{}) *MockErrorHandlingService_HandleRuleExecutionError_Call {
	return &MockErrorHandlingService_HandleRuleExecutionError_Call{Call: _e.mock.On("HandleRuleExecutionError", ctx, ruleID, eventData, err)}
}

func (_c *MockErrorHandlingService_HandleRuleExecutionError_Call) Run(run func(ctx context.Context, ruleID uint64, eventData map[string]interface{}, err error)) *MockErrorHandlingService_HandleRuleExecutionError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}), args[3].(error))
	})
	return _c
}

func (_c *MockErrorHandlingService_HandleRuleExecutionError_Call) Return(_a0 error) *MockErrorHandlingService_HandleRuleExecutionError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockErrorHandlingService_HandleRuleExecutionError_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}, error) error) *MockErrorHandlingService_HandleRuleExecutionError_Call {
	_c.Call.Return(run)
	return _c
}

// LogExecutionError provides a mock function with given fields: ctx, operation, details, err
func (_m *MockErrorHandlingService) LogExecutionError(ctx context.Context, operation string, details map[string]interface{}, err error) {
	_m.Called(ctx, operation, details, err)
}

// MockErrorHandlingService_LogExecutionError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LogExecutionError'
type MockErrorHandlingService_LogExecutionError_Call struct {
	*mock.Call
}

// LogExecutionError is a helper method to define mock.On call
//   - ctx context.Context
//   - operation string
//   - details map[string]interface{}
//   - err error
func (_e *MockErrorHandlingService_Expecter) LogExecutionError(ctx interface{}, operation interface{}, details interface{}, err interface{}) *MockErrorHandlingService_LogExecutionError_Call {
	return &MockErrorHandlingService_LogExecutionError_Call{Call: _e.mock.On("LogExecutionError", ctx, operation, details, err)}
}

func (_c *MockErrorHandlingService_LogExecutionError_Call) Run(run func(ctx context.Context, operation string, details map[string]interface{}, err error)) *MockErrorHandlingService_LogExecutionError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(map[string]interface{}), args[3].(error))
	})
	return _c
}

func (_c *MockErrorHandlingService_LogExecutionError_Call) Return() *MockErrorHandlingService_LogExecutionError_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockErrorHandlingService_LogExecutionError_Call) RunAndReturn(run func(context.Context, string, map[string]interface{}, error)) *MockErrorHandlingService_LogExecutionError_Call {
	_c.Run(run)
	return _c
}

// RecoverFromPanic provides a mock function with given fields: ctx, operation, details
func (_m *MockErrorHandlingService) RecoverFromPanic(ctx context.Context, operation string, details map[string]interface{}) error {
	ret := _m.Called(ctx, operation, details)

	if len(ret) == 0 {
		panic("no return value specified for RecoverFromPanic")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, map[string]interface{}) error); ok {
		r0 = rf(ctx, operation, details)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockErrorHandlingService_RecoverFromPanic_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecoverFromPanic'
type MockErrorHandlingService_RecoverFromPanic_Call struct {
	*mock.Call
}

// RecoverFromPanic is a helper method to define mock.On call
//   - ctx context.Context
//   - operation string
//   - details map[string]interface{}
func (_e *MockErrorHandlingService_Expecter) RecoverFromPanic(ctx interface{}, operation interface{}, details interface{}) *MockErrorHandlingService_RecoverFromPanic_Call {
	return &MockErrorHandlingService_RecoverFromPanic_Call{Call: _e.mock.On("RecoverFromPanic", ctx, operation, details)}
}

func (_c *MockErrorHandlingService_RecoverFromPanic_Call) Run(run func(ctx context.Context, operation string, details map[string]interface{})) *MockErrorHandlingService_RecoverFromPanic_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockErrorHandlingService_RecoverFromPanic_Call) Return(_a0 error) *MockErrorHandlingService_RecoverFromPanic_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockErrorHandlingService_RecoverFromPanic_Call) RunAndReturn(run func(context.Context, string, map[string]interface{}) error) *MockErrorHandlingService_RecoverFromPanic_Call {
	_c.Call.Return(run)
	return _c
}

// RetryRuleExecution provides a mock function with given fields: ctx, ruleID, eventData, maxRetries
func (_m *MockErrorHandlingService) RetryRuleExecution(ctx context.Context, ruleID uint64, eventData map[string]interface{}, maxRetries int) error {
	ret := _m.Called(ctx, ruleID, eventData, maxRetries)

	if len(ret) == 0 {
		panic("no return value specified for RetryRuleExecution")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, map[string]interface{}, int) error); ok {
		r0 = rf(ctx, ruleID, eventData, maxRetries)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockErrorHandlingService_RetryRuleExecution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetryRuleExecution'
type MockErrorHandlingService_RetryRuleExecution_Call struct {
	*mock.Call
}

// RetryRuleExecution is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
//   - eventData map[string]interface{}
//   - maxRetries int
func (_e *MockErrorHandlingService_Expecter) RetryRuleExecution(ctx interface{}, ruleID interface{}, eventData interface{}, maxRetries interface{}) *MockErrorHandlingService_RetryRuleExecution_Call {
	return &MockErrorHandlingService_RetryRuleExecution_Call{Call: _e.mock.On("RetryRuleExecution", ctx, ruleID, eventData, maxRetries)}
}

func (_c *MockErrorHandlingService_RetryRuleExecution_Call) Run(run func(ctx context.Context, ruleID uint64, eventData map[string]interface{}, maxRetries int)) *MockErrorHandlingService_RetryRuleExecution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(map[string]interface{}), args[3].(int))
	})
	return _c
}

func (_c *MockErrorHandlingService_RetryRuleExecution_Call) Return(_a0 error) *MockErrorHandlingService_RetryRuleExecution_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockErrorHandlingService_RetryRuleExecution_Call) RunAndReturn(run func(context.Context, uint64, map[string]interface{}, int) error) *MockErrorHandlingService_RetryRuleExecution_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockErrorHandlingService creates a new instance of MockErrorHandlingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockErrorHandlingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockErrorHandlingService {
	mock := &MockErrorHandlingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
