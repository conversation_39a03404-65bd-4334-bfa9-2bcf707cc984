// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"

	mock "github.com/stretchr/testify/mock"
)

// MockDocumentServiceRegistry is an autogenerated mock type for the DocumentServiceRegistry type
type MockDocumentServiceRegistry struct {
	mock.Mock
}

type MockDocumentServiceRegistry_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentServiceRegistry) EXPECT() *MockDocumentServiceRegistry_Expecter {
	return &MockDocumentServiceRegistry_Expecter{mock: &_m.Mock}
}

// GetDefaultProvider provides a mock function with no fields
func (_m *MockDocumentServiceRegistry) GetDefaultProvider() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultProvider")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockDocumentServiceRegistry_GetDefaultProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultProvider'
type MockDocumentServiceRegistry_GetDefaultProvider_Call struct {
	*mock.Call
}

// GetDefaultProvider is a helper method to define mock.On call
func (_e *MockDocumentServiceRegistry_Expecter) GetDefaultProvider() *MockDocumentServiceRegistry_GetDefaultProvider_Call {
	return &MockDocumentServiceRegistry_GetDefaultProvider_Call{Call: _e.mock.On("GetDefaultProvider")}
}

func (_c *MockDocumentServiceRegistry_GetDefaultProvider_Call) Run(run func()) *MockDocumentServiceRegistry_GetDefaultProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_GetDefaultProvider_Call) Return(_a0 string) *MockDocumentServiceRegistry_GetDefaultProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_GetDefaultProvider_Call) RunAndReturn(run func() string) *MockDocumentServiceRegistry_GetDefaultProvider_Call {
	_c.Call.Return(run)
	return _c
}

// GetProvider provides a mock function with given fields: provider
func (_m *MockDocumentServiceRegistry) GetProvider(provider string) (autodoc.DocumentService, error) {
	ret := _m.Called(provider)

	if len(ret) == 0 {
		panic("no return value specified for GetProvider")
	}

	var r0 autodoc.DocumentService
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (autodoc.DocumentService, error)); ok {
		return rf(provider)
	}
	if rf, ok := ret.Get(0).(func(string) autodoc.DocumentService); ok {
		r0 = rf(provider)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.DocumentService)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(provider)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentServiceRegistry_GetProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProvider'
type MockDocumentServiceRegistry_GetProvider_Call struct {
	*mock.Call
}

// GetProvider is a helper method to define mock.On call
//   - provider string
func (_e *MockDocumentServiceRegistry_Expecter) GetProvider(provider interface{}) *MockDocumentServiceRegistry_GetProvider_Call {
	return &MockDocumentServiceRegistry_GetProvider_Call{Call: _e.mock.On("GetProvider", provider)}
}

func (_c *MockDocumentServiceRegistry_GetProvider_Call) Run(run func(provider string)) *MockDocumentServiceRegistry_GetProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_GetProvider_Call) Return(_a0 autodoc.DocumentService, _a1 error) *MockDocumentServiceRegistry_GetProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentServiceRegistry_GetProvider_Call) RunAndReturn(run func(string) (autodoc.DocumentService, error)) *MockDocumentServiceRegistry_GetProvider_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderWithFallback provides a mock function with given fields: provider
func (_m *MockDocumentServiceRegistry) GetProviderWithFallback(provider string) (autodoc.DocumentService, string, error) {
	ret := _m.Called(provider)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderWithFallback")
	}

	var r0 autodoc.DocumentService
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(string) (autodoc.DocumentService, string, error)); ok {
		return rf(provider)
	}
	if rf, ok := ret.Get(0).(func(string) autodoc.DocumentService); ok {
		r0 = rf(provider)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(autodoc.DocumentService)
		}
	}

	if rf, ok := ret.Get(1).(func(string) string); ok {
		r1 = rf(provider)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(string) error); ok {
		r2 = rf(provider)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockDocumentServiceRegistry_GetProviderWithFallback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderWithFallback'
type MockDocumentServiceRegistry_GetProviderWithFallback_Call struct {
	*mock.Call
}

// GetProviderWithFallback is a helper method to define mock.On call
//   - provider string
func (_e *MockDocumentServiceRegistry_Expecter) GetProviderWithFallback(provider interface{}) *MockDocumentServiceRegistry_GetProviderWithFallback_Call {
	return &MockDocumentServiceRegistry_GetProviderWithFallback_Call{Call: _e.mock.On("GetProviderWithFallback", provider)}
}

func (_c *MockDocumentServiceRegistry_GetProviderWithFallback_Call) Run(run func(provider string)) *MockDocumentServiceRegistry_GetProviderWithFallback_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_GetProviderWithFallback_Call) Return(_a0 autodoc.DocumentService, _a1 string, _a2 error) *MockDocumentServiceRegistry_GetProviderWithFallback_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockDocumentServiceRegistry_GetProviderWithFallback_Call) RunAndReturn(run func(string) (autodoc.DocumentService, string, error)) *MockDocumentServiceRegistry_GetProviderWithFallback_Call {
	_c.Call.Return(run)
	return _c
}

// IsProviderRegistered provides a mock function with given fields: provider
func (_m *MockDocumentServiceRegistry) IsProviderRegistered(provider string) bool {
	ret := _m.Called(provider)

	if len(ret) == 0 {
		panic("no return value specified for IsProviderRegistered")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(provider)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockDocumentServiceRegistry_IsProviderRegistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsProviderRegistered'
type MockDocumentServiceRegistry_IsProviderRegistered_Call struct {
	*mock.Call
}

// IsProviderRegistered is a helper method to define mock.On call
//   - provider string
func (_e *MockDocumentServiceRegistry_Expecter) IsProviderRegistered(provider interface{}) *MockDocumentServiceRegistry_IsProviderRegistered_Call {
	return &MockDocumentServiceRegistry_IsProviderRegistered_Call{Call: _e.mock.On("IsProviderRegistered", provider)}
}

func (_c *MockDocumentServiceRegistry_IsProviderRegistered_Call) Run(run func(provider string)) *MockDocumentServiceRegistry_IsProviderRegistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_IsProviderRegistered_Call) Return(_a0 bool) *MockDocumentServiceRegistry_IsProviderRegistered_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_IsProviderRegistered_Call) RunAndReturn(run func(string) bool) *MockDocumentServiceRegistry_IsProviderRegistered_Call {
	_c.Call.Return(run)
	return _c
}

// ListProviders provides a mock function with no fields
func (_m *MockDocumentServiceRegistry) ListProviders() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListProviders")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockDocumentServiceRegistry_ListProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListProviders'
type MockDocumentServiceRegistry_ListProviders_Call struct {
	*mock.Call
}

// ListProviders is a helper method to define mock.On call
func (_e *MockDocumentServiceRegistry_Expecter) ListProviders() *MockDocumentServiceRegistry_ListProviders_Call {
	return &MockDocumentServiceRegistry_ListProviders_Call{Call: _e.mock.On("ListProviders")}
}

func (_c *MockDocumentServiceRegistry_ListProviders_Call) Run(run func()) *MockDocumentServiceRegistry_ListProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_ListProviders_Call) Return(_a0 []string) *MockDocumentServiceRegistry_ListProviders_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_ListProviders_Call) RunAndReturn(run func() []string) *MockDocumentServiceRegistry_ListProviders_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterProvider provides a mock function with given fields: provider, service
func (_m *MockDocumentServiceRegistry) RegisterProvider(provider string, service autodoc.DocumentService) error {
	ret := _m.Called(provider, service)

	if len(ret) == 0 {
		panic("no return value specified for RegisterProvider")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, autodoc.DocumentService) error); ok {
		r0 = rf(provider, service)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentServiceRegistry_RegisterProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterProvider'
type MockDocumentServiceRegistry_RegisterProvider_Call struct {
	*mock.Call
}

// RegisterProvider is a helper method to define mock.On call
//   - provider string
//   - service autodoc.DocumentService
func (_e *MockDocumentServiceRegistry_Expecter) RegisterProvider(provider interface{}, service interface{}) *MockDocumentServiceRegistry_RegisterProvider_Call {
	return &MockDocumentServiceRegistry_RegisterProvider_Call{Call: _e.mock.On("RegisterProvider", provider, service)}
}

func (_c *MockDocumentServiceRegistry_RegisterProvider_Call) Run(run func(provider string, service autodoc.DocumentService)) *MockDocumentServiceRegistry_RegisterProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(autodoc.DocumentService))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_RegisterProvider_Call) Return(_a0 error) *MockDocumentServiceRegistry_RegisterProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_RegisterProvider_Call) RunAndReturn(run func(string, autodoc.DocumentService) error) *MockDocumentServiceRegistry_RegisterProvider_Call {
	_c.Call.Return(run)
	return _c
}

// SetDefaultProvider provides a mock function with given fields: provider
func (_m *MockDocumentServiceRegistry) SetDefaultProvider(provider string) error {
	ret := _m.Called(provider)

	if len(ret) == 0 {
		panic("no return value specified for SetDefaultProvider")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(provider)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentServiceRegistry_SetDefaultProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetDefaultProvider'
type MockDocumentServiceRegistry_SetDefaultProvider_Call struct {
	*mock.Call
}

// SetDefaultProvider is a helper method to define mock.On call
//   - provider string
func (_e *MockDocumentServiceRegistry_Expecter) SetDefaultProvider(provider interface{}) *MockDocumentServiceRegistry_SetDefaultProvider_Call {
	return &MockDocumentServiceRegistry_SetDefaultProvider_Call{Call: _e.mock.On("SetDefaultProvider", provider)}
}

func (_c *MockDocumentServiceRegistry_SetDefaultProvider_Call) Run(run func(provider string)) *MockDocumentServiceRegistry_SetDefaultProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_SetDefaultProvider_Call) Return(_a0 error) *MockDocumentServiceRegistry_SetDefaultProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_SetDefaultProvider_Call) RunAndReturn(run func(string) error) *MockDocumentServiceRegistry_SetDefaultProvider_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterProvider provides a mock function with given fields: provider
func (_m *MockDocumentServiceRegistry) UnregisterProvider(provider string) error {
	ret := _m.Called(provider)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterProvider")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(provider)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentServiceRegistry_UnregisterProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterProvider'
type MockDocumentServiceRegistry_UnregisterProvider_Call struct {
	*mock.Call
}

// UnregisterProvider is a helper method to define mock.On call
//   - provider string
func (_e *MockDocumentServiceRegistry_Expecter) UnregisterProvider(provider interface{}) *MockDocumentServiceRegistry_UnregisterProvider_Call {
	return &MockDocumentServiceRegistry_UnregisterProvider_Call{Call: _e.mock.On("UnregisterProvider", provider)}
}

func (_c *MockDocumentServiceRegistry_UnregisterProvider_Call) Run(run func(provider string)) *MockDocumentServiceRegistry_UnregisterProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDocumentServiceRegistry_UnregisterProvider_Call) Return(_a0 error) *MockDocumentServiceRegistry_UnregisterProvider_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentServiceRegistry_UnregisterProvider_Call) RunAndReturn(run func(string) error) *MockDocumentServiceRegistry_UnregisterProvider_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentServiceRegistry creates a new instance of MockDocumentServiceRegistry. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentServiceRegistry(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentServiceRegistry {
	mock := &MockDocumentServiceRegistry{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
