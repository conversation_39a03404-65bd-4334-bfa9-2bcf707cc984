// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockAutoDocService is an autogenerated mock type for the AutoDocService type
type MockAutoDocService struct {
	mock.Mock
}

type MockAutoDocService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAutoDocService) EXPECT() *MockAutoDocService_Expecter {
	return &MockAutoDocService_Expecter{mock: &_m.Mock}
}

// CopyFile provides a mock function with given fields: ctx, tenantID, sourceFileID, targetParentID, newName
func (_m *MockAutoDocService) CopyFile(ctx context.Context, tenantID uint64, sourceFileID uint64, targetParentID uint64, newName string) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID, sourceFileID, targetParentID, newName)

	if len(ret) == 0 {
		panic("no return value specified for CopyFile")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64, uint64, string) (*model.Document, error)); ok {
		return rf(ctx, tenantID, sourceFileID, targetParentID, newName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64, uint64, string) *model.Document); ok {
		r0 = rf(ctx, tenantID, sourceFileID, targetParentID, newName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64, uint64, string) error); ok {
		r1 = rf(ctx, tenantID, sourceFileID, targetParentID, newName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_CopyFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyFile'
type MockAutoDocService_CopyFile_Call struct {
	*mock.Call
}

// CopyFile is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - sourceFileID uint64
//   - targetParentID uint64
//   - newName string
func (_e *MockAutoDocService_Expecter) CopyFile(ctx interface{}, tenantID interface{}, sourceFileID interface{}, targetParentID interface{}, newName interface{}) *MockAutoDocService_CopyFile_Call {
	return &MockAutoDocService_CopyFile_Call{Call: _e.mock.On("CopyFile", ctx, tenantID, sourceFileID, targetParentID, newName)}
}

func (_c *MockAutoDocService_CopyFile_Call) Run(run func(ctx context.Context, tenantID uint64, sourceFileID uint64, targetParentID uint64, newName string)) *MockAutoDocService_CopyFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64), args[3].(uint64), args[4].(string))
	})
	return _c
}

func (_c *MockAutoDocService_CopyFile_Call) Return(_a0 *model.Document, _a1 error) *MockAutoDocService_CopyFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_CopyFile_Call) RunAndReturn(run func(context.Context, uint64, uint64, uint64, string) (*model.Document, error)) *MockAutoDocService_CopyFile_Call {
	_c.Call.Return(run)
	return _c
}

// CopyFolder provides a mock function with given fields: ctx, tenantID, sourceFolderID, targetParentID, newName
func (_m *MockAutoDocService) CopyFolder(ctx context.Context, tenantID uint64, sourceFolderID uint64, targetParentID uint64, newName string) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID, sourceFolderID, targetParentID, newName)

	if len(ret) == 0 {
		panic("no return value specified for CopyFolder")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64, uint64, string) (*model.Document, error)); ok {
		return rf(ctx, tenantID, sourceFolderID, targetParentID, newName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64, uint64, string) *model.Document); ok {
		r0 = rf(ctx, tenantID, sourceFolderID, targetParentID, newName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64, uint64, string) error); ok {
		r1 = rf(ctx, tenantID, sourceFolderID, targetParentID, newName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_CopyFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CopyFolder'
type MockAutoDocService_CopyFolder_Call struct {
	*mock.Call
}

// CopyFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - sourceFolderID uint64
//   - targetParentID uint64
//   - newName string
func (_e *MockAutoDocService_Expecter) CopyFolder(ctx interface{}, tenantID interface{}, sourceFolderID interface{}, targetParentID interface{}, newName interface{}) *MockAutoDocService_CopyFolder_Call {
	return &MockAutoDocService_CopyFolder_Call{Call: _e.mock.On("CopyFolder", ctx, tenantID, sourceFolderID, targetParentID, newName)}
}

func (_c *MockAutoDocService_CopyFolder_Call) Run(run func(ctx context.Context, tenantID uint64, sourceFolderID uint64, targetParentID uint64, newName string)) *MockAutoDocService_CopyFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64), args[3].(uint64), args[4].(string))
	})
	return _c
}

func (_c *MockAutoDocService_CopyFolder_Call) Return(_a0 *model.Document, _a1 error) *MockAutoDocService_CopyFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_CopyFolder_Call) RunAndReturn(run func(context.Context, uint64, uint64, uint64, string) (*model.Document, error)) *MockAutoDocService_CopyFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRule provides a mock function with given fields: ctx, rule
func (_m *MockAutoDocService) CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	ret := _m.Called(ctx, rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule) error); ok {
		r0 = rf(ctx, rule)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAutoDocService_CreateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRule'
type MockAutoDocService_CreateRule_Call struct {
	*mock.Call
}

// CreateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
func (_e *MockAutoDocService_Expecter) CreateRule(ctx interface{}, rule interface{}) *MockAutoDocService_CreateRule_Call {
	return &MockAutoDocService_CreateRule_Call{Call: _e.mock.On("CreateRule", ctx, rule)}
}

func (_c *MockAutoDocService_CreateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule)) *MockAutoDocService_CreateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule))
	})
	return _c
}

func (_c *MockAutoDocService_CreateRule_Call) Return(_a0 error) *MockAutoDocService_CreateRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAutoDocService_CreateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule) error) *MockAutoDocService_CreateRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRule provides a mock function with given fields: ctx, ruleID
func (_m *MockAutoDocService) DeleteRule(ctx context.Context, ruleID uint64) error {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) error); ok {
		r0 = rf(ctx, ruleID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAutoDocService_DeleteRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRule'
type MockAutoDocService_DeleteRule_Call struct {
	*mock.Call
}

// DeleteRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
func (_e *MockAutoDocService_Expecter) DeleteRule(ctx interface{}, ruleID interface{}) *MockAutoDocService_DeleteRule_Call {
	return &MockAutoDocService_DeleteRule_Call{Call: _e.mock.On("DeleteRule", ctx, ruleID)}
}

func (_c *MockAutoDocService_DeleteRule_Call) Run(run func(ctx context.Context, ruleID uint64)) *MockAutoDocService_DeleteRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_DeleteRule_Call) Return(_a0 error) *MockAutoDocService_DeleteRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAutoDocService_DeleteRule_Call) RunAndReturn(run func(context.Context, uint64) error) *MockAutoDocService_DeleteRule_Call {
	_c.Call.Return(run)
	return _c
}

// EnsureAutoDocRoot provides a mock function with given fields: ctx, tenantID
func (_m *MockAutoDocService) EnsureAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for EnsureAutoDocRoot")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.Document, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.Document); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_EnsureAutoDocRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnsureAutoDocRoot'
type MockAutoDocService_EnsureAutoDocRoot_Call struct {
	*mock.Call
}

// EnsureAutoDocRoot is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockAutoDocService_Expecter) EnsureAutoDocRoot(ctx interface{}, tenantID interface{}) *MockAutoDocService_EnsureAutoDocRoot_Call {
	return &MockAutoDocService_EnsureAutoDocRoot_Call{Call: _e.mock.On("EnsureAutoDocRoot", ctx, tenantID)}
}

func (_c *MockAutoDocService_EnsureAutoDocRoot_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockAutoDocService_EnsureAutoDocRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_EnsureAutoDocRoot_Call) Return(_a0 *model.Document, _a1 error) *MockAutoDocService_EnsureAutoDocRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_EnsureAutoDocRoot_Call) RunAndReturn(run func(context.Context, uint64) (*model.Document, error)) *MockAutoDocService_EnsureAutoDocRoot_Call {
	_c.Call.Return(run)
	return _c
}

// ExtractPlaceholders provides a mock function with given fields: ctx, templateContent
func (_m *MockAutoDocService) ExtractPlaceholders(ctx context.Context, templateContent []byte) ([]string, error) {
	ret := _m.Called(ctx, templateContent)

	if len(ret) == 0 {
		panic("no return value specified for ExtractPlaceholders")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte) ([]string, error)); ok {
		return rf(ctx, templateContent)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte) []string); ok {
		r0 = rf(ctx, templateContent)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte) error); ok {
		r1 = rf(ctx, templateContent)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_ExtractPlaceholders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExtractPlaceholders'
type MockAutoDocService_ExtractPlaceholders_Call struct {
	*mock.Call
}

// ExtractPlaceholders is a helper method to define mock.On call
//   - ctx context.Context
//   - templateContent []byte
func (_e *MockAutoDocService_Expecter) ExtractPlaceholders(ctx interface{}, templateContent interface{}) *MockAutoDocService_ExtractPlaceholders_Call {
	return &MockAutoDocService_ExtractPlaceholders_Call{Call: _e.mock.On("ExtractPlaceholders", ctx, templateContent)}
}

func (_c *MockAutoDocService_ExtractPlaceholders_Call) Run(run func(ctx context.Context, templateContent []byte)) *MockAutoDocService_ExtractPlaceholders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte))
	})
	return _c
}

func (_c *MockAutoDocService_ExtractPlaceholders_Call) Return(_a0 []string, _a1 error) *MockAutoDocService_ExtractPlaceholders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_ExtractPlaceholders_Call) RunAndReturn(run func(context.Context, []byte) ([]string, error)) *MockAutoDocService_ExtractPlaceholders_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateFromTemplate provides a mock function with given fields: ctx, templateDoc, targetParent, fileName, placeholders, override
func (_m *MockAutoDocService) GenerateFromTemplate(ctx context.Context, templateDoc *model.Document, targetParent *autodoc.TargetParentResult, fileName string, placeholders map[string]interface{}, override bool) (*model.Document, error) {
	ret := _m.Called(ctx, templateDoc, targetParent, fileName, placeholders, override)

	if len(ret) == 0 {
		panic("no return value specified for GenerateFromTemplate")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Document, *autodoc.TargetParentResult, string, map[string]interface{}, bool) (*model.Document, error)); ok {
		return rf(ctx, templateDoc, targetParent, fileName, placeholders, override)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Document, *autodoc.TargetParentResult, string, map[string]interface{}, bool) *model.Document); ok {
		r0 = rf(ctx, templateDoc, targetParent, fileName, placeholders, override)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Document, *autodoc.TargetParentResult, string, map[string]interface{}, bool) error); ok {
		r1 = rf(ctx, templateDoc, targetParent, fileName, placeholders, override)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GenerateFromTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateFromTemplate'
type MockAutoDocService_GenerateFromTemplate_Call struct {
	*mock.Call
}

// GenerateFromTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - templateDoc *model.Document
//   - targetParent *autodoc.TargetParentResult
//   - fileName string
//   - placeholders map[string]interface{}
//   - override bool
func (_e *MockAutoDocService_Expecter) GenerateFromTemplate(ctx interface{}, templateDoc interface{}, targetParent interface{}, fileName interface{}, placeholders interface{}, override interface{}) *MockAutoDocService_GenerateFromTemplate_Call {
	return &MockAutoDocService_GenerateFromTemplate_Call{Call: _e.mock.On("GenerateFromTemplate", ctx, templateDoc, targetParent, fileName, placeholders, override)}
}

func (_c *MockAutoDocService_GenerateFromTemplate_Call) Run(run func(ctx context.Context, templateDoc *model.Document, targetParent *autodoc.TargetParentResult, fileName string, placeholders map[string]interface{}, override bool)) *MockAutoDocService_GenerateFromTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Document), args[2].(*autodoc.TargetParentResult), args[3].(string), args[4].(map[string]interface{}), args[5].(bool))
	})
	return _c
}

func (_c *MockAutoDocService_GenerateFromTemplate_Call) Return(_a0 *model.Document, _a1 error) *MockAutoDocService_GenerateFromTemplate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GenerateFromTemplate_Call) RunAndReturn(run func(context.Context, *model.Document, *autodoc.TargetParentResult, string, map[string]interface{}, bool) (*model.Document, error)) *MockAutoDocService_GenerateFromTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// GetActiveRules provides a mock function with given fields: ctx, tenantID
func (_m *MockAutoDocService) GetActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveRules")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GetActiveRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActiveRules'
type MockAutoDocService_GetActiveRules_Call struct {
	*mock.Call
}

// GetActiveRules is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockAutoDocService_Expecter) GetActiveRules(ctx interface{}, tenantID interface{}) *MockAutoDocService_GetActiveRules_Call {
	return &MockAutoDocService_GetActiveRules_Call{Call: _e.mock.On("GetActiveRules", ctx, tenantID)}
}

func (_c *MockAutoDocService_GetActiveRules_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockAutoDocService_GetActiveRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_GetActiveRules_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockAutoDocService_GetActiveRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GetActiveRules_Call) RunAndReturn(run func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)) *MockAutoDocService_GetActiveRules_Call {
	_c.Call.Return(run)
	return _c
}

// GetAutoDocRoot provides a mock function with given fields: ctx, tenantID
func (_m *MockAutoDocService) GetAutoDocRoot(ctx context.Context, tenantID uint64) (*model.Document, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetAutoDocRoot")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.Document, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.Document); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GetAutoDocRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAutoDocRoot'
type MockAutoDocService_GetAutoDocRoot_Call struct {
	*mock.Call
}

// GetAutoDocRoot is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockAutoDocService_Expecter) GetAutoDocRoot(ctx interface{}, tenantID interface{}) *MockAutoDocService_GetAutoDocRoot_Call {
	return &MockAutoDocService_GetAutoDocRoot_Call{Call: _e.mock.On("GetAutoDocRoot", ctx, tenantID)}
}

func (_c *MockAutoDocService_GetAutoDocRoot_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockAutoDocService_GetAutoDocRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_GetAutoDocRoot_Call) Return(_a0 *model.Document, _a1 error) *MockAutoDocService_GetAutoDocRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GetAutoDocRoot_Call) RunAndReturn(run func(context.Context, uint64) (*model.Document, error)) *MockAutoDocService_GetAutoDocRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetRule provides a mock function with given fields: ctx, ruleID
func (_m *MockAutoDocService) GetRule(ctx context.Context, ruleID uint64) (*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for GetRule")
	}

	var r0 *model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, ruleID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.DocumentAutomationRule); ok {
		r0 = rf(ctx, ruleID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, ruleID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GetRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRule'
type MockAutoDocService_GetRule_Call struct {
	*mock.Call
}

// GetRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
func (_e *MockAutoDocService_Expecter) GetRule(ctx interface{}, ruleID interface{}) *MockAutoDocService_GetRule_Call {
	return &MockAutoDocService_GetRule_Call{Call: _e.mock.On("GetRule", ctx, ruleID)}
}

func (_c *MockAutoDocService_GetRule_Call) Run(run func(ctx context.Context, ruleID uint64)) *MockAutoDocService_GetRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_GetRule_Call) Return(_a0 *model.DocumentAutomationRule, _a1 error) *MockAutoDocService_GetRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GetRule_Call) RunAndReturn(run func(context.Context, uint64) (*model.DocumentAutomationRule, error)) *MockAutoDocService_GetRule_Call {
	_c.Call.Return(run)
	return _c
}

// GetRuleWithTenant provides a mock function with given fields: ctx, ruleID, tenantID
func (_m *MockAutoDocService) GetRuleWithTenant(ctx context.Context, ruleID uint64, tenantID uint64) (*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, ruleID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetRuleWithTenant")
	}

	var r0 *model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, ruleID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) *model.DocumentAutomationRule); ok {
		r0 = rf(ctx, ruleID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, ruleID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GetRuleWithTenant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRuleWithTenant'
type MockAutoDocService_GetRuleWithTenant_Call struct {
	*mock.Call
}

// GetRuleWithTenant is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID uint64
//   - tenantID uint64
func (_e *MockAutoDocService_Expecter) GetRuleWithTenant(ctx interface{}, ruleID interface{}, tenantID interface{}) *MockAutoDocService_GetRuleWithTenant_Call {
	return &MockAutoDocService_GetRuleWithTenant_Call{Call: _e.mock.On("GetRuleWithTenant", ctx, ruleID, tenantID)}
}

func (_c *MockAutoDocService_GetRuleWithTenant_Call) Run(run func(ctx context.Context, ruleID uint64, tenantID uint64)) *MockAutoDocService_GetRuleWithTenant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_GetRuleWithTenant_Call) Return(_a0 *model.DocumentAutomationRule, _a1 error) *MockAutoDocService_GetRuleWithTenant_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GetRuleWithTenant_Call) RunAndReturn(run func(context.Context, uint64, uint64) (*model.DocumentAutomationRule, error)) *MockAutoDocService_GetRuleWithTenant_Call {
	_c.Call.Return(run)
	return _c
}

// GetRulesByTenant provides a mock function with given fields: ctx, tenantID
func (_m *MockAutoDocService) GetRulesByTenant(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetRulesByTenant")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_GetRulesByTenant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRulesByTenant'
type MockAutoDocService_GetRulesByTenant_Call struct {
	*mock.Call
}

// GetRulesByTenant is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockAutoDocService_Expecter) GetRulesByTenant(ctx interface{}, tenantID interface{}) *MockAutoDocService_GetRulesByTenant_Call {
	return &MockAutoDocService_GetRulesByTenant_Call{Call: _e.mock.On("GetRulesByTenant", ctx, tenantID)}
}

func (_c *MockAutoDocService_GetRulesByTenant_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockAutoDocService_GetRulesByTenant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockAutoDocService_GetRulesByTenant_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockAutoDocService_GetRulesByTenant_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_GetRulesByTenant_Call) RunAndReturn(run func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)) *MockAutoDocService_GetRulesByTenant_Call {
	_c.Call.Return(run)
	return _c
}

// ProcessDocxTemplate provides a mock function with given fields: ctx, templateContent, placeholders
func (_m *MockAutoDocService) ProcessDocxTemplate(ctx context.Context, templateContent []byte, placeholders map[string]interface{}) ([]byte, error) {
	ret := _m.Called(ctx, templateContent, placeholders)

	if len(ret) == 0 {
		panic("no return value specified for ProcessDocxTemplate")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte, map[string]interface{}) ([]byte, error)); ok {
		return rf(ctx, templateContent, placeholders)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte, map[string]interface{}) []byte); ok {
		r0 = rf(ctx, templateContent, placeholders)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte, map[string]interface{}) error); ok {
		r1 = rf(ctx, templateContent, placeholders)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_ProcessDocxTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessDocxTemplate'
type MockAutoDocService_ProcessDocxTemplate_Call struct {
	*mock.Call
}

// ProcessDocxTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - templateContent []byte
//   - placeholders map[string]interface{}
func (_e *MockAutoDocService_Expecter) ProcessDocxTemplate(ctx interface{}, templateContent interface{}, placeholders interface{}) *MockAutoDocService_ProcessDocxTemplate_Call {
	return &MockAutoDocService_ProcessDocxTemplate_Call{Call: _e.mock.On("ProcessDocxTemplate", ctx, templateContent, placeholders)}
}

func (_c *MockAutoDocService_ProcessDocxTemplate_Call) Run(run func(ctx context.Context, templateContent []byte, placeholders map[string]interface{})) *MockAutoDocService_ProcessDocxTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *MockAutoDocService_ProcessDocxTemplate_Call) Return(_a0 []byte, _a1 error) *MockAutoDocService_ProcessDocxTemplate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_ProcessDocxTemplate_Call) RunAndReturn(run func(context.Context, []byte, map[string]interface{}) ([]byte, error)) *MockAutoDocService_ProcessDocxTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// ResolveTargetParentPath provides a mock function with given fields: ctx, tenantID, parentPath, placeholderData
func (_m *MockAutoDocService) ResolveTargetParentPath(ctx context.Context, tenantID uint64, parentPath string, placeholderData map[string]interface{}) (*autodoc.TargetParentResult, error) {
	ret := _m.Called(ctx, tenantID, parentPath, placeholderData)

	if len(ret) == 0 {
		panic("no return value specified for ResolveTargetParentPath")
	}

	var r0 *autodoc.TargetParentResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) (*autodoc.TargetParentResult, error)); ok {
		return rf(ctx, tenantID, parentPath, placeholderData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) *autodoc.TargetParentResult); ok {
		r0 = rf(ctx, tenantID, parentPath, placeholderData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.TargetParentResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r1 = rf(ctx, tenantID, parentPath, placeholderData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockAutoDocService_ResolveTargetParentPath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResolveTargetParentPath'
type MockAutoDocService_ResolveTargetParentPath_Call struct {
	*mock.Call
}

// ResolveTargetParentPath is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - parentPath string
//   - placeholderData map[string]interface{}
func (_e *MockAutoDocService_Expecter) ResolveTargetParentPath(ctx interface{}, tenantID interface{}, parentPath interface{}, placeholderData interface{}) *MockAutoDocService_ResolveTargetParentPath_Call {
	return &MockAutoDocService_ResolveTargetParentPath_Call{Call: _e.mock.On("ResolveTargetParentPath", ctx, tenantID, parentPath, placeholderData)}
}

func (_c *MockAutoDocService_ResolveTargetParentPath_Call) Run(run func(ctx context.Context, tenantID uint64, parentPath string, placeholderData map[string]interface{})) *MockAutoDocService_ResolveTargetParentPath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(map[string]interface{}))
	})
	return _c
}

func (_c *MockAutoDocService_ResolveTargetParentPath_Call) Return(_a0 *autodoc.TargetParentResult, _a1 error) *MockAutoDocService_ResolveTargetParentPath_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockAutoDocService_ResolveTargetParentPath_Call) RunAndReturn(run func(context.Context, uint64, string, map[string]interface{}) (*autodoc.TargetParentResult, error)) *MockAutoDocService_ResolveTargetParentPath_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRule provides a mock function with given fields: ctx, rule
func (_m *MockAutoDocService) UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	ret := _m.Called(ctx, rule)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule) error); ok {
		r0 = rf(ctx, rule)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAutoDocService_UpdateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRule'
type MockAutoDocService_UpdateRule_Call struct {
	*mock.Call
}

// UpdateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
func (_e *MockAutoDocService_Expecter) UpdateRule(ctx interface{}, rule interface{}) *MockAutoDocService_UpdateRule_Call {
	return &MockAutoDocService_UpdateRule_Call{Call: _e.mock.On("UpdateRule", ctx, rule)}
}

func (_c *MockAutoDocService_UpdateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule)) *MockAutoDocService_UpdateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule))
	})
	return _c
}

func (_c *MockAutoDocService_UpdateRule_Call) Return(_a0 error) *MockAutoDocService_UpdateRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAutoDocService_UpdateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule) error) *MockAutoDocService_UpdateRule_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateTemplate provides a mock function with given fields: ctx, templateContent
func (_m *MockAutoDocService) ValidateTemplate(ctx context.Context, templateContent []byte) error {
	ret := _m.Called(ctx, templateContent)

	if len(ret) == 0 {
		panic("no return value specified for ValidateTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte) error); ok {
		r0 = rf(ctx, templateContent)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockAutoDocService_ValidateTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateTemplate'
type MockAutoDocService_ValidateTemplate_Call struct {
	*mock.Call
}

// ValidateTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - templateContent []byte
func (_e *MockAutoDocService_Expecter) ValidateTemplate(ctx interface{}, templateContent interface{}) *MockAutoDocService_ValidateTemplate_Call {
	return &MockAutoDocService_ValidateTemplate_Call{Call: _e.mock.On("ValidateTemplate", ctx, templateContent)}
}

func (_c *MockAutoDocService_ValidateTemplate_Call) Run(run func(ctx context.Context, templateContent []byte)) *MockAutoDocService_ValidateTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte))
	})
	return _c
}

func (_c *MockAutoDocService_ValidateTemplate_Call) Return(_a0 error) *MockAutoDocService_ValidateTemplate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockAutoDocService_ValidateTemplate_Call) RunAndReturn(run func(context.Context, []byte) error) *MockAutoDocService_ValidateTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAutoDocService creates a new instance of MockAutoDocService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAutoDocService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAutoDocService {
	mock := &MockAutoDocService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
