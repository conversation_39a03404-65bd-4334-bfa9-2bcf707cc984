// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockDefaultDMSService is an autogenerated mock type for the DefaultDMSService type
type MockDefaultDMSService struct {
	mock.Mock
}

type MockDefaultDMSService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDefaultDMSService) EXPECT() *MockDefaultDMSService_Expecter {
	return &MockDefaultDMSService_Expecter{mock: &_m.Mock}
}

// GetDefaultProvider provides a mock function with given fields: ctx, tenantID
func (_m *MockDefaultDMSService) GetDefaultProvider(ctx context.Context, tenantID uint64) (string, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultProvider")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (string, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) string); ok {
		r0 = rf(ctx, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDefaultDMSService_GetDefaultProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultProvider'
type MockDefaultDMSService_GetDefaultProvider_Call struct {
	*mock.Call
}

// GetDefaultProvider is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockDefaultDMSService_Expecter) GetDefaultProvider(ctx interface{}, tenantID interface{}) *MockDefaultDMSService_GetDefaultProvider_Call {
	return &MockDefaultDMSService_GetDefaultProvider_Call{Call: _e.mock.On("GetDefaultProvider", ctx, tenantID)}
}

func (_c *MockDefaultDMSService_GetDefaultProvider_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockDefaultDMSService_GetDefaultProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockDefaultDMSService_GetDefaultProvider_Call) Return(_a0 string, _a1 error) *MockDefaultDMSService_GetDefaultProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDefaultDMSService_GetDefaultProvider_Call) RunAndReturn(run func(context.Context, uint64) (string, error)) *MockDefaultDMSService_GetDefaultProvider_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDefaultDMSService creates a new instance of MockDefaultDMSService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDefaultDMSService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDefaultDMSService {
	mock := &MockDefaultDMSService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
