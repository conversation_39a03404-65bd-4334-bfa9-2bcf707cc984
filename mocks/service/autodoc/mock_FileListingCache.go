// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockFileListingCache is an autogenerated mock type for the FileListingCache type
type MockFileListingCache struct {
	mock.Mock
}

type MockFileListingCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFileListingCache) EXPECT() *MockFileListingCache_Expecter {
	return &MockFileListingCache_Expecter{mock: &_m.Mock}
}

// CacheFiles provides a mock function with given fields: ctx, req, resp, provider
func (_m *MockFileListingCache) CacheFiles(ctx context.Context, req *autodoc.ListFilesRequest, resp *autodoc.ListFilesResponse, provider string) error {
	ret := _m.Called(ctx, req, resp, provider)

	if len(ret) == 0 {
		panic("no return value specified for CacheFiles")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest, *autodoc.ListFilesResponse, string) error); ok {
		r0 = rf(ctx, req, resp, provider)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_CacheFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CacheFiles'
type MockFileListingCache_CacheFiles_Call struct {
	*mock.Call
}

// CacheFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.ListFilesRequest
//   - resp *autodoc.ListFilesResponse
//   - provider string
func (_e *MockFileListingCache_Expecter) CacheFiles(ctx interface{}, req interface{}, resp interface{}, provider interface{}) *MockFileListingCache_CacheFiles_Call {
	return &MockFileListingCache_CacheFiles_Call{Call: _e.mock.On("CacheFiles", ctx, req, resp, provider)}
}

func (_c *MockFileListingCache_CacheFiles_Call) Run(run func(ctx context.Context, req *autodoc.ListFilesRequest, resp *autodoc.ListFilesResponse, provider string)) *MockFileListingCache_CacheFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ListFilesRequest), args[2].(*autodoc.ListFilesResponse), args[3].(string))
	})
	return _c
}

func (_c *MockFileListingCache_CacheFiles_Call) Return(_a0 error) *MockFileListingCache_CacheFiles_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_CacheFiles_Call) RunAndReturn(run func(context.Context, *autodoc.ListFilesRequest, *autodoc.ListFilesResponse, string) error) *MockFileListingCache_CacheFiles_Call {
	_c.Call.Return(run)
	return _c
}

// GetCacheStats provides a mock function with given fields: ctx
func (_m *MockFileListingCache) GetCacheStats(ctx context.Context) (*autodoc.CacheStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetCacheStats")
	}

	var r0 *autodoc.CacheStats
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*autodoc.CacheStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *autodoc.CacheStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.CacheStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileListingCache_GetCacheStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCacheStats'
type MockFileListingCache_GetCacheStats_Call struct {
	*mock.Call
}

// GetCacheStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockFileListingCache_Expecter) GetCacheStats(ctx interface{}) *MockFileListingCache_GetCacheStats_Call {
	return &MockFileListingCache_GetCacheStats_Call{Call: _e.mock.On("GetCacheStats", ctx)}
}

func (_c *MockFileListingCache_GetCacheStats_Call) Run(run func(ctx context.Context)) *MockFileListingCache_GetCacheStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockFileListingCache_GetCacheStats_Call) Return(_a0 *autodoc.CacheStats, _a1 error) *MockFileListingCache_GetCacheStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileListingCache_GetCacheStats_Call) RunAndReturn(run func(context.Context) (*autodoc.CacheStats, error)) *MockFileListingCache_GetCacheStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetCachedFiles provides a mock function with given fields: ctx, req, provider
func (_m *MockFileListingCache) GetCachedFiles(ctx context.Context, req *autodoc.ListFilesRequest, provider string) (*autodoc.ListFilesResponse, bool) {
	ret := _m.Called(ctx, req, provider)

	if len(ret) == 0 {
		panic("no return value specified for GetCachedFiles")
	}

	var r0 *autodoc.ListFilesResponse
	var r1 bool
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest, string) (*autodoc.ListFilesResponse, bool)); ok {
		return rf(ctx, req, provider)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ListFilesRequest, string) *autodoc.ListFilesResponse); ok {
		r0 = rf(ctx, req, provider)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.ListFilesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ListFilesRequest, string) bool); ok {
		r1 = rf(ctx, req, provider)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockFileListingCache_GetCachedFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCachedFiles'
type MockFileListingCache_GetCachedFiles_Call struct {
	*mock.Call
}

// GetCachedFiles is a helper method to define mock.On call
//   - ctx context.Context
//   - req *autodoc.ListFilesRequest
//   - provider string
func (_e *MockFileListingCache_Expecter) GetCachedFiles(ctx interface{}, req interface{}, provider interface{}) *MockFileListingCache_GetCachedFiles_Call {
	return &MockFileListingCache_GetCachedFiles_Call{Call: _e.mock.On("GetCachedFiles", ctx, req, provider)}
}

func (_c *MockFileListingCache_GetCachedFiles_Call) Run(run func(ctx context.Context, req *autodoc.ListFilesRequest, provider string)) *MockFileListingCache_GetCachedFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ListFilesRequest), args[2].(string))
	})
	return _c
}

func (_c *MockFileListingCache_GetCachedFiles_Call) Return(_a0 *autodoc.ListFilesResponse, _a1 bool) *MockFileListingCache_GetCachedFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileListingCache_GetCachedFiles_Call) RunAndReturn(run func(context.Context, *autodoc.ListFilesRequest, string) (*autodoc.ListFilesResponse, bool)) *MockFileListingCache_GetCachedFiles_Call {
	_c.Call.Return(run)
	return _c
}

// InvalidateCache provides a mock function with given fields: ctx, tenantID, provider, path
func (_m *MockFileListingCache) InvalidateCache(ctx context.Context, tenantID uint64, provider string, path string) error {
	ret := _m.Called(ctx, tenantID, provider, path)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateCache")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, string) error); ok {
		r0 = rf(ctx, tenantID, provider, path)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_InvalidateCache_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvalidateCache'
type MockFileListingCache_InvalidateCache_Call struct {
	*mock.Call
}

// InvalidateCache is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - provider string
//   - path string
func (_e *MockFileListingCache_Expecter) InvalidateCache(ctx interface{}, tenantID interface{}, provider interface{}, path interface{}) *MockFileListingCache_InvalidateCache_Call {
	return &MockFileListingCache_InvalidateCache_Call{Call: _e.mock.On("InvalidateCache", ctx, tenantID, provider, path)}
}

func (_c *MockFileListingCache_InvalidateCache_Call) Run(run func(ctx context.Context, tenantID uint64, provider string, path string)) *MockFileListingCache_InvalidateCache_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockFileListingCache_InvalidateCache_Call) Return(_a0 error) *MockFileListingCache_InvalidateCache_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_InvalidateCache_Call) RunAndReturn(run func(context.Context, uint64, string, string) error) *MockFileListingCache_InvalidateCache_Call {
	_c.Call.Return(run)
	return _c
}

// InvalidateProviderCache provides a mock function with given fields: ctx, tenantID, provider
func (_m *MockFileListingCache) InvalidateProviderCache(ctx context.Context, tenantID uint64, provider string) error {
	ret := _m.Called(ctx, tenantID, provider)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateProviderCache")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string) error); ok {
		r0 = rf(ctx, tenantID, provider)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_InvalidateProviderCache_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvalidateProviderCache'
type MockFileListingCache_InvalidateProviderCache_Call struct {
	*mock.Call
}

// InvalidateProviderCache is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - provider string
func (_e *MockFileListingCache_Expecter) InvalidateProviderCache(ctx interface{}, tenantID interface{}, provider interface{}) *MockFileListingCache_InvalidateProviderCache_Call {
	return &MockFileListingCache_InvalidateProviderCache_Call{Call: _e.mock.On("InvalidateProviderCache", ctx, tenantID, provider)}
}

func (_c *MockFileListingCache_InvalidateProviderCache_Call) Run(run func(ctx context.Context, tenantID uint64, provider string)) *MockFileListingCache_InvalidateProviderCache_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string))
	})
	return _c
}

func (_c *MockFileListingCache_InvalidateProviderCache_Call) Return(_a0 error) *MockFileListingCache_InvalidateProviderCache_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_InvalidateProviderCache_Call) RunAndReturn(run func(context.Context, uint64, string) error) *MockFileListingCache_InvalidateProviderCache_Call {
	_c.Call.Return(run)
	return _c
}

// InvalidateTenantCache provides a mock function with given fields: ctx, tenantID
func (_m *MockFileListingCache) InvalidateTenantCache(ctx context.Context, tenantID uint64) error {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateTenantCache")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) error); ok {
		r0 = rf(ctx, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_InvalidateTenantCache_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvalidateTenantCache'
type MockFileListingCache_InvalidateTenantCache_Call struct {
	*mock.Call
}

// InvalidateTenantCache is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockFileListingCache_Expecter) InvalidateTenantCache(ctx interface{}, tenantID interface{}) *MockFileListingCache_InvalidateTenantCache_Call {
	return &MockFileListingCache_InvalidateTenantCache_Call{Call: _e.mock.On("InvalidateTenantCache", ctx, tenantID)}
}

func (_c *MockFileListingCache_InvalidateTenantCache_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockFileListingCache_InvalidateTenantCache_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockFileListingCache_InvalidateTenantCache_Call) Return(_a0 error) *MockFileListingCache_InvalidateTenantCache_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_InvalidateTenantCache_Call) RunAndReturn(run func(context.Context, uint64) error) *MockFileListingCache_InvalidateTenantCache_Call {
	_c.Call.Return(run)
	return _c
}

// PublishInvalidation provides a mock function with given fields: ctx, event
func (_m *MockFileListingCache) PublishInvalidation(ctx context.Context, event *autodoc.CacheInvalidationEvent) error {
	ret := _m.Called(ctx, event)

	if len(ret) == 0 {
		panic("no return value specified for PublishInvalidation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.CacheInvalidationEvent) error); ok {
		r0 = rf(ctx, event)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_PublishInvalidation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishInvalidation'
type MockFileListingCache_PublishInvalidation_Call struct {
	*mock.Call
}

// PublishInvalidation is a helper method to define mock.On call
//   - ctx context.Context
//   - event *autodoc.CacheInvalidationEvent
func (_e *MockFileListingCache_Expecter) PublishInvalidation(ctx interface{}, event interface{}) *MockFileListingCache_PublishInvalidation_Call {
	return &MockFileListingCache_PublishInvalidation_Call{Call: _e.mock.On("PublishInvalidation", ctx, event)}
}

func (_c *MockFileListingCache_PublishInvalidation_Call) Run(run func(ctx context.Context, event *autodoc.CacheInvalidationEvent)) *MockFileListingCache_PublishInvalidation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.CacheInvalidationEvent))
	})
	return _c
}

func (_c *MockFileListingCache_PublishInvalidation_Call) Return(_a0 error) *MockFileListingCache_PublishInvalidation_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_PublishInvalidation_Call) RunAndReturn(run func(context.Context, *autodoc.CacheInvalidationEvent) error) *MockFileListingCache_PublishInvalidation_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeInvalidation provides a mock function with given fields: ctx, handler
func (_m *MockFileListingCache) SubscribeInvalidation(ctx context.Context, handler func(*autodoc.CacheInvalidationEvent)) error {
	ret := _m.Called(ctx, handler)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeInvalidation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(*autodoc.CacheInvalidationEvent)) error); ok {
		r0 = rf(ctx, handler)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_SubscribeInvalidation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeInvalidation'
type MockFileListingCache_SubscribeInvalidation_Call struct {
	*mock.Call
}

// SubscribeInvalidation is a helper method to define mock.On call
//   - ctx context.Context
//   - handler func(*autodoc.CacheInvalidationEvent)
func (_e *MockFileListingCache_Expecter) SubscribeInvalidation(ctx interface{}, handler interface{}) *MockFileListingCache_SubscribeInvalidation_Call {
	return &MockFileListingCache_SubscribeInvalidation_Call{Call: _e.mock.On("SubscribeInvalidation", ctx, handler)}
}

func (_c *MockFileListingCache_SubscribeInvalidation_Call) Run(run func(ctx context.Context, handler func(*autodoc.CacheInvalidationEvent))) *MockFileListingCache_SubscribeInvalidation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(*autodoc.CacheInvalidationEvent)))
	})
	return _c
}

func (_c *MockFileListingCache_SubscribeInvalidation_Call) Return(_a0 error) *MockFileListingCache_SubscribeInvalidation_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_SubscribeInvalidation_Call) RunAndReturn(run func(context.Context, func(*autodoc.CacheInvalidationEvent)) error) *MockFileListingCache_SubscribeInvalidation_Call {
	_c.Call.Return(run)
	return _c
}

// WarmCache provides a mock function with given fields: ctx, tenantID, provider, paths
func (_m *MockFileListingCache) WarmCache(ctx context.Context, tenantID uint64, provider string, paths []string) error {
	ret := _m.Called(ctx, tenantID, provider, paths)

	if len(ret) == 0 {
		panic("no return value specified for WarmCache")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, []string) error); ok {
		r0 = rf(ctx, tenantID, provider, paths)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileListingCache_WarmCache_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WarmCache'
type MockFileListingCache_WarmCache_Call struct {
	*mock.Call
}

// WarmCache is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - provider string
//   - paths []string
func (_e *MockFileListingCache_Expecter) WarmCache(ctx interface{}, tenantID interface{}, provider interface{}, paths interface{}) *MockFileListingCache_WarmCache_Call {
	return &MockFileListingCache_WarmCache_Call{Call: _e.mock.On("WarmCache", ctx, tenantID, provider, paths)}
}

func (_c *MockFileListingCache_WarmCache_Call) Run(run func(ctx context.Context, tenantID uint64, provider string, paths []string)) *MockFileListingCache_WarmCache_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].([]string))
	})
	return _c
}

func (_c *MockFileListingCache_WarmCache_Call) Return(_a0 error) *MockFileListingCache_WarmCache_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileListingCache_WarmCache_Call) RunAndReturn(run func(context.Context, uint64, string, []string) error) *MockFileListingCache_WarmCache_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFileListingCache creates a new instance of MockFileListingCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFileListingCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFileListingCache {
	mock := &MockFileListingCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
