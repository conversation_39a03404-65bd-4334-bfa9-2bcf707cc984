// Code generated by mockery. DO NOT EDIT.

package autodoc

import (
	autodoc "bilabl/docman/internal/service/autodoc"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockActionHandler is an autogenerated mock type for the ActionHandler type
type MockActionHandler struct {
	mock.Mock
}

type MockActionHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockActionHandler) EXPECT() *MockActionHandler_Expecter {
	return &MockActionHandler_Expecter{mock: &_m.Mock}
}

// Execute provides a mock function with given fields: ctx, params
func (_m *MockActionHandler) Execute(ctx context.Context, params *autodoc.ActionExecutionParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Execute")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockActionHandler_Execute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Execute'
type MockActionHandler_Execute_Call struct {
	*mock.Call
}

// Execute is a helper method to define mock.On call
//   - ctx context.Context
//   - params *autodoc.ActionExecutionParams
func (_e *MockActionHandler_Expecter) Execute(ctx interface{}, params interface{}) *MockActionHandler_Execute_Call {
	return &MockActionHandler_Execute_Call{Call: _e.mock.On("Execute", ctx, params)}
}

func (_c *MockActionHandler_Execute_Call) Run(run func(ctx context.Context, params *autodoc.ActionExecutionParams)) *MockActionHandler_Execute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ActionExecutionParams))
	})
	return _c
}

func (_c *MockActionHandler_Execute_Call) Return(_a0 error) *MockActionHandler_Execute_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandler_Execute_Call) RunAndReturn(run func(context.Context, *autodoc.ActionExecutionParams) error) *MockActionHandler_Execute_Call {
	_c.Call.Return(run)
	return _c
}

// GetActionType provides a mock function with no fields
func (_m *MockActionHandler) GetActionType() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetActionType")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockActionHandler_GetActionType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActionType'
type MockActionHandler_GetActionType_Call struct {
	*mock.Call
}

// GetActionType is a helper method to define mock.On call
func (_e *MockActionHandler_Expecter) GetActionType() *MockActionHandler_GetActionType_Call {
	return &MockActionHandler_GetActionType_Call{Call: _e.mock.On("GetActionType")}
}

func (_c *MockActionHandler_GetActionType_Call) Run(run func()) *MockActionHandler_GetActionType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockActionHandler_GetActionType_Call) Return(_a0 string) *MockActionHandler_GetActionType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandler_GetActionType_Call) RunAndReturn(run func() string) *MockActionHandler_GetActionType_Call {
	_c.Call.Return(run)
	return _c
}

// Test provides a mock function with given fields: ctx, params
func (_m *MockActionHandler) Test(ctx context.Context, params *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Test")
	}

	var r0 *autodoc.ActionTestResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) *autodoc.ActionTestResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*autodoc.ActionTestResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *autodoc.ActionExecutionParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockActionHandler_Test_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Test'
type MockActionHandler_Test_Call struct {
	*mock.Call
}

// Test is a helper method to define mock.On call
//   - ctx context.Context
//   - params *autodoc.ActionExecutionParams
func (_e *MockActionHandler_Expecter) Test(ctx interface{}, params interface{}) *MockActionHandler_Test_Call {
	return &MockActionHandler_Test_Call{Call: _e.mock.On("Test", ctx, params)}
}

func (_c *MockActionHandler_Test_Call) Run(run func(ctx context.Context, params *autodoc.ActionExecutionParams)) *MockActionHandler_Test_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ActionExecutionParams))
	})
	return _c
}

func (_c *MockActionHandler_Test_Call) Return(_a0 *autodoc.ActionTestResult, _a1 error) *MockActionHandler_Test_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockActionHandler_Test_Call) RunAndReturn(run func(context.Context, *autodoc.ActionExecutionParams) (*autodoc.ActionTestResult, error)) *MockActionHandler_Test_Call {
	_c.Call.Return(run)
	return _c
}

// Validate provides a mock function with given fields: ctx, params
func (_m *MockActionHandler) Validate(ctx context.Context, params *autodoc.ActionExecutionParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Validate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *autodoc.ActionExecutionParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockActionHandler_Validate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Validate'
type MockActionHandler_Validate_Call struct {
	*mock.Call
}

// Validate is a helper method to define mock.On call
//   - ctx context.Context
//   - params *autodoc.ActionExecutionParams
func (_e *MockActionHandler_Expecter) Validate(ctx interface{}, params interface{}) *MockActionHandler_Validate_Call {
	return &MockActionHandler_Validate_Call{Call: _e.mock.On("Validate", ctx, params)}
}

func (_c *MockActionHandler_Validate_Call) Run(run func(ctx context.Context, params *autodoc.ActionExecutionParams)) *MockActionHandler_Validate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*autodoc.ActionExecutionParams))
	})
	return _c
}

func (_c *MockActionHandler_Validate_Call) Return(_a0 error) *MockActionHandler_Validate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockActionHandler_Validate_Call) RunAndReturn(run func(context.Context, *autodoc.ActionExecutionParams) error) *MockActionHandler_Validate_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockActionHandler creates a new instance of MockActionHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockActionHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockActionHandler {
	mock := &MockActionHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
