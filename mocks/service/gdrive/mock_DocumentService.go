// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/internal/service/gdrive"
	context "context"
	io "io"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"

	pkggdrive "bilabl/docman/pkg/gdrive"

	repositories "bilabl/docman/pkg/repositories"
)

// MockDocumentService is an autogenerated mock type for the DocumentService type
type MockDocumentService struct {
	mock.Mock
}

type MockDocumentService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentService) EXPECT() *MockDocumentService_Expecter {
	return &MockDocumentService_Expecter{mock: &_m.Mock}
}

// CreateDocument provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CreateDocument(ctx context.Context, req *gdrive.CreateDocumentRequest) (*gdrive.DocumentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateDocument")
	}

	var r0 *gdrive.DocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateDocumentRequest) (*gdrive.DocumentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateDocumentRequest) *gdrive.DocumentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CreateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDocument'
type MockDocumentService_CreateDocument_Call struct {
	*mock.Call
}

// CreateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateDocumentRequest
func (_e *MockDocumentService_Expecter) CreateDocument(ctx interface{}, req interface{}) *MockDocumentService_CreateDocument_Call {
	return &MockDocumentService_CreateDocument_Call{Call: _e.mock.On("CreateDocument", ctx, req)}
}

func (_c *MockDocumentService_CreateDocument_Call) Run(run func(ctx context.Context, req *gdrive.CreateDocumentRequest)) *MockDocumentService_CreateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_CreateDocument_Call) Return(_a0 *gdrive.DocumentResponse, _a1 error) *MockDocumentService_CreateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CreateDocument_Call) RunAndReturn(run func(context.Context, *gdrive.CreateDocumentRequest) (*gdrive.DocumentResponse, error)) *MockDocumentService_CreateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUploadSession provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) CreateUploadSession(ctx context.Context, req *gdrive.CreateUploadSessionRequest) (*gdrive.UploadSessionResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateUploadSession")
	}

	var r0 *gdrive.UploadSessionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateUploadSessionRequest) (*gdrive.UploadSessionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateUploadSessionRequest) *gdrive.UploadSessionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.UploadSessionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateUploadSessionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_CreateUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUploadSession'
type MockDocumentService_CreateUploadSession_Call struct {
	*mock.Call
}

// CreateUploadSession is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateUploadSessionRequest
func (_e *MockDocumentService_Expecter) CreateUploadSession(ctx interface{}, req interface{}) *MockDocumentService_CreateUploadSession_Call {
	return &MockDocumentService_CreateUploadSession_Call{Call: _e.mock.On("CreateUploadSession", ctx, req)}
}

func (_c *MockDocumentService_CreateUploadSession_Call) Run(run func(ctx context.Context, req *gdrive.CreateUploadSessionRequest)) *MockDocumentService_CreateUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateUploadSessionRequest))
	})
	return _c
}

func (_c *MockDocumentService_CreateUploadSession_Call) Return(_a0 *gdrive.UploadSessionResponse, _a1 error) *MockDocumentService_CreateUploadSession_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_CreateUploadSession_Call) RunAndReturn(run func(context.Context, *gdrive.CreateUploadSessionRequest) (*gdrive.UploadSessionResponse, error)) *MockDocumentService_CreateUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDocument provides a mock function with given fields: ctx, driveFileID
func (_m *MockDocumentService) DeleteDocument(ctx context.Context, driveFileID string) error {
	ret := _m.Called(ctx, driveFileID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocument")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, driveFileID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentService_DeleteDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDocument'
type MockDocumentService_DeleteDocument_Call struct {
	*mock.Call
}

// DeleteDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - driveFileID string
func (_e *MockDocumentService_Expecter) DeleteDocument(ctx interface{}, driveFileID interface{}) *MockDocumentService_DeleteDocument_Call {
	return &MockDocumentService_DeleteDocument_Call{Call: _e.mock.On("DeleteDocument", ctx, driveFileID)}
}

func (_c *MockDocumentService_DeleteDocument_Call) Run(run func(ctx context.Context, driveFileID string)) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockDocumentService_DeleteDocument_Call) Return(_a0 error) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_DeleteDocument_Call) RunAndReturn(run func(context.Context, string) error) *MockDocumentService_DeleteDocument_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocument provides a mock function with given fields: ctx, driveFileID
func (_m *MockDocumentService) GetDocument(ctx context.Context, driveFileID string) (*gdrive.DocumentResponse, error) {
	ret := _m.Called(ctx, driveFileID)

	if len(ret) == 0 {
		panic("no return value specified for GetDocument")
	}

	var r0 *gdrive.DocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*gdrive.DocumentResponse, error)); ok {
		return rf(ctx, driveFileID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *gdrive.DocumentResponse); ok {
		r0 = rf(ctx, driveFileID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, driveFileID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocument'
type MockDocumentService_GetDocument_Call struct {
	*mock.Call
}

// GetDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - driveFileID string
func (_e *MockDocumentService_Expecter) GetDocument(ctx interface{}, driveFileID interface{}) *MockDocumentService_GetDocument_Call {
	return &MockDocumentService_GetDocument_Call{Call: _e.mock.On("GetDocument", ctx, driveFileID)}
}

func (_c *MockDocumentService_GetDocument_Call) Run(run func(ctx context.Context, driveFileID string)) *MockDocumentService_GetDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockDocumentService_GetDocument_Call) Return(_a0 *gdrive.DocumentResponse, _a1 error) *MockDocumentService_GetDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetDocument_Call) RunAndReturn(run func(context.Context, string) (*gdrive.DocumentResponse, error)) *MockDocumentService_GetDocument_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentMappingRepository provides a mock function with no fields
func (_m *MockDocumentService) GetDocumentMappingRepository() repositories.DocumentMappingRepository {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentMappingRepository")
	}

	var r0 repositories.DocumentMappingRepository
	if rf, ok := ret.Get(0).(func() repositories.DocumentMappingRepository); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repositories.DocumentMappingRepository)
		}
	}

	return r0
}

// MockDocumentService_GetDocumentMappingRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentMappingRepository'
type MockDocumentService_GetDocumentMappingRepository_Call struct {
	*mock.Call
}

// GetDocumentMappingRepository is a helper method to define mock.On call
func (_e *MockDocumentService_Expecter) GetDocumentMappingRepository() *MockDocumentService_GetDocumentMappingRepository_Call {
	return &MockDocumentService_GetDocumentMappingRepository_Call{Call: _e.mock.On("GetDocumentMappingRepository")}
}

func (_c *MockDocumentService_GetDocumentMappingRepository_Call) Run(run func()) *MockDocumentService_GetDocumentMappingRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDocumentService_GetDocumentMappingRepository_Call) Return(_a0 repositories.DocumentMappingRepository) *MockDocumentService_GetDocumentMappingRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_GetDocumentMappingRepository_Call) RunAndReturn(run func() repositories.DocumentMappingRepository) *MockDocumentService_GetDocumentMappingRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentRepository provides a mock function with no fields
func (_m *MockDocumentService) GetDocumentRepository() repositories.DocumentRepository {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentRepository")
	}

	var r0 repositories.DocumentRepository
	if rf, ok := ret.Get(0).(func() repositories.DocumentRepository); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repositories.DocumentRepository)
		}
	}

	return r0
}

// MockDocumentService_GetDocumentRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentRepository'
type MockDocumentService_GetDocumentRepository_Call struct {
	*mock.Call
}

// GetDocumentRepository is a helper method to define mock.On call
func (_e *MockDocumentService_Expecter) GetDocumentRepository() *MockDocumentService_GetDocumentRepository_Call {
	return &MockDocumentService_GetDocumentRepository_Call{Call: _e.mock.On("GetDocumentRepository")}
}

func (_c *MockDocumentService_GetDocumentRepository_Call) Run(run func()) *MockDocumentService_GetDocumentRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDocumentService_GetDocumentRepository_Call) Return(_a0 repositories.DocumentRepository) *MockDocumentService_GetDocumentRepository_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_GetDocumentRepository_Call) RunAndReturn(run func() repositories.DocumentRepository) *MockDocumentService_GetDocumentRepository_Call {
	_c.Call.Return(run)
	return _c
}

// GetGDriveClient provides a mock function with no fields
func (_m *MockDocumentService) GetGDriveClient() pkggdrive.DriveClient {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetGDriveClient")
	}

	var r0 pkggdrive.DriveClient
	if rf, ok := ret.Get(0).(func() pkggdrive.DriveClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(pkggdrive.DriveClient)
		}
	}

	return r0
}

// MockDocumentService_GetGDriveClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGDriveClient'
type MockDocumentService_GetGDriveClient_Call struct {
	*mock.Call
}

// GetGDriveClient is a helper method to define mock.On call
func (_e *MockDocumentService_Expecter) GetGDriveClient() *MockDocumentService_GetGDriveClient_Call {
	return &MockDocumentService_GetGDriveClient_Call{Call: _e.mock.On("GetGDriveClient")}
}

func (_c *MockDocumentService_GetGDriveClient_Call) Run(run func()) *MockDocumentService_GetGDriveClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDocumentService_GetGDriveClient_Call) Return(_a0 pkggdrive.DriveClient) *MockDocumentService_GetGDriveClient_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentService_GetGDriveClient_Call) RunAndReturn(run func() pkggdrive.DriveClient) *MockDocumentService_GetGDriveClient_Call {
	_c.Call.Return(run)
	return _c
}

// GetTenantConfig provides a mock function with given fields: ctx, tenantID
func (_m *MockDocumentService) GetTenantConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetTenantConfig")
	}

	var r0 *model.GDriveConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) (*model.GDriveConfig, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) *model.GDriveConfig); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.GDriveConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetTenantConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTenantConfig'
type MockDocumentService_GetTenantConfig_Call struct {
	*mock.Call
}

// GetTenantConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockDocumentService_Expecter) GetTenantConfig(ctx interface{}, tenantID interface{}) *MockDocumentService_GetTenantConfig_Call {
	return &MockDocumentService_GetTenantConfig_Call{Call: _e.mock.On("GetTenantConfig", ctx, tenantID)}
}

func (_c *MockDocumentService_GetTenantConfig_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockDocumentService_GetTenantConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockDocumentService_GetTenantConfig_Call) Return(_a0 *model.GDriveConfig, _a1 error) *MockDocumentService_GetTenantConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetTenantConfig_Call) RunAndReturn(run func(context.Context, uint64) (*model.GDriveConfig, error)) *MockDocumentService_GetTenantConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetUploadSession provides a mock function with given fields: ctx, sessionToken
func (_m *MockDocumentService) GetUploadSession(ctx context.Context, sessionToken string) (*gdrive.UploadSessionResponse, error) {
	ret := _m.Called(ctx, sessionToken)

	if len(ret) == 0 {
		panic("no return value specified for GetUploadSession")
	}

	var r0 *gdrive.UploadSessionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*gdrive.UploadSessionResponse, error)); ok {
		return rf(ctx, sessionToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *gdrive.UploadSessionResponse); ok {
		r0 = rf(ctx, sessionToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.UploadSessionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, sessionToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_GetUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUploadSession'
type MockDocumentService_GetUploadSession_Call struct {
	*mock.Call
}

// GetUploadSession is a helper method to define mock.On call
//   - ctx context.Context
//   - sessionToken string
func (_e *MockDocumentService_Expecter) GetUploadSession(ctx interface{}, sessionToken interface{}) *MockDocumentService_GetUploadSession_Call {
	return &MockDocumentService_GetUploadSession_Call{Call: _e.mock.On("GetUploadSession", ctx, sessionToken)}
}

func (_c *MockDocumentService_GetUploadSession_Call) Run(run func(ctx context.Context, sessionToken string)) *MockDocumentService_GetUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockDocumentService_GetUploadSession_Call) Return(_a0 *gdrive.UploadSessionResponse, _a1 error) *MockDocumentService_GetUploadSession_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_GetUploadSession_Call) RunAndReturn(run func(context.Context, string) (*gdrive.UploadSessionResponse, error)) *MockDocumentService_GetUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// ListDocuments provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) ListDocuments(ctx context.Context, req *gdrive.ListDocumentsRequest) (*gdrive.DocumentListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListDocuments")
	}

	var r0 *gdrive.DocumentListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.ListDocumentsRequest) (*gdrive.DocumentListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.ListDocumentsRequest) *gdrive.DocumentListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DocumentListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.ListDocumentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_ListDocuments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDocuments'
type MockDocumentService_ListDocuments_Call struct {
	*mock.Call
}

// ListDocuments is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.ListDocumentsRequest
func (_e *MockDocumentService_Expecter) ListDocuments(ctx interface{}, req interface{}) *MockDocumentService_ListDocuments_Call {
	return &MockDocumentService_ListDocuments_Call{Call: _e.mock.On("ListDocuments", ctx, req)}
}

func (_c *MockDocumentService_ListDocuments_Call) Run(run func(ctx context.Context, req *gdrive.ListDocumentsRequest)) *MockDocumentService_ListDocuments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.ListDocumentsRequest))
	})
	return _c
}

func (_c *MockDocumentService_ListDocuments_Call) Return(_a0 *gdrive.DocumentListResponse, _a1 error) *MockDocumentService_ListDocuments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_ListDocuments_Call) RunAndReturn(run func(context.Context, *gdrive.ListDocumentsRequest) (*gdrive.DocumentListResponse, error)) *MockDocumentService_ListDocuments_Call {
	_c.Call.Return(run)
	return _c
}

// ResolveHierarchicalPath provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) ResolveHierarchicalPath(ctx context.Context, req *gdrive.ResolveHierarchicalPathRequest) (*gdrive.ResolveHierarchicalPathResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ResolveHierarchicalPath")
	}

	var r0 *gdrive.ResolveHierarchicalPathResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.ResolveHierarchicalPathRequest) (*gdrive.ResolveHierarchicalPathResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.ResolveHierarchicalPathRequest) *gdrive.ResolveHierarchicalPathResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.ResolveHierarchicalPathResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.ResolveHierarchicalPathRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_ResolveHierarchicalPath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResolveHierarchicalPath'
type MockDocumentService_ResolveHierarchicalPath_Call struct {
	*mock.Call
}

// ResolveHierarchicalPath is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.ResolveHierarchicalPathRequest
func (_e *MockDocumentService_Expecter) ResolveHierarchicalPath(ctx interface{}, req interface{}) *MockDocumentService_ResolveHierarchicalPath_Call {
	return &MockDocumentService_ResolveHierarchicalPath_Call{Call: _e.mock.On("ResolveHierarchicalPath", ctx, req)}
}

func (_c *MockDocumentService_ResolveHierarchicalPath_Call) Run(run func(ctx context.Context, req *gdrive.ResolveHierarchicalPathRequest)) *MockDocumentService_ResolveHierarchicalPath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.ResolveHierarchicalPathRequest))
	})
	return _c
}

func (_c *MockDocumentService_ResolveHierarchicalPath_Call) Return(_a0 *gdrive.ResolveHierarchicalPathResponse, _a1 error) *MockDocumentService_ResolveHierarchicalPath_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_ResolveHierarchicalPath_Call) RunAndReturn(run func(context.Context, *gdrive.ResolveHierarchicalPathRequest) (*gdrive.ResolveHierarchicalPathResponse, error)) *MockDocumentService_ResolveHierarchicalPath_Call {
	_c.Call.Return(run)
	return _c
}

// SearchDocuments provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) SearchDocuments(ctx context.Context, req *gdrive.SearchDocumentsRequest) (*gdrive.DocumentListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SearchDocuments")
	}

	var r0 *gdrive.DocumentListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.SearchDocumentsRequest) (*gdrive.DocumentListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.SearchDocumentsRequest) *gdrive.DocumentListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DocumentListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.SearchDocumentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_SearchDocuments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchDocuments'
type MockDocumentService_SearchDocuments_Call struct {
	*mock.Call
}

// SearchDocuments is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.SearchDocumentsRequest
func (_e *MockDocumentService_Expecter) SearchDocuments(ctx interface{}, req interface{}) *MockDocumentService_SearchDocuments_Call {
	return &MockDocumentService_SearchDocuments_Call{Call: _e.mock.On("SearchDocuments", ctx, req)}
}

func (_c *MockDocumentService_SearchDocuments_Call) Run(run func(ctx context.Context, req *gdrive.SearchDocumentsRequest)) *MockDocumentService_SearchDocuments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.SearchDocumentsRequest))
	})
	return _c
}

func (_c *MockDocumentService_SearchDocuments_Call) Return(_a0 *gdrive.DocumentListResponse, _a1 error) *MockDocumentService_SearchDocuments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_SearchDocuments_Call) RunAndReturn(run func(context.Context, *gdrive.SearchDocumentsRequest) (*gdrive.DocumentListResponse, error)) *MockDocumentService_SearchDocuments_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDocument provides a mock function with given fields: ctx, driveFileID, req
func (_m *MockDocumentService) UpdateDocument(ctx context.Context, driveFileID string, req *gdrive.UpdateDocumentRequest) (*gdrive.DocumentResponse, error) {
	ret := _m.Called(ctx, driveFileID, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDocument")
	}

	var r0 *gdrive.DocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *gdrive.UpdateDocumentRequest) (*gdrive.DocumentResponse, error)); ok {
		return rf(ctx, driveFileID, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *gdrive.UpdateDocumentRequest) *gdrive.DocumentResponse); ok {
		r0 = rf(ctx, driveFileID, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *gdrive.UpdateDocumentRequest) error); ok {
		r1 = rf(ctx, driveFileID, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_UpdateDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDocument'
type MockDocumentService_UpdateDocument_Call struct {
	*mock.Call
}

// UpdateDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - driveFileID string
//   - req *gdrive.UpdateDocumentRequest
func (_e *MockDocumentService_Expecter) UpdateDocument(ctx interface{}, driveFileID interface{}, req interface{}) *MockDocumentService_UpdateDocument_Call {
	return &MockDocumentService_UpdateDocument_Call{Call: _e.mock.On("UpdateDocument", ctx, driveFileID, req)}
}

func (_c *MockDocumentService_UpdateDocument_Call) Run(run func(ctx context.Context, driveFileID string, req *gdrive.UpdateDocumentRequest)) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*gdrive.UpdateDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_UpdateDocument_Call) Return(_a0 *gdrive.DocumentResponse, _a1 error) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_UpdateDocument_Call) RunAndReturn(run func(context.Context, string, *gdrive.UpdateDocumentRequest) (*gdrive.DocumentResponse, error)) *MockDocumentService_UpdateDocument_Call {
	_c.Call.Return(run)
	return _c
}

// UploadDocument provides a mock function with given fields: ctx, req
func (_m *MockDocumentService) UploadDocument(ctx context.Context, req *gdrive.UploadDocumentRequest) (*gdrive.UploadDocumentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UploadDocument")
	}

	var r0 *gdrive.UploadDocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.UploadDocumentRequest) (*gdrive.UploadDocumentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.UploadDocumentRequest) *gdrive.UploadDocumentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.UploadDocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.UploadDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_UploadDocument_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadDocument'
type MockDocumentService_UploadDocument_Call struct {
	*mock.Call
}

// UploadDocument is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.UploadDocumentRequest
func (_e *MockDocumentService_Expecter) UploadDocument(ctx interface{}, req interface{}) *MockDocumentService_UploadDocument_Call {
	return &MockDocumentService_UploadDocument_Call{Call: _e.mock.On("UploadDocument", ctx, req)}
}

func (_c *MockDocumentService_UploadDocument_Call) Run(run func(ctx context.Context, req *gdrive.UploadDocumentRequest)) *MockDocumentService_UploadDocument_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.UploadDocumentRequest))
	})
	return _c
}

func (_c *MockDocumentService_UploadDocument_Call) Return(_a0 *gdrive.UploadDocumentResponse, _a1 error) *MockDocumentService_UploadDocument_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_UploadDocument_Call) RunAndReturn(run func(context.Context, *gdrive.UploadDocumentRequest) (*gdrive.UploadDocumentResponse, error)) *MockDocumentService_UploadDocument_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFileContent provides a mock function with given fields: ctx, sessionToken, content, contentType, contentLength
func (_m *MockDocumentService) UploadFileContent(ctx context.Context, sessionToken string, content io.Reader, contentType string, contentLength int64) (*gdrive.UploadContentResponse, error) {
	ret := _m.Called(ctx, sessionToken, content, contentType, contentLength)

	if len(ret) == 0 {
		panic("no return value specified for UploadFileContent")
	}

	var r0 *gdrive.UploadContentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, io.Reader, string, int64) (*gdrive.UploadContentResponse, error)); ok {
		return rf(ctx, sessionToken, content, contentType, contentLength)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, io.Reader, string, int64) *gdrive.UploadContentResponse); ok {
		r0 = rf(ctx, sessionToken, content, contentType, contentLength)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.UploadContentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, io.Reader, string, int64) error); ok {
		r1 = rf(ctx, sessionToken, content, contentType, contentLength)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentService_UploadFileContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFileContent'
type MockDocumentService_UploadFileContent_Call struct {
	*mock.Call
}

// UploadFileContent is a helper method to define mock.On call
//   - ctx context.Context
//   - sessionToken string
//   - content io.Reader
//   - contentType string
//   - contentLength int64
func (_e *MockDocumentService_Expecter) UploadFileContent(ctx interface{}, sessionToken interface{}, content interface{}, contentType interface{}, contentLength interface{}) *MockDocumentService_UploadFileContent_Call {
	return &MockDocumentService_UploadFileContent_Call{Call: _e.mock.On("UploadFileContent", ctx, sessionToken, content, contentType, contentLength)}
}

func (_c *MockDocumentService_UploadFileContent_Call) Run(run func(ctx context.Context, sessionToken string, content io.Reader, contentType string, contentLength int64)) *MockDocumentService_UploadFileContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(io.Reader), args[3].(string), args[4].(int64))
	})
	return _c
}

func (_c *MockDocumentService_UploadFileContent_Call) Return(_a0 *gdrive.UploadContentResponse, _a1 error) *MockDocumentService_UploadFileContent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentService_UploadFileContent_Call) RunAndReturn(run func(context.Context, string, io.Reader, string, int64) (*gdrive.UploadContentResponse, error)) *MockDocumentService_UploadFileContent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentService creates a new instance of MockDocumentService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentService {
	mock := &MockDocumentService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
