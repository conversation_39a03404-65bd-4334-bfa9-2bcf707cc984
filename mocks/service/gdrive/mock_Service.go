// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/internal/service/gdrive"
	context "context"

	mock "github.com/stretchr/testify/mock"

	pkggdrive "bilabl/docman/pkg/gdrive"

	repositories "bilabl/docman/pkg/repositories"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// CompleteSetup provides a mock function with given fields: ctx, req
func (_m *MockService) CompleteSetup(ctx context.Context, req *gdrive.CompleteSetupRequest) (*gdrive.CompleteSetupResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CompleteSetup")
	}

	var r0 *gdrive.CompleteSetupResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CompleteSetupRequest) (*gdrive.CompleteSetupResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CompleteSetupRequest) *gdrive.CompleteSetupResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CompleteSetupResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CompleteSetupRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CompleteSetup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CompleteSetup'
type MockService_CompleteSetup_Call struct {
	*mock.Call
}

// CompleteSetup is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CompleteSetupRequest
func (_e *MockService_Expecter) CompleteSetup(ctx interface{}, req interface{}) *MockService_CompleteSetup_Call {
	return &MockService_CompleteSetup_Call{Call: _e.mock.On("CompleteSetup", ctx, req)}
}

func (_c *MockService_CompleteSetup_Call) Run(run func(ctx context.Context, req *gdrive.CompleteSetupRequest)) *MockService_CompleteSetup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CompleteSetupRequest))
	})
	return _c
}

func (_c *MockService_CompleteSetup_Call) Return(_a0 *gdrive.CompleteSetupResponse, _a1 error) *MockService_CompleteSetup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CompleteSetup_Call) RunAndReturn(run func(context.Context, *gdrive.CompleteSetupRequest) (*gdrive.CompleteSetupResponse, error)) *MockService_CompleteSetup_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClientFolder provides a mock function with given fields: ctx, req
func (_m *MockService) CreateClientFolder(ctx context.Context, req *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateClientFolder")
	}

	var r0 *gdrive.CreateClientFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateClientFolderRequest) *gdrive.CreateClientFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateClientFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateClientFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateClientFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClientFolder'
type MockService_CreateClientFolder_Call struct {
	*mock.Call
}

// CreateClientFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateClientFolderRequest
func (_e *MockService_Expecter) CreateClientFolder(ctx interface{}, req interface{}) *MockService_CreateClientFolder_Call {
	return &MockService_CreateClientFolder_Call{Call: _e.mock.On("CreateClientFolder", ctx, req)}
}

func (_c *MockService_CreateClientFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateClientFolderRequest)) *MockService_CreateClientFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateClientFolderRequest))
	})
	return _c
}

func (_c *MockService_CreateClientFolder_Call) Return(_a0 *gdrive.CreateClientFolderResponse, _a1 error) *MockService_CreateClientFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateClientFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error)) *MockService_CreateClientFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateMatterFolder provides a mock function with given fields: ctx, req
func (_m *MockService) CreateMatterFolder(ctx context.Context, req *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateMatterFolder")
	}

	var r0 *gdrive.CreateMatterFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterFolderRequest) *gdrive.CreateMatterFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateMatterFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateMatterFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateMatterFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMatterFolder'
type MockService_CreateMatterFolder_Call struct {
	*mock.Call
}

// CreateMatterFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateMatterFolderRequest
func (_e *MockService_Expecter) CreateMatterFolder(ctx interface{}, req interface{}) *MockService_CreateMatterFolder_Call {
	return &MockService_CreateMatterFolder_Call{Call: _e.mock.On("CreateMatterFolder", ctx, req)}
}

func (_c *MockService_CreateMatterFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateMatterFolderRequest)) *MockService_CreateMatterFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateMatterFolderRequest))
	})
	return _c
}

func (_c *MockService_CreateMatterFolder_Call) Return(_a0 *gdrive.CreateMatterFolderResponse, _a1 error) *MockService_CreateMatterFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateMatterFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error)) *MockService_CreateMatterFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateMatterParentFolder provides a mock function with given fields: ctx, req
func (_m *MockService) CreateMatterParentFolder(ctx context.Context, req *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateMatterParentFolder")
	}

	var r0 *gdrive.CreateMatterParentFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) *gdrive.CreateMatterParentFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateMatterParentFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateMatterParentFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMatterParentFolder'
type MockService_CreateMatterParentFolder_Call struct {
	*mock.Call
}

// CreateMatterParentFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateMatterParentFolderRequest
func (_e *MockService_Expecter) CreateMatterParentFolder(ctx interface{}, req interface{}) *MockService_CreateMatterParentFolder_Call {
	return &MockService_CreateMatterParentFolder_Call{Call: _e.mock.On("CreateMatterParentFolder", ctx, req)}
}

func (_c *MockService_CreateMatterParentFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateMatterParentFolderRequest)) *MockService_CreateMatterParentFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateMatterParentFolderRequest))
	})
	return _c
}

func (_c *MockService_CreateMatterParentFolder_Call) Return(_a0 *gdrive.CreateMatterParentFolderResponse, _a1 error) *MockService_CreateMatterParentFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateMatterParentFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error)) *MockService_CreateMatterParentFolder_Call {
	_c.Call.Return(run)
	return _c
}

// GetClientEventConsumer provides a mock function with no fields
func (_m *MockService) GetClientEventConsumer() *gdrive.ClientEventConsumer {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetClientEventConsumer")
	}

	var r0 *gdrive.ClientEventConsumer
	if rf, ok := ret.Get(0).(func() *gdrive.ClientEventConsumer); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.ClientEventConsumer)
		}
	}

	return r0
}

// MockService_GetClientEventConsumer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClientEventConsumer'
type MockService_GetClientEventConsumer_Call struct {
	*mock.Call
}

// GetClientEventConsumer is a helper method to define mock.On call
func (_e *MockService_Expecter) GetClientEventConsumer() *MockService_GetClientEventConsumer_Call {
	return &MockService_GetClientEventConsumer_Call{Call: _e.mock.On("GetClientEventConsumer")}
}

func (_c *MockService_GetClientEventConsumer_Call) Run(run func()) *MockService_GetClientEventConsumer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetClientEventConsumer_Call) Return(_a0 *gdrive.ClientEventConsumer) *MockService_GetClientEventConsumer_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetClientEventConsumer_Call) RunAndReturn(run func() *gdrive.ClientEventConsumer) *MockService_GetClientEventConsumer_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentMappingRepo provides a mock function with no fields
func (_m *MockService) GetDocumentMappingRepo() repositories.DocumentMappingRepository {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentMappingRepo")
	}

	var r0 repositories.DocumentMappingRepository
	if rf, ok := ret.Get(0).(func() repositories.DocumentMappingRepository); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repositories.DocumentMappingRepository)
		}
	}

	return r0
}

// MockService_GetDocumentMappingRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentMappingRepo'
type MockService_GetDocumentMappingRepo_Call struct {
	*mock.Call
}

// GetDocumentMappingRepo is a helper method to define mock.On call
func (_e *MockService_Expecter) GetDocumentMappingRepo() *MockService_GetDocumentMappingRepo_Call {
	return &MockService_GetDocumentMappingRepo_Call{Call: _e.mock.On("GetDocumentMappingRepo")}
}

func (_c *MockService_GetDocumentMappingRepo_Call) Run(run func()) *MockService_GetDocumentMappingRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetDocumentMappingRepo_Call) Return(_a0 repositories.DocumentMappingRepository) *MockService_GetDocumentMappingRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetDocumentMappingRepo_Call) RunAndReturn(run func() repositories.DocumentMappingRepository) *MockService_GetDocumentMappingRepo_Call {
	_c.Call.Return(run)
	return _c
}

// GetDocumentSettingRepo provides a mock function with no fields
func (_m *MockService) GetDocumentSettingRepo() repositories.DocumentSettingRepository {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDocumentSettingRepo")
	}

	var r0 repositories.DocumentSettingRepository
	if rf, ok := ret.Get(0).(func() repositories.DocumentSettingRepository); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repositories.DocumentSettingRepository)
		}
	}

	return r0
}

// MockService_GetDocumentSettingRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDocumentSettingRepo'
type MockService_GetDocumentSettingRepo_Call struct {
	*mock.Call
}

// GetDocumentSettingRepo is a helper method to define mock.On call
func (_e *MockService_Expecter) GetDocumentSettingRepo() *MockService_GetDocumentSettingRepo_Call {
	return &MockService_GetDocumentSettingRepo_Call{Call: _e.mock.On("GetDocumentSettingRepo")}
}

func (_c *MockService_GetDocumentSettingRepo_Call) Run(run func()) *MockService_GetDocumentSettingRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetDocumentSettingRepo_Call) Return(_a0 repositories.DocumentSettingRepository) *MockService_GetDocumentSettingRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetDocumentSettingRepo_Call) RunAndReturn(run func() repositories.DocumentSettingRepository) *MockService_GetDocumentSettingRepo_Call {
	_c.Call.Return(run)
	return _c
}

// GetFolderService provides a mock function with no fields
func (_m *MockService) GetFolderService() gdrive.FolderService {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetFolderService")
	}

	var r0 gdrive.FolderService
	if rf, ok := ret.Get(0).(func() gdrive.FolderService); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FolderService)
		}
	}

	return r0
}

// MockService_GetFolderService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFolderService'
type MockService_GetFolderService_Call struct {
	*mock.Call
}

// GetFolderService is a helper method to define mock.On call
func (_e *MockService_Expecter) GetFolderService() *MockService_GetFolderService_Call {
	return &MockService_GetFolderService_Call{Call: _e.mock.On("GetFolderService")}
}

func (_c *MockService_GetFolderService_Call) Run(run func()) *MockService_GetFolderService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetFolderService_Call) Return(_a0 gdrive.FolderService) *MockService_GetFolderService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetFolderService_Call) RunAndReturn(run func() gdrive.FolderService) *MockService_GetFolderService_Call {
	_c.Call.Return(run)
	return _c
}

// GetMatterEventConsumer provides a mock function with no fields
func (_m *MockService) GetMatterEventConsumer() *gdrive.MatterEventConsumer {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetMatterEventConsumer")
	}

	var r0 *gdrive.MatterEventConsumer
	if rf, ok := ret.Get(0).(func() *gdrive.MatterEventConsumer); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.MatterEventConsumer)
		}
	}

	return r0
}

// MockService_GetMatterEventConsumer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMatterEventConsumer'
type MockService_GetMatterEventConsumer_Call struct {
	*mock.Call
}

// GetMatterEventConsumer is a helper method to define mock.On call
func (_e *MockService_Expecter) GetMatterEventConsumer() *MockService_GetMatterEventConsumer_Call {
	return &MockService_GetMatterEventConsumer_Call{Call: _e.mock.On("GetMatterEventConsumer")}
}

func (_c *MockService_GetMatterEventConsumer_Call) Run(run func()) *MockService_GetMatterEventConsumer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetMatterEventConsumer_Call) Return(_a0 *gdrive.MatterEventConsumer) *MockService_GetMatterEventConsumer_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetMatterEventConsumer_Call) RunAndReturn(run func() *gdrive.MatterEventConsumer) *MockService_GetMatterEventConsumer_Call {
	_c.Call.Return(run)
	return _c
}

// SetPermissionSyncer provides a mock function with given fields: syncer
func (_m *MockService) SetPermissionSyncer(syncer gdrive.PermissionSyncer) {
	_m.Called(syncer)
}

// MockService_SetPermissionSyncer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetPermissionSyncer'
type MockService_SetPermissionSyncer_Call struct {
	*mock.Call
}

// SetPermissionSyncer is a helper method to define mock.On call
//   - syncer gdrive.PermissionSyncer
func (_e *MockService_Expecter) SetPermissionSyncer(syncer interface{}) *MockService_SetPermissionSyncer_Call {
	return &MockService_SetPermissionSyncer_Call{Call: _e.mock.On("SetPermissionSyncer", syncer)}
}

func (_c *MockService_SetPermissionSyncer_Call) Run(run func(syncer gdrive.PermissionSyncer)) *MockService_SetPermissionSyncer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(gdrive.PermissionSyncer))
	})
	return _c
}

func (_c *MockService_SetPermissionSyncer_Call) Return() *MockService_SetPermissionSyncer_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockService_SetPermissionSyncer_Call) RunAndReturn(run func(gdrive.PermissionSyncer)) *MockService_SetPermissionSyncer_Call {
	_c.Run(run)
	return _c
}

// TestSetup provides a mock function with given fields: ctx, urlOrID
func (_m *MockService) TestSetup(ctx context.Context, urlOrID string) (*pkggdrive.DriveInfo, error) {
	ret := _m.Called(ctx, urlOrID)

	if len(ret) == 0 {
		panic("no return value specified for TestSetup")
	}

	var r0 *pkggdrive.DriveInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*pkggdrive.DriveInfo, error)); ok {
		return rf(ctx, urlOrID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *pkggdrive.DriveInfo); ok {
		r0 = rf(ctx, urlOrID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pkggdrive.DriveInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, urlOrID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_TestSetup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TestSetup'
type MockService_TestSetup_Call struct {
	*mock.Call
}

// TestSetup is a helper method to define mock.On call
//   - ctx context.Context
//   - urlOrID string
func (_e *MockService_Expecter) TestSetup(ctx interface{}, urlOrID interface{}) *MockService_TestSetup_Call {
	return &MockService_TestSetup_Call{Call: _e.mock.On("TestSetup", ctx, urlOrID)}
}

func (_c *MockService_TestSetup_Call) Run(run func(ctx context.Context, urlOrID string)) *MockService_TestSetup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_TestSetup_Call) Return(_a0 *pkggdrive.DriveInfo, _a1 error) *MockService_TestSetup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_TestSetup_Call) RunAndReturn(run func(context.Context, string) (*pkggdrive.DriveInfo, error)) *MockService_TestSetup_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
