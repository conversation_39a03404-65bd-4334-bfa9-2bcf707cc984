// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockPermissionSyncer is an autogenerated mock type for the PermissionSyncer type
type MockPermissionSyncer struct {
	mock.Mock
}

type MockPermissionSyncer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPermissionSyncer) EXPECT() *MockPermissionSyncer_Expecter {
	return &MockPermissionSyncer_Expecter{mock: &_m.Mock}
}

// SyncGoogleDrivePermissions provides a mock function with given fields: ctx, tenantID, objectType, objectID, ownerEmails
func (_m *MockPermissionSyncer) SyncGoogleDrivePermissions(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string) error {
	ret := _m.Called(ctx, tenantID, objectType, objectID, ownerEmails)

	if len(ret) == 0 {
		panic("no return value specified for SyncGoogleDrivePermissions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, uint64, []string) error); ok {
		r0 = rf(ctx, tenantID, objectType, objectID, ownerEmails)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPermissionSyncer_SyncGoogleDrivePermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncGoogleDrivePermissions'
type MockPermissionSyncer_SyncGoogleDrivePermissions_Call struct {
	*mock.Call
}

// SyncGoogleDrivePermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - objectType string
//   - objectID uint64
//   - ownerEmails []string
func (_e *MockPermissionSyncer_Expecter) SyncGoogleDrivePermissions(ctx interface{}, tenantID interface{}, objectType interface{}, objectID interface{}, ownerEmails interface{}) *MockPermissionSyncer_SyncGoogleDrivePermissions_Call {
	return &MockPermissionSyncer_SyncGoogleDrivePermissions_Call{Call: _e.mock.On("SyncGoogleDrivePermissions", ctx, tenantID, objectType, objectID, ownerEmails)}
}

func (_c *MockPermissionSyncer_SyncGoogleDrivePermissions_Call) Run(run func(ctx context.Context, tenantID uint64, objectType string, objectID uint64, ownerEmails []string)) *MockPermissionSyncer_SyncGoogleDrivePermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(uint64), args[4].([]string))
	})
	return _c
}

func (_c *MockPermissionSyncer_SyncGoogleDrivePermissions_Call) Return(_a0 error) *MockPermissionSyncer_SyncGoogleDrivePermissions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionSyncer_SyncGoogleDrivePermissions_Call) RunAndReturn(run func(context.Context, uint64, string, uint64, []string) error) *MockPermissionSyncer_SyncGoogleDrivePermissions_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPermissionSyncer creates a new instance of MockPermissionSyncer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPermissionSyncer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPermissionSyncer {
	mock := &MockPermissionSyncer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
