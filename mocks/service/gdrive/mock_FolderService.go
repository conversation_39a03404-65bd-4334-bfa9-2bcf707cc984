// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/internal/service/gdrive"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockFolderService is an autogenerated mock type for the FolderService type
type MockFolderService struct {
	mock.Mock
}

type MockFolderService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFolderService) EXPECT() *MockFolderService_Expecter {
	return &MockFolderService_Expecter{mock: &_m.Mock}
}

// CreateClientFolder provides a mock function with given fields: ctx, req
func (_m *MockFolderService) CreateClientFolder(ctx context.Context, req *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateClientFolder")
	}

	var r0 *gdrive.CreateClientFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateClientFolderRequest) *gdrive.CreateClientFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateClientFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateClientFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFolderService_CreateClientFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClientFolder'
type MockFolderService_CreateClientFolder_Call struct {
	*mock.Call
}

// CreateClientFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateClientFolderRequest
func (_e *MockFolderService_Expecter) CreateClientFolder(ctx interface{}, req interface{}) *MockFolderService_CreateClientFolder_Call {
	return &MockFolderService_CreateClientFolder_Call{Call: _e.mock.On("CreateClientFolder", ctx, req)}
}

func (_c *MockFolderService_CreateClientFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateClientFolderRequest)) *MockFolderService_CreateClientFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateClientFolderRequest))
	})
	return _c
}

func (_c *MockFolderService_CreateClientFolder_Call) Return(_a0 *gdrive.CreateClientFolderResponse, _a1 error) *MockFolderService_CreateClientFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFolderService_CreateClientFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateClientFolderRequest) (*gdrive.CreateClientFolderResponse, error)) *MockFolderService_CreateClientFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateMatterFolder provides a mock function with given fields: ctx, req
func (_m *MockFolderService) CreateMatterFolder(ctx context.Context, req *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateMatterFolder")
	}

	var r0 *gdrive.CreateMatterFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterFolderRequest) *gdrive.CreateMatterFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateMatterFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateMatterFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFolderService_CreateMatterFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMatterFolder'
type MockFolderService_CreateMatterFolder_Call struct {
	*mock.Call
}

// CreateMatterFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateMatterFolderRequest
func (_e *MockFolderService_Expecter) CreateMatterFolder(ctx interface{}, req interface{}) *MockFolderService_CreateMatterFolder_Call {
	return &MockFolderService_CreateMatterFolder_Call{Call: _e.mock.On("CreateMatterFolder", ctx, req)}
}

func (_c *MockFolderService_CreateMatterFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateMatterFolderRequest)) *MockFolderService_CreateMatterFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateMatterFolderRequest))
	})
	return _c
}

func (_c *MockFolderService_CreateMatterFolder_Call) Return(_a0 *gdrive.CreateMatterFolderResponse, _a1 error) *MockFolderService_CreateMatterFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFolderService_CreateMatterFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateMatterFolderRequest) (*gdrive.CreateMatterFolderResponse, error)) *MockFolderService_CreateMatterFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateMatterParentFolder provides a mock function with given fields: ctx, req
func (_m *MockFolderService) CreateMatterParentFolder(ctx context.Context, req *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateMatterParentFolder")
	}

	var r0 *gdrive.CreateMatterParentFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) *gdrive.CreateMatterParentFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.CreateMatterParentFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.CreateMatterParentFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFolderService_CreateMatterParentFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMatterParentFolder'
type MockFolderService_CreateMatterParentFolder_Call struct {
	*mock.Call
}

// CreateMatterParentFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.CreateMatterParentFolderRequest
func (_e *MockFolderService_Expecter) CreateMatterParentFolder(ctx interface{}, req interface{}) *MockFolderService_CreateMatterParentFolder_Call {
	return &MockFolderService_CreateMatterParentFolder_Call{Call: _e.mock.On("CreateMatterParentFolder", ctx, req)}
}

func (_c *MockFolderService_CreateMatterParentFolder_Call) Run(run func(ctx context.Context, req *gdrive.CreateMatterParentFolderRequest)) *MockFolderService_CreateMatterParentFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.CreateMatterParentFolderRequest))
	})
	return _c
}

func (_c *MockFolderService_CreateMatterParentFolder_Call) Return(_a0 *gdrive.CreateMatterParentFolderResponse, _a1 error) *MockFolderService_CreateMatterParentFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFolderService_CreateMatterParentFolder_Call) RunAndReturn(run func(context.Context, *gdrive.CreateMatterParentFolderRequest) (*gdrive.CreateMatterParentFolderResponse, error)) *MockFolderService_CreateMatterParentFolder_Call {
	_c.Call.Return(run)
	return _c
}

// RenameClientFolder provides a mock function with given fields: ctx, req
func (_m *MockFolderService) RenameClientFolder(ctx context.Context, req *gdrive.RenameClientFolderRequest) (*gdrive.RenameClientFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RenameClientFolder")
	}

	var r0 *gdrive.RenameClientFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.RenameClientFolderRequest) (*gdrive.RenameClientFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.RenameClientFolderRequest) *gdrive.RenameClientFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.RenameClientFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.RenameClientFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFolderService_RenameClientFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RenameClientFolder'
type MockFolderService_RenameClientFolder_Call struct {
	*mock.Call
}

// RenameClientFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.RenameClientFolderRequest
func (_e *MockFolderService_Expecter) RenameClientFolder(ctx interface{}, req interface{}) *MockFolderService_RenameClientFolder_Call {
	return &MockFolderService_RenameClientFolder_Call{Call: _e.mock.On("RenameClientFolder", ctx, req)}
}

func (_c *MockFolderService_RenameClientFolder_Call) Run(run func(ctx context.Context, req *gdrive.RenameClientFolderRequest)) *MockFolderService_RenameClientFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.RenameClientFolderRequest))
	})
	return _c
}

func (_c *MockFolderService_RenameClientFolder_Call) Return(_a0 *gdrive.RenameClientFolderResponse, _a1 error) *MockFolderService_RenameClientFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFolderService_RenameClientFolder_Call) RunAndReturn(run func(context.Context, *gdrive.RenameClientFolderRequest) (*gdrive.RenameClientFolderResponse, error)) *MockFolderService_RenameClientFolder_Call {
	_c.Call.Return(run)
	return _c
}

// RenameMatterFolder provides a mock function with given fields: ctx, req
func (_m *MockFolderService) RenameMatterFolder(ctx context.Context, req *gdrive.RenameMatterFolderRequest) (*gdrive.RenameMatterFolderResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RenameMatterFolder")
	}

	var r0 *gdrive.RenameMatterFolderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.RenameMatterFolderRequest) (*gdrive.RenameMatterFolderResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *gdrive.RenameMatterFolderRequest) *gdrive.RenameMatterFolderResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.RenameMatterFolderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *gdrive.RenameMatterFolderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFolderService_RenameMatterFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RenameMatterFolder'
type MockFolderService_RenameMatterFolder_Call struct {
	*mock.Call
}

// RenameMatterFolder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *gdrive.RenameMatterFolderRequest
func (_e *MockFolderService_Expecter) RenameMatterFolder(ctx interface{}, req interface{}) *MockFolderService_RenameMatterFolder_Call {
	return &MockFolderService_RenameMatterFolder_Call{Call: _e.mock.On("RenameMatterFolder", ctx, req)}
}

func (_c *MockFolderService_RenameMatterFolder_Call) Run(run func(ctx context.Context, req *gdrive.RenameMatterFolderRequest)) *MockFolderService_RenameMatterFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*gdrive.RenameMatterFolderRequest))
	})
	return _c
}

func (_c *MockFolderService_RenameMatterFolder_Call) Return(_a0 *gdrive.RenameMatterFolderResponse, _a1 error) *MockFolderService_RenameMatterFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFolderService_RenameMatterFolder_Call) RunAndReturn(run func(context.Context, *gdrive.RenameMatterFolderRequest) (*gdrive.RenameMatterFolderResponse, error)) *MockFolderService_RenameMatterFolder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFolderService creates a new instance of MockFolderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFolderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFolderService {
	mock := &MockFolderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
