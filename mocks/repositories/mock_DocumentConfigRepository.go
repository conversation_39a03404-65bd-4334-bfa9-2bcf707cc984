// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockDocumentConfigRepository is an autogenerated mock type for the DocumentConfigRepository type
type MockDocumentConfigRepository struct {
	mock.Mock
}

type MockDocumentConfigRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentConfigRepository) EXPECT() *MockDocumentConfigRepository_Expecter {
	return &MockDocumentConfigRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, documentConfig
func (_m *MockDocumentConfigRepository) Create(ctx context.Context, documentConfig *model.DocumentConfig) error {
	ret := _m.Called(ctx, documentConfig)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentConfig) error); ok {
		r0 = rf(ctx, documentConfig)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentConfigRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentConfigRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - documentConfig *model.DocumentConfig
func (_e *MockDocumentConfigRepository_Expecter) Create(ctx interface{}, documentConfig interface{}) *MockDocumentConfigRepository_Create_Call {
	return &MockDocumentConfigRepository_Create_Call{Call: _e.mock.On("Create", ctx, documentConfig)}
}

func (_c *MockDocumentConfigRepository_Create_Call) Run(run func(ctx context.Context, documentConfig *model.DocumentConfig)) *MockDocumentConfigRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentConfig))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_Create_Call) Return(err error) *MockDocumentConfigRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentConfigRepository_Create_Call) RunAndReturn(run func(context.Context, *model.DocumentConfig) error) *MockDocumentConfigRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentConfigRepository) DeleteOne(ctx context.Context, query *model.Query) (uint64, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOne")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (uint64, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) uint64); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentConfigRepository_DeleteOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOne'
type MockDocumentConfigRepository_DeleteOne_Call struct {
	*mock.Call
}

// DeleteOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentConfigRepository_Expecter) DeleteOne(ctx interface{}, query interface{}) *MockDocumentConfigRepository_DeleteOne_Call {
	return &MockDocumentConfigRepository_DeleteOne_Call{Call: _e.mock.On("DeleteOne", ctx, query)}
}

func (_c *MockDocumentConfigRepository_DeleteOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentConfigRepository_DeleteOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_DeleteOne_Call) Return(id uint64, err error) *MockDocumentConfigRepository_DeleteOne_Call {
	_c.Call.Return(id, err)
	return _c
}

func (_c *MockDocumentConfigRepository_DeleteOne_Call) RunAndReturn(run func(context.Context, *model.Query) (uint64, error)) *MockDocumentConfigRepository_DeleteOne_Call {
	_c.Call.Return(run)
	return _c
}

// Destroy provides a mock function with given fields: ctx
func (_m *MockDocumentConfigRepository) Destroy(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Destroy")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentConfigRepository_Destroy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Destroy'
type MockDocumentConfigRepository_Destroy_Call struct {
	*mock.Call
}

// Destroy is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDocumentConfigRepository_Expecter) Destroy(ctx interface{}) *MockDocumentConfigRepository_Destroy_Call {
	return &MockDocumentConfigRepository_Destroy_Call{Call: _e.mock.On("Destroy", ctx)}
}

func (_c *MockDocumentConfigRepository_Destroy_Call) Run(run func(ctx context.Context)) *MockDocumentConfigRepository_Destroy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_Destroy_Call) Return(err error) *MockDocumentConfigRepository_Destroy_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentConfigRepository_Destroy_Call) RunAndReturn(run func(context.Context) error) *MockDocumentConfigRepository_Destroy_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function with given fields: ctx, query
func (_m *MockDocumentConfigRepository) Find(ctx context.Context, query *model.Query) ([]*model.DocumentConfig, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*model.DocumentConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) ([]*model.DocumentConfig, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) []*model.DocumentConfig); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentConfigRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockDocumentConfigRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentConfigRepository_Expecter) Find(ctx interface{}, query interface{}) *MockDocumentConfigRepository_Find_Call {
	return &MockDocumentConfigRepository_Find_Call{Call: _e.mock.On("Find", ctx, query)}
}

func (_c *MockDocumentConfigRepository_Find_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentConfigRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_Find_Call) Return(res []*model.DocumentConfig, err error) *MockDocumentConfigRepository_Find_Call {
	_c.Call.Return(res, err)
	return _c
}

func (_c *MockDocumentConfigRepository_Find_Call) RunAndReturn(run func(context.Context, *model.Query) ([]*model.DocumentConfig, error)) *MockDocumentConfigRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentConfigRepository) FindOne(ctx context.Context, query *model.Query) (*model.DocumentConfig, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *model.DocumentConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (*model.DocumentConfig, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) *model.DocumentConfig); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentConfigRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDocumentConfigRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentConfigRepository_Expecter) FindOne(ctx interface{}, query interface{}) *MockDocumentConfigRepository_FindOne_Call {
	return &MockDocumentConfigRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, query)}
}

func (_c *MockDocumentConfigRepository_FindOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentConfigRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_FindOne_Call) Return(res *model.DocumentConfig, err error) *MockDocumentConfigRepository_FindOne_Call {
	_c.Call.Return(res, err)
	return _c
}

func (_c *MockDocumentConfigRepository_FindOne_Call) RunAndReturn(run func(context.Context, *model.Query) (*model.DocumentConfig, error)) *MockDocumentConfigRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function with given fields: ctx, query, document
func (_m *MockDocumentConfigRepository) UpdateOne(ctx context.Context, query *model.Query, document *model.DocumentConfig) error {
	ret := _m.Called(ctx, query, document)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.DocumentConfig) error); ok {
		r0 = rf(ctx, query, document)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentConfigRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDocumentConfigRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - document *model.DocumentConfig
func (_e *MockDocumentConfigRepository_Expecter) UpdateOne(ctx interface{}, query interface{}, document interface{}) *MockDocumentConfigRepository_UpdateOne_Call {
	return &MockDocumentConfigRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, query, document)}
}

func (_c *MockDocumentConfigRepository_UpdateOne_Call) Run(run func(ctx context.Context, query *model.Query, document *model.DocumentConfig)) *MockDocumentConfigRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.DocumentConfig))
	})
	return _c
}

func (_c *MockDocumentConfigRepository_UpdateOne_Call) Return(err error) *MockDocumentConfigRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentConfigRepository_UpdateOne_Call) RunAndReturn(run func(context.Context, *model.Query, *model.DocumentConfig) error) *MockDocumentConfigRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentConfigRepository creates a new instance of MockDocumentConfigRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentConfigRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentConfigRepository {
	mock := &MockDocumentConfigRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
