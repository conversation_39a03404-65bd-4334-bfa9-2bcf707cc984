// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockDocumentSettingRepository is an autogenerated mock type for the DocumentSettingRepository type
type MockDocumentSettingRepository struct {
	mock.Mock
}

type MockDocumentSettingRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentSettingRepository) EXPECT() *MockDocumentSettingRepository_Expecter {
	return &MockDocumentSettingRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, documentSetting
func (_m *MockDocumentSettingRepository) Create(ctx context.Context, documentSetting *model.DocumentSetting) error {
	ret := _m.Called(ctx, documentSetting)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentSetting) error); ok {
		r0 = rf(ctx, documentSetting)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentSettingRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentSettingRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - documentSetting *model.DocumentSetting
func (_e *MockDocumentSettingRepository_Expecter) Create(ctx interface{}, documentSetting interface{}) *MockDocumentSettingRepository_Create_Call {
	return &MockDocumentSettingRepository_Create_Call{Call: _e.mock.On("Create", ctx, documentSetting)}
}

func (_c *MockDocumentSettingRepository_Create_Call) Run(run func(ctx context.Context, documentSetting *model.DocumentSetting)) *MockDocumentSettingRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentSetting))
	})
	return _c
}

func (_c *MockDocumentSettingRepository_Create_Call) Return(err error) *MockDocumentSettingRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentSettingRepository_Create_Call) RunAndReturn(run func(context.Context, *model.DocumentSetting) error) *MockDocumentSettingRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrUpdate provides a mock function with given fields: ctx, query, documentSetting
func (_m *MockDocumentSettingRepository) CreateOrUpdate(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) error {
	ret := _m.Called(ctx, query, documentSetting)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.DocumentSetting) error); ok {
		r0 = rf(ctx, query, documentSetting)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentSettingRepository_CreateOrUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrUpdate'
type MockDocumentSettingRepository_CreateOrUpdate_Call struct {
	*mock.Call
}

// CreateOrUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - documentSetting *model.DocumentSetting
func (_e *MockDocumentSettingRepository_Expecter) CreateOrUpdate(ctx interface{}, query interface{}, documentSetting interface{}) *MockDocumentSettingRepository_CreateOrUpdate_Call {
	return &MockDocumentSettingRepository_CreateOrUpdate_Call{Call: _e.mock.On("CreateOrUpdate", ctx, query, documentSetting)}
}

func (_c *MockDocumentSettingRepository_CreateOrUpdate_Call) Run(run func(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting)) *MockDocumentSettingRepository_CreateOrUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.DocumentSetting))
	})
	return _c
}

func (_c *MockDocumentSettingRepository_CreateOrUpdate_Call) Return(err error) *MockDocumentSettingRepository_CreateOrUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentSettingRepository_CreateOrUpdate_Call) RunAndReturn(run func(context.Context, *model.Query, *model.DocumentSetting) error) *MockDocumentSettingRepository_CreateOrUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentSettingRepository) FindOne(ctx context.Context, query *model.Query) (*model.DocumentSetting, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *model.DocumentSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (*model.DocumentSetting, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) *model.DocumentSetting); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentSettingRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDocumentSettingRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentSettingRepository_Expecter) FindOne(ctx interface{}, query interface{}) *MockDocumentSettingRepository_FindOne_Call {
	return &MockDocumentSettingRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, query)}
}

func (_c *MockDocumentSettingRepository_FindOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentSettingRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentSettingRepository_FindOne_Call) Return(documentSetting *model.DocumentSetting, err error) *MockDocumentSettingRepository_FindOne_Call {
	_c.Call.Return(documentSetting, err)
	return _c
}

func (_c *MockDocumentSettingRepository_FindOne_Call) RunAndReturn(run func(context.Context, *model.Query) (*model.DocumentSetting, error)) *MockDocumentSettingRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetValueByKey provides a mock function with given fields: ctx, tenantId, key
func (_m *MockDocumentSettingRepository) GetValueByKey(ctx context.Context, tenantId uint64, key string) (*model.DocumentSetting, error) {
	ret := _m.Called(ctx, tenantId, key)

	if len(ret) == 0 {
		panic("no return value specified for GetValueByKey")
	}

	var r0 *model.DocumentSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string) (*model.DocumentSetting, error)); ok {
		return rf(ctx, tenantId, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string) *model.DocumentSetting); ok {
		r0 = rf(ctx, tenantId, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string) error); ok {
		r1 = rf(ctx, tenantId, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentSettingRepository_GetValueByKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetValueByKey'
type MockDocumentSettingRepository_GetValueByKey_Call struct {
	*mock.Call
}

// GetValueByKey is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantId uint64
//   - key string
func (_e *MockDocumentSettingRepository_Expecter) GetValueByKey(ctx interface{}, tenantId interface{}, key interface{}) *MockDocumentSettingRepository_GetValueByKey_Call {
	return &MockDocumentSettingRepository_GetValueByKey_Call{Call: _e.mock.On("GetValueByKey", ctx, tenantId, key)}
}

func (_c *MockDocumentSettingRepository_GetValueByKey_Call) Run(run func(ctx context.Context, tenantId uint64, key string)) *MockDocumentSettingRepository_GetValueByKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string))
	})
	return _c
}

func (_c *MockDocumentSettingRepository_GetValueByKey_Call) Return(documentSetting *model.DocumentSetting, err error) *MockDocumentSettingRepository_GetValueByKey_Call {
	_c.Call.Return(documentSetting, err)
	return _c
}

func (_c *MockDocumentSettingRepository_GetValueByKey_Call) RunAndReturn(run func(context.Context, uint64, string) (*model.DocumentSetting, error)) *MockDocumentSettingRepository_GetValueByKey_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function with given fields: ctx, query, documentSetting
func (_m *MockDocumentSettingRepository) UpdateOne(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting) error {
	ret := _m.Called(ctx, query, documentSetting)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.DocumentSetting) error); ok {
		r0 = rf(ctx, query, documentSetting)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentSettingRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDocumentSettingRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - documentSetting *model.DocumentSetting
func (_e *MockDocumentSettingRepository_Expecter) UpdateOne(ctx interface{}, query interface{}, documentSetting interface{}) *MockDocumentSettingRepository_UpdateOne_Call {
	return &MockDocumentSettingRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, query, documentSetting)}
}

func (_c *MockDocumentSettingRepository_UpdateOne_Call) Run(run func(ctx context.Context, query *model.Query, documentSetting *model.DocumentSetting)) *MockDocumentSettingRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.DocumentSetting))
	})
	return _c
}

func (_c *MockDocumentSettingRepository_UpdateOne_Call) Return(err error) *MockDocumentSettingRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentSettingRepository_UpdateOne_Call) RunAndReturn(run func(context.Context, *model.Query, *model.DocumentSetting) error) *MockDocumentSettingRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentSettingRepository creates a new instance of MockDocumentSettingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentSettingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentSettingRepository {
	mock := &MockDocumentSettingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
