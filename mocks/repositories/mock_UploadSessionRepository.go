// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	model "bilabl/docman/domain/model"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockUploadSessionRepository is an autogenerated mock type for the UploadSessionRepository type
type MockUploadSessionRepository struct {
	mock.Mock
}

type MockUploadSessionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUploadSessionRepository) EXPECT() *MockUploadSessionRepository_Expecter {
	return &MockUploadSessionRepository_Expecter{mock: &_m.Mock}
}

// CleanupExpired provides a mock function with given fields: ctx
func (_m *MockUploadSessionRepository) CleanupExpired(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CleanupExpired")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadSessionRepository_CleanupExpired_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CleanupExpired'
type MockUploadSessionRepository_CleanupExpired_Call struct {
	*mock.Call
}

// CleanupExpired is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUploadSessionRepository_Expecter) CleanupExpired(ctx interface{}) *MockUploadSessionRepository_CleanupExpired_Call {
	return &MockUploadSessionRepository_CleanupExpired_Call{Call: _e.mock.On("CleanupExpired", ctx)}
}

func (_c *MockUploadSessionRepository_CleanupExpired_Call) Run(run func(ctx context.Context)) *MockUploadSessionRepository_CleanupExpired_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUploadSessionRepository_CleanupExpired_Call) Return(_a0 int64, _a1 error) *MockUploadSessionRepository_CleanupExpired_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadSessionRepository_CleanupExpired_Call) RunAndReturn(run func(context.Context) (int64, error)) *MockUploadSessionRepository_CleanupExpired_Call {
	_c.Call.Return(run)
	return _c
}

// CountByTenantAndStatus provides a mock function with given fields: ctx, tenantID, status
func (_m *MockUploadSessionRepository) CountByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus) (int64, error) {
	ret := _m.Called(ctx, tenantID, status)

	if len(ret) == 0 {
		panic("no return value specified for CountByTenantAndStatus")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, model.UploadSessionStatus) (int64, error)); ok {
		return rf(ctx, tenantID, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, model.UploadSessionStatus) int64); ok {
		r0 = rf(ctx, tenantID, status)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, model.UploadSessionStatus) error); ok {
		r1 = rf(ctx, tenantID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadSessionRepository_CountByTenantAndStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountByTenantAndStatus'
type MockUploadSessionRepository_CountByTenantAndStatus_Call struct {
	*mock.Call
}

// CountByTenantAndStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - status model.UploadSessionStatus
func (_e *MockUploadSessionRepository_Expecter) CountByTenantAndStatus(ctx interface{}, tenantID interface{}, status interface{}) *MockUploadSessionRepository_CountByTenantAndStatus_Call {
	return &MockUploadSessionRepository_CountByTenantAndStatus_Call{Call: _e.mock.On("CountByTenantAndStatus", ctx, tenantID, status)}
}

func (_c *MockUploadSessionRepository_CountByTenantAndStatus_Call) Run(run func(ctx context.Context, tenantID uint64, status model.UploadSessionStatus)) *MockUploadSessionRepository_CountByTenantAndStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(model.UploadSessionStatus))
	})
	return _c
}

func (_c *MockUploadSessionRepository_CountByTenantAndStatus_Call) Return(_a0 int64, _a1 error) *MockUploadSessionRepository_CountByTenantAndStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadSessionRepository_CountByTenantAndStatus_Call) RunAndReturn(run func(context.Context, uint64, model.UploadSessionStatus) (int64, error)) *MockUploadSessionRepository_CountByTenantAndStatus_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, session
func (_m *MockUploadSessionRepository) Create(ctx context.Context, session *model.UploadSession) error {
	ret := _m.Called(ctx, session)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.UploadSession) error); ok {
		r0 = rf(ctx, session)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUploadSessionRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUploadSessionRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - session *model.UploadSession
func (_e *MockUploadSessionRepository_Expecter) Create(ctx interface{}, session interface{}) *MockUploadSessionRepository_Create_Call {
	return &MockUploadSessionRepository_Create_Call{Call: _e.mock.On("Create", ctx, session)}
}

func (_c *MockUploadSessionRepository_Create_Call) Run(run func(ctx context.Context, session *model.UploadSession)) *MockUploadSessionRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.UploadSession))
	})
	return _c
}

func (_c *MockUploadSessionRepository_Create_Call) Return(_a0 error) *MockUploadSessionRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadSessionRepository_Create_Call) RunAndReturn(run func(context.Context, *model.UploadSession) error) *MockUploadSessionRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByToken provides a mock function with given fields: ctx, token
func (_m *MockUploadSessionRepository) GetByToken(ctx context.Context, token string) (*model.UploadSession, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for GetByToken")
	}

	var r0 *model.UploadSession
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*model.UploadSession, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *model.UploadSession); ok {
		r0 = rf(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.UploadSession)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadSessionRepository_GetByToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByToken'
type MockUploadSessionRepository_GetByToken_Call struct {
	*mock.Call
}

// GetByToken is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *MockUploadSessionRepository_Expecter) GetByToken(ctx interface{}, token interface{}) *MockUploadSessionRepository_GetByToken_Call {
	return &MockUploadSessionRepository_GetByToken_Call{Call: _e.mock.On("GetByToken", ctx, token)}
}

func (_c *MockUploadSessionRepository_GetByToken_Call) Run(run func(ctx context.Context, token string)) *MockUploadSessionRepository_GetByToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUploadSessionRepository_GetByToken_Call) Return(_a0 *model.UploadSession, _a1 error) *MockUploadSessionRepository_GetByToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadSessionRepository_GetByToken_Call) RunAndReturn(run func(context.Context, string) (*model.UploadSession, error)) *MockUploadSessionRepository_GetByToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetByTokenAndTenant provides a mock function with given fields: ctx, token, tenantID
func (_m *MockUploadSessionRepository) GetByTokenAndTenant(ctx context.Context, token string, tenantID uint64) (*model.UploadSession, error) {
	ret := _m.Called(ctx, token, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetByTokenAndTenant")
	}

	var r0 *model.UploadSession
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) (*model.UploadSession, error)); ok {
		return rf(ctx, token, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, uint64) *model.UploadSession); ok {
		r0 = rf(ctx, token, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.UploadSession)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, uint64) error); ok {
		r1 = rf(ctx, token, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadSessionRepository_GetByTokenAndTenant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByTokenAndTenant'
type MockUploadSessionRepository_GetByTokenAndTenant_Call struct {
	*mock.Call
}

// GetByTokenAndTenant is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - tenantID uint64
func (_e *MockUploadSessionRepository_Expecter) GetByTokenAndTenant(ctx interface{}, token interface{}, tenantID interface{}) *MockUploadSessionRepository_GetByTokenAndTenant_Call {
	return &MockUploadSessionRepository_GetByTokenAndTenant_Call{Call: _e.mock.On("GetByTokenAndTenant", ctx, token, tenantID)}
}

func (_c *MockUploadSessionRepository_GetByTokenAndTenant_Call) Run(run func(ctx context.Context, token string, tenantID uint64)) *MockUploadSessionRepository_GetByTokenAndTenant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uint64))
	})
	return _c
}

func (_c *MockUploadSessionRepository_GetByTokenAndTenant_Call) Return(_a0 *model.UploadSession, _a1 error) *MockUploadSessionRepository_GetByTokenAndTenant_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadSessionRepository_GetByTokenAndTenant_Call) RunAndReturn(run func(context.Context, string, uint64) (*model.UploadSession, error)) *MockUploadSessionRepository_GetByTokenAndTenant_Call {
	_c.Call.Return(run)
	return _c
}

// ListByTenantAndStatus provides a mock function with given fields: ctx, tenantID, status, limit, offset
func (_m *MockUploadSessionRepository) ListByTenantAndStatus(ctx context.Context, tenantID uint64, status model.UploadSessionStatus, limit int, offset int) ([]*model.UploadSession, error) {
	ret := _m.Called(ctx, tenantID, status, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for ListByTenantAndStatus")
	}

	var r0 []*model.UploadSession
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, model.UploadSessionStatus, int, int) ([]*model.UploadSession, error)); ok {
		return rf(ctx, tenantID, status, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, model.UploadSessionStatus, int, int) []*model.UploadSession); ok {
		r0 = rf(ctx, tenantID, status, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.UploadSession)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, model.UploadSessionStatus, int, int) error); ok {
		r1 = rf(ctx, tenantID, status, limit, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUploadSessionRepository_ListByTenantAndStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByTenantAndStatus'
type MockUploadSessionRepository_ListByTenantAndStatus_Call struct {
	*mock.Call
}

// ListByTenantAndStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - status model.UploadSessionStatus
//   - limit int
//   - offset int
func (_e *MockUploadSessionRepository_Expecter) ListByTenantAndStatus(ctx interface{}, tenantID interface{}, status interface{}, limit interface{}, offset interface{}) *MockUploadSessionRepository_ListByTenantAndStatus_Call {
	return &MockUploadSessionRepository_ListByTenantAndStatus_Call{Call: _e.mock.On("ListByTenantAndStatus", ctx, tenantID, status, limit, offset)}
}

func (_c *MockUploadSessionRepository_ListByTenantAndStatus_Call) Run(run func(ctx context.Context, tenantID uint64, status model.UploadSessionStatus, limit int, offset int)) *MockUploadSessionRepository_ListByTenantAndStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(model.UploadSessionStatus), args[3].(int), args[4].(int))
	})
	return _c
}

func (_c *MockUploadSessionRepository_ListByTenantAndStatus_Call) Return(_a0 []*model.UploadSession, _a1 error) *MockUploadSessionRepository_ListByTenantAndStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUploadSessionRepository_ListByTenantAndStatus_Call) RunAndReturn(run func(context.Context, uint64, model.UploadSessionStatus, int, int) ([]*model.UploadSession, error)) *MockUploadSessionRepository_ListByTenantAndStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFileInfo provides a mock function with given fields: ctx, token, fileSize, contentType
func (_m *MockUploadSessionRepository) UpdateFileInfo(ctx context.Context, token string, fileSize int64, contentType string) error {
	ret := _m.Called(ctx, token, fileSize, contentType)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFileInfo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, string) error); ok {
		r0 = rf(ctx, token, fileSize, contentType)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUploadSessionRepository_UpdateFileInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFileInfo'
type MockUploadSessionRepository_UpdateFileInfo_Call struct {
	*mock.Call
}

// UpdateFileInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - fileSize int64
//   - contentType string
func (_e *MockUploadSessionRepository_Expecter) UpdateFileInfo(ctx interface{}, token interface{}, fileSize interface{}, contentType interface{}) *MockUploadSessionRepository_UpdateFileInfo_Call {
	return &MockUploadSessionRepository_UpdateFileInfo_Call{Call: _e.mock.On("UpdateFileInfo", ctx, token, fileSize, contentType)}
}

func (_c *MockUploadSessionRepository_UpdateFileInfo_Call) Run(run func(ctx context.Context, token string, fileSize int64, contentType string)) *MockUploadSessionRepository_UpdateFileInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(string))
	})
	return _c
}

func (_c *MockUploadSessionRepository_UpdateFileInfo_Call) Return(_a0 error) *MockUploadSessionRepository_UpdateFileInfo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadSessionRepository_UpdateFileInfo_Call) RunAndReturn(run func(context.Context, string, int64, string) error) *MockUploadSessionRepository_UpdateFileInfo_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStatus provides a mock function with given fields: ctx, token, status
func (_m *MockUploadSessionRepository) UpdateStatus(ctx context.Context, token string, status model.UploadSessionStatus) error {
	ret := _m.Called(ctx, token, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, model.UploadSessionStatus) error); ok {
		r0 = rf(ctx, token, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUploadSessionRepository_UpdateStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStatus'
type MockUploadSessionRepository_UpdateStatus_Call struct {
	*mock.Call
}

// UpdateStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - status model.UploadSessionStatus
func (_e *MockUploadSessionRepository_Expecter) UpdateStatus(ctx interface{}, token interface{}, status interface{}) *MockUploadSessionRepository_UpdateStatus_Call {
	return &MockUploadSessionRepository_UpdateStatus_Call{Call: _e.mock.On("UpdateStatus", ctx, token, status)}
}

func (_c *MockUploadSessionRepository_UpdateStatus_Call) Run(run func(ctx context.Context, token string, status model.UploadSessionStatus)) *MockUploadSessionRepository_UpdateStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(model.UploadSessionStatus))
	})
	return _c
}

func (_c *MockUploadSessionRepository_UpdateStatus_Call) Return(_a0 error) *MockUploadSessionRepository_UpdateStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadSessionRepository_UpdateStatus_Call) RunAndReturn(run func(context.Context, string, model.UploadSessionStatus) error) *MockUploadSessionRepository_UpdateStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStatusAndFileID provides a mock function with given fields: ctx, token, status, googleFileID
func (_m *MockUploadSessionRepository) UpdateStatusAndFileID(ctx context.Context, token string, status model.UploadSessionStatus, googleFileID string) error {
	ret := _m.Called(ctx, token, status, googleFileID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStatusAndFileID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, model.UploadSessionStatus, string) error); ok {
		r0 = rf(ctx, token, status, googleFileID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUploadSessionRepository_UpdateStatusAndFileID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStatusAndFileID'
type MockUploadSessionRepository_UpdateStatusAndFileID_Call struct {
	*mock.Call
}

// UpdateStatusAndFileID is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - status model.UploadSessionStatus
//   - googleFileID string
func (_e *MockUploadSessionRepository_Expecter) UpdateStatusAndFileID(ctx interface{}, token interface{}, status interface{}, googleFileID interface{}) *MockUploadSessionRepository_UpdateStatusAndFileID_Call {
	return &MockUploadSessionRepository_UpdateStatusAndFileID_Call{Call: _e.mock.On("UpdateStatusAndFileID", ctx, token, status, googleFileID)}
}

func (_c *MockUploadSessionRepository_UpdateStatusAndFileID_Call) Run(run func(ctx context.Context, token string, status model.UploadSessionStatus, googleFileID string)) *MockUploadSessionRepository_UpdateStatusAndFileID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(model.UploadSessionStatus), args[3].(string))
	})
	return _c
}

func (_c *MockUploadSessionRepository_UpdateStatusAndFileID_Call) Return(_a0 error) *MockUploadSessionRepository_UpdateStatusAndFileID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUploadSessionRepository_UpdateStatusAndFileID_Call) RunAndReturn(run func(context.Context, string, model.UploadSessionStatus, string) error) *MockUploadSessionRepository_UpdateStatusAndFileID_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUploadSessionRepository creates a new instance of MockUploadSessionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUploadSessionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUploadSessionRepository {
	mock := &MockUploadSessionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
