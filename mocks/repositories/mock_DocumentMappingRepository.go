// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	context "context"

	db "code.mybil.net/gophers/gokit/pkg/db"
	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockDocumentMappingRepository is an autogenerated mock type for the DocumentMappingRepository type
type MockDocumentMappingRepository struct {
	mock.Mock
}

type MockDocumentMappingRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentMappingRepository) EXPECT() *MockDocumentMappingRepository_Expecter {
	return &MockDocumentMappingRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentMappingRepository) Create(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentMappingRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentMappingRepository_Expecter) Create(ctx interface{}, _a1 interface{}) *MockDocumentMappingRepository_Create_Call {
	return &MockDocumentMappingRepository_Create_Call{Call: _e.mock.On("Create", ctx, _a1)}
}

func (_c *MockDocumentMappingRepository_Create_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentMappingRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Create_Call) Return(_a0 error) *MockDocumentMappingRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Create_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentMappingRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrUpdate provides a mock function with given fields: ctx, documentMapping
func (_m *MockDocumentMappingRepository) CreateOrUpdate(ctx context.Context, documentMapping *model.DocumentMapping) error {
	ret := _m.Called(ctx, documentMapping)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentMapping) error); ok {
		r0 = rf(ctx, documentMapping)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_CreateOrUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrUpdate'
type MockDocumentMappingRepository_CreateOrUpdate_Call struct {
	*mock.Call
}

// CreateOrUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - documentMapping *model.DocumentMapping
func (_e *MockDocumentMappingRepository_Expecter) CreateOrUpdate(ctx interface{}, documentMapping interface{}) *MockDocumentMappingRepository_CreateOrUpdate_Call {
	return &MockDocumentMappingRepository_CreateOrUpdate_Call{Call: _e.mock.On("CreateOrUpdate", ctx, documentMapping)}
}

func (_c *MockDocumentMappingRepository_CreateOrUpdate_Call) Run(run func(ctx context.Context, documentMapping *model.DocumentMapping)) *MockDocumentMappingRepository_CreateOrUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentMapping))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_CreateOrUpdate_Call) Return(err error) *MockDocumentMappingRepository_CreateOrUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentMappingRepository_CreateOrUpdate_Call) RunAndReturn(run func(context.Context, *model.DocumentMapping) error) *MockDocumentMappingRepository_CreateOrUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentMappingRepository) Delete(ctx context.Context, _a1 any) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDocumentMappingRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
func (_e *MockDocumentMappingRepository_Expecter) Delete(ctx interface{}, _a1 interface{}) *MockDocumentMappingRepository_Delete_Call {
	return &MockDocumentMappingRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, _a1)}
}

func (_c *MockDocumentMappingRepository_Delete_Call) Run(run func(ctx context.Context, _a1 any)) *MockDocumentMappingRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Delete_Call) Return(_a0 error) *MockDocumentMappingRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Delete_Call) RunAndReturn(run func(context.Context, any) error) *MockDocumentMappingRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentMappingRepository) FindOne(ctx context.Context, query *model.Query) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (*model.DocumentMapping, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) *model.DocumentMapping); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentMappingRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDocumentMappingRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentMappingRepository_Expecter) FindOne(ctx interface{}, query interface{}) *MockDocumentMappingRepository_FindOne_Call {
	return &MockDocumentMappingRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, query)}
}

func (_c *MockDocumentMappingRepository_FindOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentMappingRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_FindOne_Call) Return(documentMapping *model.DocumentMapping, err error) *MockDocumentMappingRepository_FindOne_Call {
	_c.Call.Return(documentMapping, err)
	return _c
}

func (_c *MockDocumentMappingRepository_FindOne_Call) RunAndReturn(run func(context.Context, *model.Query) (*model.DocumentMapping, error)) *MockDocumentMappingRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// FirstObjectMapping provides a mock function with given fields: ctx, docType, provider, tenantID, objectID, parentID
func (_m *MockDocumentMappingRepository) FirstObjectMapping(ctx context.Context, docType string, provider string, tenantID uint64, objectID uint64, parentID uint64) (*model.DocumentMapping, error) {
	ret := _m.Called(ctx, docType, provider, tenantID, objectID, parentID)

	if len(ret) == 0 {
		panic("no return value specified for FirstObjectMapping")
	}

	var r0 *model.DocumentMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64, uint64, uint64) (*model.DocumentMapping, error)); ok {
		return rf(ctx, docType, provider, tenantID, objectID, parentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, uint64, uint64, uint64) *model.DocumentMapping); ok {
		r0 = rf(ctx, docType, provider, tenantID, objectID, parentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, uint64, uint64, uint64) error); ok {
		r1 = rf(ctx, docType, provider, tenantID, objectID, parentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentMappingRepository_FirstObjectMapping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FirstObjectMapping'
type MockDocumentMappingRepository_FirstObjectMapping_Call struct {
	*mock.Call
}

// FirstObjectMapping is a helper method to define mock.On call
//   - ctx context.Context
//   - docType string
//   - provider string
//   - tenantID uint64
//   - objectID uint64
//   - parentID uint64
func (_e *MockDocumentMappingRepository_Expecter) FirstObjectMapping(ctx interface{}, docType interface{}, provider interface{}, tenantID interface{}, objectID interface{}, parentID interface{}) *MockDocumentMappingRepository_FirstObjectMapping_Call {
	return &MockDocumentMappingRepository_FirstObjectMapping_Call{Call: _e.mock.On("FirstObjectMapping", ctx, docType, provider, tenantID, objectID, parentID)}
}

func (_c *MockDocumentMappingRepository_FirstObjectMapping_Call) Run(run func(ctx context.Context, docType string, provider string, tenantID uint64, objectID uint64, parentID uint64)) *MockDocumentMappingRepository_FirstObjectMapping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(uint64), args[4].(uint64), args[5].(uint64))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_FirstObjectMapping_Call) Return(_a0 *model.DocumentMapping, _a1 error) *MockDocumentMappingRepository_FirstObjectMapping_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentMappingRepository_FirstObjectMapping_Call) RunAndReturn(run func(context.Context, string, string, uint64, uint64, uint64) (*model.DocumentMapping, error)) *MockDocumentMappingRepository_FirstObjectMapping_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentMappingRepository) Get(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockDocumentMappingRepository_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentMappingRepository_Expecter) Get(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentMappingRepository_Get_Call {
	return &MockDocumentMappingRepository_Get_Call{Call: _e.mock.On("Get", ctx, _a1, id)}
}

func (_c *MockDocumentMappingRepository_Get_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentMappingRepository_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Get_Call) Return(_a0 error) *MockDocumentMappingRepository_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Get_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentMappingRepository_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetWithLock provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentMappingRepository) GetWithLock(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for GetWithLock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_GetWithLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWithLock'
type MockDocumentMappingRepository_GetWithLock_Call struct {
	*mock.Call
}

// GetWithLock is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentMappingRepository_Expecter) GetWithLock(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentMappingRepository_GetWithLock_Call {
	return &MockDocumentMappingRepository_GetWithLock_Call{Call: _e.mock.On("GetWithLock", ctx, _a1, id)}
}

func (_c *MockDocumentMappingRepository_GetWithLock_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentMappingRepository_GetWithLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_GetWithLock_Call) Return(_a0 error) *MockDocumentMappingRepository_GetWithLock_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_GetWithLock_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentMappingRepository_GetWithLock_Call {
	_c.Call.Return(run)
	return _c
}

// Save provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentMappingRepository) Save(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_Save_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Save'
type MockDocumentMappingRepository_Save_Call struct {
	*mock.Call
}

// Save is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentMappingRepository_Expecter) Save(ctx interface{}, _a1 interface{}) *MockDocumentMappingRepository_Save_Call {
	return &MockDocumentMappingRepository_Save_Call{Call: _e.mock.On("Save", ctx, _a1)}
}

func (_c *MockDocumentMappingRepository_Save_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentMappingRepository_Save_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Save_Call) Return(_a0 error) *MockDocumentMappingRepository_Save_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Save_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentMappingRepository_Save_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: ctx, f
func (_m *MockDocumentMappingRepository) Transaction(ctx context.Context, f func(*gorm.DB) error) error {
	ret := _m.Called(ctx, f)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(*gorm.DB) error) error); ok {
		r0 = rf(ctx, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockDocumentMappingRepository_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - ctx context.Context
//   - f func(*gorm.DB) error
func (_e *MockDocumentMappingRepository_Expecter) Transaction(ctx interface{}, f interface{}) *MockDocumentMappingRepository_Transaction_Call {
	return &MockDocumentMappingRepository_Transaction_Call{Call: _e.mock.On("Transaction", ctx, f)}
}

func (_c *MockDocumentMappingRepository_Transaction_Call) Run(run func(ctx context.Context, f func(*gorm.DB) error)) *MockDocumentMappingRepository_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(*gorm.DB) error))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Transaction_Call) Return(_a0 error) *MockDocumentMappingRepository_Transaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Transaction_Call) RunAndReturn(run func(context.Context, func(*gorm.DB) error) error) *MockDocumentMappingRepository_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// Tx provides a mock function with given fields: ctx
func (_m *MockDocumentMappingRepository) Tx(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockDocumentMappingRepository_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type MockDocumentMappingRepository_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDocumentMappingRepository_Expecter) Tx(ctx interface{}) *MockDocumentMappingRepository_Tx_Call {
	return &MockDocumentMappingRepository_Tx_Call{Call: _e.mock.On("Tx", ctx)}
}

func (_c *MockDocumentMappingRepository_Tx_Call) Run(run func(ctx context.Context)) *MockDocumentMappingRepository_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_Tx_Call) Return(_a0 *gorm.DB) *MockDocumentMappingRepository_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_Tx_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockDocumentMappingRepository_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function with given fields: ctx, id, _a2, f
func (_m *MockDocumentMappingRepository) UpdateByID(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc) error {
	ret := _m.Called(ctx, id, _a2, f)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, any, db.UpdateFunc) error); ok {
		r0 = rf(ctx, id, _a2, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type MockDocumentMappingRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - _a2 any
//   - f db.UpdateFunc
func (_e *MockDocumentMappingRepository_Expecter) UpdateByID(ctx interface{}, id interface{}, _a2 interface{}, f interface{}) *MockDocumentMappingRepository_UpdateByID_Call {
	return &MockDocumentMappingRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, _a2, f)}
}

func (_c *MockDocumentMappingRepository_UpdateByID_Call) Run(run func(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc)) *MockDocumentMappingRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(any), args[3].(db.UpdateFunc))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_UpdateByID_Call) Return(_a0 error) *MockDocumentMappingRepository_UpdateByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentMappingRepository_UpdateByID_Call) RunAndReturn(run func(context.Context, uint64, any, db.UpdateFunc) error) *MockDocumentMappingRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function with given fields: ctx, query, documentMapping
func (_m *MockDocumentMappingRepository) UpdateOne(ctx context.Context, query *model.Query, documentMapping *model.DocumentMapping) error {
	ret := _m.Called(ctx, query, documentMapping)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.DocumentMapping) error); ok {
		r0 = rf(ctx, query, documentMapping)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentMappingRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDocumentMappingRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - documentMapping *model.DocumentMapping
func (_e *MockDocumentMappingRepository_Expecter) UpdateOne(ctx interface{}, query interface{}, documentMapping interface{}) *MockDocumentMappingRepository_UpdateOne_Call {
	return &MockDocumentMappingRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, query, documentMapping)}
}

func (_c *MockDocumentMappingRepository_UpdateOne_Call) Run(run func(ctx context.Context, query *model.Query, documentMapping *model.DocumentMapping)) *MockDocumentMappingRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.DocumentMapping))
	})
	return _c
}

func (_c *MockDocumentMappingRepository_UpdateOne_Call) Return(err error) *MockDocumentMappingRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentMappingRepository_UpdateOne_Call) RunAndReturn(run func(context.Context, *model.Query, *model.DocumentMapping) error) *MockDocumentMappingRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentMappingRepository creates a new instance of MockDocumentMappingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentMappingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentMappingRepository {
	mock := &MockDocumentMappingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
