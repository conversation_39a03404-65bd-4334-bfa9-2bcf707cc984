// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	context "context"

	db "code.mybil.net/gophers/gokit/pkg/db"
	ginext "gitlab.com/goxp/cloud0/ginext"

	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockDocumentRepository is an autogenerated mock type for the DocumentRepository type
type MockDocumentRepository struct {
	mock.Mock
}

type MockDocumentRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentRepository) EXPECT() *MockDocumentRepository_Expecter {
	return &MockDocumentRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentRepository) Create(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentRepository_Expecter) Create(ctx interface{}, _a1 interface{}) *MockDocumentRepository_Create_Call {
	return &MockDocumentRepository_Create_Call{Call: _e.mock.On("Create", ctx, _a1)}
}

func (_c *MockDocumentRepository_Create_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentRepository_Create_Call) Return(_a0 error) *MockDocumentRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Create_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDoc provides a mock function with given fields: ctx, document
func (_m *MockDocumentRepository) CreateDoc(ctx context.Context, document *model.Document) error {
	ret := _m.Called(ctx, document)

	if len(ret) == 0 {
		panic("no return value specified for CreateDoc")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Document) error); ok {
		r0 = rf(ctx, document)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_CreateDoc_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDoc'
type MockDocumentRepository_CreateDoc_Call struct {
	*mock.Call
}

// CreateDoc is a helper method to define mock.On call
//   - ctx context.Context
//   - document *model.Document
func (_e *MockDocumentRepository_Expecter) CreateDoc(ctx interface{}, document interface{}) *MockDocumentRepository_CreateDoc_Call {
	return &MockDocumentRepository_CreateDoc_Call{Call: _e.mock.On("CreateDoc", ctx, document)}
}

func (_c *MockDocumentRepository_CreateDoc_Call) Run(run func(ctx context.Context, document *model.Document)) *MockDocumentRepository_CreateDoc_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Document))
	})
	return _c
}

func (_c *MockDocumentRepository_CreateDoc_Call) Return(err error) *MockDocumentRepository_CreateDoc_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentRepository_CreateDoc_Call) RunAndReturn(run func(context.Context, *model.Document) error) *MockDocumentRepository_CreateDoc_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrUpdate provides a mock function with given fields: ctx, query, document
func (_m *MockDocumentRepository) CreateOrUpdate(ctx context.Context, query *model.Query, document *model.Document) error {
	ret := _m.Called(ctx, query, document)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.Document) error); ok {
		r0 = rf(ctx, query, document)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_CreateOrUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrUpdate'
type MockDocumentRepository_CreateOrUpdate_Call struct {
	*mock.Call
}

// CreateOrUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - document *model.Document
func (_e *MockDocumentRepository_Expecter) CreateOrUpdate(ctx interface{}, query interface{}, document interface{}) *MockDocumentRepository_CreateOrUpdate_Call {
	return &MockDocumentRepository_CreateOrUpdate_Call{Call: _e.mock.On("CreateOrUpdate", ctx, query, document)}
}

func (_c *MockDocumentRepository_CreateOrUpdate_Call) Run(run func(ctx context.Context, query *model.Query, document *model.Document)) *MockDocumentRepository_CreateOrUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.Document))
	})
	return _c
}

func (_c *MockDocumentRepository_CreateOrUpdate_Call) Return(err error) *MockDocumentRepository_CreateOrUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentRepository_CreateOrUpdate_Call) RunAndReturn(run func(context.Context, *model.Query, *model.Document) error) *MockDocumentRepository_CreateOrUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentRepository) Delete(ctx context.Context, _a1 any) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDocumentRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
func (_e *MockDocumentRepository_Expecter) Delete(ctx interface{}, _a1 interface{}) *MockDocumentRepository_Delete_Call {
	return &MockDocumentRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, _a1)}
}

func (_c *MockDocumentRepository_Delete_Call) Run(run func(ctx context.Context, _a1 any)) *MockDocumentRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any))
	})
	return _c
}

func (_c *MockDocumentRepository_Delete_Call) Return(_a0 error) *MockDocumentRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Delete_Call) RunAndReturn(run func(context.Context, any) error) *MockDocumentRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentRepository) DeleteOne(ctx context.Context, query *model.Query) error {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) error); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_DeleteOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOne'
type MockDocumentRepository_DeleteOne_Call struct {
	*mock.Call
}

// DeleteOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentRepository_Expecter) DeleteOne(ctx interface{}, query interface{}) *MockDocumentRepository_DeleteOne_Call {
	return &MockDocumentRepository_DeleteOne_Call{Call: _e.mock.On("DeleteOne", ctx, query)}
}

func (_c *MockDocumentRepository_DeleteOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentRepository_DeleteOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentRepository_DeleteOne_Call) Return(err error) *MockDocumentRepository_DeleteOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentRepository_DeleteOne_Call) RunAndReturn(run func(context.Context, *model.Query) error) *MockDocumentRepository_DeleteOne_Call {
	_c.Call.Return(run)
	return _c
}

// Exists provides a mock function with given fields: ctx, query
func (_m *MockDocumentRepository) Exists(ctx context.Context, query *model.Query) (bool, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for Exists")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (bool, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) bool); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentRepository_Exists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Exists'
type MockDocumentRepository_Exists_Call struct {
	*mock.Call
}

// Exists is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentRepository_Expecter) Exists(ctx interface{}, query interface{}) *MockDocumentRepository_Exists_Call {
	return &MockDocumentRepository_Exists_Call{Call: _e.mock.On("Exists", ctx, query)}
}

func (_c *MockDocumentRepository_Exists_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentRepository_Exists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentRepository_Exists_Call) Return(exists bool, err error) *MockDocumentRepository_Exists_Call {
	_c.Call.Return(exists, err)
	return _c
}

func (_c *MockDocumentRepository_Exists_Call) RunAndReturn(run func(context.Context, *model.Query) (bool, error)) *MockDocumentRepository_Exists_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function with given fields: ctx, query, pager
func (_m *MockDocumentRepository) Find(ctx context.Context, query *model.Query, pager *ginext.Pager) ([]*model.Document, error) {
	ret := _m.Called(ctx, query, pager)

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *ginext.Pager) ([]*model.Document, error)); ok {
		return rf(ctx, query, pager)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *ginext.Pager) []*model.Document); ok {
		r0 = rf(ctx, query, pager)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query, *ginext.Pager) error); ok {
		r1 = rf(ctx, query, pager)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockDocumentRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - pager *ginext.Pager
func (_e *MockDocumentRepository_Expecter) Find(ctx interface{}, query interface{}, pager interface{}) *MockDocumentRepository_Find_Call {
	return &MockDocumentRepository_Find_Call{Call: _e.mock.On("Find", ctx, query, pager)}
}

func (_c *MockDocumentRepository_Find_Call) Run(run func(ctx context.Context, query *model.Query, pager *ginext.Pager)) *MockDocumentRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*ginext.Pager))
	})
	return _c
}

func (_c *MockDocumentRepository_Find_Call) Return(res []*model.Document, err error) *MockDocumentRepository_Find_Call {
	_c.Call.Return(res, err)
	return _c
}

func (_c *MockDocumentRepository_Find_Call) RunAndReturn(run func(context.Context, *model.Query, *ginext.Pager) ([]*model.Document, error)) *MockDocumentRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentRepository) FindOne(ctx context.Context, query *model.Query) (*model.Document, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (*model.Document, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) *model.Document); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDocumentRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentRepository_Expecter) FindOne(ctx interface{}, query interface{}) *MockDocumentRepository_FindOne_Call {
	return &MockDocumentRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, query)}
}

func (_c *MockDocumentRepository_FindOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentRepository_FindOne_Call) Return(res *model.Document, err error) *MockDocumentRepository_FindOne_Call {
	_c.Call.Return(res, err)
	return _c
}

func (_c *MockDocumentRepository_FindOne_Call) RunAndReturn(run func(context.Context, *model.Query) (*model.Document, error)) *MockDocumentRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentRepository) Get(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockDocumentRepository_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentRepository_Expecter) Get(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentRepository_Get_Call {
	return &MockDocumentRepository_Get_Call{Call: _e.mock.On("Get", ctx, _a1, id)}
}

func (_c *MockDocumentRepository_Get_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentRepository_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentRepository_Get_Call) Return(_a0 error) *MockDocumentRepository_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Get_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentRepository_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetWithLock provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentRepository) GetWithLock(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for GetWithLock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_GetWithLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWithLock'
type MockDocumentRepository_GetWithLock_Call struct {
	*mock.Call
}

// GetWithLock is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentRepository_Expecter) GetWithLock(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentRepository_GetWithLock_Call {
	return &MockDocumentRepository_GetWithLock_Call{Call: _e.mock.On("GetWithLock", ctx, _a1, id)}
}

func (_c *MockDocumentRepository_GetWithLock_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentRepository_GetWithLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentRepository_GetWithLock_Call) Return(_a0 error) *MockDocumentRepository_GetWithLock_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_GetWithLock_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentRepository_GetWithLock_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, query
func (_m *MockDocumentRepository) List(ctx context.Context, query *model.Query) ([]*model.Document, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*model.Document
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) ([]*model.Document, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) []*model.Document); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Document)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockDocumentRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentRepository_Expecter) List(ctx interface{}, query interface{}) *MockDocumentRepository_List_Call {
	return &MockDocumentRepository_List_Call{Call: _e.mock.On("List", ctx, query)}
}

func (_c *MockDocumentRepository_List_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentRepository_List_Call) Return(res []*model.Document, err error) *MockDocumentRepository_List_Call {
	_c.Call.Return(res, err)
	return _c
}

func (_c *MockDocumentRepository_List_Call) RunAndReturn(run func(context.Context, *model.Query) ([]*model.Document, error)) *MockDocumentRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Save provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentRepository) Save(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_Save_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Save'
type MockDocumentRepository_Save_Call struct {
	*mock.Call
}

// Save is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentRepository_Expecter) Save(ctx interface{}, _a1 interface{}) *MockDocumentRepository_Save_Call {
	return &MockDocumentRepository_Save_Call{Call: _e.mock.On("Save", ctx, _a1)}
}

func (_c *MockDocumentRepository_Save_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentRepository_Save_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentRepository_Save_Call) Return(_a0 error) *MockDocumentRepository_Save_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Save_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentRepository_Save_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: ctx, f
func (_m *MockDocumentRepository) Transaction(ctx context.Context, f func(*gorm.DB) error) error {
	ret := _m.Called(ctx, f)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(*gorm.DB) error) error); ok {
		r0 = rf(ctx, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockDocumentRepository_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - ctx context.Context
//   - f func(*gorm.DB) error
func (_e *MockDocumentRepository_Expecter) Transaction(ctx interface{}, f interface{}) *MockDocumentRepository_Transaction_Call {
	return &MockDocumentRepository_Transaction_Call{Call: _e.mock.On("Transaction", ctx, f)}
}

func (_c *MockDocumentRepository_Transaction_Call) Run(run func(ctx context.Context, f func(*gorm.DB) error)) *MockDocumentRepository_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(*gorm.DB) error))
	})
	return _c
}

func (_c *MockDocumentRepository_Transaction_Call) Return(_a0 error) *MockDocumentRepository_Transaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Transaction_Call) RunAndReturn(run func(context.Context, func(*gorm.DB) error) error) *MockDocumentRepository_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// Tx provides a mock function with given fields: ctx
func (_m *MockDocumentRepository) Tx(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockDocumentRepository_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type MockDocumentRepository_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDocumentRepository_Expecter) Tx(ctx interface{}) *MockDocumentRepository_Tx_Call {
	return &MockDocumentRepository_Tx_Call{Call: _e.mock.On("Tx", ctx)}
}

func (_c *MockDocumentRepository_Tx_Call) Run(run func(ctx context.Context)) *MockDocumentRepository_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDocumentRepository_Tx_Call) Return(_a0 *gorm.DB) *MockDocumentRepository_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_Tx_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockDocumentRepository_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function with given fields: ctx, id, _a2, f
func (_m *MockDocumentRepository) UpdateByID(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc) error {
	ret := _m.Called(ctx, id, _a2, f)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, any, db.UpdateFunc) error); ok {
		r0 = rf(ctx, id, _a2, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type MockDocumentRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - _a2 any
//   - f db.UpdateFunc
func (_e *MockDocumentRepository_Expecter) UpdateByID(ctx interface{}, id interface{}, _a2 interface{}, f interface{}) *MockDocumentRepository_UpdateByID_Call {
	return &MockDocumentRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, _a2, f)}
}

func (_c *MockDocumentRepository_UpdateByID_Call) Run(run func(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc)) *MockDocumentRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(any), args[3].(db.UpdateFunc))
	})
	return _c
}

func (_c *MockDocumentRepository_UpdateByID_Call) Return(_a0 error) *MockDocumentRepository_UpdateByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentRepository_UpdateByID_Call) RunAndReturn(run func(context.Context, uint64, any, db.UpdateFunc) error) *MockDocumentRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function with given fields: ctx, query, document
func (_m *MockDocumentRepository) UpdateOne(ctx context.Context, query *model.Query, document *model.Document) error {
	ret := _m.Called(ctx, query, document)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.Document) error); ok {
		r0 = rf(ctx, query, document)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDocumentRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - document *model.Document
func (_e *MockDocumentRepository_Expecter) UpdateOne(ctx interface{}, query interface{}, document interface{}) *MockDocumentRepository_UpdateOne_Call {
	return &MockDocumentRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, query, document)}
}

func (_c *MockDocumentRepository_UpdateOne_Call) Run(run func(ctx context.Context, query *model.Query, document *model.Document)) *MockDocumentRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.Document))
	})
	return _c
}

func (_c *MockDocumentRepository_UpdateOne_Call) Return(err error) *MockDocumentRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentRepository_UpdateOne_Call) RunAndReturn(run func(context.Context, *model.Query, *model.Document) error) *MockDocumentRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentRepository creates a new instance of MockDocumentRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentRepository {
	mock := &MockDocumentRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
