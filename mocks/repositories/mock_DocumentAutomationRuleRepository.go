// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	context "context"

	db "code.mybil.net/gophers/gokit/pkg/db"
	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"
)

// MockDocumentAutomationRuleRepository is an autogenerated mock type for the DocumentAutomationRuleRepository type
type MockDocumentAutomationRuleRepository struct {
	mock.Mock
}

type MockDocumentAutomationRuleRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentAutomationRuleRepository) EXPECT() *MockDocumentAutomationRuleRepository_Expecter {
	return &MockDocumentAutomationRuleRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentAutomationRuleRepository) Create(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentAutomationRuleRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentAutomationRuleRepository_Expecter) Create(ctx interface{}, _a1 interface{}) *MockDocumentAutomationRuleRepository_Create_Call {
	return &MockDocumentAutomationRuleRepository_Create_Call{Call: _e.mock.On("Create", ctx, _a1)}
}

func (_c *MockDocumentAutomationRuleRepository_Create_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentAutomationRuleRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Create_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Create_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentAutomationRuleRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRule provides a mock function with given fields: ctx, rule
func (_m *MockDocumentAutomationRuleRepository) CreateRule(ctx context.Context, rule *model.DocumentAutomationRule) error {
	ret := _m.Called(ctx, rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule) error); ok {
		r0 = rf(ctx, rule)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_CreateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRule'
type MockDocumentAutomationRuleRepository_CreateRule_Call struct {
	*mock.Call
}

// CreateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
func (_e *MockDocumentAutomationRuleRepository_Expecter) CreateRule(ctx interface{}, rule interface{}) *MockDocumentAutomationRuleRepository_CreateRule_Call {
	return &MockDocumentAutomationRuleRepository_CreateRule_Call{Call: _e.mock.On("CreateRule", ctx, rule)}
}

func (_c *MockDocumentAutomationRuleRepository_CreateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule)) *MockDocumentAutomationRuleRepository_CreateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_CreateRule_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_CreateRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_CreateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule) error) *MockDocumentAutomationRuleRepository_CreateRule_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentAutomationRuleRepository) Delete(ctx context.Context, _a1 any) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDocumentAutomationRuleRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
func (_e *MockDocumentAutomationRuleRepository_Expecter) Delete(ctx interface{}, _a1 interface{}) *MockDocumentAutomationRuleRepository_Delete_Call {
	return &MockDocumentAutomationRuleRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, _a1)}
}

func (_c *MockDocumentAutomationRuleRepository_Delete_Call) Run(run func(ctx context.Context, _a1 any)) *MockDocumentAutomationRuleRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Delete_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Delete_Call) RunAndReturn(run func(context.Context, any) error) *MockDocumentAutomationRuleRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteRule provides a mock function with given fields: ctx, id, tenantID
func (_m *MockDocumentAutomationRuleRepository) DeleteRule(ctx context.Context, id uint64, tenantID uint64) error {
	ret := _m.Called(ctx, id, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) error); ok {
		r0 = rf(ctx, id, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_DeleteRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRule'
type MockDocumentAutomationRuleRepository_DeleteRule_Call struct {
	*mock.Call
}

// DeleteRule is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) DeleteRule(ctx interface{}, id interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_DeleteRule_Call {
	return &MockDocumentAutomationRuleRepository_DeleteRule_Call{Call: _e.mock.On("DeleteRule", ctx, id, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_DeleteRule_Call) Run(run func(ctx context.Context, id uint64, tenantID uint64)) *MockDocumentAutomationRuleRepository_DeleteRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_DeleteRule_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_DeleteRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_DeleteRule_Call) RunAndReturn(run func(context.Context, uint64, uint64) error) *MockDocumentAutomationRuleRepository_DeleteRule_Call {
	_c.Call.Return(run)
	return _c
}

// FindActiveRules provides a mock function with given fields: ctx, tenantID
func (_m *MockDocumentAutomationRuleRepository) FindActiveRules(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveRules")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_FindActiveRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindActiveRules'
type MockDocumentAutomationRuleRepository_FindActiveRules_Call struct {
	*mock.Call
}

// FindActiveRules is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) FindActiveRules(ctx interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_FindActiveRules_Call {
	return &MockDocumentAutomationRuleRepository_FindActiveRules_Call{Call: _e.mock.On("FindActiveRules", ctx, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_FindActiveRules_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockDocumentAutomationRuleRepository_FindActiveRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindActiveRules_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_FindActiveRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindActiveRules_Call) RunAndReturn(run func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_FindActiveRules_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function with given fields: ctx, id, tenantID
func (_m *MockDocumentAutomationRuleRepository) FindByID(ctx context.Context, id uint64, tenantID uint64) (*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, id, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) (*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, id, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, uint64) *model.DocumentAutomationRule); ok {
		r0 = rf(ctx, id, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, uint64) error); ok {
		r1 = rf(ctx, id, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockDocumentAutomationRuleRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) FindByID(ctx interface{}, id interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_FindByID_Call {
	return &MockDocumentAutomationRuleRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_FindByID_Call) Run(run func(ctx context.Context, id uint64, tenantID uint64)) *MockDocumentAutomationRuleRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByID_Call) Return(_a0 *model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_FindByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByID_Call) RunAndReturn(run func(context.Context, uint64, uint64) (*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByTenantID provides a mock function with given fields: ctx, tenantID
func (_m *MockDocumentAutomationRuleRepository) FindByTenantID(ctx context.Context, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for FindByTenantID")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_FindByTenantID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByTenantID'
type MockDocumentAutomationRuleRepository_FindByTenantID_Call struct {
	*mock.Call
}

// FindByTenantID is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) FindByTenantID(ctx interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_FindByTenantID_Call {
	return &MockDocumentAutomationRuleRepository_FindByTenantID_Call{Call: _e.mock.On("FindByTenantID", ctx, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_FindByTenantID_Call) Run(run func(ctx context.Context, tenantID uint64)) *MockDocumentAutomationRuleRepository_FindByTenantID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByTenantID_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_FindByTenantID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByTenantID_Call) RunAndReturn(run func(context.Context, uint64) ([]*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_FindByTenantID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByTriggerType provides a mock function with given fields: ctx, tenantID, triggerType
func (_m *MockDocumentAutomationRuleRepository) FindByTriggerType(ctx context.Context, tenantID uint64, triggerType string) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID, triggerType)

	if len(ret) == 0 {
		panic("no return value specified for FindByTriggerType")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID, triggerType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID, triggerType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string) error); ok {
		r1 = rf(ctx, tenantID, triggerType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_FindByTriggerType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByTriggerType'
type MockDocumentAutomationRuleRepository_FindByTriggerType_Call struct {
	*mock.Call
}

// FindByTriggerType is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - triggerType string
func (_e *MockDocumentAutomationRuleRepository_Expecter) FindByTriggerType(ctx interface{}, tenantID interface{}, triggerType interface{}) *MockDocumentAutomationRuleRepository_FindByTriggerType_Call {
	return &MockDocumentAutomationRuleRepository_FindByTriggerType_Call{Call: _e.mock.On("FindByTriggerType", ctx, tenantID, triggerType)}
}

func (_c *MockDocumentAutomationRuleRepository_FindByTriggerType_Call) Run(run func(ctx context.Context, tenantID uint64, triggerType string)) *MockDocumentAutomationRuleRepository_FindByTriggerType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByTriggerType_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_FindByTriggerType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindByTriggerType_Call) RunAndReturn(run func(context.Context, uint64, string) ([]*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_FindByTriggerType_Call {
	_c.Call.Return(run)
	return _c
}

// FindRulesByTrigger provides a mock function with given fields: ctx, tenantID, triggerType, triggerRules
func (_m *MockDocumentAutomationRuleRepository) FindRulesByTrigger(ctx context.Context, tenantID uint64, triggerType string, triggerRules map[string]interface{}) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, tenantID, triggerType, triggerRules)

	if len(ret) == 0 {
		panic("no return value specified for FindRulesByTrigger")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, tenantID, triggerType, triggerRules)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, string, map[string]interface{}) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, tenantID, triggerType, triggerRules)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, string, map[string]interface{}) error); ok {
		r1 = rf(ctx, tenantID, triggerType, triggerRules)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRulesByTrigger'
type MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call struct {
	*mock.Call
}

// FindRulesByTrigger is a helper method to define mock.On call
//   - ctx context.Context
//   - tenantID uint64
//   - triggerType string
//   - triggerRules map[string]interface{}
func (_e *MockDocumentAutomationRuleRepository_Expecter) FindRulesByTrigger(ctx interface{}, tenantID interface{}, triggerType interface{}, triggerRules interface{}) *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call {
	return &MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call{Call: _e.mock.On("FindRulesByTrigger", ctx, tenantID, triggerType, triggerRules)}
}

func (_c *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call) Run(run func(ctx context.Context, tenantID uint64, triggerType string, triggerRules map[string]interface{})) *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(string), args[3].(map[string]interface{}))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call) RunAndReturn(run func(context.Context, uint64, string, map[string]interface{}) ([]*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_FindRulesByTrigger_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentAutomationRuleRepository) Get(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockDocumentAutomationRuleRepository_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) Get(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentAutomationRuleRepository_Get_Call {
	return &MockDocumentAutomationRuleRepository_Get_Call{Call: _e.mock.On("Get", ctx, _a1, id)}
}

func (_c *MockDocumentAutomationRuleRepository_Get_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentAutomationRuleRepository_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Get_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Get_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentAutomationRuleRepository_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetWithLock provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentAutomationRuleRepository) GetWithLock(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for GetWithLock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_GetWithLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWithLock'
type MockDocumentAutomationRuleRepository_GetWithLock_Call struct {
	*mock.Call
}

// GetWithLock is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) GetWithLock(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentAutomationRuleRepository_GetWithLock_Call {
	return &MockDocumentAutomationRuleRepository_GetWithLock_Call{Call: _e.mock.On("GetWithLock", ctx, _a1, id)}
}

func (_c *MockDocumentAutomationRuleRepository_GetWithLock_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentAutomationRuleRepository_GetWithLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_GetWithLock_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_GetWithLock_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_GetWithLock_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentAutomationRuleRepository_GetWithLock_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, query, tenantID
func (_m *MockDocumentAutomationRuleRepository) List(ctx context.Context, query *model.Query, tenantID uint64) ([]*model.DocumentAutomationRule, error) {
	ret := _m.Called(ctx, query, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*model.DocumentAutomationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, uint64) ([]*model.DocumentAutomationRule, error)); ok {
		return rf(ctx, query, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, uint64) []*model.DocumentAutomationRule); ok {
		r0 = rf(ctx, query, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentAutomationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query, uint64) error); ok {
		r1 = rf(ctx, query, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentAutomationRuleRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockDocumentAutomationRuleRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) List(ctx interface{}, query interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_List_Call {
	return &MockDocumentAutomationRuleRepository_List_Call{Call: _e.mock.On("List", ctx, query, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_List_Call) Run(run func(ctx context.Context, query *model.Query, tenantID uint64)) *MockDocumentAutomationRuleRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_List_Call) Return(_a0 []*model.DocumentAutomationRule, _a1 error) *MockDocumentAutomationRuleRepository_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_List_Call) RunAndReturn(run func(context.Context, *model.Query, uint64) ([]*model.DocumentAutomationRule, error)) *MockDocumentAutomationRuleRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Save provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentAutomationRuleRepository) Save(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Save_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Save'
type MockDocumentAutomationRuleRepository_Save_Call struct {
	*mock.Call
}

// Save is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentAutomationRuleRepository_Expecter) Save(ctx interface{}, _a1 interface{}) *MockDocumentAutomationRuleRepository_Save_Call {
	return &MockDocumentAutomationRuleRepository_Save_Call{Call: _e.mock.On("Save", ctx, _a1)}
}

func (_c *MockDocumentAutomationRuleRepository_Save_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentAutomationRuleRepository_Save_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Save_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_Save_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Save_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentAutomationRuleRepository_Save_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: ctx, f
func (_m *MockDocumentAutomationRuleRepository) Transaction(ctx context.Context, f func(*gorm.DB) error) error {
	ret := _m.Called(ctx, f)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(*gorm.DB) error) error); ok {
		r0 = rf(ctx, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockDocumentAutomationRuleRepository_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - ctx context.Context
//   - f func(*gorm.DB) error
func (_e *MockDocumentAutomationRuleRepository_Expecter) Transaction(ctx interface{}, f interface{}) *MockDocumentAutomationRuleRepository_Transaction_Call {
	return &MockDocumentAutomationRuleRepository_Transaction_Call{Call: _e.mock.On("Transaction", ctx, f)}
}

func (_c *MockDocumentAutomationRuleRepository_Transaction_Call) Run(run func(ctx context.Context, f func(*gorm.DB) error)) *MockDocumentAutomationRuleRepository_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(*gorm.DB) error))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Transaction_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_Transaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Transaction_Call) RunAndReturn(run func(context.Context, func(*gorm.DB) error) error) *MockDocumentAutomationRuleRepository_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// Tx provides a mock function with given fields: ctx
func (_m *MockDocumentAutomationRuleRepository) Tx(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockDocumentAutomationRuleRepository_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type MockDocumentAutomationRuleRepository_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDocumentAutomationRuleRepository_Expecter) Tx(ctx interface{}) *MockDocumentAutomationRuleRepository_Tx_Call {
	return &MockDocumentAutomationRuleRepository_Tx_Call{Call: _e.mock.On("Tx", ctx)}
}

func (_c *MockDocumentAutomationRuleRepository_Tx_Call) Run(run func(ctx context.Context)) *MockDocumentAutomationRuleRepository_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Tx_Call) Return(_a0 *gorm.DB) *MockDocumentAutomationRuleRepository_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_Tx_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockDocumentAutomationRuleRepository_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function with given fields: ctx, id, _a2, f
func (_m *MockDocumentAutomationRuleRepository) UpdateByID(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc) error {
	ret := _m.Called(ctx, id, _a2, f)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, any, db.UpdateFunc) error); ok {
		r0 = rf(ctx, id, _a2, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type MockDocumentAutomationRuleRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - _a2 any
//   - f db.UpdateFunc
func (_e *MockDocumentAutomationRuleRepository_Expecter) UpdateByID(ctx interface{}, id interface{}, _a2 interface{}, f interface{}) *MockDocumentAutomationRuleRepository_UpdateByID_Call {
	return &MockDocumentAutomationRuleRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, _a2, f)}
}

func (_c *MockDocumentAutomationRuleRepository_UpdateByID_Call) Run(run func(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc)) *MockDocumentAutomationRuleRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(any), args[3].(db.UpdateFunc))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_UpdateByID_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_UpdateByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_UpdateByID_Call) RunAndReturn(run func(context.Context, uint64, any, db.UpdateFunc) error) *MockDocumentAutomationRuleRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRule provides a mock function with given fields: ctx, rule, tenantID
func (_m *MockDocumentAutomationRuleRepository) UpdateRule(ctx context.Context, rule *model.DocumentAutomationRule, tenantID uint64) error {
	ret := _m.Called(ctx, rule, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentAutomationRule, uint64) error); ok {
		r0 = rf(ctx, rule, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentAutomationRuleRepository_UpdateRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRule'
type MockDocumentAutomationRuleRepository_UpdateRule_Call struct {
	*mock.Call
}

// UpdateRule is a helper method to define mock.On call
//   - ctx context.Context
//   - rule *model.DocumentAutomationRule
//   - tenantID uint64
func (_e *MockDocumentAutomationRuleRepository_Expecter) UpdateRule(ctx interface{}, rule interface{}, tenantID interface{}) *MockDocumentAutomationRuleRepository_UpdateRule_Call {
	return &MockDocumentAutomationRuleRepository_UpdateRule_Call{Call: _e.mock.On("UpdateRule", ctx, rule, tenantID)}
}

func (_c *MockDocumentAutomationRuleRepository_UpdateRule_Call) Run(run func(ctx context.Context, rule *model.DocumentAutomationRule, tenantID uint64)) *MockDocumentAutomationRuleRepository_UpdateRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentAutomationRule), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_UpdateRule_Call) Return(_a0 error) *MockDocumentAutomationRuleRepository_UpdateRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentAutomationRuleRepository_UpdateRule_Call) RunAndReturn(run func(context.Context, *model.DocumentAutomationRule, uint64) error) *MockDocumentAutomationRuleRepository_UpdateRule_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentAutomationRuleRepository creates a new instance of MockDocumentAutomationRuleRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentAutomationRuleRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentAutomationRuleRepository {
	mock := &MockDocumentAutomationRuleRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
