// Code generated by mockery. DO NOT EDIT.

package repositories

import (
	context "context"

	db "code.mybil.net/gophers/gokit/pkg/db"
	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	model "bilabl/docman/domain/model"

	repositories "bilabl/docman/pkg/repositories"
)

// MockDocumentPermissionMappingRepository is an autogenerated mock type for the DocumentPermissionMappingRepository type
type MockDocumentPermissionMappingRepository struct {
	mock.Mock
}

type MockDocumentPermissionMappingRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDocumentPermissionMappingRepository) EXPECT() *MockDocumentPermissionMappingRepository_Expecter {
	return &MockDocumentPermissionMappingRepository_Expecter{mock: &_m.Mock}
}

// BulkCreateOrUpdate provides a mock function with given fields: ctx, documentPermissionMappings
func (_m *MockDocumentPermissionMappingRepository) BulkCreateOrUpdate(ctx context.Context, documentPermissionMappings []*model.DocumentPermissionMapping) error {
	ret := _m.Called(ctx, documentPermissionMappings)

	if len(ret) == 0 {
		panic("no return value specified for BulkCreateOrUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.DocumentPermissionMapping) error); ok {
		r0 = rf(ctx, documentPermissionMappings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkCreateOrUpdate'
type MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call struct {
	*mock.Call
}

// BulkCreateOrUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - documentPermissionMappings []*model.DocumentPermissionMapping
func (_e *MockDocumentPermissionMappingRepository_Expecter) BulkCreateOrUpdate(ctx interface{}, documentPermissionMappings interface{}) *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call {
	return &MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call{Call: _e.mock.On("BulkCreateOrUpdate", ctx, documentPermissionMappings)}
}

func (_c *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call) Run(run func(ctx context.Context, documentPermissionMappings []*model.DocumentPermissionMapping)) *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.DocumentPermissionMapping))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call) Return(err error) *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call) RunAndReturn(run func(context.Context, []*model.DocumentPermissionMapping) error) *MockDocumentPermissionMappingRepository_BulkCreateOrUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentPermissionMappingRepository) Create(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDocumentPermissionMappingRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentPermissionMappingRepository_Expecter) Create(ctx interface{}, _a1 interface{}) *MockDocumentPermissionMappingRepository_Create_Call {
	return &MockDocumentPermissionMappingRepository_Create_Call{Call: _e.mock.On("Create", ctx, _a1)}
}

func (_c *MockDocumentPermissionMappingRepository_Create_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentPermissionMappingRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Create_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Create_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentPermissionMappingRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrUpdate provides a mock function with given fields: ctx, documentPermissionMapping
func (_m *MockDocumentPermissionMappingRepository) CreateOrUpdate(ctx context.Context, documentPermissionMapping *model.DocumentPermissionMapping) error {
	ret := _m.Called(ctx, documentPermissionMapping)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DocumentPermissionMapping) error); ok {
		r0 = rf(ctx, documentPermissionMapping)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_CreateOrUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrUpdate'
type MockDocumentPermissionMappingRepository_CreateOrUpdate_Call struct {
	*mock.Call
}

// CreateOrUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - documentPermissionMapping *model.DocumentPermissionMapping
func (_e *MockDocumentPermissionMappingRepository_Expecter) CreateOrUpdate(ctx interface{}, documentPermissionMapping interface{}) *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call {
	return &MockDocumentPermissionMappingRepository_CreateOrUpdate_Call{Call: _e.mock.On("CreateOrUpdate", ctx, documentPermissionMapping)}
}

func (_c *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call) Run(run func(ctx context.Context, documentPermissionMapping *model.DocumentPermissionMapping)) *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DocumentPermissionMapping))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call) Return(err error) *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call) RunAndReturn(run func(context.Context, *model.DocumentPermissionMapping) error) *MockDocumentPermissionMappingRepository_CreateOrUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentPermissionMappingRepository) Delete(ctx context.Context, _a1 any) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDocumentPermissionMappingRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
func (_e *MockDocumentPermissionMappingRepository_Expecter) Delete(ctx interface{}, _a1 interface{}) *MockDocumentPermissionMappingRepository_Delete_Call {
	return &MockDocumentPermissionMappingRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, _a1)}
}

func (_c *MockDocumentPermissionMappingRepository_Delete_Call) Run(run func(ctx context.Context, _a1 any)) *MockDocumentPermissionMappingRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Delete_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Delete_Call) RunAndReturn(run func(context.Context, any) error) *MockDocumentPermissionMappingRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentPermissionMappingRepository) DeleteOne(ctx context.Context, query *model.Query) (uint64, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOne")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (uint64, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) uint64); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentPermissionMappingRepository_DeleteOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOne'
type MockDocumentPermissionMappingRepository_DeleteOne_Call struct {
	*mock.Call
}

// DeleteOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentPermissionMappingRepository_Expecter) DeleteOne(ctx interface{}, query interface{}) *MockDocumentPermissionMappingRepository_DeleteOne_Call {
	return &MockDocumentPermissionMappingRepository_DeleteOne_Call{Call: _e.mock.On("DeleteOne", ctx, query)}
}

func (_c *MockDocumentPermissionMappingRepository_DeleteOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentPermissionMappingRepository_DeleteOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_DeleteOne_Call) Return(id uint64, err error) *MockDocumentPermissionMappingRepository_DeleteOne_Call {
	_c.Call.Return(id, err)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_DeleteOne_Call) RunAndReturn(run func(context.Context, *model.Query) (uint64, error)) *MockDocumentPermissionMappingRepository_DeleteOne_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function with given fields: ctx, args
func (_m *MockDocumentPermissionMappingRepository) Find(ctx context.Context, args *repositories.PermMapFindArgs) ([]*model.DocumentPermissionMapping, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*model.DocumentPermissionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *repositories.PermMapFindArgs) ([]*model.DocumentPermissionMapping, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *repositories.PermMapFindArgs) []*model.DocumentPermissionMapping); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DocumentPermissionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *repositories.PermMapFindArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentPermissionMappingRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockDocumentPermissionMappingRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - args *repositories.PermMapFindArgs
func (_e *MockDocumentPermissionMappingRepository_Expecter) Find(ctx interface{}, args interface{}) *MockDocumentPermissionMappingRepository_Find_Call {
	return &MockDocumentPermissionMappingRepository_Find_Call{Call: _e.mock.On("Find", ctx, args)}
}

func (_c *MockDocumentPermissionMappingRepository_Find_Call) Run(run func(ctx context.Context, args *repositories.PermMapFindArgs)) *MockDocumentPermissionMappingRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*repositories.PermMapFindArgs))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Find_Call) Return(_a0 []*model.DocumentPermissionMapping, _a1 error) *MockDocumentPermissionMappingRepository_Find_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Find_Call) RunAndReturn(run func(context.Context, *repositories.PermMapFindArgs) ([]*model.DocumentPermissionMapping, error)) *MockDocumentPermissionMappingRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function with given fields: ctx, query
func (_m *MockDocumentPermissionMappingRepository) FindOne(ctx context.Context, query *model.Query) (*model.DocumentPermissionMapping, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *model.DocumentPermissionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) (*model.DocumentPermissionMapping, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query) *model.DocumentPermissionMapping); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DocumentPermissionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDocumentPermissionMappingRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDocumentPermissionMappingRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
func (_e *MockDocumentPermissionMappingRepository_Expecter) FindOne(ctx interface{}, query interface{}) *MockDocumentPermissionMappingRepository_FindOne_Call {
	return &MockDocumentPermissionMappingRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, query)}
}

func (_c *MockDocumentPermissionMappingRepository_FindOne_Call) Run(run func(ctx context.Context, query *model.Query)) *MockDocumentPermissionMappingRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_FindOne_Call) Return(documentPermissionMapping *model.DocumentPermissionMapping, err error) *MockDocumentPermissionMappingRepository_FindOne_Call {
	_c.Call.Return(documentPermissionMapping, err)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_FindOne_Call) RunAndReturn(run func(context.Context, *model.Query) (*model.DocumentPermissionMapping, error)) *MockDocumentPermissionMappingRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentPermissionMappingRepository) Get(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockDocumentPermissionMappingRepository_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentPermissionMappingRepository_Expecter) Get(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentPermissionMappingRepository_Get_Call {
	return &MockDocumentPermissionMappingRepository_Get_Call{Call: _e.mock.On("Get", ctx, _a1, id)}
}

func (_c *MockDocumentPermissionMappingRepository_Get_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentPermissionMappingRepository_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Get_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Get_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentPermissionMappingRepository_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetWithLock provides a mock function with given fields: ctx, _a1, id
func (_m *MockDocumentPermissionMappingRepository) GetWithLock(ctx context.Context, _a1 any, id uint64) error {
	ret := _m.Called(ctx, _a1, id)

	if len(ret) == 0 {
		panic("no return value specified for GetWithLock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, any, uint64) error); ok {
		r0 = rf(ctx, _a1, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_GetWithLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWithLock'
type MockDocumentPermissionMappingRepository_GetWithLock_Call struct {
	*mock.Call
}

// GetWithLock is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 any
//   - id uint64
func (_e *MockDocumentPermissionMappingRepository_Expecter) GetWithLock(ctx interface{}, _a1 interface{}, id interface{}) *MockDocumentPermissionMappingRepository_GetWithLock_Call {
	return &MockDocumentPermissionMappingRepository_GetWithLock_Call{Call: _e.mock.On("GetWithLock", ctx, _a1, id)}
}

func (_c *MockDocumentPermissionMappingRepository_GetWithLock_Call) Run(run func(ctx context.Context, _a1 any, id uint64)) *MockDocumentPermissionMappingRepository_GetWithLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(any), args[2].(uint64))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_GetWithLock_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_GetWithLock_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_GetWithLock_Call) RunAndReturn(run func(context.Context, any, uint64) error) *MockDocumentPermissionMappingRepository_GetWithLock_Call {
	_c.Call.Return(run)
	return _c
}

// Save provides a mock function with given fields: ctx, _a1
func (_m *MockDocumentPermissionMappingRepository) Save(ctx context.Context, _a1 interface{}) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Save_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Save'
type MockDocumentPermissionMappingRepository_Save_Call struct {
	*mock.Call
}

// Save is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 interface{}
func (_e *MockDocumentPermissionMappingRepository_Expecter) Save(ctx interface{}, _a1 interface{}) *MockDocumentPermissionMappingRepository_Save_Call {
	return &MockDocumentPermissionMappingRepository_Save_Call{Call: _e.mock.On("Save", ctx, _a1)}
}

func (_c *MockDocumentPermissionMappingRepository_Save_Call) Run(run func(ctx context.Context, _a1 interface{})) *MockDocumentPermissionMappingRepository_Save_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Save_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_Save_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Save_Call) RunAndReturn(run func(context.Context, interface{}) error) *MockDocumentPermissionMappingRepository_Save_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: ctx, f
func (_m *MockDocumentPermissionMappingRepository) Transaction(ctx context.Context, f func(*gorm.DB) error) error {
	ret := _m.Called(ctx, f)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(*gorm.DB) error) error); ok {
		r0 = rf(ctx, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockDocumentPermissionMappingRepository_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - ctx context.Context
//   - f func(*gorm.DB) error
func (_e *MockDocumentPermissionMappingRepository_Expecter) Transaction(ctx interface{}, f interface{}) *MockDocumentPermissionMappingRepository_Transaction_Call {
	return &MockDocumentPermissionMappingRepository_Transaction_Call{Call: _e.mock.On("Transaction", ctx, f)}
}

func (_c *MockDocumentPermissionMappingRepository_Transaction_Call) Run(run func(ctx context.Context, f func(*gorm.DB) error)) *MockDocumentPermissionMappingRepository_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(*gorm.DB) error))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Transaction_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_Transaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Transaction_Call) RunAndReturn(run func(context.Context, func(*gorm.DB) error) error) *MockDocumentPermissionMappingRepository_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// Tx provides a mock function with given fields: ctx
func (_m *MockDocumentPermissionMappingRepository) Tx(ctx context.Context) *gorm.DB {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Tx")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// MockDocumentPermissionMappingRepository_Tx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Tx'
type MockDocumentPermissionMappingRepository_Tx_Call struct {
	*mock.Call
}

// Tx is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDocumentPermissionMappingRepository_Expecter) Tx(ctx interface{}) *MockDocumentPermissionMappingRepository_Tx_Call {
	return &MockDocumentPermissionMappingRepository_Tx_Call{Call: _e.mock.On("Tx", ctx)}
}

func (_c *MockDocumentPermissionMappingRepository_Tx_Call) Run(run func(ctx context.Context)) *MockDocumentPermissionMappingRepository_Tx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Tx_Call) Return(_a0 *gorm.DB) *MockDocumentPermissionMappingRepository_Tx_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_Tx_Call) RunAndReturn(run func(context.Context) *gorm.DB) *MockDocumentPermissionMappingRepository_Tx_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateByID provides a mock function with given fields: ctx, id, _a2, f
func (_m *MockDocumentPermissionMappingRepository) UpdateByID(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc) error {
	ret := _m.Called(ctx, id, _a2, f)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, any, db.UpdateFunc) error); ok {
		r0 = rf(ctx, id, _a2, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_UpdateByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateByID'
type MockDocumentPermissionMappingRepository_UpdateByID_Call struct {
	*mock.Call
}

// UpdateByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uint64
//   - _a2 any
//   - f db.UpdateFunc
func (_e *MockDocumentPermissionMappingRepository_Expecter) UpdateByID(ctx interface{}, id interface{}, _a2 interface{}, f interface{}) *MockDocumentPermissionMappingRepository_UpdateByID_Call {
	return &MockDocumentPermissionMappingRepository_UpdateByID_Call{Call: _e.mock.On("UpdateByID", ctx, id, _a2, f)}
}

func (_c *MockDocumentPermissionMappingRepository_UpdateByID_Call) Run(run func(ctx context.Context, id uint64, _a2 any, f db.UpdateFunc)) *MockDocumentPermissionMappingRepository_UpdateByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(any), args[3].(db.UpdateFunc))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_UpdateByID_Call) Return(_a0 error) *MockDocumentPermissionMappingRepository_UpdateByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_UpdateByID_Call) RunAndReturn(run func(context.Context, uint64, any, db.UpdateFunc) error) *MockDocumentPermissionMappingRepository_UpdateByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function with given fields: ctx, query, documentPermissionMapping
func (_m *MockDocumentPermissionMappingRepository) UpdateOne(ctx context.Context, query *model.Query, documentPermissionMapping *model.DocumentPermissionMapping) error {
	ret := _m.Called(ctx, query, documentPermissionMapping)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Query, *model.DocumentPermissionMapping) error); ok {
		r0 = rf(ctx, query, documentPermissionMapping)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDocumentPermissionMappingRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDocumentPermissionMappingRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - query *model.Query
//   - documentPermissionMapping *model.DocumentPermissionMapping
func (_e *MockDocumentPermissionMappingRepository_Expecter) UpdateOne(ctx interface{}, query interface{}, documentPermissionMapping interface{}) *MockDocumentPermissionMappingRepository_UpdateOne_Call {
	return &MockDocumentPermissionMappingRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, query, documentPermissionMapping)}
}

func (_c *MockDocumentPermissionMappingRepository_UpdateOne_Call) Run(run func(ctx context.Context, query *model.Query, documentPermissionMapping *model.DocumentPermissionMapping)) *MockDocumentPermissionMappingRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Query), args[2].(*model.DocumentPermissionMapping))
	})
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_UpdateOne_Call) Return(err error) *MockDocumentPermissionMappingRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDocumentPermissionMappingRepository_UpdateOne_Call) RunAndReturn(run func(context.Context, *model.Query, *model.DocumentPermissionMapping) error) *MockDocumentPermissionMappingRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDocumentPermissionMappingRepository creates a new instance of MockDocumentPermissionMappingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDocumentPermissionMappingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDocumentPermissionMappingRepository {
	mock := &MockDocumentPermissionMappingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
