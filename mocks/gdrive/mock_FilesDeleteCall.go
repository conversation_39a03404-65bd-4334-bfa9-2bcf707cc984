// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesDeleteCall is an autogenerated mock type for the FilesDeleteCall type
type MockFilesDeleteCall struct {
	mock.Mock
}

type MockFilesDeleteCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesDeleteCall) EXPECT() *MockFilesDeleteCall_Expecter {
	return &MockFilesDeleteCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockFilesDeleteCall) Do(opts ...googleapi.CallOption) error {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) error); ok {
		r0 = rf(opts...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFilesDeleteCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockFilesDeleteCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesDeleteCall_Expecter) Do(opts ...interface{}) *MockFilesDeleteCall_Do_Call {
	return &MockFilesDeleteCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesDeleteCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesDeleteCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesDeleteCall_Do_Call) Return(_a0 error) *MockFilesDeleteCall_Do_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesDeleteCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) error) *MockFilesDeleteCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockFilesDeleteCall) SupportsAllDrives(supportsAllDrives bool) *drive.FilesDeleteCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 *drive.FilesDeleteCall
	if rf, ok := ret.Get(0).(func(bool) *drive.FilesDeleteCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesDeleteCall)
		}
	}

	return r0
}

// MockFilesDeleteCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockFilesDeleteCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockFilesDeleteCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockFilesDeleteCall_SupportsAllDrives_Call {
	return &MockFilesDeleteCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockFilesDeleteCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockFilesDeleteCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesDeleteCall_SupportsAllDrives_Call) Return(_a0 *drive.FilesDeleteCall) *MockFilesDeleteCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesDeleteCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) *drive.FilesDeleteCall) *MockFilesDeleteCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesDeleteCall creates a new instance of MockFilesDeleteCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesDeleteCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesDeleteCall {
	mock := &MockFilesDeleteCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
