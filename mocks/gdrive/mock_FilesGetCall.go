// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesGetCall is an autogenerated mock type for the FilesGetCall type
type MockFilesGetCall struct {
	mock.Mock
}

type MockFilesGetCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesGetCall) EXPECT() *MockFilesGetCall_Expecter {
	return &MockFilesGetCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockFilesGetCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.File, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.File); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFilesGetCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockFilesGetCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesGetCall_Expecter) Do(opts ...interface{}) *MockFilesGetCall_Do_Call {
	return &MockFilesGetCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesGetCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesGetCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesGetCall_Do_Call) Return(_a0 *drive.File, _a1 error) *MockFilesGetCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFilesGetCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.File, error)) *MockFilesGetCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Download provides a mock function with given fields: opts
func (_m *MockFilesGetCall) Download(opts ...googleapi.CallOption) (*http.Response, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Download")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*http.Response, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *http.Response); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFilesGetCall_Download_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Download'
type MockFilesGetCall_Download_Call struct {
	*mock.Call
}

// Download is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesGetCall_Expecter) Download(opts ...interface{}) *MockFilesGetCall_Download_Call {
	return &MockFilesGetCall_Download_Call{Call: _e.mock.On("Download",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesGetCall_Download_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesGetCall_Download_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesGetCall_Download_Call) Return(_a0 *http.Response, _a1 error) *MockFilesGetCall_Download_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFilesGetCall_Download_Call) RunAndReturn(run func(...googleapi.CallOption) (*http.Response, error)) *MockFilesGetCall_Download_Call {
	_c.Call.Return(run)
	return _c
}

// Fields provides a mock function with given fields: fields
func (_m *MockFilesGetCall) Fields(fields ...googleapi.Field) gdrive.FilesGetCall {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Fields")
	}

	var r0 gdrive.FilesGetCall
	if rf, ok := ret.Get(0).(func(...googleapi.Field) gdrive.FilesGetCall); ok {
		r0 = rf(fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesGetCall)
		}
	}

	return r0
}

// MockFilesGetCall_Fields_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Fields'
type MockFilesGetCall_Fields_Call struct {
	*mock.Call
}

// Fields is a helper method to define mock.On call
//   - fields ...googleapi.Field
func (_e *MockFilesGetCall_Expecter) Fields(fields ...interface{}) *MockFilesGetCall_Fields_Call {
	return &MockFilesGetCall_Fields_Call{Call: _e.mock.On("Fields",
		append([]interface{}{}, fields...)...)}
}

func (_c *MockFilesGetCall_Fields_Call) Run(run func(fields ...googleapi.Field)) *MockFilesGetCall_Fields_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.Field, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.Field)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesGetCall_Fields_Call) Return(_a0 gdrive.FilesGetCall) *MockFilesGetCall_Fields_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesGetCall_Fields_Call) RunAndReturn(run func(...googleapi.Field) gdrive.FilesGetCall) *MockFilesGetCall_Fields_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockFilesGetCall) SupportsAllDrives(supportsAllDrives bool) gdrive.FilesGetCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.FilesGetCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.FilesGetCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesGetCall)
		}
	}

	return r0
}

// MockFilesGetCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockFilesGetCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockFilesGetCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockFilesGetCall_SupportsAllDrives_Call {
	return &MockFilesGetCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockFilesGetCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockFilesGetCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesGetCall_SupportsAllDrives_Call) Return(_a0 gdrive.FilesGetCall) *MockFilesGetCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesGetCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.FilesGetCall) *MockFilesGetCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesGetCall creates a new instance of MockFilesGetCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesGetCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesGetCall {
	mock := &MockFilesGetCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
