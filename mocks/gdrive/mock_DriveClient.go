// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"
	context "context"

	drive "google.golang.org/api/drive/v3"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// MockDriveClient is an autogenerated mock type for the DriveClient type
type MockDriveClient struct {
	mock.Mock
}

type MockDriveClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDriveClient) EXPECT() *MockDriveClient_Expecter {
	return &MockDriveClient_Expecter{mock: &_m.Mock}
}

// BatchCreatePermissions provides a mock function with given fields: ctx, fileID, permissions
func (_m *MockDriveClient) BatchCreatePermissions(ctx context.Context, fileID string, permissions []*drive.Permission) ([]*drive.Permission, error) {
	ret := _m.Called(ctx, fileID, permissions)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreatePermissions")
	}

	var r0 []*drive.Permission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []*drive.Permission) ([]*drive.Permission, error)); ok {
		return rf(ctx, fileID, permissions)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []*drive.Permission) []*drive.Permission); ok {
		r0 = rf(ctx, fileID, permissions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.Permission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []*drive.Permission) error); ok {
		r1 = rf(ctx, fileID, permissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_BatchCreatePermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreatePermissions'
type MockDriveClient_BatchCreatePermissions_Call struct {
	*mock.Call
}

// BatchCreatePermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID string
//   - permissions []*drive.Permission
func (_e *MockDriveClient_Expecter) BatchCreatePermissions(ctx interface{}, fileID interface{}, permissions interface{}) *MockDriveClient_BatchCreatePermissions_Call {
	return &MockDriveClient_BatchCreatePermissions_Call{Call: _e.mock.On("BatchCreatePermissions", ctx, fileID, permissions)}
}

func (_c *MockDriveClient_BatchCreatePermissions_Call) Run(run func(ctx context.Context, fileID string, permissions []*drive.Permission)) *MockDriveClient_BatchCreatePermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]*drive.Permission))
	})
	return _c
}

func (_c *MockDriveClient_BatchCreatePermissions_Call) Return(_a0 []*drive.Permission, _a1 error) *MockDriveClient_BatchCreatePermissions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_BatchCreatePermissions_Call) RunAndReturn(run func(context.Context, string, []*drive.Permission) ([]*drive.Permission, error)) *MockDriveClient_BatchCreatePermissions_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolder provides a mock function with given fields: name, parentID
func (_m *MockDriveClient) CreateFolder(name string, parentID string) (*drive.File, error) {
	ret := _m.Called(name, parentID)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolder")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(name, parentID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(name, parentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(name, parentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_CreateFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolder'
type MockDriveClient_CreateFolder_Call struct {
	*mock.Call
}

// CreateFolder is a helper method to define mock.On call
//   - name string
//   - parentID string
func (_e *MockDriveClient_Expecter) CreateFolder(name interface{}, parentID interface{}) *MockDriveClient_CreateFolder_Call {
	return &MockDriveClient_CreateFolder_Call{Call: _e.mock.On("CreateFolder", name, parentID)}
}

func (_c *MockDriveClient_CreateFolder_Call) Run(run func(name string, parentID string)) *MockDriveClient_CreateFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_CreateFolder_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_CreateFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_CreateFolder_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_CreateFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolderInSharedDrive provides a mock function with given fields: name, driveID
func (_m *MockDriveClient) CreateFolderInSharedDrive(name string, driveID string) (*drive.File, error) {
	ret := _m.Called(name, driveID)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolderInSharedDrive")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(name, driveID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(name, driveID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(name, driveID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_CreateFolderInSharedDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolderInSharedDrive'
type MockDriveClient_CreateFolderInSharedDrive_Call struct {
	*mock.Call
}

// CreateFolderInSharedDrive is a helper method to define mock.On call
//   - name string
//   - driveID string
func (_e *MockDriveClient_Expecter) CreateFolderInSharedDrive(name interface{}, driveID interface{}) *MockDriveClient_CreateFolderInSharedDrive_Call {
	return &MockDriveClient_CreateFolderInSharedDrive_Call{Call: _e.mock.On("CreateFolderInSharedDrive", name, driveID)}
}

func (_c *MockDriveClient_CreateFolderInSharedDrive_Call) Run(run func(name string, driveID string)) *MockDriveClient_CreateFolderInSharedDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_CreateFolderInSharedDrive_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_CreateFolderInSharedDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_CreateFolderInSharedDrive_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_CreateFolderInSharedDrive_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFolderInSharedDriveFolder provides a mock function with given fields: name, driveID, parentFolderID
func (_m *MockDriveClient) CreateFolderInSharedDriveFolder(name string, driveID string, parentFolderID string) (*drive.File, error) {
	ret := _m.Called(name, driveID, parentFolderID)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolderInSharedDriveFolder")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (*drive.File, error)); ok {
		return rf(name, driveID, parentFolderID)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) *drive.File); ok {
		r0 = rf(name, driveID, parentFolderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(name, driveID, parentFolderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_CreateFolderInSharedDriveFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolderInSharedDriveFolder'
type MockDriveClient_CreateFolderInSharedDriveFolder_Call struct {
	*mock.Call
}

// CreateFolderInSharedDriveFolder is a helper method to define mock.On call
//   - name string
//   - driveID string
//   - parentFolderID string
func (_e *MockDriveClient_Expecter) CreateFolderInSharedDriveFolder(name interface{}, driveID interface{}, parentFolderID interface{}) *MockDriveClient_CreateFolderInSharedDriveFolder_Call {
	return &MockDriveClient_CreateFolderInSharedDriveFolder_Call{Call: _e.mock.On("CreateFolderInSharedDriveFolder", name, driveID, parentFolderID)}
}

func (_c *MockDriveClient_CreateFolderInSharedDriveFolder_Call) Run(run func(name string, driveID string, parentFolderID string)) *MockDriveClient_CreateFolderInSharedDriveFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockDriveClient_CreateFolderInSharedDriveFolder_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_CreateFolderInSharedDriveFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_CreateFolderInSharedDriveFolder_Call) RunAndReturn(run func(string, string, string) (*drive.File, error)) *MockDriveClient_CreateFolderInSharedDriveFolder_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePermission provides a mock function with given fields: ctx, fileID, permission
func (_m *MockDriveClient) CreatePermission(ctx context.Context, fileID string, permission *drive.Permission) (*drive.Permission, error) {
	ret := _m.Called(ctx, fileID, permission)

	if len(ret) == 0 {
		panic("no return value specified for CreatePermission")
	}

	var r0 *drive.Permission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *drive.Permission) (*drive.Permission, error)); ok {
		return rf(ctx, fileID, permission)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *drive.Permission) *drive.Permission); ok {
		r0 = rf(ctx, fileID, permission)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.Permission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *drive.Permission) error); ok {
		r1 = rf(ctx, fileID, permission)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_CreatePermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePermission'
type MockDriveClient_CreatePermission_Call struct {
	*mock.Call
}

// CreatePermission is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID string
//   - permission *drive.Permission
func (_e *MockDriveClient_Expecter) CreatePermission(ctx interface{}, fileID interface{}, permission interface{}) *MockDriveClient_CreatePermission_Call {
	return &MockDriveClient_CreatePermission_Call{Call: _e.mock.On("CreatePermission", ctx, fileID, permission)}
}

func (_c *MockDriveClient_CreatePermission_Call) Run(run func(ctx context.Context, fileID string, permission *drive.Permission)) *MockDriveClient_CreatePermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*drive.Permission))
	})
	return _c
}

func (_c *MockDriveClient_CreatePermission_Call) Return(_a0 *drive.Permission, _a1 error) *MockDriveClient_CreatePermission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_CreatePermission_Call) RunAndReturn(run func(context.Context, string, *drive.Permission) (*drive.Permission, error)) *MockDriveClient_CreatePermission_Call {
	_c.Call.Return(run)
	return _c
}

// CreateResumableUploadURL provides a mock function with given fields: filename, parentFolderID, fileSize
func (_m *MockDriveClient) CreateResumableUploadURL(filename string, parentFolderID string, fileSize int64) (*gdrive.ResumableUploadInfo, error) {
	ret := _m.Called(filename, parentFolderID, fileSize)

	if len(ret) == 0 {
		panic("no return value specified for CreateResumableUploadURL")
	}

	var r0 *gdrive.ResumableUploadInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, int64) (*gdrive.ResumableUploadInfo, error)); ok {
		return rf(filename, parentFolderID, fileSize)
	}
	if rf, ok := ret.Get(0).(func(string, string, int64) *gdrive.ResumableUploadInfo); ok {
		r0 = rf(filename, parentFolderID, fileSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.ResumableUploadInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, int64) error); ok {
		r1 = rf(filename, parentFolderID, fileSize)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_CreateResumableUploadURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateResumableUploadURL'
type MockDriveClient_CreateResumableUploadURL_Call struct {
	*mock.Call
}

// CreateResumableUploadURL is a helper method to define mock.On call
//   - filename string
//   - parentFolderID string
//   - fileSize int64
func (_e *MockDriveClient_Expecter) CreateResumableUploadURL(filename interface{}, parentFolderID interface{}, fileSize interface{}) *MockDriveClient_CreateResumableUploadURL_Call {
	return &MockDriveClient_CreateResumableUploadURL_Call{Call: _e.mock.On("CreateResumableUploadURL", filename, parentFolderID, fileSize)}
}

func (_c *MockDriveClient_CreateResumableUploadURL_Call) Run(run func(filename string, parentFolderID string, fileSize int64)) *MockDriveClient_CreateResumableUploadURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockDriveClient_CreateResumableUploadURL_Call) Return(_a0 *gdrive.ResumableUploadInfo, _a1 error) *MockDriveClient_CreateResumableUploadURL_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_CreateResumableUploadURL_Call) RunAndReturn(run func(string, string, int64) (*gdrive.ResumableUploadInfo, error)) *MockDriveClient_CreateResumableUploadURL_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFile provides a mock function with given fields: fileID
func (_m *MockDriveClient) DeleteFile(fileID string) error {
	ret := _m.Called(fileID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFile")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(fileID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDriveClient_DeleteFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFile'
type MockDriveClient_DeleteFile_Call struct {
	*mock.Call
}

// DeleteFile is a helper method to define mock.On call
//   - fileID string
func (_e *MockDriveClient_Expecter) DeleteFile(fileID interface{}) *MockDriveClient_DeleteFile_Call {
	return &MockDriveClient_DeleteFile_Call{Call: _e.mock.On("DeleteFile", fileID)}
}

func (_c *MockDriveClient_DeleteFile_Call) Run(run func(fileID string)) *MockDriveClient_DeleteFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_DeleteFile_Call) Return(_a0 error) *MockDriveClient_DeleteFile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveClient_DeleteFile_Call) RunAndReturn(run func(string) error) *MockDriveClient_DeleteFile_Call {
	_c.Call.Return(run)
	return _c
}

// DeletePermission provides a mock function with given fields: ctx, fileID, permissionID
func (_m *MockDriveClient) DeletePermission(ctx context.Context, fileID string, permissionID string) error {
	ret := _m.Called(ctx, fileID, permissionID)

	if len(ret) == 0 {
		panic("no return value specified for DeletePermission")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, fileID, permissionID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDriveClient_DeletePermission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePermission'
type MockDriveClient_DeletePermission_Call struct {
	*mock.Call
}

// DeletePermission is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID string
//   - permissionID string
func (_e *MockDriveClient_Expecter) DeletePermission(ctx interface{}, fileID interface{}, permissionID interface{}) *MockDriveClient_DeletePermission_Call {
	return &MockDriveClient_DeletePermission_Call{Call: _e.mock.On("DeletePermission", ctx, fileID, permissionID)}
}

func (_c *MockDriveClient_DeletePermission_Call) Run(run func(ctx context.Context, fileID string, permissionID string)) *MockDriveClient_DeletePermission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockDriveClient_DeletePermission_Call) Return(_a0 error) *MockDriveClient_DeletePermission_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveClient_DeletePermission_Call) RunAndReturn(run func(context.Context, string, string) error) *MockDriveClient_DeletePermission_Call {
	_c.Call.Return(run)
	return _c
}

// DownloadFile provides a mock function with given fields: fileID, outputPath
func (_m *MockDriveClient) DownloadFile(fileID string, outputPath string) error {
	ret := _m.Called(fileID, outputPath)

	if len(ret) == 0 {
		panic("no return value specified for DownloadFile")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(fileID, outputPath)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDriveClient_DownloadFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DownloadFile'
type MockDriveClient_DownloadFile_Call struct {
	*mock.Call
}

// DownloadFile is a helper method to define mock.On call
//   - fileID string
//   - outputPath string
func (_e *MockDriveClient_Expecter) DownloadFile(fileID interface{}, outputPath interface{}) *MockDriveClient_DownloadFile_Call {
	return &MockDriveClient_DownloadFile_Call{Call: _e.mock.On("DownloadFile", fileID, outputPath)}
}

func (_c *MockDriveClient_DownloadFile_Call) Run(run func(fileID string, outputPath string)) *MockDriveClient_DownloadFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_DownloadFile_Call) Return(_a0 error) *MockDriveClient_DownloadFile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveClient_DownloadFile_Call) RunAndReturn(run func(string, string) error) *MockDriveClient_DownloadFile_Call {
	_c.Call.Return(run)
	return _c
}

// FindFileInFolder provides a mock function with given fields: fileName, parentFolderID
func (_m *MockDriveClient) FindFileInFolder(fileName string, parentFolderID string) (*drive.File, error) {
	ret := _m.Called(fileName, parentFolderID)

	if len(ret) == 0 {
		panic("no return value specified for FindFileInFolder")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(fileName, parentFolderID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(fileName, parentFolderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(fileName, parentFolderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_FindFileInFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindFileInFolder'
type MockDriveClient_FindFileInFolder_Call struct {
	*mock.Call
}

// FindFileInFolder is a helper method to define mock.On call
//   - fileName string
//   - parentFolderID string
func (_e *MockDriveClient_Expecter) FindFileInFolder(fileName interface{}, parentFolderID interface{}) *MockDriveClient_FindFileInFolder_Call {
	return &MockDriveClient_FindFileInFolder_Call{Call: _e.mock.On("FindFileInFolder", fileName, parentFolderID)}
}

func (_c *MockDriveClient_FindFileInFolder_Call) Run(run func(fileName string, parentFolderID string)) *MockDriveClient_FindFileInFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_FindFileInFolder_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_FindFileInFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_FindFileInFolder_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_FindFileInFolder_Call {
	_c.Call.Return(run)
	return _c
}

// FindFolderInSharedDrive provides a mock function with given fields: folderName, driveID
func (_m *MockDriveClient) FindFolderInSharedDrive(folderName string, driveID string) (*drive.File, error) {
	ret := _m.Called(folderName, driveID)

	if len(ret) == 0 {
		panic("no return value specified for FindFolderInSharedDrive")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(folderName, driveID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(folderName, driveID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(folderName, driveID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_FindFolderInSharedDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindFolderInSharedDrive'
type MockDriveClient_FindFolderInSharedDrive_Call struct {
	*mock.Call
}

// FindFolderInSharedDrive is a helper method to define mock.On call
//   - folderName string
//   - driveID string
func (_e *MockDriveClient_Expecter) FindFolderInSharedDrive(folderName interface{}, driveID interface{}) *MockDriveClient_FindFolderInSharedDrive_Call {
	return &MockDriveClient_FindFolderInSharedDrive_Call{Call: _e.mock.On("FindFolderInSharedDrive", folderName, driveID)}
}

func (_c *MockDriveClient_FindFolderInSharedDrive_Call) Run(run func(folderName string, driveID string)) *MockDriveClient_FindFolderInSharedDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_FindFolderInSharedDrive_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_FindFolderInSharedDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_FindFolderInSharedDrive_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_FindFolderInSharedDrive_Call {
	_c.Call.Return(run)
	return _c
}

// GetAvailableSharedDrives provides a mock function with no fields
func (_m *MockDriveClient) GetAvailableSharedDrives() ([]*drive.Drive, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAvailableSharedDrives")
	}

	var r0 []*drive.Drive
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*drive.Drive, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*drive.Drive); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.Drive)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_GetAvailableSharedDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAvailableSharedDrives'
type MockDriveClient_GetAvailableSharedDrives_Call struct {
	*mock.Call
}

// GetAvailableSharedDrives is a helper method to define mock.On call
func (_e *MockDriveClient_Expecter) GetAvailableSharedDrives() *MockDriveClient_GetAvailableSharedDrives_Call {
	return &MockDriveClient_GetAvailableSharedDrives_Call{Call: _e.mock.On("GetAvailableSharedDrives")}
}

func (_c *MockDriveClient_GetAvailableSharedDrives_Call) Run(run func()) *MockDriveClient_GetAvailableSharedDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveClient_GetAvailableSharedDrives_Call) Return(_a0 []*drive.Drive, _a1 error) *MockDriveClient_GetAvailableSharedDrives_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_GetAvailableSharedDrives_Call) RunAndReturn(run func() ([]*drive.Drive, error)) *MockDriveClient_GetAvailableSharedDrives_Call {
	_c.Call.Return(run)
	return _c
}

// GetDriveInfo provides a mock function with given fields: id
func (_m *MockDriveClient) GetDriveInfo(id string) (*gdrive.DriveInfo, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetDriveInfo")
	}

	var r0 *gdrive.DriveInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*gdrive.DriveInfo, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(string) *gdrive.DriveInfo); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DriveInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_GetDriveInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDriveInfo'
type MockDriveClient_GetDriveInfo_Call struct {
	*mock.Call
}

// GetDriveInfo is a helper method to define mock.On call
//   - id string
func (_e *MockDriveClient_Expecter) GetDriveInfo(id interface{}) *MockDriveClient_GetDriveInfo_Call {
	return &MockDriveClient_GetDriveInfo_Call{Call: _e.mock.On("GetDriveInfo", id)}
}

func (_c *MockDriveClient_GetDriveInfo_Call) Run(run func(id string)) *MockDriveClient_GetDriveInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_GetDriveInfo_Call) Return(_a0 *gdrive.DriveInfo, _a1 error) *MockDriveClient_GetDriveInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_GetDriveInfo_Call) RunAndReturn(run func(string) (*gdrive.DriveInfo, error)) *MockDriveClient_GetDriveInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileInfo provides a mock function with given fields: fileID
func (_m *MockDriveClient) GetFileInfo(fileID string) (*drive.File, error) {
	ret := _m.Called(fileID)

	if len(ret) == 0 {
		panic("no return value specified for GetFileInfo")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*drive.File, error)); ok {
		return rf(fileID)
	}
	if rf, ok := ret.Get(0).(func(string) *drive.File); ok {
		r0 = rf(fileID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(fileID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_GetFileInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileInfo'
type MockDriveClient_GetFileInfo_Call struct {
	*mock.Call
}

// GetFileInfo is a helper method to define mock.On call
//   - fileID string
func (_e *MockDriveClient_Expecter) GetFileInfo(fileID interface{}) *MockDriveClient_GetFileInfo_Call {
	return &MockDriveClient_GetFileInfo_Call{Call: _e.mock.On("GetFileInfo", fileID)}
}

func (_c *MockDriveClient_GetFileInfo_Call) Run(run func(fileID string)) *MockDriveClient_GetFileInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_GetFileInfo_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_GetFileInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_GetFileInfo_Call) RunAndReturn(run func(string) (*drive.File, error)) *MockDriveClient_GetFileInfo_Call {
	_c.Call.Return(run)
	return _c
}

// IsSharedDrive provides a mock function with given fields: driveID
func (_m *MockDriveClient) IsSharedDrive(driveID string) (bool, error) {
	ret := _m.Called(driveID)

	if len(ret) == 0 {
		panic("no return value specified for IsSharedDrive")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(driveID)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(driveID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(driveID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_IsSharedDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsSharedDrive'
type MockDriveClient_IsSharedDrive_Call struct {
	*mock.Call
}

// IsSharedDrive is a helper method to define mock.On call
//   - driveID string
func (_e *MockDriveClient_Expecter) IsSharedDrive(driveID interface{}) *MockDriveClient_IsSharedDrive_Call {
	return &MockDriveClient_IsSharedDrive_Call{Call: _e.mock.On("IsSharedDrive", driveID)}
}

func (_c *MockDriveClient_IsSharedDrive_Call) Run(run func(driveID string)) *MockDriveClient_IsSharedDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_IsSharedDrive_Call) Return(_a0 bool, _a1 error) *MockDriveClient_IsSharedDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_IsSharedDrive_Call) RunAndReturn(run func(string) (bool, error)) *MockDriveClient_IsSharedDrive_Call {
	_c.Call.Return(run)
	return _c
}

// IsSharedWithMe provides a mock function with given fields: folderID
func (_m *MockDriveClient) IsSharedWithMe(folderID string) (bool, error) {
	ret := _m.Called(folderID)

	if len(ret) == 0 {
		panic("no return value specified for IsSharedWithMe")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(folderID)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(folderID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(folderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_IsSharedWithMe_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsSharedWithMe'
type MockDriveClient_IsSharedWithMe_Call struct {
	*mock.Call
}

// IsSharedWithMe is a helper method to define mock.On call
//   - folderID string
func (_e *MockDriveClient_Expecter) IsSharedWithMe(folderID interface{}) *MockDriveClient_IsSharedWithMe_Call {
	return &MockDriveClient_IsSharedWithMe_Call{Call: _e.mock.On("IsSharedWithMe", folderID)}
}

func (_c *MockDriveClient_IsSharedWithMe_Call) Run(run func(folderID string)) *MockDriveClient_IsSharedWithMe_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_IsSharedWithMe_Call) Return(_a0 bool, _a1 error) *MockDriveClient_IsSharedWithMe_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_IsSharedWithMe_Call) RunAndReturn(run func(string) (bool, error)) *MockDriveClient_IsSharedWithMe_Call {
	_c.Call.Return(run)
	return _c
}

// ListAllFilesInFolder provides a mock function with given fields: folderID
func (_m *MockDriveClient) ListAllFilesInFolder(folderID string) ([]*drive.File, error) {
	ret := _m.Called(folderID)

	if len(ret) == 0 {
		panic("no return value specified for ListAllFilesInFolder")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]*drive.File, error)); ok {
		return rf(folderID)
	}
	if rf, ok := ret.Get(0).(func(string) []*drive.File); ok {
		r0 = rf(folderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(folderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListAllFilesInFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAllFilesInFolder'
type MockDriveClient_ListAllFilesInFolder_Call struct {
	*mock.Call
}

// ListAllFilesInFolder is a helper method to define mock.On call
//   - folderID string
func (_e *MockDriveClient_Expecter) ListAllFilesInFolder(folderID interface{}) *MockDriveClient_ListAllFilesInFolder_Call {
	return &MockDriveClient_ListAllFilesInFolder_Call{Call: _e.mock.On("ListAllFilesInFolder", folderID)}
}

func (_c *MockDriveClient_ListAllFilesInFolder_Call) Run(run func(folderID string)) *MockDriveClient_ListAllFilesInFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_ListAllFilesInFolder_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveClient_ListAllFilesInFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListAllFilesInFolder_Call) RunAndReturn(run func(string) ([]*drive.File, error)) *MockDriveClient_ListAllFilesInFolder_Call {
	_c.Call.Return(run)
	return _c
}

// ListFiles provides a mock function with given fields: opts
func (_m *MockDriveClient) ListFiles(opts *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ListFiles")
	}

	var r0 *gdrive.PaginatedResult
	var r1 error
	if rf, ok := ret.Get(0).(func(*gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*gdrive.PaginationOptions) *gdrive.PaginatedResult); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.PaginatedResult)
		}
	}

	if rf, ok := ret.Get(1).(func(*gdrive.PaginationOptions) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFiles'
type MockDriveClient_ListFiles_Call struct {
	*mock.Call
}

// ListFiles is a helper method to define mock.On call
//   - opts *gdrive.PaginationOptions
func (_e *MockDriveClient_Expecter) ListFiles(opts interface{}) *MockDriveClient_ListFiles_Call {
	return &MockDriveClient_ListFiles_Call{Call: _e.mock.On("ListFiles", opts)}
}

func (_c *MockDriveClient_ListFiles_Call) Run(run func(opts *gdrive.PaginationOptions)) *MockDriveClient_ListFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gdrive.PaginationOptions))
	})
	return _c
}

func (_c *MockDriveClient_ListFiles_Call) Return(_a0 *gdrive.PaginatedResult, _a1 error) *MockDriveClient_ListFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListFiles_Call) RunAndReturn(run func(*gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)) *MockDriveClient_ListFiles_Call {
	_c.Call.Return(run)
	return _c
}

// ListFilesInFolder provides a mock function with given fields: folderID, opts
func (_m *MockDriveClient) ListFilesInFolder(folderID string, opts *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error) {
	ret := _m.Called(folderID, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListFilesInFolder")
	}

	var r0 *gdrive.PaginatedResult
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)); ok {
		return rf(folderID, opts)
	}
	if rf, ok := ret.Get(0).(func(string, *gdrive.PaginationOptions) *gdrive.PaginatedResult); ok {
		r0 = rf(folderID, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.PaginatedResult)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *gdrive.PaginationOptions) error); ok {
		r1 = rf(folderID, opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListFilesInFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFilesInFolder'
type MockDriveClient_ListFilesInFolder_Call struct {
	*mock.Call
}

// ListFilesInFolder is a helper method to define mock.On call
//   - folderID string
//   - opts *gdrive.PaginationOptions
func (_e *MockDriveClient_Expecter) ListFilesInFolder(folderID interface{}, opts interface{}) *MockDriveClient_ListFilesInFolder_Call {
	return &MockDriveClient_ListFilesInFolder_Call{Call: _e.mock.On("ListFilesInFolder", folderID, opts)}
}

func (_c *MockDriveClient_ListFilesInFolder_Call) Run(run func(folderID string, opts *gdrive.PaginationOptions)) *MockDriveClient_ListFilesInFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(*gdrive.PaginationOptions))
	})
	return _c
}

func (_c *MockDriveClient_ListFilesInFolder_Call) Return(_a0 *gdrive.PaginatedResult, _a1 error) *MockDriveClient_ListFilesInFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListFilesInFolder_Call) RunAndReturn(run func(string, *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)) *MockDriveClient_ListFilesInFolder_Call {
	_c.Call.Return(run)
	return _c
}

// ListFilesInSharedDrive provides a mock function with given fields: driveID, opts
func (_m *MockDriveClient) ListFilesInSharedDrive(driveID string, opts *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error) {
	ret := _m.Called(driveID, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListFilesInSharedDrive")
	}

	var r0 *gdrive.PaginatedResult
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)); ok {
		return rf(driveID, opts)
	}
	if rf, ok := ret.Get(0).(func(string, *gdrive.PaginationOptions) *gdrive.PaginatedResult); ok {
		r0 = rf(driveID, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.PaginatedResult)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *gdrive.PaginationOptions) error); ok {
		r1 = rf(driveID, opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListFilesInSharedDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFilesInSharedDrive'
type MockDriveClient_ListFilesInSharedDrive_Call struct {
	*mock.Call
}

// ListFilesInSharedDrive is a helper method to define mock.On call
//   - driveID string
//   - opts *gdrive.PaginationOptions
func (_e *MockDriveClient_Expecter) ListFilesInSharedDrive(driveID interface{}, opts interface{}) *MockDriveClient_ListFilesInSharedDrive_Call {
	return &MockDriveClient_ListFilesInSharedDrive_Call{Call: _e.mock.On("ListFilesInSharedDrive", driveID, opts)}
}

func (_c *MockDriveClient_ListFilesInSharedDrive_Call) Run(run func(driveID string, opts *gdrive.PaginationOptions)) *MockDriveClient_ListFilesInSharedDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(*gdrive.PaginationOptions))
	})
	return _c
}

func (_c *MockDriveClient_ListFilesInSharedDrive_Call) Return(_a0 *gdrive.PaginatedResult, _a1 error) *MockDriveClient_ListFilesInSharedDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListFilesInSharedDrive_Call) RunAndReturn(run func(string, *gdrive.PaginationOptions) (*gdrive.PaginatedResult, error)) *MockDriveClient_ListFilesInSharedDrive_Call {
	_c.Call.Return(run)
	return _c
}

// ListFilesWithQuery provides a mock function with given fields: query, driveID
func (_m *MockDriveClient) ListFilesWithQuery(query string, driveID string) ([]*drive.File, error) {
	ret := _m.Called(query, driveID)

	if len(ret) == 0 {
		panic("no return value specified for ListFilesWithQuery")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]*drive.File, error)); ok {
		return rf(query, driveID)
	}
	if rf, ok := ret.Get(0).(func(string, string) []*drive.File); ok {
		r0 = rf(query, driveID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(query, driveID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListFilesWithQuery_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFilesWithQuery'
type MockDriveClient_ListFilesWithQuery_Call struct {
	*mock.Call
}

// ListFilesWithQuery is a helper method to define mock.On call
//   - query string
//   - driveID string
func (_e *MockDriveClient_Expecter) ListFilesWithQuery(query interface{}, driveID interface{}) *MockDriveClient_ListFilesWithQuery_Call {
	return &MockDriveClient_ListFilesWithQuery_Call{Call: _e.mock.On("ListFilesWithQuery", query, driveID)}
}

func (_c *MockDriveClient_ListFilesWithQuery_Call) Run(run func(query string, driveID string)) *MockDriveClient_ListFilesWithQuery_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_ListFilesWithQuery_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveClient_ListFilesWithQuery_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListFilesWithQuery_Call) RunAndReturn(run func(string, string) ([]*drive.File, error)) *MockDriveClient_ListFilesWithQuery_Call {
	_c.Call.Return(run)
	return _c
}

// ListPermissions provides a mock function with given fields: ctx, fileID
func (_m *MockDriveClient) ListPermissions(ctx context.Context, fileID string) ([]*drive.Permission, error) {
	ret := _m.Called(ctx, fileID)

	if len(ret) == 0 {
		panic("no return value specified for ListPermissions")
	}

	var r0 []*drive.Permission
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*drive.Permission, error)); ok {
		return rf(ctx, fileID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*drive.Permission); ok {
		r0 = rf(ctx, fileID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.Permission)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, fileID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListPermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPermissions'
type MockDriveClient_ListPermissions_Call struct {
	*mock.Call
}

// ListPermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - fileID string
func (_e *MockDriveClient_Expecter) ListPermissions(ctx interface{}, fileID interface{}) *MockDriveClient_ListPermissions_Call {
	return &MockDriveClient_ListPermissions_Call{Call: _e.mock.On("ListPermissions", ctx, fileID)}
}

func (_c *MockDriveClient_ListPermissions_Call) Run(run func(ctx context.Context, fileID string)) *MockDriveClient_ListPermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_ListPermissions_Call) Return(_a0 []*drive.Permission, _a1 error) *MockDriveClient_ListPermissions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListPermissions_Call) RunAndReturn(run func(context.Context, string) ([]*drive.Permission, error)) *MockDriveClient_ListPermissions_Call {
	_c.Call.Return(run)
	return _c
}

// ListSharedDrives provides a mock function with no fields
func (_m *MockDriveClient) ListSharedDrives() ([]*drive.Drive, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListSharedDrives")
	}

	var r0 []*drive.Drive
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*drive.Drive, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*drive.Drive); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.Drive)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListSharedDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSharedDrives'
type MockDriveClient_ListSharedDrives_Call struct {
	*mock.Call
}

// ListSharedDrives is a helper method to define mock.On call
func (_e *MockDriveClient_Expecter) ListSharedDrives() *MockDriveClient_ListSharedDrives_Call {
	return &MockDriveClient_ListSharedDrives_Call{Call: _e.mock.On("ListSharedDrives")}
}

func (_c *MockDriveClient_ListSharedDrives_Call) Run(run func()) *MockDriveClient_ListSharedDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveClient_ListSharedDrives_Call) Return(_a0 []*drive.Drive, _a1 error) *MockDriveClient_ListSharedDrives_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListSharedDrives_Call) RunAndReturn(run func() ([]*drive.Drive, error)) *MockDriveClient_ListSharedDrives_Call {
	_c.Call.Return(run)
	return _c
}

// ListSharedFolders provides a mock function with no fields
func (_m *MockDriveClient) ListSharedFolders() ([]*drive.File, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListSharedFolders")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*drive.File, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*drive.File); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ListSharedFolders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSharedFolders'
type MockDriveClient_ListSharedFolders_Call struct {
	*mock.Call
}

// ListSharedFolders is a helper method to define mock.On call
func (_e *MockDriveClient_Expecter) ListSharedFolders() *MockDriveClient_ListSharedFolders_Call {
	return &MockDriveClient_ListSharedFolders_Call{Call: _e.mock.On("ListSharedFolders")}
}

func (_c *MockDriveClient_ListSharedFolders_Call) Run(run func()) *MockDriveClient_ListSharedFolders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveClient_ListSharedFolders_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveClient_ListSharedFolders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ListSharedFolders_Call) RunAndReturn(run func() ([]*drive.File, error)) *MockDriveClient_ListSharedFolders_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSharedURL provides a mock function with given fields: input
func (_m *MockDriveClient) ParseSharedURL(input string) (*gdrive.DriveInfo, error) {
	ret := _m.Called(input)

	if len(ret) == 0 {
		panic("no return value specified for ParseSharedURL")
	}

	var r0 *gdrive.DriveInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*gdrive.DriveInfo, error)); ok {
		return rf(input)
	}
	if rf, ok := ret.Get(0).(func(string) *gdrive.DriveInfo); ok {
		r0 = rf(input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gdrive.DriveInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_ParseSharedURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSharedURL'
type MockDriveClient_ParseSharedURL_Call struct {
	*mock.Call
}

// ParseSharedURL is a helper method to define mock.On call
//   - input string
func (_e *MockDriveClient_Expecter) ParseSharedURL(input interface{}) *MockDriveClient_ParseSharedURL_Call {
	return &MockDriveClient_ParseSharedURL_Call{Call: _e.mock.On("ParseSharedURL", input)}
}

func (_c *MockDriveClient_ParseSharedURL_Call) Run(run func(input string)) *MockDriveClient_ParseSharedURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_ParseSharedURL_Call) Return(_a0 *gdrive.DriveInfo, _a1 error) *MockDriveClient_ParseSharedURL_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_ParseSharedURL_Call) RunAndReturn(run func(string) (*gdrive.DriveInfo, error)) *MockDriveClient_ParseSharedURL_Call {
	_c.Call.Return(run)
	return _c
}

// RenameFile provides a mock function with given fields: fileID, newName
func (_m *MockDriveClient) RenameFile(fileID string, newName string) (*drive.File, error) {
	ret := _m.Called(fileID, newName)

	if len(ret) == 0 {
		panic("no return value specified for RenameFile")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(fileID, newName)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(fileID, newName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(fileID, newName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_RenameFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RenameFile'
type MockDriveClient_RenameFile_Call struct {
	*mock.Call
}

// RenameFile is a helper method to define mock.On call
//   - fileID string
//   - newName string
func (_e *MockDriveClient_Expecter) RenameFile(fileID interface{}, newName interface{}) *MockDriveClient_RenameFile_Call {
	return &MockDriveClient_RenameFile_Call{Call: _e.mock.On("RenameFile", fileID, newName)}
}

func (_c *MockDriveClient_RenameFile_Call) Run(run func(fileID string, newName string)) *MockDriveClient_RenameFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_RenameFile_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_RenameFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_RenameFile_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_RenameFile_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFile provides a mock function with given fields: filename, filepath
func (_m *MockDriveClient) UploadFile(filename string, filepath string) (*drive.File, error) {
	ret := _m.Called(filename, filepath)

	if len(ret) == 0 {
		panic("no return value specified for UploadFile")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*drive.File, error)); ok {
		return rf(filename, filepath)
	}
	if rf, ok := ret.Get(0).(func(string, string) *drive.File); ok {
		r0 = rf(filename, filepath)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(filename, filepath)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_UploadFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFile'
type MockDriveClient_UploadFile_Call struct {
	*mock.Call
}

// UploadFile is a helper method to define mock.On call
//   - filename string
//   - filepath string
func (_e *MockDriveClient_Expecter) UploadFile(filename interface{}, filepath interface{}) *MockDriveClient_UploadFile_Call {
	return &MockDriveClient_UploadFile_Call{Call: _e.mock.On("UploadFile", filename, filepath)}
}

func (_c *MockDriveClient_UploadFile_Call) Run(run func(filename string, filepath string)) *MockDriveClient_UploadFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDriveClient_UploadFile_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_UploadFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_UploadFile_Call) RunAndReturn(run func(string, string) (*drive.File, error)) *MockDriveClient_UploadFile_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFileToSharedDrive provides a mock function with given fields: filename, filepath, driveID
func (_m *MockDriveClient) UploadFileToSharedDrive(filename string, filepath string, driveID string) (*drive.File, error) {
	ret := _m.Called(filename, filepath, driveID)

	if len(ret) == 0 {
		panic("no return value specified for UploadFileToSharedDrive")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (*drive.File, error)); ok {
		return rf(filename, filepath, driveID)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) *drive.File); ok {
		r0 = rf(filename, filepath, driveID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(filename, filepath, driveID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_UploadFileToSharedDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFileToSharedDrive'
type MockDriveClient_UploadFileToSharedDrive_Call struct {
	*mock.Call
}

// UploadFileToSharedDrive is a helper method to define mock.On call
//   - filename string
//   - filepath string
//   - driveID string
func (_e *MockDriveClient_Expecter) UploadFileToSharedDrive(filename interface{}, filepath interface{}, driveID interface{}) *MockDriveClient_UploadFileToSharedDrive_Call {
	return &MockDriveClient_UploadFileToSharedDrive_Call{Call: _e.mock.On("UploadFileToSharedDrive", filename, filepath, driveID)}
}

func (_c *MockDriveClient_UploadFileToSharedDrive_Call) Run(run func(filename string, filepath string, driveID string)) *MockDriveClient_UploadFileToSharedDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockDriveClient_UploadFileToSharedDrive_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_UploadFileToSharedDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_UploadFileToSharedDrive_Call) RunAndReturn(run func(string, string, string) (*drive.File, error)) *MockDriveClient_UploadFileToSharedDrive_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFileToSharedDriveFolder provides a mock function with given fields: filename, filepath, driveID, folderID
func (_m *MockDriveClient) UploadFileToSharedDriveFolder(filename string, filepath string, driveID string, folderID string) (*drive.File, error) {
	ret := _m.Called(filename, filepath, driveID, folderID)

	if len(ret) == 0 {
		panic("no return value specified for UploadFileToSharedDriveFolder")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, string) (*drive.File, error)); ok {
		return rf(filename, filepath, driveID, folderID)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, string) *drive.File); ok {
		r0 = rf(filename, filepath, driveID, folderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, string) error); ok {
		r1 = rf(filename, filepath, driveID, folderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_UploadFileToSharedDriveFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFileToSharedDriveFolder'
type MockDriveClient_UploadFileToSharedDriveFolder_Call struct {
	*mock.Call
}

// UploadFileToSharedDriveFolder is a helper method to define mock.On call
//   - filename string
//   - filepath string
//   - driveID string
//   - folderID string
func (_e *MockDriveClient_Expecter) UploadFileToSharedDriveFolder(filename interface{}, filepath interface{}, driveID interface{}, folderID interface{}) *MockDriveClient_UploadFileToSharedDriveFolder_Call {
	return &MockDriveClient_UploadFileToSharedDriveFolder_Call{Call: _e.mock.On("UploadFileToSharedDriveFolder", filename, filepath, driveID, folderID)}
}

func (_c *MockDriveClient_UploadFileToSharedDriveFolder_Call) Run(run func(filename string, filepath string, driveID string, folderID string)) *MockDriveClient_UploadFileToSharedDriveFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockDriveClient_UploadFileToSharedDriveFolder_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_UploadFileToSharedDriveFolder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_UploadFileToSharedDriveFolder_Call) RunAndReturn(run func(string, string, string, string) (*drive.File, error)) *MockDriveClient_UploadFileToSharedDriveFolder_Call {
	_c.Call.Return(run)
	return _c
}

// UploadToResumableURL provides a mock function with given fields: uploadURL, content, contentType
func (_m *MockDriveClient) UploadToResumableURL(uploadURL string, content io.Reader, contentType string) (*drive.File, error) {
	ret := _m.Called(uploadURL, content, contentType)

	if len(ret) == 0 {
		panic("no return value specified for UploadToResumableURL")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, io.Reader, string) (*drive.File, error)); ok {
		return rf(uploadURL, content, contentType)
	}
	if rf, ok := ret.Get(0).(func(string, io.Reader, string) *drive.File); ok {
		r0 = rf(uploadURL, content, contentType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, io.Reader, string) error); ok {
		r1 = rf(uploadURL, content, contentType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveClient_UploadToResumableURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadToResumableURL'
type MockDriveClient_UploadToResumableURL_Call struct {
	*mock.Call
}

// UploadToResumableURL is a helper method to define mock.On call
//   - uploadURL string
//   - content io.Reader
//   - contentType string
func (_e *MockDriveClient_Expecter) UploadToResumableURL(uploadURL interface{}, content interface{}, contentType interface{}) *MockDriveClient_UploadToResumableURL_Call {
	return &MockDriveClient_UploadToResumableURL_Call{Call: _e.mock.On("UploadToResumableURL", uploadURL, content, contentType)}
}

func (_c *MockDriveClient_UploadToResumableURL_Call) Run(run func(uploadURL string, content io.Reader, contentType string)) *MockDriveClient_UploadToResumableURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(io.Reader), args[2].(string))
	})
	return _c
}

func (_c *MockDriveClient_UploadToResumableURL_Call) Return(_a0 *drive.File, _a1 error) *MockDriveClient_UploadToResumableURL_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveClient_UploadToResumableURL_Call) RunAndReturn(run func(string, io.Reader, string) (*drive.File, error)) *MockDriveClient_UploadToResumableURL_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateParentForServiceAccount provides a mock function with given fields: parentFolderID
func (_m *MockDriveClient) ValidateParentForServiceAccount(parentFolderID string) error {
	ret := _m.Called(parentFolderID)

	if len(ret) == 0 {
		panic("no return value specified for ValidateParentForServiceAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(parentFolderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDriveClient_ValidateParentForServiceAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateParentForServiceAccount'
type MockDriveClient_ValidateParentForServiceAccount_Call struct {
	*mock.Call
}

// ValidateParentForServiceAccount is a helper method to define mock.On call
//   - parentFolderID string
func (_e *MockDriveClient_Expecter) ValidateParentForServiceAccount(parentFolderID interface{}) *MockDriveClient_ValidateParentForServiceAccount_Call {
	return &MockDriveClient_ValidateParentForServiceAccount_Call{Call: _e.mock.On("ValidateParentForServiceAccount", parentFolderID)}
}

func (_c *MockDriveClient_ValidateParentForServiceAccount_Call) Run(run func(parentFolderID string)) *MockDriveClient_ValidateParentForServiceAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveClient_ValidateParentForServiceAccount_Call) Return(_a0 error) *MockDriveClient_ValidateParentForServiceAccount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveClient_ValidateParentForServiceAccount_Call) RunAndReturn(run func(string) error) *MockDriveClient_ValidateParentForServiceAccount_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDriveClient creates a new instance of MockDriveClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDriveClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDriveClient {
	mock := &MockDriveClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
