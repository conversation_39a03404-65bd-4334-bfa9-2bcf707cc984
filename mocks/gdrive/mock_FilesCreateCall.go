// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesCreateCall is an autogenerated mock type for the FilesCreateCall type
type MockFilesCreateCall struct {
	mock.Mock
}

type MockFilesCreateCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesCreateCall) EXPECT() *MockFilesCreateCall_Expecter {
	return &MockFilesCreateCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockFilesCreateCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.File, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.File); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFilesCreateCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockFilesCreateCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesCreateCall_Expecter) Do(opts ...interface{}) *MockFilesCreateCall_Do_Call {
	return &MockFilesCreateCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesCreateCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesCreateCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesCreateCall_Do_Call) Return(_a0 *drive.File, _a1 error) *MockFilesCreateCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFilesCreateCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.File, error)) *MockFilesCreateCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Media provides a mock function with given fields: r, options
func (_m *MockFilesCreateCall) Media(r io.Reader, options ...googleapi.MediaOption) gdrive.FilesCreateCall {
	_va := make([]interface{}, len(options))
	for _i := range options {
		_va[_i] = options[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, r)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Media")
	}

	var r0 gdrive.FilesCreateCall
	if rf, ok := ret.Get(0).(func(io.Reader, ...googleapi.MediaOption) gdrive.FilesCreateCall); ok {
		r0 = rf(r, options...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesCreateCall)
		}
	}

	return r0
}

// MockFilesCreateCall_Media_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Media'
type MockFilesCreateCall_Media_Call struct {
	*mock.Call
}

// Media is a helper method to define mock.On call
//   - r io.Reader
//   - options ...googleapi.MediaOption
func (_e *MockFilesCreateCall_Expecter) Media(r interface{}, options ...interface{}) *MockFilesCreateCall_Media_Call {
	return &MockFilesCreateCall_Media_Call{Call: _e.mock.On("Media",
		append([]interface{}{r}, options...)...)}
}

func (_c *MockFilesCreateCall_Media_Call) Run(run func(r io.Reader, options ...googleapi.MediaOption)) *MockFilesCreateCall_Media_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.MediaOption, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.MediaOption)
			}
		}
		run(args[0].(io.Reader), variadicArgs...)
	})
	return _c
}

func (_c *MockFilesCreateCall_Media_Call) Return(_a0 gdrive.FilesCreateCall) *MockFilesCreateCall_Media_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesCreateCall_Media_Call) RunAndReturn(run func(io.Reader, ...googleapi.MediaOption) gdrive.FilesCreateCall) *MockFilesCreateCall_Media_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockFilesCreateCall) SupportsAllDrives(supportsAllDrives bool) gdrive.FilesCreateCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.FilesCreateCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.FilesCreateCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesCreateCall)
		}
	}

	return r0
}

// MockFilesCreateCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockFilesCreateCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockFilesCreateCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockFilesCreateCall_SupportsAllDrives_Call {
	return &MockFilesCreateCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockFilesCreateCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockFilesCreateCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesCreateCall_SupportsAllDrives_Call) Return(_a0 gdrive.FilesCreateCall) *MockFilesCreateCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesCreateCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.FilesCreateCall) *MockFilesCreateCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesCreateCall creates a new instance of MockFilesCreateCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesCreateCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesCreateCall {
	mock := &MockFilesCreateCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
