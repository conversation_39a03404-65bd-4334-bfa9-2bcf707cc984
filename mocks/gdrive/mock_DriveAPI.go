// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	mock "github.com/stretchr/testify/mock"
)

// MockDriveAPI is an autogenerated mock type for the DriveAPI type
type MockDriveAPI struct {
	mock.Mock
}

type MockDrive<PERSON>I_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDriveAPI) EXPECT() *MockDriveAPI_Expecter {
	return &MockDriveAPI_Expecter{mock: &_m.Mock}
}

// Drives provides a mock function with no fields
func (_m *MockDriveAPI) Drives() gdrive.DrivesAPI {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Drives")
	}

	var r0 gdrive.DrivesAPI
	if rf, ok := ret.Get(0).(func() gdrive.DrivesAPI); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.DrivesAPI)
		}
	}

	return r0
}

// MockDriveAPI_Drives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Drives'
type MockDriveAPI_Drives_Call struct {
	*mock.Call
}

// Drives is a helper method to define mock.On call
func (_e *MockDriveAPI_Expecter) Drives() *MockDriveAPI_Drives_Call {
	return &MockDriveAPI_Drives_Call{Call: _e.mock.On("Drives")}
}

func (_c *MockDriveAPI_Drives_Call) Run(run func()) *MockDriveAPI_Drives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveAPI_Drives_Call) Return(_a0 gdrive.DrivesAPI) *MockDriveAPI_Drives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveAPI_Drives_Call) RunAndReturn(run func() gdrive.DrivesAPI) *MockDriveAPI_Drives_Call {
	_c.Call.Return(run)
	return _c
}

// Files provides a mock function with no fields
func (_m *MockDriveAPI) Files() gdrive.FilesAPI {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Files")
	}

	var r0 gdrive.FilesAPI
	if rf, ok := ret.Get(0).(func() gdrive.FilesAPI); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesAPI)
		}
	}

	return r0
}

// MockDriveAPI_Files_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Files'
type MockDriveAPI_Files_Call struct {
	*mock.Call
}

// Files is a helper method to define mock.On call
func (_e *MockDriveAPI_Expecter) Files() *MockDriveAPI_Files_Call {
	return &MockDriveAPI_Files_Call{Call: _e.mock.On("Files")}
}

func (_c *MockDriveAPI_Files_Call) Run(run func()) *MockDriveAPI_Files_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveAPI_Files_Call) Return(_a0 gdrive.FilesAPI) *MockDriveAPI_Files_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveAPI_Files_Call) RunAndReturn(run func() gdrive.FilesAPI) *MockDriveAPI_Files_Call {
	_c.Call.Return(run)
	return _c
}

// Permissions provides a mock function with no fields
func (_m *MockDriveAPI) Permissions() gdrive.PermissionsAPI {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Permissions")
	}

	var r0 gdrive.PermissionsAPI
	if rf, ok := ret.Get(0).(func() gdrive.PermissionsAPI); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsAPI)
		}
	}

	return r0
}

// MockDriveAPI_Permissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Permissions'
type MockDriveAPI_Permissions_Call struct {
	*mock.Call
}

// Permissions is a helper method to define mock.On call
func (_e *MockDriveAPI_Expecter) Permissions() *MockDriveAPI_Permissions_Call {
	return &MockDriveAPI_Permissions_Call{Call: _e.mock.On("Permissions")}
}

func (_c *MockDriveAPI_Permissions_Call) Run(run func()) *MockDriveAPI_Permissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDriveAPI_Permissions_Call) Return(_a0 gdrive.PermissionsAPI) *MockDriveAPI_Permissions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDriveAPI_Permissions_Call) RunAndReturn(run func() gdrive.PermissionsAPI) *MockDriveAPI_Permissions_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDriveAPI creates a new instance of MockDriveAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDriveAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDriveAPI {
	mock := &MockDriveAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
