// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockPermissionsCreateCall is an autogenerated mock type for the PermissionsCreateCall type
type MockPermissionsCreateCall struct {
	mock.Mock
}

type MockPermissionsCreateCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPermissionsCreateCall) EXPECT() *MockPermissionsCreateCall_Expecter {
	return &MockPermissionsCreateCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockPermissionsCreateCall) Do(opts ...googleapi.CallOption) (*drive.Permission, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.Permission
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.Permission, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.Permission); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.Permission)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPermissionsCreateCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockPermissionsCreateCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockPermissionsCreateCall_Expecter) Do(opts ...interface{}) *MockPermissionsCreateCall_Do_Call {
	return &MockPermissionsCreateCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockPermissionsCreateCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockPermissionsCreateCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockPermissionsCreateCall_Do_Call) Return(_a0 *drive.Permission, _a1 error) *MockPermissionsCreateCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPermissionsCreateCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.Permission, error)) *MockPermissionsCreateCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Fields provides a mock function with given fields: fields
func (_m *MockPermissionsCreateCall) Fields(fields ...googleapi.Field) gdrive.PermissionsCreateCall {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Fields")
	}

	var r0 gdrive.PermissionsCreateCall
	if rf, ok := ret.Get(0).(func(...googleapi.Field) gdrive.PermissionsCreateCall); ok {
		r0 = rf(fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsCreateCall)
		}
	}

	return r0
}

// MockPermissionsCreateCall_Fields_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Fields'
type MockPermissionsCreateCall_Fields_Call struct {
	*mock.Call
}

// Fields is a helper method to define mock.On call
//   - fields ...googleapi.Field
func (_e *MockPermissionsCreateCall_Expecter) Fields(fields ...interface{}) *MockPermissionsCreateCall_Fields_Call {
	return &MockPermissionsCreateCall_Fields_Call{Call: _e.mock.On("Fields",
		append([]interface{}{}, fields...)...)}
}

func (_c *MockPermissionsCreateCall_Fields_Call) Run(run func(fields ...googleapi.Field)) *MockPermissionsCreateCall_Fields_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.Field, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.Field)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockPermissionsCreateCall_Fields_Call) Return(_a0 gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_Fields_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsCreateCall_Fields_Call) RunAndReturn(run func(...googleapi.Field) gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_Fields_Call {
	_c.Call.Return(run)
	return _c
}

// SendNotificationEmail provides a mock function with given fields: sendNotificationEmail
func (_m *MockPermissionsCreateCall) SendNotificationEmail(sendNotificationEmail bool) gdrive.PermissionsCreateCall {
	ret := _m.Called(sendNotificationEmail)

	if len(ret) == 0 {
		panic("no return value specified for SendNotificationEmail")
	}

	var r0 gdrive.PermissionsCreateCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.PermissionsCreateCall); ok {
		r0 = rf(sendNotificationEmail)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsCreateCall)
		}
	}

	return r0
}

// MockPermissionsCreateCall_SendNotificationEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendNotificationEmail'
type MockPermissionsCreateCall_SendNotificationEmail_Call struct {
	*mock.Call
}

// SendNotificationEmail is a helper method to define mock.On call
//   - sendNotificationEmail bool
func (_e *MockPermissionsCreateCall_Expecter) SendNotificationEmail(sendNotificationEmail interface{}) *MockPermissionsCreateCall_SendNotificationEmail_Call {
	return &MockPermissionsCreateCall_SendNotificationEmail_Call{Call: _e.mock.On("SendNotificationEmail", sendNotificationEmail)}
}

func (_c *MockPermissionsCreateCall_SendNotificationEmail_Call) Run(run func(sendNotificationEmail bool)) *MockPermissionsCreateCall_SendNotificationEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockPermissionsCreateCall_SendNotificationEmail_Call) Return(_a0 gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_SendNotificationEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsCreateCall_SendNotificationEmail_Call) RunAndReturn(run func(bool) gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_SendNotificationEmail_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockPermissionsCreateCall) SupportsAllDrives(supportsAllDrives bool) gdrive.PermissionsCreateCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.PermissionsCreateCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.PermissionsCreateCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsCreateCall)
		}
	}

	return r0
}

// MockPermissionsCreateCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockPermissionsCreateCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockPermissionsCreateCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockPermissionsCreateCall_SupportsAllDrives_Call {
	return &MockPermissionsCreateCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockPermissionsCreateCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockPermissionsCreateCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockPermissionsCreateCall_SupportsAllDrives_Call) Return(_a0 gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsCreateCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.PermissionsCreateCall) *MockPermissionsCreateCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPermissionsCreateCall creates a new instance of MockPermissionsCreateCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPermissionsCreateCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPermissionsCreateCall {
	mock := &MockPermissionsCreateCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
