// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesListCall is an autogenerated mock type for the FilesListCall type
type MockFilesListCall struct {
	mock.Mock
}

type MockFilesListCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesListCall) EXPECT() *MockFilesListCall_Expecter {
	return &MockFilesListCall_Expecter{mock: &_m.Mock}
}

// Corpora provides a mock function with given fields: corpora
func (_m *MockFilesListCall) Corpora(corpora string) *drive.FilesListCall {
	ret := _m.Called(corpora)

	if len(ret) == 0 {
		panic("no return value specified for Corpora")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(string) *drive.FilesListCall); ok {
		r0 = rf(corpora)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_Corpora_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Corpora'
type MockFilesListCall_Corpora_Call struct {
	*mock.Call
}

// Corpora is a helper method to define mock.On call
//   - corpora string
func (_e *MockFilesListCall_Expecter) Corpora(corpora interface{}) *MockFilesListCall_Corpora_Call {
	return &MockFilesListCall_Corpora_Call{Call: _e.mock.On("Corpora", corpora)}
}

func (_c *MockFilesListCall_Corpora_Call) Run(run func(corpora string)) *MockFilesListCall_Corpora_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFilesListCall_Corpora_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_Corpora_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_Corpora_Call) RunAndReturn(run func(string) *drive.FilesListCall) *MockFilesListCall_Corpora_Call {
	_c.Call.Return(run)
	return _c
}

// Do provides a mock function with given fields: opts
func (_m *MockFilesListCall) Do(opts ...googleapi.CallOption) (*drive.FileList, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.FileList
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.FileList, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.FileList); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FileList)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFilesListCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockFilesListCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesListCall_Expecter) Do(opts ...interface{}) *MockFilesListCall_Do_Call {
	return &MockFilesListCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesListCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesListCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesListCall_Do_Call) Return(_a0 *drive.FileList, _a1 error) *MockFilesListCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFilesListCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.FileList, error)) *MockFilesListCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// DriveId provides a mock function with given fields: driveId
func (_m *MockFilesListCall) DriveId(driveId string) *drive.FilesListCall {
	ret := _m.Called(driveId)

	if len(ret) == 0 {
		panic("no return value specified for DriveId")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(string) *drive.FilesListCall); ok {
		r0 = rf(driveId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_DriveId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DriveId'
type MockFilesListCall_DriveId_Call struct {
	*mock.Call
}

// DriveId is a helper method to define mock.On call
//   - driveId string
func (_e *MockFilesListCall_Expecter) DriveId(driveId interface{}) *MockFilesListCall_DriveId_Call {
	return &MockFilesListCall_DriveId_Call{Call: _e.mock.On("DriveId", driveId)}
}

func (_c *MockFilesListCall_DriveId_Call) Run(run func(driveId string)) *MockFilesListCall_DriveId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFilesListCall_DriveId_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_DriveId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_DriveId_Call) RunAndReturn(run func(string) *drive.FilesListCall) *MockFilesListCall_DriveId_Call {
	_c.Call.Return(run)
	return _c
}

// Fields provides a mock function with given fields: fields
func (_m *MockFilesListCall) Fields(fields ...googleapi.Field) *drive.FilesListCall {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Fields")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(...googleapi.Field) *drive.FilesListCall); ok {
		r0 = rf(fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_Fields_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Fields'
type MockFilesListCall_Fields_Call struct {
	*mock.Call
}

// Fields is a helper method to define mock.On call
//   - fields ...googleapi.Field
func (_e *MockFilesListCall_Expecter) Fields(fields ...interface{}) *MockFilesListCall_Fields_Call {
	return &MockFilesListCall_Fields_Call{Call: _e.mock.On("Fields",
		append([]interface{}{}, fields...)...)}
}

func (_c *MockFilesListCall_Fields_Call) Run(run func(fields ...googleapi.Field)) *MockFilesListCall_Fields_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.Field, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.Field)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesListCall_Fields_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_Fields_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_Fields_Call) RunAndReturn(run func(...googleapi.Field) *drive.FilesListCall) *MockFilesListCall_Fields_Call {
	_c.Call.Return(run)
	return _c
}

// IncludeItemsFromAllDrives provides a mock function with given fields: includeItemsFromAllDrives
func (_m *MockFilesListCall) IncludeItemsFromAllDrives(includeItemsFromAllDrives bool) *drive.FilesListCall {
	ret := _m.Called(includeItemsFromAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for IncludeItemsFromAllDrives")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(bool) *drive.FilesListCall); ok {
		r0 = rf(includeItemsFromAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_IncludeItemsFromAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncludeItemsFromAllDrives'
type MockFilesListCall_IncludeItemsFromAllDrives_Call struct {
	*mock.Call
}

// IncludeItemsFromAllDrives is a helper method to define mock.On call
//   - includeItemsFromAllDrives bool
func (_e *MockFilesListCall_Expecter) IncludeItemsFromAllDrives(includeItemsFromAllDrives interface{}) *MockFilesListCall_IncludeItemsFromAllDrives_Call {
	return &MockFilesListCall_IncludeItemsFromAllDrives_Call{Call: _e.mock.On("IncludeItemsFromAllDrives", includeItemsFromAllDrives)}
}

func (_c *MockFilesListCall_IncludeItemsFromAllDrives_Call) Run(run func(includeItemsFromAllDrives bool)) *MockFilesListCall_IncludeItemsFromAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesListCall_IncludeItemsFromAllDrives_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_IncludeItemsFromAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_IncludeItemsFromAllDrives_Call) RunAndReturn(run func(bool) *drive.FilesListCall) *MockFilesListCall_IncludeItemsFromAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// PageSize provides a mock function with given fields: pageSize
func (_m *MockFilesListCall) PageSize(pageSize int64) *drive.FilesListCall {
	ret := _m.Called(pageSize)

	if len(ret) == 0 {
		panic("no return value specified for PageSize")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(int64) *drive.FilesListCall); ok {
		r0 = rf(pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_PageSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PageSize'
type MockFilesListCall_PageSize_Call struct {
	*mock.Call
}

// PageSize is a helper method to define mock.On call
//   - pageSize int64
func (_e *MockFilesListCall_Expecter) PageSize(pageSize interface{}) *MockFilesListCall_PageSize_Call {
	return &MockFilesListCall_PageSize_Call{Call: _e.mock.On("PageSize", pageSize)}
}

func (_c *MockFilesListCall_PageSize_Call) Run(run func(pageSize int64)) *MockFilesListCall_PageSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64))
	})
	return _c
}

func (_c *MockFilesListCall_PageSize_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_PageSize_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_PageSize_Call) RunAndReturn(run func(int64) *drive.FilesListCall) *MockFilesListCall_PageSize_Call {
	_c.Call.Return(run)
	return _c
}

// Q provides a mock function with given fields: q
func (_m *MockFilesListCall) Q(q string) *drive.FilesListCall {
	ret := _m.Called(q)

	if len(ret) == 0 {
		panic("no return value specified for Q")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(string) *drive.FilesListCall); ok {
		r0 = rf(q)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_Q_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Q'
type MockFilesListCall_Q_Call struct {
	*mock.Call
}

// Q is a helper method to define mock.On call
//   - q string
func (_e *MockFilesListCall_Expecter) Q(q interface{}) *MockFilesListCall_Q_Call {
	return &MockFilesListCall_Q_Call{Call: _e.mock.On("Q", q)}
}

func (_c *MockFilesListCall_Q_Call) Run(run func(q string)) *MockFilesListCall_Q_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFilesListCall_Q_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_Q_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_Q_Call) RunAndReturn(run func(string) *drive.FilesListCall) *MockFilesListCall_Q_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockFilesListCall) SupportsAllDrives(supportsAllDrives bool) *drive.FilesListCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 *drive.FilesListCall
	if rf, ok := ret.Get(0).(func(bool) *drive.FilesListCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.FilesListCall)
		}
	}

	return r0
}

// MockFilesListCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockFilesListCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockFilesListCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockFilesListCall_SupportsAllDrives_Call {
	return &MockFilesListCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockFilesListCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockFilesListCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesListCall_SupportsAllDrives_Call) Return(_a0 *drive.FilesListCall) *MockFilesListCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesListCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) *drive.FilesListCall) *MockFilesListCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesListCall creates a new instance of MockFilesListCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesListCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesListCall {
	mock := &MockFilesListCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
