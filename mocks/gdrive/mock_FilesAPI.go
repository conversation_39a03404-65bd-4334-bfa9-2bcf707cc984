// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesAPI is an autogenerated mock type for the FilesAPI type
type MockFilesAPI struct {
	mock.Mock
}

type MockFilesAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesAPI) EXPECT() *MockFilesAPI_Expecter {
	return &MockFilesAPI_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: file
func (_m *MockFilesAPI) Create(file *drive.File) gdrive.FilesCreateCall {
	ret := _m.Called(file)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 gdrive.FilesCreateCall
	if rf, ok := ret.Get(0).(func(*drive.File) gdrive.FilesCreateCall); ok {
		r0 = rf(file)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesCreateCall)
		}
	}

	return r0
}

// MockFilesAPI_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockFilesAPI_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - file *drive.File
func (_e *MockFilesAPI_Expecter) Create(file interface{}) *MockFilesAPI_Create_Call {
	return &MockFilesAPI_Create_Call{Call: _e.mock.On("Create", file)}
}

func (_c *MockFilesAPI_Create_Call) Run(run func(file *drive.File)) *MockFilesAPI_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*drive.File))
	})
	return _c
}

func (_c *MockFilesAPI_Create_Call) Return(_a0 gdrive.FilesCreateCall) *MockFilesAPI_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesAPI_Create_Call) RunAndReturn(run func(*drive.File) gdrive.FilesCreateCall) *MockFilesAPI_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: fileId
func (_m *MockFilesAPI) Delete(fileId string) gdrive.FilesDeleteCall {
	ret := _m.Called(fileId)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 gdrive.FilesDeleteCall
	if rf, ok := ret.Get(0).(func(string) gdrive.FilesDeleteCall); ok {
		r0 = rf(fileId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesDeleteCall)
		}
	}

	return r0
}

// MockFilesAPI_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockFilesAPI_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - fileId string
func (_e *MockFilesAPI_Expecter) Delete(fileId interface{}) *MockFilesAPI_Delete_Call {
	return &MockFilesAPI_Delete_Call{Call: _e.mock.On("Delete", fileId)}
}

func (_c *MockFilesAPI_Delete_Call) Run(run func(fileId string)) *MockFilesAPI_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFilesAPI_Delete_Call) Return(_a0 gdrive.FilesDeleteCall) *MockFilesAPI_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesAPI_Delete_Call) RunAndReturn(run func(string) gdrive.FilesDeleteCall) *MockFilesAPI_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: fileId
func (_m *MockFilesAPI) Get(fileId string) gdrive.FilesGetCall {
	ret := _m.Called(fileId)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 gdrive.FilesGetCall
	if rf, ok := ret.Get(0).(func(string) gdrive.FilesGetCall); ok {
		r0 = rf(fileId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesGetCall)
		}
	}

	return r0
}

// MockFilesAPI_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockFilesAPI_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - fileId string
func (_e *MockFilesAPI_Expecter) Get(fileId interface{}) *MockFilesAPI_Get_Call {
	return &MockFilesAPI_Get_Call{Call: _e.mock.On("Get", fileId)}
}

func (_c *MockFilesAPI_Get_Call) Run(run func(fileId string)) *MockFilesAPI_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFilesAPI_Get_Call) Return(_a0 gdrive.FilesGetCall) *MockFilesAPI_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesAPI_Get_Call) RunAndReturn(run func(string) gdrive.FilesGetCall) *MockFilesAPI_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with no fields
func (_m *MockFilesAPI) List() gdrive.FilesListCall {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 gdrive.FilesListCall
	if rf, ok := ret.Get(0).(func() gdrive.FilesListCall); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesListCall)
		}
	}

	return r0
}

// MockFilesAPI_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockFilesAPI_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
func (_e *MockFilesAPI_Expecter) List() *MockFilesAPI_List_Call {
	return &MockFilesAPI_List_Call{Call: _e.mock.On("List")}
}

func (_c *MockFilesAPI_List_Call) Run(run func()) *MockFilesAPI_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockFilesAPI_List_Call) Return(_a0 gdrive.FilesListCall) *MockFilesAPI_List_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesAPI_List_Call) RunAndReturn(run func() gdrive.FilesListCall) *MockFilesAPI_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: fileId, file
func (_m *MockFilesAPI) Update(fileId string, file *drive.File) gdrive.FilesUpdateCall {
	ret := _m.Called(fileId, file)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 gdrive.FilesUpdateCall
	if rf, ok := ret.Get(0).(func(string, *drive.File) gdrive.FilesUpdateCall); ok {
		r0 = rf(fileId, file)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesUpdateCall)
		}
	}

	return r0
}

// MockFilesAPI_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockFilesAPI_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - fileId string
//   - file *drive.File
func (_e *MockFilesAPI_Expecter) Update(fileId interface{}, file interface{}) *MockFilesAPI_Update_Call {
	return &MockFilesAPI_Update_Call{Call: _e.mock.On("Update", fileId, file)}
}

func (_c *MockFilesAPI_Update_Call) Run(run func(fileId string, file *drive.File)) *MockFilesAPI_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(*drive.File))
	})
	return _c
}

func (_c *MockFilesAPI_Update_Call) Return(_a0 gdrive.FilesUpdateCall) *MockFilesAPI_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesAPI_Update_Call) RunAndReturn(run func(string, *drive.File) gdrive.FilesUpdateCall) *MockFilesAPI_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesAPI creates a new instance of MockFilesAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesAPI {
	mock := &MockFilesAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
