// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockPermissionsDeleteCall is an autogenerated mock type for the PermissionsDeleteCall type
type MockPermissionsDeleteCall struct {
	mock.Mock
}

type MockPermissionsDeleteCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPermissionsDeleteCall) EXPECT() *MockPermissionsDeleteCall_Expecter {
	return &MockPermissionsDeleteCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockPermissionsDeleteCall) Do(opts ...googleapi.CallOption) error {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) error); ok {
		r0 = rf(opts...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPermissionsDeleteCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockPermissionsDeleteCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockPermissionsDeleteCall_Expecter) Do(opts ...interface{}) *MockPermissionsDeleteCall_Do_Call {
	return &MockPermissionsDeleteCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockPermissionsDeleteCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockPermissionsDeleteCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockPermissionsDeleteCall_Do_Call) Return(_a0 error) *MockPermissionsDeleteCall_Do_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsDeleteCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) error) *MockPermissionsDeleteCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockPermissionsDeleteCall) SupportsAllDrives(supportsAllDrives bool) gdrive.PermissionsDeleteCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.PermissionsDeleteCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.PermissionsDeleteCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsDeleteCall)
		}
	}

	return r0
}

// MockPermissionsDeleteCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockPermissionsDeleteCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockPermissionsDeleteCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockPermissionsDeleteCall_SupportsAllDrives_Call {
	return &MockPermissionsDeleteCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockPermissionsDeleteCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockPermissionsDeleteCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockPermissionsDeleteCall_SupportsAllDrives_Call) Return(_a0 gdrive.PermissionsDeleteCall) *MockPermissionsDeleteCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsDeleteCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.PermissionsDeleteCall) *MockPermissionsDeleteCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPermissionsDeleteCall creates a new instance of MockPermissionsDeleteCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPermissionsDeleteCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPermissionsDeleteCall {
	mock := &MockPermissionsDeleteCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
