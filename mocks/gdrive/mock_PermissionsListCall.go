// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockPermissionsListCall is an autogenerated mock type for the PermissionsListCall type
type MockPermissionsListCall struct {
	mock.Mock
}

type MockPermissionsListCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPermissionsListCall) EXPECT() *MockPermissionsListCall_Expecter {
	return &MockPermissionsListCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockPermissionsListCall) Do(opts ...googleapi.CallOption) (*drive.PermissionList, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.PermissionList
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.PermissionList, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.PermissionList); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.PermissionList)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPermissionsListCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockPermissionsListCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockPermissionsListCall_Expecter) Do(opts ...interface{}) *MockPermissionsListCall_Do_Call {
	return &MockPermissionsListCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockPermissionsListCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockPermissionsListCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockPermissionsListCall_Do_Call) Return(_a0 *drive.PermissionList, _a1 error) *MockPermissionsListCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPermissionsListCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.PermissionList, error)) *MockPermissionsListCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Fields provides a mock function with given fields: fields
func (_m *MockPermissionsListCall) Fields(fields ...googleapi.Field) gdrive.PermissionsListCall {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Fields")
	}

	var r0 gdrive.PermissionsListCall
	if rf, ok := ret.Get(0).(func(...googleapi.Field) gdrive.PermissionsListCall); ok {
		r0 = rf(fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsListCall)
		}
	}

	return r0
}

// MockPermissionsListCall_Fields_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Fields'
type MockPermissionsListCall_Fields_Call struct {
	*mock.Call
}

// Fields is a helper method to define mock.On call
//   - fields ...googleapi.Field
func (_e *MockPermissionsListCall_Expecter) Fields(fields ...interface{}) *MockPermissionsListCall_Fields_Call {
	return &MockPermissionsListCall_Fields_Call{Call: _e.mock.On("Fields",
		append([]interface{}{}, fields...)...)}
}

func (_c *MockPermissionsListCall_Fields_Call) Run(run func(fields ...googleapi.Field)) *MockPermissionsListCall_Fields_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.Field, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.Field)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockPermissionsListCall_Fields_Call) Return(_a0 gdrive.PermissionsListCall) *MockPermissionsListCall_Fields_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsListCall_Fields_Call) RunAndReturn(run func(...googleapi.Field) gdrive.PermissionsListCall) *MockPermissionsListCall_Fields_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockPermissionsListCall) SupportsAllDrives(supportsAllDrives bool) gdrive.PermissionsListCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.PermissionsListCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.PermissionsListCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsListCall)
		}
	}

	return r0
}

// MockPermissionsListCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockPermissionsListCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockPermissionsListCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockPermissionsListCall_SupportsAllDrives_Call {
	return &MockPermissionsListCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockPermissionsListCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockPermissionsListCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockPermissionsListCall_SupportsAllDrives_Call) Return(_a0 gdrive.PermissionsListCall) *MockPermissionsListCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsListCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.PermissionsListCall) *MockPermissionsListCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPermissionsListCall creates a new instance of MockPermissionsListCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPermissionsListCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPermissionsListCall {
	mock := &MockPermissionsListCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
