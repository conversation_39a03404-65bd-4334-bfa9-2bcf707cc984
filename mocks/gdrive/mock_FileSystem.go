// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	io "io"

	mock "github.com/stretchr/testify/mock"
)

// MockFileSystem is an autogenerated mock type for the FileSystem type
type MockFileSystem struct {
	mock.Mock
}

type MockFileSystem_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFileSystem) EXPECT() *MockFileSystem_Expecter {
	return &MockFileSystem_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: filename
func (_m *MockFileSystem) Create(filename string) (io.WriteCloser, error) {
	ret := _m.Called(filename)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 io.WriteCloser
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (io.WriteCloser, error)); ok {
		return rf(filename)
	}
	if rf, ok := ret.Get(0).(func(string) io.WriteCloser); ok {
		r0 = rf(filename)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(io.WriteCloser)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(filename)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileSystem_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockFileSystem_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - filename string
func (_e *MockFileSystem_Expecter) Create(filename interface{}) *MockFileSystem_Create_Call {
	return &MockFileSystem_Create_Call{Call: _e.mock.On("Create", filename)}
}

func (_c *MockFileSystem_Create_Call) Run(run func(filename string)) *MockFileSystem_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFileSystem_Create_Call) Return(_a0 io.WriteCloser, _a1 error) *MockFileSystem_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileSystem_Create_Call) RunAndReturn(run func(string) (io.WriteCloser, error)) *MockFileSystem_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Open provides a mock function with given fields: filename
func (_m *MockFileSystem) Open(filename string) (io.ReadCloser, error) {
	ret := _m.Called(filename)

	if len(ret) == 0 {
		panic("no return value specified for Open")
	}

	var r0 io.ReadCloser
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (io.ReadCloser, error)); ok {
		return rf(filename)
	}
	if rf, ok := ret.Get(0).(func(string) io.ReadCloser); ok {
		r0 = rf(filename)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(io.ReadCloser)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(filename)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFileSystem_Open_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Open'
type MockFileSystem_Open_Call struct {
	*mock.Call
}

// Open is a helper method to define mock.On call
//   - filename string
func (_e *MockFileSystem_Expecter) Open(filename interface{}) *MockFileSystem_Open_Call {
	return &MockFileSystem_Open_Call{Call: _e.mock.On("Open", filename)}
}

func (_c *MockFileSystem_Open_Call) Run(run func(filename string)) *MockFileSystem_Open_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFileSystem_Open_Call) Return(_a0 io.ReadCloser, _a1 error) *MockFileSystem_Open_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFileSystem_Open_Call) RunAndReturn(run func(string) (io.ReadCloser, error)) *MockFileSystem_Open_Call {
	_c.Call.Return(run)
	return _c
}

// Remove provides a mock function with given fields: filename
func (_m *MockFileSystem) Remove(filename string) error {
	ret := _m.Called(filename)

	if len(ret) == 0 {
		panic("no return value specified for Remove")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(filename)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileSystem_Remove_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Remove'
type MockFileSystem_Remove_Call struct {
	*mock.Call
}

// Remove is a helper method to define mock.On call
//   - filename string
func (_e *MockFileSystem_Expecter) Remove(filename interface{}) *MockFileSystem_Remove_Call {
	return &MockFileSystem_Remove_Call{Call: _e.mock.On("Remove", filename)}
}

func (_c *MockFileSystem_Remove_Call) Run(run func(filename string)) *MockFileSystem_Remove_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockFileSystem_Remove_Call) Return(_a0 error) *MockFileSystem_Remove_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileSystem_Remove_Call) RunAndReturn(run func(string) error) *MockFileSystem_Remove_Call {
	_c.Call.Return(run)
	return _c
}

// WriteFile provides a mock function with given fields: filename, data, perm
func (_m *MockFileSystem) WriteFile(filename string, data []byte, perm uint32) error {
	ret := _m.Called(filename, data, perm)

	if len(ret) == 0 {
		panic("no return value specified for WriteFile")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, []byte, uint32) error); ok {
		r0 = rf(filename, data, perm)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFileSystem_WriteFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteFile'
type MockFileSystem_WriteFile_Call struct {
	*mock.Call
}

// WriteFile is a helper method to define mock.On call
//   - filename string
//   - data []byte
//   - perm uint32
func (_e *MockFileSystem_Expecter) WriteFile(filename interface{}, data interface{}, perm interface{}) *MockFileSystem_WriteFile_Call {
	return &MockFileSystem_WriteFile_Call{Call: _e.mock.On("WriteFile", filename, data, perm)}
}

func (_c *MockFileSystem_WriteFile_Call) Run(run func(filename string, data []byte, perm uint32)) *MockFileSystem_WriteFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].([]byte), args[2].(uint32))
	})
	return _c
}

func (_c *MockFileSystem_WriteFile_Call) Return(_a0 error) *MockFileSystem_WriteFile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFileSystem_WriteFile_Call) RunAndReturn(run func(string, []byte, uint32) error) *MockFileSystem_WriteFile_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFileSystem creates a new instance of MockFileSystem. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFileSystem(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFileSystem {
	mock := &MockFileSystem{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
