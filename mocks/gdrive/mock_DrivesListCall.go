// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockDrivesListCall is an autogenerated mock type for the DrivesListCall type
type MockDrivesListCall struct {
	mock.Mock
}

type MockDrivesListCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDrivesListCall) EXPECT() *MockDrivesListCall_Expecter {
	return &MockDrivesListCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockDrivesListCall) Do(opts ...googleapi.CallOption) (*drive.DriveList, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.DriveList
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.DriveList, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.DriveList); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.DriveList)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDrivesListCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockDrivesListCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockDrivesListCall_Expecter) Do(opts ...interface{}) *MockDrivesListCall_Do_Call {
	return &MockDrivesListCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockDrivesListCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockDrivesListCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDrivesListCall_Do_Call) Return(_a0 *drive.DriveList, _a1 error) *MockDrivesListCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDrivesListCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.DriveList, error)) *MockDrivesListCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// PageSize provides a mock function with given fields: pageSize
func (_m *MockDrivesListCall) PageSize(pageSize int64) *drive.DrivesListCall {
	ret := _m.Called(pageSize)

	if len(ret) == 0 {
		panic("no return value specified for PageSize")
	}

	var r0 *drive.DrivesListCall
	if rf, ok := ret.Get(0).(func(int64) *drive.DrivesListCall); ok {
		r0 = rf(pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.DrivesListCall)
		}
	}

	return r0
}

// MockDrivesListCall_PageSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PageSize'
type MockDrivesListCall_PageSize_Call struct {
	*mock.Call
}

// PageSize is a helper method to define mock.On call
//   - pageSize int64
func (_e *MockDrivesListCall_Expecter) PageSize(pageSize interface{}) *MockDrivesListCall_PageSize_Call {
	return &MockDrivesListCall_PageSize_Call{Call: _e.mock.On("PageSize", pageSize)}
}

func (_c *MockDrivesListCall_PageSize_Call) Run(run func(pageSize int64)) *MockDrivesListCall_PageSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64))
	})
	return _c
}

func (_c *MockDrivesListCall_PageSize_Call) Return(_a0 *drive.DrivesListCall) *MockDrivesListCall_PageSize_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDrivesListCall_PageSize_Call) RunAndReturn(run func(int64) *drive.DrivesListCall) *MockDrivesListCall_PageSize_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDrivesListCall creates a new instance of MockDrivesListCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDrivesListCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDrivesListCall {
	mock := &MockDrivesListCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
