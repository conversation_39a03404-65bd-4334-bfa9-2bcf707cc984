// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	drive "google.golang.org/api/drive/v3"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// MockDriveAPIWrapper is an autogenerated mock type for the DriveAPIWrapper type
type MockDriveAPIWrapper struct {
	mock.Mock
}

type MockDriveAPIWrapper_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDriveAPIWrapper) EXPECT() *MockDriveAPIWrapper_Expecter {
	return &MockDriveAPIWrapper_Expecter{mock: &_m.Mock}
}

// CreateFile provides a mock function with given fields: file, media, supportsAllDrives
func (_m *MockDriveAPIWrapper) CreateFile(file *drive.File, media io.Reader, supportsAllDrives bool) (*drive.File, error) {
	ret := _m.Called(file, media, supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for CreateFile")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(*drive.File, io.Reader, bool) (*drive.File, error)); ok {
		return rf(file, media, supportsAllDrives)
	}
	if rf, ok := ret.Get(0).(func(*drive.File, io.Reader, bool) *drive.File); ok {
		r0 = rf(file, media, supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(*drive.File, io.Reader, bool) error); ok {
		r1 = rf(file, media, supportsAllDrives)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_CreateFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFile'
type MockDriveAPIWrapper_CreateFile_Call struct {
	*mock.Call
}

// CreateFile is a helper method to define mock.On call
//   - file *drive.File
//   - media io.Reader
//   - supportsAllDrives bool
func (_e *MockDriveAPIWrapper_Expecter) CreateFile(file interface{}, media interface{}, supportsAllDrives interface{}) *MockDriveAPIWrapper_CreateFile_Call {
	return &MockDriveAPIWrapper_CreateFile_Call{Call: _e.mock.On("CreateFile", file, media, supportsAllDrives)}
}

func (_c *MockDriveAPIWrapper_CreateFile_Call) Run(run func(file *drive.File, media io.Reader, supportsAllDrives bool)) *MockDriveAPIWrapper_CreateFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*drive.File), args[1].(io.Reader), args[2].(bool))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_CreateFile_Call) Return(_a0 *drive.File, _a1 error) *MockDriveAPIWrapper_CreateFile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_CreateFile_Call) RunAndReturn(run func(*drive.File, io.Reader, bool) (*drive.File, error)) *MockDriveAPIWrapper_CreateFile_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileDownload provides a mock function with given fields: fileID
func (_m *MockDriveAPIWrapper) GetFileDownload(fileID string) (io.ReadCloser, error) {
	ret := _m.Called(fileID)

	if len(ret) == 0 {
		panic("no return value specified for GetFileDownload")
	}

	var r0 io.ReadCloser
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (io.ReadCloser, error)); ok {
		return rf(fileID)
	}
	if rf, ok := ret.Get(0).(func(string) io.ReadCloser); ok {
		r0 = rf(fileID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(io.ReadCloser)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(fileID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_GetFileDownload_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileDownload'
type MockDriveAPIWrapper_GetFileDownload_Call struct {
	*mock.Call
}

// GetFileDownload is a helper method to define mock.On call
//   - fileID string
func (_e *MockDriveAPIWrapper_Expecter) GetFileDownload(fileID interface{}) *MockDriveAPIWrapper_GetFileDownload_Call {
	return &MockDriveAPIWrapper_GetFileDownload_Call{Call: _e.mock.On("GetFileDownload", fileID)}
}

func (_c *MockDriveAPIWrapper_GetFileDownload_Call) Run(run func(fileID string)) *MockDriveAPIWrapper_GetFileDownload_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_GetFileDownload_Call) Return(_a0 io.ReadCloser, _a1 error) *MockDriveAPIWrapper_GetFileDownload_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_GetFileDownload_Call) RunAndReturn(run func(string) (io.ReadCloser, error)) *MockDriveAPIWrapper_GetFileDownload_Call {
	_c.Call.Return(run)
	return _c
}

// ListDrives provides a mock function with given fields: pageSize
func (_m *MockDriveAPIWrapper) ListDrives(pageSize int) ([]*drive.Drive, error) {
	ret := _m.Called(pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ListDrives")
	}

	var r0 []*drive.Drive
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*drive.Drive, error)); ok {
		return rf(pageSize)
	}
	if rf, ok := ret.Get(0).(func(int) []*drive.Drive); ok {
		r0 = rf(pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.Drive)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pageSize)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_ListDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDrives'
type MockDriveAPIWrapper_ListDrives_Call struct {
	*mock.Call
}

// ListDrives is a helper method to define mock.On call
//   - pageSize int
func (_e *MockDriveAPIWrapper_Expecter) ListDrives(pageSize interface{}) *MockDriveAPIWrapper_ListDrives_Call {
	return &MockDriveAPIWrapper_ListDrives_Call{Call: _e.mock.On("ListDrives", pageSize)}
}

func (_c *MockDriveAPIWrapper_ListDrives_Call) Run(run func(pageSize int)) *MockDriveAPIWrapper_ListDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_ListDrives_Call) Return(_a0 []*drive.Drive, _a1 error) *MockDriveAPIWrapper_ListDrives_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_ListDrives_Call) RunAndReturn(run func(int) ([]*drive.Drive, error)) *MockDriveAPIWrapper_ListDrives_Call {
	_c.Call.Return(run)
	return _c
}

// ListFiles provides a mock function with given fields: pageSize, fields
func (_m *MockDriveAPIWrapper) ListFiles(pageSize int, fields string) ([]*drive.File, error) {
	ret := _m.Called(pageSize, fields)

	if len(ret) == 0 {
		panic("no return value specified for ListFiles")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]*drive.File, error)); ok {
		return rf(pageSize, fields)
	}
	if rf, ok := ret.Get(0).(func(int, string) []*drive.File); ok {
		r0 = rf(pageSize, fields)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(pageSize, fields)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_ListFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFiles'
type MockDriveAPIWrapper_ListFiles_Call struct {
	*mock.Call
}

// ListFiles is a helper method to define mock.On call
//   - pageSize int
//   - fields string
func (_e *MockDriveAPIWrapper_Expecter) ListFiles(pageSize interface{}, fields interface{}) *MockDriveAPIWrapper_ListFiles_Call {
	return &MockDriveAPIWrapper_ListFiles_Call{Call: _e.mock.On("ListFiles", pageSize, fields)}
}

func (_c *MockDriveAPIWrapper_ListFiles_Call) Run(run func(pageSize int, fields string)) *MockDriveAPIWrapper_ListFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(string))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_ListFiles_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveAPIWrapper_ListFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_ListFiles_Call) RunAndReturn(run func(int, string) ([]*drive.File, error)) *MockDriveAPIWrapper_ListFiles_Call {
	_c.Call.Return(run)
	return _c
}

// ListFilesInDrive provides a mock function with given fields: driveID, pageSize, fields
func (_m *MockDriveAPIWrapper) ListFilesInDrive(driveID string, pageSize int, fields string) ([]*drive.File, error) {
	ret := _m.Called(driveID, pageSize, fields)

	if len(ret) == 0 {
		panic("no return value specified for ListFilesInDrive")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, int, string) ([]*drive.File, error)); ok {
		return rf(driveID, pageSize, fields)
	}
	if rf, ok := ret.Get(0).(func(string, int, string) []*drive.File); ok {
		r0 = rf(driveID, pageSize, fields)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, int, string) error); ok {
		r1 = rf(driveID, pageSize, fields)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_ListFilesInDrive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFilesInDrive'
type MockDriveAPIWrapper_ListFilesInDrive_Call struct {
	*mock.Call
}

// ListFilesInDrive is a helper method to define mock.On call
//   - driveID string
//   - pageSize int
//   - fields string
func (_e *MockDriveAPIWrapper_Expecter) ListFilesInDrive(driveID interface{}, pageSize interface{}, fields interface{}) *MockDriveAPIWrapper_ListFilesInDrive_Call {
	return &MockDriveAPIWrapper_ListFilesInDrive_Call{Call: _e.mock.On("ListFilesInDrive", driveID, pageSize, fields)}
}

func (_c *MockDriveAPIWrapper_ListFilesInDrive_Call) Run(run func(driveID string, pageSize int, fields string)) *MockDriveAPIWrapper_ListFilesInDrive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(int), args[2].(string))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_ListFilesInDrive_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveAPIWrapper_ListFilesInDrive_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_ListFilesInDrive_Call) RunAndReturn(run func(string, int, string) ([]*drive.File, error)) *MockDriveAPIWrapper_ListFilesInDrive_Call {
	_c.Call.Return(run)
	return _c
}

// SearchFiles provides a mock function with given fields: query, driveID, fields
func (_m *MockDriveAPIWrapper) SearchFiles(query string, driveID string, fields string) ([]*drive.File, error) {
	ret := _m.Called(query, driveID, fields)

	if len(ret) == 0 {
		panic("no return value specified for SearchFiles")
	}

	var r0 []*drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) ([]*drive.File, error)); ok {
		return rf(query, driveID, fields)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) []*drive.File); ok {
		r0 = rf(query, driveID, fields)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(query, driveID, fields)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDriveAPIWrapper_SearchFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchFiles'
type MockDriveAPIWrapper_SearchFiles_Call struct {
	*mock.Call
}

// SearchFiles is a helper method to define mock.On call
//   - query string
//   - driveID string
//   - fields string
func (_e *MockDriveAPIWrapper_Expecter) SearchFiles(query interface{}, driveID interface{}, fields interface{}) *MockDriveAPIWrapper_SearchFiles_Call {
	return &MockDriveAPIWrapper_SearchFiles_Call{Call: _e.mock.On("SearchFiles", query, driveID, fields)}
}

func (_c *MockDriveAPIWrapper_SearchFiles_Call) Run(run func(query string, driveID string, fields string)) *MockDriveAPIWrapper_SearchFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockDriveAPIWrapper_SearchFiles_Call) Return(_a0 []*drive.File, _a1 error) *MockDriveAPIWrapper_SearchFiles_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDriveAPIWrapper_SearchFiles_Call) RunAndReturn(run func(string, string, string) ([]*drive.File, error)) *MockDriveAPIWrapper_SearchFiles_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDriveAPIWrapper creates a new instance of MockDriveAPIWrapper. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDriveAPIWrapper(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDriveAPIWrapper {
	mock := &MockDriveAPIWrapper{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
