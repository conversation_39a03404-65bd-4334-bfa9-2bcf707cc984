// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockFilesUpdateCall is an autogenerated mock type for the FilesUpdateCall type
type MockFilesUpdateCall struct {
	mock.Mock
}

type MockFilesUpdateCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFilesUpdateCall) EXPECT() *MockFilesUpdateCall_Expecter {
	return &MockFilesUpdateCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockFilesUpdateCall) Do(opts ...googleapi.CallOption) (*drive.File, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.File
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.File, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.File); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.File)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFilesUpdateCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockFilesUpdateCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockFilesUpdateCall_Expecter) Do(opts ...interface{}) *MockFilesUpdateCall_Do_Call {
	return &MockFilesUpdateCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockFilesUpdateCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockFilesUpdateCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockFilesUpdateCall_Do_Call) Return(_a0 *drive.File, _a1 error) *MockFilesUpdateCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFilesUpdateCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.File, error)) *MockFilesUpdateCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// SupportsAllDrives provides a mock function with given fields: supportsAllDrives
func (_m *MockFilesUpdateCall) SupportsAllDrives(supportsAllDrives bool) gdrive.FilesUpdateCall {
	ret := _m.Called(supportsAllDrives)

	if len(ret) == 0 {
		panic("no return value specified for SupportsAllDrives")
	}

	var r0 gdrive.FilesUpdateCall
	if rf, ok := ret.Get(0).(func(bool) gdrive.FilesUpdateCall); ok {
		r0 = rf(supportsAllDrives)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.FilesUpdateCall)
		}
	}

	return r0
}

// MockFilesUpdateCall_SupportsAllDrives_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportsAllDrives'
type MockFilesUpdateCall_SupportsAllDrives_Call struct {
	*mock.Call
}

// SupportsAllDrives is a helper method to define mock.On call
//   - supportsAllDrives bool
func (_e *MockFilesUpdateCall_Expecter) SupportsAllDrives(supportsAllDrives interface{}) *MockFilesUpdateCall_SupportsAllDrives_Call {
	return &MockFilesUpdateCall_SupportsAllDrives_Call{Call: _e.mock.On("SupportsAllDrives", supportsAllDrives)}
}

func (_c *MockFilesUpdateCall_SupportsAllDrives_Call) Run(run func(supportsAllDrives bool)) *MockFilesUpdateCall_SupportsAllDrives_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockFilesUpdateCall_SupportsAllDrives_Call) Return(_a0 gdrive.FilesUpdateCall) *MockFilesUpdateCall_SupportsAllDrives_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFilesUpdateCall_SupportsAllDrives_Call) RunAndReturn(run func(bool) gdrive.FilesUpdateCall) *MockFilesUpdateCall_SupportsAllDrives_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFilesUpdateCall creates a new instance of MockFilesUpdateCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFilesUpdateCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFilesUpdateCall {
	mock := &MockFilesUpdateCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
