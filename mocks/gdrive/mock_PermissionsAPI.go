// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	mock "github.com/stretchr/testify/mock"
)

// MockPermissionsAPI is an autogenerated mock type for the PermissionsAPI type
type MockPermissionsAPI struct {
	mock.Mock
}

type MockPermissionsAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPermissionsAPI) EXPECT() *MockPermissionsAPI_Expecter {
	return &MockPermissionsAPI_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: fileId, permission
func (_m *MockPermissionsAPI) Create(fileId string, permission *drive.Permission) gdrive.PermissionsCreateCall {
	ret := _m.Called(fileId, permission)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 gdrive.PermissionsCreateCall
	if rf, ok := ret.Get(0).(func(string, *drive.Permission) gdrive.PermissionsCreateCall); ok {
		r0 = rf(fileId, permission)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsCreateCall)
		}
	}

	return r0
}

// MockPermissionsAPI_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockPermissionsAPI_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - fileId string
//   - permission *drive.Permission
func (_e *MockPermissionsAPI_Expecter) Create(fileId interface{}, permission interface{}) *MockPermissionsAPI_Create_Call {
	return &MockPermissionsAPI_Create_Call{Call: _e.mock.On("Create", fileId, permission)}
}

func (_c *MockPermissionsAPI_Create_Call) Run(run func(fileId string, permission *drive.Permission)) *MockPermissionsAPI_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(*drive.Permission))
	})
	return _c
}

func (_c *MockPermissionsAPI_Create_Call) Return(_a0 gdrive.PermissionsCreateCall) *MockPermissionsAPI_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsAPI_Create_Call) RunAndReturn(run func(string, *drive.Permission) gdrive.PermissionsCreateCall) *MockPermissionsAPI_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: fileId, permissionId
func (_m *MockPermissionsAPI) Delete(fileId string, permissionId string) gdrive.PermissionsDeleteCall {
	ret := _m.Called(fileId, permissionId)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 gdrive.PermissionsDeleteCall
	if rf, ok := ret.Get(0).(func(string, string) gdrive.PermissionsDeleteCall); ok {
		r0 = rf(fileId, permissionId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsDeleteCall)
		}
	}

	return r0
}

// MockPermissionsAPI_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockPermissionsAPI_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - fileId string
//   - permissionId string
func (_e *MockPermissionsAPI_Expecter) Delete(fileId interface{}, permissionId interface{}) *MockPermissionsAPI_Delete_Call {
	return &MockPermissionsAPI_Delete_Call{Call: _e.mock.On("Delete", fileId, permissionId)}
}

func (_c *MockPermissionsAPI_Delete_Call) Run(run func(fileId string, permissionId string)) *MockPermissionsAPI_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockPermissionsAPI_Delete_Call) Return(_a0 gdrive.PermissionsDeleteCall) *MockPermissionsAPI_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsAPI_Delete_Call) RunAndReturn(run func(string, string) gdrive.PermissionsDeleteCall) *MockPermissionsAPI_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: fileId, permissionId
func (_m *MockPermissionsAPI) Get(fileId string, permissionId string) gdrive.PermissionsGetCall {
	ret := _m.Called(fileId, permissionId)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 gdrive.PermissionsGetCall
	if rf, ok := ret.Get(0).(func(string, string) gdrive.PermissionsGetCall); ok {
		r0 = rf(fileId, permissionId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsGetCall)
		}
	}

	return r0
}

// MockPermissionsAPI_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockPermissionsAPI_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - fileId string
//   - permissionId string
func (_e *MockPermissionsAPI_Expecter) Get(fileId interface{}, permissionId interface{}) *MockPermissionsAPI_Get_Call {
	return &MockPermissionsAPI_Get_Call{Call: _e.mock.On("Get", fileId, permissionId)}
}

func (_c *MockPermissionsAPI_Get_Call) Run(run func(fileId string, permissionId string)) *MockPermissionsAPI_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockPermissionsAPI_Get_Call) Return(_a0 gdrive.PermissionsGetCall) *MockPermissionsAPI_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsAPI_Get_Call) RunAndReturn(run func(string, string) gdrive.PermissionsGetCall) *MockPermissionsAPI_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: fileId
func (_m *MockPermissionsAPI) List(fileId string) gdrive.PermissionsListCall {
	ret := _m.Called(fileId)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 gdrive.PermissionsListCall
	if rf, ok := ret.Get(0).(func(string) gdrive.PermissionsListCall); ok {
		r0 = rf(fileId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsListCall)
		}
	}

	return r0
}

// MockPermissionsAPI_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockPermissionsAPI_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - fileId string
func (_e *MockPermissionsAPI_Expecter) List(fileId interface{}) *MockPermissionsAPI_List_Call {
	return &MockPermissionsAPI_List_Call{Call: _e.mock.On("List", fileId)}
}

func (_c *MockPermissionsAPI_List_Call) Run(run func(fileId string)) *MockPermissionsAPI_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockPermissionsAPI_List_Call) Return(_a0 gdrive.PermissionsListCall) *MockPermissionsAPI_List_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsAPI_List_Call) RunAndReturn(run func(string) gdrive.PermissionsListCall) *MockPermissionsAPI_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: fileId, permissionId, permission
func (_m *MockPermissionsAPI) Update(fileId string, permissionId string, permission *drive.Permission) gdrive.PermissionsUpdateCall {
	ret := _m.Called(fileId, permissionId, permission)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 gdrive.PermissionsUpdateCall
	if rf, ok := ret.Get(0).(func(string, string, *drive.Permission) gdrive.PermissionsUpdateCall); ok {
		r0 = rf(fileId, permissionId, permission)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.PermissionsUpdateCall)
		}
	}

	return r0
}

// MockPermissionsAPI_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockPermissionsAPI_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - fileId string
//   - permissionId string
//   - permission *drive.Permission
func (_e *MockPermissionsAPI_Expecter) Update(fileId interface{}, permissionId interface{}, permission interface{}) *MockPermissionsAPI_Update_Call {
	return &MockPermissionsAPI_Update_Call{Call: _e.mock.On("Update", fileId, permissionId, permission)}
}

func (_c *MockPermissionsAPI_Update_Call) Run(run func(fileId string, permissionId string, permission *drive.Permission)) *MockPermissionsAPI_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(*drive.Permission))
	})
	return _c
}

func (_c *MockPermissionsAPI_Update_Call) Return(_a0 gdrive.PermissionsUpdateCall) *MockPermissionsAPI_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPermissionsAPI_Update_Call) RunAndReturn(run func(string, string, *drive.Permission) gdrive.PermissionsUpdateCall) *MockPermissionsAPI_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPermissionsAPI creates a new instance of MockPermissionsAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPermissionsAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPermissionsAPI {
	mock := &MockPermissionsAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
