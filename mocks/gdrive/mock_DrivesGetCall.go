// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	drive "google.golang.org/api/drive/v3"

	googleapi "google.golang.org/api/googleapi"

	mock "github.com/stretchr/testify/mock"
)

// MockDrivesGetCall is an autogenerated mock type for the DrivesGetCall type
type MockDrivesGetCall struct {
	mock.Mock
}

type MockDrivesGetCall_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDrivesGetCall) EXPECT() *MockDrivesGetCall_Expecter {
	return &MockDrivesGetCall_Expecter{mock: &_m.Mock}
}

// Do provides a mock function with given fields: opts
func (_m *MockDrivesGetCall) Do(opts ...googleapi.CallOption) (*drive.Drive, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *drive.Drive
	var r1 error
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) (*drive.Drive, error)); ok {
		return rf(opts...)
	}
	if rf, ok := ret.Get(0).(func(...googleapi.CallOption) *drive.Drive); ok {
		r0 = rf(opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drive.Drive)
		}
	}

	if rf, ok := ret.Get(1).(func(...googleapi.CallOption) error); ok {
		r1 = rf(opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDrivesGetCall_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MockDrivesGetCall_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - opts ...googleapi.CallOption
func (_e *MockDrivesGetCall_Expecter) Do(opts ...interface{}) *MockDrivesGetCall_Do_Call {
	return &MockDrivesGetCall_Do_Call{Call: _e.mock.On("Do",
		append([]interface{}{}, opts...)...)}
}

func (_c *MockDrivesGetCall_Do_Call) Run(run func(opts ...googleapi.CallOption)) *MockDrivesGetCall_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.CallOption, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.CallOption)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDrivesGetCall_Do_Call) Return(_a0 *drive.Drive, _a1 error) *MockDrivesGetCall_Do_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDrivesGetCall_Do_Call) RunAndReturn(run func(...googleapi.CallOption) (*drive.Drive, error)) *MockDrivesGetCall_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Fields provides a mock function with given fields: fields
func (_m *MockDrivesGetCall) Fields(fields ...googleapi.Field) gdrive.DrivesGetCall {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Fields")
	}

	var r0 gdrive.DrivesGetCall
	if rf, ok := ret.Get(0).(func(...googleapi.Field) gdrive.DrivesGetCall); ok {
		r0 = rf(fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.DrivesGetCall)
		}
	}

	return r0
}

// MockDrivesGetCall_Fields_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Fields'
type MockDrivesGetCall_Fields_Call struct {
	*mock.Call
}

// Fields is a helper method to define mock.On call
//   - fields ...googleapi.Field
func (_e *MockDrivesGetCall_Expecter) Fields(fields ...interface{}) *MockDrivesGetCall_Fields_Call {
	return &MockDrivesGetCall_Fields_Call{Call: _e.mock.On("Fields",
		append([]interface{}{}, fields...)...)}
}

func (_c *MockDrivesGetCall_Fields_Call) Run(run func(fields ...googleapi.Field)) *MockDrivesGetCall_Fields_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]googleapi.Field, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(googleapi.Field)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDrivesGetCall_Fields_Call) Return(_a0 gdrive.DrivesGetCall) *MockDrivesGetCall_Fields_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDrivesGetCall_Fields_Call) RunAndReturn(run func(...googleapi.Field) gdrive.DrivesGetCall) *MockDrivesGetCall_Fields_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDrivesGetCall creates a new instance of MockDrivesGetCall. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDrivesGetCall(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDrivesGetCall {
	mock := &MockDrivesGetCall{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
