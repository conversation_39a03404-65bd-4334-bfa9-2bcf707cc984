// Code generated by mockery. DO NOT EDIT.

package gdrive

import (
	gdrive "bilabl/docman/pkg/gdrive"

	mock "github.com/stretchr/testify/mock"
)

// MockDrivesAPI is an autogenerated mock type for the DrivesAPI type
type MockDrivesAPI struct {
	mock.Mock
}

type MockDrivesAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDrivesAPI) EXPECT() *MockDrivesAPI_Expecter {
	return &MockDrivesAPI_Expecter{mock: &_m.Mock}
}

// Get provides a mock function with given fields: driveId
func (_m *MockDrivesAPI) Get(driveId string) gdrive.DrivesGetCall {
	ret := _m.Called(driveId)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 gdrive.DrivesGetCall
	if rf, ok := ret.Get(0).(func(string) gdrive.DrivesGetCall); ok {
		r0 = rf(driveId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.DrivesGetCall)
		}
	}

	return r0
}

// MockDrivesAPI_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockDrivesAPI_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - driveId string
func (_e *MockDrivesAPI_Expecter) Get(driveId interface{}) *MockDrivesAPI_Get_Call {
	return &MockDrivesAPI_Get_Call{Call: _e.mock.On("Get", driveId)}
}

func (_c *MockDrivesAPI_Get_Call) Run(run func(driveId string)) *MockDrivesAPI_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDrivesAPI_Get_Call) Return(_a0 gdrive.DrivesGetCall) *MockDrivesAPI_Get_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDrivesAPI_Get_Call) RunAndReturn(run func(string) gdrive.DrivesGetCall) *MockDrivesAPI_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with no fields
func (_m *MockDrivesAPI) List() gdrive.DrivesListCall {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 gdrive.DrivesListCall
	if rf, ok := ret.Get(0).(func() gdrive.DrivesListCall); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(gdrive.DrivesListCall)
		}
	}

	return r0
}

// MockDrivesAPI_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockDrivesAPI_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
func (_e *MockDrivesAPI_Expecter) List() *MockDrivesAPI_List_Call {
	return &MockDrivesAPI_List_Call{Call: _e.mock.On("List")}
}

func (_c *MockDrivesAPI_List_Call) Run(run func()) *MockDrivesAPI_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDrivesAPI_List_Call) Return(_a0 gdrive.DrivesListCall) *MockDrivesAPI_List_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDrivesAPI_List_Call) RunAndReturn(run func() gdrive.DrivesListCall) *MockDrivesAPI_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDrivesAPI creates a new instance of MockDrivesAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDrivesAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDrivesAPI {
	mock := &MockDrivesAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
