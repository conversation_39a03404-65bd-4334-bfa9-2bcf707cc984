package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// DocumentAutomationRule represents automation rules for document generation
type DocumentAutomationRule struct {
	Model
	TenantID     uint64          `gorm:"index;not null" json:"tenant_id"`
	Name         string          `gorm:"not null" json:"name"`
	Description  string          `json:"description"`
	TriggerType  string          `gorm:"not null" json:"trigger_type"`    // "matter.create", "client.update"
	TriggerRules TriggerRulesMap `gorm:"type:jsonb" json:"trigger_rules"` // JSON config for trigger conditions
	RuleConfig   RuleConfigArray `gorm:"type:jsonb" json:"rule_config"`   // JSON array of rule actions
	IsActive     bool            `json:"is_active"`
	CreatedUser  uint64          `json:"created_user"`
	UpdatedUser  uint64          `json:"updated_user"`
}

// TriggerRulesMap represents trigger conditions as JSON
type TriggerRulesMap map[string]interface{}

// Value implements driver.Valuer interface for database storage
func (t TriggerRulesMap) Value() (driver.Value, error) {
	if t == nil {
		return nil, nil
	}
	return json.Marshal(t)
}

// <PERSON>an implements sql.Scanner interface for database retrieval
func (t *TriggerRulesMap) Scan(value interface{}) error {
	if value == nil {
		*t = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into TriggerRulesMap", value)
	}

	return json.Unmarshal(bytes, t)
}

// RuleAction represents a single action in the automation rule
type RuleAction struct {
	ActionType     string `json:"action_type"`               // "copy_file", "copy_folder", "generate_document"
	SourcePath     string `json:"source_path"`               // Path from AutoDocRoot
	TargetPath     string `json:"target_path"`               // Target path with placeholders
	Provider       string `json:"provider,omitempty"`        // Source provider: "internal", "gdrive", "sharepoint" - optional, defaults to "internal"
	TargetProvider string `json:"target_provider,omitempty"` // Target provider: "internal", "gdrive", "sharepoint" - optional, uses tenant default_dms if empty
	Override       bool   `json:"override"`                  // Whether to override existing files/folders with same name - defaults to false
}

// GetProvider returns the source provider for this action, defaulting to "internal" if not specified
func (r *RuleAction) GetProvider() string {
	if r.Provider == "" {
		return "internal"
	}
	return r.Provider
}

// SetProvider sets the source provider for this action
func (r *RuleAction) SetProvider(provider string) {
	r.Provider = provider
}

// GetTargetProvider returns the target provider for this action
// If empty, should use tenant default_dms setting
func (r *RuleAction) GetTargetProvider() string {
	return r.TargetProvider
}

// SetTargetProvider sets the target provider for this action
func (r *RuleAction) SetTargetProvider(provider string) {
	r.TargetProvider = provider
}

// IsInternalProvider returns true if this action uses the internal source provider
func (r *RuleAction) IsInternalProvider() bool {
	return r.GetProvider() == "internal"
}

// IsInternalTargetProvider returns true if this action uses the internal target provider
func (r *RuleAction) IsInternalTargetProvider() bool {
	return r.GetTargetProvider() == "internal"
}

// RuleConfigArray represents an array of rule actions
type RuleConfigArray []RuleAction

// Value implements driver.Valuer interface for database storage
func (r RuleConfigArray) Value() (driver.Value, error) {
	if r == nil {
		return nil, nil
	}
	return json.Marshal(r)
}

// Scan implements sql.Scanner interface for database retrieval
func (r *RuleConfigArray) Scan(value interface{}) error {
	if value == nil {
		*r = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into RuleConfigArray", value)
	}

	return json.Unmarshal(bytes, r)
}

// TableName returns the table name for GORM
func (DocumentAutomationRule) TableName() string {
	return "document_automation_rules"
}
