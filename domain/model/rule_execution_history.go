package model

import (
	"time"

	"gorm.io/datatypes"
)

// RuleExecutionHistory represents the execution history of automation rules
type RuleExecutionHistory struct {
	Model
	TenantID         uint64         `json:"tenant_id" gorm:"not null;index:idx_rule_execution_tenant"`
	RuleID           uint64         `json:"rule_id" gorm:"not null;index:idx_rule_execution_rule"`
	RuleName         string         `json:"rule_name" gorm:"not null;size:255"`
	TriggerType      string         `json:"trigger_type" gorm:"not null;size:50"`
	TriggerPayload   datatypes.JSON `json:"trigger_payload" gorm:"type:jsonb"`
	ExecutionStatus  string         `json:"execution_status" gorm:"not null;size:20;index:idx_rule_execution_status"` // success, failed, partial
	ExecutionResult  datatypes.JSON `json:"execution_result" gorm:"type:jsonb"`
	ActionsExecuted  int            `json:"actions_executed" gorm:"not null;default:0"`
	ActionsSucceeded int            `json:"actions_succeeded" gorm:"not null;default:0"`
	ActionsFailed    int            `json:"actions_failed" gorm:"not null;default:0"`
	ErrorMessage     string         `json:"error_message,omitempty" gorm:"type:text"`
	ExecutionTime    time.Duration  `json:"execution_time" gorm:"not null"` // in nanoseconds
	StartedAt        time.Time      `json:"started_at" gorm:"not null;index:idx_rule_execution_started"`
	CompletedAt      *time.Time     `json:"completed_at,omitempty" gorm:"index:idx_rule_execution_completed"`
	ExecutedBy       string         `json:"executed_by" gorm:"not null;size:50"` // system, manual, api
	UserID           *uint64        `json:"user_id,omitempty" gorm:"index:idx_rule_execution_user"`
}

// TableName returns the table name for RuleExecutionHistory
func (RuleExecutionHistory) TableName() string {
	return "rule_execution_histories"
}

// ExecutionStatus constants
const (
	ExecutionStatusSuccess = "success"
	ExecutionStatusFailed  = "failed"
	ExecutionStatusPartial = "partial"
	ExecutionStatusRunning = "running"
)

// ExecutedBy constants
const (
	ExecutedBySystem = "system"
	ExecutedByManual = "manual"
	ExecutedByAPI    = "api"
)

// RuleExecutionHistoryRepository defines the interface for rule execution history operations
type RuleExecutionHistoryRepository interface {
	// Create creates a new execution history record
	Create(history *RuleExecutionHistory) error

	// GetByID retrieves an execution history record by ID
	GetByID(id uint64) (*RuleExecutionHistory, error)

	// GetByRuleID retrieves execution history for a specific rule with pagination
	GetByRuleID(tenantID, ruleID uint64, limit, offset int) ([]*RuleExecutionHistory, int64, error)

	// GetByTenantID retrieves execution history for a tenant with pagination
	GetByTenantID(tenantID uint64, limit, offset int) ([]*RuleExecutionHistory, int64, error)

	// GetByStatus retrieves execution history by status with pagination
	GetByStatus(tenantID uint64, status string, limit, offset int) ([]*RuleExecutionHistory, int64, error)

	// Update updates an execution history record
	Update(history *RuleExecutionHistory) error

	// Delete deletes an execution history record
	Delete(id uint64) error

	// DeleteOldRecords deletes execution history records older than the specified duration
	DeleteOldRecords(olderThan time.Time) error
}
