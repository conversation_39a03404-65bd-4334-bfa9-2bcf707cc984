package model

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
)

// OperationCoordinationStatus tracks the completion status of multi-provider operations
type OperationCoordinationStatus struct {
	Model
	TenantID         uint64 `gorm:"index;not null" json:"tenant_id"`
	OperationType    string `gorm:"index;not null;size:50" json:"operation_type"`       // "matter_folder_creation", "client_folder_creation", "permission_sync"
	OperationID      string `gorm:"index;not null;size:255;unique" json:"operation_id"` // Unique operation identifier
	EntityType       string `gorm:"not null;size:50" json:"entity_type"`                // "matter", "client", "document"
	EntityID         uint64 `gorm:"index;not null" json:"entity_id"`
	ParentEntityType string `gorm:"size:50" json:"parent_entity_type"` // Optional: "client" for matter operations
	ParentEntityID   uint64 `gorm:"index" json:"parent_entity_id"`     // Optional: client_id for matter operations

	// Provider status tracking (flexible JSON structure)
	ProviderStatuses datatypes.JSON `gorm:"type:jsonb" json:"provider_statuses"` // {"internal": "completed", "gdrive": "pending"}

	// Configuration
	RequiredProviders datatypes.JSON `gorm:"type:jsonb" json:"required_providers"` // ["internal", "gdrive", "sharepoint"]
	OptionalProviders datatypes.JSON `gorm:"type:jsonb" json:"optional_providers"` // ["backup_provider"] - failures don't block

	// Overall coordination
	OverallStatus CompletionStatus `gorm:"not null;default:'pending';index" json:"overall_status"`
	CompletedAt   *time.Time       `json:"completed_at,omitempty"`

	// Downstream action tracking
	DownstreamTriggered   bool       `gorm:"not null;default:false" json:"downstream_triggered"`
	DownstreamTriggeredAt *time.Time `json:"downstream_triggered_at,omitempty"`
	DownstreamAction      string     `gorm:"size:100" json:"downstream_action"` // "autodoc_rules", "permission_sync"

	// Error handling
	ErrorDetails datatypes.JSON `gorm:"type:jsonb" json:"error_details,omitempty"`
	RetryCount   int            `gorm:"not null;default:0" json:"retry_count"`
	LastRetryAt  *time.Time     `json:"last_retry_at,omitempty"`
	TimeoutAt    *time.Time     `json:"timeout_at,omitempty"`

	// Operation metadata
	Metadata datatypes.JSON `gorm:"type:jsonb" json:"metadata,omitempty"` // Operation-specific data
}

// ProviderStatus represents the status of a provider operation
type ProviderStatus string

const (
	ProviderStatusPending       ProviderStatus = "pending"
	ProviderStatusCompleted     ProviderStatus = "completed"
	ProviderStatusFailed        ProviderStatus = "failed"
	ProviderStatusSkipped       ProviderStatus = "skipped"        // Provider disabled
	ProviderStatusNotApplicable ProviderStatus = "not_applicable" // Provider not configured
)

// CompletionStatus represents the overall completion status
type CompletionStatus string

const (
	CompletionStatusPending   CompletionStatus = "pending"
	CompletionStatusCompleted CompletionStatus = "completed"
	CompletionStatusFailed    CompletionStatus = "failed"
	CompletionStatusTimeout   CompletionStatus = "timeout"
)

// TableName returns the table name for OperationCoordinationStatus
func (OperationCoordinationStatus) TableName() string {
	return "operation_coordination_statuses"
}

// IsCompleted checks if the operation is completed successfully
func (o *OperationCoordinationStatus) IsCompleted() bool {
	return o.OverallStatus == CompletionStatusCompleted
}

// HasFailed checks if the operation has failed
func (o *OperationCoordinationStatus) HasFailed() bool {
	return o.OverallStatus == CompletionStatusFailed
}

// IsTimedOut checks if the operation has timed out
func (o *OperationCoordinationStatus) IsTimedOut() bool {
	return o.OverallStatus == CompletionStatusTimeout
}

// GetRequiredProviders returns the list of required providers
func (o *OperationCoordinationStatus) GetRequiredProviders() []string {
	var providers []string
	if o.RequiredProviders != nil {
		json.Unmarshal(o.RequiredProviders, &providers)
	}
	return providers
}

// SetRequiredProviders sets the list of required providers
func (o *OperationCoordinationStatus) SetRequiredProviders(providers []string) error {
	data, err := json.Marshal(providers)
	if err != nil {
		return err
	}
	o.RequiredProviders = data
	return nil
}

// GetOptionalProviders returns the list of optional providers
func (o *OperationCoordinationStatus) GetOptionalProviders() []string {
	var providers []string
	if o.OptionalProviders != nil {
		json.Unmarshal(o.OptionalProviders, &providers)
	}
	return providers
}

// SetOptionalProviders sets the list of optional providers
func (o *OperationCoordinationStatus) SetOptionalProviders(providers []string) error {
	data, err := json.Marshal(providers)
	if err != nil {
		return err
	}
	o.OptionalProviders = data
	return nil
}

// GetProviderStatuses returns the provider statuses map
func (o *OperationCoordinationStatus) GetProviderStatuses() map[string]ProviderStatus {
	var statuses map[string]ProviderStatus
	if o.ProviderStatuses != nil {
		json.Unmarshal(o.ProviderStatuses, &statuses)
	}
	if statuses == nil {
		statuses = make(map[string]ProviderStatus)
	}
	return statuses
}

// SetProviderStatuses sets the provider statuses map
func (o *OperationCoordinationStatus) SetProviderStatuses(statuses map[string]ProviderStatus) error {
	data, err := json.Marshal(statuses)
	if err != nil {
		return err
	}
	o.ProviderStatuses = data
	return nil
}

// UpdateProviderStatus updates the status of a specific provider and recalculates overall status
func (o *OperationCoordinationStatus) UpdateProviderStatus(provider string, status ProviderStatus) error {
	statuses := o.GetProviderStatuses()
	statuses[provider] = status

	if err := o.SetProviderStatuses(statuses); err != nil {
		return err
	}

	o.recalculateOverallStatus()
	return nil
}

// recalculateOverallStatus recalculates the overall completion status based on provider statuses
func (o *OperationCoordinationStatus) recalculateOverallStatus() {
	requiredProviders := o.GetRequiredProviders()
	providerStatuses := o.GetProviderStatuses()

	if len(requiredProviders) == 0 {
		o.OverallStatus = CompletionStatusCompleted
		if o.CompletedAt == nil {
			now := time.Now()
			o.CompletedAt = &now
		}
		return
	}

	allRequiredCompleted := true
	anyRequiredFailed := false

	// Check required providers
	for _, provider := range requiredProviders {
		status, exists := providerStatuses[provider]
		if !exists {
			status = ProviderStatusPending
		}

		if status == ProviderStatusFailed {
			anyRequiredFailed = true
		}

		if status != ProviderStatusCompleted && status != ProviderStatusSkipped {
			allRequiredCompleted = false
		}
	}

	// Optional providers don't affect completion, only required ones do
	if anyRequiredFailed {
		o.OverallStatus = CompletionStatusFailed
	} else if allRequiredCompleted {
		o.OverallStatus = CompletionStatusCompleted
		if o.CompletedAt == nil {
			now := time.Now()
			o.CompletedAt = &now
		}
	} else {
		o.OverallStatus = CompletionStatusPending
	}
}

// GetMetadata returns the operation metadata
func (o *OperationCoordinationStatus) GetMetadata() map[string]interface{} {
	var metadata map[string]interface{}
	if o.Metadata != nil {
		json.Unmarshal(o.Metadata, &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	return metadata
}

// SetMetadata sets the operation metadata
func (o *OperationCoordinationStatus) SetMetadata(metadata map[string]interface{}) error {
	data, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	o.Metadata = data
	return nil
}

// OperationCoordinationStatusRepository defines the interface for operation coordination status operations
type OperationCoordinationStatusRepository interface {
	// Create creates a new operation coordination status record
	Create(status *OperationCoordinationStatus) error

	// GetByOperationID retrieves status by operation ID
	GetByOperationID(operationID string) (*OperationCoordinationStatus, error)

	// GetByEntity retrieves status by entity and operation type
	GetByEntity(tenantID uint64, entityType string, entityID uint64, operationType string) (*OperationCoordinationStatus, error)

	// Update updates an operation coordination status record
	Update(status *OperationCoordinationStatus) error

	// UpdateProviderStatus updates the status of a specific provider
	UpdateProviderStatus(operationID, provider string, status ProviderStatus) error

	// GetPendingStatuses retrieves all pending statuses (for timeout monitoring)
	GetPendingStatuses(olderThan time.Time) ([]*OperationCoordinationStatus, error)

	// MarkAsTimedOut marks statuses as timed out
	MarkAsTimedOut(operationIDs []string) error

	// Delete deletes old completed records
	DeleteOldRecords(olderThan time.Time) error
}
