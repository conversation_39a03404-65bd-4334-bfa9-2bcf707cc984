package model

import (
	"code.mybil.net/gophers/gokit/domain/errors"
	"time"

	"gorm.io/gorm"
)

type Model struct {
	ID        uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time      `gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time      `gorm:"default:CURRENT_TIMESTAMP"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

type Filter struct {
	Key    string
	Value  interface{}
	Method string
}

func NewFilter(key string, value interface{}, method string) *Filter {
	return &Filter{
		Key:    key,
		Value:  value,
		Method: method,
	}
}

func NewFilterE(key string, value interface{}) *Filter {
	return NewFilter(key, value, "=")
}

type Sort struct {
	Key    string
	SortBy string
}

type Query struct {
	Q          string
	Filters    []*Filter
	Sort       []*Sort
	Pagination *Pagination
}

type Pagination struct {
	Page, Limit, Offset int
}

func (q *Query) SetQ(keyword string) {
	q.Q = keyword
}

func (q *Query) SetFilters(filters []*Filter) {
	q.Filters = filters
}

func (q *Query) SetSort(sort []*Sort) {
	q.Sort = sort
}

func (q *Query) SetPagination(pagination *Pagination) {
	q.Pagination = pagination
}

func IsNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}
