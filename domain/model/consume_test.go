package model

import (
	"reflect"
	"testing"
)

func TestFindMissingOwner(t *testing.T) {
	currentOwners := []OwnerUser{
		{Id: 1, Email: "a@b", Name: "A"},
	}
	oldOwners := []OwnerUser{
		{Id: 1, Email: "a@b", Name: "A"},
		{Id: 2, Email: "b@c", Name: "B"},
	}
	expected := []OwnerUser{
		{Id: 2, Email: "b@c", Name: "B"},
	}

	result := FindMissingOwner(currentOwners, oldOwners)

	if !reflect.DeepEqual(result, expected) {
		t.<PERSON><PERSON>rf("FindMissingOwner() = %v, want %v", result, expected)
	}
}
