package model

import (
	"encoding/json"
	"fmt"
	"time"
)

const (
	KeyExternalTenantID  = "external_tenant_id"
	KeySharepointSiteID  = "sharepoint_site_id"
	KeySharepointDriveID = "site_drive_id"
	KeyGdriveConfig      = "gdrive_config"      // JSON config for Google Drive integration
	KeyMappingFolderName = "matter_folder_name" // Currently, only use for SharePoint (gdrive use the config path template)
	KeyAccessToken       = "access_token"
	KeySharepointToken   = "sharepoint_token"
	KeySiteDriveID       = "site_drive_id"
)

// GDriveConfig represents the Google Drive configuration stored as JSON
type GDriveConfig struct {
	Enabled          bool              `json:"enabled"`
	RootID           string            `json:"root_id"`
	DriveID          string            `json:"drive_id,omitempty"`          // Optional: for shared drive operations
	ResourceType     string            `json:"resource_type"`               // shared_drive, folder, file
	PathConfig       *PathConfig       `json:"path_config,omitempty"`       // Optional: for custom folder paths
	PermissionConfig *PermissionConfig `json:"permission_config,omitempty"` // Optional: for permission settings
}

// PathConfig represents configurable folder path templates
type PathConfig struct {
	ClientFolderPath   string `json:"client_folder_path,omitempty"`   // e.g., "/clients/{short_name|name} - {code}"
	MatterFolderPath   string `json:"matter_folder_path,omitempty"`   // e.g., "{client_folder}/matters/{name} - {code}"
	CaseFormat         string `json:"case_format,omitempty"`          // original, lower, upper, title
	MaxLength          int    `json:"max_length,omitempty"`           // default: 255
	InvalidCharReplace string `json:"invalid_char_replace,omitempty"` // default: "_"
}

// PermissionConfig represents configurable permission settings
type PermissionConfig struct {
	DefaultRole         string `json:"default_role,omitempty"`         // writer, reader, commenter
	RetryCount          int    `json:"retry_count,omitempty"`          // default: 3
	RetryDelayMs        int    `json:"retry_delay_ms,omitempty"`       // default: 1000
	SyncOnCreate        bool   `json:"sync_on_create,omitempty"`       // default: true
	SyncOnUpdate        bool   `json:"sync_on_update,omitempty"`       // default: true
	TimeoutSeconds      int    `json:"timeout_seconds,omitempty"`      // default: 30
	EnableBatchSync     bool   `json:"enable_batch_sync,omitempty"`    // default: false
	BatchSize           int    `json:"batch_size,omitempty"`           // default: 10
	FailureNotification bool   `json:"failure_notification,omitempty"` // default: true
}

// ToJSON converts GDriveConfig to JSON string
func (g *GDriveConfig) ToJSON() (string, error) {
	data, err := json.Marshal(g)
	return string(data), err
}

// FromJSON parses JSON string to GDriveConfig
func (g *GDriveConfig) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), g)
}

// IsEmpty returns true if the config is not initialized
func (g *GDriveConfig) IsEmpty() bool {
	return g.RootID == "" && g.ResourceType == ""
}

// RequiresDriveID returns true if operations require driveId parameter
func (g *GDriveConfig) RequiresDriveID() bool {
	return g.DriveID != ""
}

type DocumentSetting struct {
	Model
	TenantID  uint64    `json:"tenant_id" gorm:"uniqueIndex:idx_key"`
	Key       string    `json:"key" gorm:"uniqueIndex:idx_key"`
	Value     string    `json:"value"`
	ExpiredAt time.Time `gorm:"index"`
}

func (DocumentSetting) TableName() string {
	return "document_setting"
}

// GetGDriveConfig parses the Value field as GDriveConfig (only if Key is KeyGdriveConfig)
func (d *DocumentSetting) GetGDriveConfig() (*GDriveConfig, error) {
	if d.Key != KeyGdriveConfig {
		return nil, nil // Not a gdrive config setting
	}

	config := &GDriveConfig{}
	err := config.FromJSON(d.Value)
	if err != nil {
		return nil, err
	}
	return config, nil
}

// SharePointConfig represents SharePoint integration configuration
type SharePointConfig struct {
	Enabled        bool   `json:"enabled"`
	ClientID       string `json:"client_id,omitempty"`
	ClientSecret   string `json:"client_secret,omitempty"`
	TenantID       string `json:"tenant_id,omitempty"`
	SiteID         string `json:"site_id,omitempty"`
	DriveID        string `json:"drive_id,omitempty"`
	MaxRetries     int    `json:"max_retries,omitempty"`
	RetryDelayMs   int    `json:"retry_delay_ms,omitempty"`
	TimeoutSeconds int    `json:"timeout_seconds,omitempty"`
}

// ToJSON converts SharePointConfig to JSON string
func (s *SharePointConfig) ToJSON() (string, error) {
	data, err := json.Marshal(s)
	return string(data), err
}

// FromJSON parses JSON string to SharePointConfig
func (s *SharePointConfig) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), s)
}

// IsEmpty returns true if the config is not initialized
func (s *SharePointConfig) IsEmpty() bool {
	return s.ClientID == "" && s.TenantID == ""
}

// ValidatePermissionConfig validates permission configuration settings
func (p *PermissionConfig) ValidatePermissionConfig() error {
	if p == nil {
		return nil
	}

	// Validate default role
	validRoles := []string{"reader", "commenter", "writer", "fileOrganizer", "organizer", "owner"}
	if p.DefaultRole != "" {
		valid := false
		for _, role := range validRoles {
			if p.DefaultRole == role {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid default role: %s. Valid roles are: %v", p.DefaultRole, validRoles)
		}
	}

	// Validate retry count
	if p.RetryCount < 0 || p.RetryCount > 10 {
		return fmt.Errorf("retry count must be between 0 and 10, got: %d", p.RetryCount)
	}

	// Validate retry delay
	if p.RetryDelayMs < 0 || p.RetryDelayMs > 60000 {
		return fmt.Errorf("retry delay must be between 0 and 60000ms, got: %d", p.RetryDelayMs)
	}

	// Validate timeout
	if p.TimeoutSeconds < 0 || p.TimeoutSeconds > 300 {
		return fmt.Errorf("timeout must be between 0 and 300 seconds, got: %d", p.TimeoutSeconds)
	}

	// Validate batch size (only if set)
	if p.BatchSize != 0 && (p.BatchSize < 1 || p.BatchSize > 100) {
		return fmt.Errorf("batch size must be between 1 and 100, got: %d", p.BatchSize)
	}

	return nil
}

// SetGDriveConfig sets the Value field from GDriveConfig (and sets Key to KeyGdriveConfig)
func (d *DocumentSetting) SetGDriveConfig(config *GDriveConfig) error {
	d.Key = KeyGdriveConfig
	jsonStr, err := config.ToJSON()
	if err != nil {
		return err
	}
	d.Value = jsonStr
	return nil
}
