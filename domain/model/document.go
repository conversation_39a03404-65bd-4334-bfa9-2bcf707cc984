package model

import (
	"bilabl/docman/pkg/constant"

	"github.com/lib/pq"
)

const (
	DocTypeDir  = 1
	DocTypeFile = 2

	// AutoDoc constants
	AutoDocRootFolderName = "AutoDocRoot"
)

type Document struct {
	Model
	ParentID    uint64        `json:"parent_id" gorm:"default:0"`
	Name        string        `json:"name" gorm:"name"`
	TenantID    uint64        `json:"tenant_id"`
	ObjectType  int           `json:"object_type" gorm:"default:1"`
	ObjectID    uint64        `json:"object_id"`
	Key         string        `json:"key"`
	SubObjectID uint64        `json:"sub_object_id"`
	Type        int           `json:"type"`
	DocType     int           `json:"doc_type" gorm:"default:2"`
	Status      int           `json:"status" gorm:"default:1"`
	WaiverWith  pq.Int64Array `json:"waiver_with" gorm:"type:integer[]"`
	Note        string        `json:"note"`
	CreatedUser uint64        `json:"created_user"`
	UpdatedUser uint64        `json:"updated_user"`
	Size        int64         `json:"size" gorm:"default:0"`
}

func (d *Document) GetSortableFields() []string {
	return []string{
		"id",
		"name",
		"type",
		"doc_type",
		"created_at",
		"created_user",
		"updated_user",
		"updated_at",
		"size",
	}
}

func (Document) TableName() string {
	return "documents"
}

type ConsumeDocumentData struct {
	ObjectType  int    `json:"object_type"`
	ObjectID    uint64 `json:"object_id"`
	SubObjectID uint64 `json:"sub_object_id"`
	Name        string `json:"name"`
	DocType     int    `json:"doc_type"`
	CreatedUser uint64 `json:"created_user"`
	TenantID    uint64 `json:"tenant_id"`
}

type ConsumeDocumentBody struct {
	Action int                 `json:"action"`
	Data   ConsumeDocumentData `json:"data"`
}

type ConsumeDocumentReq struct {
	Topic string              `json:"topic"`
	Body  ConsumeDocumentBody `json:"body"`
}

type CreateDocumentReq struct {
	ParentID    uint64                  `json:"parent_id"`
	Name        string                  `json:"name" validate:"required"`
	TenantID    uint64                  `json:"tenant_id"`
	ObjectType  int                     `json:"object_type"`
	ObjectID    uint64                  `json:"object_id"`
	Key         string                  `json:"key" `
	SubObjectID uint64                  `json:"sub_object_id"`
	Type        int                     `json:"type"`
	DocType     int                     `json:"doc_type"`
	Status      int                     `json:"status"`
	WaiverWith  []int64                 `json:"waiver_with"`
	Action      constant.ActivityAction `json:"action"`
	Note        string                  `json:"note"`
	Topic       string                  `json:"topic"`
	Size        int64                   `json:"size"`

	// ParentPath stores the parent path of the document
	// service will try to evaluate the parent_id from the parent_path
	// When using this, object_type & object_id will be required
	// e.g., Billing/Correspondence
	ParentPath      string `json:"parent_path"`
	DisableActivity bool   `json:"disable_activity"`
	// AutoDocRoot indicates if this document should be created in AutoDocRoot
	AutoDocRoot bool `json:"autodoc_root"`
}

type UpdateDocumentReq struct {
	ParentID   uint64 `json:"parent_id"`
	Name       string `json:"name"`
	ObjectType int    `json:"object_type"`
	Key        string `json:"key" `
	Type       int    `json:"type"`
	DocType    int    `json:"doc_type"`
	Status     int    `json:"status"`
	Action     int    `json:"action"`
	Note       string `json:"note"`
	TenantID   uint64 `json:"tenant_id"`
	Size       int64  `json:"size"`
}

type UpdateDocumentV3Req struct {
	Name string `json:"name"`
	Key  string `json:"key"`
}

type DownloadDocumentReq struct {
	Key string `json:"key" query:"key"`
}
type GetDocumentReq struct {
	ID uint64 `json:"id" uri:"id" form:"id"`
}

type ConsumeBillingCostReq struct {
	Topic string                 `json:"topic"`
	Body  ConsumeBillingCostBody `json:"body"`
}

type ConsumeBillingCostBody struct {
	UserID    uint64 `json:"user_id"`
	TenantID  uint64 `json:"tenant_id"`
	CostID    uint64 `json:"cost_id"`
	BillingID uint64 `json:"billing_id"`
	Name      string `json:"name"`
	Key       string `json:"key"`
}

type ConsumeBillingPaymentReq struct {
	Topic string                    `json:"topic"`
	Body  ConsumeBillingPaymentBody `json:"body"`
}

type ConsumeBillingPaymentBody struct {
	UserID    uint64 `json:"user_id"`
	TenantID  uint64 `json:"tenant_id"`
	PaymentID uint64 `json:"payment_id"`
	BillingID uint64 `json:"billing_id"`
	Name      string `json:"name"`
	Key       string `json:"key"`
}
