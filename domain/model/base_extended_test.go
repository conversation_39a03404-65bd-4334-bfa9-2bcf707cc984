package model

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestIsNotFound_Extended(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error should return false",
			err:      nil,
			expected: false,
		},
		{
			name:     "direct gorm.ErrRecordNotFound should return true",
			err:      gorm.ErrRecordNotFound,
			expected: true,
		},
		{
			name:     "wrapped gorm.ErrRecordNotFound should return true",
			err:      fmt.<PERSON><PERSON><PERSON>("database error: %w", gorm.ErrRecordNotFound),
			expected: true,
		},
		{
			name:     "double wrapped gorm.ErrRecordNotFound should return true",
			err:      fmt.<PERSON><PERSON><PERSON>("query failed: %w", fmt.<PERSON><PERSON><PERSON>("database error: %w", gorm.ErrRecordNotFound)),
			expected: true,
		},
		{
			name:     "other error should return false",
			err:      fmt.<PERSON>("some other error"),
			expected: false,
		},
		{
			name:     "wrapped other error should return false",
			err:      fmt.<PERSON><PERSON><PERSON>("database error: %w", fmt.<PERSON><PERSON><PERSON>("connection failed")),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsNotFound(tt.err)
			assert.Equal(t, tt.expected, result, "IsNotFound(%v) = %v, want %v", tt.err, result, tt.expected)
		})
	}
}
