package model

import (
	"time"
)

// UploadSessionStatus represents the status of an upload session
type UploadSessionStatus string

const (
	UploadSessionStatusPending   UploadSessionStatus = "pending"   // Session created, waiting for file upload
	UploadSessionStatusUploading UploadSessionStatus = "uploading" // File upload in progress
	UploadSessionStatusCompleted UploadSessionStatus = "completed" // Upload successful
	UploadSessionStatusFailed    UploadSessionStatus = "failed"    // Upload failed
	UploadSessionStatusExpired   UploadSessionStatus = "expired"   // Session expired
)

// UploadSession represents an upload session for 2-step file upload process
type UploadSession struct {
	Model
	SessionToken    string              `json:"session_token" gorm:"uniqueIndex;not null;size:255"`
	TenantID        uint64              `json:"tenant_id" gorm:"index;not null"`
	FileName        string              `json:"file_name" gorm:"not null;size:500"`
	ParentDriveID   string              `json:"parent_drive_id" gorm:"not null;size:255"`
	GoogleUploadURL string              `json:"google_upload_url" gorm:"type:text;not null"`
	GoogleFileID    string              `json:"google_file_id" gorm:"size:255"`
	Status          UploadSessionStatus `json:"status" gorm:"default:'pending';index;size:50"`
	FileSize        int64               `json:"file_size" gorm:"default:0"`
	ContentType     string              `json:"content_type" gorm:"size:255"`
	ExpiresAt       time.Time           `json:"expires_at" gorm:"index;not null"`
}

// TableName returns the table name for UploadSession
func (UploadSession) TableName() string {
	return "upload_sessions"
}

// IsExpired checks if the upload session has expired
func (u *UploadSession) IsExpired() bool {
	return time.Now().After(u.ExpiresAt)
}

// IsActive checks if the upload session is in an active state (pending or uploading)
func (u *UploadSession) IsActive() bool {
	return u.Status == UploadSessionStatusPending || u.Status == UploadSessionStatusUploading
}

// CanUpload checks if the session can accept file upload
func (u *UploadSession) CanUpload() bool {
	return u.Status == UploadSessionStatusPending && !u.IsExpired()
}

// IsCompleted checks if the upload session is completed successfully
func (u *UploadSession) IsCompleted() bool {
	return u.Status == UploadSessionStatusCompleted
}

// IsFailed checks if the upload session has failed
func (u *UploadSession) IsFailed() bool {
	return u.Status == UploadSessionStatusFailed
}
