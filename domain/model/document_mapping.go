package model

const (
	DocProviderSharepoint = "sharepoint"
	DocProviderGoogle     = "google"
	DocTypeClient         = "client"
	DocTypeParent         = "parent"
	DocTypeMatter         = "matter"
	DocTypeAutoDocRoot    = "autodoc_root"
)

type DocumentMapping struct {
	Model
	TenantID       uint64 `json:"tenant_id"`
	Type           string `json:"type" gorm:"index"`
	ParentObjectID uint64 `json:"parent_object_id"`
	ObjectID       uint64 `json:"object_id" gorm:"uniqueIndex:idx_object_drive_provider"`
	ParentDriveID  string `json:"parent_drive_id"`
	DriveID        string `json:"drive_id" gorm:"uniqueIndex:idx_object_drive_provider"`
	Provider       string `json:"provider" gorm:"uniqueIndex:idx_object_drive_provider"`
}

func (DocumentMapping) TableName() string {
	return "document_mapping"
}
