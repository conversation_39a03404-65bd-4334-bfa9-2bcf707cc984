package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGDriveConfig_ToJSON(t *testing.T) {
	testCases := []struct {
		name     string
		config   *GDriveConfig
		expected string
	}{
		{
			name: "Full config with all fields",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "1aBcDeFgHiJkLmN",
				DriveID:      "0aBcDeFgHiJkLmN",
				ResourceType: "shared_drive",
			},
			expected: `{"enabled":true,"root_id":"1aBcDeFgHiJkLmN","drive_id":"0aBcDeFgHiJkLmN","resource_type":"shared_drive"}`,
		},
		{
			name: "Config without drive_id (My Drive folder)",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "1aBcDeFgHiJkLmN",
				ResourceType: "folder",
			},
			expected: `{"enabled":true,"root_id":"1aBcDeFgHiJkLmN","resource_type":"folder"}`,
		},
		{
			name: "Disabled config",
			config: &GDriveConfig{
				Enabled:      false,
				RootID:       "1aBcDeFgHiJkLmN",
				ResourceType: "folder",
			},
			expected: `{"enabled":false,"root_id":"1aBcDeFgHiJkLmN","resource_type":"folder"}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := tc.config.ToJSON()
			require.NoError(t, err)
			assert.JSONEq(t, tc.expected, result)
		})
	}
}

func TestGDriveConfig_FromJSON(t *testing.T) {
	testCases := []struct {
		name        string
		jsonStr     string
		expected    *GDriveConfig
		expectError bool
	}{
		{
			name:    "Valid JSON with all fields",
			jsonStr: `{"enabled":true,"root_id":"1aBcDeFgHiJkLmN","drive_id":"0aBcDeFgHiJkLmN","resource_type":"shared_drive"}`,
			expected: &GDriveConfig{
				Enabled:      true,
				RootID:       "1aBcDeFgHiJkLmN",
				DriveID:      "0aBcDeFgHiJkLmN",
				ResourceType: "shared_drive",
			},
			expectError: false,
		},
		{
			name:    "Valid JSON without drive_id",
			jsonStr: `{"enabled":true,"root_id":"1aBcDeFgHiJkLmN","resource_type":"folder"}`,
			expected: &GDriveConfig{
				Enabled:      true,
				RootID:       "1aBcDeFgHiJkLmN",
				DriveID:      "", // Should be empty
				ResourceType: "folder",
			},
			expectError: false,
		},
		{
			name:        "Invalid JSON",
			jsonStr:     `{"enabled":true,"root_id":`,
			expected:    nil,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &GDriveConfig{}
			err := config.FromJSON(tc.jsonStr)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expected, config)
			}
		})
	}
}

func TestGDriveConfig_IsEmpty(t *testing.T) {
	testCases := []struct {
		name     string
		config   *GDriveConfig
		expected bool
	}{
		{
			name: "Empty config",
			config: &GDriveConfig{
				Enabled: true,
			},
			expected: true,
		},
		{
			name: "Config with only RootID",
			config: &GDriveConfig{
				Enabled: true,
				RootID:  "123",
			},
			expected: false,
		},
		{
			name: "Config with only ResourceType",
			config: &GDriveConfig{
				Enabled:      true,
				ResourceType: "folder",
			},
			expected: false,
		},
		{
			name: "Full config",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "123",
				ResourceType: "folder",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.config.IsEmpty()
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestGDriveConfig_RequiresDriveID(t *testing.T) {
	testCases := []struct {
		name     string
		config   *GDriveConfig
		expected bool
	}{
		{
			name: "Config with DriveID",
			config: &GDriveConfig{
				RootID:  "123",
				DriveID: "456",
			},
			expected: true,
		},
		{
			name: "Config without DriveID",
			config: &GDriveConfig{
				RootID: "123",
			},
			expected: false,
		},
		{
			name: "Config with empty DriveID",
			config: &GDriveConfig{
				RootID:  "123",
				DriveID: "",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.config.RequiresDriveID()
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestDocumentSetting_GetGDriveConfig(t *testing.T) {
	testCases := []struct {
		name        string
		setting     *DocumentSetting
		expected    *GDriveConfig
		expectError bool
	}{
		{
			name: "Valid GDrive config setting",
			setting: &DocumentSetting{
				Key:   KeyGdriveConfig,
				Value: `{"enabled":true,"root_id":"123","resource_type":"folder"}`,
			},
			expected: &GDriveConfig{
				Enabled:      true,
				RootID:       "123",
				ResourceType: "folder",
			},
			expectError: false,
		},
		{
			name: "Non-GDrive config setting",
			setting: &DocumentSetting{
				Key:   "other_key",
				Value: "some_value",
			},
			expected:    nil,
			expectError: false,
		},
		{
			name: "Invalid JSON in GDrive config",
			setting: &DocumentSetting{
				Key:   KeyGdriveConfig,
				Value: `{"enabled":true,"root_id":`,
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := tc.setting.GetGDriveConfig()

			if tc.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expected, result)
			}
		})
	}
}

func TestDocumentSetting_SetGDriveConfig(t *testing.T) {
	config := &GDriveConfig{
		Enabled:      true,
		RootID:       "123",
		DriveID:      "456",
		ResourceType: "shared_drive",
	}

	setting := &DocumentSetting{
		TenantID: 1,
	}

	err := setting.SetGDriveConfig(config)
	require.NoError(t, err)

	assert.Equal(t, KeyGdriveConfig, setting.Key)
	assert.Equal(t, uint64(1), setting.TenantID)

	// Verify the JSON is valid and contains expected data
	parsedConfig := &GDriveConfig{}
	err = parsedConfig.FromJSON(setting.Value)
	require.NoError(t, err)
	assert.Equal(t, config, parsedConfig)
}

func TestPermissionConfig_ValidatePermissionConfig(t *testing.T) {
	testCases := []struct {
		name        string
		config      *PermissionConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Nil config should pass",
			config:      nil,
			expectError: false,
		},
		{
			name: "Valid config with all fields",
			config: &PermissionConfig{
				DefaultRole:         "writer",
				RetryCount:          3,
				RetryDelayMs:        1000,
				SyncOnCreate:        true,
				SyncOnUpdate:        true,
				TimeoutSeconds:      30,
				EnableBatchSync:     false,
				BatchSize:           10,
				FailureNotification: true,
			},
			expectError: false,
		},
		{
			name: "Valid config with all valid roles",
			config: &PermissionConfig{
				DefaultRole: "reader",
			},
			expectError: false,
		},
		{
			name: "Valid config with commenter role",
			config: &PermissionConfig{
				DefaultRole: "commenter",
			},
			expectError: false,
		},
		{
			name: "Valid config with fileOrganizer role",
			config: &PermissionConfig{
				DefaultRole: "fileOrganizer",
			},
			expectError: false,
		},
		{
			name: "Valid config with organizer role",
			config: &PermissionConfig{
				DefaultRole: "organizer",
			},
			expectError: false,
		},
		{
			name: "Valid config with owner role",
			config: &PermissionConfig{
				DefaultRole: "owner",
			},
			expectError: false,
		},
		{
			name: "Invalid default role",
			config: &PermissionConfig{
				DefaultRole: "invalid_role",
			},
			expectError: true,
			errorMsg:    "invalid default role: invalid_role",
		},
		{
			name: "Invalid retry count - negative",
			config: &PermissionConfig{
				RetryCount: -1,
			},
			expectError: true,
			errorMsg:    "retry count must be between 0 and 10, got: -1",
		},
		{
			name: "Invalid retry count - too high",
			config: &PermissionConfig{
				RetryCount: 11,
			},
			expectError: true,
			errorMsg:    "retry count must be between 0 and 10, got: 11",
		},
		{
			name: "Invalid retry delay - negative",
			config: &PermissionConfig{
				RetryDelayMs: -1,
			},
			expectError: true,
			errorMsg:    "retry delay must be between 0 and 60000ms, got: -1",
		},
		{
			name: "Invalid retry delay - too high",
			config: &PermissionConfig{
				RetryDelayMs: 60001,
			},
			expectError: true,
			errorMsg:    "retry delay must be between 0 and 60000ms, got: 60001",
		},
		{
			name: "Invalid timeout - negative",
			config: &PermissionConfig{
				TimeoutSeconds: -1,
			},
			expectError: true,
			errorMsg:    "timeout must be between 0 and 300 seconds, got: -1",
		},
		{
			name: "Invalid timeout - too high",
			config: &PermissionConfig{
				TimeoutSeconds: 301,
			},
			expectError: true,
			errorMsg:    "timeout must be between 0 and 300 seconds, got: 301",
		},
		{
			name: "Valid batch size - zero (default)",
			config: &PermissionConfig{
				BatchSize: 0, // Zero means use default, should not be validated
			},
			expectError: false,
		},
		{
			name: "Invalid batch size - too high",
			config: &PermissionConfig{
				BatchSize: 101,
			},
			expectError: true,
			errorMsg:    "batch size must be between 1 and 100, got: 101",
		},
		{
			name: "Valid edge values",
			config: &PermissionConfig{
				DefaultRole:    "reader",
				RetryCount:     0,
				RetryDelayMs:   0,
				TimeoutSeconds: 0,
				BatchSize:      1,
			},
			expectError: false,
		},
		{
			name: "Valid max values",
			config: &PermissionConfig{
				DefaultRole:    "owner",
				RetryCount:     10,
				RetryDelayMs:   60000,
				TimeoutSeconds: 300,
				BatchSize:      100,
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.config.ValidatePermissionConfig()

			if tc.expectError {
				assert.Error(t, err)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGDriveConfig_WithPathConfig(t *testing.T) {
	testCases := []struct {
		name     string
		config   *GDriveConfig
		expected string
	}{
		{
			name: "Config with PathConfig",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "root123",
				ResourceType: "folder",
				PathConfig: &PathConfig{
					ClientFolderPath:   "/clients/{short_name|name} - {code}",
					MatterFolderPath:   "{client_folder}/matters/{name} - {code}",
					CaseFormat:         "title",
					MaxLength:          255,
					InvalidCharReplace: "_",
				},
			},
			expected: `{"enabled":true,"root_id":"root123","resource_type":"folder","path_config":{"client_folder_path":"/clients/{short_name|name} - {code}","matter_folder_path":"{client_folder}/matters/{name} - {code}","case_format":"title","max_length":255,"invalid_char_replace":"_"}}`,
		},
		{
			name: "Config with PermissionConfig",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "root123",
				ResourceType: "folder",
				PermissionConfig: &PermissionConfig{
					DefaultRole:         "writer",
					RetryCount:          3,
					RetryDelayMs:        1000,
					SyncOnCreate:        true,
					SyncOnUpdate:        true,
					TimeoutSeconds:      30,
					EnableBatchSync:     false,
					BatchSize:           10,
					FailureNotification: true,
				},
			},
			expected: `{"enabled":true,"root_id":"root123","resource_type":"folder","permission_config":{"default_role":"writer","retry_count":3,"retry_delay_ms":1000,"sync_on_create":true,"sync_on_update":true,"timeout_seconds":30,"batch_size":10,"failure_notification":true}}`,
		},
		{
			name: "Config with both PathConfig and PermissionConfig",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "root123",
				ResourceType: "folder",
				PathConfig: &PathConfig{
					ClientFolderPath: "/clients/{name}",
					MatterFolderPath: "{client_folder}/{name}",
				},
				PermissionConfig: &PermissionConfig{
					DefaultRole: "reader",
					RetryCount:  2,
				},
			},
			expected: `{"enabled":true,"root_id":"root123","resource_type":"folder","path_config":{"client_folder_path":"/clients/{name}","matter_folder_path":"{client_folder}/{name}"},"permission_config":{"default_role":"reader","retry_count":2}}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := tc.config.ToJSON()
			require.NoError(t, err)
			assert.JSONEq(t, tc.expected, result)

			// Test round-trip conversion
			parsedConfig := &GDriveConfig{}
			err = parsedConfig.FromJSON(result)
			require.NoError(t, err)
			assert.Equal(t, tc.config, parsedConfig)
		})
	}
}

func TestDocumentSetting_TableName(t *testing.T) {
	setting := DocumentSetting{}
	assert.Equal(t, "document_setting", setting.TableName())
}

func TestGDriveConfig_EdgeCases(t *testing.T) {
	testCases := []struct {
		name     string
		config   *GDriveConfig
		testFunc func(*testing.T, *GDriveConfig)
	}{
		{
			name: "Empty config should be empty",
			config: &GDriveConfig{
				Enabled: false,
			},
			testFunc: func(t *testing.T, config *GDriveConfig) {
				assert.True(t, config.IsEmpty())
				assert.False(t, config.RequiresDriveID())
			},
		},
		{
			name: "Config with only enabled flag should be empty",
			config: &GDriveConfig{
				Enabled: true,
			},
			testFunc: func(t *testing.T, config *GDriveConfig) {
				assert.True(t, config.IsEmpty())
				assert.False(t, config.RequiresDriveID())
			},
		},
		{
			name: "Config with RootID and ResourceType should not be empty",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "root123",
				ResourceType: "folder",
			},
			testFunc: func(t *testing.T, config *GDriveConfig) {
				assert.False(t, config.IsEmpty())
				assert.False(t, config.RequiresDriveID())
			},
		},
		{
			name: "Config with DriveID should require drive ID",
			config: &GDriveConfig{
				Enabled:      true,
				RootID:       "root123",
				DriveID:      "drive123",
				ResourceType: "shared_drive",
			},
			testFunc: func(t *testing.T, config *GDriveConfig) {
				assert.False(t, config.IsEmpty())
				assert.True(t, config.RequiresDriveID())
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.testFunc(t, tc.config)
		})
	}
}
