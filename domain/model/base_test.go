package model

import (
	"errors"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func TestNewFilter(t *testing.T) {
	key := "status"
	value := "active"
	method := "equals"

	filter := NewFilter(key, value, method)

	assert.Equal(t, key, filter.Key)
	assert.Equal(t, value, filter.Value)
	assert.Equal(t, method, filter.Method)
}

func TestNewFilterE(t *testing.T) {
	key := "status"
	value := "active"

	filter := NewFilterE(key, value)

	assert.Equal(t, key, filter.Key)
	assert.Equal(t, value, filter.Value)
	assert.Equal(t, "=", filter.Method)
}

func TestIsNotFound(t *testing.T) {
	err := gorm.ErrRecordNotFound
	assert.True(t, IsNotFound(err))

	err = errors.New("some other error")
	assert.False(t, IsNotFound(err))
}
