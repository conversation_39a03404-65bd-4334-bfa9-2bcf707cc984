package model

type DocumentPermissionMapping struct {
	ID       uint64 `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID uint64 `json:"tenant_id" gorm:"index"`
	Email    string `json:"email" gorm:"uniqueIndex:idx_email_drive_perm_provider"`
	DriveID  string `json:"drive_id" gorm:"uniqueIndex:idx_email_drive_perm_provider"`
	PermID   string `json:"perm_id" gorm:"uniqueIndex:idx_email_drive_perm_provider"`
	Provider string `json:"provider" gorm:"uniqueIndex:idx_email_drive_perm_provider;index;default:'sharepoint'"` // Provider: "google" or "sharepoint", default "sharepoint" for backward compatibility
	ClientID uint64 `json:"client_id" gorm:"index"`                                                               // Parent client ID for matter permissions (0 for client permissions)
}

func (DocumentPermissionMapping) TableName() string {
	return "document_permission_mapping"
}
