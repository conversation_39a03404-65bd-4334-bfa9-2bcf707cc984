package model

type DocumentConfig struct {
	Model
	TenantID    uint64 `json:"tenant_id"`
	Name        string `json:"name" gorm:"name"`
	Code        string `json:"code" gorm:"code"`
	ConfigType  string `json:"config_type" gorm:"config_type"`
	OrderConfig int    `json:"order" gorm:"order_config" column:"order_config"`
	CreatedUser uint64 `json:"created_user"`
}

func (DocumentConfig) TableName() string {
	return "document_configs"
}
