package model

import (
	"database/sql/driver"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTriggerRulesMap_Value(t *testing.T) {
	tests := []struct {
		name     string
		input    TriggerRulesMap
		expected driver.Value
		hasError bool
	}{
		{
			name:     "nil map",
			input:    nil,
			expected: nil,
			hasError: false,
		},
		{
			name: "valid map",
			input: TriggerRulesMap{
				"matter_category": "Business Registration",
				"client_stage":    "lead",
			},
			expected: []byte(`{"client_stage":"lead","matter_category":"Business Registration"}`),
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.input.Value()

			if tt.hasError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				// For JSON comparison, we need to unmarshal and compare
				var expectedMap, resultMap map[string]interface{}
				json.Unmarshal(tt.expected.([]byte), &expectedMap)
				json.Unmarshal(result.([]byte), &resultMap)
				assert.Equal(t, expectedMap, resultMap)
			}
		})
	}
}

func TestTriggerRulesMap_Scan(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected TriggerRulesMap
		hasError bool
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
			hasError: false,
		},
		{
			name:  "valid JSON bytes",
			input: []byte(`{"matter_category":"Business Registration","client_stage":"lead"}`),
			expected: TriggerRulesMap{
				"matter_category": "Business Registration",
				"client_stage":    "lead",
			},
			hasError: false,
		},
		{
			name:     "invalid input type",
			input:    "not bytes",
			expected: nil,
			hasError: true,
		},
		{
			name:     "invalid JSON",
			input:    []byte(`{"invalid": json}`),
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result TriggerRulesMap
			err := result.Scan(tt.input)

			if tt.hasError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRuleConfigArray_Value(t *testing.T) {
	tests := []struct {
		name     string
		input    RuleConfigArray
		expected string // JSON string for easier comparison
		hasError bool
	}{
		{
			name:     "nil array",
			input:    nil,
			expected: "",
			hasError: false,
		},
		{
			name: "valid array",
			input: RuleConfigArray{
				{
					ActionType: "copy_file",
					SourcePath: "templates/form.docx",
					TargetPath: "Forms/{client_name}_form.docx",
				},
				{
					ActionType: "copy_folder",
					SourcePath: "templates/business_registration",
					TargetPath: "Business Registration",
				},
			},
			expected: `[{"action_type":"copy_file","source_path":"templates/form.docx","target_path":"Forms/{client_name}_form.docx","override":false},{"action_type":"copy_folder","source_path":"templates/business_registration","target_path":"Business Registration","override":false}]`,
			hasError: false,
		},
		{
			name: "array with provider fields",
			input: RuleConfigArray{
				{
					ActionType: "copy_file",
					SourcePath: "templates/welcome.docx",
					TargetPath: "{client_folder}/Welcome.docx",
					Provider:   "gdrive",
				},
				{
					ActionType: "copy_folder",
					SourcePath: "templates/forms",
					TargetPath: "{client_folder}/Forms",
					Provider:   "internal",
				},
				{
					ActionType: "generate_document",
					SourcePath: "templates/contract.docx",
					TargetPath: "{client_folder}/Contract_{date}.docx",
					// Provider omitted - should serialize without provider field due to omitempty
				},
			},
			expected: `[{"action_type":"copy_file","source_path":"templates/welcome.docx","target_path":"{client_folder}/Welcome.docx","provider":"gdrive","override":false},{"action_type":"copy_folder","source_path":"templates/forms","target_path":"{client_folder}/Forms","provider":"internal","override":false},{"action_type":"generate_document","source_path":"templates/contract.docx","target_path":"{client_folder}/Contract_{date}.docx","override":false}]`,
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.input.Value()

			if tt.hasError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			if tt.expected == "" {
				assert.Nil(t, result)
			} else {
				assert.JSONEq(t, tt.expected, string(result.([]byte)))
			}
		})
	}
}

func TestRuleConfigArray_Scan(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected RuleConfigArray
		hasError bool
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
			hasError: false,
		},
		{
			name:  "valid JSON bytes",
			input: []byte(`[{"action_type":"copy_file","source_path":"templates/form.docx","target_path":"Forms/{client_name}_form.docx"}]`),
			expected: RuleConfigArray{
				{
					ActionType: "copy_file",
					SourcePath: "templates/form.docx",
					TargetPath: "Forms/{client_name}_form.docx",
				},
			},
			hasError: false,
		},
		{
			name:  "JSON with provider fields",
			input: []byte(`[{"action_type":"copy_file","source_path":"templates/welcome.docx","target_path":"{client_folder}/Welcome.docx","provider":"gdrive"},{"action_type":"copy_folder","source_path":"templates/forms","target_path":"{client_folder}/Forms","provider":"internal"}]`),
			expected: RuleConfigArray{
				{
					ActionType: "copy_file",
					SourcePath: "templates/welcome.docx",
					TargetPath: "{client_folder}/Welcome.docx",
					Provider:   "gdrive",
				},
				{
					ActionType: "copy_folder",
					SourcePath: "templates/forms",
					TargetPath: "{client_folder}/Forms",
					Provider:   "internal",
				},
			},
			hasError: false,
		},
		{
			name:  "JSON with target_provider fields",
			input: []byte(`[{"action_type":"copy_file","source_path":"templates/contract.docx","target_path":"{client_folder}/Contract.docx","provider":"internal","target_provider":"gdrive"},{"action_type":"generate_document","source_path":"templates/welcome.docx","target_path":"{matter_folder}/Welcome.docx","provider":"internal","target_provider":"sharepoint"}]`),
			expected: RuleConfigArray{
				{
					ActionType:     "copy_file",
					SourcePath:     "templates/contract.docx",
					TargetPath:     "{client_folder}/Contract.docx",
					Provider:       "internal",
					TargetProvider: "gdrive",
				},
				{
					ActionType:     "generate_document",
					SourcePath:     "templates/welcome.docx",
					TargetPath:     "{matter_folder}/Welcome.docx",
					Provider:       "internal",
					TargetProvider: "sharepoint",
				},
			},
			hasError: false,
		},
		{
			name:     "invalid input type",
			input:    "not bytes",
			expected: nil,
			hasError: true,
		},
		{
			name:     "invalid JSON",
			input:    []byte(`[{"invalid": json}]`),
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result RuleConfigArray
			err := result.Scan(tt.input)

			if tt.hasError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDocumentAutomationRule_TableName(t *testing.T) {
	rule := DocumentAutomationRule{}
	assert.Equal(t, "document_automation_rules", rule.TableName())
}

func TestRuleAction_GetProvider(t *testing.T) {
	tests := []struct {
		name     string
		action   RuleAction
		expected string
	}{
		{
			name: "empty provider defaults to internal",
			action: RuleAction{
				ActionType: "copy_file",
				SourcePath: "templates/test.docx",
				TargetPath: "{client_folder}/test.docx",
				Provider:   "",
			},
			expected: "internal",
		},
		{
			name: "explicit internal provider",
			action: RuleAction{
				ActionType: "copy_file",
				SourcePath: "templates/test.docx",
				TargetPath: "{client_folder}/test.docx",
				Provider:   "internal",
			},
			expected: "internal",
		},
		{
			name: "gdrive provider",
			action: RuleAction{
				ActionType: "copy_file",
				SourcePath: "templates/test.docx",
				TargetPath: "{client_folder}/test.docx",
				Provider:   "gdrive",
			},
			expected: "gdrive",
		},
		{
			name: "sharepoint provider",
			action: RuleAction{
				ActionType: "copy_file",
				SourcePath: "templates/test.docx",
				TargetPath: "{client_folder}/test.docx",
				Provider:   "sharepoint",
			},
			expected: "sharepoint",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.action.GetProvider()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRuleAction_SetProvider(t *testing.T) {
	action := RuleAction{
		ActionType: "copy_file",
		SourcePath: "templates/test.docx",
		TargetPath: "{client_folder}/test.docx",
	}

	// Test setting provider
	action.SetProvider("gdrive")
	assert.Equal(t, "gdrive", action.Provider)
	assert.Equal(t, "gdrive", action.GetProvider())

	// Test setting empty provider
	action.SetProvider("")
	assert.Equal(t, "", action.Provider)
	assert.Equal(t, "internal", action.GetProvider()) // Should default to internal
}

func TestRuleAction_IsInternalProvider(t *testing.T) {
	tests := []struct {
		name     string
		provider string
		expected bool
	}{
		{
			name:     "empty provider is internal",
			provider: "",
			expected: true,
		},
		{
			name:     "explicit internal provider",
			provider: "internal",
			expected: true,
		},
		{
			name:     "gdrive provider is not internal",
			provider: "gdrive",
			expected: false,
		},
		{
			name:     "sharepoint provider is not internal",
			provider: "sharepoint",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action := RuleAction{
				ActionType: "copy_file",
				SourcePath: "templates/test.docx",
				TargetPath: "{client_folder}/test.docx",
				Provider:   tt.provider,
			}
			result := action.IsInternalProvider()
			assert.Equal(t, tt.expected, result)
		})
	}
}
