package model

type OwnerUser struct {
	Id     uint64 `json:"id"`
	Email  string `json:"email"`
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
}

type State struct {
	Name       string      `json:"name"`
	Owners     []uint64    `json:"owners"`
	OwnerUsers []OwnerUser `json:"owner_users"`
}

type Extra struct {
	Current State `json:"current"`
	Old     State `json:"old"`
}

type ConsumeClientBody struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	ShortName string `json:"short_name"`
	Code      string `json:"code"`
	TenantID  uint64 `json:"tenant_id"`
	Extra     Extra  `json:"extra"`
}

type ConsumeClientReq struct {
	Topic string            `json:"topic"`
	Body  ConsumeClientBody `json:"body"`
}

type ConsumeMatterBody struct {
	ID       uint64 `json:"id"`
	ClientID uint64 `json:"client_id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	TenantID uint64 `json:"tenant_id"`
	Extra    Extra  `json:"extra"`
}

type ConsumeMatterReq struct {
	Topic string            `json:"topic"`
	Body  ConsumeMatterBody `json:"body"`
}

// FindMissingOwner find missing owner from current owner list
// e.g.
// currentOwners = [{Id: 1, Email: "a@b", Name: "A"}]
// oldOwners = [{Id: 1, Email: "a@b", Name: "A"}, {Id: 2, Email: "b@c", Name: "B"}]
// FindMissingOwner(currentOwners, oldOwners) => [{Id: 2, Email: "b@c", Name: "B"}]
func FindMissingOwner(currentOwners []OwnerUser, oldOwners []OwnerUser) []OwnerUser {
	var missingOwners []OwnerUser
	for _, oldOwner := range oldOwners {
		found := false
		for _, currentOwner := range currentOwners {
			if oldOwner.Id == currentOwner.Id {
				found = true
				break
			}
		}
		if !found {
			missingOwners = append(missingOwners, oldOwner)
		}
	}
	return missingOwners
}
