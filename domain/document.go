package domain

type ListDocumentsFilter struct {
	ObjectID   string `json:"object_id,omitempty" query:"object_id" form:"object_id"`
	ObjectType string `json:"object_type,omitempty" query:"object_type" form:"object_type"`
	Search     string `json:"search" form:"search" query:"search"`
	Status     string `json:"status,omitempty" query:"status" form:"status"`
	Key        string `json:"key,omitempty" query:"key" form:"key"`
}

type ListDocumentsQuery struct {
	Search   string `form:"search"`
	TenantID string `form:"tenant_id"`
}

type ListDocumentsV3Filter struct {
	ID          uint64 `json:"id,omitempty" query:"id" form:"id"`
	ObjectID    string `json:"object_id,omitempty" query:"object_id" form:"object_id"`
	ObjectType  string `json:"object_type,omitempty" query:"object_type" form:"object_type"`
	Search      string `json:"search" form:"search" query:"search"`
	Status      string `json:"status,omitempty" query:"status" form:"status"`
	AutoDocRoot bool   `json:"autodoc_root,omitempty" query:"autodoc_root" form:"autodoc_root"`
}
