package share

import (
	"gorm.io/gorm"
	"time"
)

type Share struct {
	ID         uint64 `gorm:"primaryKey; autoIncrement"`
	TenantID   uint64 `gorm:"index"`
	ObjectType string `gorm:"index"`
	ObjectID   uint64 `gorm:"index"`
	GlobKey    string
	Short      string         `gorm:"index"`
	Expiry     uint64         `gorm:"index"`
	CreatedAt  time.Time      `gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time      `gorm:"default:CURRENT_TIMESTAMP"`
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}

func (Share) TableName() string {
	return "share"
}
