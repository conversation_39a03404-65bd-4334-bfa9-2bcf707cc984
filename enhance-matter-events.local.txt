1. In create matter, chúng ta cần handler t<PERSON><PERSON><PERSON><PERSON> hợ<PERSON>, client folder chưa tồn tại (check mapping parent not found), khi đó chúng ta cần
- fetch thông tin client từ client service
- tạo client folder theo config + create mapping
- tiế<PERSON> tục tạo matter.

2. In update matter, chúng ta cần handler t<PERSON><PERSON><PERSON><PERSON> hợ<PERSON>, matter folder chưa tồn tại (check mapping not found), khi đó chúng ta cần tạo matter folder theo config + create mapping, fallback về tương tự case 1

3. <PERSON><PERSON><PERSON> fetch thông tin client
- Sử dụng pkg code.mybil.net/gophers/gokit/components/entity
- Khởi tạo NewGenericFetcher() (IoC từ main app vào service)
- Sử dụng method FetchCache(ctx, KindClient, clientID) để fetch thông tin client
- Thông tin client (type *Generic) sẽ có đủ để tạo folder theo config
