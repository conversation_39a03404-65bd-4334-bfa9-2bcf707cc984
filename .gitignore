/pkg/cloud0
bin/
coverage.txt
.coverage.txt
.envrc
vendor/
go_test.sh

# build binary
/server

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Cursor IDE - ignore project-specific config, use global instead
.cursor/mcp.json

# TaskMaster - ignore generated files
.taskmaster/templates/
.taskmaster/reports/
!.taskmaster/tasks/

.serena/

# Task files
# tasks.json
# tasks/ 
