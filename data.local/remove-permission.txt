we have issue with permission removal from google drive.
When remove permission on client folder, gdrive will remove all shares on matter folder under this client for that user. To prevent this, we need to store shared matter to specified email and the client_id of this matter while saving permission mapping.
When remove client permission, we check if the email is have any matter shared to it. If any, we do re-share all matter under this client to that email.
Please help me make the detail plan to implement this.
