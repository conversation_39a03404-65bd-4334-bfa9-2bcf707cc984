- presign upload request

request:

POST http://glob/presign HTTP/1.1
content-type: application/json
x-user-id: 1
x-tenant-id: 1
x-user-roles: 8

{
  "filename": "filename.docx"
}

response:

{
	"data": {
		"upload_url": "https://uploadurl-with-token",
		"key": "2025/07/1/b718e8e1-9fdc-4984-9bb9-c3e68c2e876e/filename.docx"
	}
}

- upload file to upload url

request:

PUT https://uploadurl-with-token HTTP/1.1
content-type: application/octet-stream

<file content>

response:

200 OK

