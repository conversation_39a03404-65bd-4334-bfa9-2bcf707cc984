# Autodoc Google Drive Integration Design

## Tổng quan

Thiết kế này mô tả việc tích hợp Google Drive vào hệ thống autodoc, cho phép các automation rules copy files và folders từ internal storage sang Google Drive dựa trên tenant configuration.

## Hiện trạng

### Autodoc System hiện tại:
- **ActionHandler**: Interface cho các action types (`copy_file`, `copy_folder`, `generate_document`)
- **RuleAction**: Chỉ có `action_type`, `source_path`, `target_path` - chưa có provider config
- **CopyFileHandler**: Copy file trong internal storage (AutoDocRoot)
- **RuleExecutionEngine**: Execute rules với action registry

### GDrive System hiện tại:
- **GDriveConfig**: Đã có config cho Google Drive integration trong `DocumentSetting`
- **DriveClient**: Interface đầy đủ cho Google Drive operations
- **Service**: Business logic cho GDrive operations

## Thiết kế

### 1. Mở rộng RuleAction Model

```go
// Mở rộng RuleAction để hỗ trợ provider
type RuleAction struct {
    ActionType string `json:"action_type"`
    SourcePath string `json:"source_path"`
    TargetPath string `json:"target_path"`
    Provider   string `json:"provider,omitempty"` // NEW: "internal", "gdrive", "sharepoint"
}
```

### 2. Provider Interface

```go
// Provider interface cho các storage providers
type Provider interface {
    // File operations
    CopyFile(ctx context.Context, params *ProviderCopyParams) error
    CopyFolder(ctx context.Context, params *ProviderCopyParams) error
    GenerateDocument(ctx context.Context, params *ProviderGenerateParams) error
    
    // Provider info
    GetProviderType() string
    GetSupportedActions() []string
}

type ProviderCopyParams struct {
    SourcePath      string
    TargetPath      string
    PlaceholderData map[string]interface{}
    TenantID        uint64
    // Không cần config riêng - lấy từ tenant settings
}

type ProviderGenerateParams struct {
    TemplatePath    string
    TargetPath      string
    PlaceholderData map[string]interface{}
    TenantID        uint64
    // Không cần config riêng - lấy từ tenant settings
}
```

### 3. GDrive Provider Implementation

```go
// GDriveProvider implements Provider interface
type GDriveProvider struct {
    gdriveService   gdriveSvc.DriveClient
    docSettingRepo  repositories.DocumentSettingRepository
    autoDocService  AutoDocService
}

func NewGDriveProvider(
    gdriveService gdriveSvc.DriveClient,
    docSettingRepo repositories.DocumentSettingRepository,
    autoDocService AutoDocService,
) *GDriveProvider {
    return &GDriveProvider{
        gdriveService:  gdriveService,
        docSettingRepo: docSettingRepo,
        autoDocService: autoDocService,
    }
}

func (p *GDriveProvider) GetProviderType() string {
    return "gdrive"
}

func (p *GDriveProvider) GetSupportedActions() []string {
    return []string{"copy_file", "copy_folder", "generate_document"}
}

func (p *GDriveProvider) CopyFile(ctx context.Context, params *ProviderCopyParams) error {
    log := logger.WithCtx(ctx, "GDriveProvider.CopyFile")
    
    // 1. Lấy GDrive config từ tenant settings
    gdriveConfig, err := p.getGDriveConfig(ctx, params.TenantID)
    if err != nil {
        return fmt.Errorf("failed to get GDrive config: %w", err)
    }
    
    if !gdriveConfig.Enabled {
        return fmt.Errorf("GDrive is not enabled for tenant %d", params.TenantID)
    }
    
    // 2. Resolve source path (có thể từ AutoDocRoot hoặc GDrive)
    sourceResult, err := p.resolveSourcePath(ctx, params.SourcePath, params.TenantID, gdriveConfig)
    if err != nil {
        return fmt.Errorf("failed to resolve source path: %w", err)
    }
    
    // 3. Resolve target path trong GDrive
    targetPath := ReplacePlaceholders(params.TargetPath, params.PlaceholderData)
    targetFolderID, err := p.resolveTargetPath(ctx, targetPath, params.TenantID, gdriveConfig)
    if err != nil {
        return fmt.Errorf("failed to resolve target path: %w", err)
    }
    
    // 4. Copy file sử dụng GDrive API
    err = p.copyFileToGDrive(ctx, sourceResult, targetFolderID, gdriveConfig)
    if err != nil {
        return fmt.Errorf("failed to copy file to GDrive: %w", err)
    }
    
    log.Infof("Successfully copied file to GDrive tenant_id=%d target_folder=%s", 
        params.TenantID, targetFolderID)
    
    return nil
}

func (p *GDriveProvider) getGDriveConfig(ctx context.Context, tenantID uint64) (*model.GDriveConfig, error) {
    // Lấy GDrive config từ document settings
    setting, err := p.docSettingRepo.FindOne(ctx, &model.Query{
        Filters: []*model.Filter{
            model.NewFilterE("tenant_id", tenantID),
            model.NewFilterE("key", model.KeyGdriveConfig),
        },
    })
    if err != nil {
        return nil, err
    }
    
    if setting == nil {
        return nil, fmt.Errorf("GDrive config not found for tenant %d", tenantID)
    }
    
    return setting.GetGDriveConfig()
}

func (p *GDriveProvider) resolveSourcePath(ctx context.Context, sourcePath string, tenantID uint64, config *model.GDriveConfig) (*SourcePathResult, error) {
    // Logic để resolve source path
    // Có thể từ AutoDocRoot hoặc từ GDrive
    // Trả về thông tin cần thiết để copy
    return &SourcePathResult{
        DocumentID:  0, // TODO: Implement
        DriveFileID: "", // TODO: Implement
        IsFromGDrive: false,
    }, nil
}

func (p *GDriveProvider) resolveTargetPath(ctx context.Context, targetPath string, tenantID uint64, config *model.GDriveConfig) (string, error) {
    // Logic để resolve target path trong GDrive
    // Sử dụng PathConfig từ GDrive config
    // Tạo folder structure nếu cần
    return "", nil // TODO: Implement
}

func (p *GDriveProvider) copyFileToGDrive(ctx context.Context, source *SourcePathResult, targetFolderID string, config *model.GDriveConfig) error {
    // Logic copy file sử dụng GDrive API
    // Xử lý permission sync nếu cần
    return nil // TODO: Implement
}

// SourcePathResult represents the result of resolving a source path
type SourcePathResult struct {
    DocumentID   uint64 `json:"document_id"`
    DriveFileID  string `json:"drive_file_id"`
    IsFromGDrive bool   `json:"is_from_gdrive"`
}
```

### 4. Provider Registry

```go
// ProviderRegistry quản lý các providers
type ProviderRegistry interface {
    RegisterProvider(provider Provider) error
    GetProvider(providerType string) (Provider, error)
    GetSupportedProviders() []string
}

type providerRegistry struct {
    providers map[string]Provider
}

func NewProviderRegistry() ProviderRegistry {
    return &providerRegistry{
        providers: make(map[string]Provider),
    }
}

func (r *providerRegistry) RegisterProvider(provider Provider) error {
    providerType := provider.GetProviderType()
    r.providers[providerType] = provider
    return nil
}

func (r *providerRegistry) GetProvider(providerType string) (Provider, error) {
    provider, exists := r.providers[providerType]
    if !exists {
        return nil, fmt.Errorf("provider %s not found", providerType)
    }
    return provider, nil
}

func (r *providerRegistry) GetSupportedProviders() []string {
    providers := make([]string, 0, len(r.providers))
    for providerType := range r.providers {
        providers = append(providers, providerType)
    }
    return providers
}
```

### 5. Mở rộng ActionHandler

```go
// Mở rộng ActionHandler để hỗ trợ provider
type ActionHandler interface {
    Execute(ctx context.Context, params *ActionExecutionParams) error
    Test(ctx context.Context, params *ActionExecutionParams) (*ActionTestResult, error)
    GetActionType() string
    Validate(ctx context.Context, params *ActionExecutionParams) error
    
    // NEW: Provider support
    GetSupportedProviders() []string
}

// Mở rộng ActionExecutionParams
type ActionExecutionParams struct {
    TenantID        uint64
    Action          model.RuleAction
    PlaceholderData map[string]interface{}
    RuleID          uint64
    // Không cần Provider instance - sẽ resolve từ tenant settings
}
```

### 6. Mở rộng CopyFileHandler

```go
// Mở rộng CopyFileHandler để hỗ trợ providers
type CopyFileHandler struct {
    *BaseActionHandler
    autoDocService   AutoDocService
    documentRepo     repositories.DocumentRepository
    pathTraverser    *PathTraverser
    providerRegistry ProviderRegistry // NEW
}

func NewCopyFileHandler(
    autoDocService AutoDocService,
    documentRepo repositories.DocumentRepository,
    providerRegistry ProviderRegistry, // NEW
) *CopyFileHandler {
    return &CopyFileHandler{
        BaseActionHandler: NewBaseActionHandler("copy_file"),
        autoDocService:    autoDocService,
        documentRepo:      documentRepo,
        pathTraverser:     NewPathTraverser(documentRepo),
        providerRegistry:  providerRegistry,
    }
}

func (h *CopyFileHandler) Execute(ctx context.Context, params *ActionExecutionParams) error {
    // Nếu có provider specified, sử dụng provider
    if params.Action.Provider != "" && params.Action.Provider != "internal" {
        return h.executeWithProvider(ctx, params)
    }
    
    // Fallback to internal storage (existing logic)
    return h.executeInternal(ctx, params)
}

func (h *CopyFileHandler) executeWithProvider(ctx context.Context, params *ActionExecutionParams) error {
    // Get provider từ registry
    provider, err := h.providerRegistry.GetProvider(params.Action.Provider)
    if err != nil {
        return fmt.Errorf("failed to get provider %s: %w", params.Action.Provider, err)
    }
    
    providerParams := &ProviderCopyParams{
        SourcePath:      params.Action.SourcePath,
        TargetPath:      params.Action.TargetPath,
        PlaceholderData: params.PlaceholderData,
        TenantID:        params.TenantID,
    }
    
    return provider.CopyFile(ctx, providerParams)
}

func (h *CopyFileHandler) executeInternal(ctx context.Context, params *ActionExecutionParams) error {
    // Existing internal storage logic
    // ... (giữ nguyên logic hiện tại)
    return nil
}

func (h *CopyFileHandler) GetSupportedProviders() []string {
    return []string{"internal", "gdrive", "sharepoint"}
}
```

### 7. Mở rộng RuleExecutionEngine

```go
// Mở rộng RuleExecutionEngine để hỗ trợ providers
type ruleExecutionEngine struct {
    autoDocService   AutoDocService
    documentRepo     repositories.DocumentRepository
    actionRegistry   ActionHandlerRegistry
    providerRegistry ProviderRegistry // NEW
}

func NewRuleExecutionEngine(
    autoDocService AutoDocService,
    documentRepo repositories.DocumentRepository,
    providerRegistry ProviderRegistry, // NEW
) RuleExecutionEngine {
    // Create action handler registry
    registry := NewActionHandlerRegistry()

    // Register all action handlers với provider registry
    copyFileHandler := NewCopyFileHandler(autoDocService, documentRepo, providerRegistry)
    copyFolderHandler := NewCopyFolderHandler(autoDocService, documentRepo, providerRegistry)
    generateDocHandler := NewGenerateDocumentHandler(autoDocService, documentRepo, providerRegistry)

    registry.RegisterHandler(copyFileHandler)
    registry.RegisterHandler(copyFolderHandler)
    registry.RegisterHandler(generateDocHandler)

    return &ruleExecutionEngine{
        autoDocService:   autoDocService,
        documentRepo:     documentRepo,
        actionRegistry:   registry,
        providerRegistry: providerRegistry,
    }
}

func (e *ruleExecutionEngine) executeAction(ctx context.Context, tenantID uint64, action model.RuleAction, placeholderData map[string]interface{}, ruleID uint64) error {
    // Get action handler
    handler, err := e.actionRegistry.GetHandler(action.ActionType)
    if err != nil {
        return err
    }
    
    // Validate provider if specified
    if action.Provider != "" && action.Provider != "internal" {
        _, err = e.providerRegistry.GetProvider(action.Provider)
        if err != nil {
            return fmt.Errorf("invalid provider %s: %w", action.Provider, err)
        }
    }
    
    params := &ActionExecutionParams{
        TenantID:        tenantID,
        Action:          action,
        PlaceholderData: placeholderData,
        RuleID:          ruleID,
    }
    
    return handler.Execute(ctx, params)
}
```

### 8. Mở rộng AutoDocService

```go
// Mở rộng AutoDocService để hỗ trợ provider setup
type AutoDocService interface {
    // Existing methods...
    
    // NEW: Provider management
    RegisterProvider(provider Provider) error
    GetProvider(providerType string) (Provider, error)
}

// Mở rộng autoDocService
type autoDocService struct {
    documentRepo      repositories.DocumentRepository
    documentService   DocumentService
    ruleRepo          repositories.DocumentAutomationRuleRepository
    docxProcessor     *DocxProcessor
    providerRegistry  ProviderRegistry // NEW
}

func NewAutoDocService(
    documentRepo repositories.DocumentRepository,
    documentService DocumentService,
    ruleRepo repositories.DocumentAutomationRuleRepository,
    globClient transport.Glob,
    providerRegistry ProviderRegistry, // NEW
) AutoDocService {
    return &autoDocService{
        documentRepo:     documentRepo,
        documentService:  documentService,
        ruleRepo:         ruleRepo,
        docxProcessor:    NewDocxProcessor(globClient),
        providerRegistry: providerRegistry,
    }
}

func (s *autoDocService) RegisterProvider(provider Provider) error {
    return s.providerRegistry.RegisterProvider(provider)
}

func (s *autoDocService) GetProvider(providerType string) (Provider, error) {
    return s.providerRegistry.GetProvider(providerType)
}
```

### 9. Main Application Setup

```go
func setupAutoDocService() AutoDocService {
    // Create provider registry
    providerRegistry := NewProviderRegistry()
    
    // Register GDrive provider
    gdriveProvider := NewGDriveProvider(gdriveService, docSettingRepo, autoDocService)
    providerRegistry.RegisterProvider(gdriveProvider)
    
    // Create AutoDocService với provider registry
    return NewAutoDocService(
        documentRepo,
        documentService,
        ruleRepo,
        globClient,
        providerRegistry,
    )
}
```

## Configuration Examples

### Rule với GDrive provider:
```json
{
  "action_type": "copy_file",
  "source_path": "/templates/contract.docx",
  "target_path": "/clients/{client.short_name}/matters/{matter.name}/contract.docx",
  "provider": "gdrive"
}
```

### Rule với internal storage (default):
```json
{
  "action_type": "copy_file",
  "source_path": "/templates/contract.docx",
  "target_path": "/clients/{client.short_name}/matters/{matter.name}/contract.docx"
  // Không có provider = internal
}
```

## Implementation Steps

### Phase 1: Core Infrastructure
1. Tạo Provider interface và registry
2. Mở rộng RuleAction model
3. Tạo base provider implementations

### Phase 2: GDrive Provider
1. Implement GDriveProvider
2. Integration với existing GDrive service
3. Path resolution cho GDrive

### Phase 3: Action Handler Updates
1. Mở rộng CopyFileHandler, CopyFolderHandler
2. Update RuleExecutionEngine
3. Provider validation và error handling

### Phase 4: Testing & Documentation
1. Unit tests cho providers
2. Integration tests
3. Documentation và examples

## Benefits

- **Flexibility**: Support multiple storage providers
- **Extensibility**: Easy to add new providers (SharePoint, S3, etc.)
- **Backward Compatibility**: Existing rules continue to work
- **Configuration Driven**: Provider config từ tenant settings
- **Reuse**: Leverage existing GDrive infrastructure
- **Centralized**: Provider config tập trung ở tenant level
- **Consistent**: Tất cả actions của tenant dùng cùng provider config

## Notes

- Provider config được quản lý tập trung ở tenant level thông qua `DocumentSetting`
- Actions chỉ cần specify provider type, không cần config phức tạp
- Backward compatible với existing rules
- Dễ mở rộng cho các providers khác trong tương lai 
