As we are exploring document automation, pls propose a solution to manage template within bilabl.

Example use case:

Business Registration Matter:

User creates a Matter with category "Business Registration."

System copies templates (e.g., "Registration Form.docx") to the Matter folder.

User fills a form with "Client Name: ABC Corp," "Matter Info: New Entity," "Owner: <PERSON>."

System generates "Registration Form_ABC Corp.docx" with placeholders replaced, ready for review.

Client Onboarding:

User updates a contact from "Contact" to "Lead."

System create folder “Onboarding“ under Client root directory

System copy all files and folders in “Onboarding template“ into “Onboarding“ folder

System generates "Onboarding_Form_LeadName.docx" in the “Onboarding“ folder, pre-filled with lead details. This file is generated using docx template “Onboarding_Form.docx” under “Onboarding form“
Templates structure:
/.. root
/Onboarding template
         /template folder here
                   /template file inside template folder
         /template file here
/Onboarding form
         /Onboarding_Form.docx



Suggestion:

Create a dedicated "Document templates" under “Setting > Document”.

Under Document template, user can create “document template”. Each document template will have their own name - User can name it (e.g., business registration forms, onboarding documents).

Under document template, <PERSON><PERSON> can upload and organize template files and folder (this is where User organize template folder and files)
