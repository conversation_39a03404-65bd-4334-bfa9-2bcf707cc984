# Google Drive Permission Removal with Matter Access Preservation
## SIMPLIFIED Implementation Plan (Based on Existing Design)

### Problem Analysis
The current Google Drive permission system has a critical flaw: when removing a user's permission from a client folder, Google Drive automatically removes that user's access to all matter folders under that client due to inheritance. This breaks the intended access pattern where users should retain access to specific matters even if client-level access is removed.

### Simplified Solution (Aligned with Existing Plan)

After reviewing the existing solution in `docs/gdrive-int/permission-removal-plan.md`, we'll implement the **SIMPLIFIED APPROACH** that requires minimal schema changes:

**Core Concept:** Add a single `ClientID` field to track matter-to-client relationships and implement preservation logic before client permission removal.

**Key Benefits of Simplified Approach:**
1. **Minimal Schema Changes**: Only one new field (`ClientID`) instead of multiple complex fields
2. **Clean Separation**: Clear distinction between client and matter permissions
3. **Backward Compatible**: Existing permissions continue to work unchanged
4. **Simple Logic**: Straightforward preservation workflow

### Current System Analysis

**Existing Components:**
- `DocumentPermissionMapping` model: Tracks basic permission mappings (ID, TenantID, Email, DriveID, PermID)
- `DocumentMapping` model: Maps business objects to Google Drive folders
- `GDrivePermissionHandler`: Handles permission sync operations
- Event consumers: Client and matter event consumers for automatic permission syncing
- Repository pattern: Clean separation of data access logic

**Current Permission Flow:**
1. Client/matter events trigger permission sync
2. `SyncGoogleDrivePermissions` computes permission differences
3. Permissions are added/removed via Google Drive API
4. Permission mappings are stored/updated in database

### Phase 1: Simplified Database Schema Enhancement

#### 1.1 Simplified DocumentPermissionMapping Model
**File**: `domain/model/document_permission_mapping.go`

```go
type DocumentPermissionMapping struct {
    // Existing fields (unchanged)
    ID       uint64 `json:"id" gorm:"primaryKey;autoIncrement"`
    TenantID uint64 `json:"tenant_id" gorm:"index"`
    Email    string `json:"email" gorm:"uniqueIndex:idx_email_drive_perm"`
    DriveID  string `json:"drive_id" gorm:"uniqueIndex:idx_email_drive_perm"`
    PermID   string `json:"perm_id" gorm:"uniqueIndex:idx_email_drive_perm"`

    // NEW FIELD - Simple approach (only one field needed)
    ClientID uint64 `json:"client_id" gorm:"index"` // Parent client ID for matter permissions (0 for client permissions)
}

// Helper methods for permission type identification
func (dpm *DocumentPermissionMapping) IsClientPermission() bool {
    return dpm.ClientID == 0
}

func (dpm *DocumentPermissionMapping) IsMatterPermission() bool {
    return dpm.ClientID > 0
}
```

**Migration Strategy:**
- Use GORM AutoMigrate for seamless schema updates
- Add single `ClientID` field with index
- Set `ClientID = 0` for client permissions, `ClientID = client_id` for matter permissions
- Maintain backward compatibility with existing data

#### 1.2 Simplified Repository Interface Extensions
**File**: `pkg/repositories/document_permission_mapping.go`

```go
// Add minimal new methods to existing interface
type DocumentPermissionMappingRepository interface {
    // ... existing methods ...

    // New methods for simplified permission preservation
    FindMatterPermissionsByClientAndEmail(ctx context.Context, tenantID uint64, clientID uint64, email string) ([]*model.DocumentPermissionMapping, error)
    FindByClientID(ctx context.Context, tenantID uint64, clientID uint64) ([]*model.DocumentPermissionMapping, error)
}
```

### Phase 2: Simplified Permission Preservation Service

#### 2.1 Simplified Preservation Service
**File**: `pkg/handlers/gdrive_permission_preservation.go`

```go
type PermissionPreservationService struct {
    gdriveService     gdriveSvc.DriveClient
    permissionRepo    repositories.DocumentPermissionMappingRepository
    documentRepo      repositories.DocumentMappingRepository
}

type PreservePermissionsRequest struct {
    TenantID  uint64
    ClientID  uint64  // Use ClientID instead of ClientDriveID for simplicity
    Email     string
    Role      string
}

// Simplified preservation workflow
func (s *PermissionPreservationService) PreserveMatterPermissions(ctx context.Context, req *PreservePermissionsRequest) error {
    log := logger.WithCtx(ctx, "PreserveMatterPermissions")

    // 1. Find all matter permissions for this client and email
    matterPermissions, err := s.permissionRepo.FindMatterPermissionsByClientAndEmail(ctx, req.TenantID, req.ClientID, req.Email)
    if err != nil {
        return fmt.Errorf("failed to find matter permissions: %w", err)
    }

    if len(matterPermissions) == 0 {
        log.Info("No matter permissions to preserve")
        return nil
    }

    // 2. For each matter permission, ensure direct access exists
    for _, matterPerm := range matterPermissions {
        if err := s.ensureDirectMatterPermission(ctx, matterPerm); err != nil {
            log.WithError(err).Errorf("Failed to preserve permission for matter %s", matterPerm.DriveID)
            // Continue with other permissions instead of failing completely
        }
    }

    log.Infof("Preserved %d matter permissions for user %s", len(matterPermissions), req.Email)
    return nil
}

// Simplified permission granting
func (s *PermissionPreservationService) ensureDirectMatterPermission(ctx context.Context, matterPerm *model.DocumentPermissionMapping) error {
    // Check if user already has direct permission to this matter folder
    permissions, err := s.gdriveService.ListPermissions(ctx, matterPerm.DriveID)
    if err != nil {
        return fmt.Errorf("failed to list permissions for matter %s: %w", matterPerm.DriveID, err)
    }

    // Check if user already has direct permission
    for _, perm := range permissions {
        if perm.EmailAddress == matterPerm.Email {
            // User already has direct permission, no need to re-grant
            return nil
        }
    }

    // Grant direct permission to matter folder
    permission := &drive.Permission{
        Type:         "user",
        Role:         "writer", // Use default role or get from existing permission
        EmailAddress: matterPerm.Email,
    }

    _, err = s.gdriveService.CreatePermission(ctx, matterPerm.DriveID, permission)
    if err != nil {
        return fmt.Errorf("failed to create direct permission for matter %s: %w", matterPerm.DriveID, err)
    }

    return nil
}
```

### Phase 3: Integration with Existing Permission Handler

#### 3.1 Simplified Integration with GDrivePermissionHandler
**File**: `pkg/handlers/gdrive_permission.go`

```go
// Add preservation service to existing handler
type GDrivePermissionHandler struct {
    // ... existing fields ...
    preservationService *PermissionPreservationService
}

// Update existing removePermissions method
func (h *GDrivePermissionHandler) removePermissions(ctx context.Context, mapping *model.DocumentMapping, permsToRemove []*model.DocumentPermissionMapping) error {
    log := logger.WithCtx(ctx, "GDrivePermissionHandler.removePermissions")

    if len(permsToRemove) == 0 {
        return nil
    }

    // NEW: Check if this is a client folder and preserve matter permissions
    if mapping.Type == "client" {
        for _, perm := range permsToRemove {
            preserveReq := &PreservePermissionsRequest{
                TenantID: mapping.TenantID,
                ClientID: mapping.ObjectID, // Use ObjectID as ClientID
                Email:    perm.Email,
                Role:     "writer", // Default role
            }

            if err := h.preservationService.PreserveMatterPermissions(ctx, preserveReq); err != nil {
                log.WithError(err).Errorf("Failed to preserve matter permissions for %s", perm.Email)
                // Continue with removal but log the error - don't fail the entire operation
            }
        }
    }

    // Continue with existing removal logic
    for _, perm := range permsToRemove {
        if err := h.removeSinglePermissionWithRetry(ctx, mapping, perm); err != nil {
            log.Errorf("Failed to remove permission for %s: %v", perm.Email, err)
            return err
        }
    }

    return nil
}
```

### Phase 4: Simplified Permission Storage Updates

#### 4.1 Update Permission Creation Logic for Matter Permissions
```go
// Update existing storePermissionMapping to include ClientID
func (h *GDrivePermissionHandler) storePermissionMapping(ctx context.Context, tenantID uint64, driveID, email, permID string) error {
    // Get the document mapping to determine if this is a matter permission
    mapping, err := h.getDocumentMappingByDriveID(ctx, tenantID, driveID)
    if err != nil {
        return fmt.Errorf("failed to get document mapping: %w", err)
    }

    // Set ClientID based on object type
    var clientID uint64
    if mapping.Type == "matter" {
        clientID = mapping.ParentObjectID // For matters, use parent client ID
    } else {
        clientID = 0 // For clients, set to 0
    }

    permMapping := &model.DocumentPermissionMapping{
        TenantID: tenantID,
        Email:    email,
        DriveID:  driveID,
        PermID:   permID,
        ClientID: clientID, // NEW: Set ClientID for tracking
    }

    return h.rDocumentPermission.CreateOrUpdate(ctx, permMapping)
}

// Helper method to get document mapping by drive ID
func (h *GDrivePermissionHandler) getDocumentMappingByDriveID(ctx context.Context, tenantID uint64, driveID string) (*model.DocumentMapping, error) {
    return h.rDocumentMapping.FindOne(ctx, &model.Query{
        Filters: []*model.Filter{
            model.NewFilterE("tenant_id", tenantID),
            model.NewFilterE("drive_id", driveID),
            model.NewFilterE("provider", "google"),
        },
    })
}
```

### Phase 5: Simplified Repository Implementation

#### 5.1 Repository Method Implementation
**File**: `pkg/repositories/document_permission_mapping.go`

```go
// Add new methods to existing repository implementation
func (r *documentPermissionMappingRepositoryImpl) FindMatterPermissionsByClientAndEmail(ctx context.Context, tenantID uint64, clientID uint64, email string) ([]*model.DocumentPermissionMapping, error) {
    var items []*model.DocumentPermissionMapping
    query := r.Tx(ctx).Model(&model.DocumentPermissionMapping{}).
        Where("tenant_id = ?", tenantID).
        Where("email = ?", email).
        Where("client_id = ?", clientID) // Find matter permissions for this client

    err := query.Find(&items).Error
    return items, err
}

func (r *documentPermissionMappingRepositoryImpl) FindByClientID(ctx context.Context, tenantID uint64, clientID uint64) ([]*model.DocumentPermissionMapping, error) {
    var items []*model.DocumentPermissionMapping
    query := r.Tx(ctx).Model(&model.DocumentPermissionMapping{}).
        Where("tenant_id = ?", tenantID).
        Where("client_id = ?", clientID)

    err := query.Find(&items).Error
    return items, err
}
```

### Phase 6: Testing Strategy (Simplified)

#### 6.1 Unit Tests
- Test `FindMatterPermissionsByClientAndEmail` with various scenarios
- Test preservation service with mocked Google Drive client
- Test permission creation with ClientID field population
- Test backward compatibility with existing permissions (ClientID = 0)

#### 6.2 Integration Tests
- End-to-end permission removal and preservation
- Verify matter folders retain access after client permission removal
- Test with multiple users and complex permission scenarios

### Phase 7: Migration and Deployment (Simplified)

#### 7.1 Database Migration
```go
// Migration will be handled automatically by GORM AutoMigrate
// in pkg/repositories/migrate.go - no manual migration needed
func (r *MigrationRepo) MigrateDB(ctx context.Context) {
    models := []interface{}{
        &model.Document{},
        &model.DocumentConfig{},
        &model.DocumentMapping{},
        &model.DocumentPermissionMapping{}, // Will auto-migrate ClientID field
        &model.DocumentSetting{},
        &model.UploadSession{},
    }
    tx := r.db.WithContext(ctx)
    panicIf(tx.AutoMigrate(models...))
}
```

#### 7.2 Data Migration for Existing Records
```go
// One-time data migration to populate ClientID for existing matter permissions
func (r *MigrationRepo) PopulateClientIDForExistingPermissions(ctx context.Context) error {
    // Update existing matter permissions to set ClientID based on DocumentMapping
    query := `
        UPDATE document_permission_mapping
        SET client_id = dm.parent_object_id
        FROM document_mapping dm
        WHERE document_permission_mapping.drive_id = dm.drive_id
        AND dm.type = 'matter'
        AND document_permission_mapping.client_id = 0
    `
    return r.db.WithContext(ctx).Exec(query).Error
}
```

### Implementation Timeline (Simplified)

**Day 1-2**: Database schema update (add ClientID field)
**Day 3-4**: Repository method implementation and testing
**Day 5-6**: Permission preservation service implementation
**Day 7-8**: Integration with existing permission handler
**Day 9-10**: Testing and deployment

### Key Benefits of Simplified Approach

1. **Minimal Changes**: Only one new field instead of multiple complex fields
2. **Clear Logic**: Simple ClientID-based relationship tracking
3. **Backward Compatible**: Existing permissions work unchanged (ClientID = 0)
4. **Easy Testing**: Straightforward test scenarios
5. **Quick Implementation**: Can be completed in 2 weeks instead of 5

This simplified implementation provides the same functionality with much less complexity and faster delivery.

## Detailed Implementation Specifications

### Repository Implementation Details

#### Enhanced PermMapFindArgs Structure
```go
// Update existing PermMapFindArgs in pkg/repositories/document_permission_mapping.go
type PermMapFindArgs struct {
    TenantID            *uint64
    Email               *string
    DriveID             *string
    PermID              *string
    Emails              *[]string
    ParentDriveID       *string  // NEW: Filter by parent drive ID
    PermissionType      *string  // NEW: Filter by permission type
    SourceObjectType    *string  // NEW: Filter by source object type
    SourceObjectID      *uint64  // NEW: Filter by source object ID
    Limit               int
    FireNotFoundErr     bool
}
```

#### Repository Method Implementations
```go
// New repository methods implementation
func (r *documentPermissionMappingRepositoryImpl) FindMatterPermissionsByClientAndEmail(ctx context.Context, tenantID uint64, clientDriveID string, email string) ([]*model.DocumentPermissionMapping, error) {
    var items []*model.DocumentPermissionMapping
    query := r.Tx(ctx).Model(&model.DocumentPermissionMapping{}).
        Where("tenant_id = ?", tenantID).
        Where("email = ?", email).
        Where("parent_drive_id = ?", clientDriveID).
        Where("source_object_type = ?", "matter")

    err := query.Find(&items).Error
    return items, err
}

func (r *documentPermissionMappingRepositoryImpl) FindByParentDriveID(ctx context.Context, tenantID uint64, parentDriveID string) ([]*model.DocumentPermissionMapping, error) {
    var items []*model.DocumentPermissionMapping
    query := r.Tx(ctx).Model(&model.DocumentPermissionMapping{}).
        Where("tenant_id = ?", tenantID).
        Where("parent_drive_id = ?", parentDriveID)

    err := query.Find(&items).Error
    return items, err
}

func (r *documentPermissionMappingRepositoryImpl) BulkCreateOrUpdate(ctx context.Context, mappings []*model.DocumentPermissionMapping) error {
    if len(mappings) == 0 {
        return nil
    }

    // Use batch insert with conflict resolution
    tx := r.Tx(ctx).Clauses(clause.OnConflict{
        UpdateAll: true,
    }).CreateInBatches(mappings, 100) // Process in batches of 100

    return tx.Error
}
```

### Event Consumer Integration

#### Updated Client Event Consumer
```go
// Modify HandleClientUpdated in internal/service/gdrive/client_event_consumer.go
func (c *ClientEventConsumer) HandleClientUpdated(ctx context.Context, payloadJSON string) error {
    // ... existing parsing logic ...

    // Enhanced permission sync with preservation
    if c.permissionSyncer != nil {
        ownerEmails := extractOwnerEmails(body.OwnerUsers)

        // Create enhanced sync request
        req := &SyncPermissionsRequest{
            TenantID:         body.TenantID,
            ObjectType:       "client",
            ObjectID:         body.ID,
            Owners:           ownerEmails,
            PreservationMode: true, // Enable preservation for client updates
        }

        err = c.permissionSyncer.SyncGoogleDrivePermissions(ctx, req)
        if err != nil {
            log.WithError(err).Error("Failed to sync permissions with preservation")
            // Don't fail the entire operation
        }
    }

    return nil
}
```

#### Matter Event Consumer Updates
```go
// Update matter permission creation to include metadata
func (c *MatterEventConsumer) syncMatterPermissions(ctx context.Context, tenantID uint64, matterID uint64, clientID uint64, ownerEmails []string) error {
    // Get client drive ID for parent relationship
    clientMapping, err := c.docMappingRepo.FirstObjectMapping(ctx, "client", "google", tenantID, clientID, 0)
    if err != nil {
        return fmt.Errorf("failed to get client mapping: %w", err)
    }

    // Enhanced sync request with matter metadata
    req := &SyncPermissionsRequest{
        TenantID:         tenantID,
        ObjectType:       "matter",
        ObjectID:         matterID,
        Owners:           ownerEmails,
        ParentDriveID:    &clientMapping.DriveID, // NEW: Include parent relationship
        SourceObjectType: "matter",
        SourceObjectID:   matterID,
    }

    return c.permissionSyncer.SyncGoogleDrivePermissions(ctx, req)
}
```

### Google Drive API Integration

#### Enhanced Permission Creation
```go
// Update addPermissions method in pkg/handlers/gdrive_permission.go
func (h *GDrivePermissionHandler) addPermissions(ctx context.Context, mapping *model.DocumentMapping, ownersToAdd []string, req *SyncPermissionsRequest) error {
    for _, email := range ownersToAdd {
        // Create Google Drive permission
        permission := &drive.Permission{
            Type:         "user",
            Role:         h.config.DefaultRole, // "writer" or "reader"
            EmailAddress: email,
        }

        createdPerm, err := h.gdriveService.CreatePermission(ctx, mapping.DriveID, permission)
        if err != nil {
            return fmt.Errorf("failed to create permission for %s: %w", email, err)
        }

        // Store enhanced permission mapping
        storeReq := &StorePermissionRequest{
            TenantID:         req.TenantID,
            Email:            email,
            DriveID:          mapping.DriveID,
            PermID:           createdPerm.Id,
            Role:             createdPerm.Role,
            PermissionType:   "direct",
            SourceObjectType: &req.ObjectType,
            SourceObjectID:   &req.ObjectID,
            ParentDriveID:    req.ParentDriveID,
        }

        if err := h.storePermissionMapping(ctx, storeReq); err != nil {
            log.WithError(err).Warnf("Failed to store permission mapping for %s", email)
            // Continue processing other permissions
        }
    }

    return nil
}
```

### Monitoring and Observability

#### Metrics and Logging
```go
// Add metrics for permission preservation
type PermissionMetrics struct {
    PreservationAttempts   prometheus.Counter
    PreservationSuccesses  prometheus.Counter
    PreservationFailures   prometheus.Counter
    PreservationDuration   prometheus.Histogram
}

func (s *PermissionPreservationService) PreserveMatterPermissions(ctx context.Context, req *PreservePermissionsRequest) (*PreservationResult, error) {
    start := time.Now()
    defer func() {
        s.metrics.PreservationDuration.Observe(time.Since(start).Seconds())
    }()

    s.metrics.PreservationAttempts.Inc()

    result, err := s.preserveMatterPermissionsInternal(ctx, req)
    if err != nil {
        s.metrics.PreservationFailures.Inc()
        return nil, err
    }

    s.metrics.PreservationSuccesses.Inc()

    // Structured logging for audit trail
    log := logger.WithCtx(ctx, "PreserveMatterPermissions").WithFields(map[string]interface{}{
        "tenant_id":       req.TenantID,
        "client_drive_id": req.ClientDriveID,
        "email":           req.Email,
        "preserved_count": result.PreservedCount,
        "failed_count":    len(result.FailedMatters),
    })

    if len(result.Errors) > 0 {
        log.WithField("errors", result.Errors).Warn("Permission preservation completed with errors")
    } else {
        log.Info("Permission preservation completed successfully")
    }

    return result, nil
}
```

### Configuration Management

#### Feature Flags and Configuration
```go
// Add to document settings for tenant-specific configuration
type PermissionPreservationConfig struct {
    Enabled              bool   `json:"enabled"`
    MaxRetryAttempts     int    `json:"max_retry_attempts"`
    RetryDelaySeconds    int    `json:"retry_delay_seconds"`
    BatchSize            int    `json:"batch_size"`
    EnableAuditLogging   bool   `json:"enable_audit_logging"`
    PreservationTimeout  int    `json:"preservation_timeout_seconds"`
}

// Default configuration
func DefaultPreservationConfig() *PermissionPreservationConfig {
    return &PermissionPreservationConfig{
        Enabled:              true,
        MaxRetryAttempts:     3,
        RetryDelaySeconds:    2,
        BatchSize:            50,
        EnableAuditLogging:   true,
        PreservationTimeout:  300, // 5 minutes
    }
}
```

### Testing Implementation

#### Unit Test Examples
```go
// Test permission discovery logic
func TestPermissionPreservationService_FindMatterPermissionsToPreserve(t *testing.T) {
    tests := []struct {
        name           string
        setupMocks     func(*repositories.MockDocumentMappingRepository, *repositories.MockDocumentPermissionMappingRepository)
        request        *PreservePermissionsRequest
        expectedCount  int
        expectedError  bool
    }{
        {
            name: "Success_MultipleMattersWithPermissions",
            setupMocks: func(docRepo *repositories.MockDocumentMappingRepository, permRepo *repositories.MockDocumentPermissionMappingRepository) {
                // Mock matter mappings under client
                matterMappings := []*model.DocumentMapping{
                    {DriveID: "matter1", ObjectID: 101, Type: "matter"},
                    {DriveID: "matter2", ObjectID: 102, Type: "matter"},
                }
                docRepo.On("Find", mock.Anything, mock.MatchedBy(func(query *model.Query) bool {
                    return len(query.Filters) == 4 // tenant_id, provider, type, parent_drive_id
                })).Return(matterMappings, nil)

                // Mock existing permissions for matters
                permRepo.On("FindOne", mock.Anything, mock.MatchedBy(func(query *model.Query) bool {
                    return query.Filters[2].Value == "matter1" // drive_id filter
                })).Return(&model.DocumentPermissionMapping{
                    Email: "<EMAIL>", DriveID: "matter1",
                }, nil)

                permRepo.On("FindOne", mock.Anything, mock.MatchedBy(func(query *model.Query) bool {
                    return query.Filters[2].Value == "matter2"
                })).Return(nil, gorm.ErrRecordNotFound)
            },
            request: &PreservePermissionsRequest{
                TenantID: 1, ClientDriveID: "client123", Email: "<EMAIL>",
            },
            expectedCount: 1, // Only matter1 has existing permission
            expectedError: false,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation...
        })
    }
}
```

### Performance Considerations

#### Optimization Strategies
```go
// Batch processing for large numbers of matter folders
func (s *PermissionPreservationService) preservePermissionsBatch(ctx context.Context, permissions []*model.DocumentPermissionMapping, batchSize int) error {
    for i := 0; i < len(permissions); i += batchSize {
        end := i + batchSize
        if end > len(permissions) {
            end = len(permissions)
        }

        batch := permissions[i:end]
        if err := s.processBatch(ctx, batch); err != nil {
            return fmt.Errorf("failed to process batch %d-%d: %w", i, end-1, err)
        }

        // Add small delay between batches to avoid rate limiting
        time.Sleep(100 * time.Millisecond)
    }

    return nil
}

// Concurrent processing with worker pool
func (s *PermissionPreservationService) preservePermissionsConcurrent(ctx context.Context, permissions []*model.DocumentPermissionMapping) error {
    const numWorkers = 5
    jobs := make(chan *model.DocumentPermissionMapping, len(permissions))
    results := make(chan error, len(permissions))

    // Start workers
    for w := 0; w < numWorkers; w++ {
        go func() {
            for perm := range jobs {
                results <- s.grantSinglePermission(ctx, perm)
            }
        }()
    }

    // Send jobs
    for _, perm := range permissions {
        jobs <- perm
    }
    close(jobs)

    // Collect results
    var errors []error
    for i := 0; i < len(permissions); i++ {
        if err := <-results; err != nil {
            errors = append(errors, err)
        }
    }

    if len(errors) > 0 {
        return fmt.Errorf("failed to preserve %d permissions: %v", len(errors), errors)
    }

    return nil
}
```

This comprehensive implementation plan provides all the necessary technical details for implementing the Google Drive permission preservation system while maintaining consistency with existing patterns and ensuring robust error handling and performance.
