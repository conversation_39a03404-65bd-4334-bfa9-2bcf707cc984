{
  "topic": "client.create",
  "body": {
    "id": 565,
    "name": "Client Sharepoint Sample",
    "short_name": "CSS",
    "tenant_id": 1,
    "code": "C06855",
    "extra": {
      "current": {
        "name": "Client Sharepoint Sample",
        "client_name": "",
        "client_id": 0,
        "short_name": "CSS",
        "owners": [1],
        "owner_users": [
          {
            "id": 1,
            "name": "Admin User",
            "avatar_url": "https://stg-static.bilabl.io/v1/192x192/avatar-path",
            "email": "<EMAIL>"
          }
        ],
        "associates": [],
        "stage": 256,
        "stage_name": "Initial",
        "stage_text": null,
        "note": ""
      }
    }
  }
}

{
  "topic": "client.update",
  "body": {
    "id": 10219,
    "client_id": 0,
    "name": "acclime Vietnam",
    "short_name": "ACCLIME",
    "code": "C06855",
    "tenant_id": 1,
    "extra": {
      "current": {
        "name": "acclime Vietnam",
        "client_name": "",
        "client_id": 0,
        "short_name": "",
        "owners": [23],
        "owner_users": [
          {
            "id": 23,
            "name": "Le Hoang Tam",
            "avatar_url": "https://stg-static.bilabl.io/v1/192x192/2022/07/1/3788c8c5-b3d1-4f5d-9cf7-846a96309c4b/photo-1633332755192-727a05c4013d.jpg",
            "email": "<EMAIL>"
          }
        ],
        "associates": null,
        "stage": 256,
        "stage_name": "Initial",
        "stage_text": null,
        "note": ""
      },
      "old": {
        "name": "acclime Vietnam",
        "client_name": "",
        "client_id": 0,
        "short_name": "",
        "owners": [23],
        "owner_users": [
          {
            "id": 23,
            "name": "Le Hoang Tam",
            "avatar_url": "https://stg-static.bilabl.io/v1/192x192/2022/07/1/3788c8c5-b3d1-4f5d-9cf7-846a96309c4b/photo-1633332755192-727a05c4013d.jpg",
            "email": "<EMAIL>"
          }
        ],
        "associates": null,
        "stage": 256,
        "stage_name": "Initial",
        "stage_text": null,
        "note": ""
      }
    }
  }
}


---
matter.create
matter.update 

{
  "id": 123,
  "client_id": 456,
  "name": "Matter Name",
  "code": "M00123",            // Matter code in main body
  "tenant_id": 1,
  "actor_id": 789,
  "no_notify": false,          // Only in create events
  "extra": {
    "current": {
      "name": "Matter Name",
      "owners": [101, 102],
      "owner_users": [
        {
          "id": 101,
          "email": "<EMAIL>",
          "name": "User Name",
          "avatar_url": ""
        }
      ]
    },
    "old": {
      "name": "Old Matter Name",
      "owners": [101],
      "owner_users": [...]
    }
  }
}

