# Google Drive External Activity Loading - Updated Technical Design

## Executive Summary

This document outlines the updated technical design for Google Drive external activity loading, following SharePoint's external-only pattern. Key updates:

- **Endpoint**: `/v3/gdrive/activities` (unified with gdrive group)
- **Service Account Actor Resolution**: Addresses People API limitations
- **External-Only Approach**: No local storage, direct API calls

## Updated API Design

### Endpoint Specification

**Endpoint**: `GET /v3/gdrive/activities`

**Query Parameters:**
- `next_page` (string): Pagination token from previous response
- Compatible with SharePoint activity endpoint parameters

**Response Format**: Identical to SharePoint
```json
{
  "data": [
    {
      "id": "activity_123",
      "actor_email": "<EMAIL>", 
      "actor_name": "<PERSON>",
      "action_type": "edit",
      "action_data": {
        "doc_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "Project Plan.docx",
        "web_url": "https://drive.google.com/file/d/...",
        "parent_folder": "Projects",
        "is_file": true
      },
      "tenant_id": 12345,
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ],
  "meta": {
    "next_page": "token_abc123",
    "page_size": 20
  }
}
```

## Service Account Actor Information Solution

### Problem Analysis

**Challenge**: Service accounts cannot access People API to resolve user details from Drive Activity API's `personName` field (e.g., "people/PERSON_ID").

**Root Cause**: People API requires user consent or domain-wide delegation with specific scopes that may not be available in all tenant configurations.

### Recommended Solution Strategy

**Multi-layered Approach:**

1. **Primary: Drive API File Metadata**
   ```go
   func getActorFromFileMetadata(fileID string) (email, name string) {
       file, err := driveService.Files.Get(fileID).
           Fields("lastModifyingUser(displayName,emailAddress),owners(displayName,emailAddress)").
           Do()
       
       if err == nil && file.LastModifyingUser != nil {
           return file.LastModifyingUser.EmailAddress, file.LastModifyingUser.DisplayName
       }
       
       // Fallback to file owner if lastModifyingUser not available
       if err == nil && len(file.Owners) > 0 {
           return file.Owners[0].EmailAddress, file.Owners[0].DisplayName
       }
       
       return "", ""
   }
   ```

2. **Secondary: Activity API Actor Types**
   ```go
   func extractActorInfo(actors []*driveactivity.Actor, targetFileID string) (email, name string) {
       for _, actor := range actors {
           switch {
           case actor.User != nil && actor.User.KnownUser != nil:
               // Try to get from file metadata first
               if email, name := getActorFromFileMetadata(targetFileID); email != "" {
                   return email, name
               }
               // Extract person ID for potential future resolution
               personID := extractPersonID(actor.User.KnownUser.PersonName)
               return fmt.Sprintf("user+%s@unknown", personID), "Unknown User"
               
           case actor.User != nil && actor.User.DeletedUser != nil:
               return "deleted@user", "Deleted User"
               
           case actor.Administrator != nil:
               return "admin@system", "Administrator"
               
           case actor.System != nil:
               return "system@google", "Google System"
           }
       }
       return "unknown@system", "Unknown User"
   }
   ```

3. **Tertiary: Admin SDK (Optional)**
   ```go
   // Only if domain-wide delegation is configured
   func getActorFromAdminSDK(personID string) (email, name string) {
       if !isDomainWideDelegationEnabled() {
           return "", ""
       }
       
       user, err := adminService.Users.Get(personID).Do()
       if err == nil {
           return user.PrimaryEmail, user.Name.FullName
       }
       return "", ""
   }
   ```

### Implementation Priority

1. **Phase 1**: Implement Drive API file metadata approach
2. **Phase 2**: Add proper handling for system/admin/deleted actors
3. **Phase 3**: Optional Admin SDK integration for enhanced user resolution

## Updated Handler Implementation

```go
func (h *GDriveHandler) ListActivity(r *ginext.Request) (*ginext.Response, error) {
    // 1. Parse query parameters
    query := struct {
        NextPage string `json:"next_page" query:"next_page" form:"next_page"`
    }{}
    r.MustBind(&query)
    
    // 2. Get tenant authentication
    tenantID := ginext.Uint64TenantID(r.GinCtx)
    gdriveConfig, err := h.loadGDriveConfig(r.Context(), tenantID)
    r.MustNoError(err)
    
    // 3. Call Google Drive Activity API
    activities, nextPageToken, err := h.gdriveClient.QueryActivities(
        r.Context(), 
        gdriveConfig.ServiceAccount, 
        query.NextPage,
    )
    r.MustNoError(err)
    
    // 4. Transform to standard format with enhanced actor resolution
    standardActivities := h.transformActivitiesWithActorResolution(activities, tenantID, gdriveConfig)
    
    // 5. Return standardized response
    resp := ActivityListResponse{
        Data: standardActivities,
        Meta: ActivityMeta{
            NextPage: nextPageToken,
            PageSize: 20,
        },
    }
    
    return ginext.NewResponse(200, ginext.WithRawBody(resp)), nil
}
```

## Route Registration

```go
// Updated route to match gdrive group pattern
app.Router().GET("/v3/gdrive/activities", ginext.AuthRequiredMiddleware, w(gdriveHandler.ListActivity))
```

## Data Transformation Enhancements

### Enhanced Actor Resolution

```go
func (h *GDriveHandler) transformActivitiesWithActorResolution(
    activities []*driveactivity.DriveActivity, 
    tenantID uint64,
    config *GDriveConfig,
) []ActivityItemResponse {
    
    var result []ActivityItemResponse
    
    for _, activity := range activities {
        // Extract target file ID for actor resolution
        targetFileID := extractTargetFileID(activity.Targets)
        
        // Resolve actor with multiple strategies
        actorEmail, actorName := h.resolveActor(activity.Actors, targetFileID, config)
        
        // Map action type
        actionType := mapGDriveActionType(activity.PrimaryActionDetail)
        
        // Extract action data
        actionData := extractActionData(activity.Targets, activity.PrimaryActionDetail)
        
        // Build response item
        item := ActivityItemResponse{
            ID:         activity.Id,
            ActorEmail: actorEmail,
            ActorName:  actorName,
            ActionType: actionType,
            ActionData: actionData,
            TenantID:   tenantID,
            Timestamp:  activity.Timestamp.AsTime().Format(time.RFC3339),
        }
        
        result = append(result, item)
    }
    
    return result
}
```

## Error Handling Strategy

### Actor Resolution Failures

```go
func (h *GDriveHandler) resolveActor(actors []*driveactivity.Actor, fileID string, config *GDriveConfig) (email, name string) {
    // Try multiple resolution strategies
    strategies := []func() (string, string){
        func() (string, string) { return h.getActorFromFileMetadata(fileID, config) },
        func() (string, string) { return h.getActorFromActivityAPI(actors) },
        func() (string, string) { return h.getActorFromAdminSDK(actors, config) },
    }
    
    for _, strategy := range strategies {
        if email, name := strategy(); email != "" {
            return email, name
        }
    }
    
    // Final fallback
    return "unknown@system", "Unknown User"
}
```

## Performance Considerations

### Caching Strategy

```go
// Cache actor resolution results to avoid repeated API calls
type ActorCache struct {
    cache map[string]ActorInfo
    mutex sync.RWMutex
    ttl   time.Duration
}

func (c *ActorCache) GetActor(personID string) (ActorInfo, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    info, exists := c.cache[personID]
    if exists && time.Since(info.CachedAt) < c.ttl {
        return info, true
    }
    return ActorInfo{}, false
}
```

## Testing Strategy

### Actor Resolution Testing

1. **Unit Tests**: Test each actor resolution strategy independently
2. **Integration Tests**: Test with real Google Drive Activity API responses
3. **Fallback Tests**: Verify graceful degradation when APIs are unavailable
4. **Performance Tests**: Measure impact of actor resolution on response times

## Migration Path

### Phase 1: Basic Implementation
- Implement `/v3/gdrive/activities` endpoint
- Basic actor resolution using file metadata
- Standard response format matching SharePoint

### Phase 2: Enhanced Actor Resolution
- Add caching for actor information
- Implement multiple resolution strategies
- Add comprehensive error handling

### Phase 3: Optimization
- Performance tuning and caching
- Admin SDK integration (if available)
- Monitoring and alerting

## Success Metrics

1. **API Consistency**: 100% response format compatibility with SharePoint
2. **Actor Resolution Rate**: >90% successful actor identification
3. **Performance**: <500ms response time including actor resolution
4. **Reliability**: 99.5% success rate for activity fetching

---

**Document Version**: 3.0 (Updated for /v3 endpoint and service account limitations)  
**Last Updated**: 2024-01-15  
**Author**: Development Team
