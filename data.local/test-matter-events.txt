## Testing Coordination System - Always Enabled

### Prerequisites
- dev server is running at http://localhost:8088
- **Coordination is ALWAYS ENABLED (permanently)**

### Test: Coordination Flow (Always Active)
```bash
# Test coordinated behavior - this should trigger coordination flow
curl -X POST "http://localhost:8088/internal/consume/autodoc/matter/created" \
     -H "Content-Type: application/json" \
     -d '{
       "topic": "matter.create",
       "body": {
         "id": 789,
         "client_id": 456,
         "tenant_id": 123,
         "name": "Test Matter - With Coordination",
         "description": "Testing coordination system"
       }
     }'
```

### Expected Coordination Flow:
1. **CoordinatedEventRuleMatchingService** intercepts matter.create event
2. **MatterEventCoordinator** initializes coordination tracking
3. **Provider Operations** execute in parallel:
   - GDrive folder creation
   - SharePoint folder creation (if configured)
   - Internal folder creation
4. **Wait for all providers** to complete
5. **Trigger autodoc rules** only after all folders exist

### Expected Logs to Verify:
- "Initiating coordinated matter.create processing"
- "Matter folder creation coordination initiated"
- "Triggering operation for provider"
- "Provider completion notification"
- "Successfully executed autodoc rules"
```

### Test 3: Direct Provider Endpoints (Conflict Detection)
```bash
# Test direct GDrive endpoint (should show warnings)
curl -X POST "http://localhost:8088/internal/consume/gdrive/matter/crud" \
     -H "Content-Type: application/json" \
     -d '{
       "topic": "matter.create",
       "body": {
         "id": 999,
         "client_id": 888,
         "tenant_id": 123,
         "name": "Direct GDrive Test"
       }
     }'

# Test direct SharePoint endpoint (should show warnings)
curl -X POST "http://localhost:8088/internal/consume/sharepoint/matter/crud" \
     -H "Content-Type: application/json" \
     -d '{
       "topic": "matter.create",
       "body": {
         "id": 888,
         "client_id": 999,
         "tenant_id": 123,
         "name": "Direct SharePoint Test"
       }
     }'
```

### Expected Logs to Check

#### Coordination Disabled:
- "Received matter.created event for autodoc processing"
- "Processing matter.create event"
- "Starting rule matching" (immediate execution)

#### Coordination Enabled:
- "Initiating coordinated matter.create processing"
- "Matter folder creation coordination initiated"
- "Provider completion notification"
- "Successfully executed autodoc rules"

#### Direct Endpoints (with coordination enabled):
- "Direct [Provider] provider endpoint called while coordination is enabled"
- "Consider using /internal/consume/autodoc/matter/created"

### Check Logs
```bash
# Monitor logs in real-time
tail -f /usr/local/var/log/runlog_docman.log | grep -E "(coordination|matter|autodoc)"

# Or check recent logs
tail -n 50 /usr/local/var/log/runlog_docman.log
```
- to update COORDINATION_ENABLED, edit file .env like COORDINATION_ENABLED=true then run `svcman restart docman` to restart server
